!(function(){"use strict";var K=(P,D,a)=>new Promise((m,o)=>{var s=r=>{try{p(a.next(r))}catch(l){o(l)}},e=r=>{try{p(a.throw(r))}catch(l){o(l)}},p=r=>r.done?m(r.value):Promise.resolve(r.value).then(s,e);p((a=a.apply(P,D)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6939],{96547:function(P,D,a){a.d(D,{A:function(){return o}});var m=a(29134);function o(s,e){var p,r,l;s.accDescr&&((p=e.setAccDescription)==null||p.call(e,s.accDescr)),s.accTitle&&((r=e.setAccTitle)==null||r.call(e,s.accTitle)),s.title&&((l=e.setDiagramTitle)==null||l.call(e,s.title))}(0,m.eW)(o,"populateCommonDb")},16939:function(P,D,a){a.d(D,{diagram:function(){return te}});var m=a(96547),o=a(50854),s=a(44133),e=a(29134),p=a(38663),r=a(69471),l=e.vZ.pie,S={sections:new Map,showData:!1,config:l},C=S.sections,W=S.showData,N=structuredClone(l),z=(0,e.eW)(()=>structuredClone(N),"getConfig"),F=(0,e.eW)(()=>{C=new Map,W=S.showData,(0,e.ZH)()},"clear"),j=(0,e.eW)(({label:t,value:n})=>{C.has(t)||(C.set(t,n),e.cM.debug(`added new section: ${t}, with value: ${n}`))},"addSection"),V=(0,e.eW)(()=>C,"getSections"),H=(0,e.eW)(t=>{W=t},"setShowData"),Z=(0,e.eW)(()=>W,"getShowData"),$={getConfig:z,clear:F,setDiagramTitle:e.g2,getDiagramTitle:e.Kr,setAccTitle:e.GN,getAccTitle:e.eu,setAccDescription:e.U$,getAccDescription:e.Mx,addSection:j,getSections:V,setShowData:H,getShowData:Z},Q=(0,e.eW)((t,n)=>{(0,m.A)(t,n),n.setShowData(t.showData),t.sections.map(n.addSection)},"populateDb"),X={parse:(0,e.eW)(t=>K(this,null,function*(){const n=yield(0,p.Qc)("pie",t);e.cM.debug(n),Q(n,$)}),"parse")},Y=(0,e.eW)(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),J=Y,b=(0,e.eW)(t=>{const n=[...t.entries()].map(d=>({label:d[0],value:d[1]})).sort((d,g)=>g.value-d.value);return(0,r.ve8)().value(d=>d.value)(n)},"createPieArcs"),q=(0,e.eW)((t,n,ae,d)=>{e.cM.debug(`rendering pie chart
`+t);const g=d.db,R=(0,e.nV)(),L=(0,o.Rb)(g.getConfig(),R.pie),k=40,_=18,E=4,h=450,M=h,w=(0,s.P)(n),f=w.append("g");f.attr("transform","translate("+M/2+","+h/2+")");const{themeVariables:c}=R;let[T]=(0,o.VG)(c.pieOuterStrokeWidth);T!=null||(T=2);const I=L.textPosition,x=Math.min(M,h)/2-k,re=(0,r.Nb1)().innerRadius(0).outerRadius(x),ie=(0,r.Nb1)().innerRadius(x*I).outerRadius(x*I);f.append("circle").attr("cx",0).attr("cy",0).attr("r",x+T/2).attr("class","pieOuterCircle");const B=g.getSections(),y=b(B),ne=[c.pie1,c.pie2,c.pie3,c.pie4,c.pie5,c.pie6,c.pie7,c.pie8,c.pie9,c.pie10,c.pie11,c.pie12],v=(0,r.PKp)(ne);f.selectAll("mySlices").data(y).enter().append("path").attr("d",re).attr("fill",i=>v(i.data.label)).attr("class","pieCircle");let G=0;B.forEach(i=>{G+=i}),f.selectAll("mySlices").data(y).enter().append("text").text(i=>(i.data.value/G*100).toFixed(0)+"%").attr("transform",i=>"translate("+ie.centroid(i)+")").style("text-anchor","middle").attr("class","slice"),f.append("text").text(g.getDiagramTitle()).attr("x",0).attr("y",-(h-50)/2).attr("class","pieTitleText");const O=f.selectAll(".legend").data(v.domain()).enter().append("g").attr("class","legend").attr("transform",(i,u)=>{const A=_+E,se=A*v.domain().length/2,le=12*_,oe=u*A-se;return"translate("+le+","+oe+")"});O.append("rect").attr("width",_).attr("height",_).style("fill",v).style("stroke",v),O.data(y).append("text").attr("x",_+E).attr("y",_-E).text(i=>{const{label:u,value:A}=i.data;return g.getShowData()?`${u} [${A}]`:u});const ce=Math.max(...O.selectAll("text").nodes().map(i=>{var u;return(u=i==null?void 0:i.getBoundingClientRect().width)!=null?u:0})),U=M+k+_+E+ce;w.attr("viewBox",`0 0 ${U} ${h}`),(0,e.v2)(w,h,U,L.useMaxWidth)},"draw"),ee={draw:q},te={parser:X,db:$,renderer:ee,styles:J}}}]);
}());