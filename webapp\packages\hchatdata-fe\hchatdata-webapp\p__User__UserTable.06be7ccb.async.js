(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[8629],{61314:function(D,u,_){"use strict";var C=_(90819),g=_.n(C),b=_(73193),t=_.n(b),J=_(89933),j=_.n(J),X=_(45332),k=_.n(X),U=_(44194),O=_(20263),Z=_(7477),x=_(34284),Q=_(15783),M=_(46504),a=_(31549),L={labelCol:{span:4},wrapperCol:{span:18}},F=O.Z.Item,ee=function(H){var _e=(0,U.useState)({name:"",password:""}),W=k()(_e,2),q=W[0],re=W[1],oe=(0,U.useState)(!1),se=k()(oe,2),le=se[0],V=se[1],ge=O.Z.useForm(),ue=k()(ge,1),ne=ue[0],ke=H.onSubmit,pe=H.onCancel,ve=H.createModalVisible,Pe=function(){var ae=j()(g()().mark(function te(){var G,ce;return g()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,ne.validateFields();case 2:return G=m.sent,re(t()(t()({},q),G)),V(!0),ce=t()(t()({},q),G),m.prev=6,m.next=9,ke(ce);case 9:V(!1),m.next=16;break;case 12:m.prev=12,m.t0=m.catch(6),V(!1),Z.ZP.error(m.t0.message);case 16:case"end":return m.stop()}},te,null,[[6,12]])}));return function(){return ae.apply(this,arguments)}}(),De=function(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.ZP,{onClick:pe,children:"\u53D6\u6D88"}),(0,a.jsx)(x.ZP,{type:"primary",loading:le,onClick:Pe,children:"\u63D0\u4EA4"})]})};return(0,a.jsx)(Q.Z,{width:600,styles:{padding:"32px 40px 48px"},destroyOnHidden:!0,title:"\u521B\u5EFA\u7528\u6237",open:ve,footer:De(),onCancel:pe,children:(0,a.jsxs)(O.Z,t()(t()({},L),{},{form:ne,initialValues:t()({},q),children:[(0,a.jsx)(F,{name:"name",rules:[{required:!0}],label:"\u8D26\u53F7",children:(0,a.jsx)(M.default,{size:"large",placeholder:"\u8BF7\u8F93\u5165\u8D26\u53F7"})}),(0,a.jsx)(F,{name:"password",rules:[{required:!0,min:6,max:10}],label:"\u5BC6\u7801",children:(0,a.jsx)(M.default,{size:"large",type:"password",placeholder:"\u8BF7\u8F93\u5165\u5BC6\u7801"})})]}))})};u.Z=ee},83568:function(D,u,_){"use strict";_.d(u,{N:function(){return g},w:function(){return b}});var C=_(43047);function g(t){return(0,C.ZP)("".concat("/api/auth/","user/register"),{method:"POST",data:t})}function b(t){return(0,C.ZP)("".concat("/api/auth/","user/login"),{method:"POST",data:t})}},75325:function(D,u,_){"use strict";_.r(u),_.d(u,{default:function(){return we}});var C=_(73193),g=_.n(C),b=_(90819),t=_.n(b),J=_(89933),j=_.n(J),X=_(45332),k=_.n(X),U=_(49841),O=_.n(U),Z=_(12914),x=_(7477),Q=_(88732),M=_(34284),a=_(44194),L=_(43047),F=_(84025),ee=_.n(F),ye=_(42520),H=function(){return window.location.pathname.includes("/chatSetting/")?"chat":"semantic"};function _e(){return L.ZP.get("".concat("/api/auth/","user/getUserALlList"))}function W(e){return L.ZP.post("".concat("/api/auth/","user/disabled "),{data:e})}function q(){return request.get("".concat("/api/semantic/","domain/getDomainList"))}function re(e){return request.get("".concat("/api/semantic/","domain/getDomain/").concat(e.modelId))}function oe(e){return request.post("".concat("/api/semantic/","domain/createDomain"),{data:e})}function se(e){return request.post("".concat("/api/semantic/","domain/updateDomain"),{data:e})}function le(e){return request.post("".concat("/api/semantic/","datasource/createDatasource"),{data:e})}function V(e){return request.post("".concat("/api/semantic/","datasource/updateDatasource"),{data:e})}function ge(e){var s=e.domainId,p=e.modelId,n={data:_objectSpread(_objectSpread(_objectSpread({current:1,pageSize:999999},e),s?{domainIds:[s]}:{}),p?{modelIds:[p]}:{})};return request.post("".concat("/api/semantic/","dimension/queryDimension"),n)}function ue(e){return e.id?request("".concat("/api/semantic/","commonDimension"),{method:"PUT",data:e}):request.post("".concat("/api/semantic/","commonDimension"),{data:e})}function ne(e){return request("".concat("/api/semantic/","commonDimension/").concat(e),{method:"DELETE"})}function ke(e){return request.get("".concat("/api/semantic/","dimension/getDimensionInModelCluster/").concat(e))}function pe(e){return request.post("".concat("/api/semantic/","dimension/createDimension"),{data:e})}function ve(e){return request.post("".concat("/api/semantic/","dimension/updateDimension"),{data:e})}function Pe(e){return request.post("".concat("/api/semantic/","dimension/updateDimension/alias/value"),{data:e})}function De(e){return request.post("".concat("/api/semantic/","dimension/mockDimensionAlias"),{data:e})}function ae(e){return request.post("".concat("/api/semantic/","dimension/mockDimensionValuesAlias"),{data:e})}function te(e){return request.post("".concat("/api/semantic/","knowledge/dict/data"),{data:e})}function G(e){var s=e.domainId,p=e.modelId,n={data:_objectSpread(_objectSpread(_objectSpread({current:1,pageSize:999999},e),s?{domainIds:[s]}:{}),p?{modelIds:[p]}:{})};return request.post("".concat("/api/semantic/","metric/queryMetric"),n)}function ce(e){return request.post("".concat("/api/semantic/","metric/createMetric"),{data:e})}function Ae(e){return request.post("".concat("/api/semantic/","metric/updateMetric"),{data:e})}function m(e){return request.post("".concat("/api/semantic/","metric/batchUpdateStatus"),{data:e})}function Ue(e){return request.post("".concat("/api/semantic/","dimension/batchUpdateStatus"),{data:e})}function Me(e){return me.apply(this,arguments)}function me(){return me=_asyncToGenerator(_regeneratorRuntime().mark(function e(s){var p;return _regeneratorRuntime().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,request.post("".concat("/api/semantic/","query/downloadBatch/metric"),{responseType:"blob",getResponse:!0,data:s});case 2:p=r.sent,Ee(p.data);case 4:case"end":return r.stop()}},e)})),me.apply(this,arguments)}function Le(e){return request.post("".concat("/api/semantic/","metric/mockMetricAlias"),{data:e})}function Fe(){return request.get("".concat("/api/semantic/","metric/getMetricTags"))}function He(e){return request.get("".concat("/api/semantic/","metric/getMetric/").concat(e))}function We(e){return request.get("".concat("/api/semantic/","metric/getDrillDownDimension"),{params:{metricId:e}})}function qe(e){return request.get("".concat("/api/semantic/","datasource/getMeasureListOfModel/").concat(e))}function Ve(e){return request("".concat("/api/semantic/","datasource/deleteDatasource/").concat(e),{method:"DELETE"})}function Ge(e){return request("".concat("/api/semantic/","dimension/deleteDimension/").concat(e),{method:"DELETE"})}function Be(e){return request("".concat("/api/semantic/","metric/deleteMetric/").concat(e),{method:"DELETE"})}function ze(e){return request("".concat("/api/semantic/","domain/deleteDomain/").concat(e),{method:"DELETE"})}function Ye(e){return request("".concat("/api/auth/","queryGroup"),{method:"GET",params:{modelId:e}})}function Ke(e){return request("".concat("/api/auth/","createGroup"),{method:"POST",data:e})}function $e(e){return request("".concat("/api/auth/","updateGroup"),{method:"POST",data:e})}function Je(e){return request("".concat("/api/auth/","removeGroup"),{method:"POST",data:e})}function Xe(e){return request("".concat("/api/chat/","conf"),{method:"POST",data:e})}function Ze(e){return request("".concat("/api/chat/","conf"),{method:"PUT",data:e})}function Qe(e){return request("".concat("/api/chat/","conf/search"),{method:"POST",data:e})}function e_(e){return request("".concat("/api/semantic/","datasource/getDatasourceRelaList/").concat(e),{method:"GET"})}function __(e){return request("".concat("/api/semantic/","viewInfo/createOrUpdateDatasourceRela"),{method:"POST",data:e})}function s_(e){return request("".concat("/api/semantic/","modelRela"),{method:e!=null&&e.id?"PUT":"POST",data:e})}function n_(e){if(e)return request("".concat("/api/semantic/","modelRela/").concat(e),{method:"DELETE"})}function p_(e){return request("".concat("/api/semantic/","modelRela/list"),{method:"GET",params:{domainId:e}})}function a_(e){return request("".concat("/api/semantic/","viewInfo/createOrUpdateViewInfo"),{method:"POST",data:e})}function t_(e){return request("".concat("/api/semantic/","viewInfo/getViewInfoList/").concat(e),{method:"GET"})}function c_(e){return request("".concat("/api/semantic/","viewInfo/deleteViewInfo/").concat(e),{method:"DELETE"})}function m_(e){return request("".concat("/api/semantic/","viewInfo/deleteDatasourceRela/").concat(e),{method:"DELETE"})}function i_(e){return request("".concat("/api/semantic/","viewInfo/getDomainSchemaRela/").concat(e),{method:"GET"})}function d_(){return request("".concat("/api/semantic/","database/getDatabaseList"),{method:"GET"})}function r_(e){return request("".concat("/api/semantic/","database/").concat(e),{method:"DELETE"})}function o_(e){return request("".concat("/api/semantic/","database/createOrUpdateDatabase"),{method:"POST",data:e})}function l_(e){return request("".concat("/api/semantic/","database/testConnect"),{method:"POST",data:e})}function g_(e){return request("".concat("/api/semantic/","database/getCatalogs"),{method:"GET",params:{id:e}})}function u_(e,s){return request("".concat("/api/semantic/","database/getDbNames"),{method:"GET",params:{id:e,catalog:s}})}function k_(e,s,p){return request("".concat("/api/semantic/","database/getTables"),{method:"GET",params:{databaseId:e,catalog:s,db:p}})}function v_(e,s,p,n){return request("".concat("/api/semantic/","database/getColumnsByName"),{method:"GET",params:{databaseId:e,catalog:s,db:p,table:n}})}function P_(e){return request("".concat("/api/semantic/","model/getModelList/").concat(e),{method:"GET"})}function D_(e){return request("".concat("/api/semantic/","model/createModel"),{method:"POST",data:e})}function E_(e){return request("".concat("/api/semantic/","model/updateModel"),{method:"POST",data:e})}function C_(e){return request("".concat("/api/semantic/","model/batchUpdateStatus"),{method:"POST",data:e})}function x_(e){return request("".concat("/api/semantic/","model/deleteModel/").concat(e),{method:"DELETE"})}function y_(e){return request("".concat("/api/semantic/","model/getUnAvailableItem"),{method:"POST",data:e})}function A_(e){return e.modelId?request.get("".concat("/api/semantic/","model/getModel/").concat(e.modelId)):{}}function h_(e){return request.get("".concat("/api/semantic/","metric/getMetricsToCreateNewMetric/").concat(e.modelId))}function R_(e){return request("".concat("/api/semantic/","model/getAllModelByDomainId"),{method:"GET",params:{domainId:e}})}function S_(e){return request("".concat("/api/semantic/","knowledge/task"),{method:"POST",data:e})}function b_(e){return request("".concat("/api/semantic/","knowledge/conf"),{method:"POST",data:e})}function O_(e){return request("".concat("/api/semantic/","knowledge/conf"),{method:"PUT",data:e})}function T_(e){return request("".concat("/api/semantic/","knowledge/task/delete"),{method:"PUT",data:e})}function w_(e){return request("".concat("/api/semantic/","knowledge/task/search"),{method:"POST",data:e})}function I_(e){return request("".concat("/api/semantic/","knowledge/conf/query"),{method:"POST",data:e})}var Ee=function(s){var p="hchatdata_".concat(moment().format("YYYYMMDDhhmmss"),".xlsx"),n=document.createElement("a");n.href=URL.createObjectURL(new Blob([s])),n.download=p,document.body.appendChild(n),n.click(),URL.revokeObjectURL(n.href),document.body.removeChild(n)};function N_(e){return request("".concat("/api/semantic/","dimension/queryDimValue"),{method:"POST",data:e})}function f_(e){return ie.apply(this,arguments)}function ie(){return ie=_asyncToGenerator(_regeneratorRuntime().mark(function e(s){var p,n,r,y,B,A,T,w,z,h,Y,K,I,N,$,R;return _regeneratorRuntime().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return p=s.domainId,n=s.dateField,r=n===void 0?"sys_imp_date":n,y=s.startDate,B=s.endDate,A=s.download,T=A===void 0?!1:A,w=s.period,z=w===void 0?DateRangeType.DAY:w,h=s.dimensionIds,Y=h===void 0?[]:h,K=s.metricIds,I=s.filters,N=I===void 0?[]:I,$=s.isTransform,d.next=3,request("".concat("/api/semantic/","query/").concat(T?"download/":"","metric"),_objectSpread(_objectSpread({method:"POST"},T?{responseType:"blob",getResponse:!0}:{}),{},{data:{domainId:p,filters:N,isTransform:$,metricIds:K,dimensionIds:Y,orders:[{column:r,direction:"desc"}],params:[],dateInfo:{dateMode:"BETWEEN",startDate:y,endDate:B,dateList:[],unit:7,groupByDate:!0,period:z,text:"null"},limit:2e3,nativeQuery:!1}}));case 3:if(R=d.sent,!T){d.next=8;break}Ee(R.data),d.next=9;break;case 8:return d.abrupt("return",R);case 9:case"end":return d.stop()}},e)})),ie.apply(this,arguments)}function j_(e){var s=e.id,p=e.state,n=e.type;return p?request("".concat("/api/semantic/","collect/createCollectionIndicators"),{method:"POST",data:{collectId:s,type:n}}):request("".concat("/api/semantic/","collect/deleteCollectionIndicators"),{method:"POST",data:{collectId:s,type:n}})}function U_(){return request.get("".concat("/api/semantic/","database/getDatabaseParameters"))}function M_(e){return request.get("".concat("/api/semantic/","database/").concat(e))}function L_(e){return request("".concat("/api/semantic/","dataSet/getDataSetList"),{method:"GET",params:{domainId:e}})}function F_(e){return request("".concat("/api/semantic/","dataSet/").concat(e),{method:"GET"})}function H_(e){return request("".concat("/api/semantic/","dataSet"),{method:"POST",data:e})}function W_(e){return request("".concat("/api/semantic/","dataSet"),{method:"PUT",data:e})}function q_(e){return request("".concat("/api/semantic/","dataSet/").concat(e),{method:"DELETE"})}function V_(e){return request("".concat("/api/semantic/","tag/queryTag/market"),{method:"POST",data:_objectSpread({pageSize:9999},e)})}function G_(e){return request("".concat("/api/semantic/","tag/delete/").concat(e),{method:"DELETE"})}function B_(e){return request.post("".concat("/api/semantic/","tag/batchUpdateStatus"),{data:e})}function z_(e){return request("".concat("/api/semantic/","tag/create"),{method:"POST",data:e})}function Y_(e){return request("".concat("/api/semantic/","tag/update"),{method:"POST",data:e})}function K_(e){return request("".concat("/api/semantic/","tag/getTag/").concat(e),{method:"GET"})}function $_(e){return request("".concat("/api/semantic/","tag/value/distribution"),{method:"POST",data:e})}function J_(e){return request("".concat("/api/semantic/","tag/create/batch"),{method:"POST",data:e})}function X_(e){return request("".concat("/api/semantic/","tag/delete/batch"),{method:"POST",data:e})}function Z_(e){return request("".concat("/api/semantic/","metric/batchPublish"),{method:"POST",data:e})}function Q_(e){return request("".concat("/api/semantic/","metric/batchUnPublish"),{method:"POST",data:e})}function es(e){return request("".concat("/api/semantic/","tagObject/create"),{method:"POST",data:e})}function _s(e){return request("".concat("/api/semantic/","tagObject/update"),{method:"POST",data:e})}function ss(e){return request("".concat("/api/semantic/","tagObject/delete/").concat(e),{method:"DELETE"})}function ns(e){return request("".concat("/api/semantic/","tagObject/query"),{method:"POST",data:_objectSpread({pageSize:9999,status:1},e)})}function ps(){return request("".concat("/api/semantic/","metric/getMetricClassifications"),{method:"GET"})}function as(e){return request("".concat("/api/semantic/","metric/batchUpdateClassifications"),{method:"POST",data:_objectSpread({},e)})}function ts(e){return request("".concat("/api/semantic/","dimension/batchUpdateSensitiveLevel"),{method:"POST",data:_objectSpread({},e)})}function cs(e){return request("".concat("/api/semantic/","metric/batchUpdateSensitiveLevel"),{method:"POST",data:_objectSpread({},e)})}function ms(e){return request("".concat("/api/semantic/","term"),{method:"GET",params:e})}function is(e){return request("".concat("/api/semantic/","term/saveOrUpdate"),{method:"POST",data:_objectSpread({},e)})}function ds(e){return request("".concat("/api/semantic/","term/deleteBatch"),{method:"POST",data:_objectSpread({},e)})}function rs(e){return request("".concat("/api/chat/","chat/model"),{method:"POST",data:_objectSpread({},e)})}function os(e){return e.id?request("".concat("/api/chat/","model"),{method:"PUT",data:e}):request("".concat("/api/chat/","model"),{method:"POST",data:e})}function ls(e){return request("".concat("/api/chat/","model/").concat(e),{method:"DELETE"})}var he=_(83568),Re=_(94202),Se=_(46803),be=_.n(Se),Oe=_(61314),Ce={projectBody:"projectBody___MclhD",projectManger:"projectManger___pVIWR",sider:"sider___D4ooJ",treeContainer:"treeContainer___QMQQm",siderCollapsed:"siderCollapsed___palz3",siderCollapsedButton:"siderCollapsedButton___K1hX_",domainTitle:"domainTitle___YLzC4",addBtn:"addBtn___jzvPd",treeTitle:"treeTitle___eBAnR",title:"title___jr4az",search:"search___odi57",projectItem:"projectItem___VuILn",projectItemTitle:"projectItemTitle___ELyAh",operation:"operation___VAVPU",icon:"icon___qa5k2",rowHover:"rowHover____tUe4",content:"content___TsOG9",collapseLeftBtn:"collapseLeftBtn___mYxMJ",navContainer:"navContainer___z_VzV",tab:"tab___bixKX",backBtn:"backBtn___jNiUX",chatSettingSectionTab:"chatSettingSectionTab___BVIF6",mainTip:"mainTip___H0Toq",domainList:"domainList___Uriia",searchContainer:"searchContainer___mmldI",disabledSearchTable:"disabledSearchTable___ye_Dd",classTable:"classTable___Csm5L",classTableSelectColumnAlignLeft:"classTableSelectColumnAlignLeft___xpjHh",permissionDrawer:"permissionDrawer___bCJAf",domainSelector:"domainSelector___QKGSn",infoTagList:"infoTagList___X9Bck",siteTagPlus:"siteTagPlus___DInBd",editTag:"editTag___R4ztu",tagInput:"tagInput___yiTA4",semanticGraphCanvas:"semanticGraphCanvas___zOupL",toolbar:"toolbar___Sib0y",canvasContainer:"canvasContainer___R3nPj",taskStateRefreshIcon:"taskStateRefreshIcon___CudAq",ctrlBtnContainer:"ctrlBtnContainer___OhrLe",breadcrumb:"breadcrumb___YFk2l",classDataSourceTypeModal:"classDataSourceTypeModal___tZaJ3",desc:"desc___tS7kG",markerTag:"markerTag___HFvvF",textLink:"textLink___D3QN8",tableHeaderTitle:"tableHeaderTitle___DmFWB",headerTitleLabel:"headerTitleLabel___qVjOI",infoCard:"infoCard___HmrFa",infoCardTitle:"infoCardTitle___gxtpt",infoCardContainer:"infoCardContainer___mzHFQ",infoCardFooter:"infoCardFooter___pYCXb",infoCardFooterContainer:"infoCardFooterContainer___tlsc8",dimensionValueFilterTable:"dimensionValueFilterTable___eflUF",toggleStatus:"toggleStatus___nbQr8",online:"online___Jyez0"},v=_(31549),Te=function(s){O()(s);var p=(0,a.useState)(!1),n=k()(p,2),r=n[0],y=n[1],B=(0,a.useState)(),A=k()(B,2),T=A[0],w=A[1],z=(0,a.useState)([]),h=k()(z,2),Y=h[0],K=h[1],I=(0,a.useState)(!1),N=k()(I,2),$=N[0],R=N[1],xe=(0,a.useRef)(),d=function(){var o=j()(t()().mark(function c(){var i,E,l,f;return t()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return R(!0),S.next=3,_e();case 3:i=S.sent,E=i.code,l=i.data,f=i.msg,E===200?K(l):x.ZP.error(f),R(!1);case 9:case"end":return S.stop()}},c)}));return function(){return o.apply(this,arguments)}}(),Ie=be().enc.Utf8.parse("hchatdata@2024"),Ne=function(){var o=j()(t()().mark(function c(i){var E,l,f,de;return t()().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return E=g()(g()({},i),{},{password:(0,Re.vC)(i.password,Ie)}),P.next=3,(0,he.N)(E);case 3:if(l=P.sent,f=l.code,de=l.msg,f!==200){P.next=13;break}return x.ZP.success("\u7528\u6237\u65B0\u589E\u6210\u529F"),y(!1),P.next=11,d();case 11:P.next=14;break;case 13:x.ZP.error(de);case 14:case"end":return P.stop()}},c)}));return function(i){return o.apply(this,arguments)}}(),fe=function(c){W(c).then(function(){d()})};(0,a.useEffect)(function(){d()},[]);var je=[{dataIndex:"id",title:"ID",width:80},{dataIndex:"name",title:"\u7528\u6237\u540D"},{dataIndex:"displayName",title:"\u6635\u79F0",search:!1},{dataIndex:"email",title:"\u90AE\u7BB1",search:!1},{dataIndex:"isAdmin",title:"\u662F\u5426\u662F\u7BA1\u7406\u5458",search:!1,render:function(c){return c===1?"\u662F":"\u5426"}},{dataIndex:"isDisabled",title:"\u72B6\u6001",search:!1,render:function(c,i){return(0,v.jsxs)("div",{className:Ce.toggleStatus,children:[i.isDisabled===1?"\u5DF2\u7981\u7528":(0,v.jsx)("span",{className:Ce.online,children:"\u5DF2\u542F\u7528"}),(0,v.jsx)("span",{onClick:function(l){l.stopPropagation()},children:(0,v.jsx)(Q.Z,{size:"small",defaultChecked:i.isDisabled===0,onChange:function(l){fe(g()(g()({},i),{},{isDisabled:l?0:1}))}},i.id)})]})}},{dataIndex:"lastLogin",title:"\u6700\u540E\u767B\u5F55\u65F6\u95F4",search:!1,render:function(c){return c&&c!=="-"?ee()(c).format("YYYY-MM-DD HH:mm:ss"):"-"}}];return(0,v.jsxs)("div",{style:{margin:20},children:[(0,v.jsx)(Z.Z,{actionRef:xe,rowKey:"id",loading:$,columns:je,dataSource:Y,search:!1,tableAlertRender:function(){return!1},size:"small",options:{reload:!1,density:!1,fullScreen:!1},toolBarRender:function(){return[(0,v.jsx)(M.ZP,{type:"primary",onClick:function(){w(void 0),y(!0)},children:"\u521B\u5EFA\u7528\u6237"},"create")]}}),(0,v.jsx)(Oe.Z,{onCancel:function(){y(!1)},onSubmit:Ne,createModalVisible:r})]})},we=Te},49841:function(D){function u(_){if(_==null)throw new TypeError("Cannot destructure "+_)}D.exports=u,D.exports.__esModule=!0,D.exports.default=D.exports}}]);
