!(function(){"use strict";var st=Object.defineProperty;var k=Object.getOwnPropertySymbols;var ot=Object.prototype.hasOwnProperty,it=Object.prototype.propertyIsEnumerable;var I=(x,h,c)=>h in x?st(x,h,{enumerable:!0,configurable:!0,writable:!0,value:c}):x[h]=c,T=(x,h)=>{for(var c in h||(h={}))ot.call(h,c)&&I(x,c,h[c]);if(k)for(var c of k(h))it.call(h,c)&&I(x,c,h[c]);return x};var S=(x,h,c)=>new Promise((A,$)=>{var _=d=>{try{y(c.next(d))}catch(f){$(f)}},r=d=>{try{y(c.throw(d))}catch(f){$(f)}},y=d=>d.done?A(d.value):Promise.resolve(d.value).then(_,r);y((c=c.apply(x,h)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3233],{96547:function(x,h,c){c.d(h,{A:function(){return $}});var A=c(29134);function $(_,r){var y,d,f;_.accDescr&&((y=r.setAccDescription)==null||y.call(r,_.accDescr)),_.accTitle&&((d=r.setAccTitle)==null||d.call(r,_.accTitle)),_.title&&((f=r.setDiagramTitle)==null||f.call(r,_.title))}(0,A.eW)($,"populateCommonDb")},73233:function(x,h,c){c.d(h,{diagram:function(){return rt}});var A=c(96547),$=c(50854),_=c(44133),r=c(29134),y=c(38663),d={showLegend:!0,ticks:5,max:null,min:0,graticule:"circle"},f={axes:[],curves:[],options:d},M=structuredClone(f),B=r.vZ.radar,F=(0,r.eW)(()=>(0,$.Rb)(T(T({},B),(0,r.iE)().radar)),"getConfig"),O=(0,r.eW)(()=>M.axes,"getAxes"),j=(0,r.eW)(()=>M.curves,"getCurves"),G=(0,r.eW)(()=>M.options,"getOptions"),U=(0,r.eW)(a=>{M.axes=a.map(t=>{var e;return{name:t.name,label:(e=t.label)!=null?e:t.name}})},"setAxes"),K=(0,r.eW)(a=>{M.curves=a.map(t=>{var e;return{name:t.name,label:(e=t.label)!=null?e:t.name,entries:z(t.entries)}})},"setCurves"),z=(0,r.eW)(a=>{if(a[0].axis==null)return a.map(e=>e.value);const t=O();if(t.length===0)throw new Error("Axes must be populated before curves for reference entries");return t.map(e=>{const n=a.find(s=>{var i;return((i=s.axis)==null?void 0:i.$refText)===e.name});if(n===void 0)throw new Error("Missing entry for axis "+e.label);return n.value})},"computeCurveEntries"),H=(0,r.eW)(a=>{var e,n,s,i,l,o,u,p,g,m;const t=a.reduce((v,C)=>(v[C.name]=C,v),{});M.options={showLegend:(n=(e=t.showLegend)==null?void 0:e.value)!=null?n:d.showLegend,ticks:(i=(s=t.ticks)==null?void 0:s.value)!=null?i:d.ticks,max:(o=(l=t.max)==null?void 0:l.value)!=null?o:d.max,min:(p=(u=t.min)==null?void 0:u.value)!=null?p:d.min,graticule:(m=(g=t.graticule)==null?void 0:g.value)!=null?m:d.graticule}},"setOptions"),V=(0,r.eW)(()=>{(0,r.ZH)(),M=structuredClone(f)},"clear"),W={getAxes:O,getCurves:j,getOptions:G,setAxes:U,setCurves:K,setOptions:H,getConfig:F,clear:V,setAccTitle:r.GN,getAccTitle:r.eu,setDiagramTitle:r.g2,getDiagramTitle:r.Kr,getAccDescription:r.Mx,setAccDescription:r.U$},N=(0,r.eW)(a=>{(0,A.A)(a,W);const{axes:t,curves:e,options:n}=a;W.setAxes(t),W.setCurves(e),W.setOptions(n)},"populate"),X={parse:(0,r.eW)(a=>S(this,null,function*(){const t=yield(0,y.Qc)("radar",a);r.cM.debug(t),N(t)}),"parse")},Y=(0,r.eW)((a,t,e,n)=>{var L;const s=n.db,i=s.getAxes(),l=s.getCurves(),o=s.getOptions(),u=s.getConfig(),p=s.getDiagramTitle(),g=(0,_.P)(t),m=Z(g,u),v=(L=o.max)!=null?L:Math.max(...l.map(E=>Math.max(...E.entries))),C=o.min,w=Math.min(u.width,u.height)/2;Q(m,i,w,o.ticks,o.graticule),J(m,i,w,u),D(m,i,l,C,v,o.graticule,u),b(m,l,o.showLegend,u),m.append("text").attr("class","radarTitle").text(p).attr("x",0).attr("y",-u.height/2-u.marginTop)},"draw"),Z=(0,r.eW)((a,t)=>{const e=t.width+t.marginLeft+t.marginRight,n=t.height+t.marginTop+t.marginBottom,s={x:t.marginLeft+t.width/2,y:t.marginTop+t.height/2};return a.attr("viewbox",`0 0 ${e} ${n}`).attr("width",e).attr("height",n),a.append("g").attr("transform",`translate(${s.x}, ${s.y})`)},"drawFrame"),Q=(0,r.eW)((a,t,e,n,s)=>{if(s==="circle")for(let i=0;i<n;i++){const l=e*(i+1)/n;a.append("circle").attr("r",l).attr("class","radarGraticule")}else if(s==="polygon"){const i=t.length;for(let l=0;l<n;l++){const o=e*(l+1)/n,u=t.map((p,g)=>{const m=2*g*Math.PI/i-Math.PI/2,v=o*Math.cos(m),C=o*Math.sin(m);return`${v},${C}`}).join(" ");a.append("polygon").attr("points",u).attr("class","radarGraticule")}}},"drawGraticule"),J=(0,r.eW)((a,t,e,n)=>{const s=t.length;for(let i=0;i<s;i++){const l=t[i].label,o=2*i*Math.PI/s-Math.PI/2;a.append("line").attr("x1",0).attr("y1",0).attr("x2",e*n.axisScaleFactor*Math.cos(o)).attr("y2",e*n.axisScaleFactor*Math.sin(o)).attr("class","radarAxisLine"),a.append("text").text(l).attr("x",e*n.axisLabelFactor*Math.cos(o)).attr("y",e*n.axisLabelFactor*Math.sin(o)).attr("class","radarAxisLabel")}},"drawAxes");function D(a,t,e,n,s,i,l){const o=t.length,u=Math.min(l.width,l.height)/2;e.forEach((p,g)=>{if(p.entries.length!==o)return;const m=p.entries.map((v,C)=>{const w=2*Math.PI*C/o-Math.PI/2,L=P(v,n,s,u),E=L*Math.cos(w),nt=L*Math.sin(w);return{x:E,y:nt}});i==="circle"?a.append("path").attr("d",R(m,l.curveTension)).attr("class",`radarCurve-${g}`):i==="polygon"&&a.append("polygon").attr("points",m.map(v=>`${v.x},${v.y}`).join(" ")).attr("class",`radarCurve-${g}`)})}(0,r.eW)(D,"drawCurves");function P(a,t,e,n){const s=Math.min(Math.max(a,t),e);return n*(s-t)/(e-t)}(0,r.eW)(P,"relativeRadius");function R(a,t){const e=a.length;let n=`M${a[0].x},${a[0].y}`;for(let s=0;s<e;s++){const i=a[(s-1+e)%e],l=a[s],o=a[(s+1)%e],u=a[(s+2)%e],p={x:l.x+(o.x-i.x)*t,y:l.y+(o.y-i.y)*t},g={x:o.x-(u.x-l.x)*t,y:o.y-(u.y-l.y)*t};n+=` C${p.x},${p.y} ${g.x},${g.y} ${o.x},${o.y}`}return`${n} Z`}(0,r.eW)(R,"closedRoundCurve");function b(a,t,e,n){if(!e)return;const s=(n.width/2+n.marginRight)*3/4,i=-(n.height/2+n.marginTop)*3/4,l=20;t.forEach((o,u)=>{const p=a.append("g").attr("transform",`translate(${s}, ${i+u*l})`);p.append("rect").attr("width",12).attr("height",12).attr("class",`radarLegendBox-${u}`),p.append("text").attr("x",16).attr("y",0).attr("class","radarLegendText").text(o.label)})}(0,r.eW)(b,"drawLegend");var q={draw:Y},tt=(0,r.eW)((a,t)=>{let e="";for(let n=0;n<a.THEME_COLOR_LIMIT;n++){const s=a[`cScale${n}`];e+=`
		.radarCurve-${n} {
			color: ${s};
			fill: ${s};
			fill-opacity: ${t.curveOpacity};
			stroke: ${s};
			stroke-width: ${t.curveStrokeWidth};
		}
		.radarLegendBox-${n} {
			fill: ${s};
			fill-opacity: ${t.curveOpacity};
			stroke: ${s};
		}
		`}return e},"genIndexStyles"),et=(0,r.eW)(a=>{const t=(0,r.xN)(),e=(0,r.iE)(),n=(0,$.Rb)(t,e.themeVariables),s=(0,$.Rb)(n.radar,a);return{themeVariables:n,radarOptions:s}},"buildRadarStyleOptions"),at=(0,r.eW)(({radar:a}={})=>{const{themeVariables:t,radarOptions:e}=et(a);return`
	.radarTitle {
		font-size: ${t.fontSize};
		color: ${t.titleColor};
		dominant-baseline: hanging;
		text-anchor: middle;
	}
	.radarAxisLine {
		stroke: ${e.axisColor};
		stroke-width: ${e.axisStrokeWidth};
	}
	.radarAxisLabel {
		dominant-baseline: middle;
		text-anchor: middle;
		font-size: ${e.axisLabelFontSize}px;
		color: ${e.axisColor};
	}
	.radarGraticule {
		fill: ${e.graticuleColor};
		fill-opacity: ${e.graticuleOpacity};
		stroke: ${e.graticuleColor};
		stroke-width: ${e.graticuleStrokeWidth};
	}
	.radarLegendText {
		text-anchor: start;
		font-size: ${e.legendFontSize}px;
		dominant-baseline: hanging;
	}
	${tt(t,e)}
	`},"styles"),rt={parser:X,db:W,renderer:q,styles:at}}}]);
}());