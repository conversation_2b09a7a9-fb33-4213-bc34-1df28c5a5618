{"cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "pkg": {"name": "hchatdata-fe", "version": "0.1.0", "private": true, "description": "data chat", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "npm run build:os", "build:os": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build", "build:os-local": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "build:inner": "cross-env REACT_APP_ENV=prod APP_TARGET=inner max build", "build:test": "cross-env REACT_APP_ENV=test max build", "deploy": "npm run site && npm run gh-pages", "dev": "npm run start:osdev", "dev:os": "npm run start:osdev", "dev:inner": "npm run start:dev", "no:dev:os": "NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev", "no:dev:inner": "NODE_OPTIONS=--openssl-legacy-provider npm run start:dev", "no:build:inner": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "lint": "max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "start": "npm run start:osdev", "start:dev": "cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev", "start:osdev": "cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev", "start:no-mock": "cross-env MOCK=none max dev", "start:no-ui": "cross-env UMI_UI=none max dev", "start:pre": "cross-env REACT_APP_ENV=pre max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none max dev", "pretest": "node ./tests/beforeTest", "test": "max test", "test:all": "node ./tests/run-tests.js", "test:component": "max test ./src/components", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "2.7.0", "@antv/dom-util": "^2.0.4", "@antv/g6": "^4.8.23", "@antv/g6-core": "^0.8.23", "@antv/layout": "^0.3.20", "@antv/x6": "1.30.1", "@babel/runtime": "^7.22.5", "@types/numeral": "^2.0.2", "@types/react-draft-wysiwyg": "^1.13.2", "@types/react-syntax-highlighter": "^13.5.0", "@umijs/route-utils": "2.2.2", "ace-builds": "^1.4.12", "ahooks": "^3.7.7", "antd": "^5.17.4", "classnames": "^2.2.6", "compression-webpack-plugin": "^11.0.0", "copy-to-clipboard": "^3.3.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.0.2", "echarts-for-react": "^3.0.1", "eslint-config-tencent": "^1.0.4", "hchatdata-chat-sdk": "workspace:*", "jsencrypt": "^3.0.1", "lodash": "^4.17.11", "moment": "^2.29.1", "nprogress": "^0.2.0", "numeral": "^2.0.6", "omit.js": "^2.0.2", "path-to-regexp": "^2.4.0", "qs": "^6.9.0", "query-string": "^9.0.0", "react": "^18.3.1", "react-ace": "^9.4.1", "react-dom": "^18.3.1", "react-spinners": "^0.13.8", "react-split-pane": "^2.0.3", "react-syntax-highlighter": "^15.4.3", "sql-formatter": "^15.6.1", "supersonic-insights-flow-components": "^1.4.9", "umi-request": "1.4.0"}, "devDependencies": {"@ant-design/pro-cli": "^2.0.2", "@types/classnames": "^2.2.7", "@types/crypto-js": "^4.0.1", "@types/draftjs-to-html": "^0.8.0", "@types/echarts": "^4.9.4", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/pinyin": "^2.8.3", "@types/qs": "^6.5.3", "@types/react": "18.3.1", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^4.0.1", "@umijs/max": "^4.2.5", "@umijs/plugin-model": "^2.6.2", "carlo": "^0.9.46", "compression": "^1.8.1", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "eslint": "^7.1.0", "eslint-plugin-chalk": "^1.0.0", "eslint-plugin-import": "^2.27.5", "express": "^4.21.2", "gh-pages": "^3.0.0", "http-proxy-middleware": "^2.0.6", "jsdom-global": "^3.0.2", "lint-staged": "^10.0.0", "prettier": "^2.3.1", "pro-download": "1.0.1", "puppeteer-core": "^5.0.0", "stylelint": "^13.0.0", "typescript": "^4.0.3", "umi-presets-pro": "2.0.2"}, "engines": {"node": ">=16.0.0"}, "__npminstall_done": false, "packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee"}, "pkgPath": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\package.json", "plugins": {"@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 79}, "enableBy": "register", "type": "preset", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi", "key": "umi"}, "@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 29}, "enableBy": "register", "type": "preset", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu/node_modules/@umijs/max/dist/preset.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/max/dist/preset", "key": "preset"}, "umi-presets-pro": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 14}, "enableBy": "register", "type": "preset", "path": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/node_modules/umi-presets-pro/dist/index.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "umi-presets-pro", "key": "umiPresetsPro"}, "@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [3]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+did-you-know@1.0.3/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/404/404", "key": "404"}, "@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [558]}, "register": 48}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/check/check", "key": "check"}, "@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 32}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react", "react-dom": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react-dom", "react-router": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\react-router@6.3.0_react@18.3.1\\node_modules\\react-router", "react-router-dom": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\react-router-dom@6.3.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 120}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 267}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 113}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 63}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [4]}, "register": 7}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 15}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 11}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 15}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 23}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 39}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/test/test", "key": "test"}, "@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 9}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 7}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 20}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/build", "key": "build"}, "@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 78}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {}, "register": 67}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/help", "key": "help"}, "@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/version", "key": "version"}, "@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 38}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+preset-umi@4.4.11_@types+node@22.15.21_@types+react@18.3.1_lightningcss@1.30.1_rollup@_fgnzoo7fzjug3hyslrjolnwef4/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 15}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugin-run@4.4.11/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugin-run", "key": "run"}, "@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/access.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/access", "key": "access"}, "@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/analytics", "key": "analytics"}, "@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {"modifyConfig": [9], "modifyAppData": [1]}, "register": 14}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/antd.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/antd", "key": "antd"}, "@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 218}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/dva.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/dva", "key": "dva"}, "@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/initial-state", "key": "initialState"}, "@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {"modifyConfig": [0], "addLayouts": [0], "modifyAppData": [2]}, "register": 7}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/layout.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/layout", "key": "layout"}, "@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/locale.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/locale", "key": "locale"}, "@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/mf.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/mf", "key": "mf"}, "@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/model.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/model", "key": "model"}, "@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/qiankun", "key": "qiankun"}, "@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {}, "register": 5}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/react-query", "key": "reactQuery"}, "@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/request.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/request", "key": "request"}, "@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+plugins@4.4.11_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_iew536spvk43qifmceeuhgkg4m/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/plugins/dist/valtio", "key": "valtio"}, "@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "umi-presets-pro/dist/features/proconfig": {"config": {}, "time": {"hooks": {"modifyConfig": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/umi-presets-pro@2.0.2_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_rrt5cerolo6vu7da3v235fmrvm/node_modules/umi-presets-pro/dist/features/proconfig.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "umi-presets-pro/dist/features/proconfig", "key": "proconfig"}, "umi-presets-pro/dist/features/maxtabs": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/umi-presets-pro@2.0.2_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+_rrt5cerolo6vu7da3v235fmrvm/node_modules/umi-presets-pro/dist/features/maxtabs.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "umi-presets-pro/dist/features/maxtabs", "key": "maxtabs"}, "@umijs/max-plugin-openapi": {"config": {}, "time": {"hooks": {}, "register": 428}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+max-plugin-openapi@2.0.2_chokidar@3.6.0_encoding@0.1.13_typescript@4.9.5/node_modules/@umijs/max-plugin-openapi/dist/index.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/max-plugin-openapi", "key": "openAPI"}, "@alita/plugins/dist/keepalive": {"config": {}, "time": {"hooks": {}, "register": 73}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@alita+plugins@3.5.4_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+r_xjyezr4jqj2sp7sjtfcwklpdai/node_modules/@alita/plugins/dist/keepalive.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@alita/plugins/dist/keepalive", "key": "keepalive"}, "@alita/plugins/dist/tabs-layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@alita+plugins@3.5.4_@babel+core@7.27.1_@types+react-dom@18.3.7_@types+react@18.3.1__@types+r_xjyezr4jqj2sp7sjtfcwklpdai/node_modules/@alita/plugins/dist/tabs-layout.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@alita/plugins/dist/tabs-layout", "key": "tabsLayout"}, "@umijs/request-record": {"config": {"default": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}}, "time": {"hooks": {}, "register": 103}, "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+request-record@1.1.4_umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@1_asintjdctlndgelhi6go2ubfk4/node_modules/@umijs/request-record/dist/cjs/index.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/request-record", "key": "requestRecord"}, "@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "D:/项目/xx/HChatData/webapp/node_modules/.pnpm/@umijs+core@4.4.11/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "id": "@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "build", "args": {"_": []}, "userConfig": {"favicons": ["/favicon.ico"], "define": {"process.env": {"AISE_MINIO_API_PORT": "19000", "AISE_MYSQL_PORT": "13306", "AISE_NACOS_PORT": "18848", "AISE_REDIS_PORT": "16379", "ALLUSERSPROFILE": "C:\\ProgramData", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "APP_TARGET": "opensource", "asl.log": "Destination=file", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "ISS", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\issuser", "INIT_CWD": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\ISS", "major_version": "18", "NODE": "E:\\node\\node.exe", "NODE_ENV": "production", "NODE_OPTIONS": "--openssl-legacy-provider", "NODE_PATH": "D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\bin\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\bin\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules", "node_version": "v20.18.1", "npm_command": "run-script", "npm_config_cache": "E:\\nvm\\nvm\\node_cache", "npm_config_frozen_lockfile": "", "npm_config_node_gyp": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_prefix": "E:\\nvm\\nvm\\node_global", "npm_config_registry": "https://registry.npmmirror.com", "npm_config_user_agent": "pnpm/9.12.3 npm/? node/v20.18.1 win32 x64", "npm_config__tiptap_pro_registry": "https://registry.tiptap.dev/", "npm_execpath": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\bin\\pnpm.cjs", "npm_lifecycle_event": "build:os-local", "npm_lifecycle_script": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "npm_node_execpath": "E:\\node\\node.exe", "npm_package_browserslist_0": "> 1%", "npm_package_browserslist_1": "last 2 versions", "npm_package_browserslist_2": "not ie <= 10", "npm_package_dependencies_ace_builds": "^1.4.12", "npm_package_dependencies_ahooks": "^3.7.7", "npm_package_dependencies_antd": "^5.17.4", "npm_package_dependencies_classnames": "^2.2.6", "npm_package_dependencies_compression_webpack_plugin": "^11.0.0", "npm_package_dependencies_copy_to_clipboard": "^3.3.1", "npm_package_dependencies_cross_env": "^7.0.3", "npm_package_dependencies_crypto_js": "^4.2.0", "npm_package_dependencies_dayjs": "^1.11.10", "npm_package_dependencies_echarts": "^5.0.2", "npm_package_dependencies_echarts_for_react": "^3.0.1", "npm_package_dependencies_eslint_config_tencent": "^1.0.4", "npm_package_dependencies_hchatdata_chat_sdk": "workspace:*", "npm_package_dependencies_jsencrypt": "^3.0.1", "npm_package_dependencies_lodash": "^4.17.11", "npm_package_dependencies_moment": "^2.29.1", "npm_package_dependencies_nprogress": "^0.2.0", "npm_package_dependencies_numeral": "^2.0.6", "npm_package_dependencies_omit_js": "^2.0.2", "npm_package_dependencies_path_to_regexp": "^2.4.0", "npm_package_dependencies_qs": "^6.9.0", "npm_package_dependencies_query_string": "^9.0.0", "npm_package_dependencies_react": "^18.3.1", "npm_package_dependencies_react_ace": "^9.4.1", "npm_package_dependencies_react_dom": "^18.3.1", "npm_package_dependencies_react_spinners": "^0.13.8", "npm_package_dependencies_react_split_pane": "^2.0.3", "npm_package_dependencies_react_syntax_highlighter": "^15.4.3", "npm_package_dependencies_sql_formatter": "^15.6.1", "npm_package_dependencies_supersonic_insights_flow_components": "^1.4.9", "npm_package_dependencies_umi_request": "1.4.0", "npm_package_dependencies__antv_dom_util": "^2.0.4", "npm_package_dependencies__antv_g6": "^4.8.23", "npm_package_dependencies__antv_g6_core": "^0.8.23", "npm_package_dependencies__antv_layout": "^0.3.20", "npm_package_dependencies__antv_x6": "1.30.1", "npm_package_dependencies__ant_design_icons": "^5.2.6", "npm_package_dependencies__ant_design_pro_components": "2.7.0", "npm_package_dependencies__babel_runtime": "^7.22.5", "npm_package_dependencies__types_numeral": "^2.0.2", "npm_package_dependencies__types_react_draft_wysiwyg": "^1.13.2", "npm_package_dependencies__types_react_syntax_highlighter": "^13.5.0", "npm_package_dependencies__umijs_route_utils": "2.2.2", "npm_package_description": "data chat", "npm_package_devDependencies_carlo": "^0.9.46", "npm_package_devDependencies_compression": "^1.8.1", "npm_package_devDependencies_cross_port_killer": "^1.1.1", "npm_package_devDependencies_detect_installer": "^1.0.1", "npm_package_devDependencies_eslint": "^7.1.0", "npm_package_devDependencies_eslint_plugin_chalk": "^1.0.0", "npm_package_devDependencies_eslint_plugin_import": "^2.27.5", "npm_package_devDependencies_express": "^4.21.2", "npm_package_devDependencies_gh_pages": "^3.0.0", "npm_package_devDependencies_http_proxy_middleware": "^2.0.6", "npm_package_devDependencies_jsdom_global": "^3.0.2", "npm_package_devDependencies_lint_staged": "^10.0.0", "npm_package_devDependencies_prettier": "^2.3.1", "npm_package_devDependencies_pro_download": "1.0.1", "npm_package_devDependencies_puppeteer_core": "^5.0.0", "npm_package_devDependencies_stylelint": "^13.0.0", "npm_package_devDependencies_typescript": "^4.0.3", "npm_package_devDependencies_umi_presets_pro": "2.0.2", "npm_package_devDependencies__ant_design_pro_cli": "^2.0.2", "npm_package_devDependencies__types_classnames": "^2.2.7", "npm_package_devDependencies__types_crypto_js": "^4.0.1", "npm_package_devDependencies__types_draftjs_to_html": "^0.8.0", "npm_package_devDependencies__types_echarts": "^4.9.4", "npm_package_devDependencies__types_express": "^4.17.0", "npm_package_devDependencies__types_history": "^4.7.2", "npm_package_devDependencies__types_jest": "^26.0.0", "npm_package_devDependencies__types_lodash": "^4.14.144", "npm_package_devDependencies__types_pinyin": "^2.8.3", "npm_package_devDependencies__types_qs": "^6.5.3", "npm_package_devDependencies__types_react": "18.3.1", "npm_package_devDependencies__types_react_dom": "^18.3.0", "npm_package_devDependencies__types_react_helmet": "^6.1.0", "npm_package_devDependencies__umijs_fabric": "^4.0.1", "npm_package_devDependencies__umijs_max": "^4.2.5", "npm_package_devDependencies__umijs_plugin_model": "^2.6.2", "npm_package_engines_node": ">=16.0.0", "npm_package_lint_staged______less": "stylelint --syntax less", "npm_package_lint_staged_______js_jsx_tsx_ts_less_md_json__0": "prettier --write", "npm_package_lint_staged_______js_jsx_ts_tsx_": "npm run lint-staged:js", "npm_package_name": "hchatdata-fe", "npm_package_packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee", "npm_package_private": "true", "npm_package_scripts_analyze": "cross-env ANALYZE=1 max build", "npm_package_scripts_build": "npm run build:os", "npm_package_scripts_build_inner": "cross-env REACT_APP_ENV=prod APP_TARGET=inner max build", "npm_package_scripts_build_os": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build", "npm_package_scripts_build_os_local": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "npm_package_scripts_build_test": "cross-env REACT_APP_ENV=test max build", "npm_package_scripts_deploy": "npm run site && npm run gh-pages", "npm_package_scripts_dev": "npm run start:osdev", "npm_package_scripts_dev_inner": "npm run start:dev", "npm_package_scripts_dev_os": "npm run start:osdev", "npm_package_scripts_gh_pages": "gh-pages -d dist", "npm_package_scripts_i18n_remove": "pro i18n-remove --locale=zh-CN --write", "npm_package_scripts_lint": "max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "npm_package_scripts_lint_fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "npm_package_scripts_lint_js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "npm_package_scripts_lint_prettier": "prettier --check \"src/**/*\" --end-of-line auto", "npm_package_scripts_lint_staged": "lint-staged", "npm_package_scripts_lint_staged_js": "eslint --ext .js,.jsx,.ts,.tsx ", "npm_package_scripts_lint_style": "stylelint --fix \"src/**/*.less\" --syntax less", "npm_package_scripts_no_build_inner": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build", "npm_package_scripts_no_dev_inner": "NODE_OPTIONS=--openssl-legacy-provider npm run start:dev", "npm_package_scripts_no_dev_os": "NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev", "npm_package_scripts_postinstall": "max setup", "npm_package_scripts_precommit": "lint-staged", "npm_package_scripts_pretest": "node ./tests/beforeTest", "npm_package_scripts_prettier": "prettier -c --write \"src/**/*\"", "npm_package_scripts_start": "npm run start:osdev", "npm_package_scripts_start_dev": "cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev", "npm_package_scripts_start_no_mock": "cross-env MOCK=none max dev", "npm_package_scripts_start_no_ui": "cross-env UMI_UI=none max dev", "npm_package_scripts_start_osdev": "cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev", "npm_package_scripts_start_pre": "cross-env REACT_APP_ENV=pre max dev", "npm_package_scripts_start_test": "cross-env REACT_APP_ENV=test MOCK=none max dev", "npm_package_scripts_test": "max test", "npm_package_scripts_test_all": "node ./tests/run-tests.js", "npm_package_scripts_test_component": "max test ./src/components", "npm_package_scripts_tsc": "tsc --noEmit", "npm_package_version": "0.1.0", "NUMBER_OF_PROCESSORS": "8", "NVM_HOME": "E:\\nvm\\nvm", "NVM_NODE_GLOBAL": "E:\\nvm\\nvm\\node_global", "NVM_SYMLINK": "E:\\node", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node-gyp-bin;D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;E:\\nvm\\nvm;E:\\node;C:\\Program Files\\CorpLink\\current\\module\\mdm\\x64\\policy\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\python311\\Scripts\\;C:\\python311\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\nvm\\nvm;E:\\node;D:\\软通\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC", "PNPM_SCRIPT_SRC_DIR": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 142 Stepping 12, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "8e0c", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "REACT_APP_ENV": "prod", "RUN_TYPE": "local", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SystemDrive": "C:", "SystemRoot": "C:\\Windows", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "UMI_DIR": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq\\node_modules\\umi", "UMI_PRESETS": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\dist\\preset.js", "USERDOMAIN": "ISS", "USERDOMAIN_ROAMINGPROFILE": "ISS", "USERNAME": "issuser", "USERPROFILE": "C:\\Users\\<USER>", "windir": "C:\\Windows", "API_BASE_URL": "/api/semantic/", "CHAT_API_BASE_URL": "/api/chat/", "AUTH_API_BASE_URL": "/api/auth/", "SHOW_TAG": false, "tmeAvatarUrl": ""}}, "metas": [{"name": "app_version", "content": "2025-07-31 17:04:33"}], "hash": true, "routes": [{"path": "/chat/mobile", "name": "chat", "component": "@/pages/ChatPage", "hideInMenu": true, "layout": false, "envEnableList": ["chat"]}, {"path": "/chat/external", "name": "chat", "component": "@/pages/ChatPage", "hideInMenu": true, "layout": false, "envEnableList": ["chat"]}, {"path": "/chat", "name": "chat", "component": "@/pages/ChatPage", "envEnableList": ["chat"]}, {"path": "/agent", "name": "agent", "component": "@/pages/Agent", "envEnableList": ["chat"], "access": "ADMIN"}, {"path": "/plugin", "name": "plugin", "component": "@/pages/ChatPlugin", "envEnableList": ["chat"], "access": "ADMIN"}, {"path": "/model/metric/edit/:metricId", "name": "metricEdit", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}, {"path": "/model/", "component": "@/pages/SemanticModel", "name": "semanticModel", "envEnableList": ["semantic"], "access": "ADMIN", "routes": [{"path": "/model/", "redirect": "/model/domain"}, {"path": "/model/domain/", "component": "@/pages/SemanticModel/OverviewContainer", "access": "ADMIN", "routes": [{"path": "/model/domain/:domainId", "component": "@/pages/SemanticModel/DomainManager", "routes": [{"path": "/model/domain/:domainId/:menuKey", "component": "@/pages/SemanticModel/DomainManager"}]}, {"path": "/model/domain/manager/:domainId/:modelId", "component": "@/pages/SemanticModel/ModelManager", "routes": [{"path": "/model/domain/manager/:domainId/:modelId/:menuKey", "component": "@/pages/SemanticModel/ModelManager"}]}]}, {"path": "/model/dataset/:domainId/:datasetId", "component": "@/pages/SemanticModel/View/components/Detail", "envEnableList": ["semantic"], "routes": [{"path": "/model/dataset/:domainId/:datasetId/:menuKey", "component": "@/pages/SemanticModel/View/components/Detail"}]}, {"path": "/model/metric/:domainId/:modelId/:metricId", "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}, {"path": "/model/dimension/:domainId/:modelId/:dimensionId", "component": "@/pages/SemanticModel/Dimension/Detail", "envEnableList": ["semantic"]}]}, {"path": "/metric", "name": "metric", "component": "@/pages/SemanticModel/Metric", "envEnableList": ["semantic"], "access": "ADMIN", "routes": [{"path": "/metric", "redirect": "/metric/market"}, {"path": "/metric/market", "component": "@/pages/SemanticModel/Metric/Market", "access": "ADMIN", "hideInMenu": true, "envEnableList": ["semantic"]}, {"path": "/metric/detail/:metricId", "name": "metricDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Detail", "envEnableList": ["semantic"]}, {"path": "/metric/detail/edit/:metricId", "name": "metricDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}]}, {"path": "/tag", "name": "tag", "component": "@/pages/SemanticModel/Insights", "envEnableList": ["semantic"], "hideInMenu": true, "routes": [{"path": "/tag", "redirect": "/tag/market"}, {"path": "/tag/market", "component": "@/pages/SemanticModel/Insights/Market", "hideInMenu": true, "envEnableList": ["semantic"]}, {"path": "/tag/detail/:tagId", "name": "tagDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Insights/Detail", "envEnableList": ["semantic"]}]}, {"path": "/login", "name": "login", "layout": false, "hideInMenu": true, "component": "./Login"}, {"path": "/database", "name": "database", "component": "@/pages/SemanticModel/components/Database/DatabaseTable", "envEnableList": ["semantic"], "access": "ADMIN"}, {"path": "/llm", "name": "llm", "component": "@/pages/SemanticModel/components/LLM/LlmTable", "envEnableList": ["semantic"], "access": "ADMIN"}, {"path": "/system", "name": "system", "component": "@/pages/System", "access": "SYSTEM_ADMIN"}, {"path": "/user", "name": "user", "component": "@/pages/User/UserTable", "access": "ADMIN"}, {"path": "/", "redirect": "/chat"}, {"path": "/401", "component": "@/pages/401"}], "theme": {"root-entry-name": "variable", "primary-color": "#cc0000"}, "ignoreMomentLocale": true, "base": "/webapp/", "publicPath": "/webapp/", "outputPath": "hchatdata-webapp", "fastRefresh": true, "model": {}, "initialState": {}, "title": "AIData", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#0057ff", "layout": "top", "contentWidth": "Fluid", "fixedHeader": true, "fixSiderbar": true, "colorWeak": false, "title": "", "pwa": false, "iconfontUrl": "//at.alicdn.com/t/c/font_4120566_x5c4www9bqm.js"}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {}, "request": {}, "access": {}, "headScripts": [{"src": "/webapp/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "mfsu": false, "requestRecord": {}, "exportStatic": {}, "alias": {"hchatdata-chat-sdk": "D:\\项目\\xx\\HChatData\\webapp\\packages\\chat-sdk\\src", "genie-ui": "D:\\项目\\xx\\HChatData\\webapp\\packages\\genie-ui"}, "esbuildMinifyIIFE": true, "extraPostCSSPlugins": [null, null]}, "mainConfigFile": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\config\\config.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/webapp/", "svgr": {}, "publicPath": "/webapp/", "mfsu": false, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react", "react-dom": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react-dom", "react-router": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\react-router@6.3.0_react@18.3.1\\node_modules\\react-router", "react-router-dom": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\react-router-dom@6.3.0_react-dom@18.3.1_react@18.3.1__react@18.3.1\\node_modules\\react-router-dom", "hchatdata-chat-sdk": "D:\\项目\\xx\\HChatData\\webapp\\packages\\chat-sdk\\src", "genie-ui": "D:\\项目\\xx\\HChatData\\webapp\\packages\\genie-ui", "@": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src", "@@": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production", "regenerator-runtime": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\regenerator-runtime@0.13.11\\node_modules\\regenerator-runtime", "antd": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\antd", "@umijs/max": "@@/exports"}, "requestRecord": {"mock": {"outputDir": "./mock", "fileName": "requestRecord.mock.js", "usingRole": "default"}, "outputDir": "./types"}, "favicons": ["/favicon.ico"], "define": {"process.env": {"AISE_MINIO_API_PORT": "19000", "AISE_MYSQL_PORT": "13306", "AISE_NACOS_PORT": "18848", "AISE_REDIS_PORT": "16379", "ALLUSERSPROFILE": "C:\\ProgramData", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "APP_TARGET": "opensource", "asl.log": "Destination=file", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "ISS", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\issuser", "INIT_CWD": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\ISS", "major_version": "18", "NODE": "E:\\node\\node.exe", "NODE_ENV": "production", "NODE_OPTIONS": "--openssl-legacy-provider", "NODE_PATH": "D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\bin\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\bin\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules;D:\\椤圭洰\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules", "node_version": "v20.18.1", "npm_command": "run-script", "npm_config_cache": "E:\\nvm\\nvm\\node_cache", "npm_config_frozen_lockfile": "", "npm_config_node_gyp": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_prefix": "E:\\nvm\\nvm\\node_global", "npm_config_registry": "https://registry.npmmirror.com", "npm_config_user_agent": "pnpm/9.12.3 npm/? node/v20.18.1 win32 x64", "npm_config__tiptap_pro_registry": "https://registry.tiptap.dev/", "npm_execpath": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\bin\\pnpm.cjs", "npm_lifecycle_event": "build:os-local", "npm_lifecycle_script": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "npm_node_execpath": "E:\\node\\node.exe", "npm_package_browserslist_0": "> 1%", "npm_package_browserslist_1": "last 2 versions", "npm_package_browserslist_2": "not ie <= 10", "npm_package_dependencies_ace_builds": "^1.4.12", "npm_package_dependencies_ahooks": "^3.7.7", "npm_package_dependencies_antd": "^5.17.4", "npm_package_dependencies_classnames": "^2.2.6", "npm_package_dependencies_compression_webpack_plugin": "^11.0.0", "npm_package_dependencies_copy_to_clipboard": "^3.3.1", "npm_package_dependencies_cross_env": "^7.0.3", "npm_package_dependencies_crypto_js": "^4.2.0", "npm_package_dependencies_dayjs": "^1.11.10", "npm_package_dependencies_echarts": "^5.0.2", "npm_package_dependencies_echarts_for_react": "^3.0.1", "npm_package_dependencies_eslint_config_tencent": "^1.0.4", "npm_package_dependencies_hchatdata_chat_sdk": "workspace:*", "npm_package_dependencies_jsencrypt": "^3.0.1", "npm_package_dependencies_lodash": "^4.17.11", "npm_package_dependencies_moment": "^2.29.1", "npm_package_dependencies_nprogress": "^0.2.0", "npm_package_dependencies_numeral": "^2.0.6", "npm_package_dependencies_omit_js": "^2.0.2", "npm_package_dependencies_path_to_regexp": "^2.4.0", "npm_package_dependencies_qs": "^6.9.0", "npm_package_dependencies_query_string": "^9.0.0", "npm_package_dependencies_react": "^18.3.1", "npm_package_dependencies_react_ace": "^9.4.1", "npm_package_dependencies_react_dom": "^18.3.1", "npm_package_dependencies_react_spinners": "^0.13.8", "npm_package_dependencies_react_split_pane": "^2.0.3", "npm_package_dependencies_react_syntax_highlighter": "^15.4.3", "npm_package_dependencies_sql_formatter": "^15.6.1", "npm_package_dependencies_supersonic_insights_flow_components": "^1.4.9", "npm_package_dependencies_umi_request": "1.4.0", "npm_package_dependencies__antv_dom_util": "^2.0.4", "npm_package_dependencies__antv_g6": "^4.8.23", "npm_package_dependencies__antv_g6_core": "^0.8.23", "npm_package_dependencies__antv_layout": "^0.3.20", "npm_package_dependencies__antv_x6": "1.30.1", "npm_package_dependencies__ant_design_icons": "^5.2.6", "npm_package_dependencies__ant_design_pro_components": "2.7.0", "npm_package_dependencies__babel_runtime": "^7.22.5", "npm_package_dependencies__types_numeral": "^2.0.2", "npm_package_dependencies__types_react_draft_wysiwyg": "^1.13.2", "npm_package_dependencies__types_react_syntax_highlighter": "^13.5.0", "npm_package_dependencies__umijs_route_utils": "2.2.2", "npm_package_description": "data chat", "npm_package_devDependencies_carlo": "^0.9.46", "npm_package_devDependencies_compression": "^1.8.1", "npm_package_devDependencies_cross_port_killer": "^1.1.1", "npm_package_devDependencies_detect_installer": "^1.0.1", "npm_package_devDependencies_eslint": "^7.1.0", "npm_package_devDependencies_eslint_plugin_chalk": "^1.0.0", "npm_package_devDependencies_eslint_plugin_import": "^2.27.5", "npm_package_devDependencies_express": "^4.21.2", "npm_package_devDependencies_gh_pages": "^3.0.0", "npm_package_devDependencies_http_proxy_middleware": "^2.0.6", "npm_package_devDependencies_jsdom_global": "^3.0.2", "npm_package_devDependencies_lint_staged": "^10.0.0", "npm_package_devDependencies_prettier": "^2.3.1", "npm_package_devDependencies_pro_download": "1.0.1", "npm_package_devDependencies_puppeteer_core": "^5.0.0", "npm_package_devDependencies_stylelint": "^13.0.0", "npm_package_devDependencies_typescript": "^4.0.3", "npm_package_devDependencies_umi_presets_pro": "2.0.2", "npm_package_devDependencies__ant_design_pro_cli": "^2.0.2", "npm_package_devDependencies__types_classnames": "^2.2.7", "npm_package_devDependencies__types_crypto_js": "^4.0.1", "npm_package_devDependencies__types_draftjs_to_html": "^0.8.0", "npm_package_devDependencies__types_echarts": "^4.9.4", "npm_package_devDependencies__types_express": "^4.17.0", "npm_package_devDependencies__types_history": "^4.7.2", "npm_package_devDependencies__types_jest": "^26.0.0", "npm_package_devDependencies__types_lodash": "^4.14.144", "npm_package_devDependencies__types_pinyin": "^2.8.3", "npm_package_devDependencies__types_qs": "^6.5.3", "npm_package_devDependencies__types_react": "18.3.1", "npm_package_devDependencies__types_react_dom": "^18.3.0", "npm_package_devDependencies__types_react_helmet": "^6.1.0", "npm_package_devDependencies__umijs_fabric": "^4.0.1", "npm_package_devDependencies__umijs_max": "^4.2.5", "npm_package_devDependencies__umijs_plugin_model": "^2.6.2", "npm_package_engines_node": ">=16.0.0", "npm_package_lint_staged______less": "stylelint --syntax less", "npm_package_lint_staged_______js_jsx_tsx_ts_less_md_json__0": "prettier --write", "npm_package_lint_staged_______js_jsx_ts_tsx_": "npm run lint-staged:js", "npm_package_name": "hchatdata-fe", "npm_package_packageManager": "pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee", "npm_package_private": "true", "npm_package_scripts_analyze": "cross-env ANALYZE=1 max build", "npm_package_scripts_build": "npm run build:os", "npm_package_scripts_build_inner": "cross-env REACT_APP_ENV=prod APP_TARGET=inner max build", "npm_package_scripts_build_os": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build", "npm_package_scripts_build_os_local": "cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build", "npm_package_scripts_build_test": "cross-env REACT_APP_ENV=test max build", "npm_package_scripts_deploy": "npm run site && npm run gh-pages", "npm_package_scripts_dev": "npm run start:osdev", "npm_package_scripts_dev_inner": "npm run start:dev", "npm_package_scripts_dev_os": "npm run start:osdev", "npm_package_scripts_gh_pages": "gh-pages -d dist", "npm_package_scripts_i18n_remove": "pro i18n-remove --locale=zh-CN --write", "npm_package_scripts_lint": "max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "npm_package_scripts_lint_fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "npm_package_scripts_lint_js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "npm_package_scripts_lint_prettier": "prettier --check \"src/**/*\" --end-of-line auto", "npm_package_scripts_lint_staged": "lint-staged", "npm_package_scripts_lint_staged_js": "eslint --ext .js,.jsx,.ts,.tsx ", "npm_package_scripts_lint_style": "stylelint --fix \"src/**/*.less\" --syntax less", "npm_package_scripts_no_build_inner": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build", "npm_package_scripts_no_dev_inner": "NODE_OPTIONS=--openssl-legacy-provider npm run start:dev", "npm_package_scripts_no_dev_os": "NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev", "npm_package_scripts_postinstall": "max setup", "npm_package_scripts_precommit": "lint-staged", "npm_package_scripts_pretest": "node ./tests/beforeTest", "npm_package_scripts_prettier": "prettier -c --write \"src/**/*\"", "npm_package_scripts_start": "npm run start:osdev", "npm_package_scripts_start_dev": "cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev", "npm_package_scripts_start_no_mock": "cross-env MOCK=none max dev", "npm_package_scripts_start_no_ui": "cross-env UMI_UI=none max dev", "npm_package_scripts_start_osdev": "cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev", "npm_package_scripts_start_pre": "cross-env REACT_APP_ENV=pre max dev", "npm_package_scripts_start_test": "cross-env REACT_APP_ENV=test MOCK=none max dev", "npm_package_scripts_test": "max test", "npm_package_scripts_test_all": "node ./tests/run-tests.js", "npm_package_scripts_test_component": "max test ./src/components", "npm_package_scripts_tsc": "tsc --noEmit", "npm_package_version": "0.1.0", "NUMBER_OF_PROCESSORS": "8", "NVM_HOME": "E:\\nvm\\nvm", "NVM_NODE_GLOBAL": "E:\\nvm\\nvm\\node_global", "NVM_SYMLINK": "E:\\node", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node-gyp-bin;D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;E:\\nvm\\nvm;E:\\node;C:\\Program Files\\CorpLink\\current\\module\\mdm\\x64\\policy\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\python311\\Scripts\\;C:\\python311\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\nvm\\nvm;E:\\node;D:\\软通\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC", "PNPM_SCRIPT_SRC_DIR": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 142 Stepping 12, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "8e0c", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "REACT_APP_ENV": "prod", "RUN_TYPE": "local", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SystemDrive": "C:", "SystemRoot": "C:\\Windows", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "UMI_DIR": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq\\node_modules\\umi", "UMI_PRESETS": "D:\\项目\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\dist\\preset.js", "USERDOMAIN": "ISS", "USERDOMAIN_ROAMINGPROFILE": "ISS", "USERNAME": "issuser", "USERPROFILE": "C:\\Users\\<USER>", "windir": "C:\\Windows", "API_BASE_URL": "/api/semantic/", "CHAT_API_BASE_URL": "/api/chat/", "AUTH_API_BASE_URL": "/api/auth/", "SHOW_TAG": false, "tmeAvatarUrl": ""}, "ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION": "", "REACT_APP_ENV": "prod"}, "metas": [{"name": "app_version", "content": "2025-07-31 17:04:36"}], "hash": true, "routes": [{"path": "/chat/mobile", "name": "chat", "component": "@/pages/ChatPage", "hideInMenu": true, "layout": false, "envEnableList": ["chat"]}, {"path": "/chat/external", "name": "chat", "component": "@/pages/ChatPage", "hideInMenu": true, "layout": false, "envEnableList": ["chat"]}, {"path": "/chat", "name": "chat", "component": "@/pages/ChatPage", "envEnableList": ["chat"]}, {"path": "/agent", "name": "agent", "component": "@/pages/Agent", "envEnableList": ["chat"], "access": "ADMIN"}, {"path": "/plugin", "name": "plugin", "component": "@/pages/ChatPlugin", "envEnableList": ["chat"], "access": "ADMIN"}, {"path": "/model/metric/edit/:metricId", "name": "metricEdit", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}, {"path": "/model/", "component": "@/pages/SemanticModel", "name": "semanticModel", "envEnableList": ["semantic"], "access": "ADMIN", "routes": [{"path": "/model/", "redirect": "/model/domain"}, {"path": "/model/domain/", "component": "@/pages/SemanticModel/OverviewContainer", "access": "ADMIN", "routes": [{"path": "/model/domain/:domainId", "component": "@/pages/SemanticModel/DomainManager", "routes": [{"path": "/model/domain/:domainId/:menuKey", "component": "@/pages/SemanticModel/DomainManager"}]}, {"path": "/model/domain/manager/:domainId/:modelId", "component": "@/pages/SemanticModel/ModelManager", "routes": [{"path": "/model/domain/manager/:domainId/:modelId/:menuKey", "component": "@/pages/SemanticModel/ModelManager"}]}]}, {"path": "/model/dataset/:domainId/:datasetId", "component": "@/pages/SemanticModel/View/components/Detail", "envEnableList": ["semantic"], "routes": [{"path": "/model/dataset/:domainId/:datasetId/:menuKey", "component": "@/pages/SemanticModel/View/components/Detail"}]}, {"path": "/model/metric/:domainId/:modelId/:metricId", "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}, {"path": "/model/dimension/:domainId/:modelId/:dimensionId", "component": "@/pages/SemanticModel/Dimension/Detail", "envEnableList": ["semantic"]}]}, {"path": "/metric", "name": "metric", "component": "@/pages/SemanticModel/Metric", "envEnableList": ["semantic"], "access": "ADMIN", "routes": [{"path": "/metric", "redirect": "/metric/market"}, {"path": "/metric/market", "component": "@/pages/SemanticModel/Metric/Market", "access": "ADMIN", "hideInMenu": true, "envEnableList": ["semantic"]}, {"path": "/metric/detail/:metricId", "name": "metricDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Detail", "envEnableList": ["semantic"]}, {"path": "/metric/detail/edit/:metricId", "name": "metricDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Metric/Edit", "envEnableList": ["semantic"]}]}, {"path": "/tag", "name": "tag", "component": "@/pages/SemanticModel/Insights", "envEnableList": ["semantic"], "hideInMenu": true, "routes": [{"path": "/tag", "redirect": "/tag/market"}, {"path": "/tag/market", "component": "@/pages/SemanticModel/Insights/Market", "hideInMenu": true, "envEnableList": ["semantic"]}, {"path": "/tag/detail/:tagId", "name": "tagDetail", "hideInMenu": true, "component": "@/pages/SemanticModel/Insights/Detail", "envEnableList": ["semantic"]}]}, {"path": "/login", "name": "login", "layout": false, "hideInMenu": true, "component": "./Login"}, {"path": "/database", "name": "database", "component": "@/pages/SemanticModel/components/Database/DatabaseTable", "envEnableList": ["semantic"], "access": "ADMIN"}, {"path": "/llm", "name": "llm", "component": "@/pages/SemanticModel/components/LLM/LlmTable", "envEnableList": ["semantic"], "access": "ADMIN"}, {"path": "/system", "name": "system", "component": "@/pages/System", "access": "SYSTEM_ADMIN"}, {"path": "/user", "name": "user", "component": "@/pages/User/UserTable", "access": "ADMIN"}, {"path": "/", "redirect": "/chat"}, {"path": "/401", "component": "@/pages/401"}], "theme": {"blue-base": "#1890ff", "blue-1": "#e6f7ff", "blue-2": "#bae7ff", "blue-3": "#91d5ff", "blue-4": "#69c0ff", "blue-5": "#40a9ff", "blue-6": "#1890ff", "blue-7": "#096dd9", "blue-8": "#0050b3", "blue-9": "#003a8c", "blue-10": "#002766", "purple-base": "#722ed1", "purple-1": "#f9f0ff", "purple-2": "#efdbff", "purple-3": "#d3adf7", "purple-4": "#b37feb", "purple-5": "#9254de", "purple-6": "#722ed1", "purple-7": "#531dab", "purple-8": "#391085", "purple-9": "#22075e", "purple-10": "#120338", "cyan-base": "#13c2c2", "cyan-1": "#e6fffb", "cyan-2": "#b5f5ec", "cyan-3": "#87e8de", "cyan-4": "#5cdbd3", "cyan-5": "#36cfc9", "cyan-6": "#13c2c2", "cyan-7": "#08979c", "cyan-8": "#006d75", "cyan-9": "#00474f", "cyan-10": "#002329", "green-base": "#52c41a", "green-1": "#f6ffed", "green-2": "#d9f7be", "green-3": "#b7eb8f", "green-4": "#95de64", "green-5": "#73d13d", "green-6": "#52c41a", "green-7": "#389e0d", "green-8": "#237804", "green-9": "#135200", "green-10": "#092b00", "magenta-base": "#eb2f96", "magenta-1": "#fff0f6", "magenta-2": "#ffd6e7", "magenta-3": "#ffadd2", "magenta-4": "#ff85c0", "magenta-5": "#f759ab", "magenta-6": "#eb2f96", "magenta-7": "#c41d7f", "magenta-8": "#9e1068", "magenta-9": "#780650", "magenta-10": "#520339", "pink-base": "#eb2f96", "pink-1": "#fff0f6", "pink-2": "#ffd6e7", "pink-3": "#ffadd2", "pink-4": "#ff85c0", "pink-5": "#f759ab", "pink-6": "#eb2f96", "pink-7": "#c41d7f", "pink-8": "#9e1068", "pink-9": "#780650", "pink-10": "#520339", "red-base": "#f5222d", "red-1": "#fff1f0", "red-2": "#ffccc7", "red-3": "#ffa39e", "red-4": "#ff7875", "red-5": "#ff4d4f", "red-6": "#f5222d", "red-7": "#cf1322", "red-8": "#a8071a", "red-9": "#820014", "red-10": "#5c0011", "orange-base": "#fa8c16", "orange-1": "#fff7e6", "orange-2": "#ffe7ba", "orange-3": "#ffd591", "orange-4": "#ffc069", "orange-5": "#ffa940", "orange-6": "#fa8c16", "orange-7": "#d46b08", "orange-8": "#ad4e00", "orange-9": "#873800", "orange-10": "#612500", "yellow-base": "#fadb14", "yellow-1": "#feffe6", "yellow-2": "#ffffb8", "yellow-3": "#fffb8f", "yellow-4": "#fff566", "yellow-5": "#ffec3d", "yellow-6": "#fadb14", "yellow-7": "#d4b106", "yellow-8": "#ad8b00", "yellow-9": "#876800", "yellow-10": "#614700", "volcano-base": "#fa541c", "volcano-1": "#fff2e8", "volcano-2": "#ffd8bf", "volcano-3": "#ffbb96", "volcano-4": "#ff9c6e", "volcano-5": "#ff7a45", "volcano-6": "#fa541c", "volcano-7": "#d4380d", "volcano-8": "#ad2102", "volcano-9": "#871400", "volcano-10": "#610b00", "geekblue-base": "#2f54eb", "geekblue-1": "#f0f5ff", "geekblue-2": "#d6e4ff", "geekblue-3": "#adc6ff", "geekblue-4": "#85a5ff", "geekblue-5": "#597ef7", "geekblue-6": "#2f54eb", "geekblue-7": "#1d39c4", "geekblue-8": "#10239e", "geekblue-9": "#061178", "geekblue-10": "#030852", "lime-base": "#a0d911", "lime-1": "#fcffe6", "lime-2": "#f4ffb8", "lime-3": "#eaff8f", "lime-4": "#d3f261", "lime-5": "#bae637", "lime-6": "#a0d911", "lime-7": "#7cb305", "lime-8": "#5b8c00", "lime-9": "#3f6600", "lime-10": "#254000", "gold-base": "#faad14", "gold-1": "#fffbe6", "gold-2": "#fff1b8", "gold-3": "#ffe58f", "gold-4": "#ffd666", "gold-5": "#ffc53d", "gold-6": "#faad14", "gold-7": "#d48806", "gold-8": "#ad6800", "gold-9": "#874d00", "gold-10": "#613400", "preset-colors": "pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,", "theme": "default", "ant-prefix": "ant", "html-selector": "html", "primary-color": "#cc0000", "primary-color-hover": "#40a9ff", "primary-color-active": "#096dd9", "primary-color-outline": "rgba(24, 144, 255, 0.2)", "processing-color": "#1890ff", "info-color": "#1890ff", "info-color-deprecated-bg": "#e6f7ff", "info-color-deprecated-border": "#91d5ff", "success-color": "#52c41a", "success-color-hover": "#73d13d", "success-color-active": "#389e0d", "success-color-outline": "rgba(82, 196, 26, 0.2)", "success-color-deprecated-bg": "#f6ffed", "success-color-deprecated-border": "#b7eb8f", "warning-color": "#faad14", "warning-color-hover": "#ffc53d", "warning-color-active": "#d48806", "warning-color-outline": "rgba(250, 173, 20, 0.2)", "warning-color-deprecated-bg": "#fffbe6", "warning-color-deprecated-border": "#ffe58f", "error-color": "#ff4d4f", "error-color-hover": "#ff7875", "error-color-active": "#d9363e", "error-color-outline": "rgba(255, 77, 79, 0.2)", "error-color-deprecated-bg": "#fff2f0", "error-color-deprecated-border": "#ffccc7", "highlight-color": "#ff4d4f", "normal-color": "#d9d9d9", "white": "#fff", "black": "#000", "primary-1": "#e6f7ff", "primary-2": "#bae7ff", "primary-3": "#91d5ff", "primary-4": "#69c0ff", "primary-5": "#40a9ff", "primary-6": "#1890ff", "primary-7": "#096dd9", "primary-8": "#0050b3", "primary-9": "#003a8c", "primary-10": "#002766", "component-background": "#fff", "popover-background": "#fff", "popover-customize-border-color": "#f0f0f0", "font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "code-family": "'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, Courier, monospace", "text-color": "rgba(0, 0, 0, 0.85)", "text-color-secondary": "rgba(0, 0, 0, 0.45)", "text-color-inverse": "#fff", "icon-color": "inherit", "icon-color-hover": "rgba(0, 0, 0, 0.75)", "heading-color": "rgba(0, 0, 0, 0.85)", "text-color-dark": "rgba(255, 255, 255, 0.85)", "text-color-secondary-dark": "rgba(255, 255, 255, 0.65)", "text-selection-bg": "#1890ff", "font-variant-base": "tabular-nums", "font-feature-settings-base": "tnum", "font-size-base": "14px", "font-size-lg": "16px", "font-size-sm": "12px", "heading-1-size": "38px", "heading-2-size": "30px", "heading-3-size": "24px", "heading-4-size": "20px", "heading-5-size": "16px", "line-height-base": "1.5715", "border-radius-base": "2px", "border-radius-sm": "2px", "control-border-radius": "2px", "arrow-border-radius": "2px", "padding-lg": "24px", "padding-md": "16px", "padding-sm": "12px", "padding-xs": "8px", "padding-xss": "4px", "control-padding-horizontal": "12px", "control-padding-horizontal-sm": "8px", "margin-lg": "24px", "margin-md": "16px", "margin-sm": "12px", "margin-xs": "8px", "margin-xss": "4px", "height-base": "32px", "height-lg": "40px", "height-sm": "24px", "item-active-bg": "#e6f7ff", "item-hover-bg": "#f5f5f5", "iconfont-css-prefix": "anticon", "link-color": "#1890ff", "link-hover-color": "#40a9ff", "link-active-color": "#096dd9", "link-decoration": "none", "link-hover-decoration": "none", "link-focus-decoration": "none", "link-focus-outline": "0", "ease-base-out": "cubic-bezier(0.7, 0.3, 0.1, 1)", "ease-base-in": "cubic-bezier(0.9, 0, 0.3, 0.7)", "ease-out": "cubic-bezier(0.215, 0.61, 0.355, 1)", "ease-in": "cubic-bezier(0.55, 0.055, 0.675, 0.19)", "ease-in-out": "cubic-bezier(0.645, 0.045, 0.355, 1)", "ease-out-back": "cubic-bezier(0.12, 0.4, 0.29, 1.46)", "ease-in-back": "cubic-bezier(0.71, -0.46, 0.88, 0.6)", "ease-in-out-back": "cubic-bezier(0.71, -0.46, 0.29, 1.46)", "ease-out-circ": "cubic-bezier(0.08, 0.82, 0.17, 1)", "ease-in-circ": "cubic-bezier(0.6, 0.04, 0.98, 0.34)", "ease-in-out-circ": "cubic-bezier(0.78, 0.14, 0.15, 0.86)", "ease-out-quint": "cubic-bezier(0.23, 1, 0.32, 1)", "ease-in-quint": "cubic-bezier(0.755, 0.05, 0.855, 0.06)", "ease-in-out-quint": "cubic-bezier(0.86, 0, 0.07, 1)", "border-color-base": "#d9d9d9", "border-color-split": "#f0f0f0", "border-color-inverse": "#fff", "border-width-base": "1px", "border-style-base": "solid", "outline-blur-size": "0", "outline-width": "2px", "outline-color": "#1890ff", "outline-fade": "20%", "background-color-light": "#fafafa", "background-color-base": "#f5f5f5", "disabled-color": "rgba(0, 0, 0, 0.25)", "disabled-bg": "#f5f5f5", "disabled-active-bg": "#e6e6e6", "disabled-color-dark": "rgba(255, 255, 255, 0.35)", "shadow-color": "rgba(0, 0, 0, 0.15)", "shadow-color-inverse": "#fff", "box-shadow-base": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "shadow-1-up": "0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-down": "0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-left": "-6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-1-right": "6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03)", "shadow-2": "0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)", "btn-font-weight": "400", "btn-border-radius-base": "2px", "btn-border-radius-sm": "2px", "btn-border-width": "1px", "btn-border-style": "solid", "btn-shadow": "0 2px 0 rgba(0, 0, 0, 0.015)", "btn-primary-shadow": "0 2px 0 rgba(0, 0, 0, 0.045)", "btn-text-shadow": "0 -1px 0 rgba(0, 0, 0, 0.12)", "btn-primary-color": "#fff", "btn-primary-bg": "#1890ff", "btn-default-color": "rgba(0, 0, 0, 0.85)", "btn-default-bg": "#fff", "btn-default-border": "#d9d9d9", "btn-danger-color": "#fff", "btn-danger-bg": "#ff4d4f", "btn-danger-border": "#ff4d4f", "btn-disable-color": "rgba(0, 0, 0, 0.25)", "btn-disable-bg": "#f5f5f5", "btn-disable-border": "#d9d9d9", "btn-default-ghost-color": "#fff", "btn-default-ghost-bg": "transparent", "btn-default-ghost-border": "#fff", "btn-font-size-lg": "16px", "btn-font-size-sm": "14px", "btn-padding-horizontal-base": "15px", "btn-padding-horizontal-lg": "15px", "btn-padding-horizontal-sm": "7px", "btn-height-base": "32px", "btn-height-lg": "40px", "btn-height-sm": "24px", "btn-line-height": "1.5715", "btn-circle-size": "32px", "btn-circle-size-lg": "40px", "btn-circle-size-sm": "24px", "btn-square-size": "32px", "btn-square-size-lg": "40px", "btn-square-size-sm": "24px", "btn-square-only-icon-size": "16px", "btn-square-only-icon-size-sm": "14px", "btn-square-only-icon-size-lg": "18px", "btn-group-border": "#40a9ff", "btn-link-hover-bg": "transparent", "btn-text-hover-bg": "rgba(0, 0, 0, 0.018)", "checkbox-size": "16px", "checkbox-color": "#1890ff", "checkbox-check-color": "#fff", "checkbox-check-bg": "#fff", "checkbox-border-width": "1px", "checkbox-border-radius": "2px", "checkbox-group-item-margin-right": "8px", "descriptions-bg": "#fafafa", "descriptions-title-margin-bottom": "20px", "descriptions-default-padding": "16px 24px", "descriptions-middle-padding": "12px 24px", "descriptions-small-padding": "8px 16px", "descriptions-item-padding-bottom": "16px", "descriptions-item-trailing-colon": "true", "descriptions-item-label-colon-margin-right": "8px", "descriptions-item-label-colon-margin-left": "2px", "descriptions-extra-color": "rgba(0, 0, 0, 0.85)", "divider-text-padding": "1em", "divider-orientation-margin": "5%", "divider-color": "rgba(0, 0, 0, 0.06)", "divider-vertical-gutter": "8px", "dropdown-selected-color": "#1890ff", "dropdown-menu-submenu-disabled-bg": "#fff", "dropdown-selected-bg": "#e6f7ff", "empty-font-size": "14px", "radio-size": "16px", "radio-top": "0.2em", "radio-border-width": "1px", "radio-dot-size": "8px", "radio-dot-color": "#1890ff", "radio-dot-disabled-color": "rgba(0, 0, 0, 0.2)", "radio-solid-checked-color": "#fff", "radio-button-bg": "#fff", "radio-button-checked-bg": "#fff", "radio-button-color": "rgba(0, 0, 0, 0.85)", "radio-button-hover-color": "#40a9ff", "radio-button-active-color": "#096dd9", "radio-button-padding-horizontal": "15px", "radio-disabled-button-checked-bg": "#e6e6e6", "radio-disabled-button-checked-color": "rgba(0, 0, 0, 0.25)", "radio-wrapper-margin-right": "8px", "screen-xs": "480px", "screen-xs-min": "480px", "screen-sm": "576px", "screen-sm-min": "576px", "screen-md": "768px", "screen-md-min": "768px", "screen-lg": "992px", "screen-lg-min": "992px", "screen-xl": "1200px", "screen-xl-min": "1200px", "screen-xxl": "1600px", "screen-xxl-min": "1600px", "screen-xs-max": "575px", "screen-sm-max": "767px", "screen-md-max": "991px", "screen-lg-max": "1199px", "screen-xl-max": "1599px", "grid-columns": "24", "layout-header-background": "#001529", "layout-header-height": "64px", "layout-header-padding": "0 50px", "layout-header-color": "rgba(0, 0, 0, 0.85)", "layout-footer-padding": "24px 50px", "layout-footer-background": "#f0f2f5", "layout-sider-background": "#001529", "layout-trigger-height": "48px", "layout-trigger-background": "#002140", "layout-trigger-color": "#fff", "layout-zero-trigger-width": "36px", "layout-zero-trigger-height": "42px", "layout-sider-background-light": "#fff", "layout-trigger-background-light": "#fff", "layout-trigger-color-light": "rgba(0, 0, 0, 0.85)", "zindex-badge": "auto", "zindex-table-fixed": "2", "zindex-affix": "10", "zindex-back-top": "10", "zindex-picker-panel": "10", "zindex-popup-close": "10", "zindex-modal": "1000", "zindex-modal-mask": "1000", "zindex-message": "1010", "zindex-notification": "1010", "zindex-popover": "1030", "zindex-dropdown": "1050", "zindex-picker": "1050", "zindex-popoconfirm": "1060", "zindex-tooltip": "1070", "zindex-image": "1080", "animation-duration-slow": "0.3s", "animation-duration-base": "0.2s", "animation-duration-fast": "0.1s", "collapse-panel-border-radius": "2px", "dropdown-menu-bg": "#fff", "dropdown-vertical-padding": "5px", "dropdown-edge-child-vertical-padding": "4px", "dropdown-font-size": "14px", "dropdown-line-height": "22px", "label-required-color": "#ff4d4f", "label-color": "rgba(0, 0, 0, 0.85)", "form-warning-input-bg": "#fff", "form-item-margin-bottom": "24px", "form-item-trailing-colon": "true", "form-vertical-label-padding": "0 0 8px", "form-vertical-label-margin": "0", "form-item-label-font-size": "14px", "form-item-label-height": "32px", "form-item-label-colon-margin-right": "8px", "form-item-label-colon-margin-left": "2px", "form-error-input-bg": "#fff", "input-height-base": "32px", "input-height-lg": "40px", "input-height-sm": "24px", "input-padding-horizontal": "11px", "input-padding-horizontal-base": "11px", "input-padding-horizontal-sm": "7px", "input-padding-horizontal-lg": "11px", "input-padding-vertical-base": "4px", "input-padding-vertical-sm": "0px", "input-padding-vertical-lg": "6.5px", "input-placeholder-color": "#bfbfbf", "input-color": "rgba(0, 0, 0, 0.85)", "input-icon-color": "rgba(0, 0, 0, 0.85)", "input-border-color": "#d9d9d9", "input-bg": "#fff", "input-number-hover-border-color": "#40a9ff", "input-number-handler-active-bg": "#f4f4f4", "input-number-handler-hover-bg": "#40a9ff", "input-number-handler-bg": "#fff", "input-number-handler-border-color": "#d9d9d9", "input-addon-bg": "#fafafa", "input-hover-border-color": "#40a9ff", "input-disabled-bg": "#f5f5f5", "input-outline-offset": "0 0", "input-icon-hover-color": "rgba(0, 0, 0, 0.85)", "input-disabled-color": "rgba(0, 0, 0, 0.25)", "mentions-dropdown-bg": "#fff", "mentions-dropdown-menu-item-hover-bg": "#fff", "select-border-color": "#d9d9d9", "select-item-selected-color": "rgba(0, 0, 0, 0.85)", "select-item-selected-font-weight": "600", "select-dropdown-bg": "#fff", "select-item-selected-bg": "#e6f7ff", "select-item-active-bg": "#f5f5f5", "select-dropdown-vertical-padding": "5px", "select-dropdown-font-size": "14px", "select-dropdown-line-height": "22px", "select-dropdown-height": "32px", "select-background": "#fff", "select-clear-background": "#fff", "select-selection-item-bg": "#f5f5f5", "select-selection-item-border-color": "#f0f0f0", "select-single-item-height-lg": "40px", "select-multiple-item-height": "24px", "select-multiple-item-height-lg": "32px", "select-multiple-item-spacing-half": "2px", "select-multiple-disabled-background": "#f5f5f5", "select-multiple-item-disabled-color": "#bfbfbf", "select-multiple-item-disabled-border-color": "#d9d9d9", "cascader-bg": "#fff", "cascader-item-selected-bg": "#e6f7ff", "cascader-menu-bg": "#fff", "cascader-menu-border-color-split": "#f0f0f0", "cascader-dropdown-vertical-padding": "5px", "cascader-dropdown-edge-child-vertical-padding": "4px", "cascader-dropdown-font-size": "14px", "cascader-dropdown-line-height": "22px", "anchor-bg": "transparent", "anchor-border-color": "#f0f0f0", "anchor-link-top": "4px", "anchor-link-left": "16px", "anchor-link-padding": "4px 0 4px 16px", "tooltip-max-width": "250px", "tooltip-color": "#fff", "tooltip-bg": "rgba(0, 0, 0, 0.75)", "tooltip-arrow-width": "11.3137085px", "tooltip-distance": "14.3137085px", "tooltip-arrow-color": "rgba(0, 0, 0, 0.75)", "tooltip-border-radius": "2px", "popover-bg": "#fff", "popover-color": "rgba(0, 0, 0, 0.85)", "popover-min-width": "177px", "popover-min-height": "32px", "popover-arrow-width": "11.3137085px", "popover-arrow-color": "#fff", "popover-arrow-outer-color": "#fff", "popover-distance": "15.3137085px", "popover-padding-horizontal": "16px", "modal-header-padding-vertical": "16px", "modal-header-padding-horizontal": "24px", "modal-header-bg": "#fff", "modal-header-padding": "16px 24px", "modal-header-border-width": "1px", "modal-header-border-style": "solid", "modal-header-title-line-height": "22px", "modal-header-title-font-size": "16px", "modal-header-border-color-split": "#f0f0f0", "modal-header-close-size": "54px", "modal-content-bg": "#fff", "modal-heading-color": "rgba(0, 0, 0, 0.85)", "modal-close-color": "rgba(0, 0, 0, 0.45)", "modal-footer-bg": "transparent", "modal-footer-border-color-split": "#f0f0f0", "modal-footer-border-style": "solid", "modal-footer-padding-vertical": "10px", "modal-footer-padding-horizontal": "16px", "modal-footer-border-width": "1px", "modal-mask-bg": "rgba(0, 0, 0, 0.45)", "modal-confirm-title-font-size": "16px", "modal-border-radius": "2px", "progress-default-color": "#1890ff", "progress-remaining-color": "#f5f5f5", "progress-info-text-color": "rgba(0, 0, 0, 0.85)", "progress-radius": "100px", "progress-steps-item-bg": "#f3f3f3", "progress-text-font-size": "1em", "progress-text-color": "rgba(0, 0, 0, 0.85)", "progress-circle-text-font-size": "1em", "menu-inline-toplevel-item-height": "40px", "menu-item-height": "40px", "menu-item-group-height": "1.5715", "menu-collapsed-width": "80px", "menu-bg": "#fff", "menu-popup-bg": "#fff", "menu-item-color": "rgba(0, 0, 0, 0.85)", "menu-inline-submenu-bg": "#fafafa", "menu-highlight-color": "#1890ff", "menu-highlight-danger-color": "#ff4d4f", "menu-item-active-bg": "#e6f7ff", "menu-item-active-danger-bg": "#fff1f0", "menu-item-active-border-width": "3px", "menu-item-group-title-color": "rgba(0, 0, 0, 0.45)", "menu-item-vertical-margin": "4px", "menu-item-font-size": "14px", "menu-item-boundary-margin": "8px", "menu-item-padding-horizontal": "20px", "menu-item-padding": "0 20px", "menu-horizontal-line-height": "46px", "menu-icon-margin-right": "10px", "menu-icon-size": "14px", "menu-icon-size-lg": "16px", "menu-item-group-title-font-size": "14px", "menu-dark-color": "rgba(255, 255, 255, 0.65)", "menu-dark-danger-color": "#ff4d4f", "menu-dark-bg": "#001529", "menu-dark-arrow-color": "#fff", "menu-dark-inline-submenu-bg": "#000c17", "menu-dark-highlight-color": "#fff", "menu-dark-item-active-bg": "#1890ff", "menu-dark-item-active-danger-bg": "#ff4d4f", "menu-dark-selected-item-icon-color": "#fff", "menu-dark-selected-item-text-color": "#fff", "menu-dark-item-hover-bg": "transparent", "spin-dot-size-sm": "14px", "spin-dot-size": "20px", "spin-dot-size-lg": "32px", "table-bg": "#fff", "table-header-bg": "#fafafa", "table-header-color": "rgba(0, 0, 0, 0.85)", "table-header-sort-bg": "#f5f5f5", "table-row-hover-bg": "#fafafa", "table-selected-row-color": "inherit", "table-selected-row-bg": "#e6f7ff", "table-selected-row-hover-bg": "#dcf4ff", "table-expanded-row-bg": "#fbfbfb", "table-padding-vertical": "16px", "table-padding-horizontal": "16px", "table-padding-vertical-md": "12px", "table-padding-horizontal-md": "8px", "table-padding-vertical-sm": "8px", "table-padding-horizontal-sm": "8px", "table-border-color": "#f0f0f0", "table-border-radius-base": "2px", "table-footer-bg": "#fafafa", "table-footer-color": "rgba(0, 0, 0, 0.85)", "table-header-bg-sm": "#fafafa", "table-font-size": "14px", "table-font-size-md": "14px", "table-font-size-sm": "14px", "table-header-cell-split-color": "rgba(0, 0, 0, 0.06)", "table-header-sort-active-bg": "rgba(0, 0, 0, 0.04)", "table-fixed-header-sort-active-bg": "#f5f5f5", "table-header-filter-active-bg": "rgba(0, 0, 0, 0.04)", "table-filter-btns-bg": "inherit", "table-filter-dropdown-bg": "#fff", "table-expand-icon-bg": "#fff", "table-selection-column-width": "32px", "table-sticky-scroll-bar-bg": "rgba(0, 0, 0, 0.35)", "table-sticky-scroll-bar-radius": "4px", "tag-border-radius": "2px", "tag-default-bg": "#fafafa", "tag-default-color": "rgba(0, 0, 0, 0.85)", "tag-font-size": "12px", "tag-line-height": "20px", "picker-bg": "#fff", "picker-basic-cell-hover-color": "#f5f5f5", "picker-basic-cell-active-with-range-color": "#e6f7ff", "picker-basic-cell-hover-with-range-color": "#cbe6ff", "picker-basic-cell-disabled-bg": "rgba(0, 0, 0, 0.04)", "picker-border-color": "#f0f0f0", "picker-date-hover-range-border-color": "#7ec1ff", "picker-date-hover-range-color": "#cbe6ff", "picker-time-panel-column-width": "56px", "picker-time-panel-column-height": "224px", "picker-time-panel-cell-height": "28px", "picker-panel-cell-height": "24px", "picker-panel-cell-width": "36px", "picker-text-height": "40px", "picker-panel-without-time-cell-height": "66px", "calendar-bg": "#fff", "calendar-input-bg": "#fff", "calendar-border-color": "#fff", "calendar-item-active-bg": "#e6f7ff", "calendar-column-active-bg": "rgba(230, 247, 255, 0.2)", "calendar-full-bg": "#fff", "calendar-full-panel-bg": "#fff", "carousel-dot-width": "16px", "carousel-dot-height": "3px", "carousel-dot-active-width": "24px", "badge-height": "20px", "badge-height-sm": "14px", "badge-dot-size": "6px", "badge-font-size": "12px", "badge-font-size-sm": "12px", "badge-font-weight": "normal", "badge-status-size": "6px", "badge-text-color": "#fff", "badge-color": "#ff4d4f", "rate-star-color": "#fadb14", "rate-star-bg": "#f0f0f0", "rate-star-size": "20px", "rate-star-hover-scale": "scale(1.1)", "card-head-color": "rgba(0, 0, 0, 0.85)", "card-head-background": "transparent", "card-head-font-size": "16px", "card-head-font-size-sm": "14px", "card-head-padding": "16px", "card-head-padding-sm": "8px", "card-head-height": "48px", "card-head-height-sm": "36px", "card-inner-head-padding": "12px", "card-padding-base": "24px", "card-padding-base-sm": "12px", "card-actions-background": "#fff", "card-actions-li-margin": "12px 0", "card-skeleton-bg": "#cfd8dc", "card-background": "#fff", "card-shadow": "0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)", "card-radius": "2px", "card-head-tabs-margin-bottom": "-17px", "card-head-extra-color": "rgba(0, 0, 0, 0.85)", "comment-bg": "inherit", "comment-padding-base": "16px 0", "comment-nest-indent": "44px", "comment-font-size-base": "14px", "comment-font-size-sm": "12px", "comment-author-name-color": "rgba(0, 0, 0, 0.45)", "comment-author-time-color": "#ccc", "comment-action-color": "rgba(0, 0, 0, 0.45)", "comment-action-hover-color": "#595959", "comment-actions-margin-bottom": "inherit", "comment-actions-margin-top": "12px", "comment-content-detail-p-margin-bottom": "inherit", "tabs-card-head-background": "#fafafa", "tabs-card-height": "40px", "tabs-card-active-color": "#1890ff", "tabs-card-horizontal-padding": "8px 16px", "tabs-card-horizontal-padding-sm": "6px 16px", "tabs-card-horizontal-padding-lg": "7px 16px 6px", "tabs-title-font-size": "14px", "tabs-title-font-size-lg": "16px", "tabs-title-font-size-sm": "14px", "tabs-ink-bar-color": "#1890ff", "tabs-bar-margin": "0 0 16px 0", "tabs-horizontal-gutter": "32px", "tabs-horizontal-margin": "0 0 0 32px", "tabs-horizontal-margin-rtl": "0 0 0 32px", "tabs-horizontal-padding": "12px 0", "tabs-horizontal-padding-lg": "16px 0", "tabs-horizontal-padding-sm": "8px 0", "tabs-vertical-padding": "8px 24px", "tabs-vertical-margin": "16px 0 0 0", "tabs-scrolling-size": "32px", "tabs-highlight-color": "#1890ff", "tabs-hover-color": "#40a9ff", "tabs-active-color": "#096dd9", "tabs-card-gutter": "2px", "tabs-card-tab-active-border-top": "2px solid transparent", "back-top-color": "#fff", "back-top-bg": "rgba(0, 0, 0, 0.45)", "back-top-hover-bg": "rgba(0, 0, 0, 0.85)", "avatar-size-base": "32px", "avatar-size-lg": "40px", "avatar-size-sm": "24px", "avatar-font-size-base": "18px", "avatar-font-size-lg": "24px", "avatar-font-size-sm": "14px", "avatar-bg": "#ccc", "avatar-color": "#fff", "avatar-border-radius": "2px", "avatar-group-overlapping": "-8px", "avatar-group-space": "3px", "avatar-group-border-color": "#fff", "switch-height": "22px", "switch-sm-height": "16px", "switch-min-width": "44px", "switch-sm-min-width": "28px", "switch-disabled-opacity": "0.4", "switch-color": "#1890ff", "switch-bg": "#fff", "switch-shadow-color": "rgba(0, 35, 11, 0.2)", "switch-padding": "2px", "switch-inner-margin-min": "7px", "switch-inner-margin-max": "25px", "switch-sm-inner-margin-min": "5px", "switch-sm-inner-margin-max": "18px", "pagination-item-bg": "#fff", "pagination-item-size": "32px", "pagination-item-size-sm": "24px", "pagination-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "pagination-font-weight-active": "500", "pagination-item-bg-active": "#fff", "pagination-item-link-bg": "#fff", "pagination-item-disabled-color-active": "rgba(0, 0, 0, 0.25)", "pagination-item-disabled-bg-active": "#e6e6e6", "pagination-item-input-bg": "#fff", "pagination-mini-options-size-changer-top": "0px", "page-header-padding": "24px", "page-header-padding-vertical": "16px", "page-header-padding-breadcrumb": "12px", "page-header-content-padding-vertical": "12px", "page-header-back-color": "#000", "page-header-ghost-bg": "inherit", "page-header-heading-title": "20px", "page-header-heading-sub-title": "14px", "page-header-tabs-tab-font-size": "16px", "breadcrumb-base-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-last-item-color": "rgba(0, 0, 0, 0.85)", "breadcrumb-font-size": "14px", "breadcrumb-icon-font-size": "14px", "breadcrumb-link-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-link-color-hover": "rgba(0, 0, 0, 0.85)", "breadcrumb-separator-color": "rgba(0, 0, 0, 0.45)", "breadcrumb-separator-margin": "0 8px", "slider-margin": "10px 6px 10px", "slider-rail-background-color": "#f5f5f5", "slider-rail-background-color-hover": "#e1e1e1", "slider-track-background-color": "#91d5ff", "slider-track-background-color-hover": "#69c0ff", "slider-handle-border-width": "2px", "slider-handle-background-color": "#fff", "slider-handle-color": "#91d5ff", "slider-handle-color-hover": "#69c0ff", "slider-handle-color-focus": "#46a6ff", "slider-handle-color-focus-shadow": "rgba(24, 144, 255, 0.12)", "slider-handle-color-tooltip-open": "#1890ff", "slider-handle-size": "14px", "slider-handle-margin-top": "-5px", "slider-handle-shadow": "0", "slider-dot-border-color": "#f0f0f0", "slider-dot-border-color-active": "#8cc8ff", "slider-disabled-color": "rgba(0, 0, 0, 0.25)", "slider-disabled-background-color": "#fff", "tree-bg": "#fff", "tree-title-height": "24px", "tree-child-padding": "18px", "tree-directory-selected-color": "#fff", "tree-directory-selected-bg": "#1890ff", "tree-node-hover-bg": "#f5f5f5", "tree-node-selected-bg": "#bae7ff", "collapse-header-padding": "12px 16px", "collapse-header-padding-extra": "40px", "collapse-header-bg": "#fafafa", "collapse-content-padding": "16px", "collapse-content-bg": "#fff", "collapse-header-arrow-left": "16px", "skeleton-color": "rgba(190, 190, 190, 0.2)", "skeleton-to-color": "rgba(129, 129, 129, 0.24)", "skeleton-paragraph-margin-top": "28px", "skeleton-paragraph-li-margin-top": "16px", "skeleton-paragraph-li-height": "16px", "skeleton-title-height": "16px", "skeleton-title-paragraph-margin-top": "24px", "transfer-header-height": "40px", "transfer-item-height": "32px", "transfer-disabled-bg": "#f5f5f5", "transfer-list-height": "200px", "transfer-item-hover-bg": "#f5f5f5", "transfer-item-selected-hover-bg": "#dcf4ff", "transfer-item-padding-vertical": "6px", "transfer-list-search-icon-top": "12px", "message-notice-content-padding": "10px 16px", "message-notice-content-bg": "#fff", "wave-animation-width": "6px", "alert-success-border-color": "#b7eb8f", "alert-success-bg-color": "#f6ffed", "alert-success-icon-color": "#52c41a", "alert-info-border-color": "#91d5ff", "alert-info-bg-color": "#e6f7ff", "alert-info-icon-color": "#1890ff", "alert-warning-border-color": "#ffe58f", "alert-warning-bg-color": "#fffbe6", "alert-warning-icon-color": "#faad14", "alert-error-border-color": "#ffccc7", "alert-error-bg-color": "#fff2f0", "alert-error-icon-color": "#ff4d4f", "alert-message-color": "rgba(0, 0, 0, 0.85)", "alert-text-color": "rgba(0, 0, 0, 0.85)", "alert-close-color": "rgba(0, 0, 0, 0.45)", "alert-close-hover-color": "rgba(0, 0, 0, 0.75)", "alert-no-icon-padding-vertical": "8px", "alert-with-description-no-icon-padding-vertical": "15px", "alert-with-description-padding-vertical": "15px", "alert-with-description-padding": "15px 15px 15px 24px", "alert-icon-top": "12.0005px", "alert-with-description-icon-size": "24px", "list-header-background": "transparent", "list-footer-background": "transparent", "list-empty-text-padding": "16px", "list-item-padding": "12px 0", "list-item-padding-sm": "8px 16px", "list-item-padding-lg": "16px 24px", "list-item-meta-margin-bottom": "16px", "list-item-meta-avatar-margin-right": "16px", "list-item-meta-title-margin-bottom": "12px", "list-customize-card-bg": "#fff", "list-item-meta-description-font-size": "14px", "statistic-title-font-size": "14px", "statistic-content-font-size": "24px", "statistic-unit-font-size": "24px", "statistic-font-family": "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON>l, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'", "drawer-header-padding": "16px 24px", "drawer-bg": "#fff", "drawer-footer-padding-vertical": "10px", "drawer-footer-padding-horizontal": "16px", "drawer-header-close-size": "56px", "drawer-title-font-size": "16px", "drawer-title-line-height": "22px", "timeline-width": "2px", "timeline-color": "#f0f0f0", "timeline-dot-border-width": "2px", "timeline-dot-color": "#1890ff", "timeline-dot-bg": "#fff", "timeline-item-padding-bottom": "20px", "typography-title-font-weight": "600", "typography-title-margin-top": "1.2em", "typography-title-margin-bottom": "0.5em", "upload-actions-color": "rgba(0, 0, 0, 0.45)", "process-tail-color": "#f0f0f0", "steps-nav-arrow-color": "rgba(0, 0, 0, 0.25)", "steps-background": "#fff", "steps-icon-size": "32px", "steps-icon-custom-size": "32px", "steps-icon-custom-top": "0px", "steps-icon-custom-font-size": "24px", "steps-icon-top": "-0.5px", "steps-icon-font-size": "16px", "steps-icon-margin": "0 8px 0 0", "steps-title-line-height": "32px", "steps-small-icon-size": "24px", "steps-small-icon-margin": "0 8px 0 0", "steps-dot-size": "8px", "steps-dot-top": "2px", "steps-current-dot-size": "10px", "steps-description-max-width": "140px", "steps-nav-content-max-width": "auto", "steps-vertical-icon-width": "16px", "steps-vertical-tail-width": "16px", "steps-vertical-tail-width-sm": "12px", "notification-bg": "#fff", "notification-padding-vertical": "16px", "notification-padding-horizontal": "24px", "result-title-font-size": "24px", "result-subtitle-font-size": "14px", "result-icon-font-size": "72px", "result-extra-margin": "24px 0 0 0", "image-size-base": "48px", "image-font-size-base": "24px", "image-bg": "#f5f5f5", "image-color": "#fff", "image-mask-font-size": "16px", "image-preview-operation-size": "18px", "image-preview-operation-color": "rgba(255, 255, 255, 0.85)", "image-preview-operation-disabled-color": "rgba(255, 255, 255, 0.25)", "segmented-bg": "rgba(0, 0, 0, 0.04)", "segmented-hover-bg": "rgba(0, 0, 0, 0.06)", "segmented-selected-bg": "#fff", "segmented-label-color": "rgba(0, 0, 0, 0.65)", "segmented-label-hover-color": "#262626", "root-entry-name": "variable"}, "outputPath": "hchatdata-webapp", "fastRefresh": true, "model": {}, "initialState": {}, "title": "AIData", "layout": {"locale": true, "navTheme": "light", "colorPrimary": "#0057ff", "layout": "top", "contentWidth": "Fluid", "fixedHeader": true, "fixSiderbar": true, "colorWeak": false, "title": "", "pwa": false, "iconfontUrl": "//at.alicdn.com/t/c/font_4120566_x5c4www9bqm.js"}, "locale": {"default": "zh-CN", "antd": true, "baseNavigator": true}, "antd": {}, "request": {}, "access": {}, "headScripts": [{"src": "/webapp/scripts/loading.js", "async": true}], "presets": ["umi-presets-pro"], "exportStatic": {}, "esbuildMinifyIIFE": true, "extraPostCSSPlugins": [null, null], "targets": {"chrome": 80}}, "routes": {"1": {"path": "/chat/mobile", "name": "chat", "hideInMenu": true, "layout": false, "file": "@/pages/ChatPage/index.tsx", "id": "1", "absPath": "/chat/mobile", "__content": "import { useLocation } from '@umijs/max';\r\nimport { getToken } from '@/utils/utils';\r\nimport queryString from 'query-string';\r\nimport { Chat } from 'hchatdata-chat-sdk';\r\n\r\nconst ChatPage = () => {\r\n  const location = useLocation();\r\n  const query = queryString.parse(location.search) || {};\r\n  const { agentId } = query;\r\n\r\n  return (\r\n    <div style={{ height: 'calc(100vh - 66px)', minHeight: 'calc(100vh - 66px)' }}>\r\n      <Chat initialAgentId={agentId ? +agentId : undefined} token={getToken() || ''} isDeveloper />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatPage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/ChatPage/index.tsx"}, "2": {"path": "/chat/external", "name": "chat", "hideInMenu": true, "layout": false, "file": "@/pages/ChatPage/index.tsx", "id": "2", "absPath": "/chat/external", "__content": "import { useLocation } from '@umijs/max';\r\nimport { getToken } from '@/utils/utils';\r\nimport queryString from 'query-string';\r\nimport { Chat } from 'hchatdata-chat-sdk';\r\n\r\nconst ChatPage = () => {\r\n  const location = useLocation();\r\n  const query = queryString.parse(location.search) || {};\r\n  const { agentId } = query;\r\n\r\n  return (\r\n    <div style={{ height: 'calc(100vh - 66px)', minHeight: 'calc(100vh - 66px)' }}>\r\n      <Chat initialAgentId={agentId ? +agentId : undefined} token={getToken() || ''} isDeveloper />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatPage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/ChatPage/index.tsx"}, "3": {"path": "/chat", "name": "chat", "file": "@/pages/ChatPage/index.tsx", "parentId": "ant-design-pro-layout", "id": "3", "absPath": "/chat", "__content": "import { useLocation } from '@umijs/max';\r\nimport { getToken } from '@/utils/utils';\r\nimport queryString from 'query-string';\r\nimport { Chat } from 'hchatdata-chat-sdk';\r\n\r\nconst ChatPage = () => {\r\n  const location = useLocation();\r\n  const query = queryString.parse(location.search) || {};\r\n  const { agentId } = query;\r\n\r\n  return (\r\n    <div style={{ height: 'calc(100vh - 66px)', minHeight: 'calc(100vh - 66px)' }}>\r\n      <Chat initialAgentId={agentId ? +agentId : undefined} token={getToken() || ''} isDeveloper />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatPage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/ChatPage/index.tsx"}, "4": {"path": "/agent", "name": "agent", "access": "ADMIN", "file": "@/pages/Agent/index.tsx", "parentId": "ant-design-pro-layout", "id": "4", "absPath": "/agent", "__content": "import { message } from 'antd';\r\nimport { useEffect, useState } from 'react';\r\nimport AgentsSection from './AgentsSection';\r\nimport { uuid, jsonParse } from '@/utils/utils';\r\nimport { deleteAgent, getAgentList, saveAgent } from './service';\r\nimport styles from './style.less';\r\nimport ToolModal from './ToolModal';\r\nimport AgentDetail from './AgentDetail';\r\nimport { AgentToolType, AgentType } from './type';\r\n\r\nconst Agent = () => {\r\n  const [agents, setAgents] = useState<AgentType[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentAgent, setCurrentAgent] = useState<AgentType>();\r\n  const [modalVisible, setModalVisible] = useState(false);\r\n  const [showDetail, setShowDetail] = useState<boolean>(false);\r\n  const [toolConfig, setAgentConfig] = useState<any>({});\r\n\r\n  useEffect(() => {\r\n    const config = jsonParse(currentAgent?.toolConfig, {});\r\n    setAgentConfig(config);\r\n  }, [currentAgent]);\r\n\r\n  const updateData = async () => {\r\n    setLoading(true);\r\n    const res = await getAgentList();\r\n    setLoading(false);\r\n    setAgents(res.data || []);\r\n  };\r\n\r\n  useEffect(() => {\r\n    updateData();\r\n  }, []);\r\n\r\n  const onSaveTool = async (tool: AgentToolType) => {\r\n    const newAgentConfig = toolConfig || ({} as any);\r\n    if (!newAgentConfig.tools) {\r\n      newAgentConfig.tools = [];\r\n    }\r\n    if (tool.id) {\r\n      const index = newAgentConfig.tools.findIndex((item: AgentToolType) => item.id === tool.id);\r\n      newAgentConfig.tools[index] = tool;\r\n    } else {\r\n      newAgentConfig.tools.push({ ...tool, id: uuid() });\r\n    }\r\n    setAgentConfig(newAgentConfig);\r\n    if (!currentAgent?.id) {\r\n      setCurrentAgent({\r\n        ...currentAgent,\r\n        toolConfig: JSON.stringify(newAgentConfig) as any,\r\n      });\r\n      setModalVisible(false);\r\n      return;\r\n    }\r\n    await onSaveAgent({\r\n      ...currentAgent,\r\n      toolConfig: JSON.stringify(newAgentConfig) as any,\r\n    });\r\n    setModalVisible(false);\r\n  };\r\n\r\n  const onSaveAgent = async (agent: AgentType, noTip?: boolean) => {\r\n    const { data, code } = await saveAgent(agent);\r\n    if (code === 200) {\r\n      setCurrentAgent({\r\n        ...data,\r\n      });\r\n      updateData();\r\n    }\r\n    if (!noTip) {\r\n      message.success('保存成功');\r\n    }\r\n  };\r\n\r\n  const onDeleteAgent = async (id: number) => {\r\n    await deleteAgent(id);\r\n    message.success('删除成功');\r\n    updateData();\r\n  };\r\n\r\n  return (\r\n    <div className={styles.agent}>\r\n      {!showDetail ? (\r\n        <AgentsSection\r\n          agents={agents}\r\n          loading={loading}\r\n          onSelectAgent={(agent) => {\r\n            setCurrentAgent(agent);\r\n            setShowDetail(true);\r\n          }}\r\n          onDeleteAgent={onDeleteAgent}\r\n          onSaveAgent={onSaveAgent}\r\n          onCreatBtnClick={() => {\r\n            setCurrentAgent(undefined);\r\n            setShowDetail(true);\r\n          }}\r\n        />\r\n      ) : (\r\n        <AgentDetail\r\n          currentAgent={currentAgent}\r\n          onSaveAgent={onSaveAgent}\r\n          onCreateToolBtnClick={() => {\r\n            setModalVisible(true);\r\n          }}\r\n          goBack={() => {\r\n            setShowDetail(false);\r\n            setCurrentAgent(undefined);\r\n          }}\r\n        />\r\n      )}\r\n      {modalVisible && (\r\n        <ToolModal\r\n          onSaveTool={onSaveTool}\r\n          onCancel={() => {\r\n            setModalVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Agent;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/Agent/index.tsx"}, "5": {"path": "/plugin", "name": "plugin", "access": "ADMIN", "file": "@/pages/ChatPlugin/index.tsx", "parentId": "ant-design-pro-layout", "id": "5", "absPath": "/plugin", "__content": "import { getLeafNodes } from '@/utils/utils';\r\nimport { PlusOutlined } from '@ant-design/icons';\r\nimport { Button, Input, message, Popconfirm, Select, Table, Tag } from 'antd';\r\nimport moment from 'moment';\r\nimport { useEffect, useState } from 'react';\r\nimport { PLUGIN_TYPE_MAP } from './constants';\r\nimport DetailModal from './DetailModal';\r\nimport { deletePlugin, getModelList, getPluginList } from './service';\r\nimport styles from './style.less';\r\nimport { ModelType, PluginType, PluginTypeEnum } from './type';\r\n\r\nconst { Search } = Input;\r\n\r\nconst PluginManage = () => {\r\n  const [name, setName] = useState<string>();\r\n  const [type, setType] = useState<PluginTypeEnum>();\r\n  const [pattern, setPattern] = useState<string>();\r\n  const [model, setModel] = useState<string>();\r\n  const [data, setData] = useState<PluginType[]>([]);\r\n  const [modelList, setModelList] = useState<ModelType[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [currentPluginDetail, setCurrentPluginDetail] = useState<PluginType>();\r\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\r\n\r\n  const initModelList = async () => {\r\n    const res = await getModelList();\r\n    setModelList(getLeafNodes(res.data));\r\n  };\r\n\r\n  const updateData = async (filters?: any) => {\r\n    setLoading(true);\r\n    const res = await getPluginList({ name, type, pattern, model, ...(filters || {}) });\r\n    setLoading(false);\r\n    setData(res.data?.map((item) => ({ ...item, config: JSON.parse(item.config || '{}') })) || []);\r\n  };\r\n\r\n  useEffect(() => {\r\n    initModelList();\r\n    updateData();\r\n  }, []);\r\n\r\n  const onCheckPluginDetail = (record: PluginType) => {\r\n    setCurrentPluginDetail(record);\r\n    setDetailModalVisible(true);\r\n  };\r\n\r\n  const onDeletePlugin = async (record: PluginType) => {\r\n    await deletePlugin(record.id);\r\n    message.success('插件删除成功');\r\n    updateData();\r\n  };\r\n\r\n  const columns: any[] = [\r\n    {\r\n      title: '插件名称',\r\n      dataIndex: 'name',\r\n      key: 'name',\r\n    },\r\n    {\r\n      title: '数据集',\r\n      dataIndex: 'dataSetList',\r\n      key: 'dataSetList',\r\n      width: 200,\r\n      render: (value: number[]) => {\r\n        if (value?.includes(-1)) {\r\n          return '默认';\r\n        }\r\n        return value ? (\r\n          <div className={styles.modelColumn}>\r\n            {value.map((id) => {\r\n              const name = modelList.find((model) => model.id === id)?.name;\r\n              return name ? <Tag key={id}>{name}</Tag> : null;\r\n            })}\r\n          </div>\r\n        ) : (\r\n          '-'\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '插件类型',\r\n      dataIndex: 'type',\r\n      key: 'type',\r\n      render: (value: string) => {\r\n        return (\r\n          <Tag color={value === PluginTypeEnum.WEB_PAGE ? 'blue' : 'cyan'}>\r\n            {PLUGIN_TYPE_MAP[value]}\r\n          </Tag>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      title: '函数描述',\r\n      dataIndex: 'pattern',\r\n      key: 'pattern',\r\n      width: 450,\r\n    },\r\n    {\r\n      title: '更新人',\r\n      dataIndex: 'updatedBy',\r\n      key: 'updatedBy',\r\n      render: (value: string) => {\r\n        return value || '-';\r\n      },\r\n    },\r\n    {\r\n      title: '更新时间',\r\n      dataIndex: 'updatedAt',\r\n      key: 'updatedAt',\r\n      render: (value: string) => {\r\n        return value ? moment(value).format('YYYY-MM-DD HH:mm') : '-';\r\n      },\r\n    },\r\n    {\r\n      title: '操作',\r\n      dataIndex: 'x',\r\n      key: 'x',\r\n      render: (_: any, record: any) => {\r\n        return (\r\n          <div className={styles.operator}>\r\n            <a\r\n              onClick={() => {\r\n                onCheckPluginDetail(record);\r\n              }}\r\n            >\r\n              编辑\r\n            </a>\r\n            <Popconfirm\r\n              title=\"确定删除吗？\"\r\n              onConfirm={() => {\r\n                onDeletePlugin(record);\r\n              }}\r\n            >\r\n              <a>删除</a>\r\n            </Popconfirm>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  const onModelChange = (value: string) => {\r\n    setModel(value);\r\n    updateData({ model: value });\r\n  };\r\n\r\n  const onTypeChange = (value: PluginTypeEnum) => {\r\n    setType(value);\r\n    updateData({ type: value });\r\n  };\r\n\r\n  const onSearch = () => {\r\n    updateData();\r\n  };\r\n\r\n  const onCreatePlugin = () => {\r\n    setCurrentPluginDetail(undefined);\r\n    setDetailModalVisible(true);\r\n  };\r\n\r\n  const onSavePlugin = () => {\r\n    setDetailModalVisible(false);\r\n    updateData();\r\n  };\r\n\r\n  return (\r\n    <div className={styles.pluginManage}>\r\n      <div className={styles.filterSection}>\r\n        <div className={styles.filterItem}>\r\n          <div className={styles.filterItemTitle}>主题域</div>\r\n          <Select\r\n            className={styles.filterItemControl}\r\n            placeholder=\"请选择主题域\"\r\n            options={modelList.map((model) => ({ label: model.name, value: model.id }))}\r\n            value={model}\r\n            allowClear\r\n            onChange={onModelChange}\r\n          />\r\n        </div>\r\n        <div className={styles.filterItem}>\r\n          <div className={styles.filterItemTitle}>插件名称</div>\r\n          <Search\r\n            className={styles.filterItemControl}\r\n            placeholder=\"请输入插件名称\"\r\n            value={name}\r\n            onChange={(e) => {\r\n              setName(e.target.value);\r\n            }}\r\n            onSearch={onSearch}\r\n          />\r\n        </div>\r\n        <div className={styles.filterItem}>\r\n          <div className={styles.filterItemTitle}>函数描述</div>\r\n          <Search\r\n            className={styles.filterItemControl}\r\n            placeholder=\"请输入函数描述\"\r\n            value={pattern}\r\n            onChange={(e) => {\r\n              setPattern(e.target.value);\r\n            }}\r\n            onSearch={onSearch}\r\n          />\r\n        </div>\r\n        <div className={styles.filterItem}>\r\n          <div className={styles.filterItemTitle}>插件类型</div>\r\n          <Select\r\n            className={styles.filterItemControl}\r\n            placeholder=\"请选择插件类型\"\r\n            options={Object.keys(PLUGIN_TYPE_MAP).map((key) => ({\r\n              label: PLUGIN_TYPE_MAP[key],\r\n              value: key,\r\n            }))}\r\n            value={type}\r\n            allowClear\r\n            onChange={onTypeChange}\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className={styles.pluginList}>\r\n        <div className={styles.titleBar}>\r\n          <div className={styles.title}>插件列表</div>\r\n          <Button type=\"primary\" onClick={onCreatePlugin}>\r\n            <PlusOutlined />\r\n            新建插件\r\n          </Button>\r\n        </div>\r\n        <Table\r\n          columns={columns}\r\n          dataSource={data}\r\n          size=\"small\"\r\n          pagination={{ defaultPageSize: 20 }}\r\n          loading={loading}\r\n        />\r\n      </div>\r\n      {detailModalVisible && (\r\n        <DetailModal\r\n          detail={currentPluginDetail}\r\n          onSubmit={onSavePlugin}\r\n          onCancel={() => {\r\n            setDetailModalVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PluginManage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/ChatPlugin/index.tsx"}, "6": {"path": "/model/metric/edit/:metricId", "name": "metricEdit", "hideInMenu": true, "file": "@/pages/SemanticModel/Metric/Edit.tsx", "parentId": "ant-design-pro-layout", "id": "6", "absPath": "/model/metric/edit/:metricId", "__content": "import { message } from 'antd';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getMetricData } from '../service';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../data';\r\nimport MetricInfoCreateForm from './components/MetricInfoCreateForm';\r\nimport DetailContainer from '../components/DetailContainer';\r\nimport DetailSider from '../components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport { MetricSettingKey, MetricSettingWording } from './constants';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst MetricDetail: React.FC<Props> = () => {\r\n  const params: any = useParams();\r\n  const metricId = +params.metricId;\r\n  const modelId = +params.modelId;\r\n  const domainId = +params.domainId;\r\n  const [metircData, setMetircData] = useState<ISemantic.IMetricItem>();\r\n  const metricModel = useModel('SemanticModel.metricData');\r\n  const { setSelectMetric } = metricModel;\r\n  const [settingKey, setSettingKey] = useState<MetricSettingKey>(MetricSettingKey.BASIC);\r\n\r\n  useEffect(() => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    queryMetricData(metricId);\r\n  }, [metricId]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectMetric(undefined);\r\n    };\r\n  }, []);\r\n\r\n  const queryMetricData = async (metricId: number) => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getMetricData(metricId);\r\n    if (code === 200) {\r\n      setMetircData({ ...data });\r\n      setSelectMetric({ ...data });\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: MetricSettingKey.BASIC,\r\n      text: MetricSettingWording[MetricSettingKey.BASIC],\r\n    },\r\n    {\r\n      icon: <ConsoleSqlOutlined />,\r\n      key: MetricSettingKey.SQL_CONFIG,\r\n      text: MetricSettingWording[MetricSettingKey.SQL_CONFIG],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <Helmet\r\n        title={`${\r\n          metircData?.id ? `[指标]${metircData?.name}-${BASE_TITLE}` : `新建指标-${BASE_TITLE}`\r\n        }`}\r\n      />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={MetricSettingKey.BASIC}\r\n            menuList={settingList}\r\n            detailData={metircData}\r\n            onMenuKeyChange={(key: string) => {\r\n              setSettingKey(key);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <MetricInfoCreateForm\r\n            settingKey={settingKey}\r\n            metricItem={metircData}\r\n            modelId={metircData?.modelId || modelId}\r\n            domainId={metircData?.domainId || domainId}\r\n          />\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MetricDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/Edit.tsx"}, "7": {"path": "/model/", "name": "semanticModel", "access": "ADMIN", "file": "@/pages/SemanticModel/index.tsx", "parentId": "ant-design-pro-layout", "id": "7", "absPath": "/model/", "__content": "import { message } from 'antd';\r\nimport React, { useEffect } from 'react';\r\nimport { useParams, useModel, Outlet } from '@umijs/max';\r\nimport { ISemantic } from './data';\r\nimport { getDomainList, getModelDetail } from './service';\r\nimport PageBreadcrumb from './PageBreadcrumb';\r\n\r\ntype Props = {};\r\n\r\nconst SemanticModel: React.FC<Props> = ({}) => {\r\n  const params: any = useParams();\r\n  const domainId = params.domainId;\r\n  const modelId = params.modelId;\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const modelModel = useModel('SemanticModel.modelData');\r\n  const databaseModel = useModel('SemanticModel.databaseData');\r\n  const { setSelectDomain, setDomainList } = domainModel;\r\n  const { selectModel, setSelectModel } = modelModel;\r\n  const { MrefreshDatabaseList } = databaseModel;\r\n\r\n  const initSelectedDomain = (domainList: ISemantic.IDomainItem[]) => {\r\n    const targetNode = domainList.filter((item: any) => {\r\n      return `${item.id}` === domainId;\r\n    })[0];\r\n    if (!targetNode) {\r\n      const firstRootNode = domainList.filter((item: any) => {\r\n        return item.parentId === 0;\r\n      })[0];\r\n      if (firstRootNode) {\r\n        setSelectDomain(firstRootNode);\r\n      }\r\n    } else {\r\n      setSelectDomain(targetNode);\r\n    }\r\n  };\r\n\r\n  const initProjectTree = async () => {\r\n    const { code, data, msg } = await getDomainList();\r\n    if (code === 200) {\r\n      initSelectedDomain(data);\r\n      setDomainList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const initModelData = async () => {\r\n    const { code, data, msg } = await getModelDetail({ modelId });\r\n    if (code === 200) {\r\n      setSelectModel(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    initProjectTree();\r\n    MrefreshDatabaseList();\r\n    if (modelId && modelId !== selectModel) {\r\n      initModelData();\r\n    }\r\n\r\n    return () => {\r\n      setSelectDomain(undefined);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div>\r\n      <div style={{ background: '#fff' }}>\r\n        <PageBreadcrumb />\r\n      </div>\r\n      <div>\r\n        <Outlet />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SemanticModel;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/index.tsx"}, "8": {"path": "/model/", "redirect": "/model/domain", "parentId": "7", "id": "8", "absPath": "/model/"}, "9": {"path": "/model/domain/", "access": "ADMIN", "file": "@/pages/SemanticModel/OverviewContainer.tsx", "parentId": "7", "id": "9", "absPath": "/model/domain/", "__content": "import React, { useEffect, useState } from 'react';\r\nimport { useParams, useModel, Outlet } from '@umijs/max';\r\nimport DomainListTree from './components/DomainList';\r\nimport styles from './components/style.less';\r\nimport { LeftOutlined, RightOutlined } from '@ant-design/icons';\r\nimport { ISemantic } from './data';\r\nimport { toDomainList } from '@/pages/SemanticModel/utils';\r\n\r\ntype Props = {};\r\n\r\nconst OverviewContainer: React.FC<Props> = ({}) => {\r\n  const defaultTabKey = 'overview';\r\n  const params: any = useParams();\r\n  const domainId = params.domainId;\r\n  const modelId = params.modelId;\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const modelModel = useModel('SemanticModel.modelData');\r\n  const { setSelectDomain, selectDomainId } = domainModel;\r\n  const { setSelectModel, setModelTableHistoryParams, MrefreshModelList } = modelModel;\r\n  const menuKey = params.menuKey ? params.menuKey : !Number(modelId) ? defaultTabKey : '';\r\n  const [collapsedState, setCollapsedState] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (!selectDomainId || `${domainId}` === `${selectDomainId}`) {\r\n      return;\r\n    }\r\n    toDomainList(selectDomainId, menuKey);\r\n  }, [selectDomainId]);\r\n\r\n  const cleanModelInfo = (domainId: number) => {\r\n    toDomainList(domainId, defaultTabKey);\r\n    setSelectModel(undefined);\r\n  };\r\n\r\n  const handleCollapsedBtn = () => {\r\n    setCollapsedState(!collapsedState);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!selectDomainId) {\r\n      return;\r\n    }\r\n    queryModelList();\r\n  }, [selectDomainId]);\r\n\r\n  const queryModelList = async () => {\r\n    await MrefreshModelList(selectDomainId);\r\n  };\r\n\r\n  return (\r\n    <div className={styles.projectBody}>\r\n      <div className={styles.projectManger}>\r\n        <div className={`${styles.sider} ${!collapsedState ? styles.siderCollapsed : ''}`}>\r\n          <div className={styles.treeContainer}>\r\n            <DomainListTree\r\n              onTreeSelected={(domainData: ISemantic.IDomainItem) => {\r\n                const { id } = domainData;\r\n                cleanModelInfo(id);\r\n                setSelectDomain(domainData);\r\n                setModelTableHistoryParams({\r\n                  [id]: {},\r\n                });\r\n              }}\r\n            />\r\n          </div>\r\n\r\n          <div\r\n            className={styles.siderCollapsedButton}\r\n            onClick={() => {\r\n              handleCollapsedBtn();\r\n            }}\r\n          >\r\n            {collapsedState ? <LeftOutlined /> : <RightOutlined />}\r\n          </div>\r\n        </div>\r\n        <div className={styles.content}>\r\n          <Outlet />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default OverviewContainer;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/OverviewContainer.tsx"}, "10": {"path": "/model/domain/:domainId", "file": "@/pages/SemanticModel/DomainManager.tsx", "parentId": "9", "id": "10", "absPath": "/model/domain/:domainId", "__content": "import React, { useState } from 'react';\r\nimport { useParams, useModel } from '@umijs/max';\r\nimport DomainManagerTab from './components/DomainManagerTab';\r\nimport { toDomainList } from '@/pages/SemanticModel/utils';\r\n\r\ntype Props = {};\r\n\r\nconst DomainManager: React.FC<Props> = ({}) => {\r\n  const defaultTabKey = 'overview';\r\n  const params: any = useParams();\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n\r\n  const { selectDomainId } = domainModel;\r\n  const menuKey = params.menuKey ? params.menuKey : defaultTabKey;\r\n\r\n  const [activeKey, setActiveKey] = useState<string>(menuKey);\r\n\r\n  return (\r\n    <DomainManagerTab\r\n      activeKey={activeKey}\r\n      onMenuChange={(menuKey) => {\r\n        setActiveKey(menuKey);\r\n        toDomainList(selectDomainId, menuKey);\r\n      }}\r\n    />\r\n  );\r\n};\r\n\r\nexport default DomainManager;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/DomainManager.tsx"}, "11": {"path": "/model/domain/:domainId/:menuKey", "file": "@/pages/SemanticModel/DomainManager.tsx", "parentId": "10", "id": "11", "absPath": "/model/domain/:domainId/:menuKey", "__content": "import React, { useState } from 'react';\r\nimport { useParams, useModel } from '@umijs/max';\r\nimport DomainManagerTab from './components/DomainManagerTab';\r\nimport { toDomainList } from '@/pages/SemanticModel/utils';\r\n\r\ntype Props = {};\r\n\r\nconst DomainManager: React.FC<Props> = ({}) => {\r\n  const defaultTabKey = 'overview';\r\n  const params: any = useParams();\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n\r\n  const { selectDomainId } = domainModel;\r\n  const menuKey = params.menuKey ? params.menuKey : defaultTabKey;\r\n\r\n  const [activeKey, setActiveKey] = useState<string>(menuKey);\r\n\r\n  return (\r\n    <DomainManagerTab\r\n      activeKey={activeKey}\r\n      onMenuChange={(menuKey) => {\r\n        setActiveKey(menuKey);\r\n        toDomainList(selectDomainId, menuKey);\r\n      }}\r\n    />\r\n  );\r\n};\r\n\r\nexport default DomainManager;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/DomainManager.tsx"}, "12": {"path": "/model/domain/manager/:domainId/:modelId", "file": "@/pages/SemanticModel/ModelManager.tsx", "parentId": "9", "id": "12", "absPath": "/model/domain/manager/:domainId/:modelId", "__content": "import React, { useEffect, useState } from 'react';\r\nimport { history, useParams, useModel } from '@umijs/max';\r\nimport ModelManagerTab from './components/ModelManagerTab';\r\nimport { toModelList } from '@/pages/SemanticModel/utils';\r\n\r\ntype Props = {};\r\n\r\nconst ModelManager: React.FC<Props> = ({}) => {\r\n  const defaultTabKey = 'overview';\r\n  const params: any = useParams();\r\n  const modelId = params.modelId;\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const modelModel = useModel('SemanticModel.modelData');\r\n  const dimensionModel = useModel('SemanticModel.dimensionData');\r\n  const metricModel = useModel('SemanticModel.metricData');\r\n  const { selectDomainId } = domainModel;\r\n  const { selectModelId, modelList } = modelModel;\r\n  const { MrefreshDimensionList } = dimensionModel;\r\n  const { MrefreshMetricList } = metricModel;\r\n  const menuKey = params.menuKey ? params.menuKey : !Number(modelId) ? defaultTabKey : '';\r\n  const [activeKey, setActiveKey] = useState<string>(menuKey);\r\n\r\n  const initModelConfig = () => {\r\n    const currentMenuKey = menuKey === defaultTabKey ? '' : menuKey;\r\n    toModelList(selectDomainId, selectModelId!, currentMenuKey);\r\n    setActiveKey(currentMenuKey);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!selectModelId || `${selectModelId}` === `${modelId}`) {\r\n      return;\r\n    }\r\n    initModelConfig();\r\n    MrefreshDimensionList({ modelId: selectModelId });\r\n    MrefreshMetricList({ modelId: selectModelId });\r\n  }, [selectModelId]);\r\n\r\n  return (\r\n    <ModelManagerTab\r\n      activeKey={activeKey}\r\n      modelList={modelList}\r\n      onMenuChange={(menuKey) => {\r\n        setActiveKey(menuKey);\r\n        toModelList(selectDomainId, selectModelId!, menuKey);\r\n      }}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ModelManager;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/ModelManager.tsx"}, "13": {"path": "/model/domain/manager/:domainId/:modelId/:menuKey", "file": "@/pages/SemanticModel/ModelManager.tsx", "parentId": "12", "id": "13", "absPath": "/model/domain/manager/:domainId/:modelId/:menuKey", "__content": "import React, { useEffect, useState } from 'react';\r\nimport { history, useParams, useModel } from '@umijs/max';\r\nimport ModelManagerTab from './components/ModelManagerTab';\r\nimport { toModelList } from '@/pages/SemanticModel/utils';\r\n\r\ntype Props = {};\r\n\r\nconst ModelManager: React.FC<Props> = ({}) => {\r\n  const defaultTabKey = 'overview';\r\n  const params: any = useParams();\r\n  const modelId = params.modelId;\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const modelModel = useModel('SemanticModel.modelData');\r\n  const dimensionModel = useModel('SemanticModel.dimensionData');\r\n  const metricModel = useModel('SemanticModel.metricData');\r\n  const { selectDomainId } = domainModel;\r\n  const { selectModelId, modelList } = modelModel;\r\n  const { MrefreshDimensionList } = dimensionModel;\r\n  const { MrefreshMetricList } = metricModel;\r\n  const menuKey = params.menuKey ? params.menuKey : !Number(modelId) ? defaultTabKey : '';\r\n  const [activeKey, setActiveKey] = useState<string>(menuKey);\r\n\r\n  const initModelConfig = () => {\r\n    const currentMenuKey = menuKey === defaultTabKey ? '' : menuKey;\r\n    toModelList(selectDomainId, selectModelId!, currentMenuKey);\r\n    setActiveKey(currentMenuKey);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!selectModelId || `${selectModelId}` === `${modelId}`) {\r\n      return;\r\n    }\r\n    initModelConfig();\r\n    MrefreshDimensionList({ modelId: selectModelId });\r\n    MrefreshMetricList({ modelId: selectModelId });\r\n  }, [selectModelId]);\r\n\r\n  return (\r\n    <ModelManagerTab\r\n      activeKey={activeKey}\r\n      modelList={modelList}\r\n      onMenuChange={(menuKey) => {\r\n        setActiveKey(menuKey);\r\n        toModelList(selectDomainId, selectModelId!, menuKey);\r\n      }}\r\n    />\r\n  );\r\n};\r\n\r\nexport default ModelManager;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/ModelManager.tsx"}, "14": {"path": "/model/dataset/:domainId/:datasetId", "file": "@/pages/SemanticModel/View/components/Detail.tsx", "parentId": "7", "id": "14", "absPath": "/model/dataset/:domainId/:datasetId", "__content": "import { message, Form } from 'antd';\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../../data';\r\nimport { getAllModelByDomainId, getDataSetDetail } from '../../service';\r\nimport DetailContainer from '@/pages/SemanticModel/components/DetailContainer';\r\nimport DetailSider from '@/pages/SemanticModel/components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport DatasetCreateForm from './DatasetCreateForm';\r\nimport DetailFormWrapper from '@/pages/SemanticModel/components/DetailContainer/DetailFormWrapper';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst DataSetDetail: React.FC<Props> = () => {\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: 'basic',\r\n      text: '基本信息',\r\n    },\r\n    {\r\n      icon: <ConsoleSqlOutlined />,\r\n      key: 'relation',\r\n      text: '关联信息',\r\n    },\r\n  ];\r\n  const params: any = useParams();\r\n  const detailId = params.datasetId;\r\n  const menuKey = params.menuKey;\r\n  const [detailData, setDetailData] = useState<ISemantic.IDatasetItem>();\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const { selectDomainId, setSelectDataSet } = domainModel;\r\n  const [modelList, setModelList] = useState<ISemantic.IModelItem[]>([]);\r\n  const [activeMenu, setActiveMenu] = useState<any>(() => {\r\n    if (menuKey) {\r\n      const target = settingList.find((item) => item.key === menuKey);\r\n      if (target) {\r\n        return target;\r\n      }\r\n    }\r\n\r\n    return settingList[0];\r\n  });\r\n  const detailFormRef = useRef<any>();\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectDataSet(undefined);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!detailId) {\r\n      return;\r\n    }\r\n    queryDetailData(detailId);\r\n  }, [detailId]);\r\n\r\n  useEffect(() => {\r\n    if (!selectDomainId) {\r\n      return;\r\n    }\r\n    queryDomainAllModel();\r\n  }, [selectDomainId]);\r\n\r\n  const queryDomainAllModel = async () => {\r\n    const { code, data, msg } = await getAllModelByDomainId(selectDomainId);\r\n    if (code === 200) {\r\n      setModelList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const queryDetailData = async (id: number) => {\r\n    const { code, data, msg } = await getDataSetDetail(id);\r\n    if (code === 200) {\r\n      setDetailData(data);\r\n      setSelectDataSet(data);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet title={`[数据集]${detailData?.name}-${BASE_TITLE}`} />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={activeMenu.key}\r\n            menuList={settingList}\r\n            detailData={detailData}\r\n            onMenuKeyChange={(key: string, menu) => {\r\n              // setSettingKey(key);\r\n              setActiveMenu(menu);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <DetailFormWrapper\r\n            currentMenu={activeMenu}\r\n            onSave={() => {\r\n              detailFormRef.current.onSave();\r\n            }}\r\n          >\r\n            <DatasetCreateForm\r\n              ref={detailFormRef}\r\n              activeKey={activeMenu.key}\r\n              domainId={selectDomainId}\r\n              datasetItem={detailData}\r\n              modelList={modelList}\r\n            />\r\n          </DetailFormWrapper>\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DataSetDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/View/components/Detail.tsx"}, "15": {"path": "/model/dataset/:domainId/:datasetId/:menuKey", "file": "@/pages/SemanticModel/View/components/Detail.tsx", "parentId": "14", "id": "15", "absPath": "/model/dataset/:domainId/:datasetId/:menuKey", "__content": "import { message, Form } from 'antd';\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../../data';\r\nimport { getAllModelByDomainId, getDataSetDetail } from '../../service';\r\nimport DetailContainer from '@/pages/SemanticModel/components/DetailContainer';\r\nimport DetailSider from '@/pages/SemanticModel/components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport DatasetCreateForm from './DatasetCreateForm';\r\nimport DetailFormWrapper from '@/pages/SemanticModel/components/DetailContainer/DetailFormWrapper';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst DataSetDetail: React.FC<Props> = () => {\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: 'basic',\r\n      text: '基本信息',\r\n    },\r\n    {\r\n      icon: <ConsoleSqlOutlined />,\r\n      key: 'relation',\r\n      text: '关联信息',\r\n    },\r\n  ];\r\n  const params: any = useParams();\r\n  const detailId = params.datasetId;\r\n  const menuKey = params.menuKey;\r\n  const [detailData, setDetailData] = useState<ISemantic.IDatasetItem>();\r\n  const domainModel = useModel('SemanticModel.domainData');\r\n  const { selectDomainId, setSelectDataSet } = domainModel;\r\n  const [modelList, setModelList] = useState<ISemantic.IModelItem[]>([]);\r\n  const [activeMenu, setActiveMenu] = useState<any>(() => {\r\n    if (menuKey) {\r\n      const target = settingList.find((item) => item.key === menuKey);\r\n      if (target) {\r\n        return target;\r\n      }\r\n    }\r\n\r\n    return settingList[0];\r\n  });\r\n  const detailFormRef = useRef<any>();\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectDataSet(undefined);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!detailId) {\r\n      return;\r\n    }\r\n    queryDetailData(detailId);\r\n  }, [detailId]);\r\n\r\n  useEffect(() => {\r\n    if (!selectDomainId) {\r\n      return;\r\n    }\r\n    queryDomainAllModel();\r\n  }, [selectDomainId]);\r\n\r\n  const queryDomainAllModel = async () => {\r\n    const { code, data, msg } = await getAllModelByDomainId(selectDomainId);\r\n    if (code === 200) {\r\n      setModelList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const queryDetailData = async (id: number) => {\r\n    const { code, data, msg } = await getDataSetDetail(id);\r\n    if (code === 200) {\r\n      setDetailData(data);\r\n      setSelectDataSet(data);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet title={`[数据集]${detailData?.name}-${BASE_TITLE}`} />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={activeMenu.key}\r\n            menuList={settingList}\r\n            detailData={detailData}\r\n            onMenuKeyChange={(key: string, menu) => {\r\n              // setSettingKey(key);\r\n              setActiveMenu(menu);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <DetailFormWrapper\r\n            currentMenu={activeMenu}\r\n            onSave={() => {\r\n              detailFormRef.current.onSave();\r\n            }}\r\n          >\r\n            <DatasetCreateForm\r\n              ref={detailFormRef}\r\n              activeKey={activeMenu.key}\r\n              domainId={selectDomainId}\r\n              datasetItem={detailData}\r\n              modelList={modelList}\r\n            />\r\n          </DetailFormWrapper>\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DataSetDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/View/components/Detail.tsx"}, "16": {"path": "/model/metric/:domainId/:modelId/:metricId", "file": "@/pages/SemanticModel/Metric/Edit.tsx", "parentId": "7", "id": "16", "absPath": "/model/metric/:domainId/:modelId/:metricId", "__content": "import { message } from 'antd';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getMetricData } from '../service';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../data';\r\nimport MetricInfoCreateForm from './components/MetricInfoCreateForm';\r\nimport DetailContainer from '../components/DetailContainer';\r\nimport DetailSider from '../components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport { MetricSettingKey, MetricSettingWording } from './constants';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst MetricDetail: React.FC<Props> = () => {\r\n  const params: any = useParams();\r\n  const metricId = +params.metricId;\r\n  const modelId = +params.modelId;\r\n  const domainId = +params.domainId;\r\n  const [metircData, setMetircData] = useState<ISemantic.IMetricItem>();\r\n  const metricModel = useModel('SemanticModel.metricData');\r\n  const { setSelectMetric } = metricModel;\r\n  const [settingKey, setSettingKey] = useState<MetricSettingKey>(MetricSettingKey.BASIC);\r\n\r\n  useEffect(() => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    queryMetricData(metricId);\r\n  }, [metricId]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectMetric(undefined);\r\n    };\r\n  }, []);\r\n\r\n  const queryMetricData = async (metricId: number) => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getMetricData(metricId);\r\n    if (code === 200) {\r\n      setMetircData({ ...data });\r\n      setSelectMetric({ ...data });\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: MetricSettingKey.BASIC,\r\n      text: MetricSettingWording[MetricSettingKey.BASIC],\r\n    },\r\n    {\r\n      icon: <ConsoleSqlOutlined />,\r\n      key: MetricSettingKey.SQL_CONFIG,\r\n      text: MetricSettingWording[MetricSettingKey.SQL_CONFIG],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <Helmet\r\n        title={`${\r\n          metircData?.id ? `[指标]${metircData?.name}-${BASE_TITLE}` : `新建指标-${BASE_TITLE}`\r\n        }`}\r\n      />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={MetricSettingKey.BASIC}\r\n            menuList={settingList}\r\n            detailData={metircData}\r\n            onMenuKeyChange={(key: string) => {\r\n              setSettingKey(key);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <MetricInfoCreateForm\r\n            settingKey={settingKey}\r\n            metricItem={metircData}\r\n            modelId={metircData?.modelId || modelId}\r\n            domainId={metircData?.domainId || domainId}\r\n          />\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MetricDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/Edit.tsx"}, "17": {"path": "/model/dimension/:domainId/:modelId/:dimensionId", "file": "@/pages/SemanticModel/Dimension/Detail.tsx", "parentId": "7", "id": "17", "absPath": "/model/dimension/:domainId/:modelId/:dimensionId", "__content": "import { message } from 'antd';\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../data';\r\nimport { getDimensionList } from '../service';\r\nimport DetailContainer from '@/pages/SemanticModel/components/DetailContainer';\r\nimport DetailSider from '@/pages/SemanticModel/components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport DimensionInfoForm from '../components/DimensionInfoForm';\r\nimport DetailFormWrapper from '@/pages/SemanticModel/components/DetailContainer/DetailFormWrapper';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst DataSetDetail: React.FC<Props> = () => {\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: 'basic',\r\n      text: '基本信息',\r\n    },\r\n  ];\r\n  const params: any = useParams();\r\n  const detailId = params.dimensionId;\r\n  const modelId = params.modelId;\r\n  const domainId = params.domainId;\r\n  const menuKey = params.menuKey;\r\n  const [detailData, setDetailData] = useState<ISemantic.IDimensionItem>();\r\n  const dimensionModel = useModel('SemanticModel.dimensionData');\r\n  const { setSelectDimension } = dimensionModel;\r\n  const [activeMenu, setActiveMenu] = useState<any>(() => {\r\n    if (menuKey) {\r\n      const target = settingList.find((item) => item.key === menuKey);\r\n      if (target) {\r\n        return target;\r\n      }\r\n    }\r\n\r\n    return settingList[0];\r\n  });\r\n  const detailFormRef = useRef<any>();\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectDimension(undefined);\r\n    };\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (!detailId) {\r\n      return;\r\n    }\r\n    queryDetailData(detailId);\r\n  }, [detailId]);\r\n\r\n  const queryDetailData = async (id: number) => {\r\n    const { code, data, msg } = await getDimensionList({ ids: [id] });\r\n    if (code === 200) {\r\n      const target = data?.list?.[0];\r\n      setDetailData(target);\r\n      setSelectDimension(target);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Helmet title={`[数据集]${detailData?.name}-${BASE_TITLE}`} />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={activeMenu.key}\r\n            menuList={settingList}\r\n            detailData={detailData}\r\n            onMenuKeyChange={(key: string, menu) => {\r\n              setActiveMenu(menu);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <DetailFormWrapper\r\n            currentMenu={activeMenu}\r\n            onSave={() => {\r\n              detailFormRef.current.onSave();\r\n            }}\r\n          >\r\n            <DimensionInfoForm\r\n              ref={detailFormRef}\r\n              modelId={modelId}\r\n              domainId={domainId}\r\n              dimensionItem={detailData}\r\n            />\r\n          </DetailFormWrapper>\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default DataSetDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Dimension/Detail.tsx"}, "18": {"path": "/metric", "name": "metric", "access": "ADMIN", "file": "@/pages/SemanticModel/Metric/index.tsx", "parentId": "ant-design-pro-layout", "id": "18", "absPath": "/metric", "__content": "import React from 'react';\r\nimport { Outlet } from '@umijs/max';\r\n\r\nconst market: React.FC = () => {\r\n  return (\r\n    <>\r\n      <Outlet />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default market;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/index.tsx"}, "19": {"path": "/metric", "redirect": "/metric/market", "parentId": "18", "id": "19", "absPath": "/metric"}, "20": {"path": "/metric/market", "access": "ADMIN", "hideInMenu": true, "file": "@/pages/SemanticModel/Metric/Market.tsx", "parentId": "18", "id": "20", "absPath": "/metric/market", "__content": "import type { ActionType, ProColumns } from '@ant-design/pro-components';\r\nimport { ProTable } from '@ant-design/pro-components';\r\nimport { message, Space, Popconfirm, Spin } from 'antd';\r\nimport MetricAddClass from './components/MetricAddClass';\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport { history, useModel } from '@umijs/max';\r\nimport { SENSITIVE_LEVEL_ENUM } from '../constant';\r\nimport {\r\n  queryMetric,\r\n  deleteMetric,\r\n  batchUpdateMetricStatus,\r\n  batchDownloadMetric,\r\n  batchMetricPublish,\r\n  batchMetricUnPublish,\r\n} from '../service';\r\nimport MetricFilter from './components/MetricFilter';\r\nimport MetricInfoCreateForm from '../components/MetricInfoCreateForm';\r\nimport MetricCardList from './components/MetricCardList';\r\nimport { StatusEnum } from '../enum';\r\nimport moment from 'moment';\r\nimport styles from './style.less';\r\nimport { ISemantic } from '../data';\r\nimport BatchCtrlDropDownButton from '@/components/BatchCtrlDropDownButton';\r\nimport { ColumnsConfig } from '../components/TableColumnRender';\r\n\r\ntype Props = {};\r\n\r\ntype QueryMetricListParams = {\r\n  id?: string;\r\n  name?: string;\r\n  bizName?: string;\r\n  sensitiveLevel?: string;\r\n  type?: string;\r\n  [key: string]: any;\r\n};\r\n\r\nconst ClassMetricTable: React.FC<Props> = ({}) => {\r\n  const { initialState = {} } = useModel('@@initialState');\r\n  const { currentUser = {} } = initialState as any;\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const defaultPagination = {\r\n    current: 1,\r\n    pageSize: 20,\r\n    total: 0,\r\n  };\r\n  const [pagination, setPagination] = useState(defaultPagination);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [dataSource, setDataSource] = useState<ISemantic.IMetricItem[]>([]);\r\n  const [metricItem, setMetricItem] = useState<ISemantic.IMetricItem>();\r\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\r\n  const [filterParams, setFilterParams] = useState<Record<string, any>>({\r\n    showType: localStorage.getItem('metricMarketShowType') === '1' ? true : false,\r\n  });\r\n\r\n  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);\r\n\r\n  const [hasAllPermission, setHasAllPermission] = useState<boolean>(true);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n\r\n  const [addClassVisible, setAddClassVisible] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    queryMetricList(filterParams);\r\n  }, []);\r\n\r\n  const queryBatchUpdateStatus = async (ids: React.Key[], status: StatusEnum) => {\r\n    if (Array.isArray(ids) && ids.length === 0) {\r\n      return;\r\n    }\r\n    const { code, msg } = await batchUpdateMetricStatus({\r\n      ids,\r\n      status,\r\n    });\r\n    if (code === 200) {\r\n      queryMetricList(filterParams);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const queryMetricList = async (params: QueryMetricListParams = {}, disabledLoading = false) => {\r\n    if (!disabledLoading) {\r\n      setLoading(true);\r\n    }\r\n    const { code, data, msg } = await queryMetric({\r\n      ...pagination,\r\n      ...params,\r\n      createdBy: params.onlyShowMe ? currentUser.name : null,\r\n      pageSize: params.showType ? 100 : params.pageSize || pagination.pageSize,\r\n      isPublish: 1,\r\n    });\r\n    setLoading(false);\r\n    const { list, pageSize, pageNum, total } = data || {};\r\n    let resData: any = {};\r\n    if (code === 200) {\r\n      if (!params.showType) {\r\n        setPagination({\r\n          ...pagination,\r\n          pageSize: Math.min(pageSize, 100),\r\n          current: pageNum,\r\n          total,\r\n        });\r\n      }\r\n\r\n      setDataSource(list);\r\n      resData = {\r\n        data: list || [],\r\n        success: true,\r\n      };\r\n    } else {\r\n      message.error(msg);\r\n      setDataSource([]);\r\n      resData = {\r\n        data: [],\r\n        total: 0,\r\n        success: false,\r\n      };\r\n    }\r\n    return resData;\r\n  };\r\n\r\n  const deleteMetricQuery = async (id: number) => {\r\n    const { code, msg } = await deleteMetric(id);\r\n    if (code === 200) {\r\n      setMetricItem(undefined);\r\n      queryMetricList(filterParams);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const downloadMetricQuery = async (\r\n    ids: React.Key[],\r\n    dateStringList: string[],\r\n    pickerType: string,\r\n  ) => {\r\n    if (Array.isArray(ids) && ids.length > 0) {\r\n      setDownloadLoading(true);\r\n      const [startDate, endDate] = dateStringList;\r\n      await batchDownloadMetric({\r\n        metricIds: ids,\r\n        dateInfo: {\r\n          dateMode: 'BETWEEN',\r\n          startDate,\r\n          endDate,\r\n          period: pickerType.toUpperCase(),\r\n        },\r\n      });\r\n      setDownloadLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleMetricEdit = (metricItem: ISemantic.IMetricItem) => {\r\n    history.push(`/model/metric/edit/${metricItem.id}`);\r\n  };\r\n\r\n  const queryBatchUpdatePublish = async (ids: React.Key[], status: boolean) => {\r\n    if (Array.isArray(ids) && ids.length === 0) {\r\n      return;\r\n    }\r\n    const queryPublish = status ? batchMetricPublish : batchMetricUnPublish;\r\n    const { code, msg } = await queryPublish({\r\n      ids,\r\n    });\r\n    if (code === 200) {\r\n      queryMetricList(filterParams);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const columnsConfig = ColumnsConfig();\r\n\r\n  const columns: ProColumns[] = [\r\n    {\r\n      dataIndex: 'id',\r\n      title: 'ID',\r\n      width: 80,\r\n      fixed: 'left',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'name',\r\n      title: '指标',\r\n      width: 280,\r\n      fixed: 'left',\r\n      render: columnsConfig.indicatorInfo.render,\r\n    },\r\n    {\r\n      dataIndex: 'sensitiveLevel',\r\n      title: '敏感度',\r\n      valueEnum: SENSITIVE_LEVEL_ENUM,\r\n      render: columnsConfig.sensitiveLevel.render,\r\n    },\r\n    {\r\n      dataIndex: 'description',\r\n      title: '描述',\r\n      search: false,\r\n      width: 300,\r\n      render: columnsConfig.description.render,\r\n    },\r\n    {\r\n      dataIndex: 'status',\r\n      title: '状态',\r\n      width: 180,\r\n      search: false,\r\n      render: columnsConfig.state.render,\r\n    },\r\n    {\r\n      dataIndex: 'createdBy',\r\n      title: '创建人',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'updatedAt',\r\n      title: '更新时间',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value && value !== '-' ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';\r\n      },\r\n    },\r\n    {\r\n      title: '操作',\r\n      dataIndex: 'x',\r\n      valueType: 'option',\r\n      width: 180,\r\n      render: (_, record) => {\r\n        if (record.hasAdminRes) {\r\n          return (\r\n            <Space>\r\n              <a\r\n                key=\"metricEditBtn\"\r\n                onClick={() => {\r\n                  handleMetricEdit(record);\r\n                }}\r\n              >\r\n                编辑\r\n              </a>\r\n              {record.isPublish ? (\r\n                <a\r\n                  key=\"metricUnPublishBtn\"\r\n                  onClick={() => {\r\n                    queryBatchUpdatePublish([record.id], false);\r\n                  }}\r\n                >\r\n                  下架\r\n                </a>\r\n              ) : (\r\n                <a\r\n                  key=\"metricPublishBtn\"\r\n                  onClick={() => {\r\n                    queryBatchUpdatePublish([record.id], true);\r\n                  }}\r\n                >\r\n                  发布\r\n                </a>\r\n              )}\r\n\r\n              <Popconfirm\r\n                title=\"确认删除？\"\r\n                okText=\"是\"\r\n                cancelText=\"否\"\r\n                onConfirm={async () => {\r\n                  deleteMetricQuery(record.id);\r\n                }}\r\n              >\r\n                <a\r\n                  key=\"metricDeleteBtn\"\r\n                  onClick={() => {\r\n                    setMetricItem(record);\r\n                  }}\r\n                >\r\n                  删除\r\n                </a>\r\n              </Popconfirm>\r\n            </Space>\r\n          );\r\n        } else {\r\n          return <></>;\r\n        }\r\n      },\r\n    },\r\n  ];\r\n\r\n  const handleFilterChange = async (filterParams: {\r\n    key: string;\r\n    sensitiveLevel: string[];\r\n    showFilter: string[];\r\n    type: string;\r\n  }) => {\r\n    const { sensitiveLevel, type, showFilter } = filterParams;\r\n    const params: QueryMetricListParams = { ...filterParams };\r\n    const sensitiveLevelValue = sensitiveLevel?.[0];\r\n    const showFilterValue = showFilter?.[0];\r\n    const typeValue = type?.[0];\r\n    showFilterValue ? (params[showFilterValue] = true) : null;\r\n    params.sensitiveLevel = sensitiveLevelValue;\r\n    params.type = typeValue;\r\n    setFilterParams(params);\r\n    await queryMetricList(\r\n      {\r\n        ...params,\r\n        ...defaultPagination,\r\n      },\r\n      filterParams.key ? false : true,\r\n    );\r\n  };\r\n\r\n  const rowSelection = {\r\n    onChange: (selectedRowKeys: React.Key[]) => {\r\n      const permissionList: boolean[] = [];\r\n      selectedRowKeys.forEach((id: React.Key) => {\r\n        const target = dataSource.find((item) => {\r\n          return item.id === id;\r\n        });\r\n        if (target) {\r\n          permissionList.push(target.hasAdminRes);\r\n        }\r\n      });\r\n      if (permissionList.includes(false)) {\r\n        setHasAllPermission(false);\r\n      } else {\r\n        setHasAllPermission(true);\r\n      }\r\n      setSelectedRowKeys(selectedRowKeys);\r\n    },\r\n  };\r\n\r\n  const onMenuClick = (key: string) => {\r\n    switch (key) {\r\n      case 'batchStart':\r\n        queryBatchUpdateStatus(selectedRowKeys, StatusEnum.ONLINE);\r\n        break;\r\n      case 'batchStop':\r\n        queryBatchUpdateStatus(selectedRowKeys, StatusEnum.OFFLINE);\r\n        break;\r\n      case 'batchAddClass':\r\n        setAddClassVisible(true);\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className={styles.metricFilterWrapper}>\r\n        <MetricFilter\r\n          initFilterValues={filterParams}\r\n          extraNode={\r\n            <BatchCtrlDropDownButton\r\n              key=\"ctrlBtnList\"\r\n              downloadLoading={downloadLoading}\r\n              onDeleteConfirm={() => {\r\n                queryBatchUpdateStatus(selectedRowKeys, StatusEnum.DELETED);\r\n              }}\r\n              disabledList={hasAllPermission ? [] : ['batchStart', 'batchStop', 'batchDelete']}\r\n              extenderList={['batchAddClass']}\r\n              onMenuClick={onMenuClick}\r\n              onDownloadDateRangeChange={(searchDateRange, pickerType) => {\r\n                downloadMetricQuery(selectedRowKeys, searchDateRange, pickerType);\r\n              }}\r\n            />\r\n          }\r\n          onFiltersChange={(_, values) => {\r\n            if (_.showType !== undefined) {\r\n              setLoading(true);\r\n              setDataSource([]);\r\n            }\r\n            handleFilterChange(values);\r\n          }}\r\n        />\r\n      </div>\r\n      <>\r\n        {filterParams.showType ? (\r\n          <Spin spinning={loading} style={{ minHeight: 500 }}>\r\n            <MetricCardList\r\n              metricList={dataSource}\r\n              disabledEdit={true}\r\n              onMetricChange={(metricItem: ISemantic.IMetricItem) => {\r\n                history.push(`/metric/detail/${metricItem.id}`);\r\n              }}\r\n              onDeleteBtnClick={(metricItem: ISemantic.IMetricItem) => {\r\n                deleteMetricQuery(metricItem.id);\r\n              }}\r\n              onEditBtnClick={(metricItem: ISemantic.IMetricItem) => {\r\n                setMetricItem(metricItem);\r\n                setCreateModalVisible(true);\r\n              }}\r\n            />\r\n          </Spin>\r\n        ) : (\r\n          <ProTable\r\n            className={`${styles.metricTable}`}\r\n            actionRef={actionRef}\r\n            rowKey=\"id\"\r\n            search={false}\r\n            dataSource={dataSource}\r\n            columns={columns}\r\n            pagination={pagination}\r\n            size=\"large\"\r\n            scroll={{ x: 1500 }}\r\n            tableAlertRender={() => {\r\n              return false;\r\n            }}\r\n            sticky={{ offsetHeader: 0 }}\r\n            rowSelection={{\r\n              type: 'checkbox',\r\n              ...rowSelection,\r\n            }}\r\n            loading={loading}\r\n            onChange={(data: any) => {\r\n              const { current, pageSize, total } = data;\r\n              const pagin = {\r\n                current,\r\n                pageSize,\r\n                total,\r\n              };\r\n              setPagination(pagin);\r\n              queryMetricList({ ...pagin, ...filterParams });\r\n            }}\r\n            options={false}\r\n          />\r\n        )}\r\n      </>\r\n\r\n      {createModalVisible && (\r\n        <MetricInfoCreateForm\r\n          createModalVisible={createModalVisible}\r\n          metricItem={metricItem}\r\n          onSubmit={() => {\r\n            setCreateModalVisible(false);\r\n            queryMetricList(filterParams);\r\n          }}\r\n          onCancel={() => {\r\n            setCreateModalVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {addClassVisible && (\r\n        <MetricAddClass\r\n          ids={selectedRowKeys as number[]}\r\n          createModalVisible={addClassVisible}\r\n          onCancel={() => {\r\n            setAddClassVisible(false);\r\n          }}\r\n          onSuccess={() => {\r\n            setAddClassVisible(false);\r\n            queryMetricList(filterParams);\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\nexport default ClassMetricTable;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/Market.tsx"}, "21": {"path": "/metric/detail/:metricId", "name": "metricDetail", "hideInMenu": true, "file": "@/pages/SemanticModel/Metric/Detail.tsx", "parentId": "18", "id": "21", "absPath": "/metric/detail/:metricId", "__content": "import { message, Ta<PERSON>, Button, Space } from 'antd';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getMetricData, getDimensionList, getDrillDownDimension } from '../service';\r\nimport { useParams, history, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport styles from './style.less';\r\nimport { ArrowLeftOutlined } from '@ant-design/icons';\r\nimport MetricTrendSection from '@/pages/SemanticModel/Metric/components/MetricTrendSection';\r\nimport { ISemantic } from '../data';\r\nimport MetricBasicInfo from './components/MetricBasicInfo';\r\nimport DimensionAndMetricRelationModal from '../components/DimensionAndMetricRelationModal';\r\nimport MetricInfoSider from './MetricInfoSider';\r\nimport type { TabsProps } from 'antd';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst MetricDetail: React.FC<Props> = () => {\r\n  const params: any = useParams();\r\n  const metricId = params.metricId;\r\n  const [metricRelationModalOpenState, setMetricRelationModalOpenState] = useState<boolean>(false);\r\n  const [metircData, setMetircData] = useState<ISemantic.IMetricItem>();\r\n  const [dimensionList, setDimensionList] = useState<ISemantic.IDimensionItem[]>([]);\r\n  const [drillDownDimension, setDrillDownDimension] = useState<ISemantic.IDrillDownDimensionItem[]>(\r\n    [],\r\n  );\r\n  const [relationDimensionOptions, setRelationDimensionOptions] = useState<\r\n    { value: string; label: string; modelId: number }[]\r\n  >([]);\r\n\r\n  useEffect(() => {\r\n    queryMetricData(metricId);\r\n    queryDrillDownDimension(metricId);\r\n  }, [metricId]);\r\n\r\n  const queryMetricData = async (metricId: string) => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getMetricData(metricId);\r\n    if (code === 200) {\r\n      setMetircData({ ...data });\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const queryDrillDownDimension = async (metricId: number) => {\r\n    const { code, data, msg } = await getDrillDownDimension(metricId);\r\n    if (code === 200 && Array.isArray(data)) {\r\n      setDrillDownDimension(data);\r\n      const ids = data.map((item) => item.dimensionId);\r\n      queryDimensionList(ids);\r\n      return data;\r\n    } else {\r\n      setDimensionList([]);\r\n      setRelationDimensionOptions([]);\r\n    }\r\n    if (code !== 200) {\r\n      message.error(msg);\r\n    }\r\n    return [];\r\n  };\r\n\r\n  const queryDimensionList = async (ids: number[]) => {\r\n    if (!(Array.isArray(ids) && ids.length > 0)) {\r\n      setRelationDimensionOptions([]);\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getDimensionList({ ids });\r\n    if (code === 200 && Array.isArray(data?.list)) {\r\n      setDimensionList(data.list);\r\n      setRelationDimensionOptions(\r\n        data.list.map((item: ISemantic.IMetricItem) => {\r\n          return { label: item.name, value: item.bizName, modelId: item.modelId };\r\n        }),\r\n      );\r\n      return data.list;\r\n    }\r\n    message.error(msg);\r\n    return [];\r\n  };\r\n\r\n  const tabItems: TabsProps['items'] = [\r\n    {\r\n      key: 'metricCaliberInput',\r\n      label: '基础信息',\r\n      children: <MetricBasicInfo metircData={metircData} />,\r\n    },\r\n    {\r\n      key: 'metricTrend',\r\n      label: '指标探索',\r\n      children: (\r\n        <MetricTrendSection\r\n          metircData={metircData}\r\n          relationDimensionOptions={relationDimensionOptions}\r\n          dimensionList={dimensionList}\r\n        />\r\n      ),\r\n    },\r\n\r\n    // {\r\n    //   key: 'metricDataRemark',\r\n    //   label: '备注',\r\n    //   children: <></>,\r\n    // },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <div className={styles.metricDetailWrapper}>\r\n        <div className={styles.metricDetail}>\r\n          <div className={styles.tabContainer}>\r\n            <Tabs\r\n              defaultActiveKey=\"metricCaliberInput\"\r\n              items={tabItems}\r\n              tabBarExtraContent={{\r\n                right: (\r\n                  <Button\r\n                    size=\"middle\"\r\n                    type=\"link\"\r\n                    key=\"backListBtn\"\r\n                    onClick={() => {\r\n                      history.push('/metric/market');\r\n                    }}\r\n                  >\r\n                    <Space>\r\n                      <ArrowLeftOutlined />\r\n                      返回列表页\r\n                    </Space>\r\n                  </Button>\r\n                ),\r\n              }}\r\n              size=\"large\"\r\n              className={styles.metricDetailTab}\r\n            />\r\n          </div>\r\n          <div className={styles.siderContainer}>\r\n            <MetricInfoSider\r\n              relationDimensionOptions={relationDimensionOptions}\r\n              metircData={metircData}\r\n              onDimensionRelationBtnClick={() => {\r\n                setMetricRelationModalOpenState(true);\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n        <DimensionAndMetricRelationModal\r\n          metricItem={metircData}\r\n          relationsInitialValue={drillDownDimension}\r\n          open={metricRelationModalOpenState}\r\n          onCancel={() => {\r\n            setMetricRelationModalOpenState(false);\r\n          }}\r\n          onSubmit={(relations) => {\r\n            queryMetricData(metricId);\r\n            queryDrillDownDimension(metricId);\r\n            setMetricRelationModalOpenState(false);\r\n          }}\r\n        />\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MetricDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/Detail.tsx"}, "22": {"path": "/metric/detail/edit/:metricId", "name": "metricDetail", "hideInMenu": true, "file": "@/pages/SemanticModel/Metric/Edit.tsx", "parentId": "18", "id": "22", "absPath": "/metric/detail/edit/:metricId", "__content": "import { message } from 'antd';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getMetricData } from '../service';\r\nimport { useParams, useModel, Helmet } from '@umijs/max';\r\nimport { BASE_TITLE } from '@/common/constants';\r\nimport { ISemantic } from '../data';\r\nimport MetricInfoCreateForm from './components/MetricInfoCreateForm';\r\nimport DetailContainer from '../components/DetailContainer';\r\nimport DetailSider from '../components/DetailContainer/DetailSider';\r\nimport { ProjectOutlined, ConsoleSqlOutlined } from '@ant-design/icons';\r\nimport { MetricSettingKey, MetricSettingWording } from './constants';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst MetricDetail: React.FC<Props> = () => {\r\n  const params: any = useParams();\r\n  const metricId = +params.metricId;\r\n  const modelId = +params.modelId;\r\n  const domainId = +params.domainId;\r\n  const [metircData, setMetircData] = useState<ISemantic.IMetricItem>();\r\n  const metricModel = useModel('SemanticModel.metricData');\r\n  const { setSelectMetric } = metricModel;\r\n  const [settingKey, setSettingKey] = useState<MetricSettingKey>(MetricSettingKey.BASIC);\r\n\r\n  useEffect(() => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    queryMetricData(metricId);\r\n  }, [metricId]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      setSelectMetric(undefined);\r\n    };\r\n  }, []);\r\n\r\n  const queryMetricData = async (metricId: number) => {\r\n    if (!metricId) {\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getMetricData(metricId);\r\n    if (code === 200) {\r\n      setMetircData({ ...data });\r\n      setSelectMetric({ ...data });\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const settingList = [\r\n    {\r\n      icon: <ProjectOutlined />,\r\n      key: MetricSettingKey.BASIC,\r\n      text: MetricSettingWording[MetricSettingKey.BASIC],\r\n    },\r\n    {\r\n      icon: <ConsoleSqlOutlined />,\r\n      key: MetricSettingKey.SQL_CONFIG,\r\n      text: MetricSettingWording[MetricSettingKey.SQL_CONFIG],\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <Helmet\r\n        title={`${\r\n          metircData?.id ? `[指标]${metircData?.name}-${BASE_TITLE}` : `新建指标-${BASE_TITLE}`\r\n        }`}\r\n      />\r\n      <DetailContainer\r\n        siderNode={\r\n          <DetailSider\r\n            menuKey={MetricSettingKey.BASIC}\r\n            menuList={settingList}\r\n            detailData={metircData}\r\n            onMenuKeyChange={(key: string) => {\r\n              setSettingKey(key);\r\n            }}\r\n          />\r\n        }\r\n        containerNode={\r\n          <MetricInfoCreateForm\r\n            settingKey={settingKey}\r\n            metricItem={metircData}\r\n            modelId={metircData?.modelId || modelId}\r\n            domainId={metircData?.domainId || domainId}\r\n          />\r\n        }\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default MetricDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Metric/Edit.tsx"}, "23": {"path": "/tag", "name": "tag", "hideInMenu": true, "file": "@/pages/SemanticModel/Insights/index.tsx", "parentId": "ant-design-pro-layout", "id": "23", "absPath": "/tag", "__content": "import React from 'react';\r\nimport { Outlet } from '@umijs/max';\r\n\r\nconst market: React.FC = () => {\r\n  return (\r\n    <>\r\n      <Outlet />\r\n    </>\r\n  );\r\n};\r\n\r\nexport default market;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Insights/index.tsx"}, "24": {"path": "/tag", "redirect": "/tag/market", "parentId": "23", "id": "24", "absPath": "/tag"}, "25": {"path": "/tag/market", "hideInMenu": true, "file": "@/pages/SemanticModel/Insights/Market.tsx", "parentId": "23", "id": "25", "absPath": "/tag/market", "__content": "import type { ActionType, ProColumns } from '@ant-design/pro-components';\r\nimport { ProTable } from '@ant-design/pro-components';\r\nimport { message, Space, Popconfirm } from 'antd';\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport { useModel } from '@umijs/max';\r\nimport { SENSITIVE_LEVEL_ENUM } from '../constant';\r\nimport { getTagList, deleteTag, batchDeleteTag, getTagObjectList } from '../service';\r\nimport TagFilter from './components/TagFilter';\r\nimport TagInfoCreateForm from './components/TagInfoCreateForm';\r\nimport moment from 'moment';\r\nimport styles from './style.less';\r\nimport { ISemantic } from '../data';\r\nimport BatchCtrlDropDownButton from '@/components/BatchCtrlDropDownButton';\r\nimport { ColumnsConfig } from '../components/TableColumnRender';\r\n\r\ntype Props = {};\r\n\r\ntype QueryMetricListParams = {\r\n  id?: string;\r\n  name?: string;\r\n  bizName?: string;\r\n  sensitiveLevel?: string;\r\n  type?: string;\r\n  [key: string]: any;\r\n};\r\n\r\nconst ClassMetricTable: React.FC<Props> = ({}) => {\r\n  const { initialState = {} } = useModel('@@initialState');\r\n  const { currentUser = {} } = initialState as any;\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const defaultPagination = {\r\n    current: 1,\r\n    pageSize: 20,\r\n    total: 0,\r\n  };\r\n  const [pagination, setPagination] = useState(defaultPagination);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [dataSource, setDataSource] = useState<ISemantic.ITagItem[]>([]);\r\n  const [tagItem, setTagItem] = useState<ISemantic.ITagItem>();\r\n  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);\r\n  const [filterParams, setFilterParams] = useState<Record<string, any>>({});\r\n\r\n  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);\r\n\r\n  const [hasAllPermission, setHasAllPermission] = useState<boolean>(true);\r\n\r\n  const [tagObjectList, setTagObjectList] = useState<ISemantic.ITagObjectItem[]>([]);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n\r\n  useEffect(() => {\r\n    queryTagObjectList();\r\n  }, []);\r\n\r\n  const queryTagObjectList = async () => {\r\n    const { code, msg, data } = await getTagObjectList({});\r\n    if (code === 200) {\r\n      setTagObjectList(data);\r\n      // const target = data[0];\r\n      // if (target) {\r\n      //   queryTagList({ ...filterParams, tagObjectId: target.id });\r\n      // }\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  // const getTagList = (ids: React.Key[])=>{\r\n  //   const filterItem = dataSource.filter((item)=>{\r\n  //     return ids.includes(item.id);\r\n  //   });\r\n  //   const dimension = {\r\n\r\n  //   }\r\n  //   filterItem.forEach((item)=>{\r\n\r\n  //   })\r\n  // }\r\n\r\n  const queryBatchDeleteTag = async (ids: React.Key[]) => {\r\n    if (Array.isArray(ids) && ids.length === 0) {\r\n      return;\r\n    }\r\n    const { code, msg } = await batchDeleteTag([\r\n      {\r\n        ids,\r\n      },\r\n    ]);\r\n    if (code === 200) {\r\n      queryTagList(filterParams);\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const queryTagList = async (params: QueryMetricListParams = {}, disabledLoading = false) => {\r\n    if (!disabledLoading) {\r\n      setLoading(true);\r\n    }\r\n    if (!params.tagObjectId) {\r\n      setLoading(false);\r\n      setDataSource([]);\r\n      return;\r\n    }\r\n    const { code, data, msg } = await getTagList({\r\n      ...pagination,\r\n      ...params,\r\n      createdBy: params.onlyShowMe ? currentUser.name : null,\r\n      pageSize: params.showType ? 100 : params.pageSize || pagination.pageSize,\r\n    });\r\n    setLoading(false);\r\n    const { list, pageSize, pageNum, total } = data || {};\r\n    let resData: any = {};\r\n    if (code === 200) {\r\n      if (!params.showType) {\r\n        setPagination({\r\n          ...pagination,\r\n          pageSize: Math.min(pageSize, 100),\r\n          current: pageNum,\r\n          total,\r\n        });\r\n      }\r\n\r\n      setDataSource(list);\r\n      resData = {\r\n        data: list || [],\r\n        success: true,\r\n      };\r\n    } else {\r\n      message.error(msg);\r\n      setDataSource([]);\r\n      resData = {\r\n        data: [],\r\n        total: 0,\r\n        success: false,\r\n      };\r\n    }\r\n    return resData;\r\n  };\r\n\r\n  const deleteMetricQuery = async (id: number) => {\r\n    const { code, msg } = await deleteTag(id);\r\n    if (code === 200) {\r\n      setTagItem(undefined);\r\n      queryTagList(filterParams);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const handleMetricEdit = (tagItem: ISemantic.ITagItem) => {\r\n    setTagItem(tagItem);\r\n    setCreateModalVisible(true);\r\n  };\r\n\r\n  const columnsConfig = ColumnsConfig({\r\n    indicatorInfo: {\r\n      url: '/tag/detail/:indicatorId',\r\n      starType: 'tag',\r\n    },\r\n  });\r\n\r\n  const columns: ProColumns[] = [\r\n    {\r\n      dataIndex: 'id',\r\n      title: 'ID',\r\n      width: 80,\r\n      fixed: 'left',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'name',\r\n      title: '标签',\r\n      width: 280,\r\n      fixed: 'left',\r\n      render: columnsConfig.indicatorInfo.render,\r\n    },\r\n    {\r\n      dataIndex: 'sensitiveLevel',\r\n      title: '敏感度',\r\n      width: 150,\r\n      valueEnum: SENSITIVE_LEVEL_ENUM,\r\n      render: columnsConfig.sensitiveLevel.render,\r\n    },\r\n\r\n    {\r\n      dataIndex: 'description',\r\n      title: '描述',\r\n      search: false,\r\n      width: 300,\r\n      render: columnsConfig.description.render,\r\n    },\r\n    // {\r\n    //   dataIndex: 'status',\r\n    //   title: '状态',\r\n    //   width: 180,\r\n    //   search: false,\r\n    //   render: columnsConfig.state.render,\r\n    // },\r\n    {\r\n      dataIndex: 'domainName',\r\n      title: '主题域',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'tagObjectName',\r\n      title: '标签对象',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'createdBy',\r\n      title: '创建人',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'updatedAt',\r\n      title: '更新时间',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value && value !== '-' ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';\r\n      },\r\n    },\r\n    {\r\n      title: '操作',\r\n      dataIndex: 'x',\r\n      valueType: 'option',\r\n      width: 180,\r\n      render: (_, record) => {\r\n        if (record.hasAdminRes) {\r\n          return (\r\n            <Space>\r\n              {/* <a\r\n                key=\"metricEditBtn\"\r\n                onClick={() => {\r\n                  handleMetricEdit(record);\r\n                }}\r\n              >\r\n                编辑\r\n              </a> */}\r\n\r\n              <Popconfirm\r\n                title=\"确认删除？\"\r\n                okText=\"是\"\r\n                cancelText=\"否\"\r\n                onConfirm={async () => {\r\n                  deleteMetricQuery(record.id);\r\n                }}\r\n              >\r\n                <a\r\n                  key=\"metricDeleteBtn\"\r\n                  onClick={() => {\r\n                    setTagItem(record);\r\n                  }}\r\n                >\r\n                  删除\r\n                </a>\r\n              </Popconfirm>\r\n            </Space>\r\n          );\r\n        } else {\r\n          return <></>;\r\n        }\r\n      },\r\n    },\r\n  ];\r\n\r\n  const handleFilterChange = async (filterParams: {\r\n    key: string;\r\n    sensitiveLevel: string;\r\n    showFilter: string[];\r\n    type: string;\r\n  }) => {\r\n    const { sensitiveLevel, type, showFilter } = filterParams;\r\n    const params: QueryMetricListParams = { ...filterParams };\r\n    const sensitiveLevelValue = sensitiveLevel?.[0];\r\n    const showFilterValue = showFilter?.[0];\r\n    const typeValue = type?.[0];\r\n    showFilterValue ? (params[showFilterValue] = true) : null;\r\n    params.sensitiveLevel = sensitiveLevelValue;\r\n    params.type = typeValue;\r\n    setFilterParams(params);\r\n    await queryTagList(\r\n      {\r\n        ...params,\r\n        ...defaultPagination,\r\n      },\r\n      filterParams.key ? false : true,\r\n    );\r\n  };\r\n\r\n  const rowSelection = {\r\n    onChange: (selectedRowKeys: React.Key[]) => {\r\n      const permissionList: boolean[] = [];\r\n      selectedRowKeys.forEach((id: React.Key) => {\r\n        const target = dataSource.find((item) => {\r\n          return item.id === id;\r\n        });\r\n        if (target) {\r\n          permissionList.push(target.hasAdminRes);\r\n        }\r\n      });\r\n      if (permissionList.includes(false)) {\r\n        setHasAllPermission(false);\r\n      } else {\r\n        setHasAllPermission(true);\r\n      }\r\n      setSelectedRowKeys(selectedRowKeys);\r\n    },\r\n    // getCheckboxProps: (record: ISemantic.ITagItem) => ({\r\n    //   disabled: !record.hasAdminRes,\r\n    // }),\r\n  };\r\n\r\n  // const onMenuClick = (key: string) => {\r\n  //   switch (key) {\r\n  //     case 'batchStart':\r\n  //       queryBatchUpdateStatus(selectedRowKeys, StatusEnum.ONLINE);\r\n  //       break;\r\n  //     case 'batchStop':\r\n  //       queryBatchUpdateStatus(selectedRowKeys, StatusEnum.OFFLINE);\r\n  //       break;\r\n  //     default:\r\n  //       break;\r\n  //   }\r\n  // };\r\n\r\n  return (\r\n    <>\r\n      <div className={styles.TagFilterWrapper}>\r\n        <TagFilter\r\n          tagObjectList={tagObjectList}\r\n          initFilterValues={filterParams}\r\n          extraNode={\r\n            <BatchCtrlDropDownButton\r\n              key=\"ctrlBtnList\"\r\n              downloadLoading={downloadLoading}\r\n              onDeleteConfirm={() => {\r\n                queryBatchDeleteTag(selectedRowKeys);\r\n              }}\r\n              hiddenList={['batchDownload', 'batchStart', 'batchStop']}\r\n              disabledList={hasAllPermission ? [] : ['batchStart', 'batchDelete']}\r\n            />\r\n          }\r\n          onFilterInit={(values) => {\r\n            setFilterParams({\r\n              ...filterParams,\r\n              ...values,\r\n            });\r\n            queryTagList(values);\r\n          }}\r\n          onFiltersChange={(_, values) => {\r\n            if (_.showType !== undefined) {\r\n              setLoading(true);\r\n              setDataSource([]);\r\n            }\r\n            handleFilterChange(values);\r\n          }}\r\n        />\r\n      </div>\r\n      <>\r\n        <ProTable\r\n          className={`${styles.tagTable}`}\r\n          actionRef={actionRef}\r\n          rowKey=\"id\"\r\n          search={false}\r\n          dataSource={dataSource}\r\n          columns={columns}\r\n          pagination={pagination}\r\n          size=\"large\"\r\n          scroll={{ x: 1500 }}\r\n          tableAlertRender={() => {\r\n            return false;\r\n          }}\r\n          sticky={{ offsetHeader: 0 }}\r\n          rowSelection={{\r\n            type: 'checkbox',\r\n            ...rowSelection,\r\n          }}\r\n          loading={loading}\r\n          onChange={(data: any) => {\r\n            const { current, pageSize, total } = data;\r\n            const pagin = {\r\n              current,\r\n              pageSize,\r\n              total,\r\n            };\r\n            setPagination(pagin);\r\n            queryTagList({ ...pagin, ...filterParams });\r\n          }}\r\n          options={false}\r\n        />\r\n      </>\r\n\r\n      {createModalVisible && (\r\n        <TagInfoCreateForm\r\n          createModalVisible={createModalVisible}\r\n          tagItem={tagItem}\r\n          onSubmit={() => {\r\n            setCreateModalVisible(false);\r\n            queryTagList({ ...filterParams, ...defaultPagination });\r\n          }}\r\n          onCancel={() => {\r\n            setCreateModalVisible(false);\r\n          }}\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n};\r\nexport default ClassMetricTable;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Insights/Market.tsx"}, "26": {"path": "/tag/detail/:tagId", "name": "tagDetail", "hideInMenu": true, "file": "@/pages/SemanticModel/Insights/Detail.tsx", "parentId": "23", "id": "26", "absPath": "/tag/detail/:tagId", "__content": "import { message, Ta<PERSON>, Button, Space } from 'antd';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { getTagData } from '../service';\r\nimport { useParams, history } from '@umijs/max';\r\nimport styles from './style.less';\r\nimport { ArrowLeftOutlined } from '@ant-design/icons';\r\nimport TagTrendSection from './components/TagTrendSection';\r\nimport { ISemantic } from '../data';\r\nimport TagInfoSider from './components/TagInfoSider';\r\nimport { getDimensionList, queryMetric } from '../service';\r\nimport type { TabsProps } from 'antd';\r\n\r\ntype Props = Record<string, any>;\r\n\r\nconst TagDetail: React.FC<Props> = () => {\r\n  const params: any = useParams();\r\n  const tagId = params.tagId;\r\n  const [tagData, setTagData] = useState<ISemantic.ITagItem>();\r\n  const [dimensionMap, setDimensionMap] = useState<Record<string, ISemantic.IDimensionItem>>({});\r\n\r\n  const [metricMap, setMetricMap] = useState<Record<string, ISemantic.IMetricItem>>({});\r\n\r\n  const [relationDimensionOptions, setRelationDimensionOptions] = useState<\r\n    { value: string; label: string; modelId: number }[]\r\n  >([]);\r\n\r\n  useEffect(() => {\r\n    queryTagData(tagId);\r\n  }, [tagId]);\r\n\r\n  const queryTagData = async (tagId: number) => {\r\n    const { code, data, msg } = await getTagData(tagId);\r\n    if (code === 200) {\r\n      queryDimensionList(data.modelId);\r\n      queryMetricList(data.modelId);\r\n      setTagData({ ...data });\r\n      return;\r\n    }\r\n    message.error(msg);\r\n  };\r\n\r\n  const tabItems: TabsProps['items'] = [\r\n    {\r\n      key: 'trend',\r\n      label: '图表',\r\n      children: (\r\n        // <></>\r\n        <TagTrendSection\r\n          tagData={tagData}\r\n          relationDimensionOptions={relationDimensionOptions}\r\n          dimensionList={[]}\r\n        />\r\n      ),\r\n    },\r\n    // {\r\n    //   key: 'metricCaliberInput',\r\n    //   label: '基础信息',\r\n    //   children: <></>,\r\n    // },\r\n    // {\r\n    //   key: 'metricDataRemark',\r\n    //   label: '备注',\r\n    //   children: <></>,\r\n    // },\r\n  ];\r\n\r\n  const queryDimensionList = async (modelId: number) => {\r\n    const { code, data, msg } = await getDimensionList({ modelId });\r\n    if (code === 200 && Array.isArray(data?.list)) {\r\n      const { list } = data;\r\n      setDimensionMap(\r\n        list.reduce(\r\n          (infoMap: Record<string, ISemantic.IDimensionItem>, item: ISemantic.IDimensionItem) => {\r\n            infoMap[`${item.id}`] = item;\r\n            return infoMap;\r\n          },\r\n          {},\r\n        ),\r\n      );\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const queryMetricList = async (modelId: number) => {\r\n    const { code, data, msg } = await queryMetric({\r\n      modelId: modelId,\r\n    });\r\n    const { list } = data || {};\r\n    if (code === 200) {\r\n      setMetricMap(\r\n        list.reduce(\r\n          (infoMap: Record<string, ISemantic.IMetricItem>, item: ISemantic.IMetricItem) => {\r\n            infoMap[`${item.id}`] = item;\r\n            return infoMap;\r\n          },\r\n          {},\r\n        ),\r\n      );\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <div className={styles.tagDetailWrapper}>\r\n        <div className={styles.tagDetail}>\r\n          <div className={styles.tabContainer}>\r\n            <Tabs\r\n              defaultActiveKey=\"trend\"\r\n              items={tabItems}\r\n              tabBarExtraContent={{\r\n                right: (\r\n                  <Button\r\n                    size=\"middle\"\r\n                    type=\"link\"\r\n                    key=\"backListBtn\"\r\n                    onClick={() => {\r\n                      history.push('/tag/market');\r\n                    }}\r\n                  >\r\n                    <Space>\r\n                      <ArrowLeftOutlined />\r\n                      返回列表页\r\n                    </Space>\r\n                  </Button>\r\n                ),\r\n              }}\r\n              size=\"large\"\r\n              className={styles.tagDetailTab}\r\n            />\r\n          </div>\r\n          <div className={styles.siderContainer}>\r\n            <TagInfoSider\r\n              dimensionMap={dimensionMap}\r\n              metricMap={metricMap}\r\n              // relationDimensionOptions={relationDimensionOptions}\r\n              tagData={tagData}\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default TagDetail;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/Insights/Detail.tsx"}, "27": {"path": "/login", "name": "login", "layout": false, "hideInMenu": true, "file": "@/pages/Login/index.tsx", "id": "27", "absPath": "/login", "__content": "// import type { FC } from 'react';\r\nimport styles from './style.less';\r\nimport { Button, Form, Input, message, Space } from 'antd';\r\nimport { LockOutlined, UserOutlined } from '@ant-design/icons';\r\nimport RegisterForm from './components/RegisterForm';\r\n// import ForgetPwdForm from './components/ForgetPwdForm';\r\nimport { ROUTE_AUTH_CODES } from '../../../config/routes';\r\n// import S2Icon, { ICON } from '@/components/S2Icon';\r\nimport React, { useState } from 'react';\r\nimport { useForm } from 'antd/lib/form/Form';\r\nimport type { RegisterFormDetail } from './components/types';\r\nimport { postUserLogin, userRegister } from './services';\r\nimport { AUTH_TOKEN_KEY } from '@/common/constants';\r\nimport { queryCurrentUser } from '@/services/user';\r\nimport { history, useModel } from '@umijs/max';\r\nimport CryptoJS from 'crypto-js';\r\nimport { encryptPassword } from '@/utils/utils';\r\nimport { preloadCriticalRoutes, preloadRoutesByRole } from '@/utils/routePreloader';\r\n\r\n// import logo from '@/assets/icon/logo.png';\r\n\r\nconst { Item } = Form;\r\nconst LoginPage: React.FC = () => {\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [loading,setLoading] = useState<boolean>(false);\r\n  const encryptKey = CryptoJS.enc.Utf8.parse('hchatdata@2024');\r\n  const [form] = useForm();\r\n  const { initialState = {}, setInitialState } = useModel('@@initialState');\r\n  // 通过用户信息进行登录\r\n  const loginDone = async (values: RegisterFormDetail) => {\r\n    const { code, data, msg } = await postUserLogin(values);\r\n    if (code === 200) {\r\n      localStorage.setItem(AUTH_TOKEN_KEY, data);\r\n      const { code: queryUserCode, data: queryUserData } = await queryCurrentUser();\r\n      if (queryUserCode === 200) {\r\n        const currentUser = {\r\n          ...queryUserData,\r\n          staffName: queryUserData.staffName || queryUserData.name,\r\n        };\r\n        const authCodes = Array.isArray(initialState?.authCodes) ? initialState?.authCodes : [];\r\n        if (queryUserData.superAdmin) {\r\n          authCodes.push(ROUTE_AUTH_CODES.SYSTEM_ADMIN);\r\n          authCodes.push(ROUTE_AUTH_CODES.ADMIN);\r\n        }\r\n        setInitialState({ ...initialState, currentUser, authCodes });\r\n\r\n        // 登录成功后立即开始预加载关键路由\r\n        preloadCriticalRoutes().catch(error => {\r\n          console.warn('预加载关键路由失败:', error);\r\n        });\r\n\r\n        // 根据用户角色预加载特定路由\r\n        preloadRoutesByRole(authCodes).catch(error => {\r\n          console.warn('预加载角色路由失败:', error);\r\n        });\r\n      }\r\n      history.push('/');\r\n      return;\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  // 处理登录按钮响应\r\n  const handleLogin = async () => {\r\n    setLoading(true);\r\n    const { validateFields } = form;\r\n    try{\r\n      const content = await validateFields();\r\n      await loginDone({ ...content, password: encryptPassword(content.password, encryptKey) });\r\n    }catch(error){\r\n      console.log('Failed:', error);\r\n    }finally{\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // 处理注册弹窗确定按钮\r\n  const handleRegister = async (values: RegisterFormDetail) => {\r\n    const enCodeValues = { ...values, password: encryptPassword(values.password, encryptKey) };\r\n    const { code, msg } = await userRegister(enCodeValues);\r\n    if (code === 200) {\r\n      message.success('注册成功');\r\n      setCreateModalVisible(false);\r\n      // 注册完自动帮用户登录\r\n      await loginDone(enCodeValues);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  // 相应注册按钮\r\n  const handleRegisterBtn = () => {\r\n    setCreateModalVisible(true);\r\n  };\r\n\r\n  // // 忘记密码弹窗确定响应\r\n  // const handleForgetPwd = async (values: RegisterFormDetail) => {\r\n  //   await getUserForgetPwd({ ...values });\r\n  //   message.success('发送邮件成功，请在收到邮件后进入邮件链接进行密码重置');\r\n  //   setForgetModalVisible(false);\r\n  // };\r\n\r\n  // // 响应忘记密码按钮\r\n  // const handleForgetPwdBtn = () => {\r\n  //   setForgetModalVisible(true);\r\n  // };\r\n\r\n  return (\r\n    <div className={styles.loginWarp}>\r\n      <div className={styles.content}>\r\n        <div className={styles.formContent}>\r\n          <div className={styles.formBox}>\r\n            <Form form={form} labelCol={{ span: 6 }} colon={false}>\r\n              <div className={styles.loginMain}>\r\n                <h3 className={styles.title}>\r\n                  <Space align=\"center\">\r\n                    <div>AIData</div>\r\n                  </Space>\r\n                </h3>\r\n                <Item name=\"name\" rules={[{ required: true }]} label=\"\">\r\n                  <Input size=\"large\" placeholder=\"用户名\" prefix={<UserOutlined />} />\r\n                </Item>\r\n                <Item name=\"password\" rules={[{ required: true }]} label=\"\">\r\n                  <Input\r\n                    size=\"large\"\r\n                    type=\"password\"\r\n                    placeholder=\"密码\"\r\n                    onPressEnter={handleLogin}\r\n                    prefix={<LockOutlined />}\r\n                  />\r\n                </Item>\r\n\r\n                {\r\n                  loading ? (\r\n                    <Button className={styles.signInBtn} type=\"primary\" style={{opacity: 0.7}}>\r\n                      登录中...\r\n                    </Button>\r\n                  ):(\r\n                    <Button className={styles.signInBtn} type=\"primary\" onClick={handleLogin}>\r\n                      登录\r\n                    </Button>\r\n                  )\r\n                }\r\n\r\n                {/* <div className={styles.tool}>\r\n                  <Button className={styles.button} onClick={handleRegisterBtn}>\r\n                    注册\r\n                  </Button>\r\n                </div> */}\r\n              </div>\r\n            </Form>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <RegisterForm\r\n        onCancel={() => {\r\n          setCreateModalVisible(false);\r\n        }}\r\n        onSubmit={handleRegister}\r\n        createModalVisible={createModalVisible}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginPage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/Login/index.tsx"}, "28": {"path": "/database", "name": "database", "access": "ADMIN", "file": "@/pages/SemanticModel/components/Database/DatabaseTable.tsx", "parentId": "ant-design-pro-layout", "id": "28", "absPath": "/database", "__content": "import type { ActionType, ProColumns } from '@ant-design/pro-components';\r\nimport { ProTable } from '@ant-design/pro-components';\r\nimport { message, Button, Space, Popconfirm } from 'antd';\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport DatabaseSettingModal from './DatabaseSettingModal';\r\nimport { ISemantic } from '../../data';\r\nimport { getDatabaseList, deleteDatabase } from '../../service';\r\n\r\nimport moment from 'moment';\r\nimport styles from '../style.less';\r\n\r\ntype Props = {};\r\n\r\nconst DatabaseTable: React.FC<Props> = ({}) => {\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [databaseItem, setDatabaseItem] = useState<ISemantic.IDatabaseItem>();\r\n  const [dataBaseList, setDataBaseList] = useState<any[]>([]);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n\r\n  const queryDatabaseList = async () => {\r\n    const { code, data, msg } = await getDatabaseList();\r\n    if (code === 200) {\r\n      setDataBaseList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    queryDatabaseList();\r\n  }, []);\r\n\r\n  const columns: ProColumns[] = [\r\n    {\r\n      dataIndex: 'id',\r\n      title: 'ID',\r\n      width: 80,\r\n    },\r\n    {\r\n      dataIndex: 'name',\r\n      title: '连接名称',\r\n    },\r\n\r\n    {\r\n      dataIndex: 'type',\r\n      title: '类型',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'createdBy',\r\n      title: '创建人',\r\n      search: false,\r\n    },\r\n\r\n    {\r\n      dataIndex: 'description',\r\n      title: '描述',\r\n      search: false,\r\n    },\r\n\r\n    {\r\n      dataIndex: 'updatedAt',\r\n      title: '更新时间',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value && value !== '-' ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';\r\n      },\r\n    },\r\n\r\n    {\r\n      title: '操作',\r\n      dataIndex: 'x',\r\n      valueType: 'option',\r\n      width: 100,\r\n      render: (_, record) => {\r\n        if (!record.hasEditPermission) {\r\n          return <></>;\r\n        }\r\n        return (\r\n          <Space>\r\n            <a\r\n              key=\"dimensionEditBtn\"\r\n              onClick={() => {\r\n                setDatabaseItem(record);\r\n                setCreateModalVisible(true);\r\n              }}\r\n            >\r\n              编辑\r\n            </a>\r\n            <Popconfirm\r\n              title=\"确认删除？\"\r\n              okText=\"是\"\r\n              cancelText=\"否\"\r\n              onConfirm={async () => {\r\n                const { code, msg } = await deleteDatabase(record.id);\r\n                if (code === 200) {\r\n                  setDatabaseItem(undefined);\r\n                  queryDatabaseList();\r\n                } else {\r\n                  message.error(msg);\r\n                }\r\n              }}\r\n            >\r\n              <a\r\n                key=\"dimensionDeleteEditBtn\"\r\n                onClick={() => {\r\n                  setDatabaseItem(record);\r\n                }}\r\n              >\r\n                删除\r\n              </a>\r\n            </Popconfirm>\r\n          </Space>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div style={{ margin: 20 }}>\r\n      <ProTable\r\n        // className={`${styles.classTable}`}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        columns={columns}\r\n        dataSource={dataBaseList}\r\n        search={false}\r\n        tableAlertRender={() => {\r\n          return false;\r\n        }}\r\n        size=\"small\"\r\n        options={{ reload: false, density: false, fullScreen: false }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            key=\"create\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              setDatabaseItem(undefined);\r\n              setCreateModalVisible(true);\r\n            }}\r\n          >\r\n            创建数据库连接\r\n          </Button>,\r\n        ]}\r\n      />\r\n      {createModalVisible && (\r\n        <DatabaseSettingModal\r\n          open={createModalVisible}\r\n          databaseItem={databaseItem}\r\n          onCancel={() => {\r\n            setCreateModalVisible(false);\r\n          }}\r\n          onSubmit={() => {\r\n            setCreateModalVisible(false);\r\n            queryDatabaseList();\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\nexport default DatabaseTable;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/components/Database/DatabaseTable.tsx"}, "29": {"path": "/llm", "name": "llm", "access": "ADMIN", "file": "@/pages/SemanticModel/components/LLM/LlmTable.tsx", "parentId": "ant-design-pro-layout", "id": "29", "absPath": "/llm", "__content": "import type { ActionType, ProColumns } from '@ant-design/pro-components';\r\nimport { ProTable } from '@ant-design/pro-components';\r\nimport { message, Button, Space, Popconfirm } from 'antd';\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport LlmSettingModal from './LlmSettingModal';\r\nimport { ISemantic } from '../../data';\r\nimport { deleteLlmConfig } from '../../service';\r\nimport { getLlmList } from '@/services/system';\r\nimport dayjs from 'dayjs';\r\n\r\ntype Props = {};\r\n\r\nconst LlmTable: React.FC<Props> = ({}) => {\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [llmItem, setLlmItem] = useState<ISemantic.ILlmItem>();\r\n  const [dataBaseList, setDataBaseList] = useState<any[]>([]);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n\r\n  const queryLlmList = async () => {\r\n    const { code, data, msg } = await getLlmList();\r\n    if (code === 200) {\r\n      setDataBaseList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    queryLlmList();\r\n  }, []);\r\n\r\n  const columns: ProColumns[] = [\r\n    {\r\n      dataIndex: 'id',\r\n      title: 'ID',\r\n      width: 80,\r\n    },\r\n    {\r\n      dataIndex: 'name',\r\n      title: '连接名称',\r\n    },\r\n    {\r\n      dataIndex: ['config', 'modelName'],\r\n      title: '模型名称',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: ['config', 'provider'],\r\n      title: '接口协议',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'createdBy',\r\n      title: '创建人',\r\n      search: false,\r\n    },\r\n\r\n    {\r\n      dataIndex: 'description',\r\n      title: '描述',\r\n      search: false,\r\n    },\r\n\r\n    {\r\n      dataIndex: 'updatedAt',\r\n      title: '更新时间',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value && value !== '-' ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : '-';\r\n      },\r\n    },\r\n\r\n    {\r\n      title: '操作',\r\n      dataIndex: 'x',\r\n      valueType: 'option',\r\n      width: 100,\r\n      render: (_, record) => {\r\n        // if (!record.hasEditPermission) {\r\n        //   return <></>;\r\n        // }\r\n        return (\r\n          <Space>\r\n            <a\r\n              key=\"dimensionEditBtn\"\r\n              onClick={() => {\r\n                setLlmItem(record);\r\n                setCreateModalVisible(true);\r\n              }}\r\n            >\r\n              编辑\r\n            </a>\r\n            <Popconfirm\r\n              title=\"确认删除？\"\r\n              okText=\"是\"\r\n              cancelText=\"否\"\r\n              onConfirm={async () => {\r\n                const { code, msg } = await deleteLlmConfig(record.id);\r\n                if (code === 200) {\r\n                  setLlmItem(undefined);\r\n                  queryLlmList();\r\n                } else {\r\n                  message.error(msg);\r\n                }\r\n              }}\r\n            >\r\n              <a\r\n                key=\"dimensionDeleteEditBtn\"\r\n                onClick={() => {\r\n                  setLlmItem(record);\r\n                }}\r\n              >\r\n                删除\r\n              </a>\r\n            </Popconfirm>\r\n          </Space>\r\n        );\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div style={{ margin: 20 }}>\r\n      <ProTable\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        columns={columns}\r\n        dataSource={dataBaseList}\r\n        search={false}\r\n        tableAlertRender={() => {\r\n          return false;\r\n        }}\r\n        size=\"small\"\r\n        options={{ reload: false, density: false, fullScreen: false }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            key=\"create\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              setLlmItem(undefined);\r\n              setCreateModalVisible(true);\r\n            }}\r\n          >\r\n            创建大模型连接\r\n          </Button>,\r\n        ]}\r\n      />\r\n      {createModalVisible && (\r\n        <LlmSettingModal\r\n          open={createModalVisible}\r\n          llmItem={llmItem}\r\n          onCancel={() => {\r\n            setCreateModalVisible(false);\r\n          }}\r\n          onSubmit={() => {\r\n            setCreateModalVisible(false);\r\n            queryLlmList();\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\nexport default LlmTable;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/SemanticModel/components/LLM/LlmTable.tsx"}, "30": {"path": "/system", "name": "system", "access": "SYSTEM_ADMIN", "file": "@/pages/System/index.tsx", "parentId": "ant-design-pro-layout", "id": "30", "absPath": "/system", "__content": "import styles from './style.less';\r\nimport { Button, Form, message, Space, Divider, Anchor, Row, Col } from 'antd';\r\nimport React, { useState, useEffect, useRef } from 'react';\r\nimport { getSystemConfig, saveSystemConfig } from '@/services/user';\r\nimport { ProCard } from '@ant-design/pro-components';\r\nimport SelectTMEPerson from '@/components/SelectTMEPerson';\r\nimport { ConfigParametersItem, SystemConfig, dependenciesItem } from './types';\r\n// import { testLLMConn } from '../../services/system';\r\nimport { groupBy } from 'lodash';\r\nimport { genneratorFormItemList } from '../SemanticModel/utils';\r\n\r\nconst FormItem = Form.Item;\r\n\r\ntype Admin = string[];\r\n\r\nconst System: React.FC = () => {\r\n  const [systemConfig, setSystemConfig] = useState<Record<string, ConfigParametersItem[]>>({});\r\n  const [anchorItems, setAnchorItems] = useState<{ key: string; href: string; title: string }[]>(\r\n    [],\r\n  );\r\n  const [configSource, setConfigSource] = useState<SystemConfig>();\r\n\r\n  const configMap = useRef<Record<string, ConfigParametersItem>>();\r\n\r\n  const configIocDepMap = useRef<Record<string, any>>();\r\n  // const [llmTestLoading, setLlmTestLoading] = useState<boolean>(false);\r\n\r\n  useEffect(() => {\r\n    querySystemConfig();\r\n  }, []);\r\n  const [form] = Form.useForm();\r\n  const querySystemConfig = async () => {\r\n    const { code, data, msg } = await getSystemConfig();\r\n\r\n    if (code === 200 && data) {\r\n      const { parameters = [], admins = [] } = data;\r\n\r\n      const parametersMap = parameters.reduce(\r\n        (configReduceMap: Record<string, ConfigParametersItem>, item: ConfigParametersItem) => {\r\n          return {\r\n            ...configReduceMap,\r\n            [item.name]: item,\r\n          };\r\n        },\r\n        {},\r\n      );\r\n\r\n      configMap.current = parametersMap;\r\n\r\n      groupConfigAndSet(parameters);\r\n      initDepConfig(parameters, admins);\r\n\r\n      setConfigSource(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const initDepConfig = (parameters: ConfigParametersItem[], admins: Admin) => {\r\n    const iocMap = getDepIoc(parameters);\r\n    configIocDepMap.current = iocMap;\r\n    const initFormValues = setInitData(admins, parameters);\r\n    Object.keys(initFormValues).forEach((itemName) => {\r\n      const targetDep = iocMap[itemName] || {};\r\n      const excuteStack = Object.values(targetDep);\r\n      if (Array.isArray(excuteStack)) {\r\n        excuteDepConfig(itemName, initFormValues, true);\r\n      }\r\n    });\r\n  };\r\n\r\n  const groupConfigAndSet = (parameters: ConfigParametersItem[]) => {\r\n    const groupByConfig = groupBy(parameters, 'module');\r\n    const anchor = Object.keys(groupByConfig).map((key: string) => {\r\n      return {\r\n        key,\r\n        href: `#${key}`,\r\n        title: key,\r\n      };\r\n    });\r\n    setAnchorItems(anchor);\r\n    setSystemConfig(groupByConfig);\r\n  };\r\n\r\n  const getDepIoc = (parameters: ConfigParametersItem[]) => {\r\n    const iocMap: Record<string, Record<string, ConfigParametersItem>> = {};\r\n    parameters.forEach((item) => {\r\n      const { name: itemName, dependencies } = item;\r\n      if (Array.isArray(dependencies)) {\r\n        dependencies.forEach((depItem) => {\r\n          const { name } = depItem;\r\n\r\n          if (iocMap[name]) {\r\n            iocMap[name] = {\r\n              ...iocMap[name],\r\n              [itemName]: item,\r\n            };\r\n          } else {\r\n            iocMap[name] = {\r\n              [itemName]: item,\r\n            };\r\n          }\r\n        });\r\n      }\r\n    });\r\n    return iocMap;\r\n  };\r\n\r\n  const setInitData = (admins: string[], systemConfigParameters: ConfigParametersItem[]) => {\r\n    const fieldsValue = systemConfigParameters.reduce(\r\n      (fields, item) => {\r\n        const { name, value } = item;\r\n        return {\r\n          ...fields,\r\n          [name]: value,\r\n        };\r\n      },\r\n      { admins },\r\n    );\r\n    form.setFieldsValue(fieldsValue);\r\n    return fieldsValue;\r\n  };\r\n\r\n  const querySaveSystemConfig = async () => {\r\n    const submitData = await form.validateFields();\r\n    const { code, msg } = await saveSystemConfig({\r\n      ...configSource,\r\n      admins: submitData.admins,\r\n      parameters: configSource!.parameters.map((item) => {\r\n        const { name } = item;\r\n        if (submitData[name] !== undefined) {\r\n          return {\r\n            ...item,\r\n            value: submitData[name],\r\n          };\r\n        }\r\n        return item;\r\n      }),\r\n    });\r\n    if (code === 200) {\r\n      message.success('保存成功');\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const excuteDepConfig = (\r\n    itemName: string,\r\n    formValues: Record<string, any>,\r\n    isInit: boolean = false,\r\n  ) => {\r\n    const targetDep = configIocDepMap?.current?.[itemName];\r\n    if (!targetDep) {\r\n      return;\r\n    }\r\n    const excuteStack = Object.values(targetDep);\r\n    if (!Array.isArray(excuteStack)) {\r\n      return;\r\n    }\r\n    const tempConfigMap: any = { ...configMap.current };\r\n    const currentFormValues = formValues;\r\n\r\n    excuteStack.forEach((configItem: any) => {\r\n      const showStateList: boolean[] = [];\r\n      const hasValueFieldsSetDefaultValueList: any[] = [];\r\n      const { dependencies, name: configItemName } = configItem;\r\n      dependencies.forEach((item: dependenciesItem) => {\r\n        const { name, setDefaultValue } = item;\r\n        const currentDepValue = currentFormValues[name];\r\n        const showIncludesValue = item.show?.includesValue;\r\n        if (Array.isArray(showIncludesValue)) {\r\n          showStateList.push(showIncludesValue.includes(currentDepValue));\r\n        }\r\n        if (setDefaultValue && currentDepValue) {\r\n          hasValueFieldsSetDefaultValueList.push({\r\n            excuteItem: configItemName,\r\n            ...item,\r\n          });\r\n        }\r\n      });\r\n\r\n      const visible = showStateList.every((item) => item);\r\n      tempConfigMap[configItemName].visible = visible;\r\n      const lastSetDefaultValueItem =\r\n        hasValueFieldsSetDefaultValueList[hasValueFieldsSetDefaultValueList.length - 1];\r\n      const lastSetDefaultValue = lastSetDefaultValueItem?.setDefaultValue;\r\n\r\n      if (lastSetDefaultValue) {\r\n        const targetValue = lastSetDefaultValue[currentFormValues[lastSetDefaultValueItem.name]];\r\n        if (targetValue && !isInit) {\r\n          form.setFieldValue(lastSetDefaultValueItem.excuteItem, targetValue);\r\n        }\r\n      }\r\n    });\r\n\r\n    groupConfigAndSet(Object.values(tempConfigMap));\r\n  };\r\n\r\n  // const testLLMConnect = async (params: any) => {\r\n  //   setLlmTestLoading(true);\r\n  //   const { code, data } = await testLLMConn(params);\r\n  //   setLlmTestLoading(false);\r\n  //   if (code === 200 && data) {\r\n  //     message.success('连接成功');\r\n  //   } else {\r\n  //     message.error('模型连接失败');\r\n  //   }\r\n  // };\r\n\r\n  return (\r\n    <>\r\n      <div style={{ margin: '40px auto', width: 1200 }}>\r\n        <Row>\r\n          <Col span={18}>\r\n            <ProCard\r\n              title=\"系统设置\"\r\n              extra={\r\n                <Space>\r\n                  <Button\r\n                    type=\"primary\"\r\n                    onClick={() => {\r\n                      querySaveSystemConfig();\r\n                    }}\r\n                  >\r\n                    保 存\r\n                  </Button>\r\n                </Space>\r\n              }\r\n            >\r\n              <Form\r\n                form={form}\r\n                layout=\"vertical\"\r\n                className={styles.form}\r\n                onValuesChange={(value, values) => {\r\n                  const valueKey = Object.keys(value)[0];\r\n                  excuteDepConfig(valueKey, values);\r\n                }}\r\n              >\r\n                <FormItem name=\"admins\" label=\"管理员\">\r\n                  <SelectTMEPerson placeholder=\"请邀请团队成员\" />\r\n                </FormItem>\r\n\r\n                <Divider />\r\n\r\n                <Space direction=\"vertical\" style={{ width: '100%' }} size={35}>\r\n                  {Object.keys(systemConfig).map((key: string) => {\r\n                    const itemList = systemConfig[key];\r\n                    return (\r\n                      <ProCard\r\n                        title={<span style={{ color: '#0057ff' }}>{key}</span>}\r\n                        key={key}\r\n                        bordered\r\n                        id={key}\r\n                      >\r\n                        {genneratorFormItemList(itemList)}\r\n                      </ProCard>\r\n                    );\r\n                  })}\r\n                </Space>\r\n              </Form>\r\n            </ProCard>\r\n          </Col>\r\n          <Col span={6} style={{ background: '#fff' }}>\r\n            <div style={{ marginTop: 20 }}>\r\n              <Anchor items={anchorItems} />\r\n            </div>\r\n          </Col>\r\n        </Row>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default System;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/System/index.tsx"}, "31": {"path": "/user", "name": "user", "access": "ADMIN", "file": "@/pages/User/UserTable.tsx", "parentId": "ant-design-pro-layout", "id": "31", "absPath": "/user", "__content": "import type { ActionType, ProColumns } from '@ant-design/pro-components';\r\nimport type { RegisterFormDetail } from '@/pages/Login/components/types';\r\nimport { ProTable } from '@ant-design/pro-components';\r\nimport { message, Button, Space, Popconfirm, Switch } from 'antd';\r\nimport React, { useRef, useState, useEffect } from 'react';\r\nimport DatabaseSettingModal from './DatabaseSettingModal';\r\nimport { ISemantic } from './data';\r\n// import { getDatabaseList, deleteDatabase } from './service';\r\nimport { getUserList, userDisabled } from './service';\r\nimport { userRegister } from '@/pages/Login/services';\r\nimport { encryptPassword } from '@/utils/utils';\r\nimport CryptoJS from 'crypto-js';\r\n\r\nimport moment from 'moment';\r\nimport RegisterForm from '@/pages/Login/components/RegisterForm';\r\nimport styles from './style.less';\r\n\r\ntype Props = {};\r\n\r\nconst UserTable: React.FC<Props> = ({}) => {\r\n  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);\r\n  const [userItem, setUserItem] = useState<ISemantic.IDatabaseItem>();\r\n  const [userList, setUserList] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n\r\n  const actionRef = useRef<ActionType>();\r\n\r\n  const queryUserList = async () => {\r\n    setLoading(true)\r\n    const { code, data, msg } = await getUserList();\r\n    if (code === 200) {\r\n      setUserList(data);\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n    setLoading(false)\r\n  };\r\n\r\n  const encryptKey = CryptoJS.enc.Utf8.parse('hchatdata@2024');\r\n  \r\n\r\n  const handleRegister = async (values: RegisterFormDetail) => {\r\n    const enCodeValues = { ...values, password: encryptPassword(values.password, encryptKey) };\r\n    const { code, msg } = await userRegister(enCodeValues);\r\n    if (code === 200) {\r\n      message.success('用户新增成功');\r\n      setCreateModalVisible(false);\r\n      await queryUserList();\r\n    } else {\r\n      message.error(msg);\r\n    }\r\n  };\r\n\r\n  const handelDisabled = (value: any) => {\r\n    userDisabled(value).then(() => {\r\n      queryUserList();\r\n    })\r\n  }\r\n\r\n  useEffect(() => {\r\n    queryUserList();\r\n  }, []);\r\n\r\n  const columns: ProColumns[] = [\r\n    {\r\n      dataIndex: 'id',\r\n      title: 'ID',\r\n      width: 80,\r\n    },\r\n    {\r\n      dataIndex: 'name',\r\n      title: '用户名',\r\n    },\r\n    {\r\n      dataIndex: 'displayName',\r\n      title: '昵称',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'email',\r\n      title: '邮箱',\r\n      search: false,\r\n    },\r\n    {\r\n      dataIndex: 'isAdmin',\r\n      title: '是否是管理员',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value === 1 ? '是' : '否';\r\n      },\r\n    },\r\n    {\r\n      dataIndex: 'isDisabled',\r\n      title: '状态',\r\n      search: false,\r\n      render: (_, record) => {\r\n        return (\r\n          <div className={styles.toggleStatus}>\r\n            {record.isDisabled === 1 ? '已禁用' : <span className={styles.online}>已启用</span>}\r\n            <span\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n              }}\r\n            >\r\n              <Switch\r\n                key={record.id}\r\n                size=\"small\"\r\n                defaultChecked={record.isDisabled === 0}\r\n                onChange={(value) => {\r\n                  handelDisabled({ ...record, isDisabled: value ? 0 : 1 });\r\n                }}\r\n              />\r\n            </span>\r\n          </div>\r\n        );\r\n      },\r\n    },\r\n    {\r\n      dataIndex: 'lastLogin',\r\n      title: '最后登录时间',\r\n      search: false,\r\n      render: (value: any) => {\r\n        return value && value !== '-' ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';\r\n      },\r\n    },\r\n    // {\r\n    //   title: '操作',\r\n    //   dataIndex: 'x',\r\n    //   valueType: 'option',\r\n    //   width: 150,\r\n    //   render: (_, record) => {\r\n    //     return (\r\n    //       <Space>\r\n    //         <Popconfirm\r\n    //           title=\"确认删除？\"\r\n    //           okText=\"是\"\r\n    //           cancelText=\"否\"\r\n    //           onConfirm={async () => {\r\n    //             let newParams = {...record,isDisabled: 1}\r\n    //             const { code, msg } = await userDisabled(newParams);\r\n    //             if (code === 200) {\r\n    //               // setDatabaseItem(undefined);\r\n    //               queryUserList();\r\n    //             } else {\r\n    //               message.error(msg);\r\n    //             }\r\n    //           }}\r\n    //         >\r\n    //           <a\r\n    //             key=\"dimensionDeleteEditBtn\"\r\n    //             onClick={() => {\r\n    //               // setDatabaseItem(record);\r\n    //             }}\r\n    //           >\r\n    //             删除\r\n    //           </a>\r\n    //         </Popconfirm>\r\n    //       </Space>\r\n    //     );\r\n    //   },\r\n    // },\r\n  ];\r\n\r\n  return (\r\n    <div style={{ margin: 20 }}>\r\n      <ProTable\r\n        // className={`${styles.classTable}`}\r\n        actionRef={actionRef}\r\n        rowKey=\"id\"\r\n        loading={loading}\r\n        columns={columns}\r\n        dataSource={userList}\r\n        search={false}\r\n        tableAlertRender={() => {\r\n          return false;\r\n        }}\r\n        size=\"small\"\r\n        options={{ reload: false, density: false, fullScreen: false }}\r\n        toolBarRender={() => [\r\n          <Button\r\n            key=\"create\"\r\n            type=\"primary\"\r\n            onClick={() => {\r\n              setUserItem(undefined);\r\n              setCreateModalVisible(true);\r\n            }}\r\n          >\r\n            创建用户\r\n          </Button>,\r\n        ]}\r\n      />\r\n      <RegisterForm\r\n        onCancel={() => {\r\n          setCreateModalVisible(false);\r\n        }}\r\n        onSubmit={handleRegister}\r\n        createModalVisible={createModalVisible}\r\n      />\r\n      {/* {createModalVisible && (\r\n        <DatabaseSettingModal\r\n          open={createModalVisible}\r\n          // databaseItem={userItem}\r\n          onCancel={() => {\r\n            setCreateModalVisible(false);\r\n          }}\r\n          onSubmit={() => {\r\n            setCreateModalVisible(false);\r\n            queryUserList();\r\n          }}\r\n        />\r\n      )} */}\r\n    </div>\r\n  );\r\n};\r\nexport default UserTable;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/User/UserTable.tsx"}, "32": {"path": "/", "redirect": "/chat", "parentId": "ant-design-pro-layout", "id": "32", "absPath": "/"}, "33": {"path": "/401", "file": "@/pages/401.tsx", "parentId": "ant-design-pro-layout", "id": "33", "absPath": "/401", "__content": "import { Button, Result } from 'antd';\r\nimport React from 'react';\r\nimport { history } from '@umijs/max';\r\n\r\nconst NoAuthPage: React.FC = () => (\r\n  <Result\r\n    status=\"403\"\r\n    title=\"当前页面无权限\"\r\n    subTitle={1 ? '请联系项目管理员 liuxizi 开通权限' : '请申请加入自己业务的项目'}\r\n    extra={\r\n      <Button type=\"primary\" onClick={() => history.push('/homepage')}>\r\n        回到首页\r\n      </Button>\r\n    }\r\n  />\r\n);\r\n\r\nexport default NoAuthPage;\r\n", "__isJSFile": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/pages/401.tsx"}, "ant-design-pro-layout": {"id": "ant-design-pro-layout", "path": "/", "file": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-layout/Layout.tsx", "absPath": "/", "isLayout": true, "__absFile": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/src/.umi-production/plugin-layout/Layout.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "pnpm", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\react-dom"}, "appJS": {"path": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\src\\app.tsx", "exports": ["getInitialState", "layout", "onRouteChange", "request"]}, "locale": "zh-CN", "globalCSS": ["D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\src\\global.less"], "globalJS": ["D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\src\\global.tsx"], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "http://**************:18888/ydvhhuybcw5i3z/HChatData.git"}, "framework": "react", "typescript": {"tsVersion": "4.9.5"}, "antd": {"pkgPath": "D:\\项目\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\antd", "version": "5.25.2"}, "pluginLayout": {"pkgPath": "D:/项目/xx/HChatData/webapp/packages/hchatdata-fe/node_modules/@ant-design/pro-components", "version": "2.7.0"}}