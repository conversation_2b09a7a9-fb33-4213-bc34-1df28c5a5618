(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3290],{51308:function(he,Y,e){"use strict";e.d(Y,{Z:function(){return Z}});var pe=e(10947),k=e(2206),me={title:"title___V3aE9",subTitleContainer:"subTitleContainer___gJqJ1"},B=e(31549),L=pe.Z.Paragraph,A=function(ie){var ee=ie.title,ge=ie.subTitle,X=ie.subTitleEditable,ye=X===void 0?!1:X,le=ie.onSubTitleChange;return(0,B.jsxs)(k.Z,{direction:"vertical",size:2,style:{width:"100%"},children:[(0,B.jsx)("div",{className:me.title,children:ee}),(0,B.jsx)("div",{className:me.subTitleContainer,children:ye?(0,B.jsx)(L,{editable:{onChange:function(T){le==null||le(T)}},children:ge||"\u6DFB\u52A0\u63CF\u8FF0"}):ge&&(0,B.jsx)("span",{style:{fontSize:"12px",color:"#7b809a"},children:ge})})]})},Z=A},70348:function(he,Y,e){"use strict";e.d(Y,{Z:function(){return le}});var pe=e(90819),k=e.n(pe),me=e(89933),B=e.n(me),L=e(45332),A=e.n(L),Z=e(44194),Q=e(26574),ie=e(20221),ee={userAvatar:"userAvatar___uI2EW",userText:"userText___VoQwT",selectPerson:"selectPerson___84ixG"},ge=e(3306),X=e(31549),ye=function(T){var Ze=T.placeholder,_e=T.value,ke=T.isMultiple,t=ke===void 0?!0:ke,We=T.onChange,te=(0,Z.useState)([]),Me=A()(te,2),et=Me[0],Ue=Me[1],tt=(0,ie.useModel)("allUserData"),be=tt.allUserList,He=tt.MrefreshUserList,Re=function(){var ne=B()(k()().mark(function Xe(){var Ge;return k()().wrap(function(q){for(;;)switch(q.prev=q.next){case 0:return q.next=2,He();case 2:Ge=q.sent,Ue(Ge);case 4:case"end":return q.stop()}},Xe)}));return function(){return ne.apply(this,arguments)}}();return(0,Z.useEffect)(function(){Array.isArray(be)&&be.length>0?Ue(be):Re()},[]),(0,X.jsx)(Q.default,{value:_e,placeholder:Ze!=null?Ze:"\u8BF7\u9009\u62E9\u7528\u6237\u540D",mode:t?"multiple":void 0,allowClear:!0,showSearch:!0,className:ee.selectPerson,onChange:We,children:et.map(function(ne){return(0,X.jsxs)(Q.default.Option,{value:ne.name,children:[(0,X.jsx)(ge.Z,{size:"small",staffName:ne.name}),(0,X.jsx)("span",{className:ee.userText,children:ne.displayName})]},ne.name)})})},le=ye},88054:function(he,Y,e){"use strict";e.d(Y,{H3:function(){return et},Mf:function(){return Ue},DJ:function(){return be},vX:function(){return ne},Ww:function(){return ot},YA:function(){return st},$w:function(){return Ge},mr:function(){return q},Ey:function(){return at},pV:function(){return Xe}});var pe=e(76711),k=e.n(pe),me=e(86222),B=e(73193),L=e.n(B),A=e(20263),Z=e(46504),Q=e(44319),ie=e(88732),ee=e(26574),ge=e(51308),X=e(49841),ye=e.n(X),le=e(44194),ze=e(37069),T=e(31549),Ze=function(o){var l=Object.assign({},(ye()(o),o)),a=(0,le.useRef)(null);(0,le.useEffect)(function(){a.current&&a.current.addEventListener("wheel",s)},[]);var s=function(F){F.stopPropagation(),F.preventDefault()};return(0,T.jsx)(ze.Z,L()({ref:a},l))},_e=Ze,ke=e(39378),t=e(20221),We=A.Z.Item,te=Z.default.TextArea,Me=function j(o,l){return o.map(function(a){var s=_objectSpread(_objectSpread({},a),{},{key:a.id,disabled:l,children:a.children?j(a.children,l):[]});return s})},et=function j(o){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return o==null?void 0:o.map(function(a){var s=a.children,g=a.parentId,F=g===void 0?[]:g,G=l.slice();return G.push(F),s?L()(L()({},a),{},{path:G,children:j(s,G)}):L()(L()({},a),{},{path:G})})},Ue=function j(o){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=o==null?void 0:o.reduce(function(s,g){if(g.parentId==l){var F=j(o,g.id);F.length&&(g.children=F),g.key=g.id,g.value=g.id,g.title=g.name||g.categoryName,s.push(g)}return s},[]);return a},tt=function j(o){var l=[];return o==null||o.forEach(function(a){a.children&&a.children.length>0&&(l.push(a.id),l=l.concat(j(a.children)))}),l},be=function j(o,l){if(o.length===0)return[];var a=[],s=o.find(function(g){return g.subOrganizations&&(a=a.concat(g.subOrganizations)),g.id===l});return s||j(a,l)},He=function j(o,l,a){if(o.parentId===l)return!0;var s=a.find(function(g){return g.id===o.parentId});return s?j(s,l,a):!1},Re=function(o){var l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,a=[],s=_createForOfIteratorHelper(o),g;try{for(s.s();!(g=s.n()).done;){var F=g.value,G=!0,xe=_createForOfIteratorHelper(o),Se;try{for(xe.s();!(Se=xe.n()).done;){var Je=Se.value;if(Je.parentId===F.id){G=!1;break}}}catch(Le){xe.e(Le)}finally{xe.f()}G&&(l===null||He(F,l,o))&&a.push(F)}}catch(Le){s.e(Le)}finally{s.f()}return a},ne=function(o){var l=o.reduce(function(a,s){var g,F,G,xe,Se,Je,Le=s.dataType,ce=s.name,Ke=s.comment,Ce=s.placeholder,ct=s.description,ut=s.require,dt=s.visible,S=s.sliderConfig;if(dt===!1)return a;var re=(0,T.jsx)(Z.default,{});switch(Le){case"string":ce==="password"?re=(0,T.jsx)(Z.default.Password,{placeholder:Ce}):re=(0,T.jsx)(Z.default,{placeholder:Ce});break;case"password":{re=(0,T.jsx)(Z.default.Password,{placeholder:Ce,visibilityToggle:ce!=="apiKey"});break}case"longText":re=(0,T.jsx)(te,{placeholder:Ce,style:{height:100}});break;case"number":re=(0,T.jsx)(_e,{placeholder:Ce,style:{width:"100%"}});break;case"slider":re=(0,T.jsx)(Q.Z,{min:S!=null&&(g=S.start)!==null&&g!==void 0&&g.value?Number(S==null||(F=S.start)===null||F===void 0?void 0:F.value):0,max:S!=null&&(G=S.end)!==null&&G!==void 0&&G.value?Number(S==null||(xe=S.end)===null||xe===void 0?void 0:xe.value):1,step:S!=null&&S.unit?Number(S==null?void 0:S.unit):.1,marks:{0:(S==null||(Se=S.start)===null||Se===void 0?void 0:Se.text)||"\u7CBE\u786E",1:(S==null||(Je=S.end)===null||Je===void 0?void 0:Je.text)||"\u968F\u673A"}});break;case"bool":return a.push((0,T.jsx)(We,{name:ce,label:Ke,valuePropName:"checked",getValueFromEvent:function(Ye){return Ye===!0?"true":"false"},getValueProps:function(Ye){return{checked:Ye==="true"}},children:(0,T.jsx)(ie.Z,{})},ce)),a;case"list":{var nt=s.candidateValues,it=nt===void 0?[]:nt,vt=it.map(function(J){return(0,ke.isString)(J)?{label:J,value:J}:J!=null&&J.label?J:{label:J,value:J}});re=(0,T.jsx)(ee.default,{style:{width:"100%"},options:vt,placeholder:Ce});break}default:re=(0,T.jsx)(Z.default,{placeholder:Ce});break}return a.push((0,T.jsx)(We,{name:ce,rules:[{required:!!ut,message:"\u8BF7\u8F93\u5165".concat(Ke)}],label:(0,T.jsx)(ge.Z,{title:Ke,subTitle:ct}),children:re},ce)),a},[]);return k()(l)},Xe=function(o,l){return"".concat(o,"-").concat(l)},Ge=function(o,l){t.history.push("/model/domain/".concat(o,"/").concat(l))},at=function(o,l,a){t.history.push("/model/domain/manager/".concat(o,"/").concat(l).concat(a?"/".concat(a):""))},q=function(o,l,a,s){t.history.push("/model/metric/".concat(o,"/").concat(l,"/").concat(a).concat(s?"/".concat(s):""))},ot=function(o,l,a){t.history.push("/model/dataset/".concat(o,"/").concat(l).concat(a?"/".concat(a):""))},st=function(o,l,a,s){t.history.push("/model/dimension/".concat(o,"/").concat(l,"/").concat(a).concat(s?"/".concat(s):""))}},82819:function(he,Y,e){"use strict";e.r(Y),e.d(Y,{default:function(){return Pt}});var pe=e(90819),k=e.n(pe),me=e(10154),B=e.n(me),L=e(73193),A=e.n(L),Z=e(89933),Q=e.n(Z),ie=e(45332),ee=e.n(ie),ge={},X=e(20263),ye=e(7477),le=e(69367),ze=e(60654),T=e(2206),Ze=e(34284),_e=e(56430),ke=e(18191),t=e(44194),We=e(51865),te=e.n(We),Me=e(97721),et=e(43389),Ue=e(33729),tt=e(30844),be=e(20488),He=e(34927),Re=e(47506),ne=e(88370);const Xe=n=>{const{componentCls:r}=n;return{[r]:{position:"fixed",zIndex:n.zIndexPopup}}},Ge=n=>({zIndexPopup:n.zIndexBase+10});var at=(0,ne.I$)("Affix",Xe,Ge);function q(n){return n!==window?n.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function ot(n,r,i){if(i!==void 0&&Math.round(r.top)>Math.round(n.top)-i)return i+r.top}function st(n,r,i){if(i!==void 0&&Math.round(r.bottom)<Math.round(n.bottom)+i){const y=window.innerHeight-r.bottom;return i+y}}var j=function(n,r){var i={};for(var y in n)Object.prototype.hasOwnProperty.call(n,y)&&r.indexOf(y)<0&&(i[y]=n[y]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var x=0,y=Object.getOwnPropertySymbols(n);x<y.length;x++)r.indexOf(y[x])<0&&Object.prototype.propertyIsEnumerable.call(n,y[x])&&(i[y[x]]=n[y[x]]);return i};const o=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function l(){return typeof window!="undefined"?window:null}const a=0,s=1;var F=t.forwardRef((n,r)=>{var i;const{style:y,offsetTop:x,offsetBottom:M,prefixCls:R,className:W,rootClassName:Te,children:Ee,target:U,onChange:N,onTestUpdatePosition:Fe}=n,K=j(n,["style","offsetTop","offsetBottom","prefixCls","className","rootClassName","children","target","onChange","onTestUpdatePosition"]),{getPrefixCls:ae,getTargetContainer:je}=t.useContext(Re.E_),ue=ae("affix",R),[$,Ne]=t.useState(!1),[V,Pe]=t.useState(),[H,Qe]=t.useState(),oe=t.useRef(a),Ae=t.useRef(null),C=t.useRef(null),m=t.useRef(null),h=t.useRef(null),f=t.useRef(null),u=(i=U!=null?U:je)!==null&&i!==void 0?i:l,P=M===void 0&&x===void 0?0:x,O=()=>{if(oe.current!==s||!h.current||!m.current||!u)return;const I=u();if(I){const v={status:a},d=q(m.current);if(d.top===0&&d.left===0&&d.width===0&&d.height===0)return;const ve=q(I),De=ot(d,ve,P),Ve=st(d,ve,M);De!==void 0?(v.affixStyle={position:"fixed",top:De,width:d.width,height:d.height},v.placeholderStyle={width:d.width,height:d.height}):Ve!==void 0&&(v.affixStyle={position:"fixed",bottom:Ve,width:d.width,height:d.height},v.placeholderStyle={width:d.width,height:d.height}),v.lastAffix=!!v.affixStyle,$!==v.lastAffix&&(N==null||N(v.lastAffix)),oe.current=v.status,Pe(v.affixStyle),Qe(v.placeholderStyle),Ne(v.lastAffix)}},c=()=>{oe.current=s,O()},D=(0,He.Z)(()=>{c()}),w=(0,He.Z)(()=>{if(u&&V){const I=u();if(I&&m.current){const v=q(I),d=q(m.current),ve=ot(d,v,P),De=st(d,v,M);if(ve!==void 0&&V.top===ve||De!==void 0&&V.bottom===De)return}}c()}),de=()=>{const I=u==null?void 0:u();I&&(o.forEach(v=>{var d;C.current&&((d=Ae.current)===null||d===void 0||d.removeEventListener(v,C.current)),I==null||I.addEventListener(v,w)}),Ae.current=I,C.current=w)},Ie=()=>{f.current&&(clearTimeout(f.current),f.current=null);const I=u==null?void 0:u();o.forEach(v=>{var d;I==null||I.removeEventListener(v,w),C.current&&((d=Ae.current)===null||d===void 0||d.removeEventListener(v,C.current))}),D.cancel(),w.cancel()};t.useImperativeHandle(r,()=>({updatePosition:D})),t.useEffect(()=>(f.current=setTimeout(de),()=>Ie()),[]),t.useEffect(()=>(de(),()=>Ie()),[U,V,$,x,M]),t.useEffect(()=>{D()},[U,x,M]);const[z,fe,Oe]=at(ue),$e=te()(Te,fe,ue,Oe),qe=te()({[$e]:V});return z(t.createElement(be.Z,{onResize:D},t.createElement("div",Object.assign({style:y,className:W,ref:m},K),V&&t.createElement("div",{style:H,"aria-hidden":"true"}),t.createElement("div",{className:qe,ref:h,style:V},t.createElement(be.Z,{onResize:D},Ee)))))}),G=e(94192),Se=t.createContext(void 0),Le=n=>{const{href:r,title:i,prefixCls:y,children:x,className:M,target:R,replace:W}=n,Te=t.useContext(Se),{registerLink:Ee,unregisterLink:U,scrollTo:N,onClick:Fe,activeLink:K,direction:ae}=Te||{};t.useEffect(()=>(Ee==null||Ee(r),()=>{U==null||U(r)}),[r]);const je=H=>{if(Fe==null||Fe(H,{title:i,href:r}),N==null||N(r),H.defaultPrevented)return;if(r.startsWith("http://")||r.startsWith("https://")){W&&(H.preventDefault(),window.location.replace(r));return}H.preventDefault();const oe=W?"replaceState":"pushState";window.history[oe](null,"",r)},{getPrefixCls:ue}=t.useContext(Re.E_),$=ue("anchor",y),Ne=K===r,V=te()(`${$}-link`,M,{[`${$}-link-active`]:Ne}),Pe=te()(`${$}-link-title`,{[`${$}-link-title-active`]:Ne});return t.createElement("div",{className:V},t.createElement("a",{className:Pe,href:r,title:typeof i=="string"?i:"",target:R,onClick:je},i),ae!=="horizontal"?x:null)},ce=e(40044),Ke=e(19107),Ce=e(77167);const ct=n=>{const{componentCls:r,holderOffsetBlock:i,motionDurationSlow:y,lineWidthBold:x,colorPrimary:M,lineType:R,colorSplit:W,calc:Te}=n;return{[`${r}-wrapper`]:{marginBlockStart:Te(i).mul(-1).equal(),paddingBlockStart:i,[r]:Object.assign(Object.assign({},(0,Ke.Wf)(n)),{position:"relative",paddingInlineStart:x,[`${r}-link`]:{paddingBlock:n.linkPaddingBlock,paddingInline:`${(0,ce.unit)(n.linkPaddingInlineStart)} 0`,"&-title":Object.assign(Object.assign({},Ke.vS),{position:"relative",display:"block",marginBlockEnd:n.anchorTitleBlock,color:n.colorText,transition:`all ${n.motionDurationSlow}`,"&:only-child":{marginBlockEnd:0}}),[`&-active > ${r}-link-title`]:{color:n.colorPrimary},[`${r}-link`]:{paddingBlock:n.anchorPaddingBlockSecondary}}}),[`&:not(${r}-wrapper-horizontal)`]:{[r]:{"&::before":{position:"absolute",insetInlineStart:0,top:0,height:"100%",borderInlineStart:`${(0,ce.unit)(x)} ${R} ${W}`,content:'" "'},[`${r}-ink`]:{position:"absolute",insetInlineStart:0,display:"none",transform:"translateY(-50%)",transition:`top ${y} ease-in-out`,width:x,backgroundColor:M,[`&${r}-ink-visible`]:{display:"inline-block"}}}},[`${r}-fixed ${r}-ink ${r}-ink`]:{display:"none"}}}},ut=n=>{const{componentCls:r,motionDurationSlow:i,lineWidthBold:y,colorPrimary:x}=n;return{[`${r}-wrapper-horizontal`]:{position:"relative","&::before":{position:"absolute",left:{_skip_check_:!0,value:0},right:{_skip_check_:!0,value:0},bottom:0,borderBottom:`${(0,ce.unit)(n.lineWidth)} ${n.lineType} ${n.colorSplit}`,content:'" "'},[r]:{overflowX:"scroll",position:"relative",display:"flex",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"},[`${r}-link:first-of-type`]:{paddingInline:0},[`${r}-ink`]:{position:"absolute",bottom:0,transition:`left ${i} ease-in-out, width ${i} ease-in-out`,height:y,backgroundColor:x}}}}},dt=n=>({linkPaddingBlock:n.paddingXXS,linkPaddingInlineStart:n.padding});var S=(0,ne.I$)("Anchor",n=>{const{fontSize:r,fontSizeLG:i,paddingXXS:y,calc:x}=n,M=(0,Ce.mergeToken)(n,{holderOffsetBlock:y,anchorPaddingBlockSecondary:x(y).div(2).equal(),anchorTitleBlock:x(r).div(14).mul(3).equal(),anchorBallSize:x(i).div(2).equal()});return[ct(M),ut(M)]},dt);function re(){return window}function nt(n,r){if(!n.getClientRects().length)return 0;const i=n.getBoundingClientRect();return i.width||i.height?r===window?i.top-n.ownerDocument.documentElement.clientTop:i.top-r.getBoundingClientRect().top:i.top}const it=/#([\S ]+)$/;var J=n=>{var r;const{rootClassName:i,prefixCls:y,className:x,style:M,offsetTop:R,affix:W=!0,showInkInFixed:Te=!1,children:Ee,items:U,direction:N="vertical",bounds:Fe,targetOffset:K,onClick:ae,onChange:je,getContainer:ue,getCurrentAnchor:$,replace:Ne}=n,[V,Pe]=t.useState([]),[H,Qe]=t.useState(null),oe=t.useRef(H),Ae=t.useRef(null),C=t.useRef(null),m=t.useRef(!1),{direction:h,getPrefixCls:f,className:u,style:P}=(0,Re.dj)("anchor"),{getTargetContainer:O}=t.useContext(Re.E_),c=f("anchor",y),D=(0,G.Z)(c),[w,de,Ie]=S(c,D),z=(r=ue!=null?ue:O)!==null&&r!==void 0?r:re,fe=JSON.stringify(V),Oe=(0,Me.Z)(p=>{V.includes(p)||Pe(E=>[].concat((0,ke.Z)(E),[p]))}),$e=(0,Me.Z)(p=>{V.includes(p)&&Pe(E=>E.filter(se=>se!==p))}),qe=()=>{var p;const E=(p=Ae.current)===null||p===void 0?void 0:p.querySelector(`.${c}-link-title-active`);if(E&&C.current){const{style:se}=C.current,_=N==="horizontal";se.top=_?"":`${E.offsetTop+E.clientHeight/2}px`,se.height=_?"":`${E.clientHeight}px`,se.left=_?`${E.offsetLeft}px`:"",se.width=_?`${E.clientWidth}px`:"",_&&(0,et.Z)(E,{scrollMode:"if-needed",block:"nearest"})}},I=(p,E=0,se=5)=>{const _=[],ft=z();return p.forEach(we=>{const Be=it.exec(we==null?void 0:we.toString());if(!Be)return;const rt=document.getElementById(Be[1]);if(rt){const yt=nt(rt,ft);yt<=E+se&&_.push({link:we,top:yt})}}),_.length?_.reduce((Be,rt)=>rt.top>Be.top?rt:Be).link:""},v=(0,Me.Z)(p=>{if(oe.current===p)return;const E=typeof $=="function"?$(p):p;Qe(E),oe.current=E,je==null||je(p)}),d=t.useCallback(()=>{if(m.current)return;const p=I(V,K!==void 0?K:R||0,Fe);v(p)},[fe,K,R]),ve=t.useCallback(p=>{v(p);const E=it.exec(p);if(!E)return;const se=document.getElementById(E[1]);if(!se)return;const _=z(),ft=(0,Ue.Z)(_),we=nt(se,_);let Be=ft+we;Be-=K!==void 0?K:R||0,m.current=!0,(0,tt.Z)(Be,{getContainer:z,callback(){m.current=!1}})},[K,R]),De=te()(de,Ie,D,i,`${c}-wrapper`,{[`${c}-wrapper-horizontal`]:N==="horizontal",[`${c}-rtl`]:h==="rtl"},x,u),Ve=te()(c,{[`${c}-fixed`]:!W&&!Te}),lt=te()(`${c}-ink`,{[`${c}-ink-visible`]:H}),At=Object.assign(Object.assign({maxHeight:R?`calc(100vh - ${R}px)`:"100vh"},P),M),mt=p=>Array.isArray(p)?p.map(E=>t.createElement(Le,Object.assign({replace:Ne},E,{key:E.key}),N==="vertical"&&mt(E.children))):null,gt=t.createElement("div",{ref:Ae,className:De,style:At},t.createElement("div",{className:Ve},t.createElement("span",{className:lt,ref:C}),"items"in n?mt(U):Ee));t.useEffect(()=>{const p=z();return d(),p==null||p.addEventListener("scroll",d),()=>{p==null||p.removeEventListener("scroll",d)}},[fe]),t.useEffect(()=>{typeof $=="function"&&v($(oe.current||""))},[$]),t.useEffect(()=>{qe()},[N,$,fe,H]);const It=t.useMemo(()=>({registerLink:Oe,unregisterLink:$e,scrollTo:ve,activeLink:H,onClick:ae,direction:N}),[H,ae,ve,N]),$t=W&&typeof W=="object"?W:void 0;return w(t.createElement(Se.Provider,{value:It},W?t.createElement(F,Object.assign({offsetTop:R,target:z},$t),gt):gt))};const Ye=J;Ye.Link=Le;var xt=Ye,ht=e(92416),pt=e(7595),St=e(70348),Ct=e(39378),Tt=e(88054),b=e(31549),Et=X.Z.Item,jt=function(){var r=(0,t.useState)({}),i=ee()(r,2),y=i[0],x=i[1],M=(0,t.useState)([]),R=ee()(M,2),W=R[0],Te=R[1],Ee=(0,t.useState)(),U=ee()(Ee,2),N=U[0],Fe=U[1],K=(0,t.useRef)(),ae=(0,t.useRef)();(0,t.useEffect)(function(){Ne()},[]);var je=X.Z.useForm(),ue=ee()(je,1),$=ue[0],Ne=function(){var C=Q()(k()().mark(function m(){var h,f,u,P,O,c,D,w,de;return k()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:return z.next=2,(0,ht.kH)();case 2:h=z.sent,f=h.code,u=h.data,P=h.msg,f===200&&u?(O=u.parameters,c=O===void 0?[]:O,D=u.admins,w=D===void 0?[]:D,de=c.reduce(function(fe,Oe){return A()(A()({},fe),{},B()({},Oe.name,Oe))},{}),K.current=de,Pe(c),V(c,w),Fe(u)):ye.ZP.error(P);case 7:case"end":return z.stop()}},m)}));return function(){return C.apply(this,arguments)}}(),V=function(m,h){var f=H(m);ae.current=f;var u=Qe(h,m);Object.keys(u).forEach(function(P){var O=f[P]||{},c=Object.values(O);Array.isArray(c)&&Ae(P,u,!0)})},Pe=function(m){var h=(0,Ct.groupBy)(m,"module"),f=Object.keys(h).map(function(u){return{key:u,href:"#".concat(u),title:u}});Te(f),x(h)},H=function(m){var h={};return m.forEach(function(f){var u=f.name,P=f.dependencies;Array.isArray(P)&&P.forEach(function(O){var c=O.name;h[c]?h[c]=A()(A()({},h[c]),{},B()({},u,f)):h[c]=B()({},u,f)})}),h},Qe=function(m,h){var f=h.reduce(function(u,P){var O=P.name,c=P.value;return A()(A()({},u),{},B()({},O,c))},{admins:m});return $.setFieldsValue(f),f},oe=function(){var C=Q()(k()().mark(function m(){var h,f,u,P;return k()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.next=2,$.validateFields();case 2:return h=c.sent,c.next=5,(0,ht.ST)(A()(A()({},N),{},{admins:h.admins,parameters:N.parameters.map(function(D){var w=D.name;return h[w]!==void 0?A()(A()({},D),{},{value:h[w]}):D})}));case 5:f=c.sent,u=f.code,P=f.msg,u===200?ye.ZP.success("\u4FDD\u5B58\u6210\u529F"):ye.ZP.error(P);case 9:case"end":return c.stop()}},m)}));return function(){return C.apply(this,arguments)}}(),Ae=function(m,h){var f,u=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,P=ae==null||(f=ae.current)===null||f===void 0?void 0:f[m];if(P){var O=Object.values(P);if(Array.isArray(O)){var c=A()({},K.current),D=h;O.forEach(function(w){var de=[],Ie=[],z=w.dependencies,fe=w.name;z.forEach(function(v){var d,ve=v.name,De=v.setDefaultValue,Ve=D[ve],lt=(d=v.show)===null||d===void 0?void 0:d.includesValue;Array.isArray(lt)&&de.push(lt.includes(Ve)),De&&Ve&&Ie.push(A()({excuteItem:fe},v))});var Oe=de.every(function(v){return v});c[fe].visible=Oe;var $e=Ie[Ie.length-1],qe=$e==null?void 0:$e.setDefaultValue;if(qe){var I=qe[D[$e.name]];I&&!u&&$.setFieldValue($e.excuteItem,I)}}),Pe(Object.values(c))}}};return(0,b.jsx)(b.Fragment,{children:(0,b.jsx)("div",{style:{margin:"40px auto",width:1200},children:(0,b.jsxs)(le.Z,{children:[(0,b.jsx)(ze.Z,{span:18,children:(0,b.jsx)(pt.Z,{title:"\u7CFB\u7EDF\u8BBE\u7F6E",extra:(0,b.jsx)(T.Z,{children:(0,b.jsx)(Ze.ZP,{type:"primary",onClick:function(){oe()},children:"\u4FDD \u5B58"})}),children:(0,b.jsxs)(X.Z,{form:$,layout:"vertical",className:ge.form,onValuesChange:function(m,h){var f=Object.keys(m)[0];Ae(f,h)},children:[(0,b.jsx)(Et,{name:"admins",label:"\u7BA1\u7406\u5458",children:(0,b.jsx)(St.Z,{placeholder:"\u8BF7\u9080\u8BF7\u56E2\u961F\u6210\u5458"})}),(0,b.jsx)(_e.Z,{}),(0,b.jsx)(T.Z,{direction:"vertical",style:{width:"100%"},size:35,children:Object.keys(y).map(function(C){var m=y[C];return(0,b.jsx)(pt.Z,{title:(0,b.jsx)("span",{style:{color:"#0057ff"},children:C}),bordered:!0,id:C,children:(0,Tt.vX)(m)},C)})})]})})}),(0,b.jsx)(ze.Z,{span:6,style:{background:"#fff"},children:(0,b.jsx)("div",{style:{marginTop:20},children:(0,b.jsx)(xt,{items:W})})})]})})})},Pt=jt},34927:function(he,Y,e){"use strict";var pe=e(18191),k=e(24589);function me(B){let L;const A=Q=>()=>{L=null,B.apply(void 0,(0,pe.Z)(Q))},Z=(...Q)=>{L==null&&(L=(0,k.Z)(A(Q)))};return Z.cancel=()=>{k.Z.cancel(L),L=null},Z}Y.Z=me},49841:function(he){function Y(e){if(e==null)throw new TypeError("Cannot destructure "+e)}he.exports=Y,he.exports.__esModule=!0,he.exports.default=he.exports}}]);
