!(function(){"use strict";var Ve=Object.defineProperty;var Te=Object.getOwnPropertySymbols;var ze=Object.prototype.hasOwnProperty,Ie=Object.prototype.propertyIsEnumerable;var ke=(Z,R,z)=>R in Z?Ve(Z,R,{enumerable:!0,configurable:!0,writable:!0,value:z}):Z[R]=z,Q=(Z,R)=>{for(var z in R||(R={}))ze.call(R,z)&&ke(Z,z,R[z]);if(Te)for(var z of Te(R))Ie.call(R,z)&&ke(Z,z,R[z]);return Z};(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6386],{86386:function(Z,R,z){var tt,et;z.d(R,{diagram:function(){return Fe}});var e=z(29134),xt=z(69471),Ct=function(){var t=(0,e.eW)(function(G,o,l,g){for(l=l||{},g=G.length;g--;l[G[g]]=o);return l},"o"),r=[1,3],x=[1,4],d=[1,5],h=[1,6],p=[1,7],y=[1,4,5,10,12,13,14,18,25,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],m=[1,4,5,10,12,13,14,18,25,28,35,37,39,41,42,48,50,51,52,53,54,55,56,57,60,61,63,64,65,66,67],n=[55,56,57],A=[2,36],u=[1,37],k=[1,36],q=[1,38],b=[1,35],T=[1,43],c=[1,41],N=[1,14],j=[1,23],K=[1,18],At=[1,19],St=[1,20],ft=[1,21],Et=[1,22],gt=[1,24],pt=[1,25],yt=[1,26],bt=[1,27],Tt=[1,28],kt=[1,29],w=[1,32],a=[1,33],S=[1,34],v=[1,39],P=[1,40],F=[1,42],W=[1,44],H=[1,62],X=[1,61],C=[4,5,8,10,12,13,14,18,44,47,49,55,56,57,63,64,65,66,67],ee=[1,65],ie=[1,66],ae=[1,67],ne=[1,68],re=[1,69],se=[1,70],oe=[1,71],le=[1,72],he=[1,73],ce=[1,74],de=[1,75],ue=[1,76],I=[4,5,6,7,8,9,10,11,12,13,14,15,18],it=[1,90],at=[1,91],nt=[1,92],rt=[1,99],st=[1,93],ot=[1,96],lt=[1,94],ht=[1,95],ct=[1,97],dt=[1,98],Dt=[1,102],xe=[10,55,56,57],U=[4,5,6,8,10,11,13,17,18,19,20,55,56,57],Vt={trace:(0,e.eW)(function(){},"trace"),yy:{},symbols_:{error:2,idStringToken:3,ALPHA:4,NUM:5,NODE_STRING:6,DOWN:7,MINUS:8,DEFAULT:9,COMMA:10,COLON:11,AMP:12,BRKT:13,MULT:14,UNICODE_TEXT:15,styleComponent:16,UNIT:17,SPACE:18,STYLE:19,PCT:20,idString:21,style:22,stylesOpt:23,classDefStatement:24,CLASSDEF:25,start:26,eol:27,QUADRANT:28,document:29,line:30,statement:31,axisDetails:32,quadrantDetails:33,points:34,title:35,title_value:36,acc_title:37,acc_title_value:38,acc_descr:39,acc_descr_value:40,acc_descr_multiline_value:41,section:42,text:43,point_start:44,point_x:45,point_y:46,class_name:47,"X-AXIS":48,"AXIS-TEXT-DELIMITER":49,"Y-AXIS":50,QUADRANT_1:51,QUADRANT_2:52,QUADRANT_3:53,QUADRANT_4:54,NEWLINE:55,SEMI:56,EOF:57,alphaNumToken:58,textNoTagsToken:59,STR:60,MD_STR:61,alphaNum:62,PUNCTUATION:63,PLUS:64,EQUALS:65,DOT:66,UNDERSCORE:67,$accept:0,$end:1},terminals_:{2:"error",4:"ALPHA",5:"NUM",6:"NODE_STRING",7:"DOWN",8:"MINUS",9:"DEFAULT",10:"COMMA",11:"COLON",12:"AMP",13:"BRKT",14:"MULT",15:"UNICODE_TEXT",17:"UNIT",18:"SPACE",19:"STYLE",20:"PCT",25:"CLASSDEF",28:"QUADRANT",35:"title",36:"title_value",37:"acc_title",38:"acc_title_value",39:"acc_descr",40:"acc_descr_value",41:"acc_descr_multiline_value",42:"section",44:"point_start",45:"point_x",46:"point_y",47:"class_name",48:"X-AXIS",49:"AXIS-TEXT-DELIMITER",50:"Y-AXIS",51:"QUADRANT_1",52:"QUADRANT_2",53:"QUADRANT_3",54:"QUADRANT_4",55:"NEWLINE",56:"SEMI",57:"EOF",60:"STR",61:"MD_STR",63:"PUNCTUATION",64:"PLUS",65:"EQUALS",66:"DOT",67:"UNDERSCORE"},productions_:[0,[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[16,1],[21,1],[21,2],[22,1],[22,2],[23,1],[23,3],[24,5],[26,2],[26,2],[26,2],[29,0],[29,2],[30,2],[31,0],[31,1],[31,2],[31,1],[31,1],[31,1],[31,2],[31,2],[31,2],[31,1],[31,1],[34,4],[34,5],[34,5],[34,6],[32,4],[32,3],[32,2],[32,4],[32,3],[32,2],[33,2],[33,2],[33,2],[33,2],[27,1],[27,1],[27,1],[43,1],[43,2],[43,1],[43,1],[62,1],[62,2],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[58,1],[59,1],[59,1],[59,1]],performAction:(0,e.eW)(function(o,l,g,f,_,i,qt){var s=i.length-1;switch(_){case 23:this.$=i[s];break;case 24:this.$=i[s-1]+""+i[s];break;case 26:this.$=i[s-1]+i[s];break;case 27:this.$=[i[s].trim()];break;case 28:i[s-2].push(i[s].trim()),this.$=i[s-2];break;case 29:this.$=i[s-4],f.addClass(i[s-2],i[s]);break;case 37:this.$=[];break;case 42:this.$=i[s].trim(),f.setDiagramTitle(this.$);break;case 43:this.$=i[s].trim(),f.setAccTitle(this.$);break;case 44:case 45:this.$=i[s].trim(),f.setAccDescription(this.$);break;case 46:f.addSection(i[s].substr(8)),this.$=i[s].substr(8);break;case 47:f.addPoint(i[s-3],"",i[s-1],i[s],[]);break;case 48:f.addPoint(i[s-4],i[s-3],i[s-1],i[s],[]);break;case 49:f.addPoint(i[s-4],"",i[s-2],i[s-1],i[s]);break;case 50:f.addPoint(i[s-5],i[s-4],i[s-2],i[s-1],i[s]);break;case 51:f.setXAxisLeftText(i[s-2]),f.setXAxisRightText(i[s]);break;case 52:i[s-1].text+=" \u27F6 ",f.setXAxisLeftText(i[s-1]);break;case 53:f.setXAxisLeftText(i[s]);break;case 54:f.setYAxisBottomText(i[s-2]),f.setYAxisTopText(i[s]);break;case 55:i[s-1].text+=" \u27F6 ",f.setYAxisBottomText(i[s-1]);break;case 56:f.setYAxisBottomText(i[s]);break;case 57:f.setQuadrant1Text(i[s]);break;case 58:f.setQuadrant2Text(i[s]);break;case 59:f.setQuadrant3Text(i[s]);break;case 60:f.setQuadrant4Text(i[s]);break;case 64:this.$={text:i[s],type:"text"};break;case 65:this.$={text:i[s-1].text+""+i[s],type:i[s-1].type};break;case 66:this.$={text:i[s],type:"text"};break;case 67:this.$={text:i[s],type:"markdown"};break;case 68:this.$=i[s];break;case 69:this.$=i[s-1]+""+i[s];break}},"anonymous"),table:[{18:r,26:1,27:2,28:x,55:d,56:h,57:p},{1:[3]},{18:r,26:8,27:2,28:x,55:d,56:h,57:p},{18:r,26:9,27:2,28:x,55:d,56:h,57:p},t(y,[2,33],{29:10}),t(m,[2,61]),t(m,[2,62]),t(m,[2,63]),{1:[2,30]},{1:[2,31]},t(n,A,{30:11,31:12,24:13,32:15,33:16,34:17,43:30,58:31,1:[2,32],4:u,5:k,10:q,12:b,13:T,14:c,18:N,25:j,35:K,37:At,39:St,41:ft,42:Et,48:gt,50:pt,51:yt,52:bt,53:Tt,54:kt,60:w,61:a,63:S,64:v,65:P,66:F,67:W}),t(y,[2,34]),{27:45,55:d,56:h,57:p},t(n,[2,37]),t(n,A,{24:13,32:15,33:16,34:17,43:30,58:31,31:46,4:u,5:k,10:q,12:b,13:T,14:c,18:N,25:j,35:K,37:At,39:St,41:ft,42:Et,48:gt,50:pt,51:yt,52:bt,53:Tt,54:kt,60:w,61:a,63:S,64:v,65:P,66:F,67:W}),t(n,[2,39]),t(n,[2,40]),t(n,[2,41]),{36:[1,47]},{38:[1,48]},{40:[1,49]},t(n,[2,45]),t(n,[2,46]),{18:[1,50]},{4:u,5:k,10:q,12:b,13:T,14:c,43:51,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,10:q,12:b,13:T,14:c,43:52,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,10:q,12:b,13:T,14:c,43:53,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,10:q,12:b,13:T,14:c,43:54,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,10:q,12:b,13:T,14:c,43:55,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,10:q,12:b,13:T,14:c,43:56,58:31,60:w,61:a,63:S,64:v,65:P,66:F,67:W},{4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,44:[1,57],47:[1,58],58:60,59:59,63:S,64:v,65:P,66:F,67:W},t(C,[2,64]),t(C,[2,66]),t(C,[2,67]),t(C,[2,70]),t(C,[2,71]),t(C,[2,72]),t(C,[2,73]),t(C,[2,74]),t(C,[2,75]),t(C,[2,76]),t(C,[2,77]),t(C,[2,78]),t(C,[2,79]),t(C,[2,80]),t(y,[2,35]),t(n,[2,38]),t(n,[2,42]),t(n,[2,43]),t(n,[2,44]),{3:64,4:ee,5:ie,6:ae,7:ne,8:re,9:se,10:oe,11:le,12:he,13:ce,14:de,15:ue,21:63},t(n,[2,53],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,49:[1,77],63:S,64:v,65:P,66:F,67:W}),t(n,[2,56],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,49:[1,78],63:S,64:v,65:P,66:F,67:W}),t(n,[2,57],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),t(n,[2,58],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),t(n,[2,59],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),t(n,[2,60],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),{45:[1,79]},{44:[1,80]},t(C,[2,65]),t(C,[2,81]),t(C,[2,82]),t(C,[2,83]),{3:82,4:ee,5:ie,6:ae,7:ne,8:re,9:se,10:oe,11:le,12:he,13:ce,14:de,15:ue,18:[1,81]},t(I,[2,23]),t(I,[2,1]),t(I,[2,2]),t(I,[2,3]),t(I,[2,4]),t(I,[2,5]),t(I,[2,6]),t(I,[2,7]),t(I,[2,8]),t(I,[2,9]),t(I,[2,10]),t(I,[2,11]),t(I,[2,12]),t(n,[2,52],{58:31,43:83,4:u,5:k,10:q,12:b,13:T,14:c,60:w,61:a,63:S,64:v,65:P,66:F,67:W}),t(n,[2,55],{58:31,43:84,4:u,5:k,10:q,12:b,13:T,14:c,60:w,61:a,63:S,64:v,65:P,66:F,67:W}),{46:[1,85]},{45:[1,86]},{4:it,5:at,6:nt,8:rt,11:st,13:ot,16:89,17:lt,18:ht,19:ct,20:dt,22:88,23:87},t(I,[2,24]),t(n,[2,51],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),t(n,[2,54],{59:59,58:60,4:u,5:k,8:H,10:q,12:b,13:T,14:c,18:X,63:S,64:v,65:P,66:F,67:W}),t(n,[2,47],{22:88,16:89,23:100,4:it,5:at,6:nt,8:rt,11:st,13:ot,17:lt,18:ht,19:ct,20:dt}),{46:[1,101]},t(n,[2,29],{10:Dt}),t(xe,[2,27],{16:103,4:it,5:at,6:nt,8:rt,11:st,13:ot,17:lt,18:ht,19:ct,20:dt}),t(U,[2,25]),t(U,[2,13]),t(U,[2,14]),t(U,[2,15]),t(U,[2,16]),t(U,[2,17]),t(U,[2,18]),t(U,[2,19]),t(U,[2,20]),t(U,[2,21]),t(U,[2,22]),t(n,[2,49],{10:Dt}),t(n,[2,48],{22:88,16:89,23:104,4:it,5:at,6:nt,8:rt,11:st,13:ot,17:lt,18:ht,19:ct,20:dt}),{4:it,5:at,6:nt,8:rt,11:st,13:ot,16:89,17:lt,18:ht,19:ct,20:dt,22:105},t(U,[2,26]),t(n,[2,50],{10:Dt}),t(xe,[2,28],{16:103,4:it,5:at,6:nt,8:rt,11:st,13:ot,17:lt,18:ht,19:ct,20:dt})],defaultActions:{8:[2,30],9:[2,31]},parseError:(0,e.eW)(function(o,l){if(l.recoverable)this.trace(o);else{var g=new Error(o);throw g.hash=l,g}},"parseError"),parse:(0,e.eW)(function(o){var l=this,g=[0],f=[],_=[null],i=[],qt=this.table,s="",Pt=0,fe=0,ge=0,Ce=2,pe=1,Le=i.slice.call(arguments,1),L=Object.create(this.lexer),J={yy:{}};for(var zt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,zt)&&(J.yy[zt]=this.yy[zt]);L.setInput(o,J.yy),J.yy.lexer=L,J.yy.parser=this,typeof L.yylloc=="undefined"&&(L.yylloc={});var It=L.yylloc;i.push(It);var Ee=L.options&&L.options.ranges;typeof J.yy.parseError=="function"?this.parseError=J.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function De(B){g.length=g.length-2*B,_.length=_.length-B,i.length=i.length-B}(0,e.eW)(De,"popStack");function ye(){var B;return B=f.pop()||L.lex()||pe,typeof B!="number"&&(B instanceof Array&&(f=B,B=f.pop()),B=l.symbols_[B]||B),B}(0,e.eW)(ye,"lex");for(var V,wt,$,O,we,Bt,ut={},Ft,Y,be,Wt;;){if($=g[g.length-1],this.defaultActions[$]?O=this.defaultActions[$]:((V===null||typeof V=="undefined")&&(V=ye()),O=qt[$]&&qt[$][V]),typeof O=="undefined"||!O.length||!O[0]){var Rt="";Wt=[];for(Ft in qt[$])this.terminals_[Ft]&&Ft>Ce&&Wt.push("'"+this.terminals_[Ft]+"'");L.showPosition?Rt="Parse error on line "+(Pt+1)+`:
`+L.showPosition()+`
Expecting `+Wt.join(", ")+", got '"+(this.terminals_[V]||V)+"'":Rt="Parse error on line "+(Pt+1)+": Unexpected "+(V==pe?"end of input":"'"+(this.terminals_[V]||V)+"'"),this.parseError(Rt,{text:L.match,token:this.terminals_[V]||V,line:L.yylineno,loc:It,expected:Wt})}if(O[0]instanceof Array&&O.length>1)throw new Error("Parse Error: multiple actions possible at state: "+$+", token: "+V);switch(O[0]){case 1:g.push(V),_.push(L.yytext),i.push(L.yylloc),g.push(O[1]),V=null,wt?(V=wt,wt=null):(fe=L.yyleng,s=L.yytext,Pt=L.yylineno,It=L.yylloc,ge>0&&ge--);break;case 2:if(Y=this.productions_[O[1]][1],ut.$=_[_.length-Y],ut._$={first_line:i[i.length-(Y||1)].first_line,last_line:i[i.length-1].last_line,first_column:i[i.length-(Y||1)].first_column,last_column:i[i.length-1].last_column},Ee&&(ut._$.range=[i[i.length-(Y||1)].range[0],i[i.length-1].range[1]]),Bt=this.performAction.apply(ut,[s,fe,Pt,J.yy,O[1],_,i].concat(Le)),typeof Bt!="undefined")return Bt;Y&&(g=g.slice(0,-1*Y*2),_=_.slice(0,-1*Y),i=i.slice(0,-1*Y)),g.push(this.productions_[O[1]][0]),_.push(ut.$),i.push(ut._$),be=qt[g[g.length-2]][g[g.length-1]],g.push(be);break;case 3:return!0}}return!0},"parse")},We=function(){var G={EOF:1,parseError:(0,e.eW)(function(l,g){if(this.yy.parser)this.yy.parser.parseError(l,g);else throw new Error(l)},"parseError"),setInput:(0,e.eW)(function(o,l){return this.yy=l||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,e.eW)(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var l=o.match(/(?:\r\n?|\n).*/g);return l?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:(0,e.eW)(function(o){var l=o.length,g=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l),this.offset-=l;var f=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var _=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===f.length?this.yylloc.first_column:0)+f[f.length-g.length].length-g[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[_[0],_[0]+this.yyleng-l]),this.yyleng=this.yytext.length,this},"unput"),more:(0,e.eW)(function(){return this._more=!0,this},"more"),reject:(0,e.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,e.eW)(function(o){this.unput(this.match.slice(o))},"less"),pastInput:(0,e.eW)(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,e.eW)(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,e.eW)(function(){var o=this.pastInput(),l=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+l+"^"},"showPosition"),test_match:(0,e.eW)(function(o,l){var g,f,_;if(this.options.backtrack_lexer&&(_={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(_.yylloc.range=this.yylloc.range.slice(0))),f=o[0].match(/(?:\r\n?|\n).*/g),f&&(this.yylineno+=f.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:f?f[f.length-1].length-f[f.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],g=this.performAction.call(this,this.yy,this,l,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var i in _)this[i]=_[i];return!1}return!1},"test_match"),next:(0,e.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,l,g,f;this._more||(this.yytext="",this.match="");for(var _=this._currentRules(),i=0;i<_.length;i++)if(g=this._input.match(this.rules[_[i]]),g&&(!l||g[0].length>l[0].length)){if(l=g,f=i,this.options.backtrack_lexer){if(o=this.test_match(g,_[i]),o!==!1)return o;if(this._backtrack){l=!1;continue}else return!1}else if(!this.options.flex)break}return l?(o=this.test_match(l,_[f]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,e.eW)(function(){var l=this.next();return l||this.lex()},"lex"),begin:(0,e.eW)(function(l){this.conditionStack.push(l)},"begin"),popState:(0,e.eW)(function(){var l=this.conditionStack.length-1;return l>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,e.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,e.eW)(function(l){return l=this.conditionStack.length-1-Math.abs(l||0),l>=0?this.conditionStack[l]:"INITIAL"},"topState"),pushState:(0,e.eW)(function(l){this.begin(l)},"pushState"),stateStackSize:(0,e.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,e.eW)(function(l,g,f,_){var i=_;switch(f){case 0:break;case 1:break;case 2:return 55;case 3:break;case 4:return this.begin("title"),35;break;case 5:return this.popState(),"title_value";break;case 6:return this.begin("acc_title"),37;break;case 7:return this.popState(),"acc_title_value";break;case 8:return this.begin("acc_descr"),39;break;case 9:return this.popState(),"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 48;case 14:return 50;case 15:return 49;case 16:return 51;case 17:return 52;case 18:return 53;case 19:return 54;case 20:return 25;case 21:this.begin("md_string");break;case 22:return"MD_STR";case 23:this.popState();break;case 24:this.begin("string");break;case 25:this.popState();break;case 26:return"STR";case 27:this.begin("class_name");break;case 28:return this.popState(),47;break;case 29:return this.begin("point_start"),44;break;case 30:return this.begin("point_x"),45;break;case 31:this.popState();break;case 32:this.popState(),this.begin("point_y");break;case 33:return this.popState(),46;break;case 34:return 28;case 35:return 4;case 36:return 11;case 37:return 64;case 38:return 10;case 39:return 65;case 40:return 65;case 41:return 14;case 42:return 13;case 43:return 67;case 44:return 66;case 45:return 12;case 46:return 8;case 47:return 5;case 48:return 18;case 49:return 56;case 50:return 63;case 51:return 57}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?: *x-axis *)/i,/^(?: *y-axis *)/i,/^(?: *--+> *)/i,/^(?: *quadrant-1 *)/i,/^(?: *quadrant-2 *)/i,/^(?: *quadrant-3 *)/i,/^(?: *quadrant-4 *)/i,/^(?:classDef\b)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?::::)/i,/^(?:^\w+)/i,/^(?:\s*:\s*\[\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?:\s*\] *)/i,/^(?:\s*,\s*)/i,/^(?:(1)|(0(.\d+)?))/i,/^(?: *quadrantChart *)/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s)/i,/^(?:;)/i,/^(?:[!"#$%&'*+,-.`?\\_/])/i,/^(?:$)/i],conditions:{class_name:{rules:[28],inclusive:!1},point_y:{rules:[33],inclusive:!1},point_x:{rules:[32],inclusive:!1},point_start:{rules:[30,31],inclusive:!1},acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},title:{rules:[5],inclusive:!1},md_string:{rules:[22,23],inclusive:!1},string:{rules:[25,26],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,6,8,10,13,14,15,16,17,18,19,20,21,24,27,29,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51],inclusive:!0}}};return G}();Vt.lexer=We;function vt(){this.yy={}}return(0,e.eW)(vt,"Parser"),vt.prototype=Vt,Vt.Parser=vt,new vt}();Ct.parser=Ct;var qe=Ct,D=(0,e.xN)(),me=(tt=class{constructor(){this.classes=new Map,this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData()}getDefaultData(){return{titleText:"",quadrant1Text:"",quadrant2Text:"",quadrant3Text:"",quadrant4Text:"",xAxisLeftText:"",xAxisRightText:"",yAxisBottomText:"",yAxisTopText:"",points:[]}}getDefaultConfig(){var r,x,d,h,p,y,m,n,A,u,k,q,b,T,c,N,j,K;return{showXAxis:!0,showYAxis:!0,showTitle:!0,chartHeight:((r=e.vZ.quadrantChart)==null?void 0:r.chartWidth)||500,chartWidth:((x=e.vZ.quadrantChart)==null?void 0:x.chartHeight)||500,titlePadding:((d=e.vZ.quadrantChart)==null?void 0:d.titlePadding)||10,titleFontSize:((h=e.vZ.quadrantChart)==null?void 0:h.titleFontSize)||20,quadrantPadding:((p=e.vZ.quadrantChart)==null?void 0:p.quadrantPadding)||5,xAxisLabelPadding:((y=e.vZ.quadrantChart)==null?void 0:y.xAxisLabelPadding)||5,yAxisLabelPadding:((m=e.vZ.quadrantChart)==null?void 0:m.yAxisLabelPadding)||5,xAxisLabelFontSize:((n=e.vZ.quadrantChart)==null?void 0:n.xAxisLabelFontSize)||16,yAxisLabelFontSize:((A=e.vZ.quadrantChart)==null?void 0:A.yAxisLabelFontSize)||16,quadrantLabelFontSize:((u=e.vZ.quadrantChart)==null?void 0:u.quadrantLabelFontSize)||16,quadrantTextTopPadding:((k=e.vZ.quadrantChart)==null?void 0:k.quadrantTextTopPadding)||5,pointTextPadding:((q=e.vZ.quadrantChart)==null?void 0:q.pointTextPadding)||5,pointLabelFontSize:((b=e.vZ.quadrantChart)==null?void 0:b.pointLabelFontSize)||12,pointRadius:((T=e.vZ.quadrantChart)==null?void 0:T.pointRadius)||5,xAxisPosition:((c=e.vZ.quadrantChart)==null?void 0:c.xAxisPosition)||"top",yAxisPosition:((N=e.vZ.quadrantChart)==null?void 0:N.yAxisPosition)||"left",quadrantInternalBorderStrokeWidth:((j=e.vZ.quadrantChart)==null?void 0:j.quadrantInternalBorderStrokeWidth)||1,quadrantExternalBorderStrokeWidth:((K=e.vZ.quadrantChart)==null?void 0:K.quadrantExternalBorderStrokeWidth)||2}}getDefaultThemeConfig(){return{quadrant1Fill:D.quadrant1Fill,quadrant2Fill:D.quadrant2Fill,quadrant3Fill:D.quadrant3Fill,quadrant4Fill:D.quadrant4Fill,quadrant1TextFill:D.quadrant1TextFill,quadrant2TextFill:D.quadrant2TextFill,quadrant3TextFill:D.quadrant3TextFill,quadrant4TextFill:D.quadrant4TextFill,quadrantPointFill:D.quadrantPointFill,quadrantPointTextFill:D.quadrantPointTextFill,quadrantXAxisTextFill:D.quadrantXAxisTextFill,quadrantYAxisTextFill:D.quadrantYAxisTextFill,quadrantTitleFill:D.quadrantTitleFill,quadrantInternalBorderStrokeFill:D.quadrantInternalBorderStrokeFill,quadrantExternalBorderStrokeFill:D.quadrantExternalBorderStrokeFill}}clear(){this.config=this.getDefaultConfig(),this.themeConfig=this.getDefaultThemeConfig(),this.data=this.getDefaultData(),this.classes=new Map,e.cM.info("clear called")}setData(r){this.data=Q(Q({},this.data),r)}addPoints(r){this.data.points=[...r,...this.data.points]}addClass(r,x){this.classes.set(r,x)}setConfig(r){e.cM.trace("setConfig called with: ",r),this.config=Q(Q({},this.config),r)}setThemeConfig(r){e.cM.trace("setThemeConfig called with: ",r),this.themeConfig=Q(Q({},this.themeConfig),r)}calculateSpace(r,x,d,h){const p=this.config.xAxisLabelPadding*2+this.config.xAxisLabelFontSize,y={top:r==="top"&&x?p:0,bottom:r==="bottom"&&x?p:0},m=this.config.yAxisLabelPadding*2+this.config.yAxisLabelFontSize,n={left:this.config.yAxisPosition==="left"&&d?m:0,right:this.config.yAxisPosition==="right"&&d?m:0},A=this.config.titleFontSize+this.config.titlePadding*2,u={top:h?A:0},k=this.config.quadrantPadding+n.left,q=this.config.quadrantPadding+y.top+u.top,b=this.config.chartWidth-this.config.quadrantPadding*2-n.left-n.right,T=this.config.chartHeight-this.config.quadrantPadding*2-y.top-y.bottom-u.top,c=b/2,N=T/2;return{xAxisSpace:y,yAxisSpace:n,titleSpace:u,quadrantSpace:{quadrantLeft:k,quadrantTop:q,quadrantWidth:b,quadrantHalfWidth:c,quadrantHeight:T,quadrantHalfHeight:N}}}getAxisLabels(r,x,d,h){const{quadrantSpace:p,titleSpace:y}=h,{quadrantHalfHeight:m,quadrantHeight:n,quadrantLeft:A,quadrantHalfWidth:u,quadrantTop:k,quadrantWidth:q}=p,b=!!this.data.xAxisRightText,T=!!this.data.yAxisTopText,c=[];return this.data.xAxisLeftText&&x&&c.push({text:this.data.xAxisLeftText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+(b?u/2:0),y:r==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+k+n+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:0}),this.data.xAxisRightText&&x&&c.push({text:this.data.xAxisRightText,fill:this.themeConfig.quadrantXAxisTextFill,x:A+u+(b?u/2:0),y:r==="top"?this.config.xAxisLabelPadding+y.top:this.config.xAxisLabelPadding+k+n+this.config.quadrantPadding,fontSize:this.config.xAxisLabelFontSize,verticalPos:b?"center":"left",horizontalPos:"top",rotation:0}),this.data.yAxisBottomText&&d&&c.push({text:this.data.yAxisBottomText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+q+this.config.quadrantPadding,y:k+n-(T?m/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:-90}),this.data.yAxisTopText&&d&&c.push({text:this.data.yAxisTopText,fill:this.themeConfig.quadrantYAxisTextFill,x:this.config.yAxisPosition==="left"?this.config.yAxisLabelPadding:this.config.yAxisLabelPadding+A+q+this.config.quadrantPadding,y:k+m-(T?m/2:0),fontSize:this.config.yAxisLabelFontSize,verticalPos:T?"center":"left",horizontalPos:"top",rotation:-90}),c}getQuadrants(r){const{quadrantSpace:x}=r,{quadrantHalfHeight:d,quadrantLeft:h,quadrantHalfWidth:p,quadrantTop:y}=x,m=[{text:{text:this.data.quadrant1Text,fill:this.themeConfig.quadrant1TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:h+p,y,width:p,height:d,fill:this.themeConfig.quadrant1Fill},{text:{text:this.data.quadrant2Text,fill:this.themeConfig.quadrant2TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:h,y,width:p,height:d,fill:this.themeConfig.quadrant2Fill},{text:{text:this.data.quadrant3Text,fill:this.themeConfig.quadrant3TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:h,y:y+d,width:p,height:d,fill:this.themeConfig.quadrant3Fill},{text:{text:this.data.quadrant4Text,fill:this.themeConfig.quadrant4TextFill,x:0,y:0,fontSize:this.config.quadrantLabelFontSize,verticalPos:"center",horizontalPos:"middle",rotation:0},x:h+p,y:y+d,width:p,height:d,fill:this.themeConfig.quadrant4Fill}];for(const n of m)n.text.x=n.x+n.width/2,this.data.points.length===0?(n.text.y=n.y+n.height/2,n.text.horizontalPos="middle"):(n.text.y=n.y+this.config.quadrantTextTopPadding,n.text.horizontalPos="top");return m}getQuadrantPoints(r){const{quadrantSpace:x}=r,{quadrantHeight:d,quadrantLeft:h,quadrantTop:p,quadrantWidth:y}=x,m=(0,xt.BYU)().domain([0,1]).range([h,y+h]),n=(0,xt.BYU)().domain([0,1]).range([d+p,p]);return this.data.points.map(u=>{var b,T,c,N;const k=this.classes.get(u.className);return k&&(u=Q(Q({},k),u)),{x:m(u.x),y:n(u.y),fill:(b=u.color)!=null?b:this.themeConfig.quadrantPointFill,radius:(T=u.radius)!=null?T:this.config.pointRadius,text:{text:u.text,fill:this.themeConfig.quadrantPointTextFill,x:m(u.x),y:n(u.y)+this.config.pointTextPadding,verticalPos:"center",horizontalPos:"top",fontSize:this.config.pointLabelFontSize,rotation:0},strokeColor:(c=u.strokeColor)!=null?c:this.themeConfig.quadrantPointFill,strokeWidth:(N=u.strokeWidth)!=null?N:"0px"}})}getBorders(r){const x=this.config.quadrantExternalBorderStrokeWidth/2,{quadrantSpace:d}=r,{quadrantHalfHeight:h,quadrantHeight:p,quadrantLeft:y,quadrantHalfWidth:m,quadrantTop:n,quadrantWidth:A}=d;return[{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-x,y1:n,x2:y+A+x,y2:n},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y+A,y1:n+x,x2:y+A,y2:n+p-x},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y-x,y1:n+p,x2:y+A+x,y2:n+p},{strokeFill:this.themeConfig.quadrantExternalBorderStrokeFill,strokeWidth:this.config.quadrantExternalBorderStrokeWidth,x1:y,y1:n+x,x2:y,y2:n+p-x},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+m,y1:n+x,x2:y+m,y2:n+p-x},{strokeFill:this.themeConfig.quadrantInternalBorderStrokeFill,strokeWidth:this.config.quadrantInternalBorderStrokeWidth,x1:y+x,y1:n+h,x2:y+A-x,y2:n+h}]}getTitle(r){if(r)return{text:this.data.titleText,fill:this.themeConfig.quadrantTitleFill,fontSize:this.config.titleFontSize,horizontalPos:"top",verticalPos:"center",rotation:0,y:this.config.titlePadding,x:this.config.chartWidth/2}}build(){const r=this.config.showXAxis&&!!(this.data.xAxisLeftText||this.data.xAxisRightText),x=this.config.showYAxis&&!!(this.data.yAxisTopText||this.data.yAxisBottomText),d=this.config.showTitle&&!!this.data.titleText,h=this.data.points.length>0?"bottom":this.config.xAxisPosition,p=this.calculateSpace(h,r,x,d);return{points:this.getQuadrantPoints(p),quadrants:this.getQuadrants(p),axisLabels:this.getAxisLabels(h,r,x,p),borderLines:this.getBorders(p),title:this.getTitle(d)}}},(0,e.eW)(tt,"QuadrantBuilder"),tt),mt=(et=class extends Error{constructor(r,x,d){super(`value for ${r} ${x} is invalid, please use a valid ${d}`),this.name="InvalidStyleError"}},(0,e.eW)(et,"InvalidStyleError"),et);function Lt(t){return!/^#?([\dA-Fa-f]{6}|[\dA-Fa-f]{3})$/.test(t)}(0,e.eW)(Lt,"validateHexCode");function Nt(t){return!/^\d+$/.test(t)}(0,e.eW)(Nt,"validateNumber");function Ut(t){return!/^\d+px$/.test(t)}(0,e.eW)(Ut,"validateSizeInPixels");var _e=(0,e.nV)();function M(t){return(0,e.oO)(t.trim(),_e)}(0,e.eW)(M,"textSanitizer");var E=new me;function Ot(t){E.setData({quadrant1Text:M(t.text)})}(0,e.eW)(Ot,"setQuadrant1Text");function Mt(t){E.setData({quadrant2Text:M(t.text)})}(0,e.eW)(Mt,"setQuadrant2Text");function Qt(t){E.setData({quadrant3Text:M(t.text)})}(0,e.eW)(Qt,"setQuadrant3Text");function Ht(t){E.setData({quadrant4Text:M(t.text)})}(0,e.eW)(Ht,"setQuadrant4Text");function Xt(t){E.setData({xAxisLeftText:M(t.text)})}(0,e.eW)(Xt,"setXAxisLeftText");function Yt(t){E.setData({xAxisRightText:M(t.text)})}(0,e.eW)(Yt,"setXAxisRightText");function Zt(t){E.setData({yAxisTopText:M(t.text)})}(0,e.eW)(Zt,"setYAxisTopText");function jt(t){E.setData({yAxisBottomText:M(t.text)})}(0,e.eW)(jt,"setYAxisBottomText");function _t(t){const r={};for(const x of t){const[d,h]=x.trim().split(/\s*:\s*/);if(d==="radius"){if(Nt(h))throw new mt(d,h,"number");r.radius=parseInt(h)}else if(d==="color"){if(Lt(h))throw new mt(d,h,"hex code");r.color=h}else if(d==="stroke-color"){if(Lt(h))throw new mt(d,h,"hex code");r.strokeColor=h}else if(d==="stroke-width"){if(Ut(h))throw new mt(d,h,"number of pixels (eg. 10px)");r.strokeWidth=h}else throw new Error(`style named ${d} is not supported.`)}return r}(0,e.eW)(_t,"parseStyles");function Gt(t,r,x,d,h){const p=_t(h);E.addPoints([Q({x,y:d,text:M(t.text),className:r},p)])}(0,e.eW)(Gt,"addPoint");function Kt(t,r){E.addClass(t,_t(r))}(0,e.eW)(Kt,"addClass");function Jt(t){E.setConfig({chartWidth:t})}(0,e.eW)(Jt,"setWidth");function $t(t){E.setConfig({chartHeight:t})}(0,e.eW)($t,"setHeight");function te(){const t=(0,e.nV)(),{themeVariables:r,quadrantChart:x}=t;return x&&E.setConfig(x),E.setThemeConfig({quadrant1Fill:r.quadrant1Fill,quadrant2Fill:r.quadrant2Fill,quadrant3Fill:r.quadrant3Fill,quadrant4Fill:r.quadrant4Fill,quadrant1TextFill:r.quadrant1TextFill,quadrant2TextFill:r.quadrant2TextFill,quadrant3TextFill:r.quadrant3TextFill,quadrant4TextFill:r.quadrant4TextFill,quadrantPointFill:r.quadrantPointFill,quadrantPointTextFill:r.quadrantPointTextFill,quadrantXAxisTextFill:r.quadrantXAxisTextFill,quadrantYAxisTextFill:r.quadrantYAxisTextFill,quadrantExternalBorderStrokeFill:r.quadrantExternalBorderStrokeFill,quadrantInternalBorderStrokeFill:r.quadrantInternalBorderStrokeFill,quadrantTitleFill:r.quadrantTitleFill}),E.setData({titleText:(0,e.Kr)()}),E.build()}(0,e.eW)(te,"getQuadrantData");var Ae=(0,e.eW)(function(){E.clear(),(0,e.ZH)()},"clear"),Se={setWidth:Jt,setHeight:$t,setQuadrant1Text:Ot,setQuadrant2Text:Mt,setQuadrant3Text:Qt,setQuadrant4Text:Ht,setXAxisLeftText:Xt,setXAxisRightText:Yt,setYAxisTopText:Zt,setYAxisBottomText:jt,parseStyles:_t,addPoint:Gt,addClass:Kt,getQuadrantData:te,clear:Ae,setAccTitle:e.GN,getAccTitle:e.eu,setDiagramTitle:e.g2,getDiagramTitle:e.Kr,getAccDescription:e.Mx,setAccDescription:e.U$},ve=(0,e.eW)((t,r,x,d)=>{var pt,yt,bt,Tt,kt,w;function h(a){return a==="top"?"hanging":"middle"}(0,e.eW)(h,"getDominantBaseLine");function p(a){return a==="left"?"start":"middle"}(0,e.eW)(p,"getTextAnchor");function y(a){return`translate(${a.x}, ${a.y}) rotate(${a.rotation||0})`}(0,e.eW)(y,"getTransformation");const m=(0,e.nV)();e.cM.debug(`Rendering quadrant chart
`+t);const n=m.securityLevel;let A;n==="sandbox"&&(A=(0,xt.Ys)("#i"+r));const k=(n==="sandbox"?(0,xt.Ys)(A.nodes()[0].contentDocument.body):(0,xt.Ys)("body")).select(`[id="${r}"]`),q=k.append("g").attr("class","main"),b=(yt=(pt=m.quadrantChart)==null?void 0:pt.chartWidth)!=null?yt:500,T=(Tt=(bt=m.quadrantChart)==null?void 0:bt.chartHeight)!=null?Tt:500;(0,e.v2)(k,T,b,(w=(kt=m.quadrantChart)==null?void 0:kt.useMaxWidth)!=null?w:!0),k.attr("viewBox","0 0 "+b+" "+T),d.db.setHeight(T),d.db.setWidth(b);const c=d.db.getQuadrantData(),N=q.append("g").attr("class","quadrants"),j=q.append("g").attr("class","border"),K=q.append("g").attr("class","data-points"),At=q.append("g").attr("class","labels"),St=q.append("g").attr("class","title");c.title&&St.append("text").attr("x",0).attr("y",0).attr("fill",c.title.fill).attr("font-size",c.title.fontSize).attr("dominant-baseline",h(c.title.horizontalPos)).attr("text-anchor",p(c.title.verticalPos)).attr("transform",y(c.title)).text(c.title.text),c.borderLines&&j.selectAll("line").data(c.borderLines).enter().append("line").attr("x1",a=>a.x1).attr("y1",a=>a.y1).attr("x2",a=>a.x2).attr("y2",a=>a.y2).style("stroke",a=>a.strokeFill).style("stroke-width",a=>a.strokeWidth);const ft=N.selectAll("g.quadrant").data(c.quadrants).enter().append("g").attr("class","quadrant");ft.append("rect").attr("x",a=>a.x).attr("y",a=>a.y).attr("width",a=>a.width).attr("height",a=>a.height).attr("fill",a=>a.fill),ft.append("text").attr("x",0).attr("y",0).attr("fill",a=>a.text.fill).attr("font-size",a=>a.text.fontSize).attr("dominant-baseline",a=>h(a.text.horizontalPos)).attr("text-anchor",a=>p(a.text.verticalPos)).attr("transform",a=>y(a.text)).text(a=>a.text.text),At.selectAll("g.label").data(c.axisLabels).enter().append("g").attr("class","label").append("text").attr("x",0).attr("y",0).text(a=>a.text).attr("fill",a=>a.fill).attr("font-size",a=>a.fontSize).attr("dominant-baseline",a=>h(a.horizontalPos)).attr("text-anchor",a=>p(a.verticalPos)).attr("transform",a=>y(a));const gt=K.selectAll("g.data-point").data(c.points).enter().append("g").attr("class","data-point");gt.append("circle").attr("cx",a=>a.x).attr("cy",a=>a.y).attr("r",a=>a.radius).attr("fill",a=>a.fill).attr("stroke",a=>a.strokeColor).attr("stroke-width",a=>a.strokeWidth),gt.append("text").attr("x",0).attr("y",0).text(a=>a.text.text).attr("fill",a=>a.text.fill).attr("font-size",a=>a.text.fontSize).attr("dominant-baseline",a=>h(a.text.horizontalPos)).attr("text-anchor",a=>p(a.text.verticalPos)).attr("transform",a=>y(a.text))},"draw"),Pe={draw:ve},Fe={parser:qe,db:Se,renderer:Pe,styles:(0,e.eW)(()=>"","styles")}}}]);
}());