"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[862],{90862:function(q,L,f){f.d(L,{diagram:function(){return Z}});var m=f(43271),tt=f(3027),et=f(8031),nt=f(23064),at=f(78933),it=f(61150),rt=f(52387),st=f(50538),dt=f(57956),ot=f(70919),k=f(50854),t=f(29134),S=f(69471),A=f(47840),N=f(56561),R=(0,t.eW)(e=>e.append("circle").attr("class","start-state").attr("r",(0,t.nV)().state.sizeUnit).attr("cx",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit).attr("cy",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit),"drawStartState"),U=(0,t.eW)(e=>e.append("line").style("stroke","grey").style("stroke-dasharray","3").attr("x1",(0,t.nV)().state.textHeight).attr("class","divider").attr("x2",(0,t.nV)().state.textHeight*2).attr("y1",0).attr("y2",0),"drawDivider"),I=(0,t.eW)((e,a)=>{const d=e.append("text").attr("x",2*(0,t.nV)().state.padding).attr("y",(0,t.nV)().state.textHeight+2*(0,t.nV)().state.padding).attr("font-size",(0,t.nV)().state.fontSize).attr("class","state-title").text(a.id),c=d.node().getBBox();return e.insert("rect",":first-child").attr("x",(0,t.nV)().state.padding).attr("y",(0,t.nV)().state.padding).attr("width",c.width+2*(0,t.nV)().state.padding).attr("height",c.height+2*(0,t.nV)().state.padding).attr("rx",(0,t.nV)().state.radius),d},"drawSimpleState"),C=(0,t.eW)((e,a)=>{const d=(0,t.eW)(function(h,E,B){const w=h.append("tspan").attr("x",2*(0,t.nV)().state.padding).text(E);B||w.attr("dy",(0,t.nV)().state.textHeight)},"addTspan"),r=e.append("text").attr("x",2*(0,t.nV)().state.padding).attr("y",(0,t.nV)().state.textHeight+1.3*(0,t.nV)().state.padding).attr("font-size",(0,t.nV)().state.fontSize).attr("class","state-title").text(a.descriptions[0]).node().getBBox(),l=r.height,x=e.append("text").attr("x",(0,t.nV)().state.padding).attr("y",l+(0,t.nV)().state.padding*.4+(0,t.nV)().state.dividerMargin+(0,t.nV)().state.textHeight).attr("class","state-description");let n=!0,s=!0;a.descriptions.forEach(function(h){n||(d(x,h,s),s=!1),n=!1});const _=e.append("line").attr("x1",(0,t.nV)().state.padding).attr("y1",(0,t.nV)().state.padding+l+(0,t.nV)().state.dividerMargin/2).attr("y2",(0,t.nV)().state.padding+l+(0,t.nV)().state.dividerMargin/2).attr("class","descr-divider"),p=x.node().getBBox(),o=Math.max(p.width,r.width);return _.attr("x2",o+3*(0,t.nV)().state.padding),e.insert("rect",":first-child").attr("x",(0,t.nV)().state.padding).attr("y",(0,t.nV)().state.padding).attr("width",o+2*(0,t.nV)().state.padding).attr("height",p.height+l+2*(0,t.nV)().state.padding).attr("rx",(0,t.nV)().state.radius),e},"drawDescrState"),H=(0,t.eW)((e,a,d)=>{const c=(0,t.nV)().state.padding,r=2*(0,t.nV)().state.padding,l=e.node().getBBox(),x=l.width,n=l.x,s=e.append("text").attr("x",0).attr("y",(0,t.nV)().state.titleShift).attr("font-size",(0,t.nV)().state.fontSize).attr("class","state-title").text(a.id),p=s.node().getBBox().width+r;let o=Math.max(p,x);o===x&&(o=o+r);let h;const E=e.node().getBBox();a.doc,h=n-c,p>x&&(h=(x-o)/2+c),Math.abs(n-E.x)<c&&p>x&&(h=n-(p-x)/2);const B=1-(0,t.nV)().state.textHeight;return e.insert("rect",":first-child").attr("x",h).attr("y",B).attr("class",d?"alt-composit":"composit").attr("width",o).attr("height",E.height+(0,t.nV)().state.textHeight+(0,t.nV)().state.titleShift+1).attr("rx","0"),s.attr("x",h+c),p<=x&&s.attr("x",n+(o-r)/2-p/2+c),e.insert("rect",":first-child").attr("x",h).attr("y",(0,t.nV)().state.titleShift-(0,t.nV)().state.textHeight-(0,t.nV)().state.padding).attr("width",o).attr("height",(0,t.nV)().state.textHeight*3).attr("rx",(0,t.nV)().state.radius),e.insert("rect",":first-child").attr("x",h).attr("y",(0,t.nV)().state.titleShift-(0,t.nV)().state.textHeight-(0,t.nV)().state.padding).attr("width",o).attr("height",E.height+3+2*(0,t.nV)().state.textHeight).attr("rx",(0,t.nV)().state.radius),e},"addTitleAndBox"),z=(0,t.eW)(e=>(e.append("circle").attr("class","end-state-outer").attr("r",(0,t.nV)().state.sizeUnit+(0,t.nV)().state.miniPadding).attr("cx",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit+(0,t.nV)().state.miniPadding).attr("cy",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit+(0,t.nV)().state.miniPadding),e.append("circle").attr("class","end-state-inner").attr("r",(0,t.nV)().state.sizeUnit).attr("cx",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit+2).attr("cy",(0,t.nV)().state.padding+(0,t.nV)().state.sizeUnit+2)),"drawEndState"),K=(0,t.eW)((e,a)=>{let d=(0,t.nV)().state.forkWidth,c=(0,t.nV)().state.forkHeight;if(a.parentId){let r=d;d=c,c=r}return e.append("rect").style("stroke","black").style("fill","black").attr("width",d).attr("height",c).attr("x",(0,t.nV)().state.padding).attr("y",(0,t.nV)().state.padding)},"drawForkJoinState"),j=(0,t.eW)((e,a,d,c)=>{let r=0;const l=c.append("text");l.style("text-anchor","start"),l.attr("class","noteText");let x=e.replace(/\r\n/g,"<br/>");x=x.replace(/\n/g,"<br/>");const n=x.split(t.SY.lineBreakRegex);let s=1.25*(0,t.nV)().state.noteMargin;for(const _ of n){const p=_.trim();if(p.length>0){const o=l.append("tspan");if(o.text(p),s===0){const h=o.node().getBBox();s+=h.height}r+=s,o.attr("x",a+(0,t.nV)().state.noteMargin),o.attr("y",d+r+1.25*(0,t.nV)().state.noteMargin)}}return{textWidth:l.node().getBBox().width,textHeight:r}},"_drawLongText"),Y=(0,t.eW)((e,a)=>{a.attr("class","state-note");const d=a.append("rect").attr("x",0).attr("y",(0,t.nV)().state.padding),c=a.append("g"),{textWidth:r,textHeight:l}=j(e,0,0,c);return d.attr("height",l+2*(0,t.nV)().state.noteMargin),d.attr("width",r+(0,t.nV)().state.noteMargin*2),d},"drawNote"),O=(0,t.eW)(function(e,a){const d=a.id,c={id:d,label:a.id,width:0,height:0},r=e.append("g").attr("id",d).attr("class","stateGroup");a.type==="start"&&R(r),a.type==="end"&&z(r),(a.type==="fork"||a.type==="join")&&K(r,a),a.type==="note"&&Y(a.note.text,r),a.type==="divider"&&U(r),a.type==="default"&&a.descriptions.length===0&&I(r,a),a.type==="default"&&a.descriptions.length>0&&C(r,a);const l=r.node().getBBox();return c.width=l.width+2*(0,t.nV)().state.padding,c.height=l.height+2*(0,t.nV)().state.padding,c},"drawState"),T=0,G=(0,t.eW)(function(e,a,d){const c=(0,t.eW)(function(s){switch(s){case m.oI.relationType.AGGREGATION:return"aggregation";case m.oI.relationType.EXTENSION:return"extension";case m.oI.relationType.COMPOSITION:return"composition";case m.oI.relationType.DEPENDENCY:return"dependency"}},"getRelationType");a.points=a.points.filter(s=>!Number.isNaN(s.y));const r=a.points,l=(0,S.jvg)().x(function(s){return s.x}).y(function(s){return s.y}).curve(S.$0Z),x=e.append("path").attr("d",l(r)).attr("id","edge"+T).attr("class","transition");let n="";if((0,t.nV)().state.arrowMarkerAbsolute&&(n=(0,t.Gr)(!0)),x.attr("marker-end","url("+n+"#"+c(m.oI.relationType.DEPENDENCY)+"End)"),d.title!==void 0){const s=e.append("g").attr("class","stateLabel"),{x:_,y:p}=k.w8.calcLabelPosition(a.points),o=t.SY.getRows(d.title);let h=0;const E=[];let B=0,w=0;for(let i=0;i<=o.length;i++){const u=s.append("text").attr("text-anchor","middle").text(o[i]).attr("x",_).attr("y",p+h),g=u.node().getBBox();B=Math.max(B,g.width),w=Math.min(w,g.x),t.cM.info(g.x,_,p+h),h===0&&(h=u.node().getBBox().height,t.cM.info("Title height",h,p)),E.push(u)}let W=h*o.length;if(o.length>1){const i=(o.length-1)*h*.5;E.forEach((u,g)=>u.attr("y",p+g*h-i)),W=h*o.length}const v=s.node().getBBox();s.insert("rect",":first-child").attr("class","box").attr("x",_-B/2-(0,t.nV)().state.padding/2).attr("y",p-W/2-(0,t.nV)().state.padding/2-3.5).attr("width",B+(0,t.nV)().state.padding).attr("height",W+(0,t.nV)().state.padding),t.cM.info(v)}T++},"drawEdge"),V,b={},F=(0,t.eW)(function(){},"setConf"),J=(0,t.eW)(function(e){e.append("defs").append("marker").attr("id","dependencyEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"insertMarkers"),X=(0,t.eW)(function(e,a,d,c){V=(0,t.nV)().state;const r=(0,t.nV)().securityLevel;let l;r==="sandbox"&&(l=(0,S.Ys)("#i"+a));const x=r==="sandbox"?(0,S.Ys)(l.nodes()[0].contentDocument.body):(0,S.Ys)("body"),n=r==="sandbox"?l.nodes()[0].contentDocument:document;t.cM.debug("Rendering diagram "+e);const s=x.select(`[id='${a}']`);J(s);const _=c.db.getRootDoc();D(_,s,void 0,!1,x,n,c);const p=V.padding,o=s.node().getBBox(),h=o.width+p*2,E=o.height+p*2,B=h*1.75;(0,t.v2)(s,E,B,V.useMaxWidth),s.attr("viewBox",`${o.x-V.padding}  ${o.y-V.padding} `+h+" "+E)},"draw"),$=(0,t.eW)(e=>e?e.length*V.fontSizeFactor:1,"getLabelWidth"),D=(0,t.eW)((e,a,d,c,r,l,x)=>{const n=new N.k({compound:!0,multigraph:!0});let s,_=!0;for(s=0;s<e.length;s++)if(e[s].stmt==="relation"){_=!1;break}d?n.setGraph({rankdir:"LR",multigraph:!0,compound:!0,ranker:"tight-tree",ranksep:_?1:V.edgeLengthFactor,nodeSep:_?1:50,isMultiGraph:!0}):n.setGraph({rankdir:"TB",multigraph:!0,compound:!0,ranksep:_?1:V.edgeLengthFactor,nodeSep:_?1:50,ranker:"tight-tree",isMultiGraph:!0}),n.setDefaultEdgeLabel(function(){return{}});const p=x.db.getStates(),o=x.db.getRelations(),h=Object.keys(p);let E=!0;for(const i of h){const u=p[i];d&&(u.parentId=d);let g;if(u.doc){let y=a.append("g").attr("id",u.id).attr("class","stateGroup");if(g=D(u.doc,y,u.id,!c,r,l,x),E){y=H(y,u,c);let M=y.node().getBBox();g.width=M.width,g.height=M.height+V.padding/2,b[u.id]={y:V.compositTitleSize}}else{let M=y.node().getBBox();g.width=M.width,g.height=M.height}}else g=O(a,u,n);if(u.note){const y={descriptions:[],id:u.id+"-note",note:u.note,type:"note"},M=O(a,y,n);u.note.position==="left of"?(n.setNode(g.id+"-note",M),n.setNode(g.id,g)):(n.setNode(g.id,g),n.setNode(g.id+"-note",M)),n.setParent(g.id,g.id+"-group"),n.setParent(g.id+"-note",g.id+"-group")}else n.setNode(g.id,g)}t.cM.debug("Count=",n.nodeCount(),n);let B=0;o.forEach(function(i){B++,t.cM.debug("Setting edge",i),n.setEdge(i.id1,i.id2,{relation:i,width:$(i.title),height:V.labelHeight*t.SY.getRows(i.title).length,labelpos:"c"},"id"+B)}),(0,A.bK)(n),t.cM.debug("Graph after layout",n.nodes());const w=a.node();n.nodes().forEach(function(i){i!==void 0&&n.node(i)!==void 0?(t.cM.warn("Node "+i+": "+JSON.stringify(n.node(i))),r.select("#"+w.id+" #"+i).attr("transform","translate("+(n.node(i).x-n.node(i).width/2)+","+(n.node(i).y+(b[i]?b[i].y:0)-n.node(i).height/2)+" )"),r.select("#"+w.id+" #"+i).attr("data-x-shift",n.node(i).x-n.node(i).width/2),l.querySelectorAll("#"+w.id+" #"+i+" .divider").forEach(g=>{const y=g.parentElement;let M=0,P=0;y&&(y.parentElement&&(M=y.parentElement.getBBox().width),P=parseInt(y.getAttribute("data-x-shift"),10),Number.isNaN(P)&&(P=0)),g.setAttribute("x1",0-P+8),g.setAttribute("x2",M-P-8)})):t.cM.debug("No Node "+i+": "+JSON.stringify(n.node(i)))});let W=w.getBBox();n.edges().forEach(function(i){i!==void 0&&n.edge(i)!==void 0&&(t.cM.debug("Edge "+i.v+" -> "+i.w+": "+JSON.stringify(n.edge(i))),G(a,n.edge(i),n.edge(i).relation))}),W=w.getBBox();const v={id:d||"root",label:d||"root",width:0,height:0};return v.width=W.width+2*V.padding,v.height=W.height+2*V.padding,t.cM.debug("Doc rendered",v,n),v},"renderDoc"),Q={setConf:F,draw:X},Z={parser:m.J8,get db(){return new m.oI(1)},renderer:Q,styles:m.Ee,init:(0,t.eW)(e=>{e.state||(e.state={}),e.state.arrowMarkerAbsolute=e.arrowMarkerAbsolute},"init")}}}]);
