!(function(){"use strict";var Q=(At,yt,V)=>new Promise((D,ft)=>{var P=it=>{try{at(V.next(it))}catch(xt){ft(xt)}},r=it=>{try{at(V.throw(it))}catch(xt){ft(xt)}},at=it=>it.done?D(it.value):Promise.resolve(it.value).then(P,r);at((V=V.apply(At,yt)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[1617],{85163:function(At,yt,V){V.d(yt,{AD:function(){return Yt},AE:function(){return it},Mu:function(){return P},O:function(){return r},kc:function(){return Ot},rB:function(){return xt},yU:function(){return at}});var D=V(29134),ft=V(33565),P=(0,D.eW)((j,L)=>{const O=j.append("rect");if(O.attr("x",L.x),O.attr("y",L.y),O.attr("fill",L.fill),O.attr("stroke",L.stroke),O.attr("width",L.width),O.attr("height",L.height),L.name&&O.attr("name",L.name),L.rx&&O.attr("rx",L.rx),L.ry&&O.attr("ry",L.ry),L.attrs!==void 0)for(const z in L.attrs)O.attr(z,L.attrs[z]);return L.class&&O.attr("class",L.class),O},"drawRect"),r=(0,D.eW)((j,L)=>{const O={x:L.startx,y:L.starty,width:L.stopx-L.startx,height:L.stopy-L.starty,fill:L.fill,stroke:L.stroke,class:"rect"};P(j,O).lower()},"drawBackgroundRect"),at=(0,D.eW)((j,L)=>{const O=L.text.replace(D.Vw," "),z=j.append("text");z.attr("x",L.x),z.attr("y",L.y),z.attr("class","legend"),z.style("text-anchor",L.anchor),L.class&&z.attr("class",L.class);const nt=z.append("tspan");return nt.attr("x",L.x+L.textMargin*2),nt.text(O),z},"drawText"),it=(0,D.eW)((j,L,O,z)=>{const nt=j.append("image");nt.attr("x",L),nt.attr("y",O);const ht=(0,ft.N)(z);nt.attr("xlink:href",ht)},"drawImage"),xt=(0,D.eW)((j,L,O,z)=>{const nt=j.append("use");nt.attr("x",L),nt.attr("y",O);const ht=(0,ft.N)(z);nt.attr("xlink:href",`#${ht}`)},"drawEmbeddedImage"),Ot=(0,D.eW)(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),Yt=(0,D.eW)(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj")},78605:function(At,yt,V){var P;V.d(yt,{A:function(){return ft}});var D=V(29134),ft=(P=class{constructor(at){this.init=at,this.records=this.init()}reset(){this.records=this.init()}},(0,D.eW)(P,"ImperativeState"),P)},21617:function(At,yt,V){var It;V.d(yt,{diagram:function(){return qe}});var D=V(85163),ft=V(78605),P=V(50854),r=V(29134),at=V(69471),it=V(33565),xt=function(){var e=(0,r.eW)(function(bt,w,v,I){for(v=v||{},I=bt.length;I--;v[bt[I]]=w);return v},"o"),t=[1,2],c=[1,3],s=[1,4],i=[2,4],n=[1,9],o=[1,11],d=[1,13],p=[1,14],a=[1,16],f=[1,17],b=[1,18],g=[1,24],T=[1,25],m=[1,26],_=[1,27],A=[1,28],B=[1,29],M=[1,30],F=[1,31],Y=[1,32],K=[1,33],U=[1,34],$=[1,35],ot=[1,36],G=[1,37],X=[1,38],H=[1,39],R=[1,41],tt=[1,42],Z=[1,43],et=[1,44],ct=[1,45],N=[1,46],E=[1,4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,48,49,50,52,53,54,59,60,61,62,70],k=[4,5,16,50,52,53],st=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],pt=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,49,50,52,53,54,59,60,61,62,70],S=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,48,50,52,53,54,59,60,61,62,70],ce=[4,5,13,14,16,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,47,50,52,53,54,59,60,61,62,70],gt=[68,69,70],Tt=[1,122],qt={trace:(0,r.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SPACE:4,NEWLINE:5,SD:6,document:7,line:8,statement:9,box_section:10,box_line:11,participant_statement:12,create:13,box:14,restOfLine:15,end:16,signal:17,autonumber:18,NUM:19,off:20,activate:21,actor:22,deactivate:23,note_statement:24,links_statement:25,link_statement:26,properties_statement:27,details_statement:28,title:29,legacy_title:30,acc_title:31,acc_title_value:32,acc_descr:33,acc_descr_value:34,acc_descr_multiline_value:35,loop:36,rect:37,opt:38,alt:39,else_sections:40,par:41,par_sections:42,par_over:43,critical:44,option_sections:45,break:46,option:47,and:48,else:49,participant:50,AS:51,participant_actor:52,destroy:53,note:54,placement:55,text2:56,over:57,actor_pair:58,links:59,link:60,properties:61,details:62,spaceList:63,",":64,left_of:65,right_of:66,signaltype:67,"+":68,"-":69,ACTOR:70,SOLID_OPEN_ARROW:71,DOTTED_OPEN_ARROW:72,SOLID_ARROW:73,BIDIRECTIONAL_SOLID_ARROW:74,DOTTED_ARROW:75,BIDIRECTIONAL_DOTTED_ARROW:76,SOLID_CROSS:77,DOTTED_CROSS:78,SOLID_POINT:79,DOTTED_POINT:80,TXT:81,$accept:0,$end:1},terminals_:{2:"error",4:"SPACE",5:"NEWLINE",6:"SD",13:"create",14:"box",15:"restOfLine",16:"end",18:"autonumber",19:"NUM",20:"off",21:"activate",23:"deactivate",29:"title",30:"legacy_title",31:"acc_title",32:"acc_title_value",33:"acc_descr",34:"acc_descr_value",35:"acc_descr_multiline_value",36:"loop",37:"rect",38:"opt",39:"alt",41:"par",43:"par_over",44:"critical",46:"break",47:"option",48:"and",49:"else",50:"participant",51:"AS",52:"participant_actor",53:"destroy",54:"note",57:"over",59:"links",60:"link",61:"properties",62:"details",64:",",65:"left_of",66:"right_of",68:"+",69:"-",70:"ACTOR",71:"SOLID_OPEN_ARROW",72:"DOTTED_OPEN_ARROW",73:"SOLID_ARROW",74:"BIDIRECTIONAL_SOLID_ARROW",75:"DOTTED_ARROW",76:"BIDIRECTIONAL_DOTTED_ARROW",77:"SOLID_CROSS",78:"DOTTED_CROSS",79:"SOLID_POINT",80:"DOTTED_POINT",81:"TXT"},productions_:[0,[3,2],[3,2],[3,2],[7,0],[7,2],[8,2],[8,1],[8,1],[10,0],[10,2],[11,2],[11,1],[11,1],[9,1],[9,2],[9,4],[9,2],[9,4],[9,3],[9,3],[9,2],[9,3],[9,3],[9,2],[9,2],[9,2],[9,2],[9,2],[9,1],[9,1],[9,2],[9,2],[9,1],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[9,4],[45,1],[45,4],[42,1],[42,4],[40,1],[40,4],[12,5],[12,3],[12,5],[12,3],[12,3],[24,4],[24,4],[25,3],[26,3],[27,3],[28,3],[63,2],[63,1],[58,3],[58,1],[55,1],[55,1],[17,5],[17,5],[17,4],[22,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[67,1],[56,1]],performAction:(0,r.eW)(function(w,v,I,y,W,h,Pt){var u=h.length-1;switch(W){case 3:return y.apply(h[u]),h[u];break;case 4:case 9:this.$=[];break;case 5:case 10:h[u-1].push(h[u]),this.$=h[u-1];break;case 6:case 7:case 11:case 12:this.$=h[u];break;case 8:case 13:this.$=[];break;case 15:h[u].type="createParticipant",this.$=h[u];break;case 16:h[u-1].unshift({type:"boxStart",boxData:y.parseBoxData(h[u-2])}),h[u-1].push({type:"boxEnd",boxText:h[u-2]}),this.$=h[u-1];break;case 18:this.$={type:"sequenceIndex",sequenceIndex:Number(h[u-2]),sequenceIndexStep:Number(h[u-1]),sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 19:this.$={type:"sequenceIndex",sequenceIndex:Number(h[u-1]),sequenceIndexStep:1,sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 20:this.$={type:"sequenceIndex",sequenceVisible:!1,signalType:y.LINETYPE.AUTONUMBER};break;case 21:this.$={type:"sequenceIndex",sequenceVisible:!0,signalType:y.LINETYPE.AUTONUMBER};break;case 22:this.$={type:"activeStart",signalType:y.LINETYPE.ACTIVE_START,actor:h[u-1].actor};break;case 23:this.$={type:"activeEnd",signalType:y.LINETYPE.ACTIVE_END,actor:h[u-1].actor};break;case 29:y.setDiagramTitle(h[u].substring(6)),this.$=h[u].substring(6);break;case 30:y.setDiagramTitle(h[u].substring(7)),this.$=h[u].substring(7);break;case 31:this.$=h[u].trim(),y.setAccTitle(this.$);break;case 32:case 33:this.$=h[u].trim(),y.setAccDescription(this.$);break;case 34:h[u-1].unshift({type:"loopStart",loopText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.LOOP_START}),h[u-1].push({type:"loopEnd",loopText:h[u-2],signalType:y.LINETYPE.LOOP_END}),this.$=h[u-1];break;case 35:h[u-1].unshift({type:"rectStart",color:y.parseMessage(h[u-2]),signalType:y.LINETYPE.RECT_START}),h[u-1].push({type:"rectEnd",color:y.parseMessage(h[u-2]),signalType:y.LINETYPE.RECT_END}),this.$=h[u-1];break;case 36:h[u-1].unshift({type:"optStart",optText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.OPT_START}),h[u-1].push({type:"optEnd",optText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.OPT_END}),this.$=h[u-1];break;case 37:h[u-1].unshift({type:"altStart",altText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.ALT_START}),h[u-1].push({type:"altEnd",signalType:y.LINETYPE.ALT_END}),this.$=h[u-1];break;case 38:h[u-1].unshift({type:"parStart",parText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.PAR_START}),h[u-1].push({type:"parEnd",signalType:y.LINETYPE.PAR_END}),this.$=h[u-1];break;case 39:h[u-1].unshift({type:"parStart",parText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.PAR_OVER_START}),h[u-1].push({type:"parEnd",signalType:y.LINETYPE.PAR_END}),this.$=h[u-1];break;case 40:h[u-1].unshift({type:"criticalStart",criticalText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.CRITICAL_START}),h[u-1].push({type:"criticalEnd",signalType:y.LINETYPE.CRITICAL_END}),this.$=h[u-1];break;case 41:h[u-1].unshift({type:"breakStart",breakText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.BREAK_START}),h[u-1].push({type:"breakEnd",optText:y.parseMessage(h[u-2]),signalType:y.LINETYPE.BREAK_END}),this.$=h[u-1];break;case 43:this.$=h[u-3].concat([{type:"option",optionText:y.parseMessage(h[u-1]),signalType:y.LINETYPE.CRITICAL_OPTION},h[u]]);break;case 45:this.$=h[u-3].concat([{type:"and",parText:y.parseMessage(h[u-1]),signalType:y.LINETYPE.PAR_AND},h[u]]);break;case 47:this.$=h[u-3].concat([{type:"else",altText:y.parseMessage(h[u-1]),signalType:y.LINETYPE.ALT_ELSE},h[u]]);break;case 48:h[u-3].draw="participant",h[u-3].type="addParticipant",h[u-3].description=y.parseMessage(h[u-1]),this.$=h[u-3];break;case 49:h[u-1].draw="participant",h[u-1].type="addParticipant",this.$=h[u-1];break;case 50:h[u-3].draw="actor",h[u-3].type="addParticipant",h[u-3].description=y.parseMessage(h[u-1]),this.$=h[u-3];break;case 51:h[u-1].draw="actor",h[u-1].type="addParticipant",this.$=h[u-1];break;case 52:h[u-1].type="destroyParticipant",this.$=h[u-1];break;case 53:this.$=[h[u-1],{type:"addNote",placement:h[u-2],actor:h[u-1].actor,text:h[u]}];break;case 54:h[u-2]=[].concat(h[u-1],h[u-1]).slice(0,2),h[u-2][0]=h[u-2][0].actor,h[u-2][1]=h[u-2][1].actor,this.$=[h[u-1],{type:"addNote",placement:y.PLACEMENT.OVER,actor:h[u-2].slice(0,2),text:h[u]}];break;case 55:this.$=[h[u-1],{type:"addLinks",actor:h[u-1].actor,text:h[u]}];break;case 56:this.$=[h[u-1],{type:"addALink",actor:h[u-1].actor,text:h[u]}];break;case 57:this.$=[h[u-1],{type:"addProperties",actor:h[u-1].actor,text:h[u]}];break;case 58:this.$=[h[u-1],{type:"addDetails",actor:h[u-1].actor,text:h[u]}];break;case 61:this.$=[h[u-2],h[u]];break;case 62:this.$=h[u];break;case 63:this.$=y.PLACEMENT.LEFTOF;break;case 64:this.$=y.PLACEMENT.RIGHTOF;break;case 65:this.$=[h[u-4],h[u-1],{type:"addMessage",from:h[u-4].actor,to:h[u-1].actor,signalType:h[u-3],msg:h[u],activate:!0},{type:"activeStart",signalType:y.LINETYPE.ACTIVE_START,actor:h[u-1].actor}];break;case 66:this.$=[h[u-4],h[u-1],{type:"addMessage",from:h[u-4].actor,to:h[u-1].actor,signalType:h[u-3],msg:h[u]},{type:"activeEnd",signalType:y.LINETYPE.ACTIVE_END,actor:h[u-4].actor}];break;case 67:this.$=[h[u-3],h[u-1],{type:"addMessage",from:h[u-3].actor,to:h[u-1].actor,signalType:h[u-2],msg:h[u]}];break;case 68:this.$={type:"addParticipant",actor:h[u]};break;case 69:this.$=y.LINETYPE.SOLID_OPEN;break;case 70:this.$=y.LINETYPE.DOTTED_OPEN;break;case 71:this.$=y.LINETYPE.SOLID;break;case 72:this.$=y.LINETYPE.BIDIRECTIONAL_SOLID;break;case 73:this.$=y.LINETYPE.DOTTED;break;case 74:this.$=y.LINETYPE.BIDIRECTIONAL_DOTTED;break;case 75:this.$=y.LINETYPE.SOLID_CROSS;break;case 76:this.$=y.LINETYPE.DOTTED_CROSS;break;case 77:this.$=y.LINETYPE.SOLID_POINT;break;case 78:this.$=y.LINETYPE.DOTTED_POINT;break;case 79:this.$=y.parseMessage(h[u].trim().substring(1));break}},"anonymous"),table:[{3:1,4:t,5:c,6:s},{1:[3]},{3:5,4:t,5:c,6:s},{3:6,4:t,5:c,6:s},e([1,4,5,13,14,18,21,23,29,30,31,33,35,36,37,38,39,41,43,44,46,50,52,53,54,59,60,61,62,70],i,{7:7}),{1:[2,1]},{1:[2,2]},{1:[2,3],4:n,5:o,8:8,9:10,12:12,13:d,14:p,17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},e(E,[2,5]),{9:47,12:12,13:d,14:p,17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},e(E,[2,7]),e(E,[2,8]),e(E,[2,14]),{12:48,50:G,52:X,53:H},{15:[1,49]},{5:[1,50]},{5:[1,53],19:[1,51],20:[1,52]},{22:54,70:N},{22:55,70:N},{5:[1,56]},{5:[1,57]},{5:[1,58]},{5:[1,59]},{5:[1,60]},e(E,[2,29]),e(E,[2,30]),{32:[1,61]},{34:[1,62]},e(E,[2,33]),{15:[1,63]},{15:[1,64]},{15:[1,65]},{15:[1,66]},{15:[1,67]},{15:[1,68]},{15:[1,69]},{15:[1,70]},{22:71,70:N},{22:72,70:N},{22:73,70:N},{67:74,71:[1,75],72:[1,76],73:[1,77],74:[1,78],75:[1,79],76:[1,80],77:[1,81],78:[1,82],79:[1,83],80:[1,84]},{55:85,57:[1,86],65:[1,87],66:[1,88]},{22:89,70:N},{22:90,70:N},{22:91,70:N},{22:92,70:N},e([5,51,64,71,72,73,74,75,76,77,78,79,80,81],[2,68]),e(E,[2,6]),e(E,[2,15]),e(k,[2,9],{10:93}),e(E,[2,17]),{5:[1,95],19:[1,94]},{5:[1,96]},e(E,[2,21]),{5:[1,97]},{5:[1,98]},e(E,[2,24]),e(E,[2,25]),e(E,[2,26]),e(E,[2,27]),e(E,[2,28]),e(E,[2,31]),e(E,[2,32]),e(st,i,{7:99}),e(st,i,{7:100}),e(st,i,{7:101}),e(pt,i,{40:102,7:103}),e(S,i,{42:104,7:105}),e(S,i,{7:105,42:106}),e(ce,i,{45:107,7:108}),e(st,i,{7:109}),{5:[1,111],51:[1,110]},{5:[1,113],51:[1,112]},{5:[1,114]},{22:117,68:[1,115],69:[1,116],70:N},e(gt,[2,69]),e(gt,[2,70]),e(gt,[2,71]),e(gt,[2,72]),e(gt,[2,73]),e(gt,[2,74]),e(gt,[2,75]),e(gt,[2,76]),e(gt,[2,77]),e(gt,[2,78]),{22:118,70:N},{22:120,58:119,70:N},{70:[2,63]},{70:[2,64]},{56:121,81:Tt},{56:123,81:Tt},{56:124,81:Tt},{56:125,81:Tt},{4:[1,128],5:[1,130],11:127,12:129,16:[1,126],50:G,52:X,53:H},{5:[1,131]},e(E,[2,19]),e(E,[2,20]),e(E,[2,22]),e(E,[2,23]),{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[1,132],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[1,133],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[1,134],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{16:[1,135]},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[2,46],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,49:[1,136],50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{16:[1,137]},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[2,44],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,48:[1,138],50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{16:[1,139]},{16:[1,140]},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[2,42],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,47:[1,141],50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{4:n,5:o,8:8,9:10,12:12,13:d,14:p,16:[1,142],17:15,18:a,21:f,22:40,23:b,24:19,25:20,26:21,27:22,28:23,29:g,30:T,31:m,33:_,35:A,36:B,37:M,38:F,39:Y,41:K,43:U,44:$,46:ot,50:G,52:X,53:H,54:R,59:tt,60:Z,61:et,62:ct,70:N},{15:[1,143]},e(E,[2,49]),{15:[1,144]},e(E,[2,51]),e(E,[2,52]),{22:145,70:N},{22:146,70:N},{56:147,81:Tt},{56:148,81:Tt},{56:149,81:Tt},{64:[1,150],81:[2,62]},{5:[2,55]},{5:[2,79]},{5:[2,56]},{5:[2,57]},{5:[2,58]},e(E,[2,16]),e(k,[2,10]),{12:151,50:G,52:X,53:H},e(k,[2,12]),e(k,[2,13]),e(E,[2,18]),e(E,[2,34]),e(E,[2,35]),e(E,[2,36]),e(E,[2,37]),{15:[1,152]},e(E,[2,38]),{15:[1,153]},e(E,[2,39]),e(E,[2,40]),{15:[1,154]},e(E,[2,41]),{5:[1,155]},{5:[1,156]},{56:157,81:Tt},{56:158,81:Tt},{5:[2,67]},{5:[2,53]},{5:[2,54]},{22:159,70:N},e(k,[2,11]),e(pt,i,{7:103,40:160}),e(S,i,{7:105,42:161}),e(ce,i,{7:108,45:162}),e(E,[2,48]),e(E,[2,50]),{5:[2,65]},{5:[2,66]},{81:[2,61]},{16:[2,47]},{16:[2,45]},{16:[2,43]}],defaultActions:{5:[2,1],6:[2,2],87:[2,63],88:[2,64],121:[2,55],122:[2,79],123:[2,56],124:[2,57],125:[2,58],147:[2,67],148:[2,53],149:[2,54],157:[2,65],158:[2,66],159:[2,61],160:[2,47],161:[2,45],162:[2,43]},parseError:(0,r.eW)(function(w,v){if(v.recoverable)this.trace(w);else{var I=new Error(w);throw I.hash=v,I}},"parseError"),parse:(0,r.eW)(function(w){var v=this,I=[0],y=[],W=[null],h=[],Pt=this.table,u="",Wt=0,le=0,he=0,He=2,de=1,Ke=h.slice.call(arguments,1),q=Object.create(this.lexer),wt={yy:{}};for(var zt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,zt)&&(wt.yy[zt]=this.yy[zt]);q.setInput(w,wt.yy),wt.yy.lexer=q,wt.yy.parser=this,typeof q.yylloc=="undefined"&&(q.yylloc={});var Ht=q.yylloc;h.push(Ht);var Ue=q.options&&q.options.ranges;typeof wt.yy.parseError=="function"?this.parseError=wt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ge(rt){I.length=I.length-2*rt,W.length=W.length-rt,h.length=h.length-rt}(0,r.eW)(Ge,"popStack");function pe(){var rt;return rt=y.pop()||q.lex()||de,typeof rt!="number"&&(rt instanceof Array&&(y=rt,rt=y.pop()),rt=v.symbols_[rt]||rt),rt}(0,r.eW)(pe,"lex");for(var J,Kt,_t,lt,Xe,Ut,Lt={},Rt,Et,ue,Dt;;){if(_t=I[I.length-1],this.defaultActions[_t]?lt=this.defaultActions[_t]:((J===null||typeof J=="undefined")&&(J=pe()),lt=Pt[_t]&&Pt[_t][J]),typeof lt=="undefined"||!lt.length||!lt[0]){var Gt="";Dt=[];for(Rt in Pt[_t])this.terminals_[Rt]&&Rt>He&&Dt.push("'"+this.terminals_[Rt]+"'");q.showPosition?Gt="Parse error on line "+(Wt+1)+`:
`+q.showPosition()+`
Expecting `+Dt.join(", ")+", got '"+(this.terminals_[J]||J)+"'":Gt="Parse error on line "+(Wt+1)+": Unexpected "+(J==de?"end of input":"'"+(this.terminals_[J]||J)+"'"),this.parseError(Gt,{text:q.match,token:this.terminals_[J]||J,line:q.yylineno,loc:Ht,expected:Dt})}if(lt[0]instanceof Array&&lt.length>1)throw new Error("Parse Error: multiple actions possible at state: "+_t+", token: "+J);switch(lt[0]){case 1:I.push(J),W.push(q.yytext),h.push(q.yylloc),I.push(lt[1]),J=null,Kt?(J=Kt,Kt=null):(le=q.yyleng,u=q.yytext,Wt=q.yylineno,Ht=q.yylloc,he>0&&he--);break;case 2:if(Et=this.productions_[lt[1]][1],Lt.$=W[W.length-Et],Lt._$={first_line:h[h.length-(Et||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(Et||1)].first_column,last_column:h[h.length-1].last_column},Ue&&(Lt._$.range=[h[h.length-(Et||1)].range[0],h[h.length-1].range[1]]),Ut=this.performAction.apply(Lt,[u,le,Wt,wt.yy,lt[1],W,h].concat(Ke)),typeof Ut!="undefined")return Ut;Et&&(I=I.slice(0,-1*Et*2),W=W.slice(0,-1*Et),h=h.slice(0,-1*Et)),I.push(this.productions_[lt[1]][0]),W.push(Lt.$),h.push(Lt._$),ue=Pt[I[I.length-2]][I[I.length-1]],I.push(ue);break;case 3:return!0}}return!0},"parse")},ze=function(){var bt={EOF:1,parseError:(0,r.eW)(function(v,I){if(this.yy.parser)this.yy.parser.parseError(v,I);else throw new Error(v)},"parseError"),setInput:(0,r.eW)(function(w,v){return this.yy=v||this.yy||{},this._input=w,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,r.eW)(function(){var w=this._input[0];this.yytext+=w,this.yyleng++,this.offset++,this.match+=w,this.matched+=w;var v=w.match(/(?:\r\n?|\n).*/g);return v?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),w},"input"),unput:(0,r.eW)(function(w){var v=w.length,I=w.split(/(?:\r\n?|\n)/g);this._input=w+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-v),this.offset-=v;var y=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),I.length-1&&(this.yylineno-=I.length-1);var W=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:I?(I.length===y.length?this.yylloc.first_column:0)+y[y.length-I.length].length-I[0].length:this.yylloc.first_column-v},this.options.ranges&&(this.yylloc.range=[W[0],W[0]+this.yyleng-v]),this.yyleng=this.yytext.length,this},"unput"),more:(0,r.eW)(function(){return this._more=!0,this},"more"),reject:(0,r.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,r.eW)(function(w){this.unput(this.match.slice(w))},"less"),pastInput:(0,r.eW)(function(){var w=this.matched.substr(0,this.matched.length-this.match.length);return(w.length>20?"...":"")+w.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,r.eW)(function(){var w=this.match;return w.length<20&&(w+=this._input.substr(0,20-w.length)),(w.substr(0,20)+(w.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,r.eW)(function(){var w=this.pastInput(),v=new Array(w.length+1).join("-");return w+this.upcomingInput()+`
`+v+"^"},"showPosition"),test_match:(0,r.eW)(function(w,v){var I,y,W;if(this.options.backtrack_lexer&&(W={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(W.yylloc.range=this.yylloc.range.slice(0))),y=w[0].match(/(?:\r\n?|\n).*/g),y&&(this.yylineno+=y.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:y?y[y.length-1].length-y[y.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+w[0].length},this.yytext+=w[0],this.match+=w[0],this.matches=w,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(w[0].length),this.matched+=w[0],I=this.performAction.call(this,this.yy,this,v,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),I)return I;if(this._backtrack){for(var h in W)this[h]=W[h];return!1}return!1},"test_match"),next:(0,r.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var w,v,I,y;this._more||(this.yytext="",this.match="");for(var W=this._currentRules(),h=0;h<W.length;h++)if(I=this._input.match(this.rules[W[h]]),I&&(!v||I[0].length>v[0].length)){if(v=I,y=h,this.options.backtrack_lexer){if(w=this.test_match(I,W[h]),w!==!1)return w;if(this._backtrack){v=!1;continue}else return!1}else if(!this.options.flex)break}return v?(w=this.test_match(v,W[y]),w!==!1?w:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,r.eW)(function(){var v=this.next();return v||this.lex()},"lex"),begin:(0,r.eW)(function(v){this.conditionStack.push(v)},"begin"),popState:(0,r.eW)(function(){var v=this.conditionStack.length-1;return v>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,r.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,r.eW)(function(v){return v=this.conditionStack.length-1-Math.abs(v||0),v>=0?this.conditionStack[v]:"INITIAL"},"topState"),pushState:(0,r.eW)(function(v){this.begin(v)},"pushState"),stateStackSize:(0,r.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,r.eW)(function(v,I,y,W){var h=W;switch(y){case 0:return 5;case 1:break;case 2:break;case 3:break;case 4:break;case 5:break;case 6:return 19;case 7:return this.begin("LINE"),14;break;case 8:return this.begin("ID"),50;break;case 9:return this.begin("ID"),52;break;case 10:return 13;case 11:return this.begin("ID"),53;break;case 12:return I.yytext=I.yytext.trim(),this.begin("ALIAS"),70;break;case 13:return this.popState(),this.popState(),this.begin("LINE"),51;break;case 14:return this.popState(),this.popState(),5;break;case 15:return this.begin("LINE"),36;break;case 16:return this.begin("LINE"),37;break;case 17:return this.begin("LINE"),38;break;case 18:return this.begin("LINE"),39;break;case 19:return this.begin("LINE"),49;break;case 20:return this.begin("LINE"),41;break;case 21:return this.begin("LINE"),43;break;case 22:return this.begin("LINE"),48;break;case 23:return this.begin("LINE"),44;break;case 24:return this.begin("LINE"),47;break;case 25:return this.begin("LINE"),46;break;case 26:return this.popState(),15;break;case 27:return 16;case 28:return 65;case 29:return 66;case 30:return 59;case 31:return 60;case 32:return 61;case 33:return 62;case 34:return 57;case 35:return 54;case 36:return this.begin("ID"),21;break;case 37:return this.begin("ID"),23;break;case 38:return 29;case 39:return 30;case 40:return this.begin("acc_title"),31;break;case 41:return this.popState(),"acc_title_value";break;case 42:return this.begin("acc_descr"),33;break;case 43:return this.popState(),"acc_descr_value";break;case 44:this.begin("acc_descr_multiline");break;case 45:this.popState();break;case 46:return"acc_descr_multiline_value";case 47:return 6;case 48:return 18;case 49:return 20;case 50:return 64;case 51:return 5;case 52:return I.yytext=I.yytext.trim(),70;break;case 53:return 73;case 54:return 74;case 55:return 75;case 56:return 76;case 57:return 71;case 58:return 72;case 59:return 77;case 60:return 78;case 61:return 79;case 62:return 80;case 63:return 81;case 64:return 81;case 65:return 68;case 66:return 69;case 67:return 5;case 68:return"INVALID"}},"anonymous"),rules:[/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:((?!\n)\s)+)/i,/^(?:#[^\n]*)/i,/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[0-9]+(?=[ \n]+))/i,/^(?:box\b)/i,/^(?:participant\b)/i,/^(?:actor\b)/i,/^(?:create\b)/i,/^(?:destroy\b)/i,/^(?:[^\<->\->:\n,;]+?([\-]*[^\<->\->:\n,;]+?)*?(?=((?!\n)\s)+as(?!\n)\s|[#\n;]|$))/i,/^(?:as\b)/i,/^(?:(?:))/i,/^(?:loop\b)/i,/^(?:rect\b)/i,/^(?:opt\b)/i,/^(?:alt\b)/i,/^(?:else\b)/i,/^(?:par\b)/i,/^(?:par_over\b)/i,/^(?:and\b)/i,/^(?:critical\b)/i,/^(?:option\b)/i,/^(?:break\b)/i,/^(?:(?:[:]?(?:no)?wrap)?[^#\n;]*)/i,/^(?:end\b)/i,/^(?:left of\b)/i,/^(?:right of\b)/i,/^(?:links\b)/i,/^(?:link\b)/i,/^(?:properties\b)/i,/^(?:details\b)/i,/^(?:over\b)/i,/^(?:note\b)/i,/^(?:activate\b)/i,/^(?:deactivate\b)/i,/^(?:title\s[^#\n;]+)/i,/^(?:title:\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:sequenceDiagram\b)/i,/^(?:autonumber\b)/i,/^(?:off\b)/i,/^(?:,)/i,/^(?:;)/i,/^(?:[^\+\<->\->:\n,;]+((?!(-x|--x|-\)|--\)))[\-]*[^\+\<->\->:\n,;]+)*)/i,/^(?:->>)/i,/^(?:<<->>)/i,/^(?:-->>)/i,/^(?:<<-->>)/i,/^(?:->)/i,/^(?:-->)/i,/^(?:-[x])/i,/^(?:--[x])/i,/^(?:-[\)])/i,/^(?:--[\)])/i,/^(?::(?:(?:no)?wrap)?[^#\n;]*)/i,/^(?::)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[45,46],inclusive:!1},acc_descr:{rules:[43],inclusive:!1},acc_title:{rules:[41],inclusive:!1},ID:{rules:[2,3,12],inclusive:!1},ALIAS:{rules:[2,3,13,14],inclusive:!1},LINE:{rules:[2,3,26],inclusive:!1},INITIAL:{rules:[0,1,3,4,5,6,7,8,9,10,11,15,16,17,18,19,20,21,22,23,24,25,27,28,29,30,31,32,33,34,35,36,37,38,39,40,42,44,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68],inclusive:!0}}};return bt}();qt.lexer=ze;function Mt(){this.yy={}}return(0,r.eW)(Mt,"Parser"),Mt.prototype=qt,qt.Parser=Mt,new Mt}();xt.parser=xt;var Ot=xt,Yt={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25,AUTONUMBER:26,CRITICAL_START:27,CRITICAL_OPTION:28,CRITICAL_END:29,BREAK_START:30,BREAK_END:31,PAR_OVER_START:32,BIDIRECTIONAL_SOLID:33,BIDIRECTIONAL_DOTTED:34},j={FILLED:0,OPEN:1},L={LEFTOF:0,RIGHTOF:1,OVER:2},O=(It=class{constructor(){this.state=new ft.A(()=>({prevActor:void 0,actors:new Map,createdActors:new Map,destroyedActors:new Map,boxes:[],messages:[],notes:[],sequenceNumbersEnabled:!1,wrapEnabled:void 0,currentBox:void 0,lastCreated:void 0,lastDestroyed:void 0})),this.setAccTitle=r.GN,this.setAccDescription=r.U$,this.setDiagramTitle=r.g2,this.getAccTitle=r.eu,this.getAccDescription=r.Mx,this.getDiagramTitle=r.Kr,this.apply=this.apply.bind(this),this.parseBoxData=this.parseBoxData.bind(this),this.parseMessage=this.parseMessage.bind(this),this.clear(),this.setWrap((0,r.nV)().wrap),this.LINETYPE=Yt,this.ARROWTYPE=j,this.PLACEMENT=L}addBox(t){var c;this.state.records.boxes.push({name:t.text,wrap:(c=t.wrap)!=null?c:this.autoWrap(),fill:t.color,actorKeys:[]}),this.state.records.currentBox=this.state.records.boxes.slice(-1)[0]}addActor(t,c,s,i){var d;let n=this.state.records.currentBox;const o=this.state.records.actors.get(t);if(o){if(this.state.records.currentBox&&o.box&&this.state.records.currentBox!==o.box)throw new Error(`A same participant should only be defined in one Box: ${o.name} can't be in '${o.box.name}' and in '${this.state.records.currentBox.name}' at the same time.`);if(n=o.box?o.box:this.state.records.currentBox,o.box=n,o&&c===o.name&&s==null)return}if((s==null?void 0:s.text)==null&&(s={text:c,type:i}),(i==null||s.text==null)&&(s={text:c,type:i}),this.state.records.actors.set(t,{box:n,name:c,description:s.text,wrap:(d=s.wrap)!=null?d:this.autoWrap(),prevActor:this.state.records.prevActor,links:{},properties:{},actorCnt:null,rectData:null,type:i!=null?i:"participant"}),this.state.records.prevActor){const p=this.state.records.actors.get(this.state.records.prevActor);p&&(p.nextActor=t)}this.state.records.currentBox&&this.state.records.currentBox.actorKeys.push(t),this.state.records.prevActor=t}activationCount(t){let c,s=0;if(!t)return 0;for(c=0;c<this.state.records.messages.length;c++)this.state.records.messages[c].type===this.LINETYPE.ACTIVE_START&&this.state.records.messages[c].from===t&&s++,this.state.records.messages[c].type===this.LINETYPE.ACTIVE_END&&this.state.records.messages[c].from===t&&s--;return s}addMessage(t,c,s,i){var n;this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:s.text,wrap:(n=s.wrap)!=null?n:this.autoWrap(),answer:i})}addSignal(t,c,s,i,n=!1){var o,d;if(i===this.LINETYPE.ACTIVE_END&&this.activationCount(t!=null?t:"")<1){const a=new Error("Trying to inactivate an inactive participant ("+t+")");throw a.hash={text:"->>-",token:"->>-",line:"1",loc:{first_line:1,last_line:1,first_column:1,last_column:1},expected:["'ACTIVE_PARTICIPANT'"]},a}return this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:t,to:c,message:(o=s==null?void 0:s.text)!=null?o:"",wrap:(d=s==null?void 0:s.wrap)!=null?d:this.autoWrap(),type:i,activate:n}),!0}hasAtLeastOneBox(){return this.state.records.boxes.length>0}hasAtLeastOneBoxWithTitle(){return this.state.records.boxes.some(t=>t.name)}getMessages(){return this.state.records.messages}getBoxes(){return this.state.records.boxes}getActors(){return this.state.records.actors}getCreatedActors(){return this.state.records.createdActors}getDestroyedActors(){return this.state.records.destroyedActors}getActor(t){return this.state.records.actors.get(t)}getActorKeys(){return[...this.state.records.actors.keys()]}enableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!0}disableSequenceNumbers(){this.state.records.sequenceNumbersEnabled=!1}showSequenceNumbers(){return this.state.records.sequenceNumbersEnabled}setWrap(t){this.state.records.wrapEnabled=t}extractWrap(t){if(t===void 0)return{};t=t.trim();const c=/^:?wrap:/.exec(t)!==null?!0:/^:?nowrap:/.exec(t)!==null?!1:void 0;return{cleanedText:(c===void 0?t:t.replace(/^:?(?:no)?wrap:/,"")).trim(),wrap:c}}autoWrap(){var t,c;return this.state.records.wrapEnabled!==void 0?this.state.records.wrapEnabled:(c=(t=(0,r.nV)().sequence)==null?void 0:t.wrap)!=null?c:!1}clear(){this.state.reset(),(0,r.ZH)()}parseMessage(t){const c=t.trim(),{wrap:s,cleanedText:i}=this.extractWrap(c),n={text:i,wrap:s};return r.cM.debug(`parseMessage: ${JSON.stringify(n)}`),n}parseBoxData(t){const c=/^((?:rgba?|hsla?)\s*\(.*\)|\w*)(.*)$/.exec(t);let s=c!=null&&c[1]?c[1].trim():"transparent",i=c!=null&&c[2]?c[2].trim():void 0;if(window!=null&&window.CSS)window.CSS.supports("color",s)||(s="transparent",i=t.trim());else{const d=new Option().style;d.color=s,d.color!==s&&(s="transparent",i=t.trim())}const{wrap:n,cleanedText:o}=this.extractWrap(i);return{text:o?(0,r.oO)(o,(0,r.nV)()):void 0,color:s,wrap:n}}addNote(t,c,s){var o,d;const i={actor:t,placement:c,message:s.text,wrap:(o=s.wrap)!=null?o:this.autoWrap()},n=[].concat(t,t);this.state.records.notes.push(i),this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:n[0],to:n[1],message:s.text,wrap:(d=s.wrap)!=null?d:this.autoWrap(),type:this.LINETYPE.NOTE,placement:c})}addLinks(t,c){const s=this.getActor(t);try{let i=(0,r.oO)(c.text,(0,r.nV)());i=i.replace(/&equals;/g,"="),i=i.replace(/&amp;/g,"&");const n=JSON.parse(i);this.insertLinks(s,n)}catch(i){r.cM.error("error while parsing actor link text",i)}}addALink(t,c){const s=this.getActor(t);try{const i={};let n=(0,r.oO)(c.text,(0,r.nV)());const o=n.indexOf("@");n=n.replace(/&equals;/g,"="),n=n.replace(/&amp;/g,"&");const d=n.slice(0,o-1).trim(),p=n.slice(o+1).trim();i[d]=p,this.insertLinks(s,i)}catch(i){r.cM.error("error while parsing actor link text",i)}}insertLinks(t,c){if(t.links==null)t.links=c;else for(const s in c)t.links[s]=c[s]}addProperties(t,c){const s=this.getActor(t);try{const i=(0,r.oO)(c.text,(0,r.nV)()),n=JSON.parse(i);this.insertProperties(s,n)}catch(i){r.cM.error("error while parsing actor properties text",i)}}insertProperties(t,c){if(t.properties==null)t.properties=c;else for(const s in c)t.properties[s]=c[s]}boxEnd(){this.state.records.currentBox=void 0}addDetails(t,c){const s=this.getActor(t),i=document.getElementById(c.text);try{const n=i.innerHTML,o=JSON.parse(n);o.properties&&this.insertProperties(s,o.properties),o.links&&this.insertLinks(s,o.links)}catch(n){r.cM.error("error while parsing actor details text",n)}}getActorProperty(t,c){if((t==null?void 0:t.properties)!==void 0)return t.properties[c]}apply(t){if(Array.isArray(t))t.forEach(c=>{this.apply(c)});else switch(t.type){case"sequenceIndex":this.state.records.messages.push({id:this.state.records.messages.length.toString(),from:void 0,to:void 0,message:{start:t.sequenceIndex,step:t.sequenceIndexStep,visible:t.sequenceVisible},wrap:!1,type:t.signalType});break;case"addParticipant":this.addActor(t.actor,t.actor,t.description,t.draw);break;case"createParticipant":if(this.state.records.actors.has(t.actor))throw new Error("It is not possible to have actors with the same id, even if one is destroyed before the next is created. Use 'AS' aliases to simulate the behavior");this.state.records.lastCreated=t.actor,this.addActor(t.actor,t.actor,t.description,t.draw),this.state.records.createdActors.set(t.actor,this.state.records.messages.length);break;case"destroyParticipant":this.state.records.lastDestroyed=t.actor,this.state.records.destroyedActors.set(t.actor,this.state.records.messages.length);break;case"activeStart":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"activeEnd":this.addSignal(t.actor,void 0,void 0,t.signalType);break;case"addNote":this.addNote(t.actor,t.placement,t.text);break;case"addLinks":this.addLinks(t.actor,t.text);break;case"addALink":this.addALink(t.actor,t.text);break;case"addProperties":this.addProperties(t.actor,t.text);break;case"addDetails":this.addDetails(t.actor,t.text);break;case"addMessage":if(this.state.records.lastCreated){if(t.to!==this.state.records.lastCreated)throw new Error("The created participant "+this.state.records.lastCreated.name+" does not have an associated creating message after its declaration. Please check the sequence diagram.");this.state.records.lastCreated=void 0}else if(this.state.records.lastDestroyed){if(t.to!==this.state.records.lastDestroyed&&t.from!==this.state.records.lastDestroyed)throw new Error("The destroyed participant "+this.state.records.lastDestroyed.name+" does not have an associated destroying message after its declaration. Please check the sequence diagram.");this.state.records.lastDestroyed=void 0}this.addSignal(t.from,t.to,t.msg,t.signalType,t.activate);break;case"boxStart":this.addBox(t.boxData);break;case"boxEnd":this.boxEnd();break;case"loopStart":this.addSignal(void 0,void 0,t.loopText,t.signalType);break;case"loopEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"rectStart":this.addSignal(void 0,void 0,t.color,t.signalType);break;case"rectEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"optStart":this.addSignal(void 0,void 0,t.optText,t.signalType);break;case"optEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"altStart":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"else":this.addSignal(void 0,void 0,t.altText,t.signalType);break;case"altEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"setAccTitle":(0,r.GN)(t.text);break;case"parStart":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"and":this.addSignal(void 0,void 0,t.parText,t.signalType);break;case"parEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"criticalStart":this.addSignal(void 0,void 0,t.criticalText,t.signalType);break;case"option":this.addSignal(void 0,void 0,t.optionText,t.signalType);break;case"criticalEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break;case"breakStart":this.addSignal(void 0,void 0,t.breakText,t.signalType);break;case"breakEnd":this.addSignal(void 0,void 0,void 0,t.signalType);break}}getConfig(){return(0,r.nV)().sequence}},(0,r.eW)(It,"SequenceDB"),It),z=(0,r.eW)(e=>`.actor {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }

  text.actor > tspan {
    fill: ${e.actorTextColor};
    stroke: none;
  }

  .actor-line {
    stroke: ${e.actorLineColor};
  }

  .messageLine0 {
    stroke-width: 1.5;
    stroke-dasharray: none;
    stroke: ${e.signalColor};
  }

  .messageLine1 {
    stroke-width: 1.5;
    stroke-dasharray: 2, 2;
    stroke: ${e.signalColor};
  }

  #arrowhead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .sequenceNumber {
    fill: ${e.sequenceNumberColor};
  }

  #sequencenumber {
    fill: ${e.signalColor};
  }

  #crosshead path {
    fill: ${e.signalColor};
    stroke: ${e.signalColor};
  }

  .messageText {
    fill: ${e.signalTextColor};
    stroke: none;
  }

  .labelBox {
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBkgColor};
  }

  .labelText, .labelText > tspan {
    fill: ${e.labelTextColor};
    stroke: none;
  }

  .loopText, .loopText > tspan {
    fill: ${e.loopTextColor};
    stroke: none;
  }

  .loopLine {
    stroke-width: 2px;
    stroke-dasharray: 2, 2;
    stroke: ${e.labelBoxBorderColor};
    fill: ${e.labelBoxBorderColor};
  }

  .note {
    //stroke: #decc93;
    stroke: ${e.noteBorderColor};
    fill: ${e.noteBkgColor};
  }

  .noteText, .noteText > tspan {
    fill: ${e.noteTextColor};
    stroke: none;
  }

  .activation0 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation1 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .activation2 {
    fill: ${e.activationBkgColor};
    stroke: ${e.activationBorderColor};
  }

  .actorPopupMenu {
    position: absolute;
  }

  .actorPopupMenuPanel {
    position: absolute;
    fill: ${e.actorBkg};
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    filter: drop-shadow(3px 5px 2px rgb(0 0 0 / 0.4));
}
  .actor-man line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
  }
  .actor-man circle, line {
    stroke: ${e.actorBorder};
    fill: ${e.actorBkg};
    stroke-width: 2px;
  }
`,"getStyles"),nt=z,ht=18*2,Xt="actor-top",Jt="actor-bottom",ge="actor-box",Zt="actor-man",Ct=(0,r.eW)(function(e,t){return(0,D.Mu)(e,t)},"drawRect"),fe=(0,r.eW)(function(e,t,c,s,i){if(t.links===void 0||t.links===null||Object.keys(t.links).length===0)return{height:0,width:0};const n=t.links,o=t.actorCnt,d=t.rectData;var p="none";i&&(p="block !important");const a=e.append("g");a.attr("id","actor"+o+"_popup"),a.attr("class","actorPopupMenu"),a.attr("display",p);var f="";d.class!==void 0&&(f=" "+d.class);let b=d.width>c?d.width:c;const g=a.append("rect");if(g.attr("class","actorPopupMenuPanel"+f),g.attr("x",d.x),g.attr("y",d.height),g.attr("fill",d.fill),g.attr("stroke",d.stroke),g.attr("width",b),g.attr("height",d.height),g.attr("rx",d.rx),g.attr("ry",d.ry),n!=null){var T=20;for(let A in n){var m=a.append("a"),_=(0,it.N)(n[A]);m.attr("xlink:href",_),m.attr("target","_blank"),Me(s)(A,m,d.x+10,d.height+T,b,20,{class:"actor"},s),T+=30}}return g.attr("height",T),{height:d.height+T,width:b}},"drawPopup"),xe=(0,r.eW)(function(e){return"var pu = document.getElementById('"+e+"'); if (pu != null) { pu.style.display = pu.style.display == 'block' ? 'none' : 'block'; }"},"popupMenuToggle"),St=(0,r.eW)(function(e,t,c=null){return Q(this,null,function*(){let s=e.append("foreignObject");const i=yield(0,r.uT)(t.text,(0,r.iE)()),o=s.append("xhtml:div").attr("style","width: fit-content;").attr("xmlns","http://www.w3.org/1999/xhtml").html(i).node().getBoundingClientRect();if(s.attr("height",Math.round(o.height)).attr("width",Math.round(o.width)),t.class==="noteText"){const d=e.node().firstChild;d.setAttribute("height",o.height+2*t.textMargin);const p=d.getBBox();s.attr("x",Math.round(p.x+p.width/2-o.width/2)).attr("y",Math.round(p.y+p.height/2-o.height/2))}else if(c){let{startx:d,stopx:p,starty:a}=c;if(d>p){const f=d;d=p,p=f}s.attr("x",Math.round(d+Math.abs(d-p)/2-o.width/2)),t.class==="loopText"?s.attr("y",Math.round(a)):s.attr("y",Math.round(a-o.height))}return[s]})},"drawKatex"),vt=(0,r.eW)(function(e,t){let c=0,s=0;const i=t.text.split(r.SY.lineBreakRegex),[n,o]=(0,P.VG)(t.fontSize);let d=[],p=0,a=(0,r.eW)(()=>t.y,"yfunc");if(t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0)switch(t.valign){case"top":case"start":a=(0,r.eW)(()=>Math.round(t.y+t.textMargin),"yfunc");break;case"middle":case"center":a=(0,r.eW)(()=>Math.round(t.y+(c+s+t.textMargin)/2),"yfunc");break;case"bottom":case"end":a=(0,r.eW)(()=>Math.round(t.y+(c+s+2*t.textMargin)-t.textMargin),"yfunc");break}if(t.anchor!==void 0&&t.textMargin!==void 0&&t.width!==void 0)switch(t.anchor){case"left":case"start":t.x=Math.round(t.x+t.textMargin),t.anchor="start",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"middle":case"center":t.x=Math.round(t.x+t.width/2),t.anchor="middle",t.dominantBaseline="middle",t.alignmentBaseline="middle";break;case"right":case"end":t.x=Math.round(t.x+t.width-t.textMargin),t.anchor="end",t.dominantBaseline="middle",t.alignmentBaseline="middle";break}for(let[f,b]of i.entries()){t.textMargin!==void 0&&t.textMargin===0&&n!==void 0&&(p=f*n);const g=e.append("text");g.attr("x",t.x),g.attr("y",a()),t.anchor!==void 0&&g.attr("text-anchor",t.anchor).attr("dominant-baseline",t.dominantBaseline).attr("alignment-baseline",t.alignmentBaseline),t.fontFamily!==void 0&&g.style("font-family",t.fontFamily),o!==void 0&&g.style("font-size",o),t.fontWeight!==void 0&&g.style("font-weight",t.fontWeight),t.fill!==void 0&&g.attr("fill",t.fill),t.class!==void 0&&g.attr("class",t.class),t.dy!==void 0?g.attr("dy",t.dy):p!==0&&g.attr("dy",p);const T=b||P.$m;if(t.tspan){const m=g.append("tspan");m.attr("x",t.x),t.fill!==void 0&&m.attr("fill",t.fill),m.text(T)}else g.text(T);t.valign!==void 0&&t.textMargin!==void 0&&t.textMargin>0&&(s+=(g._groups||g)[0][0].getBBox().height,c=s),d.push(g)}return d},"drawText"),Qt=(0,r.eW)(function(e,t){function c(i,n,o,d,p){return i+","+n+" "+(i+o)+","+n+" "+(i+o)+","+(n+d-p)+" "+(i+o-p*1.2)+","+(n+d)+" "+i+","+(n+d)}(0,r.eW)(c,"genPoints");const s=e.append("polygon");return s.attr("points",c(t.x,t.y,t.width,t.height,7)),s.attr("class","labelBox"),t.y=t.y+t.height/2,vt(e,t),s},"drawLabel"),ut=-1,jt=(0,r.eW)((e,t,c,s)=>{e.select&&c.forEach(i=>{const n=t.get(i),o=e.select("#actor"+n.actorCnt);!s.mirrorActors&&n.stopy?o.attr("y2",n.stopy+n.height/2):s.mirrorActors&&o.attr("y2",n.stopy)})},"fixLifeLineHeights"),Te=(0,r.eW)(function(e,t,c,s){var T,m;const i=s?t.stopy:t.starty,n=t.x+t.width/2,o=i+t.height,d=e.append("g").lower();var p=d;s||(ut++,Object.keys(t.links||{}).length&&!c.forceMenus&&p.attr("onclick",xe(`actor${ut}_popup`)).attr("cursor","pointer"),p.append("line").attr("id","actor"+ut).attr("x1",n).attr("y1",o).attr("x2",n).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),p=d.append("g"),t.actorCnt=ut,t.links!=null&&p.attr("id","root-"+ut));const a=(0,D.kc)();var f="actor";(T=t.properties)!=null&&T.class?f=t.properties.class:a.fill="#eaeaea",s?f+=` ${Jt}`:f+=` ${Xt}`,a.x=t.x,a.y=i,a.width=t.width,a.height=t.height,a.class=f,a.rx=3,a.ry=3,a.name=t.name;const b=Ct(p,a);if(t.rectData=a,(m=t.properties)!=null&&m.icon){const _=t.properties.icon.trim();_.charAt(0)==="@"?(0,D.rB)(p,a.x+a.width-20,a.y+10,_.substr(1)):(0,D.AE)(p,a.x+a.width-20,a.y+10,_)}Bt(c,(0,r.l0)(t.description))(t.description,p,a.x,a.y,a.width,a.height,{class:`actor ${ge}`},c);let g=t.height;if(b.node){const _=b.node().getBBox();t.height=_.height,g=_.height}return g},"drawActorTypeParticipant"),Ee=(0,r.eW)(function(e,t,c,s){const i=s?t.stopy:t.starty,n=t.x+t.width/2,o=i+80,d=e.append("g").lower();s||(ut++,d.append("line").attr("id","actor"+ut).attr("x1",n).attr("y1",o).attr("x2",n).attr("y2",2e3).attr("class","actor-line 200").attr("stroke-width","0.5px").attr("stroke","#999").attr("name",t.name),t.actorCnt=ut);const p=e.append("g");let a=Zt;s?a+=` ${Jt}`:a+=` ${Xt}`,p.attr("class",a),p.attr("name",t.name);const f=(0,D.kc)();f.x=t.x,f.y=i,f.fill="#eaeaea",f.width=t.width,f.height=t.height,f.class="actor",f.rx=3,f.ry=3,p.append("line").attr("id","actor-man-torso"+ut).attr("x1",n).attr("y1",i+25).attr("x2",n).attr("y2",i+45),p.append("line").attr("id","actor-man-arms"+ut).attr("x1",n-ht/2).attr("y1",i+33).attr("x2",n+ht/2).attr("y2",i+33),p.append("line").attr("x1",n-ht/2).attr("y1",i+60).attr("x2",n).attr("y2",i+45),p.append("line").attr("x1",n).attr("y1",i+45).attr("x2",n+ht/2-2).attr("y2",i+60);const b=p.append("circle");b.attr("cx",t.x+t.width/2),b.attr("cy",i+10),b.attr("r",15),b.attr("width",t.width),b.attr("height",t.height);const g=p.node().getBBox();return t.height=g.height,Bt(c,(0,r.l0)(t.description))(t.description,p,f.x,f.y+35,f.width,f.height,{class:`actor ${Zt}`},c),t.height},"drawActorTypeActor"),be=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){switch(t.type){case"actor":return yield Ee(e,t,c,s);case"participant":return yield Te(e,t,c,s)}})},"drawActor"),ye=(0,r.eW)(function(e,t,c){const i=e.append("g");$t(i,t),t.name&&Bt(c)(t.name,i,t.x,t.y+c.boxTextMargin+(t.textMaxHeight||0)/2,t.width,0,{class:"text"},c),i.lower()},"drawBox"),me=(0,r.eW)(function(e){return e.append("g")},"anchorElement"),we=(0,r.eW)(function(e,t,c,s,i){const n=(0,D.kc)(),o=t.anchored;n.x=t.startx,n.y=t.starty,n.class="activation"+i%3,n.width=t.stopx-t.startx,n.height=c-t.starty,Ct(o,n)},"drawActivation"),_e=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){const{boxMargin:i,boxTextMargin:n,labelBoxHeight:o,labelBoxWidth:d,messageFontFamily:p,messageFontSize:a,messageFontWeight:f}=s,b=e.append("g"),g=(0,r.eW)(function(_,A,B,M){return b.append("line").attr("x1",_).attr("y1",A).attr("x2",B).attr("y2",M).attr("class","loopLine")},"drawLoopLine");g(t.startx,t.starty,t.stopx,t.starty),g(t.stopx,t.starty,t.stopx,t.stopy),g(t.startx,t.stopy,t.stopx,t.stopy),g(t.startx,t.starty,t.startx,t.stopy),t.sections!==void 0&&t.sections.forEach(function(_){g(t.startx,_.y,t.stopx,_.y).style("stroke-dasharray","3, 3")});let T=(0,D.AD)();T.text=c,T.x=t.startx,T.y=t.starty,T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.anchor="middle",T.valign="middle",T.tspan=!1,T.width=d||50,T.height=o||20,T.textMargin=n,T.class="labelText",Qt(b,T),T=te(),T.text=t.title,T.x=t.startx+d/2+(t.stopx-t.startx)/2,T.y=t.starty+i+n,T.anchor="middle",T.valign="middle",T.textMargin=n,T.class="loopText",T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.wrap=!0;let m=(0,r.l0)(T.text)?yield St(b,T,t):vt(b,T);if(t.sectionTitles!==void 0){for(const[_,A]of Object.entries(t.sectionTitles))if(A.message){T.text=A.message,T.x=t.startx+(t.stopx-t.startx)/2,T.y=t.sections[_].y+i+n,T.class="loopText",T.anchor="middle",T.valign="middle",T.tspan=!1,T.fontFamily=p,T.fontSize=a,T.fontWeight=f,T.wrap=t.wrap,(0,r.l0)(T.text)?(t.starty=t.sections[_].y,yield St(b,T,t)):vt(b,T);let B=Math.round(m.map(M=>(M._groups||M)[0][0].getBBox().height).reduce((M,F)=>M+F));t.sections[_].height+=B-(i+n)}}return t.height=Math.round(t.stopy-t.starty),b})},"drawLoop"),$t=(0,r.eW)(function(e,t){(0,D.O)(e,t)},"drawBackgroundRect"),ve=(0,r.eW)(function(e){e.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),ke=(0,r.eW)(function(e){e.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),Ie=(0,r.eW)(function(e){e.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),Le=(0,r.eW)(function(e){e.append("defs").append("marker").attr("id","arrowhead").attr("refX",7.9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto-start-reverse").append("path").attr("d","M -1 0 L 10 5 L 0 10 z")},"insertArrowHead"),Pe=(0,r.eW)(function(e){e.append("defs").append("marker").attr("id","filled-head").attr("refX",15.5).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),Ae=(0,r.eW)(function(e){e.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertSequenceNumber"),Se=(0,r.eW)(function(e){e.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",4).attr("refY",4.5).append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1pt").attr("d","M 1,2 L 6,7 M 6,2 L 1,7")},"insertArrowCrossHead"),te=(0,r.eW)(function(){return{x:0,y:0,fill:void 0,anchor:void 0,style:"#666",width:void 0,height:void 0,textMargin:0,rx:0,ry:0,tspan:!0,valign:void 0}},"getTextObj"),Ne=(0,r.eW)(function(){return{x:0,y:0,fill:"#EDF2AE",stroke:"#666",width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),Bt=function(){function e(n,o,d,p,a,f,b){const g=o.append("text").attr("x",d+a/2).attr("y",p+f/2+5).style("text-anchor","middle").text(n);i(g,b)}(0,r.eW)(e,"byText");function t(n,o,d,p,a,f,b,g){const{actorFontSize:T,actorFontFamily:m,actorFontWeight:_}=g,[A,B]=(0,P.VG)(T),M=n.split(r.SY.lineBreakRegex);for(let F=0;F<M.length;F++){const Y=F*A-A*(M.length-1)/2,K=o.append("text").attr("x",d+a/2).attr("y",p).style("text-anchor","middle").style("font-size",B).style("font-weight",_).style("font-family",m);K.append("tspan").attr("x",d+a/2).attr("dy",Y).text(M[F]),K.attr("y",p+f/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),i(K,b)}}(0,r.eW)(t,"byTspan");function c(n,o,d,p,a,f,b,g){const T=o.append("switch"),_=T.append("foreignObject").attr("x",d).attr("y",p).attr("width",a).attr("height",f).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");_.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(n),t(n,T,d,p,a,f,b,g),i(_,b)}(0,r.eW)(c,"byFo");function s(n,o,d,p,a,f,b,g){return Q(this,null,function*(){const T=yield(0,r.nH)(n,(0,r.iE)()),m=o.append("switch"),A=m.append("foreignObject").attr("x",d+a/2-T.width/2).attr("y",p+f/2-T.height/2).attr("width",T.width).attr("height",T.height).append("xhtml:div").style("height","100%").style("width","100%");A.append("div").style("text-align","center").style("vertical-align","middle").html(yield(0,r.uT)(n,(0,r.iE)())),t(n,m,d,p,a,f,b,g),i(A,b)})}(0,r.eW)(s,"byKatex");function i(n,o){for(const d in o)o.hasOwnProperty(d)&&n.attr(d,o[d])}return(0,r.eW)(i,"_setTextAttrs"),function(n,o=!1){return o?s:n.textPlacement==="fo"?c:n.textPlacement==="old"?e:t}}(),Me=function(){function e(i,n,o,d,p,a,f){const b=n.append("text").attr("x",o).attr("y",d).style("text-anchor","start").text(i);s(b,f)}(0,r.eW)(e,"byText");function t(i,n,o,d,p,a,f,b){const{actorFontSize:g,actorFontFamily:T,actorFontWeight:m}=b,_=i.split(r.SY.lineBreakRegex);for(let A=0;A<_.length;A++){const B=A*g-g*(_.length-1)/2,M=n.append("text").attr("x",o).attr("y",d).style("text-anchor","start").style("font-size",g).style("font-weight",m).style("font-family",T);M.append("tspan").attr("x",o).attr("dy",B).text(_[A]),M.attr("y",d+a/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),s(M,f)}}(0,r.eW)(t,"byTspan");function c(i,n,o,d,p,a,f,b){const g=n.append("switch"),m=g.append("foreignObject").attr("x",o).attr("y",d).attr("width",p).attr("height",a).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");m.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(i),t(i,g,o,d,p,a,f,b),s(m,f)}(0,r.eW)(c,"byFo");function s(i,n){for(const o in n)n.hasOwnProperty(o)&&i.attr(o,n[o])}return(0,r.eW)(s,"_setTextAttrs"),function(i){return i.textPlacement==="fo"?c:i.textPlacement==="old"?e:t}}(),C={drawRect:Ct,drawText:vt,drawLabel:Qt,drawActor:be,drawBox:ye,drawPopup:fe,anchorElement:me,drawActivation:we,drawLoop:_e,drawBackgroundRect:$t,insertArrowHead:Le,insertArrowFilledHead:Pe,insertSequenceNumber:Ae,insertArrowCrossHead:Se,insertDatabaseIcon:ve,insertComputerIcon:ke,insertClockIcon:Ie,getTextObj:te,getNoteRect:Ne,fixLifeLineHeights:jt,sanitizeUrl:it.N},l={},x={data:{startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},verticalPos:0,sequenceItems:[],activations:[],models:{getHeight:(0,r.eW)(function(){return Math.max.apply(null,this.actors.length===0?[0]:this.actors.map(e=>e.height||0))+(this.loops.length===0?0:this.loops.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.messages.length===0?0:this.messages.map(e=>e.height||0).reduce((e,t)=>e+t))+(this.notes.length===0?0:this.notes.map(e=>e.height||0).reduce((e,t)=>e+t))},"getHeight"),clear:(0,r.eW)(function(){this.actors=[],this.boxes=[],this.loops=[],this.messages=[],this.notes=[]},"clear"),addBox:(0,r.eW)(function(e){this.boxes.push(e)},"addBox"),addActor:(0,r.eW)(function(e){this.actors.push(e)},"addActor"),addLoop:(0,r.eW)(function(e){this.loops.push(e)},"addLoop"),addMessage:(0,r.eW)(function(e){this.messages.push(e)},"addMessage"),addNote:(0,r.eW)(function(e){this.notes.push(e)},"addNote"),lastActor:(0,r.eW)(function(){return this.actors[this.actors.length-1]},"lastActor"),lastLoop:(0,r.eW)(function(){return this.loops[this.loops.length-1]},"lastLoop"),lastMessage:(0,r.eW)(function(){return this.messages[this.messages.length-1]},"lastMessage"),lastNote:(0,r.eW)(function(){return this.notes[this.notes.length-1]},"lastNote"),actors:[],boxes:[],loops:[],messages:[],notes:[]},init:(0,r.eW)(function(){this.sequenceItems=[],this.activations=[],this.models.clear(),this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0},this.verticalPos=0,re((0,r.nV)())},"init"),updateVal:(0,r.eW)(function(e,t,c,s){e[t]===void 0?e[t]=c:e[t]=s(c,e[t])},"updateVal"),updateBounds:(0,r.eW)(function(e,t,c,s){const i=this;let n=0;function o(d){return(0,r.eW)(function(a){n++;const f=i.sequenceItems.length-n+1;i.updateVal(a,"starty",t-f*l.boxMargin,Math.min),i.updateVal(a,"stopy",s+f*l.boxMargin,Math.max),i.updateVal(x.data,"startx",e-f*l.boxMargin,Math.min),i.updateVal(x.data,"stopx",c+f*l.boxMargin,Math.max),d!=="activation"&&(i.updateVal(a,"startx",e-f*l.boxMargin,Math.min),i.updateVal(a,"stopx",c+f*l.boxMargin,Math.max),i.updateVal(x.data,"starty",t-f*l.boxMargin,Math.min),i.updateVal(x.data,"stopy",s+f*l.boxMargin,Math.max))},"updateItemBounds")}(0,r.eW)(o,"updateFn"),this.sequenceItems.forEach(o()),this.activations.forEach(o("activation"))},"updateBounds"),insert:(0,r.eW)(function(e,t,c,s){const i=r.SY.getMin(e,c),n=r.SY.getMax(e,c),o=r.SY.getMin(t,s),d=r.SY.getMax(t,s);this.updateVal(x.data,"startx",i,Math.min),this.updateVal(x.data,"starty",o,Math.min),this.updateVal(x.data,"stopx",n,Math.max),this.updateVal(x.data,"stopy",d,Math.max),this.updateBounds(i,o,n,d)},"insert"),newActivation:(0,r.eW)(function(e,t,c){const s=c.get(e.from),i=Nt(e.from).length||0,n=s.x+s.width/2+(i-1)*l.activationWidth/2;this.activations.push({startx:n,starty:this.verticalPos+2,stopx:n+l.activationWidth,stopy:void 0,actor:e.from,anchored:C.anchorElement(t)})},"newActivation"),endActivation:(0,r.eW)(function(e){const t=this.activations.map(function(c){return c.actor}).lastIndexOf(e.from);return this.activations.splice(t,1)[0]},"endActivation"),createLoop:(0,r.eW)(function(e={message:void 0,wrap:!1,width:void 0},t){return{startx:void 0,starty:this.verticalPos,stopx:void 0,stopy:void 0,title:e.message,wrap:e.wrap,width:e.width,height:0,fill:t}},"createLoop"),newLoop:(0,r.eW)(function(e={message:void 0,wrap:!1,width:void 0},t){this.sequenceItems.push(this.createLoop(e,t))},"newLoop"),endLoop:(0,r.eW)(function(){return this.sequenceItems.pop()},"endLoop"),isLoopOverlap:(0,r.eW)(function(){return this.sequenceItems.length?this.sequenceItems[this.sequenceItems.length-1].overlap:!1},"isLoopOverlap"),addSectionToLoop:(0,r.eW)(function(e){const t=this.sequenceItems.pop();t.sections=t.sections||[],t.sectionTitles=t.sectionTitles||[],t.sections.push({y:x.getVerticalPos(),height:0}),t.sectionTitles.push(e),this.sequenceItems.push(t)},"addSectionToLoop"),saveVerticalPos:(0,r.eW)(function(){this.isLoopOverlap()&&(this.savedVerticalPos=this.verticalPos)},"saveVerticalPos"),resetVerticalPos:(0,r.eW)(function(){this.isLoopOverlap()&&(this.verticalPos=this.savedVerticalPos)},"resetVerticalPos"),bumpVerticalPos:(0,r.eW)(function(e){this.verticalPos=this.verticalPos+e,this.data.stopy=r.SY.getMax(this.data.stopy,this.verticalPos)},"bumpVerticalPos"),getVerticalPos:(0,r.eW)(function(){return this.verticalPos},"getVerticalPos"),getBounds:(0,r.eW)(function(){return{bounds:this.data,models:this.models}},"getBounds")},We=(0,r.eW)(function(e,t){return Q(this,null,function*(){x.bumpVerticalPos(l.boxMargin),t.height=l.boxMargin,t.starty=x.getVerticalPos();const c=(0,D.kc)();c.x=t.startx,c.y=t.starty,c.width=t.width||l.width,c.class="note";const s=e.append("g"),i=C.drawRect(s,c),n=(0,D.AD)();n.x=t.startx,n.y=t.starty,n.width=c.width,n.dy="1em",n.text=t.message,n.class="noteText",n.fontFamily=l.noteFontFamily,n.fontSize=l.noteFontSize,n.fontWeight=l.noteFontWeight,n.anchor=l.noteAlign,n.textMargin=l.noteMargin,n.valign="center";const o=(0,r.l0)(n.text)?yield St(s,n):vt(s,n),d=Math.round(o.map(p=>(p._groups||p)[0][0].getBBox().height).reduce((p,a)=>p+a));i.attr("height",d+2*l.noteMargin),t.height+=d+2*l.noteMargin,x.bumpVerticalPos(d+2*l.noteMargin),t.stopy=t.starty+d+2*l.noteMargin,t.stopx=t.startx+c.width,x.insert(t.startx,t.starty,t.stopx,t.stopy),x.models.addNote(t)})},"drawNote"),mt=(0,r.eW)(e=>({fontFamily:e.messageFontFamily,fontSize:e.messageFontSize,fontWeight:e.messageFontWeight}),"messageFont"),kt=(0,r.eW)(e=>({fontFamily:e.noteFontFamily,fontSize:e.noteFontSize,fontWeight:e.noteFontWeight}),"noteFont"),Vt=(0,r.eW)(e=>({fontFamily:e.actorFontFamily,fontSize:e.actorFontSize,fontWeight:e.actorFontWeight}),"actorFont");function ee(e,t){return Q(this,null,function*(){x.bumpVerticalPos(10);const{startx:c,stopx:s,message:i}=t,n=r.SY.splitBreaks(i).length,o=(0,r.l0)(i),d=o?yield(0,r.nH)(i,(0,r.nV)()):P.w8.calculateTextDimensions(i,mt(l));if(!o){const b=d.height/n;t.height+=b,x.bumpVerticalPos(b)}let p,a=d.height-10;const f=d.width;if(c===s){p=x.getVerticalPos()+a,l.rightAngles||(a+=l.boxMargin,p=x.getVerticalPos()+a),a+=30;const b=r.SY.getMax(f/2,l.width/2);x.insert(c-b,x.getVerticalPos()-10+a,s+b,x.getVerticalPos()+30+a)}else a+=l.boxMargin,p=x.getVerticalPos()+a,x.insert(c,p-10,s,p);return x.bumpVerticalPos(a),t.height+=a,t.stopy=t.starty+t.height,x.insert(t.fromBounds,t.starty,t.toBounds,t.stopy),p})}(0,r.eW)(ee,"boundMessage");var Re=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){const{startx:i,stopx:n,starty:o,message:d,type:p,sequenceIndex:a,sequenceVisible:f}=t,b=P.w8.calculateTextDimensions(d,mt(l)),g=(0,D.AD)();g.x=i,g.y=o+10,g.width=n-i,g.class="messageText",g.dy="1em",g.text=d,g.fontFamily=l.messageFontFamily,g.fontSize=l.messageFontSize,g.fontWeight=l.messageFontWeight,g.anchor=l.messageAlign,g.valign="center",g.textMargin=l.wrapPadding,g.tspan=!1,(0,r.l0)(g.text)?yield St(e,g,{startx:i,stopx:n,starty:c}):vt(e,g);const T=b.width;let m;i===n?l.rightAngles?m=e.append("path").attr("d",`M  ${i},${c} H ${i+r.SY.getMax(l.width/2,T/2)} V ${c+25} H ${i}`):m=e.append("path").attr("d","M "+i+","+c+" C "+(i+60)+","+(c-10)+" "+(i+60)+","+(c+30)+" "+i+","+(c+20)):(m=e.append("line"),m.attr("x1",i),m.attr("y1",c),m.attr("x2",n),m.attr("y2",c)),p===s.db.LINETYPE.DOTTED||p===s.db.LINETYPE.DOTTED_CROSS||p===s.db.LINETYPE.DOTTED_POINT||p===s.db.LINETYPE.DOTTED_OPEN||p===s.db.LINETYPE.BIDIRECTIONAL_DOTTED?(m.style("stroke-dasharray","3, 3"),m.attr("class","messageLine1")):m.attr("class","messageLine0");let _="";l.arrowMarkerAbsolute&&(_=(0,r.Gr)(!0)),m.attr("stroke-width",2),m.attr("stroke","none"),m.style("fill","none"),(p===s.db.LINETYPE.SOLID||p===s.db.LINETYPE.DOTTED)&&m.attr("marker-end","url("+_+"#arrowhead)"),(p===s.db.LINETYPE.BIDIRECTIONAL_SOLID||p===s.db.LINETYPE.BIDIRECTIONAL_DOTTED)&&(m.attr("marker-start","url("+_+"#arrowhead)"),m.attr("marker-end","url("+_+"#arrowhead)")),(p===s.db.LINETYPE.SOLID_POINT||p===s.db.LINETYPE.DOTTED_POINT)&&m.attr("marker-end","url("+_+"#filled-head)"),(p===s.db.LINETYPE.SOLID_CROSS||p===s.db.LINETYPE.DOTTED_CROSS)&&m.attr("marker-end","url("+_+"#crosshead)"),(f||l.showSequenceNumbers)&&(m.attr("marker-start","url("+_+"#sequencenumber)"),e.append("text").attr("x",i).attr("y",c+4).attr("font-family","sans-serif").attr("font-size","12px").attr("text-anchor","middle").attr("class","sequenceNumber").text(a))})},"drawMessage"),De=(0,r.eW)(function(e,t,c,s,i,n,o){let d=0,p=0,a,f=0;for(const b of s){const g=t.get(b),T=g.box;a&&a!=T&&(o||x.models.addBox(a),p+=l.boxMargin+a.margin),T&&T!=a&&(o||(T.x=d+p,T.y=i),p+=T.margin),g.width=g.width||l.width,g.height=r.SY.getMax(g.height||l.height,l.height),g.margin=g.margin||l.actorMargin,f=r.SY.getMax(f,g.height),c.get(g.name)&&(p+=g.width/2),g.x=d+p,g.starty=x.getVerticalPos(),x.insert(g.x,i,g.x+g.width,g.height),d+=g.width+p,g.box&&(g.box.width=d+T.margin-g.box.x),p=g.margin,a=g.box,x.models.addActor(g)}a&&!o&&x.models.addBox(a),x.bumpVerticalPos(f)},"addActorRenderingData"),Ft=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){if(s){let i=0;x.bumpVerticalPos(l.boxMargin*2);for(const n of c){const o=t.get(n);o.stopy||(o.stopy=x.getVerticalPos());const d=yield C.drawActor(e,o,l,!0);i=r.SY.getMax(i,d)}x.bumpVerticalPos(i+l.boxMargin)}else for(const i of c){const n=t.get(i);yield C.drawActor(e,n,l,!1)}})},"drawActors"),se=(0,r.eW)(function(e,t,c,s){let i=0,n=0;for(const o of c){const d=t.get(o),p=Ye(d),a=C.drawPopup(e,d,p,l,l.forceMenus,s);a.height>i&&(i=a.height),a.width+d.x>n&&(n=a.width+d.x)}return{maxHeight:i,maxWidth:n}},"drawActorsPopup"),re=(0,r.eW)(function(e){(0,r.Yc)(l,e),e.fontFamily&&(l.actorFontFamily=l.noteFontFamily=l.messageFontFamily=e.fontFamily),e.fontSize&&(l.actorFontSize=l.noteFontSize=l.messageFontSize=e.fontSize),e.fontWeight&&(l.actorFontWeight=l.noteFontWeight=l.messageFontWeight=e.fontWeight)},"setConf"),Nt=(0,r.eW)(function(e){return x.activations.filter(function(t){return t.actor===e})},"actorActivations"),ae=(0,r.eW)(function(e,t){const c=t.get(e),s=Nt(e),i=s.reduce(function(o,d){return r.SY.getMin(o,d.startx)},c.x+c.width/2-1),n=s.reduce(function(o,d){return r.SY.getMax(o,d.stopx)},c.x+c.width/2+1);return[i,n]},"activationBounds");function dt(e,t,c,s,i){x.bumpVerticalPos(c);let n=s;if(t.id&&t.message&&e[t.id]){const o=e[t.id].width,d=mt(l);t.message=P.w8.wrapLabel(`[${t.message}]`,o-2*l.wrapPadding,d),t.width=o,t.wrap=!0;const p=P.w8.calculateTextDimensions(t.message,d),a=r.SY.getMax(p.height,l.labelBoxHeight);n=s+a,r.cM.debug(`${a} - ${t.message}`)}i(t),x.bumpVerticalPos(n)}(0,r.eW)(dt,"adjustLoopHeightForWrap");function ie(e,t,c,s,i,n,o){function d(a,f){a.x<i.get(e.from).x?(x.insert(t.stopx-f,t.starty,t.startx,t.stopy+a.height/2+l.noteMargin),t.stopx=t.stopx+f):(x.insert(t.startx,t.starty,t.stopx+f,t.stopy+a.height/2+l.noteMargin),t.stopx=t.stopx-f)}(0,r.eW)(d,"receiverAdjustment");function p(a,f){a.x<i.get(e.to).x?(x.insert(t.startx-f,t.starty,t.stopx,t.stopy+a.height/2+l.noteMargin),t.startx=t.startx+f):(x.insert(t.stopx,t.starty,t.startx+f,t.stopy+a.height/2+l.noteMargin),t.startx=t.startx-f)}if((0,r.eW)(p,"senderAdjustment"),n.get(e.to)==s){const a=i.get(e.to),f=a.type=="actor"?ht/2+3:a.width/2+3;d(a,f),a.starty=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(e.from)==s){const a=i.get(e.from);if(l.mirrorActors){const f=a.type=="actor"?ht/2:a.width/2;p(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}else if(o.get(e.to)==s){const a=i.get(e.to);if(l.mirrorActors){const f=a.type=="actor"?ht/2+3:a.width/2+3;d(a,f)}a.stopy=c-a.height/2,x.bumpVerticalPos(a.height/2)}}(0,r.eW)(ie,"adjustCreatedDestroyedData");var Oe=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){const{securityLevel:i,sequence:n}=(0,r.nV)();l=n;let o;i==="sandbox"&&(o=(0,at.Ys)("#i"+t));const d=i==="sandbox"?(0,at.Ys)(o.nodes()[0].contentDocument.body):(0,at.Ys)("body"),p=i==="sandbox"?o.nodes()[0].contentDocument:document;x.init(),r.cM.debug(s.db);const a=i==="sandbox"?d.select(`[id="${t}"]`):(0,at.Ys)(`[id="${t}"]`),f=s.db.getActors(),b=s.db.getCreatedActors(),g=s.db.getDestroyedActors(),T=s.db.getBoxes();let m=s.db.getActorKeys();const _=s.db.getMessages(),A=s.db.getDiagramTitle(),B=s.db.hasAtLeastOneBox(),M=s.db.hasAtLeastOneBoxWithTitle(),F=yield ne(f,_,s);if(l.height=yield oe(f,F,T),C.insertComputerIcon(a),C.insertDatabaseIcon(a),C.insertClockIcon(a),B&&(x.bumpVerticalPos(l.boxMargin),M&&x.bumpVerticalPos(T[0].textMaxHeight)),l.hideUnusedParticipants===!0){const E=new Set;_.forEach(k=>{E.add(k.from),E.add(k.to)}),m=m.filter(k=>E.has(k))}De(a,f,b,m,0,_,!1);const Y=yield Ve(_,f,F,s);C.insertArrowHead(a),C.insertArrowCrossHead(a),C.insertArrowFilledHead(a),C.insertSequenceNumber(a);function K(E,k){const st=x.endActivation(E);st.starty+18>k&&(st.starty=k-6,k+=12),C.drawActivation(a,st,k,l,Nt(E.from).length),x.insert(st.startx,k-10,st.stopx,k)}(0,r.eW)(K,"activeEnd");let U=1,$=1;const ot=[],G=[];let X=0;for(const E of _){let k,st,pt;switch(E.type){case s.db.LINETYPE.NOTE:x.resetVerticalPos(),st=E.noteModel,yield We(a,st);break;case s.db.LINETYPE.ACTIVE_START:x.newActivation(E,a,f);break;case s.db.LINETYPE.ACTIVE_END:K(E,x.getVerticalPos());break;case s.db.LINETYPE.LOOP_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S));break;case s.db.LINETYPE.LOOP_END:k=x.endLoop(),yield C.drawLoop(a,k,"loop",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;case s.db.LINETYPE.RECT_START:dt(Y,E,l.boxMargin,l.boxMargin,S=>x.newLoop(void 0,S.message));break;case s.db.LINETYPE.RECT_END:k=x.endLoop(),G.push(k),x.models.addLoop(k),x.bumpVerticalPos(k.stopy-x.getVerticalPos());break;case s.db.LINETYPE.OPT_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S));break;case s.db.LINETYPE.OPT_END:k=x.endLoop(),yield C.drawLoop(a,k,"opt",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;case s.db.LINETYPE.ALT_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S));break;case s.db.LINETYPE.ALT_ELSE:dt(Y,E,l.boxMargin+l.boxTextMargin,l.boxMargin,S=>x.addSectionToLoop(S));break;case s.db.LINETYPE.ALT_END:k=x.endLoop(),yield C.drawLoop(a,k,"alt",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S)),x.saveVerticalPos();break;case s.db.LINETYPE.PAR_AND:dt(Y,E,l.boxMargin+l.boxTextMargin,l.boxMargin,S=>x.addSectionToLoop(S));break;case s.db.LINETYPE.PAR_END:k=x.endLoop(),yield C.drawLoop(a,k,"par",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;case s.db.LINETYPE.AUTONUMBER:U=E.message.start||U,$=E.message.step||$,E.message.visible?s.db.enableSequenceNumbers():s.db.disableSequenceNumbers();break;case s.db.LINETYPE.CRITICAL_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S));break;case s.db.LINETYPE.CRITICAL_OPTION:dt(Y,E,l.boxMargin+l.boxTextMargin,l.boxMargin,S=>x.addSectionToLoop(S));break;case s.db.LINETYPE.CRITICAL_END:k=x.endLoop(),yield C.drawLoop(a,k,"critical",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;case s.db.LINETYPE.BREAK_START:dt(Y,E,l.boxMargin,l.boxMargin+l.boxTextMargin,S=>x.newLoop(S));break;case s.db.LINETYPE.BREAK_END:k=x.endLoop(),yield C.drawLoop(a,k,"break",l),x.bumpVerticalPos(k.stopy-x.getVerticalPos()),x.models.addLoop(k);break;default:try{pt=E.msgModel,pt.starty=x.getVerticalPos(),pt.sequenceIndex=U,pt.sequenceVisible=s.db.showSequenceNumbers();const S=yield ee(a,pt);ie(E,pt,S,X,f,b,g),ot.push({messageModel:pt,lineStartY:S}),x.models.addMessage(pt)}catch(S){r.cM.error("error while drawing message",S)}}[s.db.LINETYPE.SOLID_OPEN,s.db.LINETYPE.DOTTED_OPEN,s.db.LINETYPE.SOLID,s.db.LINETYPE.DOTTED,s.db.LINETYPE.SOLID_CROSS,s.db.LINETYPE.DOTTED_CROSS,s.db.LINETYPE.SOLID_POINT,s.db.LINETYPE.DOTTED_POINT,s.db.LINETYPE.BIDIRECTIONAL_SOLID,s.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(E.type)&&(U=U+$),X++}r.cM.debug("createdActors",b),r.cM.debug("destroyedActors",g),yield Ft(a,f,m,!1);for(const E of ot)yield Re(a,E.messageModel,E.lineStartY,s);l.mirrorActors&&(yield Ft(a,f,m,!0)),G.forEach(E=>C.drawBackgroundRect(a,E)),jt(a,f,m,l);for(const E of x.models.boxes)E.height=x.getVerticalPos()-E.y,x.insert(E.x,E.y,E.x+E.width,E.height),E.startx=E.x,E.starty=E.y,E.stopx=E.startx+E.width,E.stopy=E.starty+E.height,E.stroke="rgb(0,0,0, 0.5)",C.drawBox(a,E,l);B&&x.bumpVerticalPos(l.boxMargin);const H=se(a,f,m,p),{bounds:R}=x.getBounds();R.startx===void 0&&(R.startx=0),R.starty===void 0&&(R.starty=0),R.stopx===void 0&&(R.stopx=0),R.stopy===void 0&&(R.stopy=0);let tt=R.stopy-R.starty;tt<H.maxHeight&&(tt=H.maxHeight);let Z=tt+2*l.diagramMarginY;l.mirrorActors&&(Z=Z-l.boxMargin+l.bottomMarginAdj);let et=R.stopx-R.startx;et<H.maxWidth&&(et=H.maxWidth);const ct=et+2*l.diagramMarginX;A&&a.append("text").text(A).attr("x",(R.stopx-R.startx)/2-2*l.diagramMarginX).attr("y",-25),(0,r.v2)(a,Z,ct,l.useMaxWidth);const N=A?40:0;a.attr("viewBox",R.startx-l.diagramMarginX+" -"+(l.diagramMarginY+N)+" "+ct+" "+(Z+N)),r.cM.debug("models:",x.models)})},"draw");function ne(e,t,c){return Q(this,null,function*(){const s={};for(const i of t)if(e.get(i.to)&&e.get(i.from)){const n=e.get(i.to);if(i.placement===c.db.PLACEMENT.LEFTOF&&!n.prevActor||i.placement===c.db.PLACEMENT.RIGHTOF&&!n.nextActor)continue;const o=i.placement!==void 0,d=!o,p=o?kt(l):mt(l),a=i.wrap?P.w8.wrapLabel(i.message,l.width-2*l.wrapPadding,p):i.message,b=((0,r.l0)(a)?yield(0,r.nH)(i.message,(0,r.nV)()):P.w8.calculateTextDimensions(a,p)).width+2*l.wrapPadding;d&&i.from===n.nextActor?s[i.to]=r.SY.getMax(s[i.to]||0,b):d&&i.from===n.prevActor?s[i.from]=r.SY.getMax(s[i.from]||0,b):d&&i.from===i.to?(s[i.from]=r.SY.getMax(s[i.from]||0,b/2),s[i.to]=r.SY.getMax(s[i.to]||0,b/2)):i.placement===c.db.PLACEMENT.RIGHTOF?s[i.from]=r.SY.getMax(s[i.from]||0,b):i.placement===c.db.PLACEMENT.LEFTOF?s[n.prevActor]=r.SY.getMax(s[n.prevActor]||0,b):i.placement===c.db.PLACEMENT.OVER&&(n.prevActor&&(s[n.prevActor]=r.SY.getMax(s[n.prevActor]||0,b/2)),n.nextActor&&(s[i.from]=r.SY.getMax(s[i.from]||0,b/2)))}return r.cM.debug("maxMessageWidthPerActor:",s),s})}(0,r.eW)(ne,"getMaxMessageWidthPerActor");var Ye=(0,r.eW)(function(e){let t=0;const c=Vt(l);for(const s in e.links){const n=P.w8.calculateTextDimensions(s,c).width+2*l.wrapPadding+2*l.boxMargin;t<n&&(t=n)}return t},"getRequiredPopupWidth");function oe(e,t,c){return Q(this,null,function*(){let s=0;for(const n of e.keys()){const o=e.get(n);o.wrap&&(o.description=P.w8.wrapLabel(o.description,l.width-2*l.wrapPadding,Vt(l)));const d=(0,r.l0)(o.description)?yield(0,r.nH)(o.description,(0,r.nV)()):P.w8.calculateTextDimensions(o.description,Vt(l));o.width=o.wrap?l.width:r.SY.getMax(l.width,d.width+2*l.wrapPadding),o.height=o.wrap?r.SY.getMax(d.height,l.height):l.height,s=r.SY.getMax(s,o.height)}for(const n in t){const o=e.get(n);if(!o)continue;const d=e.get(o.nextActor);if(!d){const b=t[n]+l.actorMargin-o.width/2;o.margin=r.SY.getMax(b,l.actorMargin);continue}const a=t[n]+l.actorMargin-o.width/2-d.width/2;o.margin=r.SY.getMax(a,l.actorMargin)}let i=0;return c.forEach(n=>{const o=mt(l);let d=n.actorKeys.reduce((f,b)=>f+=e.get(b).width+(e.get(b).margin||0),0);d-=2*l.boxTextMargin,n.wrap&&(n.name=P.w8.wrapLabel(n.name,d-2*l.wrapPadding,o));const p=P.w8.calculateTextDimensions(n.name,o);i=r.SY.getMax(p.height,i);const a=r.SY.getMax(d,p.width+2*l.wrapPadding);if(n.margin=l.boxTextMargin,d<a){const f=(a-d)/2;n.margin+=f}}),c.forEach(n=>n.textMaxHeight=i),r.SY.getMax(s,l.height)})}(0,r.eW)(oe,"calculateActorMargins");var Ce=(0,r.eW)(function(e,t,c){return Q(this,null,function*(){const s=t.get(e.from),i=t.get(e.to),n=s.x,o=i.x,d=e.wrap&&e.message;let p=(0,r.l0)(e.message)?yield(0,r.nH)(e.message,(0,r.nV)()):P.w8.calculateTextDimensions(d?P.w8.wrapLabel(e.message,l.width,kt(l)):e.message,kt(l));const a={width:d?l.width:r.SY.getMax(l.width,p.width+2*l.noteMargin),height:0,startx:s.x,stopx:0,starty:0,stopy:0,message:e.message};return e.placement===c.db.PLACEMENT.RIGHTOF?(a.width=d?r.SY.getMax(l.width,p.width):r.SY.getMax(s.width/2+i.width/2,p.width+2*l.noteMargin),a.startx=n+(s.width+l.actorMargin)/2):e.placement===c.db.PLACEMENT.LEFTOF?(a.width=d?r.SY.getMax(l.width,p.width+2*l.noteMargin):r.SY.getMax(s.width/2+i.width/2,p.width+2*l.noteMargin),a.startx=n-a.width+(s.width-l.actorMargin)/2):e.to===e.from?(p=P.w8.calculateTextDimensions(d?P.w8.wrapLabel(e.message,r.SY.getMax(l.width,s.width),kt(l)):e.message,kt(l)),a.width=d?r.SY.getMax(l.width,s.width):r.SY.getMax(s.width,l.width,p.width+2*l.noteMargin),a.startx=n+(s.width-a.width)/2):(a.width=Math.abs(n+s.width/2-(o+i.width/2))+l.actorMargin,a.startx=n<o?n+s.width/2-l.actorMargin/2:o+i.width/2-l.actorMargin/2),d&&(a.message=P.w8.wrapLabel(e.message,a.width-2*l.wrapPadding,kt(l))),r.cM.debug(`NM:[${a.startx},${a.stopx},${a.starty},${a.stopy}:${a.width},${a.height}=${e.message}]`),a})},"buildNoteModel"),Be=(0,r.eW)(function(e,t,c){if(![c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN,c.db.LINETYPE.SOLID,c.db.LINETYPE.DOTTED,c.db.LINETYPE.SOLID_CROSS,c.db.LINETYPE.DOTTED_CROSS,c.db.LINETYPE.SOLID_POINT,c.db.LINETYPE.DOTTED_POINT,c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type))return{};const[s,i]=ae(e.from,t),[n,o]=ae(e.to,t),d=s<=n;let p=d?i:s,a=d?n:o;const f=Math.abs(n-o)>2,b=(0,r.eW)(_=>d?-_:_,"adjustValue");e.from===e.to?a=p:(e.activate&&!f&&(a+=b(l.activationWidth/2-1)),[c.db.LINETYPE.SOLID_OPEN,c.db.LINETYPE.DOTTED_OPEN].includes(e.type)||(a+=b(3)),[c.db.LINETYPE.BIDIRECTIONAL_SOLID,c.db.LINETYPE.BIDIRECTIONAL_DOTTED].includes(e.type)&&(p-=b(3)));const g=[s,i,n,o],T=Math.abs(p-a);e.wrap&&e.message&&(e.message=P.w8.wrapLabel(e.message,r.SY.getMax(T+2*l.wrapPadding,l.width),mt(l)));const m=P.w8.calculateTextDimensions(e.message,mt(l));return{width:r.SY.getMax(e.wrap?0:m.width+2*l.wrapPadding,T+2*l.wrapPadding,l.width),height:0,startx:p,stopx:a,starty:0,stopy:0,message:e.message,type:e.type,wrap:e.wrap,fromBounds:Math.min.apply(null,g),toBounds:Math.max.apply(null,g)}},"buildMessageModel"),Ve=(0,r.eW)(function(e,t,c,s){return Q(this,null,function*(){const i={},n=[];let o,d,p;for(const a of e){switch(a.type){case s.db.LINETYPE.LOOP_START:case s.db.LINETYPE.ALT_START:case s.db.LINETYPE.OPT_START:case s.db.LINETYPE.PAR_START:case s.db.LINETYPE.PAR_OVER_START:case s.db.LINETYPE.CRITICAL_START:case s.db.LINETYPE.BREAK_START:n.push({id:a.id,msg:a.message,from:Number.MAX_SAFE_INTEGER,to:Number.MIN_SAFE_INTEGER,width:0});break;case s.db.LINETYPE.ALT_ELSE:case s.db.LINETYPE.PAR_AND:case s.db.LINETYPE.CRITICAL_OPTION:a.message&&(o=n.pop(),i[o.id]=o,i[a.id]=o,n.push(o));break;case s.db.LINETYPE.LOOP_END:case s.db.LINETYPE.ALT_END:case s.db.LINETYPE.OPT_END:case s.db.LINETYPE.PAR_END:case s.db.LINETYPE.CRITICAL_END:case s.db.LINETYPE.BREAK_END:o=n.pop(),i[o.id]=o;break;case s.db.LINETYPE.ACTIVE_START:{const b=t.get(a.from?a.from:a.to.actor),g=Nt(a.from?a.from:a.to.actor).length,T=b.x+b.width/2+(g-1)*l.activationWidth/2,m={startx:T,stopx:T+l.activationWidth,actor:a.from,enabled:!0};x.activations.push(m)}break;case s.db.LINETYPE.ACTIVE_END:{const b=x.activations.map(g=>g.actor).lastIndexOf(a.from);x.activations.splice(b,1).splice(0,1)}break}a.placement!==void 0?(d=yield Ce(a,t,s),a.noteModel=d,n.forEach(b=>{o=b,o.from=r.SY.getMin(o.from,d.startx),o.to=r.SY.getMax(o.to,d.startx+d.width),o.width=r.SY.getMax(o.width,Math.abs(o.from-o.to))-l.labelBoxWidth})):(p=Be(a,t,s),a.msgModel=p,p.startx&&p.stopx&&n.length>0&&n.forEach(b=>{if(o=b,p.startx===p.stopx){const g=t.get(a.from),T=t.get(a.to);o.from=r.SY.getMin(g.x-p.width/2,g.x-g.width/2,o.from),o.to=r.SY.getMax(T.x+p.width/2,T.x+g.width/2,o.to),o.width=r.SY.getMax(o.width,Math.abs(o.to-o.from))-l.labelBoxWidth}else o.from=r.SY.getMin(p.startx,o.from),o.to=r.SY.getMax(p.stopx,o.to),o.width=r.SY.getMax(o.width,p.width)-l.labelBoxWidth}))}return x.activations=[],r.cM.debug("Loop type widths:",i),i})},"calculateLoopBounds"),Fe={bounds:x,drawActors:Ft,drawActorsPopup:se,setConf:re,draw:Oe},qe={parser:Ot,get db(){return new O},renderer:Fe,styles:nt,init:(0,r.eW)(e=>{e.sequence||(e.sequence={}),e.wrap&&(e.sequence.wrap=e.wrap,(0,r.Y4)({sequence:{wrap:e.wrap}}))},"init")}}}]);
}());