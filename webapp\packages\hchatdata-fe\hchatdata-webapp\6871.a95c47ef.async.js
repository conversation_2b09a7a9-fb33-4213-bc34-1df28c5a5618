(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6871],{64157:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return V}});function V(_,it,Q){if(_){if(typeof _.addEventListener=="function")return _.addEventListener(it,Q,!1),{remove:function(){_.removeEventListener(it,Q,!1)}};if(typeof _.attachEvent=="function")return _.attachEvent("on"+it,Q),{remove:function(){_.detachEvent("on"+it,Q)}}}}},25483:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return X}});var V,_,it,Q;function j(){V=document.createElement("table"),_=document.createElement("tr"),it=/^\s*<(\w+|!)[^>]*>/,Q={tr:document.createElement("tbody"),tbody:V,thead:V,tfoot:V,td:_,th:_,"*":document.createElement("div")}}function X(b){V||j();var N=it.test(b)&&RegExp.$1;(!N||!(N in Q))&&(N="*");var z=Q[N];b=typeof b=="string"?b.replace(/(^\s*)|(\s*$)/g,""):b,z.innerHTML=""+b;var q=z.childNodes[0];return q&&z.contains(q)&&z.removeChild(q),q}},57074:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return V}});function V(_,it){if(_)for(var Q in it)it.hasOwnProperty(Q)&&(_.style[Q]=it[Q]);return _}},53275:function(Lt,Mt){"use strict";var k="*",V=function(){function _(){this._events={}}return _.prototype.on=function(it,Q,j){return this._events[it]||(this._events[it]=[]),this._events[it].push({callback:Q,once:!!j}),this},_.prototype.once=function(it,Q){return this.on(it,Q,!0)},_.prototype.emit=function(it){for(var Q=this,j=[],X=1;X<arguments.length;X++)j[X-1]=arguments[X];var b=this._events[it]||[],N=this._events[k]||[],z=function(q){for(var W=q.length,Y=0;Y<W;Y++)if(q[Y]){var D=q[Y],E=D.callback,O=D.once;O&&(q.splice(Y,1),q.length===0&&delete Q._events[it],W--,Y--),E.apply(Q,j)}};z(b),z(N)},_.prototype.off=function(it,Q){if(!it)this._events={};else if(!Q)delete this._events[it];else{for(var j=this._events[it]||[],X=j.length,b=0;b<X;b++)j[b].callback===Q&&(j.splice(b,1),X--,b--);j.length===0&&delete this._events[it]}return this},_.prototype.getEvents=function(){return this._events},_}();Mt.Z=V},84670:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(53275),it=k(27127),Q=function(j){(0,V.__extends)(X,j);function X(b){var N=j.call(this)||this;N.destroyed=!1;var z=N.getDefaultCfg();return N.cfg=(0,it.CD)(z,b),N}return X.prototype.getDefaultCfg=function(){return{}},X.prototype.get=function(b){return this.cfg[b]},X.prototype.set=function(b,N){this.cfg[b]=N},X.prototype.destroy=function(){this.cfg={destroyed:!0},this.off(),this.destroyed=!0},X}(_.Z);Mt.Z=Q},78635:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return a}});var V=k(92336),_=k(79935),it=k(44761),Q=k(27127),j=k(98667),X=0,b=0,N=0,z=1e3,q,W,Y=0,D=0,E=0,O=typeof performance=="object"&&performance.now?performance:Date,U=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(s){setTimeout(s,17)};function nt(){return D||(U(st),D=O.now()+E)}function st(){D=0}function H(){this._call=this._time=this._next=null}H.prototype=C.prototype={constructor:H,restart:function(s,e,u){if(typeof s!="function")throw new TypeError("callback is not a function");u=(u==null?nt():+u)+(e==null?0:+e),!this._next&&W!==this&&(W?W._next=this:q=this,W=this),this._call=s,this._time=u,mt()},stop:function(){this._call&&(this._call=null,this._time=1/0,mt())}};function C(s,e,u){var l=new H;return l.restart(s,e,u),l}function S(){nt(),++X;for(var s=q,e;s;)(e=D-s._time)>=0&&s._call.call(null,e),s=s._next;--X}function Z(){D=(Y=O.now())+E,X=b=0;try{S()}finally{X=0,ht(),D=0}}function J(){var s=O.now(),e=s-Y;e>z&&(E-=e,Y=s)}function ht(){for(var s,e=q,u,l=1/0;e;)e._call?(l>e._time&&(l=e._time),s=e,e=e._next):(u=e._next,e._next=null,e=s?s._next=u:q=u);W=s,mt(l)}function mt(s){if(!X){b&&(b=clearTimeout(b));var e=s-D;e>24?(s<1/0&&(b=setTimeout(Z,s-O.now()-E)),N&&(N=clearInterval(N))):(N||(Y=O.now(),N=setInterval(J,z)),X=1,U(Z))}}var Pt=k(34404),Yt=k(65099),Nt=k(36278),dt=k(16900),pt=function(s){return["fill","stroke","fillStyle","strokeStyle"].includes(s)},xt=function(s){return/^[r,R,L,l]{1}[\s]*\(/.test(s)},Et=[1,0,0,0,1,0,0,0,1];function wt(s,e,u){var l={},v=e.fromAttrs,i=e.toAttrs;if(!s.destroyed){var y;for(var x in i)if(!(0,j.isEqual)(v[x],i[x]))if(x==="path"){var o=i[x],h=v[x];o.length>h.length?(o=dt.parsePathString(i[x]),h=dt.parsePathString(v[x]),h=dt.fillPathByDiff(h,o),h=dt.formatPath(h,o),e.fromAttrs.path=h,e.toAttrs.path=o):e.pathFormatted||(o=dt.parsePathString(i[x]),h=dt.parsePathString(v[x]),h=dt.formatPath(h,o),e.fromAttrs.path=h,e.toAttrs.path=o,e.pathFormatted=!0),l[x]=[];for(var d=0;d<o.length;d++){for(var B=o[d],K=h[d],tt=[],et=0;et<B.length;et++)(0,j.isNumber)(B[et])&&K&&(0,j.isNumber)(K[et])?(y=(0,Pt.Z)(K[et],B[et]),tt.push(y(u))):tt.push(B[et]);l[x].push(tt)}}else if(x==="matrix"){var ft=(0,Yt.Z)(v[x]||Et,i[x]||Et),ct=ft(u);l[x]=ct}else pt(x)&&xt(i[x])?l[x]=i[x]:(0,j.isFunction)(i[x])||(y=(0,Pt.Z)(v[x],i[x]),l[x]=y(u));s.attr(l)}}function c(s,e,u){var l=e.startTime,v=e.delay;if(u<l+v||e._paused)return!1;var i,y=e.duration,x=e.easing,o=(0,Nt._)(x);if(u=u-l-e.delay,e.repeat)i=u%y/y,i=o(i);else if(i=u/y,i<1)i=o(i);else return e.onFrame?s.attr(e.onFrame(1)):s.attr(e.toAttrs),!0;if(e.onFrame){var h=e.onFrame(i);s.attr(h)}else wt(s,e,i);return!1}var A=function(){function s(e){this.animators=[],this.current=0,this.timer=null,this.canvas=e}return s.prototype.initTimer=function(){var e=this,u=!1,l,v,i;this.timer=C(function(y){if(e.current=y,e.animators.length>0){for(var x=e.animators.length-1;x>=0;x--){if(l=e.animators[x],l.destroyed){e.removeAnimator(x);continue}if(!l.isAnimatePaused()){v=l.get("animations");for(var o=v.length-1;o>=0;o--)i=v[o],u=c(l,i,y),u&&(v.splice(o,1),u=!1,i.callback&&i.callback())}v.length===0&&e.removeAnimator(x)}var h=e.canvas.get("autoDraw");h||e.canvas.draw()}})},s.prototype.addAnimator=function(e){this.animators.push(e)},s.prototype.removeAnimator=function(e){this.animators.splice(e,1)},s.prototype.isAnimating=function(){return!!this.animators.length},s.prototype.stop=function(){this.timer&&this.timer.stop()},s.prototype.stopAllAnimations=function(e){e===void 0&&(e=!0),this.animators.forEach(function(u){u.stopAnimate(e)}),this.animators=[],this.canvas.draw()},s.prototype.getTime=function(){return this.current},s}(),G=A,vt=k(73834),yt=40,p=0,M=":",L=["mousedown","mouseup","dblclick","mouseout","mouseover","mousemove","mouseleave","mouseenter","touchstart","touchmove","touchend","dragenter","dragover","dragleave","drop","contextmenu","mousewheel"];function T(s,e){for(var u in s)if(s.hasOwnProperty(u)&&u.indexOf(M+e)>=0)return!0;return!1}function $(s,e,u){u.name=e,u.target=s,u.currentTarget=s,u.delegateTarget=s,s.emit(e,u)}function ut(s,e,u){if(u.bubbles){var l=void 0,v=!1;if(e==="mouseenter"?(l=u.fromShape,v=!0):e==="mouseleave"&&(v=!0,l=u.toShape),s.isCanvas()&&v)return;if(l&&(0,Q.UY)(s,l)){u.bubbles=!1;return}u.name=e,u.currentTarget=s,u.delegateTarget=s,s.emit(e,u)}}var P=function(){function s(e){var u=this;this.draggingShape=null,this.dragging=!1,this.currentShape=null,this.mousedownShape=null,this.mousedownPoint=null,this._eventCallback=function(l){var v=l.type;u._triggerEvent(v,l)},this._onDocumentMove=function(l){var v=u.canvas,i=v.get("el");if(i!==l.target&&(u.dragging||u.currentShape)){var y=u._getPointInfo(l);u.dragging&&u._emitEvent("drag",l,y,u.draggingShape)}},this._onDocumentMouseUp=function(l){var v=u.canvas,i=v.get("el");if(i!==l.target&&u.dragging){var y=u._getPointInfo(l);u.draggingShape&&u._emitEvent("drop",l,y,null),u._emitEvent("dragend",l,y,u.draggingShape),u._afterDrag(u.draggingShape,y,l)}},this.canvas=e.canvas}return s.prototype.init=function(){this._bindEvents()},s.prototype._bindEvents=function(){var e=this,u=this.canvas.get("el");(0,Q.S6)(L,function(l){u.addEventListener(l,e._eventCallback)}),document&&(document.addEventListener("mousemove",this._onDocumentMove),document.addEventListener("mouseup",this._onDocumentMouseUp))},s.prototype._clearEvents=function(){var e=this,u=this.canvas.get("el");(0,Q.S6)(L,function(l){u.removeEventListener(l,e._eventCallback)}),document&&(document.removeEventListener("mousemove",this._onDocumentMove),document.removeEventListener("mouseup",this._onDocumentMouseUp))},s.prototype._getEventObj=function(e,u,l,v,i,y){var x=new vt.Z(e,u);return x.fromShape=i,x.toShape=y,x.x=l.x,x.y=l.y,x.clientX=l.clientX,x.clientY=l.clientY,x.propagationPath.push(v),x},s.prototype._getShape=function(e,u){return this.canvas.getShape(e.x,e.y,u)},s.prototype._getPointInfo=function(e){var u=this.canvas,l=u.getClientByEvent(e),v=u.getPointByEvent(e);return{x:v.x,y:v.y,clientX:l.x,clientY:l.y}},s.prototype._triggerEvent=function(e,u){var l=this._getPointInfo(u),v=this._getShape(l,u),i=this["_on"+e],y=!1;if(i)i.call(this,l,v,u);else{var x=this.currentShape;e==="mouseenter"||e==="dragenter"||e==="mouseover"?(this._emitEvent(e,u,l,null,null,v),v&&this._emitEvent(e,u,l,v,null,v),e==="mouseenter"&&this.draggingShape&&this._emitEvent("dragenter",u,l,null)):e==="mouseleave"||e==="dragleave"||e==="mouseout"?(y=!0,x&&this._emitEvent(e,u,l,x,x,null),this._emitEvent(e,u,l,null,x,null),e==="mouseleave"&&this.draggingShape&&this._emitEvent("dragleave",u,l,null)):this._emitEvent(e,u,l,v,null,null)}if(y||(this.currentShape=v),v&&!v.get("destroyed")){var o=this.canvas,h=o.get("el");h.style.cursor=v.attr("cursor")||o.get("cursor")}},s.prototype._onmousedown=function(e,u,l){l.button===p&&(this.mousedownShape=u,this.mousedownPoint=e,this.mousedownTimeStamp=l.timeStamp),this._emitEvent("mousedown",l,e,u,null,null)},s.prototype._emitMouseoverEvents=function(e,u,l,v){var i=this.canvas.get("el");l!==v&&(l&&(this._emitEvent("mouseout",e,u,l,l,v),this._emitEvent("mouseleave",e,u,l,l,v),(!v||v.get("destroyed"))&&(i.style.cursor=this.canvas.get("cursor"))),v&&(this._emitEvent("mouseover",e,u,v,l,v),this._emitEvent("mouseenter",e,u,v,l,v)))},s.prototype._emitDragoverEvents=function(e,u,l,v,i){v?(v!==l&&(l&&this._emitEvent("dragleave",e,u,l,l,v),this._emitEvent("dragenter",e,u,v,l,v)),i||this._emitEvent("dragover",e,u,v)):l&&this._emitEvent("dragleave",e,u,l,l,v),i&&this._emitEvent("dragover",e,u,v)},s.prototype._afterDrag=function(e,u,l){e&&(e.set("capture",!0),this.draggingShape=null),this.dragging=!1;var v=this._getShape(u,l);v!==e&&this._emitMouseoverEvents(l,u,e,v),this.currentShape=v},s.prototype._onmouseup=function(e,u,l){if(l.button===p){var v=this.draggingShape;this.dragging?(v&&this._emitEvent("drop",l,e,u),this._emitEvent("dragend",l,e,v),this._afterDrag(v,e,l)):(this._emitEvent("mouseup",l,e,u),u===this.mousedownShape&&this._emitEvent("click",l,e,u),this.mousedownShape=null,this.mousedownPoint=null)}},s.prototype._ondragover=function(e,u,l){l.preventDefault();var v=this.currentShape;this._emitDragoverEvents(l,e,v,u,!0)},s.prototype._onmousemove=function(e,u,l){var v=this.canvas,i=this.currentShape,y=this.draggingShape;if(this.dragging)y&&this._emitDragoverEvents(l,e,i,u,!1),this._emitEvent("drag",l,e,y);else{var x=this.mousedownPoint;if(x){var o=this.mousedownShape,h=l.timeStamp,d=h-this.mousedownTimeStamp,B=x.clientX-e.clientX,K=x.clientY-e.clientY,tt=B*B+K*K;d>120||tt>yt?o&&o.get("draggable")?(y=this.mousedownShape,y.set("capture",!1),this.draggingShape=y,this.dragging=!0,this._emitEvent("dragstart",l,e,y),this.mousedownShape=null,this.mousedownPoint=null):!o&&v.get("draggable")?(this.dragging=!0,this._emitEvent("dragstart",l,e,null),this.mousedownShape=null,this.mousedownPoint=null):(this._emitMouseoverEvents(l,e,i,u),this._emitEvent("mousemove",l,e,u)):(this._emitMouseoverEvents(l,e,i,u),this._emitEvent("mousemove",l,e,u))}else this._emitMouseoverEvents(l,e,i,u),this._emitEvent("mousemove",l,e,u)}},s.prototype._emitEvent=function(e,u,l,v,i,y){var x=this._getEventObj(e,u,l,v,i,y);if(v){x.shape=v,$(v,e,x);for(var o=v.getParent();o;)o.emitDelegation(e,x),x.propagationStopped||ut(o,e,x),x.propagationPath.push(o),o=o.getParent()}else{var h=this.canvas;$(h,e,x)}},s.prototype.destroy=function(){this._clearEvents(),this.canvas=null,this.currentShape=null,this.draggingShape=null,this.mousedownPoint=null,this.mousedownShape=null,this.mousedownTimeStamp=null},s}(),g=P,r="px",t=(0,_.qY)(),f=t&&t.name==="firefox",n=function(s){(0,V.__extends)(e,s);function e(u){var l=s.call(this,u)||this;return l.initContainer(),l.initDom(),l.initEvents(),l.initTimeline(),l}return e.prototype.getDefaultCfg=function(){var u=s.prototype.getDefaultCfg.call(this);return u.cursor="default",u.supportCSSTransform=!1,u},e.prototype.initContainer=function(){var u=this.get("container");(0,Q.HD)(u)&&(u=document.getElementById(u),this.set("container",u))},e.prototype.initDom=function(){var u=this.createDom();this.set("el",u);var l=this.get("container");l.appendChild(u),this.setDOMSize(this.get("width"),this.get("height"))},e.prototype.initEvents=function(){var u=new g({canvas:this});u.init(),this.set("eventController",u)},e.prototype.initTimeline=function(){var u=new G(this);this.set("timeline",u)},e.prototype.setDOMSize=function(u,l){var v=this.get("el");Q.jU&&(v.style.width=u+r,v.style.height=l+r)},e.prototype.changeSize=function(u,l){this.setDOMSize(u,l),this.set("width",u),this.set("height",l),this.onCanvasChange("changeSize")},e.prototype.getRenderer=function(){return this.get("renderer")},e.prototype.getCursor=function(){return this.get("cursor")},e.prototype.setCursor=function(u){this.set("cursor",u);var l=this.get("el");Q.jU&&l&&(l.style.cursor=u)},e.prototype.getPointByEvent=function(u){var l=this.get("supportCSSTransform");if(l){if(f&&!(0,Q.kK)(u.layerX)&&u.layerX!==u.offsetX)return{x:u.layerX,y:u.layerY};if(!(0,Q.kK)(u.offsetX))return{x:u.offsetX,y:u.offsetY}}var v=this.getClientByEvent(u),i=v.x,y=v.y;return this.getPointByClient(i,y)},e.prototype.getClientByEvent=function(u){var l=u;return u.touches&&(u.type==="touchend"?l=u.changedTouches[0]:l=u.touches[0]),{x:l.clientX,y:l.clientY}},e.prototype.getPointByClient=function(u,l){var v=this.get("el"),i=v.getBoundingClientRect();return{x:u-i.left,y:l-i.top}},e.prototype.getClientByPoint=function(u,l){var v=this.get("el"),i=v.getBoundingClientRect();return{x:u+i.left,y:l+i.top}},e.prototype.draw=function(){},e.prototype.removeDom=function(){var u=this.get("el");u.parentNode.removeChild(u)},e.prototype.clearEvents=function(){var u=this.get("eventController");u.destroy()},e.prototype.isCanvas=function(){return!0},e.prototype.getParent=function(){return null},e.prototype.destroy=function(){var u=this.get("timeline");this.get("destroyed")||(this.clear(),u&&u.stop(),this.clearEvents(),this.removeDom(),s.prototype.destroy.call(this))},e}(it.Z),a=n},44761:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(73866),it=k(27127),Q={},j="_INDEX";function X(Y,D){if(Y.set("canvas",D),Y.isGroup()){var E=Y.get("children");E.length&&E.forEach(function(O){X(O,D)})}}function b(Y,D){if(Y.set("timeline",D),Y.isGroup()){var E=Y.get("children");E.length&&E.forEach(function(O){b(O,D)})}}function N(Y,D){var E=Y.getChildren();return E.indexOf(D)>=0}function z(Y,D,E){E===void 0&&(E=!0),E?D.destroy():(D.set("parent",null),D.set("canvas",null)),(0,it.As)(Y.getChildren(),D)}function q(Y){return function(D,E){var O=Y(D,E);return O===0?D[j]-E[j]:O}}var W=function(Y){(0,V.__extends)(D,Y);function D(){return Y!==null&&Y.apply(this,arguments)||this}return D.prototype.isCanvas=function(){return!1},D.prototype.getBBox=function(){var E=1/0,O=-1/0,U=1/0,nt=-1/0,st=this.getChildren().filter(function(C){return C.get("visible")&&(!C.isGroup()||C.isGroup()&&C.getChildren().length>0)});st.length>0?(0,it.S6)(st,function(C){var S=C.getBBox(),Z=S.minX,J=S.maxX,ht=S.minY,mt=S.maxY;Z<E&&(E=Z),J>O&&(O=J),ht<U&&(U=ht),mt>nt&&(nt=mt)}):(E=0,O=0,U=0,nt=0);var H={x:E,y:U,minX:E,minY:U,maxX:O,maxY:nt,width:O-E,height:nt-U};return H},D.prototype.getCanvasBBox=function(){var E=1/0,O=-1/0,U=1/0,nt=-1/0,st=this.getChildren().filter(function(C){return C.get("visible")&&(!C.isGroup()||C.isGroup()&&C.getChildren().length>0)});st.length>0?(0,it.S6)(st,function(C){var S=C.getCanvasBBox(),Z=S.minX,J=S.maxX,ht=S.minY,mt=S.maxY;Z<E&&(E=Z),J>O&&(O=J),ht<U&&(U=ht),mt>nt&&(nt=mt)}):(E=0,O=0,U=0,nt=0);var H={x:E,y:U,minX:E,minY:U,maxX:O,maxY:nt,width:O-E,height:nt-U};return H},D.prototype.getDefaultCfg=function(){var E=Y.prototype.getDefaultCfg.call(this);return E.children=[],E},D.prototype.onAttrChange=function(E,O,U){if(Y.prototype.onAttrChange.call(this,E,O,U),E==="matrix"){var nt=this.getTotalMatrix();this._applyChildrenMarix(nt)}},D.prototype.applyMatrix=function(E){var O=this.getTotalMatrix();Y.prototype.applyMatrix.call(this,E);var U=this.getTotalMatrix();U!==O&&this._applyChildrenMarix(U)},D.prototype._applyChildrenMarix=function(E){var O=this.getChildren();(0,it.S6)(O,function(U){U.applyMatrix(E)})},D.prototype.addShape=function(){for(var E=[],O=0;O<arguments.length;O++)E[O]=arguments[O];var U=E[0],nt=E[1];(0,it.Kn)(U)?nt=U:nt.type=U;var st=Q[nt.type];st||(st=(0,it.jC)(nt.type),Q[nt.type]=st);var H=this.getShapeBase(),C=new H[st](nt);return this.add(C),C},D.prototype.addGroup=function(){for(var E=[],O=0;O<arguments.length;O++)E[O]=arguments[O];var U=E[0],nt=E[1],st;if((0,it.mf)(U))nt?st=new U(nt):st=new U({parent:this});else{var H=U||{},C=this.getGroupBase();st=new C(H)}return this.add(st),st},D.prototype.getCanvas=function(){var E;return this.isCanvas()?E=this:E=this.get("canvas"),E},D.prototype.getShape=function(E,O,U){if(!(0,it.pP)(this))return null;var nt=this.getChildren(),st;if(this.isCanvas())st=this._findShape(nt,E,O,U);else{var H=[E,O,1];H=this.invertFromMatrix(H),this.isClipped(H[0],H[1])||(st=this._findShape(nt,H[0],H[1],U))}return st},D.prototype._findShape=function(E,O,U,nt){for(var st=null,H=E.length-1;H>=0;H--){var C=E[H];if((0,it.pP)(C)&&(C.isGroup()?st=C.getShape(O,U,nt):C.isHit(O,U)&&(st=C)),st)break}return st},D.prototype.add=function(E){var O=this.getCanvas(),U=this.getChildren(),nt=this.get("timeline"),st=E.getParent();st&&z(st,E,!1),E.set("parent",this),O&&X(E,O),nt&&b(E,nt),U.push(E),E.onCanvasChange("add"),this._applyElementMatrix(E)},D.prototype._applyElementMatrix=function(E){var O=this.getTotalMatrix();O&&E.applyMatrix(O)},D.prototype.getChildren=function(){return this.get("children")||[]},D.prototype.sort=function(){var E=this.getChildren();(0,it.S6)(E,function(O,U){return O[j]=U,O}),E.sort(q(function(O,U){return O.get("zIndex")-U.get("zIndex")})),this.onCanvasChange("sort")},D.prototype.clear=function(){if(this.set("clearing",!0),!this.destroyed){for(var E=this.getChildren(),O=E.length-1;O>=0;O--)E[O].destroy();this.set("children",[]),this.onCanvasChange("clear"),this.set("clearing",!1)}},D.prototype.destroy=function(){this.get("destroyed")||(this.clear(),Y.prototype.destroy.call(this))},D.prototype.getFirst=function(){return this.getChildByIndex(0)},D.prototype.getLast=function(){var E=this.getChildren();return this.getChildByIndex(E.length-1)},D.prototype.getChildByIndex=function(E){var O=this.getChildren();return O[E]},D.prototype.getCount=function(){var E=this.getChildren();return E.length},D.prototype.contain=function(E){var O=this.getChildren();return O.indexOf(E)>-1},D.prototype.removeChild=function(E,O){O===void 0&&(O=!0),this.contain(E)&&E.remove(O)},D.prototype.findAll=function(E){var O=[],U=this.getChildren();return(0,it.S6)(U,function(nt){E(nt)&&O.push(nt),nt.isGroup()&&(O=O.concat(nt.findAll(E)))}),O},D.prototype.find=function(E){var O=null,U=this.getChildren();return(0,it.S6)(U,function(nt){if(E(nt)?O=nt:nt.isGroup()&&(O=nt.find(E)),O)return!1}),O},D.prototype.findById=function(E){return this.find(function(O){return O.get("id")===E})},D.prototype.findByClassName=function(E){return this.find(function(O){return O.get("className")===E})},D.prototype.findAllByName=function(E){return this.findAll(function(O){return O.get("name")===E})},D}(_.Z);Mt.Z=W},73866:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(98667),it=k(82867),Q=k(27127),j=k(70530),X=k(84670),b=it.vs,N="matrix",z=["zIndex","capture","visible","type"],q=["repeat"],W=":",Y="*";function D(st){for(var H=[],C=0;C<st.length;C++)(0,_.isArray)(st[C])?H.push([].concat(st[C])):H.push(st[C]);return H}function E(st,H){var C={},S=H.attrs;for(var Z in st)C[Z]=S[Z];return C}function O(st,H){var C={},S=H.attr();return(0,_.each)(st,function(Z,J){q.indexOf(J)===-1&&!(0,_.isEqual)(S[J],Z)&&(C[J]=Z)}),C}function U(st,H){if(H.onFrame)return st;var C=H.startTime,S=H.delay,Z=H.duration,J=Object.prototype.hasOwnProperty;return(0,_.each)(st,function(ht){C+S<ht.startTime+ht.delay+ht.duration&&Z>ht.delay&&(0,_.each)(H.toAttrs,function(mt,Pt){J.call(ht.toAttrs,Pt)&&(delete ht.toAttrs[Pt],delete ht.fromAttrs[Pt])})}),st}var nt=function(st){(0,V.__extends)(H,st);function H(C){var S=st.call(this,C)||this;S.attrs={};var Z=S.getDefaultAttrs();return(0,_.mix)(Z,C.attrs),S.attrs=Z,S.initAttrs(Z),S.initAnimate(),S}return H.prototype.getDefaultCfg=function(){return{visible:!0,capture:!0,zIndex:0}},H.prototype.getDefaultAttrs=function(){return{matrix:this.getDefaultMatrix(),opacity:1}},H.prototype.onCanvasChange=function(C){},H.prototype.initAttrs=function(C){},H.prototype.initAnimate=function(){this.set("animable",!0),this.set("animating",!1)},H.prototype.isGroup=function(){return!1},H.prototype.getParent=function(){return this.get("parent")},H.prototype.getCanvas=function(){return this.get("canvas")},H.prototype.attr=function(){for(var C,S=[],Z=0;Z<arguments.length;Z++)S[Z]=arguments[Z];var J=S[0],ht=S[1];if(!J)return this.attrs;if((0,_.isObject)(J)){for(var mt in J)this.setAttr(mt,J[mt]);return this.afterAttrsChange(J),this}return S.length===2?(this.setAttr(J,ht),this.afterAttrsChange((C={},C[J]=ht,C)),this):this.attrs[J]},H.prototype.isClipped=function(C,S){var Z=this.getClip();return Z&&!Z.isHit(C,S)},H.prototype.setAttr=function(C,S){var Z=this.attrs[C];Z!==S&&(this.attrs[C]=S,this.onAttrChange(C,S,Z))},H.prototype.onAttrChange=function(C,S,Z){C==="matrix"&&this.set("totalMatrix",null)},H.prototype.afterAttrsChange=function(C){if(this.cfg.isClipShape){var S=this.cfg.applyTo;S&&S.onCanvasChange("clip")}else this.onCanvasChange("attr")},H.prototype.show=function(){return this.set("visible",!0),this.onCanvasChange("show"),this},H.prototype.hide=function(){return this.set("visible",!1),this.onCanvasChange("hide"),this},H.prototype.setZIndex=function(C){this.set("zIndex",C);var S=this.getParent();return S&&S.sort(),this},H.prototype.toFront=function(){var C=this.getParent();if(C){var S=C.getChildren(),Z=this.get("el"),J=S.indexOf(this);S.splice(J,1),S.push(this),this.onCanvasChange("zIndex")}},H.prototype.toBack=function(){var C=this.getParent();if(C){var S=C.getChildren(),Z=this.get("el"),J=S.indexOf(this);S.splice(J,1),S.unshift(this),this.onCanvasChange("zIndex")}},H.prototype.remove=function(C){C===void 0&&(C=!0);var S=this.getParent();S?((0,Q.As)(S.getChildren(),this),S.get("clearing")||this.onCanvasChange("remove")):this.onCanvasChange("remove"),C&&this.destroy()},H.prototype.resetMatrix=function(){this.attr(N,this.getDefaultMatrix()),this.onCanvasChange("matrix")},H.prototype.getMatrix=function(){return this.attr(N)},H.prototype.setMatrix=function(C){this.attr(N,C),this.onCanvasChange("matrix")},H.prototype.getTotalMatrix=function(){var C=this.cfg.totalMatrix;if(!C){var S=this.attr("matrix"),Z=this.cfg.parentMatrix;Z&&S?C=(0,j.xq)(Z,S):C=S||Z,this.set("totalMatrix",C)}return C},H.prototype.applyMatrix=function(C){var S=this.attr("matrix"),Z=null;C&&S?Z=(0,j.xq)(C,S):Z=S||C,this.set("totalMatrix",Z),this.set("parentMatrix",C)},H.prototype.getDefaultMatrix=function(){return null},H.prototype.applyToMatrix=function(C){var S=this.attr("matrix");return S?(0,j.rG)(S,C):C},H.prototype.invertFromMatrix=function(C){var S=this.attr("matrix");if(S){var Z=(0,j.U_)(S);if(Z)return(0,j.rG)(Z,C)}return C},H.prototype.setClip=function(C){var S=this.getCanvas(),Z=null;if(C){var J=this.getShapeBase(),ht=(0,_.upperFirst)(C.type),mt=J[ht];mt&&(Z=new mt({type:C.type,isClipShape:!0,applyTo:this,attrs:C.attrs,canvas:S}))}return this.set("clipShape",Z),this.onCanvasChange("clip"),Z},H.prototype.getClip=function(){var C=this.cfg.clipShape;return C||null},H.prototype.clone=function(){var C=this,S=this.attrs,Z={};(0,_.each)(S,function(mt,Pt){(0,_.isArray)(S[Pt])?Z[Pt]=D(S[Pt]):Z[Pt]=S[Pt]});var J=this.constructor,ht=new J({attrs:Z});return(0,_.each)(z,function(mt){ht.set(mt,C.get(mt))}),ht},H.prototype.destroy=function(){var C=this.destroyed;C||(this.attrs={},st.prototype.destroy.call(this))},H.prototype.isAnimatePaused=function(){return this.get("_pause").isPaused},H.prototype.animate=function(){for(var C=[],S=0;S<arguments.length;S++)C[S]=arguments[S];if(!(!this.get("timeline")&&!this.get("canvas"))){this.set("animating",!0);var Z=this.get("timeline");Z||(Z=this.get("canvas").get("timeline"),this.set("timeline",Z));var J=this.get("animations")||[];Z.timer||Z.initTimer();var ht=C[0],mt=C[1],Pt=C[2],Yt=Pt===void 0?"easeLinear":Pt,Nt=C[3],dt=Nt===void 0?_.noop:Nt,pt=C[4],xt=pt===void 0?0:pt,Et,wt,c,A,G;(0,_.isFunction)(ht)?(Et=ht,ht={}):(0,_.isObject)(ht)&&ht.onFrame&&(Et=ht.onFrame,wt=ht.repeat),(0,_.isObject)(mt)?(G=mt,mt=G.duration,Yt=G.easing||"easeLinear",xt=G.delay||0,wt=G.repeat||wt||!1,dt=G.callback||_.noop,c=G.pauseCallback||_.noop,A=G.resumeCallback||_.noop):((0,_.isNumber)(dt)&&(xt=dt,dt=null),(0,_.isFunction)(Yt)?(dt=Yt,Yt="easeLinear"):Yt=Yt||"easeLinear");var vt=O(ht,this),yt={fromAttrs:E(vt,this),toAttrs:vt,duration:mt,easing:Yt,repeat:wt,callback:dt,pauseCallback:c,resumeCallback:A,delay:xt,startTime:Z.getTime(),id:(0,_.uniqueId)(),onFrame:Et,pathFormatted:!1};J.length>0?J=U(J,yt):Z.addAnimator(this),J.push(yt),this.set("animations",J),this.set("_pause",{isPaused:!1})}},H.prototype.stopAnimate=function(C){var S=this;C===void 0&&(C=!0);var Z=this.get("animations");(0,_.each)(Z,function(J){C&&(J.onFrame?S.attr(J.onFrame(1)):S.attr(J.toAttrs)),J.callback&&J.callback()}),this.set("animating",!1),this.set("animations",[])},H.prototype.pauseAnimate=function(){var C=this.get("timeline"),S=this.get("animations"),Z=C.getTime();return(0,_.each)(S,function(J){J._paused=!0,J._pauseTime=Z,J.pauseCallback&&J.pauseCallback()}),this.set("_pause",{isPaused:!0,pauseTime:Z}),this},H.prototype.resumeAnimate=function(){var C=this.get("timeline"),S=C.getTime(),Z=this.get("animations"),J=this.get("_pause").pauseTime;return(0,_.each)(Z,function(ht){ht.startTime=ht.startTime+(S-J),ht._paused=!1,ht._pauseTime=null,ht.resumeCallback&&ht.resumeCallback()}),this.set("_pause",{isPaused:!1}),this.set("animations",Z),this},H.prototype.emitDelegation=function(C,S){var Z=this,J=S.propagationPath,ht=this.getEvents(),mt;C==="mouseenter"?mt=S.fromShape:C==="mouseleave"&&(mt=S.toShape);for(var Pt=function(pt){var xt=J[pt],Et=xt.get("name");if(Et){if((xt.isGroup()||xt.isCanvas&&xt.isCanvas())&&mt&&(0,Q.UY)(xt,mt))return"break";(0,_.isArray)(Et)?(0,_.each)(Et,function(wt){Z.emitDelegateEvent(xt,wt,S)}):Yt.emitDelegateEvent(xt,Et,S)}},Yt=this,Nt=0;Nt<J.length;Nt++){var dt=Pt(Nt);if(dt==="break")break}},H.prototype.emitDelegateEvent=function(C,S,Z){var J=this.getEvents(),ht=S+W+Z.type;(J[ht]||J[Y])&&(Z.name=ht,Z.currentTarget=C,Z.delegateTarget=this,Z.delegateObject=C.get("delegateObject"),this.emit(ht,Z))},H.prototype.translate=function(C,S){C===void 0&&(C=0),S===void 0&&(S=0);var Z=this.getMatrix(),J=b(Z,[["t",C,S]]);return this.setMatrix(J),this},H.prototype.move=function(C,S){var Z=this.attr("x")||0,J=this.attr("y")||0;return this.translate(C-Z,S-J),this},H.prototype.moveTo=function(C,S){return this.move(C,S)},H.prototype.scale=function(C,S){var Z=this.getMatrix(),J=b(Z,[["s",C,S||C]]);return this.setMatrix(J),this},H.prototype.rotate=function(C){var S=this.getMatrix(),Z=b(S,[["r",C]]);return this.setMatrix(Z),this},H.prototype.rotateAtStart=function(C){var S=this.attr(),Z=S.x,J=S.y,ht=this.getMatrix(),mt=b(ht,[["t",-Z,-J],["r",C],["t",Z,J]]);return this.setMatrix(mt),this},H.prototype.rotateAtPoint=function(C,S,Z){var J=this.getMatrix(),ht=b(J,[["t",-C,-S],["r",Z],["t",C,S]]);return this.setMatrix(ht),this},H}(X.Z);Mt.Z=nt},71876:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(44761),it=function(Q){(0,V.__extends)(j,Q);function j(){return Q!==null&&Q.apply(this,arguments)||this}return j.prototype.isGroup=function(){return!0},j.prototype.isEntityGroup=function(){return!1},j.prototype.clone=function(){for(var X=Q.prototype.clone.call(this),b=this.getChildren(),N=0;N<b.length;N++){var z=b[N];X.add(z.clone())}return X},j}(_.Z);Mt.Z=it},22482:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(73866),it=k(70530),Q=function(j){(0,V.__extends)(X,j);function X(b){return j.call(this,b)||this}return X.prototype._isInBBox=function(b,N){var z=this.getBBox();return z.minX<=b&&z.maxX>=b&&z.minY<=N&&z.maxY>=N},X.prototype.afterAttrsChange=function(b){j.prototype.afterAttrsChange.call(this,b),this.clearCacheBBox()},X.prototype.getBBox=function(){var b=this.cfg.bbox;return b||(b=this.calculateBBox(),this.set("bbox",b)),b},X.prototype.getCanvasBBox=function(){var b=this.cfg.canvasBBox;return b||(b=this.calculateCanvasBBox(),this.set("canvasBBox",b)),b},X.prototype.applyMatrix=function(b){j.prototype.applyMatrix.call(this,b),this.set("canvasBBox",null)},X.prototype.calculateCanvasBBox=function(){var b=this.getBBox(),N=this.getTotalMatrix(),z=b.minX,q=b.minY,W=b.maxX,Y=b.maxY;if(N){var D=(0,it.rG)(N,[b.minX,b.minY]),E=(0,it.rG)(N,[b.maxX,b.minY]),O=(0,it.rG)(N,[b.minX,b.maxY]),U=(0,it.rG)(N,[b.maxX,b.maxY]);z=Math.min(D[0],E[0],O[0],U[0]),W=Math.max(D[0],E[0],O[0],U[0]),q=Math.min(D[1],E[1],O[1],U[1]),Y=Math.max(D[1],E[1],O[1],U[1])}var nt=this.attrs;if(nt.shadowColor){var st=nt.shadowBlur,H=st===void 0?0:st,C=nt.shadowOffsetX,S=C===void 0?0:C,Z=nt.shadowOffsetY,J=Z===void 0?0:Z,ht=z-H+S,mt=W+H+S,Pt=q-H+J,Yt=Y+H+J;z=Math.min(z,ht),W=Math.max(W,mt),q=Math.min(q,Pt),Y=Math.max(Y,Yt)}return{x:z,y:q,minX:z,minY:q,maxX:W,maxY:Y,width:W-z,height:Y-q}},X.prototype.clearCacheBBox=function(){this.set("bbox",null),this.set("canvasBBox",null)},X.prototype.isClipShape=function(){return this.get("isClipShape")},X.prototype.isInShape=function(b,N){return!1},X.prototype.isOnlyHitBox=function(){return!1},X.prototype.isHit=function(b,N){var z=this.get("startArrowShape"),q=this.get("endArrowShape"),W=[b,N,1];W=this.invertFromMatrix(W);var Y=W[0],D=W[1],E=this._isInBBox(Y,D);return this.isOnlyHitBox()?E:!!(E&&!this.isClipped(Y,D)&&(this.isInShape(Y,D)||z&&z.isHit(Y,D)||q&&q.isHit(Y,D)))},X}(_.Z);Mt.Z=Q},36278:function(Lt,Mt,k){"use strict";k.d(Mt,{_:function(){return f},C:function(){return n}});var V={};k.r(V),k.d(V,{easeBack:function(){return L},easeBackIn:function(){return p},easeBackInOut:function(){return L},easeBackOut:function(){return M},easeBounce:function(){return G},easeBounceIn:function(){return A},easeBounceInOut:function(){return vt},easeBounceOut:function(){return G},easeCircle:function(){return ht},easeCircleIn:function(){return Z},easeCircleInOut:function(){return ht},easeCircleOut:function(){return J},easeCubic:function(){return N},easeCubicIn:function(){return X},easeCubicInOut:function(){return N},easeCubicOut:function(){return b},easeElastic:function(){return g},easeElasticIn:function(){return P},easeElasticInOut:function(){return r},easeElasticOut:function(){return g},easeExp:function(){return S},easeExpIn:function(){return H},easeExpInOut:function(){return S},easeExpOut:function(){return C},easeLinear:function(){return _},easePoly:function(){return Y},easePolyIn:function(){return q},easePolyInOut:function(){return Y},easePolyOut:function(){return W},easeQuad:function(){return j},easeQuadIn:function(){return it},easeQuadInOut:function(){return j},easeQuadOut:function(){return Q},easeSin:function(){return nt},easeSinIn:function(){return O},easeSinInOut:function(){return nt},easeSinOut:function(){return U}});function _(a){return+a}function it(a){return a*a}function Q(a){return a*(2-a)}function j(a){return((a*=2)<=1?a*a:--a*(2-a)+1)/2}function X(a){return a*a*a}function b(a){return--a*a*a+1}function N(a){return((a*=2)<=1?a*a*a:(a-=2)*a*a+2)/2}var z=3,q=function a(s){s=+s;function e(u){return Math.pow(u,s)}return e.exponent=a,e}(z),W=function a(s){s=+s;function e(u){return 1-Math.pow(1-u,s)}return e.exponent=a,e}(z),Y=function a(s){s=+s;function e(u){return((u*=2)<=1?Math.pow(u,s):2-Math.pow(2-u,s))/2}return e.exponent=a,e}(z),D=Math.PI,E=D/2;function O(a){return+a==1?1:1-Math.cos(a*E)}function U(a){return Math.sin(a*E)}function nt(a){return(1-Math.cos(D*a))/2}function st(a){return(Math.pow(2,-10*a)-.0009765625)*1.0009775171065494}function H(a){return st(1-+a)}function C(a){return 1-st(a)}function S(a){return((a*=2)<=1?st(1-a):2-st(a-1))/2}function Z(a){return 1-Math.sqrt(1-a*a)}function J(a){return Math.sqrt(1- --a*a)}function ht(a){return((a*=2)<=1?1-Math.sqrt(1-a*a):Math.sqrt(1-(a-=2)*a)+1)/2}var mt=4/11,Pt=6/11,Yt=8/11,Nt=3/4,dt=9/11,pt=10/11,xt=15/16,Et=21/22,wt=63/64,c=1/mt/mt;function A(a){return 1-G(1-a)}function G(a){return(a=+a)<mt?c*a*a:a<Yt?c*(a-=Pt)*a+Nt:a<pt?c*(a-=dt)*a+xt:c*(a-=Et)*a+wt}function vt(a){return((a*=2)<=1?1-G(1-a):G(a-1)+1)/2}var yt=1.70158,p=function a(s){s=+s;function e(u){return(u=+u)*u*(s*(u-1)+u)}return e.overshoot=a,e}(yt),M=function a(s){s=+s;function e(u){return--u*u*((u+1)*s+u)+1}return e.overshoot=a,e}(yt),L=function a(s){s=+s;function e(u){return((u*=2)<1?u*u*((s+1)*u-s):(u-=2)*u*((s+1)*u+s)+2)/2}return e.overshoot=a,e}(yt),T=2*Math.PI,$=1,ut=.3,P=function a(s,e){var u=Math.asin(1/(s=Math.max(1,s)))*(e/=T);function l(v){return s*st(- --v)*Math.sin((u-v)/e)}return l.amplitude=function(v){return a(v,e*T)},l.period=function(v){return a(s,v)},l}($,ut),g=function a(s,e){var u=Math.asin(1/(s=Math.max(1,s)))*(e/=T);function l(v){return 1-s*st(v=+v)*Math.sin((v+u)/e)}return l.amplitude=function(v){return a(v,e*T)},l.period=function(v){return a(s,v)},l}($,ut),r=function a(s,e){var u=Math.asin(1/(s=Math.max(1,s)))*(e/=T);function l(v){return((v=v*2-1)<0?s*st(-v)*Math.sin((u-v)/e):2-s*st(v)*Math.sin((u+v)/e))/2}return l.amplitude=function(v){return a(v,e*T)},l.period=function(v){return a(s,v)},l}($,ut),t={};function f(a){return t[a.toLowerCase()]||V[a]}function n(a,s){t[a.toLowerCase()]=s}},24790:function(Lt,Mt,k){"use strict";k.d(Mt,{b:function(){return it},W:function(){return _}});var V=new Map;function _(C,S){V.set(C,S)}function it(C){return V.get(C)}function Q(C){var S=C.attr(),Z=S.x,J=S.y,ht=S.width,mt=S.height;return{x:Z,y:J,width:ht,height:mt}}function j(C){var S=C.attr(),Z=S.x,J=S.y,ht=S.r;return{x:Z-ht,y:J-ht,width:ht*2,height:ht*2}}var X=k(22824);function b(C,S){return!C||!S?C||S:{minX:Math.min(C.minX,S.minX),minY:Math.min(C.minY,S.minY),maxX:Math.max(C.maxX,S.maxX),maxY:Math.max(C.maxY,S.maxY)}}function N(C,S){var Z=C.get("startArrowShape"),J=C.get("endArrowShape"),ht=null,mt=null;return Z&&(ht=Z.getCanvasBBox(),S=b(S,ht)),J&&(mt=J.getCanvasBBox(),S=b(S,mt)),S}function z(C){for(var S=C.attr(),Z=S.points,J=[],ht=[],mt=0;mt<Z.length;mt++){var Pt=Z[mt];J.push(Pt[0]),ht.push(Pt[1])}var Yt=X.Zr.getBBoxByArray(J,ht),Nt=Yt.x,dt=Yt.y,pt=Yt.width,xt=Yt.height,Et={minX:Nt,minY:dt,maxX:Nt+pt,maxY:dt+xt};return Et=N(C,Et),{x:Et.minX,y:Et.minY,width:Et.maxX-Et.minX,height:Et.maxY-Et.minY}}function q(C){for(var S=C.attr(),Z=S.points,J=[],ht=[],mt=0;mt<Z.length;mt++){var Pt=Z[mt];J.push(Pt[0]),ht.push(Pt[1])}return X.Zr.getBBoxByArray(J,ht)}var W=k(89931);function Y(C){var S=C.attr(),Z=S.x,J=S.y,ht=S.text,mt=S.fontSize,Pt=S.lineHeight,Yt=S.font;Yt||(Yt=(0,W.$O)(S));var Nt=(0,W.mY)(ht,Yt),dt;if(!Nt)dt={x:Z,y:J,width:0,height:0};else{var pt=S.textAlign,xt=S.textBaseline,Et=(0,W.FE)(ht,mt,Pt),wt={x:Z,y:J-Et};pt&&(pt==="end"||pt==="right"?wt.x-=Nt:pt==="center"&&(wt.x-=Nt/2)),xt&&(xt==="top"?wt.y+=Et:xt==="middle"&&(wt.y+=Et/2)),dt={x:wt.x,y:wt.y,width:Nt,height:Et}}return dt}var D=k(72461),E=k(98667);function O(C,S){for(var Z=[],J=[],ht=[],mt=0;mt<C.length;mt++){var Pt=C[mt],Yt=Pt.currentPoint,Nt=Pt.params,dt=Pt.prePoint,pt=void 0;switch(Pt.command){case"Q":pt=X.lD.box(dt[0],dt[1],Nt[1],Nt[2],Nt[3],Nt[4]);break;case"C":pt=X.Ll.box(dt[0],dt[1],Nt[1],Nt[2],Nt[3],Nt[4],Nt[5],Nt[6]);break;case"A":var xt=Pt.arcParams;pt=X.wN.box(xt.cx,xt.cy,xt.rx,xt.ry,xt.xRotation,xt.startAngle,xt.endAngle);break;default:Z.push(Yt[0]),J.push(Yt[1]);break}pt&&(Pt.box=pt,Z.push(pt.x,pt.x+pt.width),J.push(pt.y,pt.y+pt.height)),S&&(Pt.command==="L"||Pt.command==="M")&&Pt.prePoint&&Pt.nextPoint&&ht.push(Pt)}Z=Z.filter(function(vt){return!Number.isNaN(vt)&&vt!==1/0&&vt!==-1/0}),J=J.filter(function(vt){return!Number.isNaN(vt)&&vt!==1/0&&vt!==-1/0});var Et=(0,E.min)(Z),wt=(0,E.min)(J),c=(0,E.max)(Z),A=(0,E.max)(J);if(ht.length===0)return{x:Et,y:wt,width:c-Et,height:A-wt};for(var mt=0;mt<ht.length;mt++){var Pt=ht[mt],Yt=Pt.currentPoint,G=void 0;Yt[0]===Et?(G=U(Pt,S),Et=Et-G.xExtra):Yt[0]===c&&(G=U(Pt,S),c=c+G.xExtra),Yt[1]===wt?(G=U(Pt,S),wt=wt-G.yExtra):Yt[1]===A&&(G=U(Pt,S),A=A+G.yExtra)}return{x:Et,y:wt,width:c-Et,height:A-wt}}function U(C,S){var Z=C.prePoint,J=C.currentPoint,ht=C.nextPoint,mt=Math.pow(J[0]-Z[0],2)+Math.pow(J[1]-Z[1],2),Pt=Math.pow(J[0]-ht[0],2)+Math.pow(J[1]-ht[1],2),Yt=Math.pow(Z[0]-ht[0],2)+Math.pow(Z[1]-ht[1],2),Nt=Math.acos((mt+Pt-Yt)/(2*Math.sqrt(mt)*Math.sqrt(Pt)));if(!Nt||Math.sin(Nt)===0||(0,E.isNumberEqual)(Nt,0))return{xExtra:0,yExtra:0};var dt=Math.abs(Math.atan2(ht[1]-J[1],ht[0]-J[0])),pt=Math.abs(Math.atan2(ht[0]-J[0],ht[1]-J[1]));dt=dt>Math.PI/2?Math.PI-dt:dt,pt=pt>Math.PI/2?Math.PI-pt:pt;var xt={xExtra:Math.cos(Nt/2-dt)*(S/2*(1/Math.sin(Nt/2)))-S/2||0,yExtra:Math.cos(pt-Nt/2)*(S/2*(1/Math.sin(Nt/2)))-S/2||0};return xt}function nt(C){var S=C.attr(),Z=S.path,J=S.stroke,ht=J?S.lineWidth:0,mt=C.get("segments")||(0,D.zx)(Z),Pt=O(mt,ht),Yt=Pt.x,Nt=Pt.y,dt=Pt.width,pt=Pt.height,xt={minX:Yt,minY:Nt,maxX:Yt+dt,maxY:Nt+pt};return xt=N(C,xt),{x:xt.minX,y:xt.minY,width:xt.maxX-xt.minX,height:xt.maxY-xt.minY}}function st(C){var S=C.attr(),Z=S.x1,J=S.y1,ht=S.x2,mt=S.y2,Pt=Math.min(Z,ht),Yt=Math.max(Z,ht),Nt=Math.min(J,mt),dt=Math.max(J,mt),pt={minX:Pt,maxX:Yt,minY:Nt,maxY:dt};return pt=N(C,pt),{x:pt.minX,y:pt.minY,width:pt.maxX-pt.minX,height:pt.maxY-pt.minY}}function H(C){var S=C.attr(),Z=S.x,J=S.y,ht=S.rx,mt=S.ry;return{x:Z-ht,y:J-mt,width:ht*2,height:mt*2}}_("rect",Q),_("image",Q),_("circle",j),_("marker",j),_("polyline",z),_("polygon",q),_("text",Y),_("path",nt),_("line",st),_("ellipse",H)},73834:function(Lt,Mt){"use strict";var k=function(){function V(_,it){this.bubbles=!0,this.target=null,this.currentTarget=null,this.delegateTarget=null,this.delegateObject=null,this.defaultPrevented=!1,this.propagationStopped=!1,this.shape=null,this.fromShape=null,this.toShape=null,this.propagationPath=[],this.type=_,this.name=_,this.originalEvent=it,this.timeStamp=it.timeStamp}return V.prototype.preventDefault=function(){this.defaultPrevented=!0,this.originalEvent.preventDefault&&this.originalEvent.preventDefault()},V.prototype.stopPropagation=function(){this.propagationStopped=!0},V.prototype.toString=function(){var _=this.type;return"[Event (type="+_+")]"},V.prototype.save=function(){},V.prototype.restore=function(){},V}();Mt.Z=k},41142:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{AbstractCanvas:function(){return q.Z},AbstractGroup:function(){return W.Z},AbstractShape:function(){return Y.Z},Base:function(){return z.Z},Event:function(){return N.Z},PathUtil:function(){return V},assembleFont:function(){return E.$O},getBBoxMethod:function(){return D.b},getOffScreenContext:function(){return nt.L},getTextHeight:function(){return E.FE},invert:function(){return U.U_},isAllowCapture:function(){return O.pP},multiplyVec2:function(){return U.rG},registerBBox:function(){return D.W},registerEasing:function(){return st.C},version:function(){return H}});var V=k(16900),_=k(65562),it=k.n(_),b={};for(var Q in _)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(Q)<0&&(b[Q]=function(C){return _[C]}.bind(0,Q));k.d(Mt,b);var j=k(7980),X=k.n(j),b={};for(var Q in j)["default","Event","Base","AbstractCanvas","AbstractGroup","AbstractShape","PathUtil","getBBoxMethod","registerBBox","getTextHeight","assembleFont","isAllowCapture","multiplyVec2","invert","getOffScreenContext","registerEasing","version"].indexOf(Q)<0&&(b[Q]=function(S){return j[S]}.bind(0,Q));k.d(Mt,b);var N=k(73834),z=k(84670),q=k(78635),W=k(71876),Y=k(22482),D=k(24790),E=k(89931),O=k(27127),U=k(70530),nt=k(94080),st=k(36278),H="0.5.11"},7980:function(){},65562:function(){},70530:function(Lt,Mt,k){"use strict";k.d(Mt,{U_:function(){return it},rG:function(){return _},xq:function(){return V}});function V(Q,j){var X=[],b=Q[0],N=Q[1],z=Q[2],q=Q[3],W=Q[4],Y=Q[5],D=Q[6],E=Q[7],O=Q[8],U=j[0],nt=j[1],st=j[2],H=j[3],C=j[4],S=j[5],Z=j[6],J=j[7],ht=j[8];return X[0]=U*b+nt*q+st*D,X[1]=U*N+nt*W+st*E,X[2]=U*z+nt*Y+st*O,X[3]=H*b+C*q+S*D,X[4]=H*N+C*W+S*E,X[5]=H*z+C*Y+S*O,X[6]=Z*b+J*q+ht*D,X[7]=Z*N+J*W+ht*E,X[8]=Z*z+J*Y+ht*O,X}function _(Q,j){var X=[],b=j[0],N=j[1];return X[0]=Q[0]*b+Q[3]*N+Q[6],X[1]=Q[1]*b+Q[4]*N+Q[7],X}function it(Q){var j=[],X=Q[0],b=Q[1],N=Q[2],z=Q[3],q=Q[4],W=Q[5],Y=Q[6],D=Q[7],E=Q[8],O=E*q-W*D,U=-E*z+W*Y,nt=D*z-q*Y,st=X*O+b*U+N*nt;return st?(st=1/st,j[0]=O*st,j[1]=(-E*b+N*D)*st,j[2]=(W*b-N*q)*st,j[3]=U*st,j[4]=(E*X-N*Y)*st,j[5]=(-W*X+N*z)*st,j[6]=nt*st,j[7]=(-D*X+b*Y)*st,j[8]=(q*X-b*z)*st,j):null}},94080:function(Lt,Mt,k){"use strict";k.d(Mt,{L:function(){return _}});var V=null;function _(){if(!V){var it=document.createElement("canvas");it.width=1,it.height=1,V=it.getContext("2d")}return V}},16900:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{catmullRomToBezier:function(){return X},fillPath:function(){return xt},fillPathByDiff:function(){return A},formatPath:function(){return yt},intersection:function(){return Yt},parsePathArray:function(){return E},parsePathString:function(){return j},pathToAbsolute:function(){return N},pathToCurve:function(){return Y},rectPath:function(){return C}});var V=k(98667),_=`	
\v\f\r \xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029`,it=new RegExp("([a-z])["+_+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+_+"]*,?["+_+"]*)+)","ig"),Q=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+_+"]*,?["+_+"]*","ig"),j=function(p){if(!p)return null;if((0,V.isArray)(p))return p;var M={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},L=[];return String(p).replace(it,function(T,$,ut){var P=[],g=$.toLowerCase();if(ut.replace(Q,function(r,t){t&&P.push(+t)}),g==="m"&&P.length>2&&(L.push([$].concat(P.splice(0,2))),g="l",$=$==="m"?"l":"L"),g==="o"&&P.length===1&&L.push([$,P[0]]),g==="r")L.push([$].concat(P));else for(;P.length>=M[g]&&(L.push([$].concat(P.splice(0,M[g]))),!!M[g]););return p}),L},X=function(p,M){for(var L=[],T=0,$=p.length;$-2*!M>T;T+=2){var ut=[{x:+p[T-2],y:+p[T-1]},{x:+p[T],y:+p[T+1]},{x:+p[T+2],y:+p[T+3]},{x:+p[T+4],y:+p[T+5]}];M?T?$-4===T?ut[3]={x:+p[0],y:+p[1]}:$-2===T&&(ut[2]={x:+p[0],y:+p[1]},ut[3]={x:+p[2],y:+p[3]}):ut[0]={x:+p[$-2],y:+p[$-1]}:$-4===T?ut[3]=ut[2]:T||(ut[0]={x:+p[T],y:+p[T+1]}),L.push(["C",(-ut[0].x+6*ut[1].x+ut[2].x)/6,(-ut[0].y+6*ut[1].y+ut[2].y)/6,(ut[1].x+6*ut[2].x-ut[3].x)/6,(ut[1].y+6*ut[2].y-ut[3].y)/6,ut[2].x,ut[2].y])}return L},b=function(p,M,L,T,$){var ut=[];if($===null&&T===null&&(T=L),p=+p,M=+M,L=+L,T=+T,$!==null){var P=Math.PI/180,g=p+L*Math.cos(-T*P),r=p+L*Math.cos(-$*P),t=M+L*Math.sin(-T*P),f=M+L*Math.sin(-$*P);ut=[["M",g,t],["A",L,L,0,+($-T>180),0,r,f]]}else ut=[["M",p,M],["m",0,-T],["a",L,T,0,1,1,0,2*T],["a",L,T,0,1,1,0,-2*T],["z"]];return ut},N=function(p){if(p=j(p),!p||!p.length)return[["M",0,0]];var M=[],L=0,T=0,$=0,ut=0,P=0,g,r;p[0][0]==="M"&&(L=+p[0][1],T=+p[0][2],$=L,ut=T,P++,M[0]=["M",L,T]);for(var t=p.length===3&&p[0][0]==="M"&&p[1][0].toUpperCase()==="R"&&p[2][0].toUpperCase()==="Z",f=void 0,n=void 0,a=P,s=p.length;a<s;a++){if(M.push(f=[]),n=p[a],g=n[0],g!==g.toUpperCase())switch(f[0]=g.toUpperCase(),f[0]){case"A":f[1]=n[1],f[2]=n[2],f[3]=n[3],f[4]=n[4],f[5]=n[5],f[6]=+n[6]+L,f[7]=+n[7]+T;break;case"V":f[1]=+n[1]+T;break;case"H":f[1]=+n[1]+L;break;case"R":r=[L,T].concat(n.slice(1));for(var e=2,u=r.length;e<u;e++)r[e]=+r[e]+L,r[++e]=+r[e]+T;M.pop(),M=M.concat(X(r,t));break;case"O":M.pop(),r=b(L,T,n[1],n[2]),r.push(r[0]),M=M.concat(r);break;case"U":M.pop(),M=M.concat(b(L,T,n[1],n[2],n[3])),f=["U"].concat(M[M.length-1].slice(-2));break;case"M":$=+n[1]+L,ut=+n[2]+T;break;default:for(var e=1,u=n.length;e<u;e++)f[e]=+n[e]+(e%2?L:T)}else if(g==="R")r=[L,T].concat(n.slice(1)),M.pop(),M=M.concat(X(r,t)),f=["R"].concat(n.slice(-2));else if(g==="O")M.pop(),r=b(L,T,n[1],n[2]),r.push(r[0]),M=M.concat(r);else if(g==="U")M.pop(),M=M.concat(b(L,T,n[1],n[2],n[3])),f=["U"].concat(M[M.length-1].slice(-2));else for(var l=0,v=n.length;l<v;l++)f[l]=n[l];if(g=g.toUpperCase(),g!=="O")switch(f[0]){case"Z":L=+$,T=+ut;break;case"H":L=f[1];break;case"V":T=f[1];break;case"M":$=f[f.length-2],ut=f[f.length-1];break;default:L=f[f.length-2],T=f[f.length-1]}}return M},z=function(p,M,L,T){return[p,M,L,T,L,T]},q=function(p,M,L,T,$,ut){var P=.3333333333333333,g=2/3;return[P*p+g*L,P*M+g*T,P*$+g*L,P*ut+g*T,$,ut]},W=function(p,M,L,T,$,ut,P,g,r,t){L===T&&(L+=1);var f=Math.PI*120/180,n=Math.PI/180*(+$||0),a=[],s,e,u,l,v,i=function(Vt,Qt,tr){var ur=Vt*Math.cos(tr)-Qt*Math.sin(tr),er=Vt*Math.sin(tr)+Qt*Math.cos(tr);return{x:ur,y:er}};if(t)e=t[0],u=t[1],l=t[2],v=t[3];else{s=i(p,M,-n),p=s.x,M=s.y,s=i(g,r,-n),g=s.x,r=s.y,p===g&&M===r&&(g+=1,r+=1);var y=(p-g)/2,x=(M-r)/2,o=y*y/(L*L)+x*x/(T*T);o>1&&(o=Math.sqrt(o),L=o*L,T=o*T);var h=L*L,d=T*T,B=(ut===P?-1:1)*Math.sqrt(Math.abs((h*d-h*x*x-d*y*y)/(h*x*x+d*y*y)));l=B*L*x/T+(p+g)/2,v=B*-T*y/L+(M+r)/2,e=Math.asin(((M-v)/T).toFixed(9)),u=Math.asin(((r-v)/T).toFixed(9)),e=p<l?Math.PI-e:e,u=g<l?Math.PI-u:u,e<0&&(e=Math.PI*2+e),u<0&&(u=Math.PI*2+u),P&&e>u&&(e=e-Math.PI*2),!P&&u>e&&(u=u-Math.PI*2)}var K=u-e;if(Math.abs(K)>f){var tt=u,et=g,ft=r;u=e+f*(P&&u>e?1:-1),g=l+L*Math.cos(u),r=v+T*Math.sin(u),a=W(g,r,L,T,$,0,P,et,ft,[u,tt,l,v])}K=u-e;var ct=Math.cos(e),_t=Math.sin(e),At=Math.cos(u),It=Math.sin(u),Xt=Math.tan(K/4),St=4/3*L*Xt,kt=4/3*T*Xt,Rt=[p,M],Wt=[p+St*_t,M-kt*ct],Ot=[g+St*It,r-kt*At],Ft=[g,r];if(Wt[0]=2*Rt[0]-Wt[0],Wt[1]=2*Rt[1]-Wt[1],t)return[Wt,Ot,Ft].concat(a);a=[Wt,Ot,Ft].concat(a).join().split(",");for(var Kt=[],Zt=0,Gt=a.length;Zt<Gt;Zt++)Kt[Zt]=Zt%2?i(a[Zt-1],a[Zt],n).y:i(a[Zt],a[Zt+1],n).x;return Kt},Y=function(p,M){var L=N(p),T=M&&N(M),$={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},ut={x:0,y:0,bx:0,by:0,X:0,Y:0,qx:null,qy:null},P=[],g=[],r="",t="",f,n=function(y,x,o){var h,d;if(!y)return["C",x.x,x.y,x.x,x.y,x.x,x.y];switch(!(y[0]in{T:1,Q:1})&&(x.qx=x.qy=null),y[0]){case"M":x.X=y[1],x.Y=y[2];break;case"A":y=["C"].concat(W.apply(0,[x.x,x.y].concat(y.slice(1))));break;case"S":o==="C"||o==="S"?(h=x.x*2-x.bx,d=x.y*2-x.by):(h=x.x,d=x.y),y=["C",h,d].concat(y.slice(1));break;case"T":o==="Q"||o==="T"?(x.qx=x.x*2-x.qx,x.qy=x.y*2-x.qy):(x.qx=x.x,x.qy=x.y),y=["C"].concat(q(x.x,x.y,x.qx,x.qy,y[1],y[2]));break;case"Q":x.qx=y[1],x.qy=y[2],y=["C"].concat(q(x.x,x.y,y[1],y[2],y[3],y[4]));break;case"L":y=["C"].concat(z(x.x,x.y,y[1],y[2]));break;case"H":y=["C"].concat(z(x.x,x.y,y[1],x.y));break;case"V":y=["C"].concat(z(x.x,x.y,x.x,y[1]));break;case"Z":y=["C"].concat(z(x.x,x.y,x.X,x.Y));break;default:break}return y},a=function(y,x){if(y[x].length>7){y[x].shift();for(var o=y[x];o.length;)P[x]="A",T&&(g[x]="A"),y.splice(x++,0,["C"].concat(o.splice(0,6)));y.splice(x,1),f=Math.max(L.length,T&&T.length||0)}},s=function(y,x,o,h,d){y&&x&&y[d][0]==="M"&&x[d][0]!=="M"&&(x.splice(d,0,["M",h.x,h.y]),o.bx=0,o.by=0,o.x=y[d][1],o.y=y[d][2],f=Math.max(L.length,T&&T.length||0))};f=Math.max(L.length,T&&T.length||0);for(var e=0;e<f;e++){L[e]&&(r=L[e][0]),r!=="C"&&(P[e]=r,e&&(t=P[e-1])),L[e]=n(L[e],$,t),P[e]!=="A"&&r==="C"&&(P[e]="C"),a(L,e),T&&(T[e]&&(r=T[e][0]),r!=="C"&&(g[e]=r,e&&(t=g[e-1])),T[e]=n(T[e],ut,t),g[e]!=="A"&&r==="C"&&(g[e]="C"),a(T,e)),s(L,T,$,ut,e),s(T,L,ut,$,e);var u=L[e],l=T&&T[e],v=u.length,i=T&&l.length;$.x=u[v-2],$.y=u[v-1],$.bx=parseFloat(u[v-4])||$.x,$.by=parseFloat(u[v-3])||$.y,ut.bx=T&&(parseFloat(l[i-4])||ut.x),ut.by=T&&(parseFloat(l[i-3])||ut.y),ut.x=T&&l[i-2],ut.y=T&&l[i-1]}return T?[L,T]:L},D=/,?([a-z]),?/gi,E=function(p){return p.join(",").replace(D,"$1")},O=function(p,M,L,T,$){var ut=-3*M+9*L-9*T+3*$,P=p*ut+6*M-12*L+6*T;return p*P-3*M+3*L},U=function(p,M,L,T,$,ut,P,g,r){r===null&&(r=1),r=r>1?1:r<0?0:r;for(var t=r/2,f=12,n=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],a=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],s=0,e=0;e<f;e++){var u=t*n[e]+t,l=O(u,p,L,$,P),v=O(u,M,T,ut,g),i=l*l+v*v;s+=a[e]*Math.sqrt(i)}return t*s},nt=function(p,M,L,T,$,ut,P,g){for(var r=[],t=[[],[]],f,n,a,s,e=0;e<2;++e){if(e===0?(n=6*p-12*L+6*$,f=-3*p+9*L-9*$+3*P,a=3*L-3*p):(n=6*M-12*T+6*ut,f=-3*M+9*T-9*ut+3*g,a=3*T-3*M),Math.abs(f)<1e-12){if(Math.abs(n)<1e-12)continue;s=-a/n,s>0&&s<1&&r.push(s);continue}var u=n*n-4*a*f,l=Math.sqrt(u);if(!(u<0)){var v=(-n+l)/(2*f);v>0&&v<1&&r.push(v);var i=(-n-l)/(2*f);i>0&&i<1&&r.push(i)}}for(var y=r.length,x=y,o;y--;)s=r[y],o=1-s,t[0][y]=o*o*o*p+3*o*o*s*L+3*o*s*s*$+s*s*s*P,t[1][y]=o*o*o*M+3*o*o*s*T+3*o*s*s*ut+s*s*s*g;return t[0][x]=p,t[1][x]=M,t[0][x+1]=P,t[1][x+1]=g,t[0].length=t[1].length=x+2,{min:{x:Math.min.apply(0,t[0]),y:Math.min.apply(0,t[1])},max:{x:Math.max.apply(0,t[0]),y:Math.max.apply(0,t[1])}}},st=function(p,M,L,T,$,ut,P,g){if(!(Math.max(p,L)<Math.min($,P)||Math.min(p,L)>Math.max($,P)||Math.max(M,T)<Math.min(ut,g)||Math.min(M,T)>Math.max(ut,g))){var r=(p*T-M*L)*($-P)-(p-L)*($*g-ut*P),t=(p*T-M*L)*(ut-g)-(M-T)*($*g-ut*P),f=(p-L)*(ut-g)-(M-T)*($-P);if(f){var n=r/f,a=t/f,s=+n.toFixed(2),e=+a.toFixed(2);if(!(s<+Math.min(p,L).toFixed(2)||s>+Math.max(p,L).toFixed(2)||s<+Math.min($,P).toFixed(2)||s>+Math.max($,P).toFixed(2)||e<+Math.min(M,T).toFixed(2)||e>+Math.max(M,T).toFixed(2)||e<+Math.min(ut,g).toFixed(2)||e>+Math.max(ut,g).toFixed(2)))return{x:n,y:a}}}},H=function(p,M,L){return M>=p.x&&M<=p.x+p.width&&L>=p.y&&L<=p.y+p.height},C=function(p,M,L,T,$){if($)return[["M",+p+ +$,M],["l",L-$*2,0],["a",$,$,0,0,1,$,$],["l",0,T-$*2],["a",$,$,0,0,1,-$,$],["l",$*2-L,0],["a",$,$,0,0,1,-$,-$],["l",0,$*2-T],["a",$,$,0,0,1,$,-$],["z"]];var ut=[["M",p,M],["l",L,0],["l",0,T],["l",-L,0],["z"]];return ut.parsePathArray=E,ut},S=function(p,M,L,T){return p===null&&(p=M=L=T=0),M===null&&(M=p.y,L=p.width,T=p.height,p=p.x),{x:p,y:M,width:L,w:L,height:T,h:T,x2:p+L,y2:M+T,cx:p+L/2,cy:M+T/2,r1:Math.min(L,T)/2,r2:Math.max(L,T)/2,r0:Math.sqrt(L*L+T*T)/2,path:C(p,M,L,T),vb:[p,M,L,T].join(" ")}},Z=function(p,M){return p=S(p),M=S(M),H(M,p.x,p.y)||H(M,p.x2,p.y)||H(M,p.x,p.y2)||H(M,p.x2,p.y2)||H(p,M.x,M.y)||H(p,M.x2,M.y)||H(p,M.x,M.y2)||H(p,M.x2,M.y2)||(p.x<M.x2&&p.x>M.x||M.x<p.x2&&M.x>p.x)&&(p.y<M.y2&&p.y>M.y||M.y<p.y2&&M.y>p.y)},J=function(p,M,L,T,$,ut,P,g){(0,V.isArray)(p)||(p=[p,M,L,T,$,ut,P,g]);var r=nt.apply(null,p);return S(r.min.x,r.min.y,r.max.x-r.min.x,r.max.y-r.min.y)},ht=function(p,M,L,T,$,ut,P,g,r){var t=1-r,f=Math.pow(t,3),n=Math.pow(t,2),a=r*r,s=a*r,e=f*p+n*3*r*L+t*3*r*r*$+s*P,u=f*M+n*3*r*T+t*3*r*r*ut+s*g,l=p+2*r*(L-p)+a*($-2*L+p),v=M+2*r*(T-M)+a*(ut-2*T+M),i=L+2*r*($-L)+a*(P-2*$+L),y=T+2*r*(ut-T)+a*(g-2*ut+T),x=t*p+r*L,o=t*M+r*T,h=t*$+r*P,d=t*ut+r*g,B=90-Math.atan2(l-i,v-y)*180/Math.PI;return{x:e,y:u,m:{x:l,y:v},n:{x:i,y},start:{x,y:o},end:{x:h,y:d},alpha:B}},mt=function(p,M,L){var T=J(p),$=J(M);if(!Z(T,$))return L?0:[];for(var ut=U.apply(0,p),P=U.apply(0,M),g=~~(ut/8),r=~~(P/8),t=[],f=[],n={},a=L?0:[],s=0;s<g+1;s++){var e=ht.apply(0,p.concat(s/g));t.push({x:e.x,y:e.y,t:s/g})}for(var s=0;s<r+1;s++){var e=ht.apply(0,M.concat(s/r));f.push({x:e.x,y:e.y,t:s/r})}for(var s=0;s<g;s++)for(var u=0;u<r;u++){var l=t[s],v=t[s+1],i=f[u],y=f[u+1],x=Math.abs(v.x-l.x)<.001?"y":"x",o=Math.abs(y.x-i.x)<.001?"y":"x",h=st(l.x,l.y,v.x,v.y,i.x,i.y,y.x,y.y);if(h){if(n[h.x.toFixed(4)]===h.y.toFixed(4))continue;n[h.x.toFixed(4)]=h.y.toFixed(4);var d=l.t+Math.abs((h[x]-l[x])/(v[x]-l[x]))*(v.t-l.t),B=i.t+Math.abs((h[o]-i[o])/(y[o]-i[o]))*(y.t-i.t);d>=0&&d<=1&&B>=0&&B<=1&&(L?a+=1:a.push({x:h.x,y:h.y,t1:d,t2:B}))}}return a},Pt=function(p,M,L){p=Y(p),M=Y(M);for(var T,$,ut,P,g,r,t,f,n,a,s=L?0:[],e=0,u=p.length;e<u;e++){var l=p[e];if(l[0]==="M")T=g=l[1],$=r=l[2];else{l[0]==="C"?(n=[T,$].concat(l.slice(1)),T=n[6],$=n[7]):(n=[T,$,T,$,g,r,g,r],T=g,$=r);for(var v=0,i=M.length;v<i;v++){var y=M[v];if(y[0]==="M")ut=t=y[1],P=f=y[2];else{y[0]==="C"?(a=[ut,P].concat(y.slice(1)),ut=a[6],P=a[7]):(a=[ut,P,ut,P,t,f,t,f],ut=t,P=f);var x=mt(n,a,L);if(L)s+=x;else{for(var o=0,h=x.length;o<h;o++)x[o].segment1=e,x[o].segment2=v,x[o].bez1=n,x[o].bez2=a;s=s.concat(x)}}}}}return s},Yt=function(p,M){return Pt(p,M)};function Nt(p,M){var L=[],T=[];function $(ut,P){if(ut.length===1)L.push(ut[0]),T.push(ut[0]);else{for(var g=[],r=0;r<ut.length-1;r++)r===0&&L.push(ut[0]),r===ut.length-2&&T.push(ut[r+1]),g[r]=[(1-P)*ut[r][0]+P*ut[r+1][0],(1-P)*ut[r][1]+P*ut[r+1][1]];$(g,P)}}return p.length&&$(p,M),{left:L,right:T.reverse()}}function dt(p,M,L){var T=[[p[1],p[2]]];L=L||2;var $=[];M[0]==="A"?(T.push(M[6]),T.push(M[7])):M[0]==="C"?(T.push([M[1],M[2]]),T.push([M[3],M[4]]),T.push([M[5],M[6]])):M[0]==="S"||M[0]==="Q"?(T.push([M[1],M[2]]),T.push([M[3],M[4]])):T.push([M[1],M[2]]);for(var ut=T,P=1/L,g=0;g<L-1;g++){var r=P/(1-P*g),t=Nt(ut,r);$.push(t.left),ut=t.right}$.push(ut);var f=$.map(function(n){var a=[];return n.length===4&&(a.push("C"),a=a.concat(n[2])),n.length>=3&&(n.length===3&&a.push("Q"),a=a.concat(n[1])),n.length===2&&a.push("L"),a=a.concat(n[n.length-1]),a});return f}var pt=function(p,M,L){if(L===1)return[[].concat(p)];var T=[];if(M[0]==="L"||M[0]==="C"||M[0]==="Q")T=T.concat(dt(p,M,L));else{var $=[].concat(p);$[0]==="M"&&($[0]="L");for(var ut=0;ut<=L-1;ut++)T.push($)}return T},xt=function(p,M){if(p.length===1)return p;var L=p.length-1,T=M.length-1,$=L/T,ut=[];if(p.length===1&&p[0][0]==="M"){for(var P=0;P<T-L;P++)p.push(p[0]);return p}for(var P=0;P<T;P++){var g=Math.floor($*P);ut[g]=(ut[g]||0)+1}var r=ut.reduce(function(t,f,n){return n===L?t.concat(p[L]):t.concat(pt(p[n],p[n+1],f))},[]);return r.unshift(p[0]),(M[T]==="Z"||M[T]==="z")&&r.push("Z"),r},Et=function(p,M){if(p.length!==M.length)return!1;var L=!0;return(0,V.each)(p,function(T,$){if(T!==M[$])return L=!1,!1}),L};function wt(p,M,L){var T=null,$=L;return M<$&&($=M,T="add"),p<$&&($=p,T="del"),{type:T,min:$}}var c=function(p,M){var L=p.length,T=M.length,$,ut,P=0;if(L===0||T===0)return null;for(var g=[],r=0;r<=L;r++)g[r]=[],g[r][0]={min:r};for(var t=0;t<=T;t++)g[0][t]={min:t};for(var r=1;r<=L;r++){$=p[r-1];for(var t=1;t<=T;t++){ut=M[t-1],Et($,ut)?P=0:P=1;var f=g[r-1][t].min+1,n=g[r][t-1].min+1,a=g[r-1][t-1].min+P;g[r][t]=wt(f,n,a)}}return g},A=function(p,M){var L=c(p,M),T=p.length,$=M.length,ut=[],P=1,g=1;if(L[T][$].min!==T){for(var r=1;r<=T;r++){var t=L[r][r].min;g=r;for(var f=P;f<=$;f++)L[r][f].min<t&&(t=L[r][f].min,g=f);P=g,L[r][P].type&&ut.push({index:r-1,type:L[r][P].type})}for(var r=ut.length-1;r>=0;r--)P=ut[r].index,ut[r].type==="add"?p.splice(P,0,[].concat(p[P])):p.splice(P,1)}T=p.length;var n=$-T;if(T<$)for(var r=0;r<n;r++)p[T-1][0]==="z"||p[T-1][0]==="Z"?p.splice(T-2,0,p[T-2]):p.push(p[T-1]),T+=1;return p};function G(p,M,L){for(var T=[].concat(p),$,ut=1/(L+1),P=vt(M)[0],g=1;g<=L;g++)ut*=g,$=Math.floor(p.length*ut),$===0?T.unshift([P[0]*ut+p[$][0]*(1-ut),P[1]*ut+p[$][1]*(1-ut)]):T.splice($,0,[P[0]*ut+p[$][0]*(1-ut),P[1]*ut+p[$][1]*(1-ut)]);return T}function vt(p){var M=[];switch(p[0]){case"M":M.push([p[1],p[2]]);break;case"L":M.push([p[1],p[2]]);break;case"A":M.push([p[6],p[7]]);break;case"Q":M.push([p[3],p[4]]),M.push([p[1],p[2]]);break;case"T":M.push([p[1],p[2]]);break;case"C":M.push([p[5],p[6]]),M.push([p[1],p[2]]),M.push([p[3],p[4]]);break;case"S":M.push([p[3],p[4]]),M.push([p[1],p[2]]);break;case"H":M.push([p[1],p[1]]);break;case"V":M.push([p[1],p[1]]);break;default:}return M}var yt=function(p,M){if(p.length<=1)return p;for(var L,T=0;T<M.length;T++)if(p[T][0]!==M[T][0])switch(L=vt(p[T]),M[T][0]){case"M":p[T]=["M"].concat(L[0]);break;case"L":p[T]=["L"].concat(L[0]);break;case"A":p[T]=[].concat(M[T]),p[T][6]=L[0][0],p[T][7]=L[0][1];break;case"Q":if(L.length<2)if(T>0)L=G(L,p[T-1],1);else{p[T]=M[T];break}p[T]=["Q"].concat(L.reduce(function($,ut){return $.concat(ut)},[]));break;case"T":p[T]=["T"].concat(L[0]);break;case"C":if(L.length<3)if(T>0)L=G(L,p[T-1],2);else{p[T]=M[T];break}p[T]=["C"].concat(L.reduce(function($,ut){return $.concat(ut)},[]));break;case"S":if(L.length<2)if(T>0)L=G(L,p[T-1],1);else{p[T]=M[T];break}p[T]=["S"].concat(L.reduce(function($,ut){return $.concat(ut)},[]));break;default:p[T]=M[T]}return p}},89931:function(Lt,Mt,k){"use strict";k.d(Mt,{$O:function(){return X},FE:function(){return it},mY:function(){return j}});var V=k(27127),_=k(94080);function it(b,N,z){var q=1;if((0,V.HD)(b)&&(q=b.split(`
`).length),q>1){var W=Q(N,z);return N*q+W*(q-1)}return N}function Q(b,N){return N?N-b:b*.14}function j(b,N){var z=(0,_.L)(),q=0;if((0,V.kK)(b)||b==="")return q;if(z.save(),z.font=N,(0,V.HD)(b)&&b.includes(`
`)){var W=b.split(`
`);(0,V.S6)(W,function(Y){var D=z.measureText(Y).width;q<D&&(q=D)})}else q=z.measureText(b).width;return z.restore(),q}function X(b){var N=b.fontSize,z=b.fontFamily,q=b.fontWeight,W=b.fontStyle,Y=b.fontVariant;return[W,Y,q,N+"px",z].join(" ").trim()}},27127:function(Lt,Mt,k){"use strict";k.d(Mt,{As:function(){return _},CD:function(){return V.mix},HD:function(){return V.isString},Kn:function(){return V.isObject},S6:function(){return V.each},UY:function(){return Q},jC:function(){return V.upperFirst},jU:function(){return it},kK:function(){return V.isNil},mf:function(){return V.isFunction},pP:function(){return j}});var V=k(98667);function _(X,b){var N=X.indexOf(b);N!==-1&&X.splice(N,1)}var it=typeof window!="undefined"&&typeof window.document!="undefined";function Q(X,b){if(X.isCanvas())return!0;for(var N=b.getParent(),z=!1;N;){if(N===X){z=!0;break}N=N.getParent()}return z}function j(X){return X.cfg.visible&&X.cfg.capture}},27893:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return Y}});var V=k(92336),_=k(41142);function it(D,E){if(E){var O=(0,_.invert)(E);return(0,_.multiplyVec2)(O,D)}return D}function Q(D,E,O){var U=D.getTotalMatrix();if(U){var nt=it([E,O,1],U),st=nt[0],H=nt[1];return[st,H]}return[E,O]}function j(D,E,O){if(D.isCanvas&&D.isCanvas())return!0;if(!(0,_.isAllowCapture)(D)||D.cfg.isInView===!1)return!1;if(D.cfg.clipShape){var U=Q(D,E,O),nt=U[0],st=U[1];if(D.isClipped(nt,st))return!1}var H=D.cfg.cacheCanvasBBox||D.getCanvasBBox();return E>=H.minX&&E<=H.maxX&&O>=H.minY&&O<=H.maxY}function X(D,E,O){if(!j(D,E,O))return null;for(var U=null,nt=D.getChildren(),st=nt.length,H=st-1;H>=0;H--){var C=nt[H];if(C.isGroup())U=X(C,E,O);else if(j(C,E,O)){var S=C,Z=Q(C,E,O),J=Z[0],ht=Z[1];S.isInShape(J,ht)&&(U=C)}if(U)break}return U}var b=k(2647),N=k(81402),z=k(82389),q=k(83102),W=function(D){(0,V.__extends)(E,D);function E(){return D!==null&&D.apply(this,arguments)||this}return E.prototype.getDefaultCfg=function(){var O=D.prototype.getDefaultCfg.call(this);return O.renderer="canvas",O.autoDraw=!0,O.localRefresh=!0,O.refreshElements=[],O.clipView=!0,O.quickHit=!1,O},E.prototype.onCanvasChange=function(O){(O==="attr"||O==="sort"||O==="changeSize")&&(this.set("refreshElements",[this]),this.draw())},E.prototype.getShapeBase=function(){return b},E.prototype.getGroupBase=function(){return N.Z},E.prototype.getPixelRatio=function(){var O=this.get("pixelRatio")||(0,z.mX)();return O>=1?Math.ceil(O):1},E.prototype.getViewRange=function(){return{minX:0,minY:0,maxX:this.cfg.width,maxY:this.cfg.height}},E.prototype.createDom=function(){var O=document.createElement("canvas"),U=O.getContext("2d");return this.set("context",U),O},E.prototype.setDOMSize=function(O,U){D.prototype.setDOMSize.call(this,O,U);var nt=this.get("context"),st=this.get("el"),H=this.getPixelRatio();st.width=H*O,st.height=H*U,H>1&&nt.scale(H,H)},E.prototype.clear=function(){D.prototype.clear.call(this),this._clearFrame();var O=this.get("context"),U=this.get("el");O.clearRect(0,0,U.width,U.height)},E.prototype.getShape=function(O,U){var nt;return this.get("quickHit")?nt=X(this,O,U):nt=D.prototype.getShape.call(this,O,U,null),nt},E.prototype._getRefreshRegion=function(){var O=this.get("refreshElements"),U=this.getViewRange(),nt;if(O.length&&O[0]===this)nt=U;else if(nt=(0,q.tJ)(O),nt){nt.minX=Math.floor(nt.minX),nt.minY=Math.floor(nt.minY),nt.maxX=Math.ceil(nt.maxX),nt.maxY=Math.ceil(nt.maxY),nt.maxY+=1;var st=this.get("clipView");st&&(nt=(0,q.T2)(nt,U))}return nt},E.prototype.refreshElement=function(O){var U=this.get("refreshElements");U.push(O)},E.prototype._clearFrame=function(){var O=this.get("drawFrame");O&&((0,z.VS)(O),this.set("drawFrame",null),this.set("refreshElements",[]))},E.prototype.draw=function(){var O=this.get("drawFrame");this.get("autoDraw")&&O||this._startDraw()},E.prototype._drawAll=function(){var O=this.get("context"),U=this.get("el"),nt=this.getChildren();O.clearRect(0,0,U.width,U.height),(0,q.DE)(O,this),(0,q.NX)(O,nt),this.set("refreshElements",[])},E.prototype._drawRegion=function(){var O=this.get("context"),U=this.get("refreshElements"),nt=this.getChildren(),st=this._getRefreshRegion();st?(O.clearRect(st.minX,st.minY,st.maxX-st.minX,st.maxY-st.minY),O.save(),O.beginPath(),O.rect(st.minX,st.minY,st.maxX-st.minX,st.maxY-st.minY),O.clip(),(0,q.DE)(O,this),(0,q.kU)(this,nt,st),(0,q.NX)(O,nt,st),O.restore()):U.length&&(0,q.sT)(U),(0,z.S6)(U,function(H){H.get("hasChanged")&&H.set("hasChanged",!1)}),this.set("refreshElements",[])},E.prototype._startDraw=function(){var O=this,U=this.get("drawFrame"),nt=this.get("drawFrameCallback");U||(U=(0,z.U7)(function(){O.get("localRefresh")?O._drawRegion():O._drawAll(),O.set("drawFrame",null),nt&&nt()}),this.set("drawFrame",U))},E.prototype.skipDraw=function(){},E.prototype.removeDom=function(){var O=this.get("el");O.width=0,O.height=0,O.parentNode.removeChild(O)},E}(_.AbstractCanvas),Y=W},81402:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(41142),it=k(2647),Q=k(83102),j=k(98667),X=k(82389),b=function(N){(0,V.__extends)(z,N);function z(){return N!==null&&N.apply(this,arguments)||this}return z.prototype.onCanvasChange=function(q){(0,Q.md)(this,q)},z.prototype.getShapeBase=function(){return it},z.prototype.getGroupBase=function(){return z},z.prototype._applyClip=function(q,W){W&&(q.save(),(0,Q.DE)(q,W),W.createPath(q),q.restore(),q.clip(),W._afterDraw())},z.prototype.cacheCanvasBBox=function(){var q=this.cfg.children,W=[],Y=[];(0,j.each)(q,function(C){var S=C.cfg.cacheCanvasBBox;S&&C.cfg.isInView&&(W.push(S.minX,S.maxX),Y.push(S.minY,S.maxY))});var D=null;if(W.length){var E=(0,j.min)(W),O=(0,j.max)(W),U=(0,j.min)(Y),nt=(0,j.max)(Y);D={minX:E,minY:U,x:E,y:U,maxX:O,maxY:nt,width:O-E,height:nt-U};var st=this.cfg.canvas;if(st){var H=st.getViewRange();this.set("isInView",(0,X.qb)(D,H))}}else this.set("isInView",!1);this.set("cacheCanvasBBox",D)},z.prototype.draw=function(q,W){var Y=this.cfg.children,D=W?this.cfg.refresh:!0;Y.length&&D&&(q.save(),(0,Q.DE)(q,this),this._applyClip(q,this.getClip()),(0,Q.NX)(q,Y,W),q.restore(),this.cacheCanvasBBox()),this.cfg.refresh=null,this.set("hasChanged",!1)},z.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("hasChanged",!1)},z}(_.AbstractGroup);Mt.Z=b},15559:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{Canvas:function(){return j.Z},Group:function(){return X.Z},Shape:function(){return V},getArcParams:function(){return b.Z},version:function(){return N}});var V=k(2647),_=k(41142),it={};for(var Q in _)["default","Canvas","Group","Shape","getArcParams","version"].indexOf(Q)<0&&(it[Q]=function(z){return _[z]}.bind(0,Q));k.d(Mt,it);var j=k(27893),X=k(81402),b=k(26509),N="0.5.12"},2647:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{Base:function(){return N},Circle:function(){return q},Ellipse:function(){return D},Image:function(){return U},Line:function(){return S},Marker:function(){return Pt},Path:function(){return T},Polygon:function(){return P},Polyline:function(){return r},Rect:function(){return s},Text:function(){return u}});var V={};k.r(V),k.d(V,{Base:function(){return N},Circle:function(){return q},Ellipse:function(){return D},Image:function(){return U},Line:function(){return S},Marker:function(){return Pt},Path:function(){return T},Polygon:function(){return P},Polyline:function(){return r},Rect:function(){return s},Text:function(){return u}});var _=k(92336),it=k(41142),Q=k(82389),j=k(83102),X=k(81402),b=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},v.prototype.getShapeBase=function(){return V},v.prototype.getGroupBase=function(){return X.Z},v.prototype.onCanvasChange=function(i){(0,j.md)(this,i)},v.prototype.calculateBBox=function(){var i=this.get("type"),y=this.getHitLineWidth(),x=(0,it.getBBoxMethod)(i),o=x(this),h=y/2,d=o.x-h,B=o.y-h,K=o.x+o.width+h,tt=o.y+o.height+h;return{x:d,minX:d,y:B,minY:B,width:o.width+y,height:o.height+y,maxX:K,maxY:tt}},v.prototype.isFill=function(){return!!this.attrs.fill||this.isClipShape()},v.prototype.isStroke=function(){return!!this.attrs.stroke},v.prototype._applyClip=function(i,y){y&&(i.save(),(0,j.DE)(i,y),y.createPath(i),i.restore(),i.clip(),y._afterDraw())},v.prototype.draw=function(i,y){var x=this.cfg.clipShape;if(y){if(this.cfg.refresh===!1){this.set("hasChanged",!1);return}var o=this.getCanvasBBox();if(!(0,Q.qb)(y,o)){this.set("hasChanged",!1),this.cfg.isInView&&this._afterDraw();return}}i.save(),(0,j.DE)(i,this),this._applyClip(i,x),this.drawPath(i),i.restore(),this._afterDraw()},v.prototype.getCanvasViewBox=function(){var i=this.cfg.canvas;return i?i.getViewRange():null},v.prototype.cacheCanvasBBox=function(){var i=this.getCanvasViewBox();if(i){var y=this.getCanvasBBox(),x=(0,Q.qb)(y,i);this.set("isInView",x),x?this.set("cacheCanvasBBox",y):this.set("cacheCanvasBBox",null)}},v.prototype._afterDraw=function(){this.cacheCanvasBBox(),this.set("hasChanged",!1),this.set("refresh",null)},v.prototype.skipDraw=function(){this.set("cacheCanvasBBox",null),this.set("isInView",null),this.set("hasChanged",!1)},v.prototype.drawPath=function(i){this.createPath(i),this.strokeAndFill(i),this.afterDrawPath(i)},v.prototype.fill=function(i){i.fill()},v.prototype.stroke=function(i){i.stroke()},v.prototype.strokeAndFill=function(i){var y=this.attrs,x=y.lineWidth,o=y.opacity,h=y.strokeOpacity,d=y.fillOpacity;this.isFill()&&(!(0,Q.kK)(d)&&d!==1?(i.globalAlpha=d,this.fill(i),i.globalAlpha=o):this.fill(i)),this.isStroke()&&x>0&&(!(0,Q.kK)(h)&&h!==1&&(i.globalAlpha=h),this.stroke(i)),this.afterDrawPath(i)},v.prototype.createPath=function(i){},v.prototype.afterDrawPath=function(i){},v.prototype.isInShape=function(i,y){var x=this.isStroke(),o=this.isFill(),h=this.getHitLineWidth();return this.isInStrokeOrPath(i,y,x,o,h)},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){return!1},v.prototype.getHitLineWidth=function(){if(!this.isStroke())return 0;var i=this.attrs;return i.lineWidth+i.lineAppendWidth},v}(it.AbstractShape),N=b,z=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x:0,y:0,r:0})},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){var d=this.attr(),B=d.x,K=d.y,tt=d.r,et=h/2,ft=(0,Q.TE)(B,K,i,y);return o&&x?ft<=tt+et:o?ft<=tt:x?ft>=tt-et&&ft<=tt+et:!1},v.prototype.createPath=function(i){var y=this.attr(),x=y.x,o=y.y,h=y.r;i.beginPath(),i.arc(x,o,h,0,Math.PI*2,!1),i.closePath()},v}(N),q=z;function W(l,v,i,y){return l/(i*i)+v/(y*y)}var Y=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x:0,y:0,rx:0,ry:0})},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){var d=this.attr(),B=h/2,K=d.x,tt=d.y,et=d.rx,ft=d.ry,ct=(i-K)*(i-K),_t=(y-tt)*(y-tt);return o&&x?W(ct,_t,et+B,ft+B)<=1:o?W(ct,_t,et,ft)<=1:x?W(ct,_t,et-B,ft-B)>=1&&W(ct,_t,et+B,ft+B)<=1:!1},v.prototype.createPath=function(i){var y=this.attr(),x=y.x,o=y.y,h=y.rx,d=y.ry;if(i.beginPath(),i.ellipse)i.ellipse(x,o,h,d,0,0,Math.PI*2,!1);else{var B=h>d?h:d,K=h>d?1:h/d,tt=h>d?d/h:1;i.save(),i.translate(x,o),i.scale(K,tt),i.arc(0,0,B,0,Math.PI*2),i.restore(),i.closePath()}},v}(N),D=Y;function E(l){return l instanceof HTMLElement&&(0,Q.HD)(l.nodeName)&&l.nodeName.toUpperCase()==="CANVAS"}var O=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x:0,y:0,width:0,height:0})},v.prototype.initAttrs=function(i){this._setImage(i.img)},v.prototype.isStroke=function(){return!1},v.prototype.isOnlyHitBox=function(){return!0},v.prototype._afterLoading=function(){if(this.get("toDraw")===!0){var i=this.get("canvas");i?i.draw():this.createPath(this.get("context"))}},v.prototype._setImage=function(i){var y=this,x=this.attrs;if((0,Q.HD)(i)){var o=new Image;o.onload=function(){if(y.destroyed)return!1;y.attr("img",o),y.set("loading",!1),y._afterLoading();var h=y.get("callback");h&&h.call(y)},o.crossOrigin="Anonymous",o.src=i,this.set("loading",!0)}else i instanceof Image?(x.width||(x.width=i.width),x.height||(x.height=i.height)):E(i)&&(x.width||(x.width=Number(i.getAttribute("width"))),x.height||(x.height,Number(i.getAttribute("height"))))},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),i==="img"&&this._setImage(y)},v.prototype.createPath=function(i){if(this.get("loading")){this.set("toDraw",!0),this.set("context",i);return}var y=this.attr(),x=y.x,o=y.y,h=y.width,d=y.height,B=y.sx,K=y.sy,tt=y.swidth,et=y.sheight,ft=y.img;(ft instanceof Image||E(ft))&&(!(0,Q.kK)(B)&&!(0,Q.kK)(K)&&!(0,Q.kK)(tt)&&!(0,Q.kK)(et)?i.drawImage(ft,B,K,tt,et,x,o,h,d):i.drawImage(ft,x,o,h,d))},v}(N),U=O,nt=k(22824);function st(l,v,i,y,x,o,h){var d=Math.min(l,i),B=Math.max(l,i),K=Math.min(v,y),tt=Math.max(v,y),et=x/2;return o>=d-et&&o<=B+et&&h>=K-et&&h<=tt+et?nt.x1.pointToLine(l,v,i,y,o,h)<=x/2:!1}var H=k(19530),C=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},v.prototype.initAttrs=function(i){this.setArrow()},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),this.setArrow()},v.prototype.setArrow=function(){var i=this.attr(),y=i.x1,x=i.y1,o=i.x2,h=i.y2,d=i.startArrow,B=i.endArrow;d&&H.Yf(this,i,o,h,y,x),B&&H.YR(this,i,y,x,o,h)},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){if(!x||!h)return!1;var d=this.attr(),B=d.x1,K=d.y1,tt=d.x2,et=d.y2;return st(B,K,tt,et,h,i,y)},v.prototype.createPath=function(i){var y=this.attr(),x=y.x1,o=y.y1,h=y.x2,d=y.y2,B=y.startArrow,K=y.endArrow,tt={dx:0,dy:0},et={dx:0,dy:0};B&&B.d&&(tt=H.jF(x,o,h,d,y.startArrow.d)),K&&K.d&&(et=H.jF(x,o,h,d,y.endArrow.d)),i.beginPath(),i.moveTo(x+tt.dx,o+tt.dy),i.lineTo(h-et.dx,d-et.dy)},v.prototype.afterDrawPath=function(i){var y=this.get("startArrowShape"),x=this.get("endArrowShape");y&&y.draw(i),x&&x.draw(i)},v.prototype.getTotalLength=function(){var i=this.attr(),y=i.x1,x=i.y1,o=i.x2,h=i.y2;return nt.x1.length(y,x,o,h)},v.prototype.getPoint=function(i){var y=this.attr(),x=y.x1,o=y.y1,h=y.x2,d=y.y2;return nt.x1.pointAt(x,o,h,d,i)},v}(N),S=C,Z=k(98667),J=k(72461),ht={circle:function(l,v,i){return[["M",l-i,v],["A",i,i,0,1,0,l+i,v],["A",i,i,0,1,0,l-i,v]]},square:function(l,v,i){return[["M",l-i,v-i],["L",l+i,v-i],["L",l+i,v+i],["L",l-i,v+i],["Z"]]},diamond:function(l,v,i){return[["M",l-i,v],["L",l,v-i],["L",l+i,v],["L",l,v+i],["Z"]]},triangle:function(l,v,i){var y=i*Math.sin(.3333333333333333*Math.PI);return[["M",l-i,v+y],["L",l,v-y],["L",l+i,v+y],["Z"]]},"triangle-down":function(l,v,i){var y=i*Math.sin(.3333333333333333*Math.PI);return[["M",l-i,v-y],["L",l+i,v-y],["L",l,v+y],["Z"]]}},mt=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.initAttrs=function(i){this._resetParamsCache()},v.prototype._resetParamsCache=function(){this.set("paramsCache",{})},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),["symbol","x","y","r","radius"].indexOf(i)!==-1&&this._resetParamsCache()},v.prototype.isOnlyHitBox=function(){return!0},v.prototype._getR=function(i){return(0,Z.isNil)(i.r)?i.radius:i.r},v.prototype._getPath=function(){var i=this.attr(),y=i.x,x=i.y,o=i.symbol||"circle",h=this._getR(i),d,B;if((0,Q.mf)(o))d=o,B=d(y,x,h),B=(0,J.wb)(B);else{if(d=v.Symbols[o],!d)return console.warn(o+" marker is not supported."),null;B=d(y,x,h)}return B},v.prototype.createPath=function(i){var y=this._getPath(),x=this.get("paramsCache");(0,j.MC)(this,i,{path:y},x)},v.Symbols=ht,v}(N),Pt=mt;function Yt(l,v,i){var y=(0,it.getOffScreenContext)();return l.createPath(y),y.isPointInPath(v,i)}var Nt=1e-6;function dt(l){return Math.abs(l)<Nt?0:l<0?-1:1}function pt(l,v,i){return(i[0]-l[0])*(v[1]-l[1])===(v[0]-l[0])*(i[1]-l[1])&&Math.min(l[0],v[0])<=i[0]&&i[0]<=Math.max(l[0],v[0])&&Math.min(l[1],v[1])<=i[1]&&i[1]<=Math.max(l[1],v[1])}function xt(l,v,i){var y=!1,x=l.length;if(x<=2)return!1;for(var o=0;o<x;o++){var h=l[o],d=l[(o+1)%x];if(pt(h,d,[v,i]))return!0;dt(h[1]-i)>0!=dt(d[1]-i)>0&&dt(v-(i-h[1])*(h[0]-d[0])/(h[1]-d[1])-h[0])<0&&(y=!y)}return y}var Et=k(82867),wt=k(62329);function c(l,v,i,y,x,o,h,d){var B=(Math.atan2(d-v,h-l)+Math.PI*2)%(Math.PI*2);if(B<y||B>x)return!1;var K={x:l+i*Math.cos(B),y:v+i*Math.sin(B)};return(0,Q.TE)(K.x,K.y,h,d)<=o/2}var A=Et.vs;function G(l){for(var v=!1,i=l.length,y=0;y<i;y++){var x=l[y],o=x[0];if(o==="C"||o==="A"||o==="Q"){v=!0;break}}return v}function vt(l,v,i,y,x){for(var o=!1,h=v/2,d=0;d<l.length;d++){var B=l[d],K=B.currentPoint,tt=B.params,et=B.prePoint,ft=B.box;if(!(ft&&!(0,Q.mh)(ft.x-h,ft.y-h,ft.width+v,ft.height+v,i,y))){switch(B.command){case"L":case"Z":o=st(et[0],et[1],K[0],K[1],v,i,y);break;case"Q":var ct=nt.lD.pointDistance(et[0],et[1],tt[1],tt[2],tt[3],tt[4],i,y);o=ct<=v/2;break;case"C":var _t=nt.Ll.pointDistance(et[0],et[1],tt[1],tt[2],tt[3],tt[4],tt[5],tt[6],i,y,x);o=_t<=v/2;break;case"A":var At=B.arcParams,It=At.cx,Xt=At.cy,St=At.rx,kt=At.ry,Rt=At.startAngle,Wt=At.endAngle,Ot=At.xRotation,Ft=[i,y,1],Kt=St>kt?St:kt,Zt=St>kt?1:St/kt,Gt=St>kt?kt/St:1,Vt=A(null,[["t",-It,-Xt],["r",-Ot],["s",1/Zt,1/Gt]]);wt.transformMat3(Ft,Ft,Vt),o=c(0,0,Kt,Rt,Wt,v,Ft[0],Ft[1]);break;default:break}if(o)break}}return o}function yt(l){for(var v=l.length,i=[],y=[],x=[],o=0;o<v;o++){var h=l[o],d=h[0];d==="M"?(x.length&&(y.push(x),x=[]),x.push([h[1],h[2]])):d==="Z"?x.length&&(i.push(x),x=[]):x.push([h[1],h[2]])}return x.length>0&&y.push(x),{polygons:i,polylines:y}}var p=(0,_.__assign)({hasArc:G,extractPolygons:yt,isPointInStroke:vt},it.PathUtil);function M(l,v,i){for(var y=!1,x=0;x<l.length;x++){var o=l[x];if(y=xt(o,v,i),y)break}return y}var L=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{startArrow:!1,endArrow:!1})},v.prototype.initAttrs=function(i){this._setPathArr(i.path),this.setArrow()},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),i==="path"&&this._setPathArr(y),this.setArrow()},v.prototype._setPathArr=function(i){this.attrs.path=(0,J.wb)(i);var y=p.hasArc(i);this.set("hasArc",y),this.set("paramsCache",{}),this.set("segments",null),this.set("curve",null),this.set("tCache",null),this.set("totalLength",null)},v.prototype.getSegments=function(){var i=this.get("segements");return i||(i=(0,J.zx)(this.attr("path")),this.set("segments",i)),i},v.prototype.setArrow=function(){var i=this.attr(),y=i.startArrow,x=i.endArrow;if(y){var o=this.getStartTangent();H.Yf(this,i,o[0][0],o[0][1],o[1][0],o[1][1])}if(x){var o=this.getEndTangent();H.YR(this,i,o[0][0],o[0][1],o[1][0],o[1][1])}},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){var d=this.getSegments(),B=this.get("hasArc"),K=!1;if(x){var tt=this.getTotalLength();K=p.isPointInStroke(d,h,i,y,tt)}if(!K&&o)if(B)K=Yt(this,i,y);else{var et=this.attr("path"),ft=p.extractPolygons(et);K=M(ft.polygons,i,y)||M(ft.polylines,i,y)}return K},v.prototype.createPath=function(i){var y=this.attr(),x=this.get("paramsCache");(0,j.MC)(this,i,y,x)},v.prototype.afterDrawPath=function(i){var y=this.get("startArrowShape"),x=this.get("endArrowShape");y&&y.draw(i),x&&x.draw(i)},v.prototype.getTotalLength=function(){var i=this.get("totalLength");return(0,Z.isNil)(i)?(this._calculateCurve(),this._setTcache(),this.get("totalLength")):i},v.prototype.getPoint=function(i){var y=this.get("tCache");y||(this._calculateCurve(),this._setTcache(),y=this.get("tCache"));var x,o,h=this.get("curve");if(!y||y.length===0)return h?{x:h[0][1],y:h[0][2]}:null;(0,Z.each)(y,function(tt,et){i>=tt[0]&&i<=tt[1]&&(x=(i-tt[0])/(tt[1]-tt[0]),o=et)});var d=h[o];if((0,Z.isNil)(d)||(0,Z.isNil)(o))return null;var B=d.length,K=h[o+1];return nt.Ll.pointAt(d[B-2],d[B-1],K[1],K[2],K[3],K[4],K[5],K[6],x)},v.prototype._calculateCurve=function(){var i=this.attr().path;this.set("curve",p.pathToCurve(i))},v.prototype._setTcache=function(){var i=0,y=0,x=[],o,h,d,B,K=this.get("curve");if(K){if((0,Z.each)(K,function(tt,et){d=K[et+1],B=tt.length,d&&(i+=nt.Ll.length(tt[B-2],tt[B-1],d[1],d[2],d[3],d[4],d[5],d[6])||0)}),this.set("totalLength",i),i===0){this.set("tCache",[]);return}(0,Z.each)(K,function(tt,et){d=K[et+1],B=tt.length,d&&(o=[],o[0]=y/i,h=nt.Ll.length(tt[B-2],tt[B-1],d[1],d[2],d[3],d[4],d[5],d[6]),y+=h||0,o[1]=y/i,x.push(o))}),this.set("tCache",x)}},v.prototype.getStartTangent=function(){var i=this.getSegments(),y;if(i.length>1){var x=i[0].currentPoint,o=i[1].currentPoint,h=i[1].startTangent;y=[],h?(y.push([x[0]-h[0],x[1]-h[1]]),y.push([x[0],x[1]])):(y.push([o[0],o[1]]),y.push([x[0],x[1]]))}return y},v.prototype.getEndTangent=function(){var i=this.getSegments(),y=i.length,x;if(y>1){var o=i[y-2].currentPoint,h=i[y-1].currentPoint,d=i[y-1].endTangent;x=[],d?(x.push([h[0]-d[0],h[1]-d[1]]),x.push([h[0],h[1]])):(x.push([o[0],o[1]]),x.push([h[0],h[1]]))}return x},v}(N),T=L;function $(l,v,i,y,x){var o=l.length;if(o<2)return!1;for(var h=0;h<o-1;h++){var d=l[h][0],B=l[h][1],K=l[h+1][0],tt=l[h+1][1];if(st(d,B,K,tt,v,i,y))return!0}if(x){var et=l[0],ft=l[o-1];if(st(et[0],et[1],ft[0],ft[1],v,i,y))return!0}return!1}var ut=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.isInStrokeOrPath=function(i,y,x,o,h){var d=this.attr().points,B=!1;return x&&(B=$(d,h,i,y,!0)),!B&&o&&(B=xt(d,i,y)),B},v.prototype.createPath=function(i){var y=this.attr(),x=y.points;if(!(x.length<2)){i.beginPath();for(var o=0;o<x.length;o++){var h=x[o];o===0?i.moveTo(h[0],h[1]):i.lineTo(h[0],h[1])}i.closePath()}},v}(N),P=ut,g=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{startArrow:!1,endArrow:!1})},v.prototype.initAttrs=function(i){this.setArrow()},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),this.setArrow(),["points"].indexOf(i)!==-1&&this._resetCache()},v.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},v.prototype.setArrow=function(){var i=this.attr(),y=this.attrs,x=y.points,o=y.startArrow,h=y.endArrow,d=x.length,B=x[0][0],K=x[0][1],tt=x[d-1][0],et=x[d-1][1];o&&H.Yf(this,i,x[1][0],x[1][1],B,K),h&&H.YR(this,i,x[d-2][0],x[d-2][1],tt,et)},v.prototype.isFill=function(){return!1},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){if(!x||!h)return!1;var d=this.attr().points;return $(d,h,i,y,!1)},v.prototype.isStroke=function(){return!0},v.prototype.createPath=function(i){var y=this.attr(),x=y.points,o=y.startArrow,h=y.endArrow,d=x.length;if(!(x.length<2)){var B=x[0][0],K=x[0][1],tt=x[d-1][0],et=x[d-1][1];if(o&&o.d){var ft=H.jF(B,K,x[1][0],x[1][1],o.d);B+=ft.dx,K+=ft.dy}if(h&&h.d){var ft=H.jF(x[d-2][0],x[d-2][1],tt,et,h.d);tt-=ft.dx,et-=ft.dy}i.beginPath(),i.moveTo(B,K);for(var ct=0;ct<d-1;ct++){var _t=x[ct];i.lineTo(_t[0],_t[1])}i.lineTo(tt,et)}},v.prototype.afterDrawPath=function(i){var y=this.get("startArrowShape"),x=this.get("endArrowShape");y&&y.draw(i),x&&x.draw(i)},v.prototype.getTotalLength=function(){var i=this.attr().points,y=this.get("totalLength");return(0,Z.isNil)(y)?(this.set("totalLength",nt.aH.length(i)),this.get("totalLength")):y},v.prototype.getPoint=function(i){var y=this.attr().points,x=this.get("tCache");x||(this._setTcache(),x=this.get("tCache"));var o,h;return(0,Z.each)(x,function(d,B){i>=d[0]&&i<=d[1]&&(o=(i-d[0])/(d[1]-d[0]),h=B)}),nt.x1.pointAt(y[h][0],y[h][1],y[h+1][0],y[h+1][1],o)},v.prototype._setTcache=function(){var i=this.attr().points;if(!(!i||i.length===0)){var y=this.getTotalLength();if(!(y<=0)){var x=0,o=[],h,d;(0,Z.each)(i,function(B,K){i[K+1]&&(h=[],h[0]=x/y,d=nt.x1.length(B[0],B[1],i[K+1][0],i[K+1][1]),x+=d,h[1]=x/y,o.push(h))}),this.set("tCache",o)}}},v.prototype.getStartTangent=function(){var i=this.attr().points,y=[];return y.push([i[1][0],i[1][1]]),y.push([i[0][0],i[0][1]]),y},v.prototype.getEndTangent=function(){var i=this.attr().points,y=i.length-1,x=[];return x.push([i[y-1][0],i[y-1][1]]),x.push([i[y][0],i[y][1]]),x},v}(N),r=g,t=k(779);function f(l,v,i,y,x,o,h){var d=x/2;return(0,Q.mh)(l-d,v-d,i,x,o,h)||(0,Q.mh)(l+i-d,v-d,x,y,o,h)||(0,Q.mh)(l+d,v+y-d,i,x,o,h)||(0,Q.mh)(l-d,v+d,x,y,o,h)}function n(l,v,i,y,x,o,h,d){var B=o/2;return st(l+x,v,l+i-x,v,o,h,d)||st(l+i,v+x,l+i,v+y-x,o,h,d)||st(l+i-x,v+y,l+x,v+y,o,h,d)||st(l,v+y-x,l,v+x,o,h,d)||c(l+i-x,v+x,x,1.5*Math.PI,2*Math.PI,o,h,d)||c(l+i-x,v+y-x,x,0,.5*Math.PI,o,h,d)||c(l+x,v+y-x,x,.5*Math.PI,Math.PI,o,h,d)||c(l+x,v+x,x,Math.PI,1.5*Math.PI,o,h,d)}var a=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x:0,y:0,width:0,height:0,radius:0})},v.prototype.isInStrokeOrPath=function(i,y,x,o,h){var d=this.attr(),B=d.x,K=d.y,tt=d.width,et=d.height,ft=d.radius;if(ft){var _t=!1;return x&&(_t=n(B,K,tt,et,ft,h,i,y)),!_t&&o&&(_t=Yt(this,i,y)),_t}else{var ct=h/2;if(o&&x)return(0,Q.mh)(B-ct,K-ct,tt+ct,et+ct,i,y);if(o)return(0,Q.mh)(B,K,tt,et,i,y);if(x)return f(B,K,tt,et,h,i,y)}},v.prototype.createPath=function(i){var y=this.attr(),x=y.x,o=y.y,h=y.width,d=y.height,B=y.radius;if(i.beginPath(),B===0)i.rect(x,o,h,d);else{var K=(0,t.Fy)(B),tt=K[0],et=K[1],ft=K[2],ct=K[3];i.moveTo(x+tt,o),i.lineTo(x+h-et,o),et!==0&&i.arc(x+h-et,o+et,et,-Math.PI/2,0),i.lineTo(x+h,o+d-ft),ft!==0&&i.arc(x+h-ft,o+d-ft,ft,0,Math.PI/2),i.lineTo(x+ct,o+d),ct!==0&&i.arc(x+ct,o+d-ct,ct,Math.PI/2,Math.PI),i.lineTo(x,o+tt),tt!==0&&i.arc(x+tt,o+tt,tt,Math.PI,Math.PI*1.5),i.closePath()}},v}(N),s=a,e=function(l){(0,_.__extends)(v,l);function v(){return l!==null&&l.apply(this,arguments)||this}return v.prototype.getDefaultAttrs=function(){var i=l.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},i),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},v.prototype.isOnlyHitBox=function(){return!0},v.prototype.initAttrs=function(i){this._assembleFont(),i.text&&this._setText(i.text)},v.prototype._assembleFont=function(){var i=this.attrs;i.font=(0,it.assembleFont)(i)},v.prototype._setText=function(i){var y=null;(0,Q.HD)(i)&&i.indexOf(`
`)!==-1&&(y=i.split(`
`)),this.set("textArr",y)},v.prototype.onAttrChange=function(i,y,x){l.prototype.onAttrChange.call(this,i,y,x),i.startsWith("font")&&this._assembleFont(),i==="text"&&this._setText(y)},v.prototype._getSpaceingY=function(){var i=this.attrs,y=i.lineHeight,x=i.fontSize*1;return y?y-x:x*.14},v.prototype._drawTextArr=function(i,y,x){var o=this.attrs,h=o.textBaseline,d=o.x,B=o.y,K=o.fontSize*1,tt=this._getSpaceingY(),et=(0,it.getTextHeight)(o.text,o.fontSize,o.lineHeight),ft;(0,Q.S6)(y,function(ct,_t){ft=B+_t*(tt+K)-et+K,h==="middle"&&(ft+=et-K-(et-K)/2),h==="top"&&(ft+=et-K),(0,Q.kK)(ct)||(x?i.fillText(ct,d,ft):i.strokeText(ct,d,ft))})},v.prototype._drawText=function(i,y){var x=this.attr(),o=x.x,h=x.y,d=this.get("textArr");if(d)this._drawTextArr(i,d,y);else{var B=x.text;(0,Q.kK)(B)||(y?i.fillText(B,o,h):i.strokeText(B,o,h))}},v.prototype.strokeAndFill=function(i){var y=this.attrs,x=y.lineWidth,o=y.opacity,h=y.strokeOpacity,d=y.fillOpacity;this.isStroke()&&x>0&&(!(0,Q.kK)(h)&&h!==1&&(i.globalAlpha=o),this.stroke(i)),this.isFill()&&(!(0,Q.kK)(d)&&d!==1?(i.globalAlpha=d,this.fill(i),i.globalAlpha=o):this.fill(i)),this.afterDrawPath(i)},v.prototype.fill=function(i){this._drawText(i,!0)},v.prototype.stroke=function(i){this._drawText(i,!1)},v}(N),u=e},26509:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return j}});var V=k(82389);function _(X){return Math.sqrt(X[0]*X[0]+X[1]*X[1])}function it(X,b){return _(X)*_(b)?(X[0]*b[0]+X[1]*b[1])/(_(X)*_(b)):1}function Q(X,b){return(X[0]*b[1]<X[1]*b[0]?-1:1)*Math.acos(it(X,b))}function j(X,b){var N=b[1],z=b[2],q=(0,V.wQ)((0,V.c$)(b[3]),Math.PI*2),W=b[4],Y=b[5],D=X[0],E=X[1],O=b[6],U=b[7],nt=Math.cos(q)*(D-O)/2+Math.sin(q)*(E-U)/2,st=-1*Math.sin(q)*(D-O)/2+Math.cos(q)*(E-U)/2,H=nt*nt/(N*N)+st*st/(z*z);H>1&&(N*=Math.sqrt(H),z*=Math.sqrt(H));var C=N*N*(st*st)+z*z*(nt*nt),S=C?Math.sqrt((N*N*(z*z)-C)/C):1;W===Y&&(S*=-1),isNaN(S)&&(S=0);var Z=z?S*N*st/z:0,J=N?S*-z*nt/N:0,ht=(D+O)/2+Math.cos(q)*Z-Math.sin(q)*J,mt=(E+U)/2+Math.sin(q)*Z+Math.cos(q)*J,Pt=[(nt-Z)/N,(st-J)/z],Yt=[(-1*nt-Z)/N,(-1*st-J)/z],Nt=Q([1,0],Pt),dt=Q(Pt,Yt);return it(Pt,Yt)<=-1&&(dt=Math.PI),it(Pt,Yt)>=1&&(dt=0),Y===0&&dt>0&&(dt=dt-2*Math.PI),Y===1&&dt<0&&(dt=dt+2*Math.PI),{cx:ht,cy:mt,rx:(0,V.CF)(X,[O,U])?0:N,ry:(0,V.CF)(X,[O,U])?0:z,startAngle:Nt,endAngle:Nt+dt,xRotation:q,arcFlag:W,sweepFlag:Y}}},19530:function(Lt,Mt,k){"use strict";k.d(Mt,{YR:function(){return W},Yf:function(){return q},jF:function(){return z}});var V=k(92336),_=k(2647),it=Math.sin,Q=Math.cos,j=Math.atan2,X=Math.PI;function b(Y,D,E,O,U,nt,st){var H=D.stroke,C=D.lineWidth,S=E-U,Z=O-nt,J=j(Z,S),ht=new _.Path({type:"path",canvas:Y.get("canvas"),isArrowShape:!0,attrs:{path:"M"+10*Q(X/6)+","+10*it(X/6)+" L0,0 L"+10*Q(X/6)+",-"+10*it(X/6),stroke:H,lineWidth:C}});ht.translate(U,nt),ht.rotateAtPoint(U,nt,J),Y.set(st?"startArrowShape":"endArrowShape",ht)}function N(Y,D,E,O,U,nt,st){var H=D.startArrow,C=D.endArrow,S=D.stroke,Z=D.lineWidth,J=st?H:C,ht=J.d,mt=J.fill,Pt=J.stroke,Yt=J.lineWidth,Nt=(0,V.__rest)(J,["d","fill","stroke","lineWidth"]),dt=E-U,pt=O-nt,xt=j(pt,dt);ht&&(U=U-Q(xt)*ht,nt=nt-it(xt)*ht);var Et=new _.Path({type:"path",canvas:Y.get("canvas"),isArrowShape:!0,attrs:(0,V.__assign)((0,V.__assign)({},Nt),{stroke:Pt||S,lineWidth:Yt||Z,fill:mt})});Et.translate(U,nt),Et.rotateAtPoint(U,nt,xt),Y.set(st?"startArrowShape":"endArrowShape",Et)}function z(Y,D,E,O,U){var nt=j(O-D,E-Y);return{dx:Q(nt)*U,dy:it(nt)*U}}function q(Y,D,E,O,U,nt){typeof D.startArrow=="object"?N(Y,D,E,O,U,nt,!0):D.startArrow?b(Y,D,E,O,U,nt,!0):Y.set("startArrowShape",null)}function W(Y,D,E,O,U,nt){typeof D.endArrow=="object"?N(Y,D,E,O,U,nt,!1):D.endArrow?b(Y,D,E,O,U,nt,!1):Y.set("startArrowShape",null)}},83102:function(Lt,Mt,k){"use strict";k.d(Mt,{DE:function(){return b},MC:function(){return E},NX:function(){return N},T2:function(){return st},kU:function(){return z},md:function(){return O},sT:function(){return W},tJ:function(){return nt}});var V=k(98667),_=k(779),it=k(26509),Q=k(82389),j=k(19530),X={fill:"fillStyle",stroke:"strokeStyle",opacity:"globalAlpha"};function b(H,C){var S=C.attr();for(var Z in S){var J=S[Z],ht=X[Z]?X[Z]:Z;ht==="matrix"&&J?H.transform(J[0],J[1],J[3],J[4],J[6],J[7]):ht==="lineDash"&&H.setLineDash?(0,V.isArray)(J)&&H.setLineDash(J):(ht==="strokeStyle"||ht==="fillStyle"?J=(0,_.QK)(H,C,J):ht==="globalAlpha"&&(J=J*H.globalAlpha),H[ht]=J)}}function N(H,C,S){for(var Z=0;Z<C.length;Z++){var J=C[Z];J.cfg.visible?J.draw(H,S):J.skipDraw()}}function z(H,C,S){var Z=H.get("refreshElements");(0,V.each)(Z,function(J){if(J!==H)for(var ht=J.cfg.parent;ht&&ht!==H&&!ht.cfg.refresh;)ht.cfg.refresh=!0,ht=ht.cfg.parent}),Z[0]===H?Y(C,S):q(C,S)}function q(H,C){for(var S=0;S<H.length;S++){var Z=H[S];if(Z.cfg.visible)if(Z.cfg.hasChanged)Z.cfg.refresh=!0,Z.isGroup()&&Y(Z.cfg.children,C);else if(Z.cfg.refresh)Z.isGroup()&&q(Z.cfg.children,C);else{var J=D(Z,C);Z.cfg.refresh=J,J&&Z.isGroup()&&q(Z.cfg.children,C)}}}function W(H){for(var C=0;C<H.length;C++){var S=H[C];S.cfg.hasChanged=!1,S.isGroup()&&!S.destroyed&&W(S.cfg.children)}}function Y(H,C){for(var S=0;S<H.length;S++){var Z=H[S];Z.cfg.visible&&(Z.cfg.refresh=!0,Z.isGroup()&&Y(Z.get("children"),C))}}function D(H,C){var S=H.cfg.cacheCanvasBBox,Z=H.cfg.isInView&&S&&(0,Q.qb)(S,C);return Z}function E(H,C,S,Z){var J=S.path,ht=S.startArrow,mt=S.endArrow;if(J){var Pt=[0,0],Yt=[0,0],Nt={dx:0,dy:0};C.beginPath();for(var dt=0;dt<J.length;dt++){var pt=J[dt],xt=pt[0];if(dt===0&&ht&&ht.d){var Et=H.getStartTangent();Nt=j.jF(Et[0][0],Et[0][1],Et[1][0],Et[1][1],ht.d)}else if(dt===J.length-2&&J[dt+1][0]==="Z"&&mt&&mt.d){var wt=J[dt+1];if(wt[0]==="Z"){var Et=H.getEndTangent();Nt=j.jF(Et[0][0],Et[0][1],Et[1][0],Et[1][1],mt.d)}}else if(dt===J.length-1&&mt&&mt.d&&J[0]!=="Z"){var Et=H.getEndTangent();Nt=j.jF(Et[0][0],Et[0][1],Et[1][0],Et[1][1],mt.d)}var c=Nt.dx,A=Nt.dy;switch(xt){case"M":C.moveTo(pt[1]-c,pt[2]-A),Yt=[pt[1],pt[2]];break;case"L":C.lineTo(pt[1]-c,pt[2]-A);break;case"Q":C.quadraticCurveTo(pt[1],pt[2],pt[3]-c,pt[4]-A);break;case"C":C.bezierCurveTo(pt[1],pt[2],pt[3],pt[4],pt[5]-c,pt[6]-A);break;case"A":{var G=void 0;Z?(G=Z[dt],G||(G=(0,it.Z)(Pt,pt),Z[dt]=G)):G=(0,it.Z)(Pt,pt);var vt=G.cx,yt=G.cy,p=G.rx,M=G.ry,L=G.startAngle,T=G.endAngle,$=G.xRotation,ut=G.sweepFlag;if(C.ellipse)C.ellipse(vt,yt,p,M,$,L,T,1-ut);else{var P=p>M?p:M,g=p>M?1:p/M,r=p>M?M/p:1;C.translate(vt,yt),C.rotate($),C.scale(g,r),C.arc(0,0,P,L,T,1-ut),C.scale(1/g,1/r),C.rotate(-$),C.translate(-vt,-yt)}break}case"Z":C.closePath();break;default:break}if(xt==="Z")Pt=Yt;else{var t=pt.length;Pt=[pt[t-2],pt[t-1]]}}}}function O(H,C){var S=H.get("canvas");S&&(C==="remove"&&(H._cacheCanvasBBox=H.get("cacheCanvasBBox")),H.get("hasChanged")||(H.set("hasChanged",!0),H.cfg.parent&&H.cfg.parent.get("hasChanged")||(S.refreshElement(H,C,S),S.get("autoDraw")&&S.draw())))}function U(H){var C;if(H.destroyed)C=H._cacheCanvasBBox;else{var S=H.get("cacheCanvasBBox"),Z=S&&!!(S.width&&S.height),J=H.getCanvasBBox(),ht=J&&!!(J.width&&J.height);Z&&ht?C=(0,Q.Me)(S,J):Z?C=S:ht&&(C=J)}return C}function nt(H){if(!H.length)return null;var C=[],S=[],Z=[],J=[];return(0,V.each)(H,function(ht){var mt=U(ht);mt&&(C.push(mt.minX),S.push(mt.minY),Z.push(mt.maxX),J.push(mt.maxY))}),{minX:(0,V.min)(C),minY:(0,V.min)(S),maxX:(0,V.max)(Z),maxY:(0,V.max)(J)}}function st(H,C){return!H||!C||!(0,Q.qb)(H,C)?null:{minX:Math.max(H.minX,C.minX),minY:Math.max(H.minY,C.minY),maxX:Math.min(H.maxX,C.maxX),maxY:Math.min(H.maxY,C.maxY)}}},779:function(Lt,Mt,k){"use strict";k.d(Mt,{Fy:function(){return W},QK:function(){return q}});var V=k(82389),_=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,it=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,Q=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,j=/[\d.]+:(#[^\s]+|[^\)]+\))/gi;function X(Y,D){var E=Y.match(j);(0,V.S6)(E,function(O){var U=O.split(":");D.addColorStop(U[0],U[1])})}function b(Y,D,E){var O=_.exec(E),U=parseFloat(O[1])%360*(Math.PI/180),nt=O[2],st=D.getBBox(),H,C;U>=0&&U<1/2*Math.PI?(H={x:st.minX,y:st.minY},C={x:st.maxX,y:st.maxY}):1/2*Math.PI<=U&&U<Math.PI?(H={x:st.maxX,y:st.minY},C={x:st.minX,y:st.maxY}):Math.PI<=U&&U<3/2*Math.PI?(H={x:st.maxX,y:st.maxY},C={x:st.minX,y:st.minY}):(H={x:st.minX,y:st.maxY},C={x:st.maxX,y:st.minY});var S=Math.tan(U),Z=S*S,J=(C.x-H.x+S*(C.y-H.y))/(Z+1)+H.x,ht=S*(C.x-H.x+S*(C.y-H.y))/(Z+1)+H.y,mt=Y.createLinearGradient(H.x,H.y,J,ht);return X(nt,mt),mt}function N(Y,D,E){var O=it.exec(E),U=parseFloat(O[1]),nt=parseFloat(O[2]),st=parseFloat(O[3]),H=O[4];if(st===0){var C=H.match(j);return C[C.length-1].split(":")[1]}var S=D.getBBox(),Z=S.maxX-S.minX,J=S.maxY-S.minY,ht=Math.sqrt(Z*Z+J*J)/2,mt=Y.createRadialGradient(S.minX+Z*U,S.minY+J*nt,0,S.minX+Z/2,S.minY+J/2,st*ht);return X(H,mt),mt}function z(Y,D,E){if(D.get("patternSource")&&D.get("patternSource")===E)return D.get("pattern");var O,U,nt=Q.exec(E),st=nt[1],H=nt[2];function C(){O=Y.createPattern(U,st),D.set("pattern",O),D.set("patternSource",E)}switch(st){case"a":st="repeat";break;case"x":st="repeat-x";break;case"y":st="repeat-y";break;case"n":st="no-repeat";break;default:st="no-repeat"}return U=new Image,H.match(/^data:/i)||(U.crossOrigin="Anonymous"),U.src=H,U.complete?C():(U.onload=C,U.src=U.src),O}function q(Y,D,E){var O=D.getBBox();if(isNaN(O.x)||isNaN(O.y)||isNaN(O.width)||isNaN(O.height))return E;if((0,V.HD)(E)){if(E[1]==="("||E[2]==="("){if(E[0]==="l")return b(Y,D,E);if(E[0]==="r")return N(Y,D,E);if(E[0]==="p")return z(Y,D,E)}return E}if(E instanceof CanvasPattern)return E}function W(Y){var D=0,E=0,O=0,U=0;return(0,V.kJ)(Y)?Y.length===1?D=E=O=U=Y[0]:Y.length===2?(D=O=Y[0],E=U=Y[1]):Y.length===3?(D=Y[0],E=U=Y[1],O=Y[2]):(D=Y[0],E=Y[1],O=Y[2],U=Y[3]):D=E=O=U=Y,[D,E,O,U]}},82389:function(Lt,Mt,k){"use strict";k.d(Mt,{CF:function(){return b},HD:function(){return V.isString},Me:function(){return X},S6:function(){return V.each},TE:function(){return it},U7:function(){return V.requestAnimationFrame},VS:function(){return V.clearAnimationFrame},c$:function(){return V.toRadian},kJ:function(){return V.isArray},kK:function(){return V.isNil},mX:function(){return _},mf:function(){return V.isFunction},mh:function(){return Q},qb:function(){return j},wQ:function(){return V.mod}});var V=k(98667);function _(){return window?window.devicePixelRatio:1}function it(N,z,q,W){var Y=N-q,D=z-W;return Math.sqrt(Y*Y+D*D)}function Q(N,z,q,W,Y,D){return Y>=N&&Y<=N+q&&D>=z&&D<=z+W}function j(N,z){return!(z.minX>N.maxX||z.maxX<N.minX||z.minY>N.maxY||z.maxY<N.minY)}function X(N,z){return!N||!z?N||z:{minX:Math.min(N.minX,z.minX),minY:Math.min(N.minY,z.minY),maxX:Math.max(N.maxX,z.maxX),maxY:Math.max(N.maxY,z.maxY)}}function b(N,z){return N[0]===z[0]&&N[1]===z[1]}},22824:function(Lt,Mt,k){"use strict";k.d(Mt,{wN:function(){return G},Ll:function(){return ht},x1:function(){return z},aH:function(){return T},lD:function(){return st},Zr:function(){return V}});var V={};k.r(V),k.d(V,{distance:function(){return it},getBBoxByArray:function(){return j},getBBoxRange:function(){return X},isNumberEqual:function(){return Q},piMod:function(){return b}});var _=k(98667);function it(P,g,r,t){var f=P-r,n=g-t;return Math.sqrt(f*f+n*n)}function Q(P,g){return Math.abs(P-g)<.001}function j(P,g){var r=(0,_.min)(P),t=(0,_.min)(g),f=(0,_.max)(P),n=(0,_.max)(g);return{x:r,y:t,width:f-r,height:n-t}}function X(P,g,r,t){return{minX:(0,_.min)([P,r]),maxX:(0,_.max)([P,r]),minY:(0,_.min)([g,t]),maxY:(0,_.max)([g,t])}}function b(P){return(P+Math.PI*2)%(Math.PI*2)}var N=k(69341),z={box:function(P,g,r,t){return j([P,r],[g,t])},length:function(P,g,r,t){return it(P,g,r,t)},pointAt:function(P,g,r,t,f){return{x:(1-f)*P+f*r,y:(1-f)*g+f*t}},pointDistance:function(P,g,r,t,f,n){var a=(r-P)*(f-P)+(t-g)*(n-g);if(a<0)return it(P,g,f,n);var s=(r-P)*(r-P)+(t-g)*(t-g);return a>s?it(r,t,f,n):this.pointToLine(P,g,r,t,f,n)},pointToLine:function(P,g,r,t,f,n){var a=[r-P,t-g];if(N.exactEquals(a,[0,0]))return Math.sqrt((f-P)*(f-P)+(n-g)*(n-g));var s=[-a[1],a[0]];N.normalize(s,s);var e=[f-P,n-g];return Math.abs(N.dot(e,s))},tangentAngle:function(P,g,r,t){return Math.atan2(t-g,r-P)}},q=1e-4;function W(P,g,r,t,f,n){var a,s=1/0,e=[r,t],u=20;n&&n>200&&(u=n/10);for(var l=1/u,v=l/10,i=0;i<=u;i++){var y=i*l,x=[f.apply(null,P.concat([y])),f.apply(null,g.concat([y]))],o=it(e[0],e[1],x[0],x[1]);o<s&&(a=y,s=o)}if(a===0)return{x:P[0],y:g[0]};if(a===1){var h=P.length;return{x:P[h-1],y:g[h-1]}}s=1/0;for(var i=0;i<32&&!(v<q);i++){var d=a-v,B=a+v,x=[f.apply(null,P.concat([d])),f.apply(null,g.concat([d]))],o=it(e[0],e[1],x[0],x[1]);if(d>=0&&o<s)a=d,s=o;else{var K=[f.apply(null,P.concat([B])),f.apply(null,g.concat([B]))],tt=it(e[0],e[1],K[0],K[1]);B<=1&&tt<s?(a=B,s=tt):v*=.5}}return{x:f.apply(null,P.concat([a])),y:f.apply(null,g.concat([a]))}}function Y(P,g){for(var r=0,t=P.length,f=0;f<t;f++){var n=P[f],a=g[f],s=P[(f+1)%t],e=g[(f+1)%t];r+=it(n,a,s,e)}return r/2}function D(P,g,r,t){var f=1-t;return f*f*P+2*t*f*g+t*t*r}function E(P,g,r){var t=P+r-2*g;if(Q(t,0))return[.5];var f=(P-g)/t;return f<=1&&f>=0?[f]:[]}function O(P,g,r,t){return 2*(1-t)*(g-P)+2*t*(r-g)}function U(P,g,r,t,f,n,a){var s=D(P,r,f,a),e=D(g,t,n,a),u=z.pointAt(P,g,r,t,a),l=z.pointAt(r,t,f,n,a);return[[P,g,u.x,u.y,s,e],[s,e,l.x,l.y,f,n]]}function nt(P,g,r,t,f,n,a){if(a===0)return(it(P,g,r,t)+it(r,t,f,n)+it(P,g,f,n))/2;var s=U(P,g,r,t,f,n,.5),e=s[0],u=s[1];return e.push(a-1),u.push(a-1),nt.apply(null,e)+nt.apply(null,u)}var st={box:function(P,g,r,t,f,n){var a=E(P,r,f)[0],s=E(g,t,n)[0],e=[P,f],u=[g,n];return a!==void 0&&e.push(D(P,r,f,a)),s!==void 0&&u.push(D(g,t,n,s)),j(e,u)},length:function(P,g,r,t,f,n){return nt(P,g,r,t,f,n,3)},nearestPoint:function(P,g,r,t,f,n,a,s){return W([P,r,f],[g,t,n],a,s,D)},pointDistance:function(P,g,r,t,f,n,a,s){var e=this.nearestPoint(P,g,r,t,f,n,a,s);return it(e.x,e.y,a,s)},interpolationAt:D,pointAt:function(P,g,r,t,f,n,a){return{x:D(P,r,f,a),y:D(g,t,n,a)}},divide:function(P,g,r,t,f,n,a){return U(P,g,r,t,f,n,a)},tangentAngle:function(P,g,r,t,f,n,a){var s=O(P,r,f,a),e=O(g,t,n,a),u=Math.atan2(e,s);return b(u)}};function H(P,g,r,t,f){var n=1-f;return n*n*n*P+3*g*f*n*n+3*r*f*f*n+t*f*f*f}function C(P,g,r,t,f){var n=1-f;return 3*(n*n*(g-P)+2*n*f*(r-g)+f*f*(t-r))}function S(P,g,r,t){var f=-3*P+9*g-9*r+3*t,n=6*P-12*g+6*r,a=3*g-3*P,s=[],e,u,l;if(Q(f,0))Q(n,0)||(e=-a/n,e>=0&&e<=1&&s.push(e));else{var v=n*n-4*f*a;Q(v,0)?s.push(-n/(2*f)):v>0&&(l=Math.sqrt(v),e=(-n+l)/(2*f),u=(-n-l)/(2*f),e>=0&&e<=1&&s.push(e),u>=0&&u<=1&&s.push(u))}return s}function Z(P,g,r,t,f,n,a,s,e){var u=H(P,r,f,a,e),l=H(g,t,n,s,e),v=z.pointAt(P,g,r,t,e),i=z.pointAt(r,t,f,n,e),y=z.pointAt(f,n,a,s,e),x=z.pointAt(v.x,v.y,i.x,i.y,e),o=z.pointAt(i.x,i.y,y.x,y.y,e);return[[P,g,v.x,v.y,x.x,x.y,u,l],[u,l,o.x,o.y,y.x,y.y,a,s]]}function J(P,g,r,t,f,n,a,s,e){if(e===0)return Y([P,r,f,a],[g,t,n,s]);var u=Z(P,g,r,t,f,n,a,s,.5),l=u[0],v=u[1];return l.push(e-1),v.push(e-1),J.apply(null,l)+J.apply(null,v)}var ht={extrema:S,box:function(P,g,r,t,f,n,a,s){for(var e=[P,a],u=[g,s],l=S(P,r,f,a),v=S(g,t,n,s),i=0;i<l.length;i++)e.push(H(P,r,f,a,l[i]));for(var i=0;i<v.length;i++)u.push(H(g,t,n,s,v[i]));return j(e,u)},length:function(P,g,r,t,f,n,a,s){return J(P,g,r,t,f,n,a,s,3)},nearestPoint:function(P,g,r,t,f,n,a,s,e,u,l){return W([P,r,f,a],[g,t,n,s],e,u,H,l)},pointDistance:function(P,g,r,t,f,n,a,s,e,u,l){var v=this.nearestPoint(P,g,r,t,f,n,a,s,e,u,l);return it(v.x,v.y,e,u)},interpolationAt:H,pointAt:function(P,g,r,t,f,n,a,s,e){return{x:H(P,r,f,a,e),y:H(g,t,n,s,e)}},divide:function(P,g,r,t,f,n,a,s,e){return Z(P,g,r,t,f,n,a,s,e)},tangentAngle:function(P,g,r,t,f,n,a,s,e){var u=C(P,r,f,a,e),l=C(g,t,n,s,e);return b(Math.atan2(l,u))}};function mt(P,g){var r=Math.abs(P);return g>0?r:r*-1}var Pt={box:function(P,g,r,t){return{x:P-r,y:g-t,width:r*2,height:t*2}},length:function(P,g,r,t){return Math.PI*(3*(r+t)-Math.sqrt((3*r+t)*(r+3*t)))},nearestPoint:function(P,g,r,t,f,n){var a=r,s=t;if(a===0||s===0)return{x:P,y:g};for(var e=f-P,u=n-g,l=Math.abs(e),v=Math.abs(u),i=a*a,y=s*s,x=Math.PI/4,o,h,d=0;d<4;d++){o=a*Math.cos(x),h=s*Math.sin(x);var B=(i-y)*Math.pow(Math.cos(x),3)/a,K=(y-i)*Math.pow(Math.sin(x),3)/s,tt=o-B,et=h-K,ft=l-B,ct=v-K,_t=Math.hypot(et,tt),At=Math.hypot(ct,ft),It=_t*Math.asin((tt*ct-et*ft)/(_t*At)),Xt=It/Math.sqrt(i+y-o*o-h*h);x+=Xt,x=Math.min(Math.PI/2,Math.max(0,x))}return{x:P+mt(o,e),y:g+mt(h,u)}},pointDistance:function(P,g,r,t,f,n){var a=this.nearestPoint(P,g,r,t,f,n);return it(a.x,a.y,f,n)},pointAt:function(P,g,r,t,f){var n=2*Math.PI*f;return{x:P+r*Math.cos(n),y:g+t*Math.sin(n)}},tangentAngle:function(P,g,r,t,f){var n=2*Math.PI*f,a=Math.atan2(t*Math.cos(n),-r*Math.sin(n));return b(a)}};function Yt(P,g,r,t,f,n,a,s){return-1*r*Math.cos(f)*Math.sin(s)-t*Math.sin(f)*Math.cos(s)}function Nt(P,g,r,t,f,n,a,s){return-1*r*Math.sin(f)*Math.sin(s)+t*Math.cos(f)*Math.cos(s)}function dt(P,g,r){return Math.atan(-g/P*Math.tan(r))}function pt(P,g,r){return Math.atan(g/(P*Math.tan(r)))}function xt(P,g,r,t,f,n){return r*Math.cos(f)*Math.cos(n)-t*Math.sin(f)*Math.sin(n)+P}function Et(P,g,r,t,f,n){return r*Math.sin(f)*Math.cos(n)+t*Math.cos(f)*Math.sin(n)+g}function wt(P,g,r,t){var f=Math.atan2(t*P,r*g);return(f+Math.PI*2)%(Math.PI*2)}function c(P,g,r){return{x:P*Math.cos(r),y:g*Math.sin(r)}}function A(P,g,r){var t=Math.cos(r),f=Math.sin(r);return[P*t-g*f,P*f+g*t]}var G={box:function(P,g,r,t,f,n,a){for(var s=dt(r,t,f),e=1/0,u=-1/0,l=[n,a],v=-Math.PI*2;v<=Math.PI*2;v+=Math.PI){var i=s+v;n<a?n<i&&i<a&&l.push(i):a<i&&i<n&&l.push(i)}for(var v=0;v<l.length;v++){var y=xt(P,g,r,t,f,l[v]);y<e&&(e=y),y>u&&(u=y)}for(var x=pt(r,t,f),o=1/0,h=-1/0,d=[n,a],v=-Math.PI*2;v<=Math.PI*2;v+=Math.PI){var B=x+v;n<a?n<B&&B<a&&d.push(B):a<B&&B<n&&d.push(B)}for(var v=0;v<d.length;v++){var K=Et(P,g,r,t,f,d[v]);K<o&&(o=K),K>h&&(h=K)}return{x:e,y:o,width:u-e,height:h-o}},length:function(P,g,r,t,f,n,a){},nearestPoint:function(P,g,r,t,f,n,a,s,e){var u=A(s-P,e-g,-f),l=u[0],v=u[1],i=Pt.nearestPoint(0,0,r,t,l,v),y=wt(r,t,i.x,i.y);y<n?i=c(r,t,n):y>a&&(i=c(r,t,a));var x=A(i.x,i.y,f);return{x:x[0]+P,y:x[1]+g}},pointDistance:function(P,g,r,t,f,n,a,s,e){var u=this.nearestPoint(P,g,r,t,s,e);return it(u.x,u.y,s,e)},pointAt:function(P,g,r,t,f,n,a,s){var e=(a-n)*s+n;return{x:xt(P,g,r,t,f,e),y:Et(P,g,r,t,f,e)}},tangentAngle:function(P,g,r,t,f,n,a,s){var e=(a-n)*s+n,u=Yt(P,g,r,t,f,n,a,e),l=Nt(P,g,r,t,f,n,a,e);return b(Math.atan2(l,u))}};function vt(P){for(var g=0,r=[],t=0;t<P.length-1;t++){var f=P[t],n=P[t+1],a=it(f[0],f[1],n[0],n[1]),s={from:f,to:n,length:a};r.push(s),g+=a}return{segments:r,totalLength:g}}function yt(P){if(P.length<2)return 0;for(var g=0,r=0;r<P.length-1;r++){var t=P[r],f=P[r+1];g+=it(t[0],t[1],f[0],f[1])}return g}function p(P,g){if(g>1||g<0||P.length<2)return null;var r=vt(P),t=r.segments,f=r.totalLength;if(f===0)return{x:P[0][0],y:P[0][1]};for(var n=0,a=null,s=0;s<t.length;s++){var e=t[s],u=e.from,l=e.to,v=e.length/f;if(g>=n&&g<=n+v){var i=(g-n)/v;a=z.pointAt(u[0],u[1],l[0],l[1],i);break}n+=v}return a}function M(P,g){if(g>1||g<0||P.length<2)return 0;for(var r=vt(P),t=r.segments,f=r.totalLength,n=0,a=0,s=0;s<t.length;s++){var e=t[s],u=e.from,l=e.to,v=e.length/f;if(g>=n&&g<=n+v){a=Math.atan2(l[1]-u[1],l[0]-u[0]);break}n+=v}return a}function L(P,g,r){for(var t=1/0,f=0;f<P.length-1;f++){var n=P[f],a=P[f+1],s=z.pointDistance(n[0],n[1],a[0],a[1],g,r);s<t&&(t=s)}return t}var T={box:function(P){for(var g=[],r=[],t=0;t<P.length;t++){var f=P[t];g.push(f[0]),r.push(f[1])}return j(g,r)},length:function(P){return yt(P)},pointAt:function(P,g){return p(P,g)},pointDistance:function(P,g,r){return L(P,g,r)},tangentAngle:function(P,g){return M(P,g)}};function $(P){var g=P.slice(0);return P.length&&g.push(P[0]),g}var ut={box:function(P){return T.box(P)},length:function(P){return yt($(P))},pointAt:function(P,g){return p($(P),g)},pointDistance:function(P,g,r){return L($(P),g,r)},tangentAngle:function(P,g){return M($(P),g)}}},65865:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return Et}});var V=k(92336),_=k(41142),it=k(10486),Q=k(57960),j=k(32935),X=k(33977),b=k(85786),N=k(47094),z=k(98667),q=/^l\s*\(\s*([\d.]+)\s*\)\s*(.*)/i,W=/^r\s*\(\s*([\d.]+)\s*,\s*([\d.]+)\s*,\s*([\d.]+)\s*\)\s*(.*)/i,Y=/[\d.]+:(#[^\s]+|[^)]+\))/gi;function D(wt){var c=wt.match(Y);if(!c)return"";var A="";return c.sort(function(G,vt){return G=G.split(":"),vt=vt.split(":"),Number(G[0])-Number(vt[0])}),(0,z.each)(c,function(G){G=G.split(":"),A+='<stop offset="'+G[0]+'" stop-color="'+G[1]+'"></stop>'}),A}function E(wt,c){var A=q.exec(wt),G=(0,z.mod)((0,z.toRadian)(parseFloat(A[1])),Math.PI*2),vt=A[2],yt,p;G>=0&&G<.5*Math.PI?(yt={x:0,y:0},p={x:1,y:1}):.5*Math.PI<=G&&G<Math.PI?(yt={x:1,y:0},p={x:0,y:1}):Math.PI<=G&&G<1.5*Math.PI?(yt={x:1,y:1},p={x:0,y:0}):(yt={x:0,y:1},p={x:1,y:0});var M=Math.tan(G),L=M*M,T=(p.x-yt.x+M*(p.y-yt.y))/(L+1)+yt.x,$=M*(p.x-yt.x+M*(p.y-yt.y))/(L+1)+yt.y;c.setAttribute("x1",yt.x),c.setAttribute("y1",yt.y),c.setAttribute("x2",T),c.setAttribute("y2",$),c.innerHTML=D(vt)}function O(wt,c){var A=W.exec(wt),G=parseFloat(A[1]),vt=parseFloat(A[2]),yt=parseFloat(A[3]),p=A[4];c.setAttribute("cx",G),c.setAttribute("cy",vt),c.setAttribute("r",yt),c.innerHTML=D(p)}var U=function(){function wt(c){this.cfg={};var A=null,G=(0,z.uniqueId)("gradient_");return c.toLowerCase()[0]==="l"?(A=(0,X.rS)("linearGradient"),E(c,A)):(A=(0,X.rS)("radialGradient"),O(c,A)),A.setAttribute("id",G),this.el=A,this.id=G,this.cfg=c,this}return wt.prototype.match=function(c,A){return this.cfg===A},wt}(),nt=U,st={shadowColor:"color",shadowOpacity:"opacity",shadowBlur:"blur",shadowOffsetX:"dx",shadowOffsetY:"dy"},H={x:"-40%",y:"-40%",width:"200%",height:"200%"},C=function(){function wt(c){this.type="filter",this.cfg={},this.type="filter";var A=(0,X.rS)("filter");return(0,z.each)(H,function(G,vt){A.setAttribute(vt,G)}),this.el=A,this.id=(0,z.uniqueId)("filter_"),this.el.id=this.id,this.cfg=c,this._parseShadow(c,A),this}return wt.prototype.match=function(c,A){if(this.type!==c)return!1;var G=!0,vt=this.cfg;return(0,z.each)(Object.keys(vt),function(yt){if(vt[yt]!==A[yt])return G=!1,!1}),G},wt.prototype.update=function(c,A){var G=this.cfg;return G[st[c]]=A,this._parseShadow(G,this.el),this},wt.prototype._parseShadow=function(c,A){var G=`<feDropShadow
      dx="`+(c.dx||0)+`"
      dy="`+(c.dy||0)+`"
      stdDeviation="`+(c.blur?c.blur/10:0)+`"
      flood-color="`+(c.color?c.color:"#000")+`"
      flood-opacity="`+(c.opacity?c.opacity:1)+`"
      />`;A.innerHTML=G},wt}(),S=C,Z=function(){function wt(c,A){this.cfg={};var G=(0,X.rS)("marker"),vt=(0,z.uniqueId)("marker_");G.setAttribute("id",vt);var yt=(0,X.rS)("path");yt.setAttribute("stroke",c.stroke||"none"),yt.setAttribute("fill",c.fill||"none"),G.appendChild(yt),G.setAttribute("overflow","visible"),G.setAttribute("orient","auto-start-reverse"),this.el=G,this.child=yt,this.id=vt;var p=c[A==="marker-start"?"startArrow":"endArrow"];return this.stroke=c.stroke||"#000",p===!0?this._setDefaultPath(A,yt):(this.cfg=p,this._setMarker(c.lineWidth,yt)),this}return wt.prototype.match=function(){return!1},wt.prototype._setDefaultPath=function(c,A){var G=this.el;A.setAttribute("d","M0,0 L"+10*Math.cos(Math.PI/6)+",5 L0,10"),G.setAttribute("refX",""+10*Math.cos(Math.PI/6)),G.setAttribute("refY","5")},wt.prototype._setMarker=function(c,A){var G=this.el,vt=this.cfg.path,yt=this.cfg.d;(0,z.isArray)(vt)&&(vt=vt.map(function(p){return p.join(" ")}).join("")),A.setAttribute("d",vt),G.appendChild(A),yt&&G.setAttribute("refX",""+yt/c)},wt.prototype.update=function(c){var A=this.child;A.attr?A.attr("fill",c):A.setAttribute("fill",c)},wt}(),J=Z,ht=function(){function wt(c){this.type="clip",this.cfg={};var A=(0,X.rS)("clipPath");this.el=A,this.id=(0,z.uniqueId)("clip_"),A.id=this.id;var G=c.cfg.el;return A.appendChild(G),this.cfg=c,this}return wt.prototype.match=function(){return!1},wt.prototype.remove=function(){var c=this.el;c.parentNode.removeChild(c)},wt}(),mt=ht,Pt=/^p\s*\(\s*([axyn])\s*\)\s*(.*)/i,Yt=function(){function wt(c){this.cfg={};var A=(0,X.rS)("pattern");A.setAttribute("patternUnits","userSpaceOnUse");var G=(0,X.rS)("image");A.appendChild(G);var vt=(0,z.uniqueId)("pattern_");A.id=vt,this.el=A,this.id=vt,this.cfg=c;var yt=Pt.exec(c),p=yt[2];G.setAttribute("href",p);var M=new Image;p.match(/^data:/i)||(M.crossOrigin="Anonymous"),M.src=p;function L(){A.setAttribute("width",""+M.width),A.setAttribute("height",""+M.height)}return M.complete?L():(M.onload=L,M.src=M.src),this}return wt.prototype.match=function(c,A){return this.cfg===A},wt}(),Nt=Yt,dt=function(){function wt(c){var A=(0,X.rS)("defs"),G=(0,z.uniqueId)("defs_");A.id=G,c.appendChild(A),this.children=[],this.defaultArrow={},this.el=A,this.canvas=c}return wt.prototype.find=function(c,A){for(var G=this.children,vt=null,yt=0;yt<G.length;yt++)if(G[yt].match(c,A)){vt=G[yt].id;break}return vt},wt.prototype.findById=function(c){for(var A=this.children,G=null,vt=0;vt<A.length;vt++)if(A[vt].id===c){G=A[vt];break}return G},wt.prototype.add=function(c){this.children.push(c),c.canvas=this.canvas,c.parent=this},wt.prototype.getDefaultArrow=function(c,A){var G=c.stroke||c.strokeStyle;if(this.defaultArrow[G])return this.defaultArrow[G].id;var vt=new J(c,A);return this.defaultArrow[G]=vt,this.el.appendChild(vt.el),this.add(vt),vt.id},wt.prototype.addGradient=function(c){var A=new nt(c);return this.el.appendChild(A.el),this.add(A),A.id},wt.prototype.addArrow=function(c,A){var G=new J(c,A);return this.el.appendChild(G.el),this.add(G),G.id},wt.prototype.addShadow=function(c){var A=new S(c);return this.el.appendChild(A.el),this.add(A),A.id},wt.prototype.addPattern=function(c){var A=new Nt(c);return this.el.appendChild(A.el),this.add(A),A.id},wt.prototype.addClip=function(c){var A=new mt(c);return this.el.appendChild(A.el),this.add(A),A.id},wt}(),pt=dt,xt=function(wt){(0,V.__extends)(c,wt);function c(A){return wt.call(this,(0,V.__assign)((0,V.__assign)({},A),{autoDraw:!0,renderer:"svg"}))||this}return c.prototype.getShapeBase=function(){return b},c.prototype.getGroupBase=function(){return N.Z},c.prototype.getShape=function(A,G,vt){var yt=vt.target||vt.srcElement;if(!it.CX[yt.tagName]){for(var p=yt.parentNode;p&&!it.CX[p.tagName];)p=p.parentNode;yt=p}return this.find(function(M){return M.get("el")===yt})},c.prototype.createDom=function(){var A=(0,X.rS)("svg"),G=new pt(A);return A.setAttribute("width",""+this.get("width")),A.setAttribute("height",""+this.get("height")),this.set("context",G),A},c.prototype.onCanvasChange=function(A){var G=this.get("context"),vt=this.get("el");if(A==="sort"){var yt=this.get("children");yt&&yt.length&&(0,X.ZL)(this,function(M,L){return yt.indexOf(M)-yt.indexOf(L)?1:0})}else if(A==="clear"){if(vt){vt.innerHTML="";var p=G.el;p.innerHTML="",vt.appendChild(p)}}else A==="matrix"?(0,j.B2)(this):A==="clip"?(0,j.r7)(this,G):A==="changeSize"&&(vt.setAttribute("width",""+this.get("width")),vt.setAttribute("height",""+this.get("height")))},c.prototype.draw=function(){var A=this.get("context"),G=this.getChildren();(0,j.r7)(this,A),G.length&&(0,Q.N)(A,G)},c}(_.AbstractCanvas),Et=xt},10486:function(Lt,Mt,k){"use strict";k.d(Mt,{A8:function(){return _},CX:function(){return V}});var V={rect:"path",circle:"circle",line:"line",path:"path",marker:"path",text:"text",polyline:"polyline",polygon:"polygon",image:"image",ellipse:"ellipse",dom:"foreignObject"},_={opacity:"opacity",fillStyle:"fill",fill:"fill",fillOpacity:"fill-opacity",strokeStyle:"stroke",strokeOpacity:"stroke-opacity",stroke:"stroke",x:"x",y:"y",r:"r",rx:"rx",ry:"ry",width:"width",height:"height",x1:"x1",x2:"x2",y1:"y1",y2:"y2",lineCap:"stroke-linecap",lineJoin:"stroke-linejoin",lineWidth:"stroke-width",lineDash:"stroke-dasharray",lineDashOffset:"stroke-dashoffset",miterLimit:"stroke-miterlimit",font:"font",fontSize:"font-size",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",fontFamily:"font-family",startArrow:"marker-start",endArrow:"marker-end",path:"d",class:"class",id:"id",style:"style",preserveAspectRatio:"preserveAspectRatio"},it=null},47094:function(Lt,Mt,k){"use strict";var V=k(92336),_=k(41142),it=k(98667),Q=k(85786),j=k(57960),X=k(32935),b=k(10486),N=k(33977),z=function(q){(0,V.__extends)(W,q);function W(){return q!==null&&q.apply(this,arguments)||this}return W.prototype.isEntityGroup=function(){return!0},W.prototype.createDom=function(){var Y=(0,N.rS)("g");this.set("el",Y);var D=this.getParent();if(D){var E=D.get("el");E||(E=D.createDom(),D.set("el",E)),E.appendChild(Y)}return Y},W.prototype.afterAttrsChange=function(Y){q.prototype.afterAttrsChange.call(this,Y);var D=this.get("canvas");if(D&&D.get("autoDraw")){var E=D.get("context");this.createPath(E,Y)}},W.prototype.onCanvasChange=function(Y){(0,j.m)(this,Y)},W.prototype.getShapeBase=function(){return Q},W.prototype.getGroupBase=function(){return W},W.prototype.draw=function(Y){var D=this.getChildren(),E=this.get("el");this.get("destroyed")?E&&E.parentNode.removeChild(E):(E||this.createDom(),(0,X.r7)(this,Y),this.createPath(Y),D.length&&(0,j.N)(Y,D))},W.prototype.createPath=function(Y,D){var E=this.attr(),O=this.get("el");(0,it.each)(D||E,function(U,nt){b.A8[nt]&&O.setAttribute(b.A8[nt],U)}),(0,X.B2)(this)},W}(_.AbstractGroup);Mt.Z=z},98685:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{Canvas:function(){return j.Z},Group:function(){return X.Z},Shape:function(){return V},version:function(){return b}});var V=k(85786),_=k(41142),it={};for(var Q in _)["default","Canvas","Group","Shape","version"].indexOf(Q)<0&&(it[Q]=function(N){return _[N]}.bind(0,Q));k.d(Mt,it);var j=k(65865),X=k(47094),b="0.5.6"},85786:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{Base:function(){return q},Circle:function(){return D},Dom:function(){return O},Ellipse:function(){return nt},Image:function(){return H},Line:function(){return Z},Marker:function(){return Pt},Path:function(){return Nt},Polygon:function(){return pt},Polyline:function(){return Et},Rect:function(){return yt},Text:function(){return P}});var V={};k.r(V),k.d(V,{Base:function(){return q},Circle:function(){return D},Dom:function(){return O},Ellipse:function(){return nt},Image:function(){return H},Line:function(){return Z},Marker:function(){return Pt},Path:function(){return Nt},Polygon:function(){return pt},Polyline:function(){return Et},Rect:function(){return yt},Text:function(){return P}});var _=k(92336),it=k(41142),Q=k(32935),j=k(33977),X=k(57960),b=k(10486),N=k(47094),z=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="svg",t.canFill=!1,t.canStroke=!1,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{lineWidth:1,lineAppendWidth:0,strokeOpacity:1,fillOpacity:1})},r.prototype.afterAttrsChange=function(t){g.prototype.afterAttrsChange.call(this,t);var f=this.get("canvas");if(f&&f.get("autoDraw")){var n=f.get("context");this.draw(n,t)}},r.prototype.getShapeBase=function(){return V},r.prototype.getGroupBase=function(){return N.Z},r.prototype.onCanvasChange=function(t){(0,X.m)(this,t)},r.prototype.calculateBBox=function(){var t=this.get("el"),f=null;if(t)f=t.getBBox();else{var n=(0,it.getBBoxMethod)(this.get("type"));n&&(f=n(this))}if(f){var a=f.x,s=f.y,e=f.width,u=f.height,l=this.getHitLineWidth(),v=l/2,i=a-v,y=s-v,x=a+e+v,o=s+u+v;return{x:i,y,minX:i,minY:y,maxX:x,maxY:o,width:e+l,height:u+l}}return{x:0,y:0,minX:0,minY:0,maxX:0,maxY:0,width:0,height:0}},r.prototype.isFill=function(){var t=this.attr(),f=t.fill,n=t.fillStyle;return(f||n||this.isClipShape())&&this.canFill},r.prototype.isStroke=function(){var t=this.attr(),f=t.stroke,n=t.strokeStyle;return(f||n)&&this.canStroke},r.prototype.draw=function(t,f){var n=this.get("el");this.get("destroyed")?n&&n.parentNode.removeChild(n):(n||(0,j.WV)(this),(0,Q.r7)(this,t),this.createPath(t,f),this.shadow(t,f),this.strokeAndFill(t,f),this.transform(f))},r.prototype.createPath=function(t,f){},r.prototype.strokeAndFill=function(t,f){var n=f||this.attr(),a=n.fill,s=n.fillStyle,e=n.stroke,u=n.strokeStyle,l=n.fillOpacity,v=n.strokeOpacity,i=n.lineWidth,y=this.get("el");this.canFill&&(f?"fill"in n?this._setColor(t,"fill",a):"fillStyle"in n&&this._setColor(t,"fill",s):this._setColor(t,"fill",a||s),l&&y.setAttribute(b.A8.fillOpacity,l)),this.canStroke&&i>0&&(f?"stroke"in n?this._setColor(t,"stroke",e):"strokeStyle"in n&&this._setColor(t,"stroke",u):this._setColor(t,"stroke",e||u),v&&y.setAttribute(b.A8.strokeOpacity,v),i&&y.setAttribute(b.A8.lineWidth,i))},r.prototype._setColor=function(t,f,n){var a=this.get("el");if(!n){a.setAttribute(b.A8[f],"none");return}if(n=n.trim(),/^[r,R,L,l]{1}[\s]*\(/.test(n)){var s=t.find("gradient",n);s||(s=t.addGradient(n)),a.setAttribute(b.A8[f],"url(#"+s+")")}else if(/^[p,P]{1}[\s]*\(/.test(n)){var s=t.find("pattern",n);s||(s=t.addPattern(n)),a.setAttribute(b.A8[f],"url(#"+s+")")}else a.setAttribute(b.A8[f],n)},r.prototype.shadow=function(t,f){var n=this.attr(),a=f||n,s=a.shadowOffsetX,e=a.shadowOffsetY,u=a.shadowBlur,l=a.shadowColor;(s||e||u||l)&&(0,Q.kr)(this,t)},r.prototype.transform=function(t){var f=this.attr(),n=(t||f).matrix;n&&(0,Q.B2)(this)},r.prototype.isInShape=function(t,f){return this.isPointInPath(t,f)},r.prototype.isPointInPath=function(t,f){var n=this.get("el"),a=this.get("canvas"),s=a.get("el").getBoundingClientRect(),e=t+s.left,u=f+s.top,l=document.elementFromPoint(e,u);return!!(l&&l.isEqualNode(n))},r.prototype.getHitLineWidth=function(){var t=this.attrs,f=t.lineWidth,n=t.lineAppendWidth;return this.isStroke()?f+n:0},r}(it.AbstractShape),q=z,W=k(98667),Y=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="circle",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x:0,y:0,r:0})},r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");(0,W.each)(f||n,function(s,e){e==="x"||e==="y"?a.setAttribute("c"+e,s):b.A8[e]&&a.setAttribute(b.A8[e],s)})},r}(q),D=Y,E=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="dom",t.canFill=!1,t.canStroke=!1,t}return r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");if((0,W.each)(f||n,function(l,v){b.A8[v]&&a.setAttribute(b.A8[v],l)}),typeof n.html=="function"){var s=n.html.call(this,n);if(s instanceof Element||s instanceof HTMLDocument){for(var e=a.childNodes,u=e.length-1;u>=0;u--)a.removeChild(e[u]);a.appendChild(s)}else a.innerHTML=s}else a.innerHTML=n.html},r}(q),O=E,U=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="ellipse",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x:0,y:0,rx:0,ry:0})},r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");(0,W.each)(f||n,function(s,e){e==="x"||e==="y"?a.setAttribute("c"+e,s):b.A8[e]&&a.setAttribute(b.A8[e],s)})},r}(q),nt=U,st=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="image",t.canFill=!1,t.canStroke=!1,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x:0,y:0,width:0,height:0})},r.prototype.createPath=function(t,f){var n=this,a=this.attr(),s=this.get("el");(0,W.each)(f||a,function(e,u){u==="img"?n._setImage(a.img):b.A8[u]&&s.setAttribute(b.A8[u],e)})},r.prototype.setAttr=function(t,f){this.attrs[t]=f,t==="img"&&this._setImage(f)},r.prototype._setImage=function(t){var f=this.attr(),n=this.get("el");if((0,W.isString)(t))n.setAttribute("href",t);else if(t instanceof window.Image)f.width||(n.setAttribute("width",t.width),this.attr("width",t.width)),f.height||(n.setAttribute("height",t.height),this.attr("height",t.height)),n.setAttribute("href",t.src);else if(t instanceof HTMLElement&&(0,W.isString)(t.nodeName)&&t.nodeName.toUpperCase()==="CANVAS")n.setAttribute("href",t.toDataURL());else if(t instanceof ImageData){var a=document.createElement("canvas");a.setAttribute("width",""+t.width),a.setAttribute("height",""+t.height),a.getContext("2d").putImageData(t,0,0),f.width||(n.setAttribute("width",""+t.width),this.attr("width",t.width)),f.height||(n.setAttribute("height",""+t.height),this.attr("height",t.height)),n.setAttribute("href",a.toDataURL())}},r}(q),H=st,C=k(22824),S=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="line",t.canFill=!1,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x1:0,y1:0,x2:0,y2:0,startArrow:!1,endArrow:!1})},r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");(0,W.each)(f||n,function(s,e){if(e==="startArrow"||e==="endArrow")if(s){var u=(0,W.isObject)(s)?t.addArrow(n,b.A8[e]):t.getDefaultArrow(n,b.A8[e]);a.setAttribute(b.A8[e],"url(#"+u+")")}else a.removeAttribute(b.A8[e]);else b.A8[e]&&a.setAttribute(b.A8[e],s)})},r.prototype.getTotalLength=function(){var t=this.attr(),f=t.x1,n=t.y1,a=t.x2,s=t.y2;return C.x1.length(f,n,a,s)},r.prototype.getPoint=function(t){var f=this.attr(),n=f.x1,a=f.y1,s=f.x2,e=f.y2;return C.x1.pointAt(n,a,s,e,t)},r}(q),Z=S,J={circle:function(g,r,t){return[["M",g,r],["m",-t,0],["a",t,t,0,1,0,t*2,0],["a",t,t,0,1,0,-t*2,0]]},square:function(g,r,t){return[["M",g-t,r-t],["L",g+t,r-t],["L",g+t,r+t],["L",g-t,r+t],["Z"]]},diamond:function(g,r,t){return[["M",g-t,r],["L",g,r-t],["L",g+t,r],["L",g,r+t],["Z"]]},triangle:function(g,r,t){var f=t*Math.sin(.3333333333333333*Math.PI);return[["M",g-t,r+f],["L",g,r-f],["L",g+t,r+f],["z"]]},triangleDown:function(g,r,t){var f=t*Math.sin(.3333333333333333*Math.PI);return[["M",g-t,r-f],["L",g+t,r-f],["L",g,r+f],["Z"]]}},ht={get:function(g){return J[g]},register:function(g,r){J[g]=r},remove:function(g){delete J[g]},getAll:function(){return J}},mt=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="marker",t.canFill=!0,t.canStroke=!0,t}return r.prototype.createPath=function(t){var f=this.get("el");f.setAttribute("d",this._assembleMarker())},r.prototype._assembleMarker=function(){var t=this._getPath();return(0,W.isArray)(t)?t.map(function(f){return f.join(" ")}).join(""):t},r.prototype._getPath=function(){var t=this.attr(),f=t.x,n=t.y,a=t.r||t.radius,s=t.symbol||"circle",e;return(0,W.isFunction)(s)?e=s:e=ht.get(s),e?e(f,n,a):(console.warn(e+" symbol is not exist."),null)},r.symbolsFactory=ht,r}(q),Pt=mt,Yt=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="path",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{startArrow:!1,endArrow:!1})},r.prototype.createPath=function(t,f){var n=this,a=this.attr(),s=this.get("el");(0,W.each)(f||a,function(e,u){if(u==="path"&&(0,W.isArray)(e))s.setAttribute("d",n._formatPath(e));else if(u==="startArrow"||u==="endArrow")if(e){var l=(0,W.isObject)(e)?t.addArrow(a,b.A8[u]):t.getDefaultArrow(a,b.A8[u]);s.setAttribute(b.A8[u],"url(#"+l+")")}else s.removeAttribute(b.A8[u]);else b.A8[u]&&s.setAttribute(b.A8[u],e)})},r.prototype._formatPath=function(t){var f=t.map(function(n){return n.join(" ")}).join("");return~f.indexOf("NaN")?"":f},r.prototype.getTotalLength=function(){var t=this.get("el");return t?t.getTotalLength():null},r.prototype.getPoint=function(t){var f=this.get("el"),n=this.getTotalLength();if(n===0)return null;var a=f?f.getPointAtLength(t*n):null;return a?{x:a.x,y:a.y}:null},r}(q),Nt=Yt,dt=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="polygon",t.canFill=!0,t.canStroke=!0,t}return r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");(0,W.each)(f||n,function(s,e){e==="points"&&(0,W.isArray)(s)&&s.length>=2?a.setAttribute("points",s.map(function(u){return u[0]+","+u[1]}).join(" ")):b.A8[e]&&a.setAttribute(b.A8[e],s)})},r}(q),pt=dt,xt=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="polyline",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{startArrow:!1,endArrow:!1})},r.prototype.onAttrChange=function(t,f,n){g.prototype.onAttrChange.call(this,t,f,n),["points"].indexOf(t)!==-1&&this._resetCache()},r.prototype._resetCache=function(){this.set("totalLength",null),this.set("tCache",null)},r.prototype.createPath=function(t,f){var n=this.attr(),a=this.get("el");(0,W.each)(f||n,function(s,e){e==="points"&&(0,W.isArray)(s)&&s.length>=2?a.setAttribute("points",s.map(function(u){return u[0]+","+u[1]}).join(" ")):b.A8[e]&&a.setAttribute(b.A8[e],s)})},r.prototype.getTotalLength=function(){var t=this.attr().points,f=this.get("totalLength");return(0,W.isNil)(f)?(this.set("totalLength",C.aH.length(t)),this.get("totalLength")):f},r.prototype.getPoint=function(t){var f=this.attr().points,n=this.get("tCache");n||(this._setTcache(),n=this.get("tCache"));var a,s;return(0,W.each)(n,function(e,u){t>=e[0]&&t<=e[1]&&(a=(t-e[0])/(e[1]-e[0]),s=u)}),C.x1.pointAt(f[s][0],f[s][1],f[s+1][0],f[s+1][1],a)},r.prototype._setTcache=function(){var t=this.attr().points;if(!(!t||t.length===0)){var f=this.getTotalLength();if(!(f<=0)){var n=0,a=[],s,e;(0,W.each)(t,function(u,l){t[l+1]&&(s=[],s[0]=n/f,e=C.x1.length(u[0],u[1],t[l+1][0],t[l+1][1]),n+=e,s[1]=n/f,a.push(s))}),this.set("tCache",a)}}},r.prototype.getStartTangent=function(){var t=this.attr().points,f=[];return f.push([t[1][0],t[1][1]]),f.push([t[0][0],t[0][1]]),f},r.prototype.getEndTangent=function(){var t=this.attr().points,f=t.length-1,n=[];return n.push([t[f-1][0],t[f-1][1]]),n.push([t[f][0],t[f][1]]),n},r}(q),Et=xt,wt=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/gi,c=/[^\s,]+/gi;function A(g){var r=0,t=0,f=0,n=0;return(0,W.isArray)(g)?g.length===1?r=t=f=n=g[0]:g.length===2?(r=f=g[0],t=n=g[1]):g.length===3?(r=g[0],t=n=g[1],f=g[2]):(r=g[0],t=g[1],f=g[2],n=g[3]):r=t=f=n=g,{r1:r,r2:t,r3:f,r4:n}}function G(g){if(g=g||[],isArray(g))return g;if(isString(g))return g=g.match(wt),each(g,function(r,t){if(r=r.match(c),r[0].length>1){var f=r[0].charAt(0);r.splice(1,0,r[0].substr(1)),r[0]=f}each(r,function(n,a){isNaN(n)||(r[a]=+n)}),g[t]=r}),g}var vt=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="rect",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x:0,y:0,width:0,height:0,radius:0})},r.prototype.createPath=function(t,f){var n=this,a=this.attr(),s=this.get("el"),e=!1,u=["x","y","width","height","radius"];(0,W.each)(f||a,function(l,v){u.indexOf(v)!==-1&&!e?(s.setAttribute("d",n._assembleRect(a)),e=!0):u.indexOf(v)===-1&&b.A8[v]&&s.setAttribute(b.A8[v],l)})},r.prototype._assembleRect=function(t){var f=t.x,n=t.y,a=t.width,s=t.height,e=t.radius;if(!e)return"M "+f+","+n+" l "+a+",0 l 0,"+s+" l"+-a+" 0 z";var u=A(e);(0,W.isArray)(e)?e.length===1?u.r1=u.r2=u.r3=u.r4=e[0]:e.length===2?(u.r1=u.r3=e[0],u.r2=u.r4=e[1]):e.length===3?(u.r1=e[0],u.r2=u.r4=e[1],u.r3=e[2]):(u.r1=e[0],u.r2=e[1],u.r3=e[2],u.r4=e[3]):u.r1=u.r2=u.r3=u.r4=e;var l=[["M "+(f+u.r1)+","+n],["l "+(a-u.r1-u.r2)+",0"],["a "+u.r2+","+u.r2+",0,0,1,"+u.r2+","+u.r2],["l 0,"+(s-u.r2-u.r3)],["a "+u.r3+","+u.r3+",0,0,1,"+-u.r3+","+u.r3],["l "+(u.r3+u.r4-a)+",0"],["a "+u.r4+","+u.r4+",0,0,1,"+-u.r4+","+-u.r4],["l 0,"+(u.r4+u.r1-s)],["a "+u.r1+","+u.r1+",0,0,1,"+u.r1+","+-u.r1],["z"]];return l.join(" ")},r}(q),yt=vt,p=k(79935),M=.3,L={top:"before-edge",middle:"central",bottom:"after-edge",alphabetic:"baseline",hanging:"hanging"},T={top:"text-before-edge",middle:"central",bottom:"text-after-edge",alphabetic:"alphabetic",hanging:"hanging"},$={left:"left",start:"left",center:"middle",right:"end",end:"end"},ut=function(g){(0,_.__extends)(r,g);function r(){var t=g!==null&&g.apply(this,arguments)||this;return t.type="text",t.canFill=!0,t.canStroke=!0,t}return r.prototype.getDefaultAttrs=function(){var t=g.prototype.getDefaultAttrs.call(this);return(0,_.__assign)((0,_.__assign)({},t),{x:0,y:0,text:null,fontSize:12,fontFamily:"sans-serif",fontStyle:"normal",fontWeight:"normal",fontVariant:"normal",textAlign:"start",textBaseline:"bottom"})},r.prototype.createPath=function(t,f){var n=this,a=this.attr(),s=this.get("el");this._setFont(),(0,W.each)(f||a,function(e,u){u==="text"?n._setText(""+e):u==="matrix"&&e?(0,Q.B2)(n):b.A8[u]&&s.setAttribute(b.A8[u],e)}),s.setAttribute("paint-order","stroke"),s.setAttribute("style","stroke-linecap:butt; stroke-linejoin:miter;")},r.prototype._setFont=function(){var t=this.get("el"),f=this.attr(),n=f.textBaseline,a=f.textAlign,s=(0,p.qY)();s&&s.name==="firefox"?t.setAttribute("dominant-baseline",T[n]||"alphabetic"):t.setAttribute("alignment-baseline",L[n]||"baseline"),t.setAttribute("text-anchor",$[a]||"left")},r.prototype._setText=function(t){var f=this.get("el"),n=this.attr(),a=n.x,s=n.textBaseline,e=s===void 0?"bottom":s;if(!t)f.innerHTML="";else if(~t.indexOf(`
`)){var u=t.split(`
`),l=u.length-1,v="";(0,W.each)(u,function(i,y){y===0?e==="alphabetic"?v+='<tspan x="'+a+'" dy="'+-l+'em">'+i+"</tspan>":e==="top"?v+='<tspan x="'+a+'" dy="0.9em">'+i+"</tspan>":e==="middle"?v+='<tspan x="'+a+'" dy="'+-(l-1)/2+'em">'+i+"</tspan>":e==="bottom"?v+='<tspan x="'+a+'" dy="-'+(l+M)+'em">'+i+"</tspan>":e==="hanging"&&(v+='<tspan x="'+a+'" dy="'+(-(l-1)-M)+'em">'+i+"</tspan>"):v+='<tspan x="'+a+'" dy="1em">'+i+"</tspan>"}),f.innerHTML=v}else f.innerHTML=t},r}(q),P=ut},33977:function(Lt,Mt,k){"use strict";k.d(Mt,{Ao:function(){return X},WV:function(){return Q},ZL:function(){return j},rS:function(){return it}});var V=k(98667),_=k(10486);function it(b){return document.createElementNS("http://www.w3.org/2000/svg",b)}function Q(b){var N=_.CX[b.type],z=b.getParent();if(!N)throw new Error("the type "+b.type+" is not supported by svg");var q=it(N);if(b.get("id")&&(q.id=b.get("id")),b.set("el",q),b.set("attrs",{}),z){var W=z.get("el");W||(W=z.createDom(),z.set("el",W)),W.appendChild(q)}return q}function j(b,N){var z=b.get("el"),q=(0,V.toArray)(z.children).sort(N),W=document.createDocumentFragment();q.forEach(function(Y){W.appendChild(Y)}),z.appendChild(W)}function X(b,N){var z=b.parentNode,q=Array.from(z.childNodes).filter(function(E){return E.nodeType===1&&E.nodeName.toLowerCase()!=="defs"}),W=q[N],Y=q.indexOf(b);if(W){if(Y>N)z.insertBefore(b,W);else if(Y<N){var D=q[N+1];D?z.insertBefore(b,D):z.appendChild(b)}}else z.appendChild(b)}},57960:function(Lt,Mt,k){"use strict";k.d(Mt,{N:function(){return it},m:function(){return Q}});var V=k(32935),_=k(33977);function it(j,X){X.forEach(function(b){b.draw(j)})}function Q(j,X){var b=j.get("canvas");if(b&&b.get("autoDraw")){var N=b.get("context"),z=j.getParent(),q=z?z.getChildren():[b],W=j.get("el");if(X==="remove"){var Y=j.get("isClipShape");if(Y){var D=W&&W.parentNode,E=D&&D.parentNode;D&&E&&E.removeChild(D)}else W&&W.parentNode&&W.parentNode.removeChild(W)}else if(X==="show")W.setAttribute("visibility","visible");else if(X==="hide")W.setAttribute("visibility","hidden");else if(X==="zIndex")(0,_.Ao)(W,q.indexOf(j));else if(X==="sort"){var O=j.get("children");O&&O.length&&(0,_.ZL)(j,function(U,nt){return O.indexOf(U)-O.indexOf(nt)?1:0})}else X==="clear"?W&&(W.innerHTML=""):X==="matrix"?(0,V.B2)(j):X==="clip"?(0,V.r7)(j,N):X==="attr"||X==="add"&&j.draw(N)}}},32935:function(Lt,Mt,k){"use strict";k.d(Mt,{B2:function(){return it},kr:function(){return _},r7:function(){return Q}});var V=k(33977);function _(j,X){var b=j.cfg.el,N=j.attr(),z={dx:N.shadowOffsetX,dy:N.shadowOffsetY,blur:N.shadowBlur,color:N.shadowColor};if(!z.dx&&!z.dy&&!z.blur&&!z.color)b.removeAttribute("filter");else{var q=X.find("filter",z);q||(q=X.addShadow(z)),b.setAttribute("filter","url(#"+q+")")}}function it(j){var X=j.attr().matrix;if(X){for(var b=j.cfg.el,N=[],z=0;z<9;z+=3)N.push(X[z]+","+X[z+1]);N=N.join(","),N.indexOf("NaN")===-1?b.setAttribute("transform","matrix("+N+")"):console.warn("invalid matrix:",X)}}function Q(j,X){var b=j.getClip(),N=j.get("el");if(!b)N.removeAttribute("clip-path");else if(b&&!N.hasAttribute("clip-path")){(0,V.WV)(b),b.createPath(X);var z=X.addClip(b);N.setAttribute("clip-path","url(#"+z+")")}}},82867:function(Lt,Mt,k){"use strict";k.d(Mt,{Dg:function(){return z},lh:function(){return j},m$:function(){return it},vs:function(){return b},zu:function(){return Q}});var V=k(96661),_=k(69341);function it(W,Y,D){var E=[0,0,0,0,0,0,0,0,0];return V.fromTranslation(E,D),V.multiply(W,E,Y)}function Q(W,Y,D){var E=[0,0,0,0,0,0,0,0,0];return V.fromRotation(E,D),V.multiply(W,E,Y)}function j(W,Y,D){var E=[0,0,0,0,0,0,0,0,0];return V.fromScaling(E,D),V.multiply(W,E,Y)}function X(W,Y,D){return V.multiply(W,D,Y)}function b(W,Y){for(var D=W?[].concat(W):[1,0,0,0,1,0,0,0,1],E=0,O=Y.length;E<O;E++){var U=Y[E];switch(U[0]){case"t":it(D,D,[U[1],U[2]]);break;case"s":j(D,D,[U[1],U[2]]);break;case"r":Q(D,D,U[1]);break;case"m":X(D,D,U[1]);break;default:break}}return D}function N(W,Y){return W[0]*Y[1]-Y[0]*W[1]}function z(W,Y,D){var E=_.angle(W,Y),O=N(W,Y)>=0;return D?O?Math.PI*2-E:E:O?E:Math.PI*2-E}function q(W,Y,D){return D?(W[0]=Y[1],W[1]=-1*Y[0]):(W[0]=-1*Y[1],W[1]=Y[0]),W}},72461:function(Lt,Mt,k){"use strict";k.d(Mt,{e9:function(){return z},Wq:function(){return x},tr:function(){return U},wb:function(){return H},zx:function(){return t}});var V=k(98667),_=/[MLHVQTCSAZ]([^MLHVQTCSAZ]*)/ig,it=/[^\s\,]+/ig;function Q(o){var h=o||[];if((0,V.isArray)(h))return h;if((0,V.isString)(h))return h=h.match(_),(0,V.each)(h,function(d,B){if(d=d.match(it),d[0].length>1){var K=d[0].charAt(0);d.splice(1,0,d[0].substr(1)),d[0]=K}(0,V.each)(d,function(tt,et){isNaN(tt)||(d[et]=+tt)}),h[B]=d}),h}var j=Q,X=k(69341);function b(o,h,d,B){var K=[],tt=!!B,et,ft,ct,_t,At,It,Xt;if(tt){ct=B[0],_t=B[1];for(var St=0,kt=o.length;St<kt;St+=1){var Rt=o[St];ct=X.min([0,0],ct,Rt),_t=X.max([0,0],_t,Rt)}}for(var St=0,Wt=o.length;St<Wt;St+=1){var Rt=o[St];if(St===0&&!d)Xt=Rt;else if(St===Wt-1&&!d)It=Rt,K.push(Xt),K.push(It);else{var Ot=[St?St-1:Wt-1,St-1][d?0:1];et=o[Ot],ft=o[d?(St+1)%Wt:St+1];var Ft=[0,0];Ft=X.sub(Ft,ft,et),Ft=X.scale(Ft,Ft,h);var Kt=X.distance(Rt,et),Zt=X.distance(Rt,ft),Gt=Kt+Zt;Gt!==0&&(Kt/=Gt,Zt/=Gt);var Vt=X.scale([0,0],Ft,-Kt),Qt=X.scale([0,0],Ft,Zt);It=X.add([0,0],Rt,Vt),At=X.add([0,0],Rt,Qt),At=X.min([0,0],At,X.max([0,0],ft,Rt)),At=X.max([0,0],At,X.min([0,0],ft,Rt)),Vt=X.sub([0,0],At,Rt),Vt=X.scale([0,0],Vt,-Kt/Zt),It=X.add([0,0],Rt,Vt),It=X.min([0,0],It,X.max([0,0],et,Rt)),It=X.max([0,0],It,X.min([0,0],et,Rt)),Qt=X.sub([0,0],Rt,It),Qt=X.scale([0,0],Qt,Zt/Kt),At=X.add([0,0],Rt,Qt),tt&&(It=X.max([0,0],It,ct),It=X.min([0,0],It,_t),At=X.max([0,0],At,ct),At=X.min([0,0],At,_t)),K.push(Xt),K.push(It),Xt=At}}return d&&K.push(K.shift()),K}function N(o,h,d){h===void 0&&(h=!1),d===void 0&&(d=[[0,0],[1,1]]);for(var B=!!h,K=[],tt=0,et=o.length;tt<et;tt+=2)K.push([o[tt],o[tt+1]]);for(var ft=b(K,.4,B,d),ct=K.length,_t=[],At,It,Xt,tt=0;tt<ct-1;tt+=1)At=ft[tt*2],It=ft[tt*2+1],Xt=K[tt+1],_t.push(["C",At[0],At[1],It[0],It[1],Xt[0],Xt[1]]);return B&&(At=ft[ct],It=ft[ct+1],Xt=K[0],_t.push(["C",At[0],At[1],It[0],It[1],Xt[0],Xt[1]])),_t}var z=N;function q(o,h,d){var B=null,K=d;return h<K&&(K=h,B="add"),o<K&&(K=o,B="del"),{type:B,min:K}}var W=function(o,h){var d=o.length,B=h.length,K,tt,et=0;if(d===0||B===0)return null;for(var ft=[],ct=0;ct<=d;ct++)ft[ct]=[],ft[ct][0]={min:ct};for(var _t=0;_t<=B;_t++)ft[0][_t]={min:_t};for(var ct=1;ct<=d;ct++){K=o[ct-1];for(var _t=1;_t<=B;_t++){tt=h[_t-1],isEqual(K,tt)?et=0:et=1;var At=ft[ct-1][_t].min+1,It=ft[ct][_t-1].min+1,Xt=ft[ct-1][_t-1].min+et;ft[ct][_t]=q(At,It,Xt)}}return ft};function Y(o,h){var d=W(o,h),B=o.length,K=h.length,tt=[],et=1,ft=1;if(d[B][K]!==B){for(var ct=1;ct<=B;ct++){var _t=d[ct][ct].min;ft=ct;for(var At=et;At<=K;At++)d[ct][At].min<_t&&(_t=d[ct][At].min,ft=At);et=ft,d[ct][et].type&&tt.push({index:ct-1,type:d[ct][et].type})}for(var ct=tt.length-1;ct>=0;ct--)et=tt[ct].index,tt[ct].type==="add"?o.splice(et,0,[].concat(o[et])):o.splice(et,1)}if(B=o.length,B<K)for(var ct=0;ct<K-B;ct++)o[B-1][0]==="z"||o[B-1][0]==="Z"?o.splice(B-2,0,o[B-2]):o.push(o[B-1]);return o}var D=`	
\v\f\r \xA0\u1680\u180E\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029`,E=new RegExp("([a-z])["+D+",]*((-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?["+D+"]*,?["+D+"]*)+)","ig"),O=new RegExp("(-?\\d*\\.?\\d*(?:e[\\-+]?\\d+)?)["+D+"]*,?["+D+"]*","ig");function U(o){if(!o)return null;if((0,V.isArray)(o))return o;var h={a:7,c:6,o:2,h:1,l:2,m:2,r:4,q:4,s:4,t:2,v:1,u:3,z:0},d=[];return String(o).replace(E,function(B,K,tt){var et=[],ft=K.toLowerCase();if(tt.replace(O,function(ct,_t){_t&&et.push(+_t)}),ft==="m"&&et.length>2&&(d.push([K].concat(et.splice(0,2))),ft="l",K=K==="m"?"l":"L"),ft==="o"&&et.length===1&&d.push([K,et[0]]),ft==="r")d.push([K].concat(et));else for(;et.length>=h[ft]&&(d.push([K].concat(et.splice(0,h[ft]))),!!h[ft]););return""}),d}var nt=/[a-z]/;function st(o,h){return[h[0]+(h[0]-o[0]),h[1]+(h[1]-o[1])]}function H(o){var h=U(o);if(!h||!h.length)return[["M",0,0]];for(var d=!1,B=0;B<h.length;B++){var K=h[B][0];if(nt.test(K)||["V","H","T","S"].indexOf(K)>=0){d=!0;break}}if(!d)return h;var tt=[],et=0,ft=0,ct=0,_t=0,At=0,It,Xt,St=h[0];(St[0]==="M"||St[0]==="m")&&(et=+St[1],ft=+St[2],ct=et,_t=ft,At++,tt[0]=["M",et,ft]);for(var B=At,kt=h.length;B<kt;B++){var Rt=h[B],Wt=tt[B-1],Ot=[],K=Rt[0],Ft=K.toUpperCase();if(K!==Ft)switch(Ot[0]=Ft,Ft){case"A":Ot[1]=Rt[1],Ot[2]=Rt[2],Ot[3]=Rt[3],Ot[4]=Rt[4],Ot[5]=Rt[5],Ot[6]=+Rt[6]+et,Ot[7]=+Rt[7]+ft;break;case"V":Ot[1]=+Rt[1]+ft;break;case"H":Ot[1]=+Rt[1]+et;break;case"M":ct=+Rt[1]+et,_t=+Rt[2]+ft,Ot[1]=ct,Ot[2]=_t;break;default:for(var Kt=1,Zt=Rt.length;Kt<Zt;Kt++)Ot[Kt]=+Rt[Kt]+(Kt%2?et:ft)}else Ot=h[B];switch(Ft){case"Z":et=+ct,ft=+_t;break;case"H":et=Ot[1],Ot=["L",et,ft];break;case"V":ft=Ot[1],Ot=["L",et,ft];break;case"T":et=Ot[1],ft=Ot[2];var Gt=st([Wt[1],Wt[2]],[Wt[3],Wt[4]]);Ot=["Q",Gt[0],Gt[1],et,ft];break;case"S":et=Ot[Ot.length-2],ft=Ot[Ot.length-1];var Vt=Wt.length,Qt=st([Wt[Vt-4],Wt[Vt-3]],[Wt[Vt-2],Wt[Vt-1]]);Ot=["C",Qt[0],Qt[1],Ot[1],Ot[2],et,ft];break;case"M":ct=Ot[Ot.length-2],_t=Ot[Ot.length-1];break;default:et=Ot[Ot.length-2],ft=Ot[Ot.length-1]}tt.push(Ot)}return tt}var C=Math.PI*2,S=function(o,h,d,B,K,tt,et){var ft=o.x,ct=o.y;ft*=h,ct*=d;var _t=B*ft-K*ct,At=K*ft+B*ct;return{x:_t+tt,y:At+et}},Z=function(o,h){var d=h===1.5707963267948966?.551915024494:h===-1.5707963267948966?-.551915024494:1.3333333333333333*Math.tan(h/4),B=Math.cos(o),K=Math.sin(o),tt=Math.cos(o+h),et=Math.sin(o+h);return[{x:B-K*d,y:K+B*d},{x:tt+et*d,y:et-tt*d},{x:tt,y:et}]},J=function(o,h,d,B){var K=o*B-h*d<0?-1:1,tt=o*d+h*B;return tt>1&&(tt=1),tt<-1&&(tt=-1),K*Math.acos(tt)},ht=function(o,h,d,B,K,tt,et,ft,ct,_t,At,It){var Xt=Math.pow(K,2),St=Math.pow(tt,2),kt=Math.pow(At,2),Rt=Math.pow(It,2),Wt=Xt*St-Xt*Rt-St*kt;Wt<0&&(Wt=0),Wt/=Xt*Rt+St*kt,Wt=Math.sqrt(Wt)*(et===ft?-1:1);var Ot=Wt*K/tt*It,Ft=Wt*-tt/K*At,Kt=_t*Ot-ct*Ft+(o+d)/2,Zt=ct*Ot+_t*Ft+(h+B)/2,Gt=(At-Ot)/K,Vt=(It-Ft)/tt,Qt=(-At-Ot)/K,tr=(-It-Ft)/tt,ur=J(1,0,Gt,Vt),er=J(Gt,Vt,Qt,tr);return ft===0&&er>0&&(er-=C),ft===1&&er<0&&(er+=C),[Kt,Zt,ur,er]},mt=function(o){var h=o.px,d=o.py,B=o.cx,K=o.cy,tt=o.rx,et=o.ry,ft=o.xAxisRotation,ct=ft===void 0?0:ft,_t=o.largeArcFlag,At=_t===void 0?0:_t,It=o.sweepFlag,Xt=It===void 0?0:It,St=[];if(tt===0||et===0)return[{x1:0,y1:0,x2:0,y2:0,x:B,y:K}];var kt=Math.sin(ct*C/360),Rt=Math.cos(ct*C/360),Wt=Rt*(h-B)/2+kt*(d-K)/2,Ot=-kt*(h-B)/2+Rt*(d-K)/2;if(Wt===0&&Ot===0)return[{x1:0,y1:0,x2:0,y2:0,x:B,y:K}];tt=Math.abs(tt),et=Math.abs(et);var Ft=Math.pow(Wt,2)/Math.pow(tt,2)+Math.pow(Ot,2)/Math.pow(et,2);Ft>1&&(tt*=Math.sqrt(Ft),et*=Math.sqrt(Ft));var Kt=ht(h,d,B,K,tt,et,At,Xt,kt,Rt,Wt,Ot),Zt=Kt[0],Gt=Kt[1],Vt=Kt[2],Qt=Kt[3],tr=Math.abs(Qt)/(C/4);Math.abs(1-tr)<1e-7&&(tr=1);var ur=Math.max(Math.ceil(tr),1);Qt/=ur;for(var er=0;er<ur;er++)St.push(Z(Vt,Qt)),Vt+=Qt;return St.map(function(xr){var Ar=S(xr[0],tt,et,Rt,kt,Zt,Gt),nr=Ar.x,wr=Ar.y,mr=S(xr[1],tt,et,Rt,kt,Zt,Gt),vr=mr.x,Cr=mr.y,or=S(xr[2],tt,et,Rt,kt,Zt,Gt),Dr=or.x,br=or.y;return{x1:nr,y1:wr,x2:vr,y2:Cr,x:Dr,y:br}})};function Pt(o,h,d,B,K,tt,et,ft,ct){var _t=mt({px:o,py:h,cx:ft,cy:ct,rx:d,ry:B,xAxisRotation:K,largeArcFlag:tt,sweepFlag:et});return _t.reduce(function(At,It){var Xt=It.x1,St=It.y1,kt=It.x2,Rt=It.y2,Wt=It.x,Ot=It.y;return At.push(Xt,St,kt,Rt,Wt,Ot),At},[])}function Yt(o,h){"TQ".indexOf(o[0])<0&&(h.qx=null,h.qy=null);var d=o.slice(1),B=d[0],K=d[1];switch(o[0]){case"M":return h.x=B,h.y=K,o;case"A":return["C"].concat(arcToCubic.apply(0,[h.x1,h.y1].concat(o.slice(1))));case"Q":return h.qx=B,h.qy=K,["C"].concat(quadToCubic.apply(0,[h.x1,h.y1].concat(o.slice(1))));case"L":return["C"].concat(lineToCubic(h.x1,h.y1,o[1],o[2]));case"H":return["C"].concat(lineToCubic(h.x1,h.y1,o[1],h.y1));case"V":return["C"].concat(lineToCubic(h.x1,h.y1,h.x1,o[1]));case"Z":return["C"].concat(lineToCubic(h.x1,h.y1,h.x,h.y));default:}return o}function Nt(o,h){h===void 0&&(h=!1);for(var d=path2Absolute(o),B={x1:0,y1:0,x2:0,y2:0,x:0,y:0,qx:null,qy:null},K=[],tt="",et=d.length,ft,ct,_t=[],At=0;At<et;At+=1)d[At]&&(tt=d[At][0]),K[At]=tt,d[At]=segmentToCubic(d[At],B),dt(d,K,At),et=d.length,tt==="Z"&&_t.push(At),ft=d[At],ct=ft.length,B.x1=+ft[ct-2],B.y1=+ft[ct-1],B.x2=+ft[ct-4]||B.x1,B.y2=+ft[ct-3]||B.y1;return h?[d,_t]:d}function dt(o,h,d){if(o[d].length>7){o[d].shift();for(var B=o[d],K=d;B.length;)h[d]="A",o.splice(K+=1,0,["C"].concat(B.splice(0,6)));o.splice(d,1)}}var pt=function(o,h,d,B,K){var tt=-3*h+9*d-9*B+3*K,et=o*tt+6*h-12*d+6*B;return o*et-3*h+3*d},xt=function(o,h,d,B,K,tt,et,ft,ct){ct===null&&(ct=1),ct=ct>1?1:ct<0?0:ct;for(var _t=ct/2,At=12,It=[-.1252,.1252,-.3678,.3678,-.5873,.5873,-.7699,.7699,-.9041,.9041,-.9816,.9816],Xt=[.2491,.2491,.2335,.2335,.2032,.2032,.1601,.1601,.1069,.1069,.0472,.0472],St=0,kt=0;kt<At;kt++){var Rt=_t*It[kt]+_t,Wt=pt(Rt,o,d,K,et),Ot=pt(Rt,h,B,tt,ft),Ft=Wt*Wt+Ot*Ot;St+=Xt[kt]*Math.sqrt(Ft)}return _t*St},Et=function(o,h,d,B,K,tt,et,ft){for(var ct=[],_t=[[],[]],At,It,Xt,St,kt=0;kt<2;++kt){if(kt===0?(It=6*o-12*d+6*K,At=-3*o+9*d-9*K+3*et,Xt=3*d-3*o):(It=6*h-12*B+6*tt,At=-3*h+9*B-9*tt+3*ft,Xt=3*B-3*h),Math.abs(At)<1e-12){if(Math.abs(It)<1e-12)continue;St=-Xt/It,St>0&&St<1&&ct.push(St);continue}var Rt=It*It-4*Xt*At,Wt=Math.sqrt(Rt);if(!(Rt<0)){var Ot=(-It+Wt)/(2*At);Ot>0&&Ot<1&&ct.push(Ot);var Ft=(-It-Wt)/(2*At);Ft>0&&Ft<1&&ct.push(Ft)}}for(var Kt=ct.length,Zt=Kt,Gt;Kt--;)St=ct[Kt],Gt=1-St,_t[0][Kt]=Gt*Gt*Gt*o+3*Gt*Gt*St*d+3*Gt*St*St*K+St*St*St*et,_t[1][Kt]=Gt*Gt*Gt*h+3*Gt*Gt*St*B+3*Gt*St*St*tt+St*St*St*ft;return _t[0][Zt]=o,_t[1][Zt]=h,_t[0][Zt+1]=et,_t[1][Zt+1]=ft,_t[0].length=_t[1].length=Zt+2,{min:{x:Math.min.apply(0,_t[0]),y:Math.min.apply(0,_t[1])},max:{x:Math.max.apply(0,_t[0]),y:Math.max.apply(0,_t[1])}}},wt=function(o,h,d,B,K,tt,et,ft){if(!(Math.max(o,d)<Math.min(K,et)||Math.min(o,d)>Math.max(K,et)||Math.max(h,B)<Math.min(tt,ft)||Math.min(h,B)>Math.max(tt,ft))){var ct=(o*B-h*d)*(K-et)-(o-d)*(K*ft-tt*et),_t=(o*B-h*d)*(tt-ft)-(h-B)*(K*ft-tt*et),At=(o-d)*(tt-ft)-(h-B)*(K-et);if(At){var It=ct/At,Xt=_t/At,St=+It.toFixed(2),kt=+Xt.toFixed(2);if(!(St<+Math.min(o,d).toFixed(2)||St>+Math.max(o,d).toFixed(2)||St<+Math.min(K,et).toFixed(2)||St>+Math.max(K,et).toFixed(2)||kt<+Math.min(h,B).toFixed(2)||kt>+Math.max(h,B).toFixed(2)||kt<+Math.min(tt,ft).toFixed(2)||kt>+Math.max(tt,ft).toFixed(2)))return{x:It,y:Xt}}}},c=function(o,h,d){return h>=o.x&&h<=o.x+o.width&&d>=o.y&&d<=o.y+o.height},A=function(o,h,d,B){return o===null&&(o=h=d=B=0),h===null&&(h=o.y,d=o.width,B=o.height,o=o.x),{x:o,y:h,width:d,w:d,height:B,h:B,x2:o+d,y2:h+B,cx:o+d/2,cy:h+B/2,r1:Math.min(d,B)/2,r2:Math.max(d,B)/2,r0:Math.sqrt(d*d+B*B)/2,path:rectPath(o,h,d,B),vb:[o,h,d,B].join(" ")}},G=function(o,h){return o=A(o),h=A(h),c(h,o.x,o.y)||c(h,o.x2,o.y)||c(h,o.x,o.y2)||c(h,o.x2,o.y2)||c(o,h.x,h.y)||c(o,h.x2,h.y)||c(o,h.x,h.y2)||c(o,h.x2,h.y2)||(o.x<h.x2&&o.x>h.x||h.x<o.x2&&h.x>o.x)&&(o.y<h.y2&&o.y>h.y||h.y<o.y2&&h.y>o.y)},vt=function(o,h,d,B,K,tt,et,ft){isArray(o)||(o=[o,h,d,B,K,tt,et,ft]);var ct=Et.apply(null,o);return A(ct.min.x,ct.min.y,ct.max.x-ct.min.x,ct.max.y-ct.min.y)},yt=function(o,h,d,B,K,tt,et,ft,ct){var _t=1-ct,At=Math.pow(_t,3),It=Math.pow(_t,2),Xt=ct*ct,St=Xt*ct,kt=At*o+It*3*ct*d+_t*3*ct*ct*K+St*et,Rt=At*h+It*3*ct*B+_t*3*ct*ct*tt+St*ft,Wt=o+2*ct*(d-o)+Xt*(K-2*d+o),Ot=h+2*ct*(B-h)+Xt*(tt-2*B+h),Ft=d+2*ct*(K-d)+Xt*(et-2*K+d),Kt=B+2*ct*(tt-B)+Xt*(ft-2*tt+B),Zt=_t*o+ct*d,Gt=_t*h+ct*B,Vt=_t*K+ct*et,Qt=_t*tt+ct*ft,tr=90-Math.atan2(Wt-Ft,Ot-Kt)*180/Math.PI;return{x:kt,y:Rt,m:{x:Wt,y:Ot},n:{x:Ft,y:Kt},start:{x:Zt,y:Gt},end:{x:Vt,y:Qt},alpha:tr}},p=function(o,h,d){var B=vt(o),K=vt(h);if(!G(B,K))return d?0:[];for(var tt=xt.apply(0,o),et=xt.apply(0,h),ft=~~(tt/8),ct=~~(et/8),_t=[],At=[],It={},Xt=d?0:[],St=0;St<ft+1;St++){var kt=yt.apply(0,o.concat(St/ft));_t.push({x:kt.x,y:kt.y,t:St/ft})}for(var St=0;St<ct+1;St++){var kt=yt.apply(0,h.concat(St/ct));At.push({x:kt.x,y:kt.y,t:St/ct})}for(var St=0;St<ft;St++)for(var Rt=0;Rt<ct;Rt++){var Wt=_t[St],Ot=_t[St+1],Ft=At[Rt],Kt=At[Rt+1],Zt=Math.abs(Ot.x-Wt.x)<.001?"y":"x",Gt=Math.abs(Kt.x-Ft.x)<.001?"y":"x",Vt=wt(Wt.x,Wt.y,Ot.x,Ot.y,Ft.x,Ft.y,Kt.x,Kt.y);if(Vt){if(It[Vt.x.toFixed(4)]===Vt.y.toFixed(4))continue;It[Vt.x.toFixed(4)]=Vt.y.toFixed(4);var Qt=Wt.t+Math.abs((Vt[Zt]-Wt[Zt])/(Ot[Zt]-Wt[Zt]))*(Ot.t-Wt.t),tr=Ft.t+Math.abs((Vt[Gt]-Ft[Gt])/(Kt[Gt]-Ft[Gt]))*(Kt.t-Ft.t);Qt>=0&&Qt<=1&&tr>=0&&tr<=1&&(d?Xt++:Xt.push({x:Vt.x,y:Vt.y,t1:Qt,t2:tr}))}}return Xt},M=function(o,h,d){o=path2Curve(o),h=path2Curve(h);for(var B,K,tt,et,ft,ct,_t,At,It,Xt,St=d?0:[],kt=0,Rt=o.length;kt<Rt;kt++){var Wt=o[kt];if(Wt[0]==="M")B=ft=Wt[1],K=ct=Wt[2];else{Wt[0]==="C"?(It=[B,K].concat(Wt.slice(1)),B=It[6],K=It[7]):(It=[B,K,B,K,ft,ct,ft,ct],B=ft,K=ct);for(var Ot=0,Ft=h.length;Ot<Ft;Ot++){var Kt=h[Ot];if(Kt[0]==="M")tt=_t=Kt[1],et=At=Kt[2];else{Kt[0]==="C"?(Xt=[tt,et].concat(Kt.slice(1)),tt=Xt[6],et=Xt[7]):(Xt=[tt,et,tt,et,_t,At,_t,At],tt=_t,et=At);var Zt=p(It,Xt,d);if(d)St+=Zt;else{for(var Gt=0,Vt=Zt.length;Gt<Vt;Gt++)Zt[Gt].segment1=kt,Zt[Gt].segment2=Ot,Zt[Gt].bez1=It,Zt[Gt].bez2=Xt;St=St.concat(Zt)}}}}}return St};function L(o,h){return M(o,h)}function T(o){return Math.sqrt(o[0]*o[0]+o[1]*o[1])}function $(o,h){return T(o)*T(h)?(o[0]*h[0]+o[1]*h[1])/(T(o)*T(h)):1}function ut(o,h){return(o[0]*h[1]<o[1]*h[0]?-1:1)*Math.acos($(o,h))}function P(o,h){return o[0]===h[0]&&o[1]===h[1]}function g(o,h){var d=h[1],B=h[2],K=(0,V.mod)((0,V.toRadian)(h[3]),Math.PI*2),tt=h[4],et=h[5],ft=o[0],ct=o[1],_t=h[6],At=h[7],It=Math.cos(K)*(ft-_t)/2+Math.sin(K)*(ct-At)/2,Xt=-1*Math.sin(K)*(ft-_t)/2+Math.cos(K)*(ct-At)/2,St=It*It/(d*d)+Xt*Xt/(B*B);St>1&&(d*=Math.sqrt(St),B*=Math.sqrt(St));var kt=d*d*(Xt*Xt)+B*B*(It*It),Rt=kt?Math.sqrt((d*d*(B*B)-kt)/kt):1;tt===et&&(Rt*=-1),isNaN(Rt)&&(Rt=0);var Wt=B?Rt*d*Xt/B:0,Ot=d?Rt*-B*It/d:0,Ft=(ft+_t)/2+Math.cos(K)*Wt-Math.sin(K)*Ot,Kt=(ct+At)/2+Math.sin(K)*Wt+Math.cos(K)*Ot,Zt=[(It-Wt)/d,(Xt-Ot)/B],Gt=[(-1*It-Wt)/d,(-1*Xt-Ot)/B],Vt=ut([1,0],Zt),Qt=ut(Zt,Gt);return $(Zt,Gt)<=-1&&(Qt=Math.PI),$(Zt,Gt)>=1&&(Qt=0),et===0&&Qt>0&&(Qt=Qt-2*Math.PI),et===1&&Qt<0&&(Qt=Qt+2*Math.PI),{cx:Ft,cy:Kt,rx:P(o,[_t,At])?0:d,ry:P(o,[_t,At])?0:B,startAngle:Vt,endAngle:Vt+Qt,xRotation:K,arcFlag:tt,sweepFlag:et}}function r(o,h){return[h[0]+(h[0]-o[0]),h[1]+(h[1]-o[1])]}function t(o){o=j(o);for(var h=[],d=null,B=null,K=null,tt=0,et=o.length,ft=0;ft<et;ft++){var ct=o[ft];B=o[ft+1];var _t=ct[0],At={command:_t,prePoint:d,params:ct,startTangent:null,endTangent:null};switch(_t){case"M":K=[ct[1],ct[2]],tt=ft;break;case"A":var It=g(d,ct);At.arcParams=It;break;default:break}if(_t==="Z")d=K,B=o[tt+1];else{var Xt=ct.length;d=[ct[Xt-2],ct[Xt-1]]}B&&B[0]==="Z"&&(B=o[tt],h[tt]&&(h[tt].prePoint=d)),At.currentPoint=d,h[tt]&&P(d,h[tt].currentPoint)&&(h[tt].prePoint=At.prePoint);var St=B?[B[B.length-2],B[B.length-1]]:null;At.nextPoint=St;var kt=At.prePoint;if(["L","H","V"].includes(_t))At.startTangent=[kt[0]-d[0],kt[1]-d[1]],At.endTangent=[d[0]-kt[0],d[1]-kt[1]];else if(_t==="Q"){var Rt=[ct[1],ct[2]];At.startTangent=[kt[0]-Rt[0],kt[1]-Rt[1]],At.endTangent=[d[0]-Rt[0],d[1]-Rt[1]]}else if(_t==="T"){var Wt=h[ft-1],Rt=r(Wt.currentPoint,kt);Wt.command==="Q"?(At.command="Q",At.startTangent=[kt[0]-Rt[0],kt[1]-Rt[1]],At.endTangent=[d[0]-Rt[0],d[1]-Rt[1]]):(At.command="TL",At.startTangent=[kt[0]-d[0],kt[1]-d[1]],At.endTangent=[d[0]-kt[0],d[1]-kt[1]])}else if(_t==="C"){var Ot=[ct[1],ct[2]],Ft=[ct[3],ct[4]];At.startTangent=[kt[0]-Ot[0],kt[1]-Ot[1]],At.endTangent=[d[0]-Ft[0],d[1]-Ft[1]],At.startTangent[0]===0&&At.startTangent[1]===0&&(At.startTangent=[Ot[0]-Ft[0],Ot[1]-Ft[1]]),At.endTangent[0]===0&&At.endTangent[1]===0&&(At.endTangent=[Ft[0]-Ot[0],Ft[1]-Ot[1]])}else if(_t==="S"){var Wt=h[ft-1],Ot=r(Wt.currentPoint,kt),Ft=[ct[1],ct[2]];Wt.command==="C"?(At.command="C",At.startTangent=[kt[0]-Ot[0],kt[1]-Ot[1]],At.endTangent=[d[0]-Ft[0],d[1]-Ft[1]]):(At.command="SQ",At.startTangent=[kt[0]-Ft[0],kt[1]-Ft[1]],At.endTangent=[d[0]-Ft[0],d[1]-Ft[1]])}else if(_t==="A"){var Kt=.001,Zt=At.arcParams||{},Gt=Zt.cx,Vt=Gt===void 0?0:Gt,Qt=Zt.cy,tr=Qt===void 0?0:Qt,ur=Zt.rx,er=ur===void 0?0:ur,xr=Zt.ry,Ar=xr===void 0?0:xr,nr=Zt.sweepFlag,wr=nr===void 0?0:nr,mr=Zt.startAngle,vr=mr===void 0?0:mr,Cr=Zt.endAngle,or=Cr===void 0?0:Cr;wr===0&&(Kt*=-1);var Dr=er*Math.cos(vr-Kt)+Vt,br=Ar*Math.sin(vr-Kt)+tr;At.startTangent=[Dr-K[0],br-K[1]];var Ir=er*Math.cos(vr+or+Kt)+Vt,Yr=Ar*Math.sin(vr+or-Kt)+tr;At.endTangent=[kt[0]-Ir,kt[1]-Yr]}h.push(At)}return h}var f=1e-6;function n(o){return Math.abs(o)<f?0:o<0?-1:1}function a(o,h,d){return(d[0]-o[0])*(h[1]-o[1])===(h[0]-o[0])*(d[1]-o[1])&&Math.min(o[0],h[0])<=d[0]&&d[0]<=Math.max(o[0],h[0])&&Math.min(o[1],h[1])<=d[1]&&d[1]<=Math.max(o[1],h[1])}function s(o,h,d){var B=!1,K=o.length;if(K<=2)return!1;for(var tt=0;tt<K;tt++){var et=o[tt],ft=o[(tt+1)%K];if(a(et,ft,[h,d]))return!0;n(et[1]-d)>0!=n(ft[1]-d)>0&&n(h-(d-et[1])*(et[0]-ft[0])/(et[1]-ft[1])-et[0])<0&&(B=!B)}return B}var e=function(o,h,d){return o>=h&&o<=d};function u(o,h,d,B){var K=.001,tt={x:d.x-o.x,y:d.y-o.y},et={x:h.x-o.x,y:h.y-o.y},ft={x:B.x-d.x,y:B.y-d.y},ct=et.x*ft.y-et.y*ft.x,_t=ct*ct,At=et.x*et.x+et.y*et.y,It=ft.x*ft.x+ft.y*ft.y,Xt=null;if(_t>K*At*It){var St=(tt.x*ft.y-tt.y*ft.x)/ct,kt=(tt.x*et.y-tt.y*et.x)/ct;e(St,0,1)&&e(kt,0,1)&&(Xt={x:o.x+St*et.x,y:o.y+St*et.y})}return Xt}function l(o){for(var h=[],d=o.length,B=0;B<d-1;B++){var K=o[B],tt=o[B+1];h.push({from:{x:K[0],y:K[1]},to:{x:tt[0],y:tt[1]}})}if(h.length>1){var et=o[0],ft=o[d-1];h.push({from:{x:ft[0],y:ft[1]},to:{x:et[0],y:et[1]}})}return h}function v(o,h){var d=!1;return(0,V.each)(o,function(B){if(u(B.from,B.to,h.from,h.to))return d=!0,!1}),d}function i(o){var h=o.map(function(B){return B[0]}),d=o.map(function(B){return B[1]});return{minX:Math.min.apply(null,h),maxX:Math.max.apply(null,h),minY:Math.min.apply(null,d),maxY:Math.max.apply(null,d)}}function y(o,h){return!(h.minX>o.maxX||h.maxX<o.minX||h.minY>o.maxY||h.maxY<o.minY)}function x(o,h){if(o.length<2||h.length<2)return!1;var d=i(o),B=i(h);if(!y(d,B))return!1;var K=!1;if((0,V.each)(h,function(ct){if(s(o,ct[0],ct[1]))return K=!0,!1}),K||((0,V.each)(o,function(ct){if(s(h,ct[0],ct[1]))return K=!0,!1}),K))return!0;var tt=l(o),et=l(h),ft=!1;return(0,V.each)(et,function(ct){if(v(tt,ct))return ft=!0,!1}),ft}},46319:function(Lt,Mt,k){"use strict";k.d(Mt,{WD:function(){return q},bJ:function(){return o},yZ:function(){return Ot}});var V={};k.r(V),k.d(V,{assign:function(){return C},default:function(){return T},defaultI18n:function(){return mt},format:function(){return p},parse:function(){return M},setGlobalDateI18n:function(){return Yt},setGlobalDateMasks:function(){return yt}});var _=k(98667),it={};function Q(w){return it[w]}function j(w,I){it[w]=I}var X=function(){function w(I){this.type="base",this.isCategory=!1,this.isLinear=!1,this.isContinuous=!1,this.isIdentity=!1,this.values=[],this.range=[0,1],this.ticks=[],this.__cfg__=I,this.initCfg(),this.init()}return w.prototype.translate=function(I){return I},w.prototype.change=function(I){(0,_.assign)(this.__cfg__,I),this.init()},w.prototype.clone=function(){return this.constructor(this.__cfg__)},w.prototype.getTicks=function(){var I=this;return(0,_.map)(this.ticks,function(R,ot){return(0,_.isObject)(R)?R:{text:I.getText(R,ot),tickValue:R,value:I.scale(R)}})},w.prototype.getText=function(I,R){var ot=this.formatter,rt=ot?ot(I,R):I;return(0,_.isNil)(rt)||!(0,_.isFunction)(rt.toString)?"":rt.toString()},w.prototype.getConfig=function(I){return this.__cfg__[I]},w.prototype.init=function(){(0,_.assign)(this,this.__cfg__),this.setDomain(),(0,_.isEmpty)(this.getConfig("ticks"))&&(this.ticks=this.calculateTicks())},w.prototype.initCfg=function(){},w.prototype.setDomain=function(){},w.prototype.calculateTicks=function(){var I=this.tickMethod,R=[];if((0,_.isString)(I)){var ot=Q(I);if(!ot)throw new Error("There is no method to to calculate ticks!");R=ot(this)}else(0,_.isFunction)(I)&&(R=I(this));return R},w.prototype.rangeMin=function(){return this.range[0]},w.prototype.rangeMax=function(){return this.range[1]},w.prototype.calcPercent=function(I,R,ot){return(0,_.isNumber)(I)?(I-R)/(ot-R):NaN},w.prototype.calcValue=function(I,R,ot){return R+I*(ot-R)},w}(),b=X,N=k(92336),z=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="cat",R.isCategory=!0,R}return I.prototype.buildIndexMap=function(){if(!this.translateIndexMap){this.translateIndexMap=new Map;for(var R=0;R<this.values.length;R++)this.translateIndexMap.set(this.values[R],R)}},I.prototype.translate=function(R){this.buildIndexMap();var ot=this.translateIndexMap.get(R);return ot===void 0&&(ot=(0,_.isNumber)(R)?R:NaN),ot},I.prototype.scale=function(R){var ot=this.translate(R),rt=this.calcPercent(ot,this.min,this.max);return this.calcValue(rt,this.rangeMin(),this.rangeMax())},I.prototype.invert=function(R){var ot=this.max-this.min,rt=this.calcPercent(R,this.rangeMin(),this.rangeMax()),gt=Math.round(ot*rt)+this.min;return gt<this.min||gt>this.max?NaN:this.values[gt]},I.prototype.getText=function(R){for(var ot=[],rt=1;rt<arguments.length;rt++)ot[rt-1]=arguments[rt];var gt=R;return(0,_.isNumber)(R)&&!this.values.includes(R)&&(gt=this.values[gt]),w.prototype.getText.apply(this,(0,N.__spreadArrays)([gt],ot))},I.prototype.initCfg=function(){this.tickMethod="cat"},I.prototype.setDomain=function(){if((0,_.isNil)(this.getConfig("min"))&&(this.min=0),(0,_.isNil)(this.getConfig("max"))){var R=this.values.length;this.max=R>1?R-1:R}this.translateIndexMap&&(this.translateIndexMap=void 0)},I}(b),q=z,W=/d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\1?|[aA]|"[^"]*"|'[^']*'/g,Y="\\d\\d?",D="\\d\\d",E="\\d{3}",O="\\d{4}",U="[^\\s]+",nt=/\[([^]*?)\]/gm;function st(w,I){for(var R=[],ot=0,rt=w.length;ot<rt;ot++)R.push(w[ot].substr(0,I));return R}var H=function(w){return function(I,R){var ot=R[w].map(function(gt){return gt.toLowerCase()}),rt=ot.indexOf(I.toLowerCase());return rt>-1?rt:null}};function C(w){for(var I=[],R=1;R<arguments.length;R++)I[R-1]=arguments[R];for(var ot=0,rt=I;ot<rt.length;ot++){var gt=rt[ot];for(var Dt in gt)w[Dt]=gt[Dt]}return w}var S=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],Z=["January","February","March","April","May","June","July","August","September","October","November","December"],J=st(Z,3),ht=st(S,3),mt={dayNamesShort:ht,dayNames:S,monthNamesShort:J,monthNames:Z,amPm:["am","pm"],DoFn:function(w){return w+["th","st","nd","rd"][w%10>3?0:(w-w%10!==10?1:0)*w%10]}},Pt=C({},mt),Yt=function(w){return Pt=C(Pt,w)},Nt=function(w){return w.replace(/[|\\{()[^$+*?.-]/g,"\\$&")},dt=function(w,I){for(I===void 0&&(I=2),w=String(w);w.length<I;)w="0"+w;return w},pt={D:function(w){return String(w.getDate())},DD:function(w){return dt(w.getDate())},Do:function(w,I){return I.DoFn(w.getDate())},d:function(w){return String(w.getDay())},dd:function(w){return dt(w.getDay())},ddd:function(w,I){return I.dayNamesShort[w.getDay()]},dddd:function(w,I){return I.dayNames[w.getDay()]},M:function(w){return String(w.getMonth()+1)},MM:function(w){return dt(w.getMonth()+1)},MMM:function(w,I){return I.monthNamesShort[w.getMonth()]},MMMM:function(w,I){return I.monthNames[w.getMonth()]},YY:function(w){return dt(String(w.getFullYear()),4).substr(2)},YYYY:function(w){return dt(w.getFullYear(),4)},h:function(w){return String(w.getHours()%12||12)},hh:function(w){return dt(w.getHours()%12||12)},H:function(w){return String(w.getHours())},HH:function(w){return dt(w.getHours())},m:function(w){return String(w.getMinutes())},mm:function(w){return dt(w.getMinutes())},s:function(w){return String(w.getSeconds())},ss:function(w){return dt(w.getSeconds())},S:function(w){return String(Math.round(w.getMilliseconds()/100))},SS:function(w){return dt(Math.round(w.getMilliseconds()/10),2)},SSS:function(w){return dt(w.getMilliseconds(),3)},a:function(w,I){return w.getHours()<12?I.amPm[0]:I.amPm[1]},A:function(w,I){return w.getHours()<12?I.amPm[0].toUpperCase():I.amPm[1].toUpperCase()},ZZ:function(w){var I=w.getTimezoneOffset();return(I>0?"-":"+")+dt(Math.floor(Math.abs(I)/60)*100+Math.abs(I)%60,4)},Z:function(w){var I=w.getTimezoneOffset();return(I>0?"-":"+")+dt(Math.floor(Math.abs(I)/60),2)+":"+dt(Math.abs(I)%60,2)}},xt=function(w){return+w-1},Et=[null,Y],wt=[null,U],c=["isPm",U,function(w,I){var R=w.toLowerCase();return R===I.amPm[0]?0:R===I.amPm[1]?1:null}],A=["timezoneOffset","[^\\s]*?[\\+\\-]\\d\\d:?\\d\\d|[^\\s]*?Z?",function(w){var I=(w+"").match(/([+-]|\d\d)/gi);if(I){var R=+I[1]*60+parseInt(I[2],10);return I[0]==="+"?R:-R}return 0}],G={D:["day",Y],DD:["day",D],Do:["day",Y+U,function(w){return parseInt(w,10)}],M:["month",Y,xt],MM:["month",D,xt],YY:["year",D,function(w){var I=new Date,R=+(""+I.getFullYear()).substr(0,2);return+(""+(+w>68?R-1:R)+w)}],h:["hour",Y,void 0,"isPm"],hh:["hour",D,void 0,"isPm"],H:["hour",Y],HH:["hour",D],m:["minute",Y],mm:["minute",D],s:["second",Y],ss:["second",D],YYYY:["year",O],S:["millisecond","\\d",function(w){return+w*100}],SS:["millisecond",D,function(w){return+w*10}],SSS:["millisecond",E],d:Et,dd:Et,ddd:wt,dddd:wt,MMM:["month",U,H("monthNamesShort")],MMMM:["month",U,H("monthNames")],a:c,A:c,ZZ:A,Z:A},vt={default:"ddd MMM DD YYYY HH:mm:ss",shortDate:"M/D/YY",mediumDate:"MMM D, YYYY",longDate:"MMMM D, YYYY",fullDate:"dddd, MMMM D, YYYY",isoDate:"YYYY-MM-DD",isoDateTime:"YYYY-MM-DDTHH:mm:ssZ",shortTime:"HH:mm",mediumTime:"HH:mm:ss",longTime:"HH:mm:ss.SSS"},yt=function(w){return C(vt,w)},p=function(w,I,R){if(I===void 0&&(I=vt.default),R===void 0&&(R={}),typeof w=="number"&&(w=new Date(w)),Object.prototype.toString.call(w)!=="[object Date]"||isNaN(w.getTime()))throw new Error("Invalid Date pass to format");I=vt[I]||I;var ot=[];I=I.replace(nt,function(gt,Dt){return ot.push(Dt),"@@@"});var rt=C(C({},Pt),R);return I=I.replace(W,function(gt){return pt[gt](w,rt)}),I.replace(/@@@/g,function(){return ot.shift()})};function M(w,I,R){if(R===void 0&&(R={}),typeof I!="string")throw new Error("Invalid format in fecha parse");if(I=vt[I]||I,w.length>1e3)return null;var ot=new Date,rt={year:ot.getFullYear(),month:0,day:1,hour:0,minute:0,second:0,millisecond:0,isPm:null,timezoneOffset:null},gt=[],Dt=[],Tt=I.replace(nt,function(hr,fr){return Dt.push(Nt(fr)),"@@@"}),Ut={},Bt={};Tt=Nt(Tt).replace(W,function(hr){var fr=G[hr],gr=fr[0],Or=fr[1],cr=fr[3];if(Ut[gr])throw new Error("Invalid format. "+gr+" specified twice in format");return Ut[gr]=!0,cr&&(Bt[cr]=!0),gt.push(fr),"("+Or+")"}),Object.keys(Bt).forEach(function(hr){if(!Ut[hr])throw new Error("Invalid format. "+hr+" is required in specified format")}),Tt=Tt.replace(/@@@/g,function(){return Dt.shift()});var Ht=w.match(new RegExp(Tt,"i"));if(!Ht)return null;for(var Jt=C(C({},Pt),R),$t=1;$t<Ht.length;$t++){var rr=gt[$t-1],jt=rr[0],ir=rr[2],sr=ir?ir(Ht[$t],Jt):+Ht[$t];if(sr==null)return null;rt[jt]=sr}rt.isPm===1&&rt.hour!=null&&+rt.hour!=12?rt.hour=+rt.hour+12:rt.isPm===0&&+rt.hour==12&&(rt.hour=0);var pr;if(rt.timezoneOffset==null){pr=new Date(rt.year,rt.month,rt.day,rt.hour,rt.minute,rt.second,rt.millisecond);for(var ar=[["month","getMonth"],["day","getDate"],["hour","getHours"],["minute","getMinutes"],["second","getSeconds"]],$t=0,Sr=ar.length;$t<Sr;$t++)if(Ut[ar[$t][0]]&&rt[ar[$t][0]]!==pr[ar[$t][1]]())return null}else if(pr=new Date(Date.UTC(rt.year,rt.month,rt.day,rt.hour,rt.minute-rt.timezoneOffset,rt.second,rt.millisecond)),rt.month>11||rt.month<0||rt.day>31||rt.day<1||rt.hour>23||rt.hour<0||rt.minute>59||rt.minute<0||rt.second>59||rt.second<0)return null;return pr}var L={format:p,parse:M,defaultI18n:mt,setGlobalDateI18n:Yt,setGlobalDateMasks:yt},T=L;function $(w){return function(I,R,ot,rt){for(var gt=(0,_.isNil)(ot)?0:ot,Dt=(0,_.isNil)(rt)?I.length:rt;gt<Dt;){var Tt=gt+Dt>>>1;w(I[Tt])>R?Dt=Tt:gt=Tt+1}return gt}}var ut="format";function P(w,I){var R=V[ut]||T[ut];return R(w,I)}function g(w){return(0,_.isString)(w)&&(w.indexOf("T")>0?w=new Date(w).getTime():w=new Date(w.replace(/-/gi,"/")).getTime()),(0,_.isDate)(w)&&(w=w.getTime()),w}var r=1e3,t=60*r,f=60*t,n=24*f,a=n*31,s=n*365,e=[["HH:mm:ss",r],["HH:mm:ss",r*10],["HH:mm:ss",r*30],["HH:mm",t],["HH:mm",t*10],["HH:mm",t*30],["HH",f],["HH",f*6],["HH",f*12],["YYYY-MM-DD",n],["YYYY-MM-DD",n*4],["YYYY-WW",n*7],["YYYY-MM",a],["YYYY-MM",a*4],["YYYY-MM",a*6],["YYYY",n*380]];function u(w,I,R){var ot=(I-w)/R,rt=$(function(Dt){return Dt[1]})(e,ot)-1,gt=e[rt];return rt<0?gt=e[0]:rt>=e.length&&(gt=(0,_.last)(e)),gt}var l=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="timeCat",R}return I.prototype.translate=function(R){R=g(R);var ot=this.values.indexOf(R);return ot===-1&&((0,_.isNumber)(R)&&R<this.values.length?ot=R:ot=NaN),ot},I.prototype.getText=function(R,ot){var rt=this.translate(R);if(rt>-1){var gt=this.values[rt],Dt=this.formatter;return gt=Dt?Dt(gt,ot):P(gt,this.mask),gt}return R},I.prototype.initCfg=function(){this.tickMethod="time-cat",this.mask="YYYY-MM-DD",this.tickCount=7},I.prototype.setDomain=function(){var R=this.values;(0,_.each)(R,function(ot,rt){R[rt]=g(ot)}),R.sort(function(ot,rt){return ot-rt}),w.prototype.setDomain.call(this)},I}(q),v=l,i=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.isContinuous=!0,R}return I.prototype.scale=function(R){if((0,_.isNil)(R))return NaN;var ot=this.rangeMin(),rt=this.rangeMax(),gt=this.max,Dt=this.min;if(gt===Dt)return ot;var Tt=this.getScalePercent(R);return ot+Tt*(rt-ot)},I.prototype.init=function(){w.prototype.init.call(this);var R=this.ticks,ot=(0,_.head)(R),rt=(0,_.last)(R);ot<this.min&&(this.min=ot),rt>this.max&&(this.max=rt),(0,_.isNil)(this.minLimit)||(this.min=ot),(0,_.isNil)(this.maxLimit)||(this.max=rt)},I.prototype.setDomain=function(){var R=(0,_.getRange)(this.values),ot=R.min,rt=R.max;(0,_.isNil)(this.min)&&(this.min=ot),(0,_.isNil)(this.max)&&(this.max=rt),this.min>this.max&&(this.min=ot,this.max=rt)},I.prototype.calculateTicks=function(){var R=this,ot=w.prototype.calculateTicks.call(this);return this.nice||(ot=(0,_.filter)(ot,function(rt){return rt>=R.min&&rt<=R.max})),ot},I.prototype.getScalePercent=function(R){var ot=this.max,rt=this.min;return(R-rt)/(ot-rt)},I.prototype.getInvertPercent=function(R){return(R-this.rangeMin())/(this.rangeMax()-this.rangeMin())},I}(b),y=i,x=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="linear",R.isLinear=!0,R}return I.prototype.invert=function(R){var ot=this.getInvertPercent(R);return this.min+ot*(this.max-this.min)},I.prototype.initCfg=function(){this.tickMethod="wilkinson-extended",this.nice=!1},I}(y),o=x;function h(w,I){var R=Math.E,ot;return I>=0?ot=Math.pow(R,Math.log(I)/w):ot=Math.pow(R,Math.log(-I)/w)*-1,ot}function d(w,I){return w===1?1:Math.log(I)/Math.log(w)}function B(w,I,R){(0,_.isNil)(R)&&(R=Math.max.apply(null,w));var ot=R;return(0,_.each)(w,function(rt){rt>0&&rt<ot&&(ot=rt)}),ot===R&&(ot=R/I),ot>1&&(ot=1),ot}function K(w){var I=w.toString().split(/[eE]/),R=(I[0].split(".")[1]||"").length-+(I[1]||0);return R>0?R:0}function tt(w,I){var R=K(w),ot=K(I),rt=Math.pow(10,Math.max(R,ot));return(w*rt+I*rt)/rt}var et=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="log",R}return I.prototype.invert=function(R){var ot=this.base,rt=d(ot,this.max),gt=this.rangeMin(),Dt=this.rangeMax()-gt,Tt,Ut=this.positiveMin;if(Ut){if(R===0)return 0;Tt=d(ot,Ut/ot);var Bt=1/(rt-Tt)*Dt;if(R<Bt)return R/Bt*Ut}else Tt=d(ot,this.min);var Ht=(R-gt)/Dt,Jt=Ht*(rt-Tt)+Tt;return Math.pow(ot,Jt)},I.prototype.initCfg=function(){this.tickMethod="log",this.base=10,this.tickCount=6,this.nice=!0},I.prototype.setDomain=function(){w.prototype.setDomain.call(this);var R=this.min;if(R<0)throw new Error("When you use log scale, the minimum value must be greater than zero!");R===0&&(this.positiveMin=B(this.values,this.base,this.max))},I.prototype.getScalePercent=function(R){var ot=this.max,rt=this.min;if(ot===rt||R<=0)return 0;var gt=this.base,Dt=this.positiveMin;Dt&&(rt=Dt*1/gt);var Tt;return R<Dt?Tt=R/Dt/(d(gt,ot)-d(gt,rt)):Tt=(d(gt,R)-d(gt,rt))/(d(gt,ot)-d(gt,rt)),Tt},I}(y),ft=et,ct=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="pow",R}return I.prototype.invert=function(R){var ot=this.getInvertPercent(R),rt=this.exponent,gt=h(rt,this.max),Dt=h(rt,this.min),Tt=ot*(gt-Dt)+Dt,Ut=Tt>=0?1:-1;return Math.pow(Tt,rt)*Ut},I.prototype.initCfg=function(){this.tickMethod="pow",this.exponent=2,this.tickCount=5,this.nice=!0},I.prototype.getScalePercent=function(R){var ot=this.max,rt=this.min;if(ot===rt)return 0;var gt=this.exponent,Dt=(h(gt,R)-h(gt,rt))/(h(gt,ot)-h(gt,rt));return Dt},I}(y),_t=ct,At=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="time",R}return I.prototype.getText=function(R,ot){var rt=this.translate(R),gt=this.formatter;return gt?gt(rt,ot):P(rt,this.mask)},I.prototype.scale=function(R){var ot=R;return((0,_.isString)(ot)||(0,_.isDate)(ot))&&(ot=this.translate(ot)),w.prototype.scale.call(this,ot)},I.prototype.translate=function(R){return g(R)},I.prototype.initCfg=function(){this.tickMethod="time-pretty",this.mask="YYYY-MM-DD",this.tickCount=7,this.nice=!1},I.prototype.setDomain=function(){var R=this.values,ot=this.getConfig("min"),rt=this.getConfig("max");if((!(0,_.isNil)(ot)||!(0,_.isNumber)(ot))&&(this.min=this.translate(this.min)),(!(0,_.isNil)(rt)||!(0,_.isNumber)(rt))&&(this.max=this.translate(this.max)),R&&R.length){var gt=[],Dt=1/0,Tt=Dt,Ut=0;(0,_.each)(R,function(Bt){var Ht=g(Bt);if(isNaN(Ht))throw new TypeError("Invalid Time: "+Bt+" in time scale!");Dt>Ht?(Tt=Dt,Dt=Ht):Tt>Ht&&(Tt=Ht),Ut<Ht&&(Ut=Ht),gt.push(Ht)}),R.length>1&&(this.minTickInterval=Tt-Dt),(0,_.isNil)(ot)&&(this.min=Dt),(0,_.isNil)(rt)&&(this.max=Ut)}},I}(o),It=At,Xt=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="quantize",R}return I.prototype.invert=function(R){var ot=this.ticks,rt=ot.length,gt=this.getInvertPercent(R),Dt=Math.floor(gt*(rt-1));if(Dt>=rt-1)return(0,_.last)(ot);if(Dt<0)return(0,_.head)(ot);var Tt=ot[Dt],Ut=ot[Dt+1],Bt=Dt/(rt-1),Ht=(Dt+1)/(rt-1);return Tt+(gt-Bt)/(Ht-Bt)*(Ut-Tt)},I.prototype.initCfg=function(){this.tickMethod="r-pretty",this.tickCount=5,this.nice=!0},I.prototype.calculateTicks=function(){var R=w.prototype.calculateTicks.call(this);return this.nice||((0,_.last)(R)!==this.max&&R.push(this.max),(0,_.head)(R)!==this.min&&R.unshift(this.min)),R},I.prototype.getScalePercent=function(R){var ot=this.ticks;if(R<(0,_.head)(ot))return 0;if(R>(0,_.last)(ot))return 1;var rt=0;return(0,_.each)(ot,function(gt,Dt){if(R>=gt)rt=Dt;else return!1}),rt/(ot.length-1)},I}(y),St=Xt,kt=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="quantile",R}return I.prototype.initCfg=function(){this.tickMethod="quantile",this.tickCount=5,this.nice=!0},I}(St),Rt=kt,Wt={};function Ot(w){return Wt[w]}function Ft(w,I){if(Ot(w))throw new Error("type '"+w+"' existed.");Wt[w]=I}var Kt=function(w){(0,N.__extends)(I,w);function I(){var R=w!==null&&w.apply(this,arguments)||this;return R.type="identity",R.isIdentity=!0,R}return I.prototype.calculateTicks=function(){return this.values},I.prototype.scale=function(R){return this.values[0]!==R&&(0,_.isNumber)(R)?R:this.range[0]},I.prototype.invert=function(R){var ot=this.range;return R<ot[0]||R>ot[1]?NaN:this.values[0]},I}(b),Zt=Kt;function Gt(w){var I=w.values,R=w.tickInterval,ot=w.tickCount,rt=w.showLast;if((0,_.isNumber)(R)){var gt=(0,_.filter)(I,function(jt,ir){return ir%R===0}),Dt=(0,_.last)(I);return rt&&(0,_.last)(gt)!==Dt&&gt.push(Dt),gt}var Tt=I.length,Ut=w.min,Bt=w.max;if((0,_.isNil)(Ut)&&(Ut=0),(0,_.isNil)(Bt)&&(Bt=I.length-1),!(0,_.isNumber)(ot)||ot>=Tt)return I.slice(Ut,Bt+1);if(ot<=0||Bt<=0)return[];for(var Ht=ot===1?Tt:Math.floor(Tt/(ot-1)),Jt=[],$t=Ut,rr=0;rr<ot&&!($t>=Bt);rr++)$t=Math.min(Ut+rr*Ht,Bt),rr===ot-1&&rt?Jt.push(I[Bt]):Jt.push(I[$t]);return Jt}function Vt(w){var I=w.min,R=w.max,ot=w.nice,rt=w.tickCount,gt=new xr;return gt.domain([I,R]),ot&&gt.nice(rt),gt.ticks(rt)}var Qt=5,tr=Math.sqrt(50),ur=Math.sqrt(10),er=Math.sqrt(2),xr=function(){function w(){this._domain=[0,1]}return w.prototype.domain=function(I){return I?(this._domain=Array.from(I,Number),this):this._domain.slice()},w.prototype.nice=function(I){var R,ot;I===void 0&&(I=Qt);var rt=this._domain.slice(),gt=0,Dt=this._domain.length-1,Tt=this._domain[gt],Ut=this._domain[Dt],Bt;return Ut<Tt&&(R=[Ut,Tt],Tt=R[0],Ut=R[1],ot=[Dt,gt],gt=ot[0],Dt=ot[1]),Bt=nr(Tt,Ut,I),Bt>0?(Tt=Math.floor(Tt/Bt)*Bt,Ut=Math.ceil(Ut/Bt)*Bt,Bt=nr(Tt,Ut,I)):Bt<0&&(Tt=Math.ceil(Tt*Bt)/Bt,Ut=Math.floor(Ut*Bt)/Bt,Bt=nr(Tt,Ut,I)),Bt>0?(rt[gt]=Math.floor(Tt/Bt)*Bt,rt[Dt]=Math.ceil(Ut/Bt)*Bt,this.domain(rt)):Bt<0&&(rt[gt]=Math.ceil(Tt*Bt)/Bt,rt[Dt]=Math.floor(Ut*Bt)/Bt,this.domain(rt)),this},w.prototype.ticks=function(I){return I===void 0&&(I=Qt),Ar(this._domain[0],this._domain[this._domain.length-1],I||Qt)},w}();function Ar(w,I,R){var ot,rt=-1,gt,Dt,Tt;if(I=+I,w=+w,R=+R,w===I&&R>0)return[w];if((ot=I<w)&&(gt=w,w=I,I=gt),(Tt=nr(w,I,R))===0||!isFinite(Tt))return[];if(Tt>0)for(w=Math.ceil(w/Tt),I=Math.floor(I/Tt),Dt=new Array(gt=Math.ceil(I-w+1));++rt<gt;)Dt[rt]=(w+rt)*Tt;else for(w=Math.floor(w*Tt),I=Math.ceil(I*Tt),Dt=new Array(gt=Math.ceil(w-I+1));++rt<gt;)Dt[rt]=(w-rt)/Tt;return ot&&Dt.reverse(),Dt}function nr(w,I,R){var ot=(I-w)/Math.max(0,R),rt=Math.floor(Math.log(ot)/Math.LN10),gt=ot/Math.pow(10,rt);return rt>=0?(gt>=tr?10:gt>=ur?5:gt>=er?2:1)*Math.pow(10,rt):-Math.pow(10,-rt)/(gt>=tr?10:gt>=ur?5:gt>=er?2:1)}function wr(w,I,R){var ot;return R==="ceil"?ot=Math.ceil(w/I):R==="floor"?ot=Math.floor(w/I):ot=Math.round(w/I),ot*I}function mr(w,I,R){var ot=wr(w,R,"floor"),rt=wr(I,R,"ceil");ot=(0,_.fixedBase)(ot,R),rt=(0,_.fixedBase)(rt,R);for(var gt=[],Dt=Math.max((rt-ot)/(Math.pow(2,12)-1),R),Tt=ot;Tt<=rt;Tt=Tt+Dt){var Ut=(0,_.fixedBase)(Tt,Dt);gt.push(Ut)}return{min:ot,max:rt,ticks:gt}}function vr(w,I,R){var ot,rt=w.minLimit,gt=w.maxLimit,Dt=w.min,Tt=w.max,Ut=w.tickCount,Bt=Ut===void 0?5:Ut,Ht=(0,_.isNil)(rt)?(0,_.isNil)(I)?Dt:I:rt,Jt=(0,_.isNil)(gt)?(0,_.isNil)(R)?Tt:R:gt;if(Ht>Jt&&(ot=[Ht,Jt],Jt=ot[0],Ht=ot[1]),Bt<=2)return[Ht,Jt];for(var $t=(Jt-Ht)/(Bt-1),rr=[],jt=0;jt<Bt;jt++)rr.push(Ht+$t*jt);return rr}function Cr(w){var I=w.min,R=w.max,ot=w.tickInterval,rt=w.minLimit,gt=w.maxLimit,Dt=Vt(w);return!(0,_.isNil)(rt)||!(0,_.isNil)(gt)?vr(w,(0,_.head)(Dt),(0,_.last)(Dt)):ot?mr(I,R,ot).ticks:Dt}function or(w){return Math.abs(w)<1e-15?w:parseFloat(w.toFixed(15))}var Dr=[1,5,2,2.5,4,3],br=null,Ir=Number.EPSILON*100;function Yr(w,I){return(w%I+I)%I}function Gr(w){return Math.round(w*1e12)/1e12}function Vr(w,I,R,ot,rt,gt){var Dt=(0,_.size)(I),Tt=(0,_.indexOf)(I,w),Ut=0,Bt=Yr(ot,gt);return(Bt<Ir||gt-Bt<Ir)&&ot<=0&&rt>=0&&(Ut=1),1-Tt/(Dt-1)-R+Ut}function Qr(w,I,R){var ot=(0,_.size)(I),rt=(0,_.indexOf)(I,w),gt=1;return 1-rt/(ot-1)-R+gt}function $r(w,I,R,ot,rt,gt){var Dt=(w-1)/(gt-rt),Tt=(I-1)/(Math.max(gt,ot)-Math.min(R,rt));return 2-Math.max(Dt/Tt,Tt/Dt)}function Jr(w,I){return w>=I?2-(w-1)/(I-1):1}function qr(w,I,R,ot){var rt=I-w;return 1-.5*(Math.pow(I-ot,2)+Math.pow(w-R,2))/Math.pow(.1*rt,2)}function jr(w,I,R){var ot=I-w;if(R>ot){var rt=(R-ot)/2;return 1-Math.pow(rt,2)/Math.pow(.1*ot,2)}return 1}function tn(){return 1}function rn(w,I,R,ot,rt,gt){R===void 0&&(R=5),ot===void 0&&(ot=!0),rt===void 0&&(rt=Dr),gt===void 0&&(gt=[.25,.2,.5,.05]);var Dt=R<0?0:Math.round(R);if(Number.isNaN(w)||Number.isNaN(I)||typeof w!="number"||typeof I!="number"||!Dt)return{min:0,max:0,ticks:[]};if(I-w<1e-15||Dt===1)return{min:w,max:I,ticks:[w]};if(I-w>1e148){var Tt=R||5,Ut=(I-w)/Tt;return{min:w,max:I,ticks:Array(Tt).fill(null).map(function(An,yn){return or(w+Ut*yn)})}}for(var Bt={score:-2,lmin:0,lmax:0,lstep:0},Ht=1;Ht<1/0;){for(var Jt=0;Jt<rt.length;Jt+=1){var $t=rt[Jt],rr=Qr($t,rt,Ht);if(gt[0]*rr+gt[1]+gt[2]+gt[3]<Bt.score){Ht=1/0;break}for(var jt=2;jt<1/0;){var ir=Jr(jt,Dt);if(gt[0]*rr+gt[1]+gt[2]*ir+gt[3]<Bt.score)break;for(var sr=(I-w)/(jt+1)/Ht/$t,pr=Math.ceil(Math.log10(sr));pr<1/0;){var ar=Ht*$t*Math.pow(10,pr),Sr=jr(w,I,ar*(jt-1));if(gt[0]*rr+gt[1]*Sr+gt[2]*ir+gt[3]<Bt.score)break;var hr=Math.floor(I/ar)*Ht-(jt-1)*Ht,fr=Math.ceil(w/ar)*Ht;if(hr<=fr)for(var Tt=fr-hr,gr=0;gr<=Tt;gr+=1){var Or=hr+gr,cr=Or*(ar/Ht),yr=cr+ar*(jt-1),Lr=ar,Fr=Vr($t,rt,Ht,cr,yr,Lr),kr=qr(w,I,cr,yr),dn=$r(jt,Dt,w,I,cr,yr),mn=tn(),Rr=gt[0]*Fr+gt[1]*kr+gt[2]*dn+gt[3]*mn;Rr>Bt.score&&(!ot||cr<=w&&yr>=I)&&(Bt.lmin=cr,Bt.lmax=yr,Bt.lstep=Lr,Bt.score=Rr)}pr+=1}jt+=1}}Ht+=1}var Er=or(Bt.lmax),Ur=or(Bt.lmin),Zr=or(Bt.lstep),Nr=Math.floor(Gr((Er-Ur)/Zr))+1,Pr=new Array(Nr);Pr[0]=or(Ur);for(var Jt=1;Jt<Nr;Jt++)Pr[Jt]=or(Pr[Jt-1]+Zr);return{min:Math.min(w,(0,_.head)(Pr)),max:Math.max(I,(0,_.last)(Pr)),ticks:Pr}}function nn(w){var I=w.min,R=w.max,ot=w.tickCount,rt=w.nice,gt=w.tickInterval,Dt=w.minLimit,Tt=w.maxLimit,Ut=rn(I,R,ot,rt).ticks;return!(0,_.isNil)(Dt)||!(0,_.isNil)(Tt)?vr(w,(0,_.head)(Ut),(0,_.last)(Ut)):gt?mr(I,R,gt).ticks:Ut}function en(w){var I=w.base,R=w.tickCount,ot=w.min,rt=w.max,gt=w.values,Dt,Tt=d(I,rt);if(ot>0)Dt=Math.floor(d(I,ot));else{var Ut=B(gt,I,rt);Dt=Math.floor(d(I,Ut))}for(var Bt=Tt-Dt,Ht=Math.ceil(Bt/R),Jt=[],$t=Dt;$t<Tt+Ht;$t=$t+Ht)Jt.push(Math.pow(I,$t));return ot<=0&&Jt.unshift(0),Jt}function Xr(w,I,R){if(R===void 0&&(R=5),w===I)return{max:I,min:w,ticks:[w]};var ot=R<0?0:Math.round(R);if(ot===0)return{max:I,min:w,ticks:[]};var rt=1.5,gt=.5+1.5*rt,Dt=I-w,Tt=Dt/ot,Ut=Math.pow(10,Math.floor(Math.log10(Tt))),Bt=Ut;2*Ut-Tt<rt*(Tt-Bt)&&(Bt=2*Ut,5*Ut-Tt<gt*(Tt-Bt)&&(Bt=5*Ut,10*Ut-Tt<rt*(Tt-Bt)&&(Bt=10*Ut)));for(var Ht=Math.ceil(I/Bt),Jt=Math.floor(w/Bt),$t=Math.max(Ht*Bt,I),rr=Math.min(Jt*Bt,w),jt=Math.floor(($t-rr)/Bt)+1,ir=new Array(jt),sr=0;sr<jt;sr++)ir[sr]=or(rr+sr*Bt);return{min:rr,max:$t,ticks:ir}}function an(w){var I=w.exponent,R=w.tickCount,ot=Math.ceil(h(I,w.max)),rt=Math.floor(h(I,w.min)),gt=Xr(rt,ot,R).ticks;return gt.map(function(Dt){var Tt=Dt>=0?1:-1;return Math.pow(Dt,I)*Tt})}function on(w,I){var R=w.length*I;return I===1?w[w.length-1]:I===0?w[0]:R%1!==0?w[Math.ceil(R)-1]:w.length%2===0?(w[R-1]+w[R])/2:w[R]}function sn(w){var I=w.tickCount,R=w.values;if(!R||!R.length)return[];for(var ot=R.slice().sort(function(Tt,Ut){return Tt-Ut}),rt=[],gt=0;gt<I;gt++){var Dt=gt/(I-1);rt.push(on(ot,Dt))}return rt}function un(w){var I=w.min,R=w.max,ot=w.tickCount,rt=w.tickInterval,gt=w.minLimit,Dt=w.maxLimit,Tt=Xr(I,R,ot).ticks;return!(0,_.isNil)(gt)||!(0,_.isNil)(Dt)?vr(w,(0,_.head)(Tt),(0,_.last)(Tt)):rt?mr(I,R,rt).ticks:Tt}function fn(w){var I=w.min,R=w.max,ot=w.minTickInterval,rt=w.tickInterval,gt=w.tickCount;if(rt)gt=Math.ceil((R-I)/rt);else{rt=u(I,R,gt)[1];var Dt=(R-I)/rt,Tt=Dt/gt;Tt>1&&(rt=rt*Math.ceil(Tt)),ot&&rt<ot&&(rt=ot)}rt=Math.max(Math.floor((R-I)/(Math.pow(2,12)-1)),rt);for(var Ut=[],Bt=I;Bt<R+rt;Bt+=rt)Ut.push(Bt);return Ut}function Hr(w){var I=Gt((0,N.__assign)({showLast:!0},w));return I}function Br(w){return new Date(w).getFullYear()}function cn(w){return new Date(w,0,1).getTime()}function Tr(w){return new Date(w).getMonth()}function ln(w,I){var R=Br(w),ot=Br(I),rt=Tr(w),gt=Tr(I);return(ot-R)*12+(gt-rt)%12}function _r(w,I){return new Date(w,I,1).getTime()}function hn(w,I){return Math.ceil((I-w)/n)}function vn(w,I){return Math.ceil((I-w)/f)}function pn(w,I){return Math.ceil((I-w)/(60*1e3))}function gn(w){var I=w.min,R=w.max,ot=w.minTickInterval,rt=w.tickCount,gt=w.tickInterval,Dt=[];gt||(gt=(R-I)/rt,ot&&gt<ot&&(gt=ot)),gt=Math.max(Math.floor((R-I)/(Math.pow(2,12)-1)),gt);var Tt=Br(I);if(gt>s)for(var Ut=Br(R),Bt=Math.ceil(gt/s),Ht=Tt;Ht<=Ut+Bt;Ht=Ht+Bt)Dt.push(cn(Ht));else if(gt>a)for(var Jt=Math.ceil(gt/a),$t=Tr(I),rr=ln(I,R),Ht=0;Ht<=rr+Jt;Ht=Ht+Jt)Dt.push(_r(Tt,Ht+$t));else if(gt>n)for(var jt=new Date(I),ir=jt.getFullYear(),sr=jt.getMonth(),pr=jt.getDate(),ar=Math.ceil(gt/n),Sr=hn(I,R),Ht=0;Ht<Sr+ar;Ht=Ht+ar)Dt.push(new Date(ir,sr,pr+Ht).getTime());else if(gt>f)for(var jt=new Date(I),ir=jt.getFullYear(),sr=jt.getMonth(),ar=jt.getDate(),hr=jt.getHours(),fr=Math.ceil(gt/f),gr=vn(I,R),Ht=0;Ht<=gr+fr;Ht=Ht+fr)Dt.push(new Date(ir,sr,ar,hr+Ht).getTime());else if(gt>t)for(var Or=pn(I,R),cr=Math.ceil(gt/t),Ht=0;Ht<=Or+cr;Ht=Ht+cr)Dt.push(I+Ht*t);else{var yr=gt;yr<r&&(yr=r);for(var Lr=Math.floor(I/r)*r,Fr=Math.ceil((R-I)/r),kr=Math.ceil(yr/r),Ht=0;Ht<Fr+kr;Ht=Ht+kr)Dt.push(Lr+Ht*r)}return Dt.length>=512&&console.warn("Notice: current ticks length("+Dt.length+') >= 512, may cause performance issues, even out of memory. Because of the configure "tickInterval"(in milliseconds, current is '+gt+") is too small, increase the value to solve the problem!"),Dt}j("cat",Gt),j("time-cat",Hr),j("wilkinson-extended",nn),j("r-pretty",un),j("time",fn),j("time-pretty",gn),j("log",en),j("pow",an),j("quantile",sn),j("d3-linear",Cr),Ft("cat",q),Ft("category",q),Ft("identity",Zt),Ft("linear",o),Ft("log",ft),Ft("pow",_t),Ft("time",It),Ft("timeCat",v),Ft("quantize",St),Ft("quantile",Rt)},98667:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{Cache:function(){return te},assign:function(){return Er},augment:function(){return Zr},clamp:function(){return ur},clearAnimationFrame:function(){return mn},clone:function(){return Pr},contains:function(){return Q},debounce:function(){return yn},deepMix:function(){return Dn},difference:function(){return N},each:function(){return st},endsWith:function(){return tt},every:function(){return ft},extend:function(){return Sn},filter:function(){return X},find:function(){return Nt},findIndex:function(){return pt},firstValue:function(){return Et},fixedBase:function(){return xr},flatten:function(){return c},flattenDeep:function(){return G},forIn:function(){return fn},get:function(){return Wn},getEllipsisText:function(){return qn},getRange:function(){return M},getType:function(){return Ut},getWrapBehavior:function(){return Wt},group:function(){return kt},groupBy:function(){return Xt},groupToMap:function(){return St},has:function(){return Hr},hasKey:function(){return Br},hasValue:function(){return ln},head:function(){return o},identity:function(){return Qn},includes:function(){return Q},indexOf:function(){return bn},isArguments:function(){return Ht},isArray:function(){return O},isArrayLike:function(){return _},isBoolean:function(){return $t},isDate:function(){return jt},isDecimal:function(){return mr},isElement:function(){return kr},isEmpty:function(){return Ln},isEqual:function(){return _n},isEqualWith:function(){return kn},isError:function(){return sr},isEven:function(){return Cr},isFinite:function(){return pr},isFunction:function(){return Y},isInteger:function(){return Dr},isMatch:function(){return Z},isNegative:function(){return Ir},isNil:function(){return E},isNull:function(){return Sr},isNumber:function(){return nr},isNumberEqual:function(){return Gr},isObject:function(){return U},isObjectLike:function(){return ht},isOdd:function(){return Qr},isPlainObject:function(){return Pt},isPositive:function(){return Jr},isPrototype:function(){return gr},isRegExp:function(){return cr},isString:function(){return e},isType:function(){return W},isUndefined:function(){return Lr},keys:function(){return C},last:function(){return h},lowerCase:function(){return vn},lowerFirst:function(){return gn},map:function(){return Nn},mapValues:function(){return Fn},max:function(){return vt},maxBy:function(){return qr},measureTextWidth:function(){return Wr},memoize:function(){return wn},min:function(){return yt},minBy:function(){return jr},mix:function(){return Er},mod:function(){return rn},noop:function(){return Vn},number2color:function(){return Gt},omit:function(){return Zn},parseRadius:function(){return Qt},pick:function(){return Un},pull:function(){return P},pullAt:function(){return t},reduce:function(){return n},remove:function(){return s},requestAnimationFrame:function(){return dn},set:function(){return Xn},size:function(){return $n},some:function(){return _t},sortBy:function(){return l},startsWith:function(){return B},substitute:function(){return I},throttle:function(){return zn},toArray:function(){return Kn},toDegree:function(){return Xr},toInteger:function(){return an},toRadian:function(){return un},toString:function(){return _r},union:function(){return y},uniq:function(){return v},uniqueId:function(){return Gn},upperCase:function(){return ot},upperFirst:function(){return gt},values:function(){return Tr},valuesOfKey:function(){return x},wrapBehavior:function(){return Ft}});var V=function(m){return m!==null&&typeof m!="function"&&isFinite(m.length)},_=V,it=function(m,F){return _(m)?m.indexOf(F)>-1:!1},Q=it,j=function(m,F){if(!_(m))return m;for(var at=[],lt=0;lt<m.length;lt++){var Ct=m[lt];F(Ct,lt)&&at.push(Ct)}return at},X=j,b=function(m,F){return F===void 0&&(F=[]),X(m,function(at){return!Q(F,at)})},N=b,z={}.toString,q=function(m,F){return z.call(m)==="[object "+F+"]"},W=q,Y=function(m){return W(m,"Function")},D=function(m){return m==null},E=D,O=function(m){return Array.isArray?Array.isArray(m):W(m,"Array")},U=function(m){var F=typeof m;return m!==null&&F==="object"||F==="function"};function nt(m,F){if(m){var at;if(O(m))for(var lt=0,Ct=m.length;lt<Ct&&(at=F(m[lt],lt),at!==!1);lt++);else if(U(m)){for(var bt in m)if(m.hasOwnProperty(bt)&&(at=F(m[bt],bt),at===!1))break}}}var st=nt,H=Object.keys?function(m){return Object.keys(m)}:function(m){var F=[];return st(m,function(at,lt){Y(m)&&lt==="prototype"||F.push(lt)}),F},C=H;function S(m,F){var at=C(F),lt=at.length;if(E(m))return!lt;for(var Ct=0;Ct<lt;Ct+=1){var bt=at[Ct];if(F[bt]!==m[bt]||!(bt in m))return!1}return!0}var Z=S,J=function(m){return typeof m=="object"&&m!==null},ht=J,mt=function(m){if(!ht(m)||!W(m,"Object"))return!1;if(Object.getPrototypeOf(m)===null)return!0;for(var F=m;Object.getPrototypeOf(F)!==null;)F=Object.getPrototypeOf(F);return Object.getPrototypeOf(m)===F},Pt=mt;function Yt(m,F){if(!O(m))return null;var at;if(Y(F)&&(at=F),Pt(F)&&(at=function(Ct){return Z(Ct,F)}),at){for(var lt=0;lt<m.length;lt+=1)if(at(m[lt]))return m[lt]}return null}var Nt=Yt;function dt(m,F,at){at===void 0&&(at=0);for(var lt=at;lt<m.length;lt++)if(F(m[lt],lt))return lt;return-1}var pt=dt,xt=function(m,F){for(var at=null,lt=0;lt<m.length;lt++){var Ct=m[lt],bt=Ct[F];if(!E(bt)){O(bt)?at=bt[0]:at=bt;break}}return at},Et=xt,wt=function(m){if(!O(m))return[];for(var F=[],at=0;at<m.length;at++)F=F.concat(m[at]);return F},c=wt,A=function(m,F){if(F===void 0&&(F=[]),!O(m))F.push(m);else for(var at=0;at<m.length;at+=1)A(m[at],F);return F},G=A,vt=function(m){if(O(m))return m.reduce(function(F,at){return Math.max(F,at)},m[0])},yt=function(m){if(O(m))return m.reduce(function(F,at){return Math.min(F,at)},m[0])},p=function(m){var F=m.filter(function(zt){return!isNaN(zt)});if(!F.length)return{min:0,max:0};if(O(m[0])){for(var at=[],lt=0;lt<m.length;lt++)at=at.concat(m[lt]);F=at}var Ct=vt(F),bt=yt(F);return{min:bt,max:Ct}},M=p,L=Array.prototype,T=L.splice,$=L.indexOf,ut=function(m){for(var F=[],at=1;at<arguments.length;at++)F[at-1]=arguments[at];for(var lt=0;lt<F.length;lt++)for(var Ct=F[lt],bt=-1;(bt=$.call(m,Ct))>-1;)T.call(m,bt,1);return m},P=ut,g=Array.prototype.splice,r=function(F,at){if(!_(F))return[];for(var lt=F?at.length:0,Ct=lt-1;lt--;){var bt=void 0,zt=at[lt];(lt===Ct||zt!==bt)&&(bt=zt,g.call(F,zt,1))}return F},t=r,f=function(m,F,at){if(!O(m)&&!Pt(m))return m;var lt=at;return st(m,function(Ct,bt){lt=F(lt,Ct,bt)}),lt},n=f,a=function(m,F){var at=[];if(!_(m))return at;for(var lt=-1,Ct=[],bt=m.length;++lt<bt;){var zt=m[lt];F(zt,lt,m)&&(at.push(zt),Ct.push(lt))}return t(m,Ct),at},s=a,e=function(m){return W(m,"String")};function u(m,F){var at;if(Y(F))at=function(Ct,bt){return F(Ct)-F(bt)};else{var lt=[];e(F)?lt.push(F):O(F)&&(lt=F),at=function(Ct,bt){for(var zt=0;zt<lt.length;zt+=1){var qt=lt[zt];if(Ct[qt]>bt[qt])return 1;if(Ct[qt]<bt[qt])return-1}return 0}}return m.sort(at),m}var l=u;function v(m,F){F===void 0&&(F=new Map);var at=[];if(Array.isArray(m))for(var lt=0,Ct=m.length;lt<Ct;lt++){var bt=m[lt];F.has(bt)||(at.push(bt),F.set(bt,!0))}return at}var i=function(){for(var m=[],F=0;F<arguments.length;F++)m[F]=arguments[F];return v([].concat.apply([],m))},y=i,x=function(m,F){for(var at=[],lt={},Ct=0;Ct<m.length;Ct++){var bt=m[Ct],zt=bt[F];if(!E(zt)){O(zt)||(zt=[zt]);for(var qt=0;qt<zt.length;qt++){var lr=zt[qt];lt[lr]||(at.push(lr),lt[lr]=!0)}}}return at};function o(m){if(_(m))return m[0]}function h(m){if(_(m)){var F=m;return F[F.length-1]}}function d(m,F){return O(m)||e(m)?m[0]===F:!1}var B=d;function K(m,F){return O(m)||e(m)?m[m.length-1]===F:!1}var tt=K,et=function(m,F){for(var at=0;at<m.length;at++)if(!F(m[at],at))return!1;return!0},ft=et,ct=function(m,F){for(var at=0;at<m.length;at++)if(F(m[at],at))return!0;return!1},_t=ct,At=Object.prototype.hasOwnProperty;function It(m,F){if(!F||!O(m))return{};for(var at={},lt=Y(F)?F:function(qt){return qt[F]},Ct,bt=0;bt<m.length;bt++){var zt=m[bt];Ct=lt(zt),At.call(at,Ct)?at[Ct].push(zt):at[Ct]=[zt]}return at}var Xt=It;function St(m,F){if(!F)return{0:m};if(!Y(F)){var at=O(F)?F:F.replace(/\s+/g,"").split("*");F=function(lt){for(var Ct="_",bt=0,zt=at.length;bt<zt;bt++)Ct+=lt[at[bt]]&&lt[at[bt]].toString();return Ct}}return Xt(m,F)}var kt=function(m,F){if(!F)return[m];var at=St(m,F),lt=[];for(var Ct in at)lt.push(at[Ct]);return lt};function Rt(m,F){return m["_wrap_"+F]}var Wt=Rt;function Ot(m,F){if(m["_wrap_"+F])return m["_wrap_"+F];var at=function(lt){m[F](lt)};return m["_wrap_"+F]=at,at}var Ft=Ot,Kt={};function Zt(m){var F=Kt[m];if(!F){for(var at=m.toString(16),lt=at.length;lt<6;lt++)at="0"+at;F="#"+at,Kt[m]=F}return F}var Gt=Zt;function Vt(m){var F=0,at=0,lt=0,Ct=0;return O(m)?m.length===1?F=at=lt=Ct=m[0]:m.length===2?(F=lt=m[0],at=Ct=m[1]):m.length===3?(F=m[0],at=Ct=m[1],lt=m[2]):(F=m[0],at=m[1],lt=m[2],Ct=m[3]):F=at=lt=Ct=m,{r1:F,r2:at,r3:lt,r4:Ct}}var Qt=Vt,tr=function(m,F,at){return m<F?F:m>at?at:m},ur=tr,er=function(m,F){var at=F.toString(),lt=at.indexOf(".");if(lt===-1)return Math.round(m);var Ct=at.substr(lt+1).length;return Ct>20&&(Ct=20),parseFloat(m.toFixed(Ct))},xr=er,Ar=function(m){return W(m,"Number")},nr=Ar,wr=function(m){return nr(m)&&m%1!==0},mr=wr,vr=function(m){return nr(m)&&m%2===0},Cr=vr,or=Number.isInteger?Number.isInteger:function(m){return nr(m)&&m%1===0},Dr=or,br=function(m){return nr(m)&&m<0},Ir=br,Yr=1e-5;function Gr(m,F,at){return at===void 0&&(at=Yr),Math.abs(m-F)<at}var Vr=function(m){return nr(m)&&m%2!==0},Qr=Vr,$r=function(m){return nr(m)&&m>0},Jr=$r,qr=function(m,F){if(O(m)){for(var at,lt=-1/0,Ct=0;Ct<m.length;Ct++){var bt=m[Ct],zt=Y(F)?F(bt):bt[F];zt>lt&&(at=bt,lt=zt)}return at}},jr=function(m,F){if(O(m)){for(var at,lt=1/0,Ct=0;Ct<m.length;Ct++){var bt=m[Ct],zt=Y(F)?F(bt):bt[F];zt<lt&&(at=bt,lt=zt)}return at}},tn=function(m,F){return(m%F+F)%F},rn=tn,nn=180/Math.PI,en=function(m){return nn*m},Xr=en,an=parseInt,on=Math.PI/180,sn=function(m){return on*m},un=sn,fn=st,Hr=function(m,F){return m.hasOwnProperty(F)},Br=Hr,cn=Object.values?function(m){return Object.values(m)}:function(m){var F=[];return st(m,function(at,lt){Y(m)&&lt==="prototype"||F.push(at)}),F},Tr=cn,ln=function(m,F){return Q(Tr(m),F)},_r=function(m){return E(m)?"":m.toString()},hn=function(m){return _r(m).toLowerCase()},vn=hn,pn=function(m){var F=_r(m);return F.charAt(0).toLowerCase()+F.substring(1)},gn=pn;function w(m,F){return!m||!F?m:m.replace(/\\?\{([^{}]+)\}/g,function(at,lt){return at.charAt(0)==="\\"?at.slice(1):F[lt]===void 0?"":F[lt]})}var I=w,R=function(m){return _r(m).toUpperCase()},ot=R,rt=function(m){var F=_r(m);return F.charAt(0).toUpperCase()+F.substring(1)},gt=rt,Dt={}.toString,Tt=function(m){return Dt.call(m).replace(/^\[object /,"").replace(/]$/,"")},Ut=Tt,Bt=function(m){return W(m,"Arguments")},Ht=Bt,Jt=function(m){return W(m,"Boolean")},$t=Jt,rr=function(m){return W(m,"Date")},jt=rr,ir=function(m){return W(m,"Error")},sr=ir;function pr(m){return nr(m)&&isFinite(m)}var ar=function(m){return m===null},Sr=ar,hr=Object.prototype,fr=function(m){var F=m&&m.constructor,at=typeof F=="function"&&F.prototype||hr;return m===at},gr=fr,Or=function(m){return W(m,"RegExp")},cr=Or,yr=function(m){return m===void 0},Lr=yr,Fr=function(m){return m instanceof Element||m instanceof HTMLDocument},kr=Fr;function dn(m){var F=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||function(at){return setTimeout(at,16)};return F(m)}function mn(m){var F=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.msCancelAnimationFrame||clearTimeout;F(m)}function Rr(m,F){for(var at in F)F.hasOwnProperty(at)&&at!=="constructor"&&F[at]!==void 0&&(m[at]=F[at])}function Er(m,F,at,lt){return F&&Rr(m,F),at&&Rr(m,at),lt&&Rr(m,lt),m}var Ur=function(){for(var m=[],F=0;F<arguments.length;F++)m[F]=arguments[F];for(var at=m[0],lt=1;lt<m.length;lt++){var Ct=m[lt];Y(Ct)&&(Ct=Ct.prototype),Er(at.prototype,Ct)}},Zr=Ur,Nr=function(m){if(typeof m!="object"||m===null)return m;var F;if(O(m)){F=[];for(var at=0,lt=m.length;at<lt;at++)typeof m[at]=="object"&&m[at]!=null?F[at]=Nr(m[at]):F[at]=m[at]}else{F={};for(var Ct in m)typeof m[Ct]=="object"&&m[Ct]!=null?F[Ct]=Nr(m[Ct]):F[Ct]=m[Ct]}return F},Pr=Nr;function An(m,F,at){var lt;return function(){var Ct=this,bt=arguments,zt=function(){lt=null,at||m.apply(Ct,bt)},qt=at&&!lt;clearTimeout(lt),lt=setTimeout(zt,F),qt&&m.apply(Ct,bt)}}var yn=An,wn=function(m,F){if(!Y(m))throw new TypeError("Expected a function");var at=function(){for(var lt=[],Ct=0;Ct<arguments.length;Ct++)lt[Ct]=arguments[Ct];var bt=F?F.apply(this,lt):lt[0],zt=at.cache;if(zt.has(bt))return zt.get(bt);var qt=m.apply(this,lt);return zt.set(bt,qt),qt};return at.cache=new Map,at},En=5;function Cn(m,F,at,lt){at=at||0,lt=lt||En;for(var Ct in F)if(F.hasOwnProperty(Ct)){var bt=F[Ct];bt!==null&&Pt(bt)?(Pt(m[Ct])||(m[Ct]={}),at<lt?Cn(m[Ct],bt,at+1,lt):m[Ct]=F[Ct]):O(bt)?(m[Ct]=[],m[Ct]=m[Ct].concat(bt)):bt!==void 0&&(m[Ct]=bt)}}var Pn=function(m){for(var F=[],at=1;at<arguments.length;at++)F[at-1]=arguments[at];for(var lt=0;lt<F.length;lt+=1)Cn(m,F[lt]);return m},Dn=Pn,Tn=function(m,F,at,lt){Y(F)||(at=F,F=m,m=function(){});var Ct=Object.create?function(zt,qt){return Object.create(zt,{constructor:{value:qt}})}:function(zt,qt){function lr(){}lr.prototype=zt;var dr=new lr;return dr.constructor=qt,dr},bt=Ct(F.prototype,m);return m.prototype=Er(bt,m.prototype),m.superclass=Ct(F.prototype,F),Er(bt,at),Er(m,lt),m},Sn=Tn,On=function(m,F){if(!_(m))return-1;var at=Array.prototype.indexOf;if(at)return at.call(m,F);for(var lt=-1,Ct=0;Ct<m.length;Ct++)if(m[Ct]===F){lt=Ct;break}return lt},bn=On,In=Object.prototype.hasOwnProperty;function Bn(m){if(E(m))return!0;if(_(m))return!m.length;var F=Ut(m);if(F==="Map"||F==="Set")return!m.size;if(gr(m))return!Object.keys(m).length;for(var at in m)if(In.call(m,at))return!1;return!0}var Ln=Bn,Mn=function(m,F){if(m===F)return!0;if(!m||!F||e(m)||e(F))return!1;if(_(m)||_(F)){if(m.length!==F.length)return!1;for(var at=!0,lt=0;lt<m.length&&(at=Mn(m[lt],F[lt]),!!at);lt++);return at}if(ht(m)||ht(F)){var Ct=Object.keys(m),bt=Object.keys(F);if(Ct.length!==bt.length)return!1;for(var at=!0,lt=0;lt<Ct.length&&(at=Mn(m[Ct[lt]],F[Ct[lt]]),!!at);lt++);return at}return!1},_n=Mn,kn=function(m,F,at){return Y(at)?!!at(m,F):_n(m,F)},Rn=function(m,F){if(!_(m))return m;for(var at=[],lt=0;lt<m.length;lt++){var Ct=m[lt];at.push(F(Ct,lt))}return at},Nn=Rn,Yn=function(m){return m},Fn=function(m,F){F===void 0&&(F=Yn);var at={};return U(m)&&!E(m)&&Object.keys(m).forEach(function(lt){at[lt]=F(m[lt],lt)}),at},Wn=function(m,F,at){for(var lt=0,Ct=e(F)?F.split("."):F;m&&lt<Ct.length;)m=m[Ct[lt++]];return m===void 0||lt<Ct.length?at:m},Xn=function(m,F,at){var lt=m,Ct=e(F)?F.split("."):F;return Ct.forEach(function(bt,zt){zt<Ct.length-1?(U(lt[bt])||(lt[bt]=nr(Ct[zt+1])?[]:{}),lt=lt[bt]):lt[bt]=at}),m},Hn=Object.prototype.hasOwnProperty,Un=function(m,F){if(m===null||!Pt(m))return{};var at={};return st(F,function(lt){Hn.call(m,lt)&&(at[lt]=m[lt])}),at},Zn=function(m,F){return n(m,function(at,lt,Ct){return F.includes(Ct)||(at[Ct]=lt),at},{})},zn=function(m,F,at){var lt,Ct,bt,zt,qt=0;at||(at={});var lr=function(){qt=at.leading===!1?0:Date.now(),lt=null,zt=m.apply(Ct,bt),lt||(Ct=bt=null)},dr=function(){var Mr=Date.now();!qt&&at.leading===!1&&(qt=Mr);var xn=F-(Mr-qt);return Ct=this,bt=arguments,xn<=0||xn>F?(lt&&(clearTimeout(lt),lt=null),qt=Mr,zt=m.apply(Ct,bt),lt||(Ct=bt=null)):!lt&&at.trailing!==!1&&(lt=setTimeout(lr,xn)),zt};return dr.cancel=function(){clearTimeout(lt),qt=0,lt=Ct=bt=null},dr},Kn=function(m){return _(m)?Array.prototype.slice.call(m):[]},zr={},Gn=function(m){return m=m||"g",zr[m]?zr[m]+=1:zr[m]=1,m+zr[m]},Vn=function(){},Qn=function(m){return m};function $n(m){return E(m)?0:_(m)?m.length:Object.keys(m).length}var Jn=k(92336),Kr,Wr=wn(function(m,F){F===void 0&&(F={});var at=F.fontSize,lt=F.fontFamily,Ct=F.fontWeight,bt=F.fontStyle,zt=F.fontVariant;return Kr||(Kr=document.createElement("canvas").getContext("2d")),Kr.font=[bt,zt,Ct,at+"px",lt].join(" "),Kr.measureText(e(m)?m:"").width},function(m,F){return F===void 0&&(F={}),(0,Jn.__spreadArrays)([m],Tr(F)).join("")}),qn=function(m,F,at,lt){lt===void 0&&(lt="...");var Ct=16,bt=Wr(lt,at),zt=e(m)?m:_r(m),qt=F,lr=[],dr,Mr;if(Wr(m,at)<=F)return m;for(;dr=zt.substr(0,Ct),Mr=Wr(dr,at),!(Mr+bt>qt&&Mr>qt);)if(lr.push(dr),qt-=Mr,zt=zt.substr(Ct),!zt)return lr.join("");for(;dr=zt.substr(0,1),Mr=Wr(dr,at),!(Mr+bt>qt);)if(lr.push(dr),qt-=Mr,zt=zt.substr(1),!zt)return lr.join("");return""+lr.join("")+lt},jn=function(){function m(){this.map={}}return m.prototype.has=function(F){return this.map[F]!==void 0},m.prototype.get=function(F,at){var lt=this.map[F];return lt===void 0?at:lt},m.prototype.set=function(F,at){this.map[F]=at},m.prototype.clear=function(){this.map={}},m.prototype.delete=function(F){delete this.map[F]},m.prototype.size=function(){return Object.keys(this.map).length},m}(),te=jn},79935:function(Lt,Mt,k){"use strict";k.d(Mt,{qY:function(){return D}});var V=k(73656),_=function(C,S,Z){if(Z||arguments.length===2)for(var J=0,ht=S.length,mt;J<ht;J++)(mt||!(J in S))&&(mt||(mt=Array.prototype.slice.call(S,0,J)),mt[J]=S[J]);return C.concat(mt||Array.prototype.slice.call(S))},it=function(){function C(S,Z,J){this.name=S,this.version=Z,this.os=J,this.type="browser"}return C}(),Q=function(){function C(S){this.version=S,this.type="node",this.name="node",this.os=V.platform}return C}(),j=function(){function C(S,Z,J,ht){this.name=S,this.version=Z,this.os=J,this.bot=ht,this.type="bot-device"}return C}(),X=function(){function C(){this.type="bot",this.bot=!0,this.name="bot",this.version=null,this.os=null}return C}(),b=function(){function C(){this.type="react-native",this.name="react-native",this.version=null,this.os=null}return C}(),N=/alexa|bot|crawl(er|ing)|facebookexternalhit|feedburner|google web preview|nagios|postrank|pingdom|slurp|spider|yahoo!|yandex/,z=/(nuhk|curl|Googlebot|Yammybot|Openbot|Slurp|MSNBot|Ask\ Jeeves\/Teoma|ia_archiver)/,q=3,W=[["aol",/AOLShield\/([0-9\._]+)/],["edge",/Edge\/([0-9\._]+)/],["edge-ios",/EdgiOS\/([0-9\._]+)/],["yandexbrowser",/YaBrowser\/([0-9\._]+)/],["kakaotalk",/KAKAOTALK\s([0-9\.]+)/],["samsung",/SamsungBrowser\/([0-9\.]+)/],["silk",/\bSilk\/([0-9._-]+)\b/],["miui",/MiuiBrowser\/([0-9\.]+)$/],["beaker",/BeakerBrowser\/([0-9\.]+)/],["edge-chromium",/EdgA?\/([0-9\.]+)/],["chromium-webview",/(?!Chrom.*OPR)wv\).*Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["chrome",/(?!Chrom.*OPR)Chrom(?:e|ium)\/([0-9\.]+)(:?\s|$)/],["phantomjs",/PhantomJS\/([0-9\.]+)(:?\s|$)/],["crios",/CriOS\/([0-9\.]+)(:?\s|$)/],["firefox",/Firefox\/([0-9\.]+)(?:\s|$)/],["fxios",/FxiOS\/([0-9\.]+)/],["opera-mini",/Opera Mini.*Version\/([0-9\.]+)/],["opera",/Opera\/([0-9\.]+)(?:\s|$)/],["opera",/OPR\/([0-9\.]+)(:?\s|$)/],["pie",/^Microsoft Pocket Internet Explorer\/(\d+\.\d+)$/],["pie",/^Mozilla\/\d\.\d+\s\(compatible;\s(?:MSP?IE|MSInternet Explorer) (\d+\.\d+);.*Windows CE.*\)$/],["netfront",/^Mozilla\/\d\.\d+.*NetFront\/(\d.\d)/],["ie",/Trident\/7\.0.*rv\:([0-9\.]+).*\).*Gecko$/],["ie",/MSIE\s([0-9\.]+);.*Trident\/[4-7].0/],["ie",/MSIE\s(7\.0)/],["bb10",/BB10;\sTouch.*Version\/([0-9\.]+)/],["android",/Android\s([0-9\.]+)/],["ios",/Version\/([0-9\._]+).*Mobile.*Safari.*/],["safari",/Version\/([0-9\._]+).*Safari/],["facebook",/FB[AS]V\/([0-9\.]+)/],["instagram",/Instagram\s([0-9\.]+)/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Mobile/],["ios-webview",/AppleWebKit\/([0-9\.]+).*Gecko\)$/],["curl",/^curl\/([0-9\.]+)$/],["searchbot",N]],Y=[["iOS",/iP(hone|od|ad)/],["Android OS",/Android/],["BlackBerry OS",/BlackBerry|BB10/],["Windows Mobile",/IEMobile/],["Amazon OS",/Kindle/],["Windows 3.11",/Win16/],["Windows 95",/(Windows 95)|(Win95)|(Windows_95)/],["Windows 98",/(Windows 98)|(Win98)/],["Windows 2000",/(Windows NT 5.0)|(Windows 2000)/],["Windows XP",/(Windows NT 5.1)|(Windows XP)/],["Windows Server 2003",/(Windows NT 5.2)/],["Windows Vista",/(Windows NT 6.0)/],["Windows 7",/(Windows NT 6.1)/],["Windows 8",/(Windows NT 6.2)/],["Windows 8.1",/(Windows NT 6.3)/],["Windows 10",/(Windows NT 10.0)/],["Windows ME",/Windows ME/],["Windows CE",/Windows CE|WinCE|Microsoft Pocket Internet Explorer/],["Open BSD",/OpenBSD/],["Sun OS",/SunOS/],["Chrome OS",/CrOS/],["Linux",/(Linux)|(X11)/],["Mac OS",/(Mac_PowerPC)|(Macintosh)/],["QNX",/QNX/],["BeOS",/BeOS/],["OS/2",/OS\/2/]];function D(C){return C?U(C):typeof document=="undefined"&&typeof navigator!="undefined"&&navigator.product==="ReactNative"?new b:typeof navigator!="undefined"?U(navigator.userAgent):st()}function E(C){return C!==""&&W.reduce(function(S,Z){var J=Z[0],ht=Z[1];if(S)return S;var mt=ht.exec(C);return!!mt&&[J,mt]},!1)}function O(C){var S=E(C);return S?S[0]:null}function U(C){var S=E(C);if(!S)return null;var Z=S[0],J=S[1];if(Z==="searchbot")return new X;var ht=J[1]&&J[1].split(".").join("_").split("_").slice(0,3);ht?ht.length<q&&(ht=_(_([],ht,!0),H(q-ht.length),!0)):ht=[];var mt=ht.join("."),Pt=nt(C),Yt=z.exec(C);return Yt&&Yt[1]?new j(Z,mt,Pt,Yt[1]):new it(Z,mt,Pt)}function nt(C){for(var S=0,Z=Y.length;S<Z;S++){var J=Y[S],ht=J[0],mt=J[1],Pt=mt.exec(C);if(Pt)return ht}return null}function st(){var C=typeof V!="undefined"&&V.version;return C?new Q(V.version.slice(1)):null}function H(C){for(var S=[],Z=0;Z<C;Z++)S.push("0");return S}},34249:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{ARRAY_TYPE:function(){return _},EPSILON:function(){return V},RANDOM:function(){return it},equals:function(){return b},setMatrixArrayType:function(){return Q},toRadian:function(){return X}});var V=1e-6,_=typeof Float32Array!="undefined"?Float32Array:Array,it=Math.random;function Q(N){_=N}var j=Math.PI/180;function X(N){return N*j}function b(N,z){return Math.abs(N-z)<=V*Math.max(1,Math.abs(N),Math.abs(z))}Math.hypot||(Math.hypot=function(){for(var N=0,z=arguments.length;z--;)N+=arguments[z]*arguments[z];return Math.sqrt(N)})},96661:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{add:function(){return Pt},adjoint:function(){return W},clone:function(){return Q},copy:function(){return j},create:function(){return _},determinant:function(){return Y},equals:function(){return xt},exactEquals:function(){return pt},frob:function(){return mt},fromMat2d:function(){return C},fromMat4:function(){return it},fromQuat:function(){return S},fromRotation:function(){return st},fromScaling:function(){return H},fromTranslation:function(){return nt},fromValues:function(){return X},identity:function(){return N},invert:function(){return q},mul:function(){return Et},multiply:function(){return D},multiplyScalar:function(){return Nt},multiplyScalarAndAdd:function(){return dt},normalFromMat4:function(){return Z},projection:function(){return J},rotate:function(){return O},scale:function(){return U},set:function(){return b},str:function(){return ht},sub:function(){return wt},subtract:function(){return Yt},translate:function(){return E},transpose:function(){return z}});var V=k(34249);function _(){var c=new V.ARRAY_TYPE(9);return V.ARRAY_TYPE!=Float32Array&&(c[1]=0,c[2]=0,c[3]=0,c[5]=0,c[6]=0,c[7]=0),c[0]=1,c[4]=1,c[8]=1,c}function it(c,A){return c[0]=A[0],c[1]=A[1],c[2]=A[2],c[3]=A[4],c[4]=A[5],c[5]=A[6],c[6]=A[8],c[7]=A[9],c[8]=A[10],c}function Q(c){var A=new V.ARRAY_TYPE(9);return A[0]=c[0],A[1]=c[1],A[2]=c[2],A[3]=c[3],A[4]=c[4],A[5]=c[5],A[6]=c[6],A[7]=c[7],A[8]=c[8],A}function j(c,A){return c[0]=A[0],c[1]=A[1],c[2]=A[2],c[3]=A[3],c[4]=A[4],c[5]=A[5],c[6]=A[6],c[7]=A[7],c[8]=A[8],c}function X(c,A,G,vt,yt,p,M,L,T){var $=new V.ARRAY_TYPE(9);return $[0]=c,$[1]=A,$[2]=G,$[3]=vt,$[4]=yt,$[5]=p,$[6]=M,$[7]=L,$[8]=T,$}function b(c,A,G,vt,yt,p,M,L,T,$){return c[0]=A,c[1]=G,c[2]=vt,c[3]=yt,c[4]=p,c[5]=M,c[6]=L,c[7]=T,c[8]=$,c}function N(c){return c[0]=1,c[1]=0,c[2]=0,c[3]=0,c[4]=1,c[5]=0,c[6]=0,c[7]=0,c[8]=1,c}function z(c,A){if(c===A){var G=A[1],vt=A[2],yt=A[5];c[1]=A[3],c[2]=A[6],c[3]=G,c[5]=A[7],c[6]=vt,c[7]=yt}else c[0]=A[0],c[1]=A[3],c[2]=A[6],c[3]=A[1],c[4]=A[4],c[5]=A[7],c[6]=A[2],c[7]=A[5],c[8]=A[8];return c}function q(c,A){var G=A[0],vt=A[1],yt=A[2],p=A[3],M=A[4],L=A[5],T=A[6],$=A[7],ut=A[8],P=ut*M-L*$,g=-ut*p+L*T,r=$*p-M*T,t=G*P+vt*g+yt*r;return t?(t=1/t,c[0]=P*t,c[1]=(-ut*vt+yt*$)*t,c[2]=(L*vt-yt*M)*t,c[3]=g*t,c[4]=(ut*G-yt*T)*t,c[5]=(-L*G+yt*p)*t,c[6]=r*t,c[7]=(-$*G+vt*T)*t,c[8]=(M*G-vt*p)*t,c):null}function W(c,A){var G=A[0],vt=A[1],yt=A[2],p=A[3],M=A[4],L=A[5],T=A[6],$=A[7],ut=A[8];return c[0]=M*ut-L*$,c[1]=yt*$-vt*ut,c[2]=vt*L-yt*M,c[3]=L*T-p*ut,c[4]=G*ut-yt*T,c[5]=yt*p-G*L,c[6]=p*$-M*T,c[7]=vt*T-G*$,c[8]=G*M-vt*p,c}function Y(c){var A=c[0],G=c[1],vt=c[2],yt=c[3],p=c[4],M=c[5],L=c[6],T=c[7],$=c[8];return A*($*p-M*T)+G*(-$*yt+M*L)+vt*(T*yt-p*L)}function D(c,A,G){var vt=A[0],yt=A[1],p=A[2],M=A[3],L=A[4],T=A[5],$=A[6],ut=A[7],P=A[8],g=G[0],r=G[1],t=G[2],f=G[3],n=G[4],a=G[5],s=G[6],e=G[7],u=G[8];return c[0]=g*vt+r*M+t*$,c[1]=g*yt+r*L+t*ut,c[2]=g*p+r*T+t*P,c[3]=f*vt+n*M+a*$,c[4]=f*yt+n*L+a*ut,c[5]=f*p+n*T+a*P,c[6]=s*vt+e*M+u*$,c[7]=s*yt+e*L+u*ut,c[8]=s*p+e*T+u*P,c}function E(c,A,G){var vt=A[0],yt=A[1],p=A[2],M=A[3],L=A[4],T=A[5],$=A[6],ut=A[7],P=A[8],g=G[0],r=G[1];return c[0]=vt,c[1]=yt,c[2]=p,c[3]=M,c[4]=L,c[5]=T,c[6]=g*vt+r*M+$,c[7]=g*yt+r*L+ut,c[8]=g*p+r*T+P,c}function O(c,A,G){var vt=A[0],yt=A[1],p=A[2],M=A[3],L=A[4],T=A[5],$=A[6],ut=A[7],P=A[8],g=Math.sin(G),r=Math.cos(G);return c[0]=r*vt+g*M,c[1]=r*yt+g*L,c[2]=r*p+g*T,c[3]=r*M-g*vt,c[4]=r*L-g*yt,c[5]=r*T-g*p,c[6]=$,c[7]=ut,c[8]=P,c}function U(c,A,G){var vt=G[0],yt=G[1];return c[0]=vt*A[0],c[1]=vt*A[1],c[2]=vt*A[2],c[3]=yt*A[3],c[4]=yt*A[4],c[5]=yt*A[5],c[6]=A[6],c[7]=A[7],c[8]=A[8],c}function nt(c,A){return c[0]=1,c[1]=0,c[2]=0,c[3]=0,c[4]=1,c[5]=0,c[6]=A[0],c[7]=A[1],c[8]=1,c}function st(c,A){var G=Math.sin(A),vt=Math.cos(A);return c[0]=vt,c[1]=G,c[2]=0,c[3]=-G,c[4]=vt,c[5]=0,c[6]=0,c[7]=0,c[8]=1,c}function H(c,A){return c[0]=A[0],c[1]=0,c[2]=0,c[3]=0,c[4]=A[1],c[5]=0,c[6]=0,c[7]=0,c[8]=1,c}function C(c,A){return c[0]=A[0],c[1]=A[1],c[2]=0,c[3]=A[2],c[4]=A[3],c[5]=0,c[6]=A[4],c[7]=A[5],c[8]=1,c}function S(c,A){var G=A[0],vt=A[1],yt=A[2],p=A[3],M=G+G,L=vt+vt,T=yt+yt,$=G*M,ut=vt*M,P=vt*L,g=yt*M,r=yt*L,t=yt*T,f=p*M,n=p*L,a=p*T;return c[0]=1-P-t,c[3]=ut-a,c[6]=g+n,c[1]=ut+a,c[4]=1-$-t,c[7]=r-f,c[2]=g-n,c[5]=r+f,c[8]=1-$-P,c}function Z(c,A){var G=A[0],vt=A[1],yt=A[2],p=A[3],M=A[4],L=A[5],T=A[6],$=A[7],ut=A[8],P=A[9],g=A[10],r=A[11],t=A[12],f=A[13],n=A[14],a=A[15],s=G*L-vt*M,e=G*T-yt*M,u=G*$-p*M,l=vt*T-yt*L,v=vt*$-p*L,i=yt*$-p*T,y=ut*f-P*t,x=ut*n-g*t,o=ut*a-r*t,h=P*n-g*f,d=P*a-r*f,B=g*a-r*n,K=s*B-e*d+u*h+l*o-v*x+i*y;return K?(K=1/K,c[0]=(L*B-T*d+$*h)*K,c[1]=(T*o-M*B-$*x)*K,c[2]=(M*d-L*o+$*y)*K,c[3]=(yt*d-vt*B-p*h)*K,c[4]=(G*B-yt*o+p*x)*K,c[5]=(vt*o-G*d-p*y)*K,c[6]=(f*i-n*v+a*l)*K,c[7]=(n*u-t*i-a*e)*K,c[8]=(t*v-f*u+a*s)*K,c):null}function J(c,A,G){return c[0]=2/A,c[1]=0,c[2]=0,c[3]=0,c[4]=-2/G,c[5]=0,c[6]=-1,c[7]=1,c[8]=1,c}function ht(c){return"mat3("+c[0]+", "+c[1]+", "+c[2]+", "+c[3]+", "+c[4]+", "+c[5]+", "+c[6]+", "+c[7]+", "+c[8]+")"}function mt(c){return Math.hypot(c[0],c[1],c[2],c[3],c[4],c[5],c[6],c[7],c[8])}function Pt(c,A,G){return c[0]=A[0]+G[0],c[1]=A[1]+G[1],c[2]=A[2]+G[2],c[3]=A[3]+G[3],c[4]=A[4]+G[4],c[5]=A[5]+G[5],c[6]=A[6]+G[6],c[7]=A[7]+G[7],c[8]=A[8]+G[8],c}function Yt(c,A,G){return c[0]=A[0]-G[0],c[1]=A[1]-G[1],c[2]=A[2]-G[2],c[3]=A[3]-G[3],c[4]=A[4]-G[4],c[5]=A[5]-G[5],c[6]=A[6]-G[6],c[7]=A[7]-G[7],c[8]=A[8]-G[8],c}function Nt(c,A,G){return c[0]=A[0]*G,c[1]=A[1]*G,c[2]=A[2]*G,c[3]=A[3]*G,c[4]=A[4]*G,c[5]=A[5]*G,c[6]=A[6]*G,c[7]=A[7]*G,c[8]=A[8]*G,c}function dt(c,A,G,vt){return c[0]=A[0]+G[0]*vt,c[1]=A[1]+G[1]*vt,c[2]=A[2]+G[2]*vt,c[3]=A[3]+G[3]*vt,c[4]=A[4]+G[4]*vt,c[5]=A[5]+G[5]*vt,c[6]=A[6]+G[6]*vt,c[7]=A[7]+G[7]*vt,c[8]=A[8]+G[8]*vt,c}function pt(c,A){return c[0]===A[0]&&c[1]===A[1]&&c[2]===A[2]&&c[3]===A[3]&&c[4]===A[4]&&c[5]===A[5]&&c[6]===A[6]&&c[7]===A[7]&&c[8]===A[8]}function xt(c,A){var G=c[0],vt=c[1],yt=c[2],p=c[3],M=c[4],L=c[5],T=c[6],$=c[7],ut=c[8],P=A[0],g=A[1],r=A[2],t=A[3],f=A[4],n=A[5],a=A[6],s=A[7],e=A[8];return Math.abs(G-P)<=V.EPSILON*Math.max(1,Math.abs(G),Math.abs(P))&&Math.abs(vt-g)<=V.EPSILON*Math.max(1,Math.abs(vt),Math.abs(g))&&Math.abs(yt-r)<=V.EPSILON*Math.max(1,Math.abs(yt),Math.abs(r))&&Math.abs(p-t)<=V.EPSILON*Math.max(1,Math.abs(p),Math.abs(t))&&Math.abs(M-f)<=V.EPSILON*Math.max(1,Math.abs(M),Math.abs(f))&&Math.abs(L-n)<=V.EPSILON*Math.max(1,Math.abs(L),Math.abs(n))&&Math.abs(T-a)<=V.EPSILON*Math.max(1,Math.abs(T),Math.abs(a))&&Math.abs($-s)<=V.EPSILON*Math.max(1,Math.abs($),Math.abs(s))&&Math.abs(ut-e)<=V.EPSILON*Math.max(1,Math.abs(ut),Math.abs(e))}var Et=D,wt=Yt},69341:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{add:function(){return b},angle:function(){return c},ceil:function(){return W},clone:function(){return it},copy:function(){return j},create:function(){return _},cross:function(){return Pt},dist:function(){return $},distance:function(){return st},div:function(){return T},divide:function(){return q},dot:function(){return mt},equals:function(){return yt},exactEquals:function(){return vt},floor:function(){return Y},forEach:function(){return g},fromValues:function(){return Q},inverse:function(){return J},len:function(){return p},length:function(){return C},lerp:function(){return Yt},max:function(){return E},min:function(){return D},mul:function(){return L},multiply:function(){return z},negate:function(){return Z},normalize:function(){return ht},random:function(){return Nt},rotate:function(){return wt},round:function(){return O},scale:function(){return U},scaleAndAdd:function(){return nt},set:function(){return X},sqrDist:function(){return ut},sqrLen:function(){return P},squaredDistance:function(){return H},squaredLength:function(){return S},str:function(){return G},sub:function(){return M},subtract:function(){return N},transformMat2:function(){return dt},transformMat2d:function(){return pt},transformMat3:function(){return xt},transformMat4:function(){return Et},zero:function(){return A}});var V=k(34249);function _(){var r=new V.ARRAY_TYPE(2);return V.ARRAY_TYPE!=Float32Array&&(r[0]=0,r[1]=0),r}function it(r){var t=new V.ARRAY_TYPE(2);return t[0]=r[0],t[1]=r[1],t}function Q(r,t){var f=new V.ARRAY_TYPE(2);return f[0]=r,f[1]=t,f}function j(r,t){return r[0]=t[0],r[1]=t[1],r}function X(r,t,f){return r[0]=t,r[1]=f,r}function b(r,t,f){return r[0]=t[0]+f[0],r[1]=t[1]+f[1],r}function N(r,t,f){return r[0]=t[0]-f[0],r[1]=t[1]-f[1],r}function z(r,t,f){return r[0]=t[0]*f[0],r[1]=t[1]*f[1],r}function q(r,t,f){return r[0]=t[0]/f[0],r[1]=t[1]/f[1],r}function W(r,t){return r[0]=Math.ceil(t[0]),r[1]=Math.ceil(t[1]),r}function Y(r,t){return r[0]=Math.floor(t[0]),r[1]=Math.floor(t[1]),r}function D(r,t,f){return r[0]=Math.min(t[0],f[0]),r[1]=Math.min(t[1],f[1]),r}function E(r,t,f){return r[0]=Math.max(t[0],f[0]),r[1]=Math.max(t[1],f[1]),r}function O(r,t){return r[0]=Math.round(t[0]),r[1]=Math.round(t[1]),r}function U(r,t,f){return r[0]=t[0]*f,r[1]=t[1]*f,r}function nt(r,t,f,n){return r[0]=t[0]+f[0]*n,r[1]=t[1]+f[1]*n,r}function st(r,t){var f=t[0]-r[0],n=t[1]-r[1];return Math.hypot(f,n)}function H(r,t){var f=t[0]-r[0],n=t[1]-r[1];return f*f+n*n}function C(r){var t=r[0],f=r[1];return Math.hypot(t,f)}function S(r){var t=r[0],f=r[1];return t*t+f*f}function Z(r,t){return r[0]=-t[0],r[1]=-t[1],r}function J(r,t){return r[0]=1/t[0],r[1]=1/t[1],r}function ht(r,t){var f=t[0],n=t[1],a=f*f+n*n;return a>0&&(a=1/Math.sqrt(a)),r[0]=t[0]*a,r[1]=t[1]*a,r}function mt(r,t){return r[0]*t[0]+r[1]*t[1]}function Pt(r,t,f){var n=t[0]*f[1]-t[1]*f[0];return r[0]=r[1]=0,r[2]=n,r}function Yt(r,t,f,n){var a=t[0],s=t[1];return r[0]=a+n*(f[0]-a),r[1]=s+n*(f[1]-s),r}function Nt(r,t){t=t||1;var f=V.RANDOM()*2*Math.PI;return r[0]=Math.cos(f)*t,r[1]=Math.sin(f)*t,r}function dt(r,t,f){var n=t[0],a=t[1];return r[0]=f[0]*n+f[2]*a,r[1]=f[1]*n+f[3]*a,r}function pt(r,t,f){var n=t[0],a=t[1];return r[0]=f[0]*n+f[2]*a+f[4],r[1]=f[1]*n+f[3]*a+f[5],r}function xt(r,t,f){var n=t[0],a=t[1];return r[0]=f[0]*n+f[3]*a+f[6],r[1]=f[1]*n+f[4]*a+f[7],r}function Et(r,t,f){var n=t[0],a=t[1];return r[0]=f[0]*n+f[4]*a+f[12],r[1]=f[1]*n+f[5]*a+f[13],r}function wt(r,t,f,n){var a=t[0]-f[0],s=t[1]-f[1],e=Math.sin(n),u=Math.cos(n);return r[0]=a*u-s*e+f[0],r[1]=a*e+s*u+f[1],r}function c(r,t){var f=r[0],n=r[1],a=t[0],s=t[1],e=Math.sqrt(f*f+n*n)*Math.sqrt(a*a+s*s),u=e&&(f*a+n*s)/e;return Math.acos(Math.min(Math.max(u,-1),1))}function A(r){return r[0]=0,r[1]=0,r}function G(r){return"vec2("+r[0]+", "+r[1]+")"}function vt(r,t){return r[0]===t[0]&&r[1]===t[1]}function yt(r,t){var f=r[0],n=r[1],a=t[0],s=t[1];return Math.abs(f-a)<=V.EPSILON*Math.max(1,Math.abs(f),Math.abs(a))&&Math.abs(n-s)<=V.EPSILON*Math.max(1,Math.abs(n),Math.abs(s))}var p=C,M=N,L=z,T=q,$=st,ut=H,P=S,g=function(){var r=_();return function(t,f,n,a,s,e){var u,l;for(f||(f=2),n||(n=0),a?l=Math.min(a*f+n,t.length):l=t.length,u=n;u<l;u+=f)r[0]=t[u],r[1]=t[u+1],s(r,r,e),t[u]=r[0],t[u+1]=r[1];return t}}()},62329:function(Lt,Mt,k){"use strict";k.r(Mt),k.d(Mt,{add:function(){return N},angle:function(){return vt},bezier:function(){return dt},ceil:function(){return Y},clone:function(){return it},copy:function(){return X},create:function(){return _},cross:function(){return Pt},dist:function(){return P},distance:function(){return H},div:function(){return ut},divide:function(){return W},dot:function(){return mt},equals:function(){return L},exactEquals:function(){return M},floor:function(){return D},forEach:function(){return f},fromValues:function(){return j},hermite:function(){return Nt},inverse:function(){return J},len:function(){return r},length:function(){return Q},lerp:function(){return Yt},max:function(){return O},min:function(){return E},mul:function(){return $},multiply:function(){return q},negate:function(){return Z},normalize:function(){return ht},random:function(){return pt},rotateX:function(){return c},rotateY:function(){return A},rotateZ:function(){return G},round:function(){return U},scale:function(){return nt},scaleAndAdd:function(){return st},set:function(){return b},sqrDist:function(){return g},sqrLen:function(){return t},squaredDistance:function(){return C},squaredLength:function(){return S},str:function(){return p},sub:function(){return T},subtract:function(){return z},transformMat3:function(){return Et},transformMat4:function(){return xt},transformQuat:function(){return wt},zero:function(){return yt}});var V=k(34249);function _(){var n=new V.ARRAY_TYPE(3);return V.ARRAY_TYPE!=Float32Array&&(n[0]=0,n[1]=0,n[2]=0),n}function it(n){var a=new V.ARRAY_TYPE(3);return a[0]=n[0],a[1]=n[1],a[2]=n[2],a}function Q(n){var a=n[0],s=n[1],e=n[2];return Math.hypot(a,s,e)}function j(n,a,s){var e=new V.ARRAY_TYPE(3);return e[0]=n,e[1]=a,e[2]=s,e}function X(n,a){return n[0]=a[0],n[1]=a[1],n[2]=a[2],n}function b(n,a,s,e){return n[0]=a,n[1]=s,n[2]=e,n}function N(n,a,s){return n[0]=a[0]+s[0],n[1]=a[1]+s[1],n[2]=a[2]+s[2],n}function z(n,a,s){return n[0]=a[0]-s[0],n[1]=a[1]-s[1],n[2]=a[2]-s[2],n}function q(n,a,s){return n[0]=a[0]*s[0],n[1]=a[1]*s[1],n[2]=a[2]*s[2],n}function W(n,a,s){return n[0]=a[0]/s[0],n[1]=a[1]/s[1],n[2]=a[2]/s[2],n}function Y(n,a){return n[0]=Math.ceil(a[0]),n[1]=Math.ceil(a[1]),n[2]=Math.ceil(a[2]),n}function D(n,a){return n[0]=Math.floor(a[0]),n[1]=Math.floor(a[1]),n[2]=Math.floor(a[2]),n}function E(n,a,s){return n[0]=Math.min(a[0],s[0]),n[1]=Math.min(a[1],s[1]),n[2]=Math.min(a[2],s[2]),n}function O(n,a,s){return n[0]=Math.max(a[0],s[0]),n[1]=Math.max(a[1],s[1]),n[2]=Math.max(a[2],s[2]),n}function U(n,a){return n[0]=Math.round(a[0]),n[1]=Math.round(a[1]),n[2]=Math.round(a[2]),n}function nt(n,a,s){return n[0]=a[0]*s,n[1]=a[1]*s,n[2]=a[2]*s,n}function st(n,a,s,e){return n[0]=a[0]+s[0]*e,n[1]=a[1]+s[1]*e,n[2]=a[2]+s[2]*e,n}function H(n,a){var s=a[0]-n[0],e=a[1]-n[1],u=a[2]-n[2];return Math.hypot(s,e,u)}function C(n,a){var s=a[0]-n[0],e=a[1]-n[1],u=a[2]-n[2];return s*s+e*e+u*u}function S(n){var a=n[0],s=n[1],e=n[2];return a*a+s*s+e*e}function Z(n,a){return n[0]=-a[0],n[1]=-a[1],n[2]=-a[2],n}function J(n,a){return n[0]=1/a[0],n[1]=1/a[1],n[2]=1/a[2],n}function ht(n,a){var s=a[0],e=a[1],u=a[2],l=s*s+e*e+u*u;return l>0&&(l=1/Math.sqrt(l)),n[0]=a[0]*l,n[1]=a[1]*l,n[2]=a[2]*l,n}function mt(n,a){return n[0]*a[0]+n[1]*a[1]+n[2]*a[2]}function Pt(n,a,s){var e=a[0],u=a[1],l=a[2],v=s[0],i=s[1],y=s[2];return n[0]=u*y-l*i,n[1]=l*v-e*y,n[2]=e*i-u*v,n}function Yt(n,a,s,e){var u=a[0],l=a[1],v=a[2];return n[0]=u+e*(s[0]-u),n[1]=l+e*(s[1]-l),n[2]=v+e*(s[2]-v),n}function Nt(n,a,s,e,u,l){var v=l*l,i=v*(2*l-3)+1,y=v*(l-2)+l,x=v*(l-1),o=v*(3-2*l);return n[0]=a[0]*i+s[0]*y+e[0]*x+u[0]*o,n[1]=a[1]*i+s[1]*y+e[1]*x+u[1]*o,n[2]=a[2]*i+s[2]*y+e[2]*x+u[2]*o,n}function dt(n,a,s,e,u,l){var v=1-l,i=v*v,y=l*l,x=i*v,o=3*l*i,h=3*y*v,d=y*l;return n[0]=a[0]*x+s[0]*o+e[0]*h+u[0]*d,n[1]=a[1]*x+s[1]*o+e[1]*h+u[1]*d,n[2]=a[2]*x+s[2]*o+e[2]*h+u[2]*d,n}function pt(n,a){a=a||1;var s=V.RANDOM()*2*Math.PI,e=V.RANDOM()*2-1,u=Math.sqrt(1-e*e)*a;return n[0]=Math.cos(s)*u,n[1]=Math.sin(s)*u,n[2]=e*a,n}function xt(n,a,s){var e=a[0],u=a[1],l=a[2],v=s[3]*e+s[7]*u+s[11]*l+s[15];return v=v||1,n[0]=(s[0]*e+s[4]*u+s[8]*l+s[12])/v,n[1]=(s[1]*e+s[5]*u+s[9]*l+s[13])/v,n[2]=(s[2]*e+s[6]*u+s[10]*l+s[14])/v,n}function Et(n,a,s){var e=a[0],u=a[1],l=a[2];return n[0]=e*s[0]+u*s[3]+l*s[6],n[1]=e*s[1]+u*s[4]+l*s[7],n[2]=e*s[2]+u*s[5]+l*s[8],n}function wt(n,a,s){var e=s[0],u=s[1],l=s[2],v=s[3],i=a[0],y=a[1],x=a[2],o=u*x-l*y,h=l*i-e*x,d=e*y-u*i,B=u*d-l*h,K=l*o-e*d,tt=e*h-u*o,et=v*2;return o*=et,h*=et,d*=et,B*=2,K*=2,tt*=2,n[0]=i+o+B,n[1]=y+h+K,n[2]=x+d+tt,n}function c(n,a,s,e){var u=[],l=[];return u[0]=a[0]-s[0],u[1]=a[1]-s[1],u[2]=a[2]-s[2],l[0]=u[0],l[1]=u[1]*Math.cos(e)-u[2]*Math.sin(e),l[2]=u[1]*Math.sin(e)+u[2]*Math.cos(e),n[0]=l[0]+s[0],n[1]=l[1]+s[1],n[2]=l[2]+s[2],n}function A(n,a,s,e){var u=[],l=[];return u[0]=a[0]-s[0],u[1]=a[1]-s[1],u[2]=a[2]-s[2],l[0]=u[2]*Math.sin(e)+u[0]*Math.cos(e),l[1]=u[1],l[2]=u[2]*Math.cos(e)-u[0]*Math.sin(e),n[0]=l[0]+s[0],n[1]=l[1]+s[1],n[2]=l[2]+s[2],n}function G(n,a,s,e){var u=[],l=[];return u[0]=a[0]-s[0],u[1]=a[1]-s[1],u[2]=a[2]-s[2],l[0]=u[0]*Math.cos(e)-u[1]*Math.sin(e),l[1]=u[0]*Math.sin(e)+u[1]*Math.cos(e),l[2]=u[2],n[0]=l[0]+s[0],n[1]=l[1]+s[1],n[2]=l[2]+s[2],n}function vt(n,a){var s=n[0],e=n[1],u=n[2],l=a[0],v=a[1],i=a[2],y=Math.sqrt(s*s+e*e+u*u),x=Math.sqrt(l*l+v*v+i*i),o=y*x,h=o&&mt(n,a)/o;return Math.acos(Math.min(Math.max(h,-1),1))}function yt(n){return n[0]=0,n[1]=0,n[2]=0,n}function p(n){return"vec3("+n[0]+", "+n[1]+", "+n[2]+")"}function M(n,a){return n[0]===a[0]&&n[1]===a[1]&&n[2]===a[2]}function L(n,a){var s=n[0],e=n[1],u=n[2],l=a[0],v=a[1],i=a[2];return Math.abs(s-l)<=V.EPSILON*Math.max(1,Math.abs(s),Math.abs(l))&&Math.abs(e-v)<=V.EPSILON*Math.max(1,Math.abs(e),Math.abs(v))&&Math.abs(u-i)<=V.EPSILON*Math.max(1,Math.abs(u),Math.abs(i))}var T=z,$=q,ut=W,P=H,g=C,r=Q,t=S,f=function(){var n=_();return function(a,s,e,u,l,v){var i,y;for(s||(s=3),e||(e=0),u?y=Math.min(u*s+e,a.length):y=a.length,i=e;i<y;i+=s)n[0]=a[i],n[1]=a[i+1],n[2]=a[i+2],l(n,n,v),a[i]=n[0],a[i+1]=n[1],a[i+2]=n[2];return a}}()},81556:function(Lt,Mt,k){"use strict";var V=k(53023).default;Object.defineProperty(Mt,"__esModule",{value:!0}),Mt.default=it;var _=V(k(44194));function it(Q,j,X){var b=_.useRef({});return(!("value"in b.current)||X(b.current.condition,j))&&(b.current.value=Q(),b.current.condition=j),b.current.value}},53023:function(Lt,Mt,k){var V=k(48258).default;function _(it,Q){if(typeof WeakMap=="function")var j=new WeakMap,X=new WeakMap;return(Lt.exports=_=function(N,z){if(!z&&N&&N.__esModule)return N;var q,W,Y={__proto__:null,default:N};if(N===null||V(N)!="object"&&typeof N!="function")return Y;if(q=z?X:j){if(q.has(N))return q.get(N);q.set(N,Y)}for(var D in N)D!=="default"&&{}.hasOwnProperty.call(N,D)&&((W=(q=Object.defineProperty)&&Object.getOwnPropertyDescriptor(N,D))&&(W.get||W.set)?q(Y,D,W):Y[D]=N[D]);return Y},Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports)(it,Q)}Lt.exports=_,Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports},92809:function(Lt,Mt,k){var V=k(48258).default;function _(){"use strict";Lt.exports=_=function(){return Q},Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports;var it,Q={},j=Object.prototype,X=j.hasOwnProperty,b=typeof Symbol=="function"?Symbol:{},N=b.iterator||"@@iterator",z=b.asyncIterator||"@@asyncIterator",q=b.toStringTag||"@@toStringTag";function W(dt,pt,xt,Et){return Object.defineProperty(dt,pt,{value:xt,enumerable:!Et,configurable:!Et,writable:!Et})}try{W({},"")}catch(dt){W=function(xt,Et,wt){return xt[Et]=wt}}function Y(dt,pt,xt,Et){var wt=pt&&pt.prototype instanceof O?pt:O,c=Object.create(wt.prototype);return W(c,"_invoke",function(A,G,vt){var yt=1;return function(p,M){if(yt===3)throw Error("Generator is already running");if(yt===4){if(p==="throw")throw M;return{value:it,done:!0}}for(vt.method=p,vt.arg=M;;){var L=vt.delegate;if(L){var T=ht(L,vt);if(T){if(T===E)continue;return T}}if(vt.method==="next")vt.sent=vt._sent=vt.arg;else if(vt.method==="throw"){if(yt===1)throw yt=4,vt.arg;vt.dispatchException(vt.arg)}else vt.method==="return"&&vt.abrupt("return",vt.arg);yt=3;var $=D(A,G,vt);if($.type==="normal"){if(yt=vt.done?4:2,$.arg===E)continue;return{value:$.arg,done:vt.done}}$.type==="throw"&&(yt=4,vt.method="throw",vt.arg=$.arg)}}}(dt,xt,new Yt(Et||[])),!0),c}function D(dt,pt,xt){try{return{type:"normal",arg:dt.call(pt,xt)}}catch(Et){return{type:"throw",arg:Et}}}Q.wrap=Y;var E={};function O(){}function U(){}function nt(){}var st={};W(st,N,function(){return this});var H=Object.getPrototypeOf,C=H&&H(H(Nt([])));C&&C!==j&&X.call(C,N)&&(st=C);var S=nt.prototype=O.prototype=Object.create(st);function Z(dt){["next","throw","return"].forEach(function(pt){W(dt,pt,function(xt){return this._invoke(pt,xt)})})}function J(dt,pt){function xt(wt,c,A,G){var vt=D(dt[wt],dt,c);if(vt.type!=="throw"){var yt=vt.arg,p=yt.value;return p&&V(p)=="object"&&X.call(p,"__await")?pt.resolve(p.__await).then(function(M){xt("next",M,A,G)},function(M){xt("throw",M,A,G)}):pt.resolve(p).then(function(M){yt.value=M,A(yt)},function(M){return xt("throw",M,A,G)})}G(vt.arg)}var Et;W(this,"_invoke",function(wt,c){function A(){return new pt(function(G,vt){xt(wt,c,G,vt)})}return Et=Et?Et.then(A,A):A()},!0)}function ht(dt,pt){var xt=pt.method,Et=dt.i[xt];if(Et===it)return pt.delegate=null,xt==="throw"&&dt.i.return&&(pt.method="return",pt.arg=it,ht(dt,pt),pt.method==="throw")||xt!=="return"&&(pt.method="throw",pt.arg=new TypeError("The iterator does not provide a '"+xt+"' method")),E;var wt=D(Et,dt.i,pt.arg);if(wt.type==="throw")return pt.method="throw",pt.arg=wt.arg,pt.delegate=null,E;var c=wt.arg;return c?c.done?(pt[dt.r]=c.value,pt.next=dt.n,pt.method!=="return"&&(pt.method="next",pt.arg=it),pt.delegate=null,E):c:(pt.method="throw",pt.arg=new TypeError("iterator result is not an object"),pt.delegate=null,E)}function mt(dt){this.tryEntries.push(dt)}function Pt(dt){var pt=dt[4]||{};pt.type="normal",pt.arg=it,dt[4]=pt}function Yt(dt){this.tryEntries=[[-1]],dt.forEach(mt,this),this.reset(!0)}function Nt(dt){if(dt!=null){var pt=dt[N];if(pt)return pt.call(dt);if(typeof dt.next=="function")return dt;if(!isNaN(dt.length)){var xt=-1,Et=function wt(){for(;++xt<dt.length;)if(X.call(dt,xt))return wt.value=dt[xt],wt.done=!1,wt;return wt.value=it,wt.done=!0,wt};return Et.next=Et}}throw new TypeError(V(dt)+" is not iterable")}return U.prototype=nt,W(S,"constructor",nt),W(nt,"constructor",U),U.displayName=W(nt,q,"GeneratorFunction"),Q.isGeneratorFunction=function(dt){var pt=typeof dt=="function"&&dt.constructor;return!!pt&&(pt===U||(pt.displayName||pt.name)==="GeneratorFunction")},Q.mark=function(dt){return Object.setPrototypeOf?Object.setPrototypeOf(dt,nt):(dt.__proto__=nt,W(dt,q,"GeneratorFunction")),dt.prototype=Object.create(S),dt},Q.awrap=function(dt){return{__await:dt}},Z(J.prototype),W(J.prototype,z,function(){return this}),Q.AsyncIterator=J,Q.async=function(dt,pt,xt,Et,wt){wt===void 0&&(wt=Promise);var c=new J(Y(dt,pt,xt,Et),wt);return Q.isGeneratorFunction(pt)?c:c.next().then(function(A){return A.done?A.value:c.next()})},Z(S),W(S,q,"Generator"),W(S,N,function(){return this}),W(S,"toString",function(){return"[object Generator]"}),Q.keys=function(dt){var pt=Object(dt),xt=[];for(var Et in pt)xt.unshift(Et);return function wt(){for(;xt.length;)if((Et=xt.pop())in pt)return wt.value=Et,wt.done=!1,wt;return wt.done=!0,wt}},Q.values=Nt,Yt.prototype={constructor:Yt,reset:function(pt){if(this.prev=this.next=0,this.sent=this._sent=it,this.done=!1,this.delegate=null,this.method="next",this.arg=it,this.tryEntries.forEach(Pt),!pt)for(var xt in this)xt.charAt(0)==="t"&&X.call(this,xt)&&!isNaN(+xt.slice(1))&&(this[xt]=it)},stop:function(){this.done=!0;var pt=this.tryEntries[0][4];if(pt.type==="throw")throw pt.arg;return this.rval},dispatchException:function(pt){if(this.done)throw pt;var xt=this;function Et(p){A.type="throw",A.arg=pt,xt.next=p}for(var wt=xt.tryEntries.length-1;wt>=0;--wt){var c=this.tryEntries[wt],A=c[4],G=this.prev,vt=c[1],yt=c[2];if(c[0]===-1)return Et("end"),!1;if(!vt&&!yt)throw Error("try statement without catch or finally");if(c[0]!=null&&c[0]<=G){if(G<vt)return this.method="next",this.arg=it,Et(vt),!0;if(G<yt)return Et(yt),!1}}},abrupt:function(pt,xt){for(var Et=this.tryEntries.length-1;Et>=0;--Et){var wt=this.tryEntries[Et];if(wt[0]>-1&&wt[0]<=this.prev&&this.prev<wt[2]){var c=wt;break}}c&&(pt==="break"||pt==="continue")&&c[0]<=xt&&xt<=c[2]&&(c=null);var A=c?c[4]:{};return A.type=pt,A.arg=xt,c?(this.method="next",this.next=c[2],E):this.complete(A)},complete:function(pt,xt){if(pt.type==="throw")throw pt.arg;return pt.type==="break"||pt.type==="continue"?this.next=pt.arg:pt.type==="return"?(this.rval=this.arg=pt.arg,this.method="return",this.next="end"):pt.type==="normal"&&xt&&(this.next=xt),E},finish:function(pt){for(var xt=this.tryEntries.length-1;xt>=0;--xt){var Et=this.tryEntries[xt];if(Et[2]===pt)return this.complete(Et[4],Et[3]),Pt(Et),E}},catch:function(pt){for(var xt=this.tryEntries.length-1;xt>=0;--xt){var Et=this.tryEntries[xt];if(Et[0]===pt){var wt=Et[4];if(wt.type==="throw"){var c=wt.arg;Pt(Et)}return c}}throw Error("illegal catch attempt")},delegateYield:function(pt,xt,Et){return this.delegate={i:Nt(pt),r:xt,n:Et},this.method==="next"&&(this.arg=it),E}},Q}Lt.exports=_,Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports},48258:function(Lt){function Mt(k){"@babel/helpers - typeof";return Lt.exports=Mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(V){return typeof V}:function(V){return V&&typeof Symbol=="function"&&V.constructor===Symbol&&V!==Symbol.prototype?"symbol":typeof V},Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports,Mt(k)}Lt.exports=Mt,Lt.exports.__esModule=!0,Lt.exports.default=Lt.exports},55679:function(Lt,Mt,k){var V=k(92809)();Lt.exports=V;try{regeneratorRuntime=V}catch(_){typeof globalThis=="object"?globalThis.regeneratorRuntime=V:Function("r","regeneratorRuntime = r")(V)}},28521:function(Lt,Mt,k){"use strict";k.d(Mt,{B8:function(){return ht},Il:function(){return _},SU:function(){return J},Ss:function(){return mt},ZP:function(){return C}});var V=k(40406);function _(){}var it=.7,Q=1/it,j="\\s*([+-]?\\d+)\\s*",X="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",b="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",N=/^#([0-9a-f]{3,8})$/,z=new RegExp("^rgb\\(".concat(j,",").concat(j,",").concat(j,"\\)$")),q=new RegExp("^rgb\\(".concat(b,",").concat(b,",").concat(b,"\\)$")),W=new RegExp("^rgba\\(".concat(j,",").concat(j,",").concat(j,",").concat(X,"\\)$")),Y=new RegExp("^rgba\\(".concat(b,",").concat(b,",").concat(b,",").concat(X,"\\)$")),D=new RegExp("^hsl\\(".concat(X,",").concat(b,",").concat(b,"\\)$")),E=new RegExp("^hsla\\(".concat(X,",").concat(b,",").concat(b,",").concat(X,"\\)$")),O={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};(0,V.Z)(_,C,{copy:function(M){return Object.assign(new this.constructor,this,M)},displayable:function(){return this.rgb().displayable()},hex:U,formatHex:U,formatHex8:nt,formatHsl:st,formatRgb:H,toString:H});function U(){return this.rgb().formatHex()}function nt(){return this.rgb().formatHex8()}function st(){return wt(this).formatHsl()}function H(){return this.rgb().formatRgb()}function C(p){var M,L;return p=(p+"").trim().toLowerCase(),(M=N.exec(p))?(L=M[1].length,M=parseInt(M[1],16),L===6?S(M):L===3?new mt(M>>8&15|M>>4&240,M>>4&15|M&240,(M&15)<<4|M&15,1):L===8?Z(M>>24&255,M>>16&255,M>>8&255,(M&255)/255):L===4?Z(M>>12&15|M>>8&240,M>>8&15|M>>4&240,M>>4&15|M&240,((M&15)<<4|M&15)/255):null):(M=z.exec(p))?new mt(M[1],M[2],M[3],1):(M=q.exec(p))?new mt(M[1]*255/100,M[2]*255/100,M[3]*255/100,1):(M=W.exec(p))?Z(M[1],M[2],M[3],M[4]):(M=Y.exec(p))?Z(M[1]*255/100,M[2]*255/100,M[3]*255/100,M[4]):(M=D.exec(p))?Et(M[1],M[2]/100,M[3]/100,1):(M=E.exec(p))?Et(M[1],M[2]/100,M[3]/100,M[4]):O.hasOwnProperty(p)?S(O[p]):p==="transparent"?new mt(NaN,NaN,NaN,0):null}function S(p){return new mt(p>>16&255,p>>8&255,p&255,1)}function Z(p,M,L,T){return T<=0&&(p=M=L=NaN),new mt(p,M,L,T)}function J(p){return p instanceof _||(p=C(p)),p?(p=p.rgb(),new mt(p.r,p.g,p.b,p.opacity)):new mt}function ht(p,M,L,T){return arguments.length===1?J(p):new mt(p,M,L,T==null?1:T)}function mt(p,M,L,T){this.r=+p,this.g=+M,this.b=+L,this.opacity=+T}(0,V.Z)(mt,ht,(0,V.l)(_,{brighter:function(M){return M=M==null?Q:Math.pow(Q,M),new mt(this.r*M,this.g*M,this.b*M,this.opacity)},darker:function(M){return M=M==null?it:Math.pow(it,M),new mt(this.r*M,this.g*M,this.b*M,this.opacity)},rgb:function(){return this},clamp:function(){return new mt(pt(this.r),pt(this.g),pt(this.b),dt(this.opacity))},displayable:function(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Pt,formatHex:Pt,formatHex8:Yt,formatRgb:Nt,toString:Nt}));function Pt(){return"#".concat(xt(this.r)).concat(xt(this.g)).concat(xt(this.b))}function Yt(){return"#".concat(xt(this.r)).concat(xt(this.g)).concat(xt(this.b)).concat(xt((isNaN(this.opacity)?1:this.opacity)*255))}function Nt(){var p=dt(this.opacity);return"".concat(p===1?"rgb(":"rgba(").concat(pt(this.r),", ").concat(pt(this.g),", ").concat(pt(this.b)).concat(p===1?")":", ".concat(p,")"))}function dt(p){return isNaN(p)?1:Math.max(0,Math.min(1,p))}function pt(p){return Math.max(0,Math.min(255,Math.round(p)||0))}function xt(p){return p=pt(p),(p<16?"0":"")+p.toString(16)}function Et(p,M,L,T){return T<=0?p=M=L=NaN:L<=0||L>=1?p=M=NaN:M<=0&&(p=NaN),new A(p,M,L,T)}function wt(p){if(p instanceof A)return new A(p.h,p.s,p.l,p.opacity);if(p instanceof _||(p=C(p)),!p)return new A;if(p instanceof A)return p;p=p.rgb();var M=p.r/255,L=p.g/255,T=p.b/255,$=Math.min(M,L,T),ut=Math.max(M,L,T),P=NaN,g=ut-$,r=(ut+$)/2;return g?(M===ut?P=(L-T)/g+(L<T)*6:L===ut?P=(T-M)/g+2:P=(M-L)/g+4,g/=r<.5?ut+$:2-ut-$,P*=60):g=r>0&&r<1?0:P,new A(P,g,r,p.opacity)}function c(p,M,L,T){return arguments.length===1?wt(p):new A(p,M,L,T==null?1:T)}function A(p,M,L,T){this.h=+p,this.s=+M,this.l=+L,this.opacity=+T}(0,V.Z)(A,c,(0,V.l)(_,{brighter:function(M){return M=M==null?Q:Math.pow(Q,M),new A(this.h,this.s,this.l*M,this.opacity)},darker:function(M){return M=M==null?it:Math.pow(it,M),new A(this.h,this.s,this.l*M,this.opacity)},rgb:function(){var M=this.h%360+(this.h<0)*360,L=isNaN(M)||isNaN(this.s)?0:this.s,T=this.l,$=T+(T<.5?T:1-T)*L,ut=2*T-$;return new mt(yt(M>=240?M-240:M+120,ut,$),yt(M,ut,$),yt(M<120?M+240:M-120,ut,$),this.opacity)},clamp:function(){return new A(G(this.h),vt(this.s),vt(this.l),dt(this.opacity))},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl:function(){var M=dt(this.opacity);return"".concat(M===1?"hsl(":"hsla(").concat(G(this.h),", ").concat(vt(this.s)*100,"%, ").concat(vt(this.l)*100,"%").concat(M===1?")":", ".concat(M,")"))}}));function G(p){return p=(p||0)%360,p<0?p+360:p}function vt(p){return Math.max(0,Math.min(1,p||0))}function yt(p,M,L){return(p<60?M+(L-M)*p/60:p<180?L:p<240?M+(L-M)*(240-p)/60:M)*255}},40406:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return V},l:function(){return _}});function V(it,Q,j){it.prototype=Q.prototype=j,j.constructor=it}function _(it,Q){var j=Object.create(it.prototype);for(var X in Q)j[X]=Q[X];return j}},65099:function(Lt,Mt,k){"use strict";k.d(Mt,{M:function(){return Q},Z:function(){return it}});var V=k(34404),_=k(92508);function it(j,X){return((0,_.v)(X)?_.Z:Q)(j,X)}function Q(j,X){var b=X?X.length:0,N=j?Math.min(b,j.length):0,z=new Array(N),q=new Array(b),W;for(W=0;W<N;++W)z[W]=(0,V.Z)(j[W],X[W]);for(;W<b;++W)q[W]=X[W];return function(Y){for(W=0;W<N;++W)q[W]=z[W](Y);return q}}},63823:function(Lt,Mt,k){"use strict";k.d(Mt,{ZP:function(){return X},wx:function(){return Q},yi:function(){return j}});var V=k(38918);function _(b,N){return function(z){return b+z*N}}function it(b,N,z){return b=Math.pow(b,z),N=Math.pow(N,z)-b,z=1/z,function(q){return Math.pow(b+q*N,z)}}function Q(b,N){var z=N-b;return z?_(b,z>180||z<-180?z-360*Math.round(z/360):z):(0,V.Z)(isNaN(b)?N:b)}function j(b){return(b=+b)==1?X:function(N,z){return z-N?it(N,z,b):(0,V.Z)(isNaN(N)?z:N)}}function X(b,N){var z=N-b;return z?_(b,z):(0,V.Z)(isNaN(b)?N:b)}},38918:function(Lt,Mt){"use strict";Mt.Z=function(k){return function(){return k}}},87490:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return V}});function V(_,it){return _=+_,it=+it,function(Q){return _*(1-Q)+it*Q}}},92508:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return V},v:function(){return _}});function V(it,Q){Q||(Q=[]);var j=it?Math.min(Q.length,it.length):0,X=Q.slice(),b;return function(N){for(b=0;b<j;++b)X[b]=it[b]*(1-N)+Q[b]*N;return X}}function _(it){return ArrayBuffer.isView(it)&&!(it instanceof DataView)}},30651:function(Lt,Mt,k){"use strict";k.d(Mt,{ZP:function(){return X}});var V=k(28521);function _(q,W,Y,D,E){var O=q*q,U=O*q;return((1-3*q+3*O-U)*W+(4-6*O+3*U)*Y+(1+3*q+3*O-3*U)*D+U*E)/6}function it(q){var W=q.length-1;return function(Y){var D=Y<=0?Y=0:Y>=1?(Y=1,W-1):Math.floor(Y*W),E=q[D],O=q[D+1],U=D>0?q[D-1]:2*E-O,nt=D<W-1?q[D+2]:2*O-E;return _((Y-D/W)*W,U,E,O,nt)}}function Q(q){var W=q.length;return function(Y){var D=Math.floor(((Y%=1)<0?++Y:Y)*W),E=q[(D+W-1)%W],O=q[D%W],U=q[(D+1)%W],nt=q[(D+2)%W];return _((Y-D/W)*W,E,O,U,nt)}}var j=k(63823),X=function q(W){var Y=(0,j.yi)(W);function D(E,O){var U=Y((E=(0,V.B8)(E)).r,(O=(0,V.B8)(O)).r),nt=Y(E.g,O.g),st=Y(E.b,O.b),H=(0,j.ZP)(E.opacity,O.opacity);return function(C){return E.r=U(C),E.g=nt(C),E.b=st(C),E.opacity=H(C),E+""}}return D.gamma=q,D}(1);function b(q){return function(W){var Y=W.length,D=new Array(Y),E=new Array(Y),O=new Array(Y),U,nt;for(U=0;U<Y;++U)nt=(0,V.B8)(W[U]),D[U]=nt.r||0,E[U]=nt.g||0,O[U]=nt.b||0;return D=q(D),E=q(E),O=q(O),nt.opacity=1,function(st){return nt.r=D(st),nt.g=E(st),nt.b=O(st),nt+""}}}var N=b(it),z=b(Q)},98698:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return X}});var V=k(87490),_=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,it=new RegExp(_.source,"g");function Q(b){return function(){return b}}function j(b){return function(N){return b(N)+""}}function X(b,N){var z=_.lastIndex=it.lastIndex=0,q,W,Y,D=-1,E=[],O=[];for(b=b+"",N=N+"";(q=_.exec(b))&&(W=it.exec(N));)(Y=W.index)>z&&(Y=N.slice(z,Y),E[D]?E[D]+=Y:E[++D]=Y),(q=q[0])===(W=W[0])?E[D]?E[D]+=W:E[++D]=W:(E[++D]=null,O.push({i:D,x:(0,V.Z)(q,W)})),z=it.lastIndex;return z<N.length&&(Y=N.slice(z),E[D]?E[D]+=Y:E[++D]=Y),E.length<2?O[0]?j(O[0].x):Q(N):(N=O.length,function(U){for(var nt=0,st;nt<N;++nt)E[(st=O[nt]).i]=st.x(U);return E.join("")})}},34404:function(Lt,Mt,k){"use strict";k.d(Mt,{Z:function(){return W}});var V=k(89957),_=k(28521),it=k(30651),Q=k(65099);function j(Y,D){var E=new Date;return Y=+Y,D=+D,function(O){return E.setTime(Y*(1-O)+D*O),E}}var X=k(87490);function b(Y,D){var E={},O={},U;(Y===null||V(Y)!=="object")&&(Y={}),(D===null||V(D)!=="object")&&(D={});for(U in D)U in Y?E[U]=W(Y[U],D[U]):O[U]=D[U];return function(nt){for(U in E)O[U]=E[U](nt);return O}}var N=k(98698),z=k(38918),q=k(92508);function W(Y,D){var E=V(D),O;return D==null||E==="boolean"?(0,z.Z)(D):(E==="number"?X.Z:E==="string"?(O=(0,_.ZP)(D))?(D=O,it.ZP):N.Z:D instanceof _.ZP?it.ZP:D instanceof Date?j:(0,q.v)(D)?q.Z:Array.isArray(D)?Q.M:typeof D.valueOf!="function"&&typeof D.toString!="function"||isNaN(D)?b:X.Z)(Y,D)}}}]);
