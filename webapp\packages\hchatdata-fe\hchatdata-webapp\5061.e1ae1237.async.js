!(function(){var Ot=(ut,P,k)=>new Promise((N,c)=>{var s=i=>{try{e(k.next(i))}catch(h){c(h)}},t=i=>{try{e(k.throw(i))}catch(h){c(h)}},e=i=>i.done?N(i.value):Promise.resolve(i.value).then(s,t);e((k=k.apply(ut,P)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5061],{42270:function(ut,P,k){(function(c,s){ut.exports=s(k(43221))})(this,function(N){return function(c){var s={};function t(e){if(s[e])return s[e].exports;var i=s[e]={i:e,l:!1,exports:{}};return c[e].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=c,t.c=s,t.i=function(e){return e},t.d=function(e,i,h){t.o(e,i)||Object.defineProperty(e,i,{configurable:!1,enumerable:!0,get:h})},t.n=function(e){var i=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(i,"a",i),i},t.o=function(e,i){return Object.prototype.hasOwnProperty.call(e,i)},t.p="",t(t.s=7)}([function(c,s){c.exports=N},function(c,s,t){"use strict";var e=t(0).FDLayoutConstants;function i(){}for(var h in e)i[h]=e[h];i.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,i.DEFAULT_RADIAL_SEPARATION=e.DEFAULT_EDGE_LENGTH,i.DEFAULT_COMPONENT_SEPERATION=60,i.TILE=!0,i.TILING_PADDING_VERTICAL=10,i.TILING_PADDING_HORIZONTAL=10,i.TREE_REDUCTION_ON_INCREMENTAL=!1,c.exports=i},function(c,s,t){"use strict";var e=t(0).FDLayoutEdge;function i(n,g,r){e.call(this,n,g,r)}i.prototype=Object.create(e.prototype);for(var h in e)i[h]=e[h];c.exports=i},function(c,s,t){"use strict";var e=t(0).LGraph;function i(n,g,r){e.call(this,n,g,r)}i.prototype=Object.create(e.prototype);for(var h in e)i[h]=e[h];c.exports=i},function(c,s,t){"use strict";var e=t(0).LGraphManager;function i(n){e.call(this,n)}i.prototype=Object.create(e.prototype);for(var h in e)i[h]=e[h];c.exports=i},function(c,s,t){"use strict";var e=t(0).FDLayoutNode,i=t(0).IMath;function h(g,r,a,l){e.call(this,g,r,a,l)}h.prototype=Object.create(e.prototype);for(var n in e)h[n]=e[n];h.prototype.move=function(){var g=this.graphManager.getLayout();this.displacementX=g.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=g.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementX=g.coolingFactor*g.maxNodeDisplacement*i.sign(this.displacementX)),Math.abs(this.displacementY)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementY=g.coolingFactor*g.maxNodeDisplacement*i.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),g.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},h.prototype.propogateDisplacementToChildren=function(g,r){for(var a=this.getChild().getNodes(),l,f=0;f<a.length;f++)l=a[f],l.getChild()==null?(l.moveBy(g,r),l.displacementX+=g,l.displacementY+=r):l.propogateDisplacementToChildren(g,r)},h.prototype.setPred1=function(g){this.pred1=g},h.prototype.getPred1=function(){return pred1},h.prototype.getPred2=function(){return pred2},h.prototype.setNext=function(g){this.next=g},h.prototype.getNext=function(){return next},h.prototype.setProcessed=function(g){this.processed=g},h.prototype.isProcessed=function(){return processed},c.exports=h},function(c,s,t){"use strict";var e=t(0).FDLayout,i=t(4),h=t(3),n=t(5),g=t(2),r=t(1),a=t(0).FDLayoutConstants,l=t(0).LayoutConstants,f=t(0).Point,d=t(0).PointD,D=t(0).Layout,y=t(0).Integer,L=t(0).IGeometry,T=t(0).LGraph,x=t(0).Transform;function A(){e.call(this),this.toBeTiled={}}A.prototype=Object.create(e.prototype);for(var F in e)A[F]=e[F];A.prototype.newGraphManager=function(){var o=new i(this);return this.graphManager=o,o},A.prototype.newGraph=function(o){return new h(null,this.graphManager,o)},A.prototype.newNode=function(o){return new n(this.graphManager,o)},A.prototype.newEdge=function(o){return new g(null,null,o)},A.prototype.initParameters=function(){e.prototype.initParameters.call(this,arguments),this.isSubLayout||(r.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=r.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=r.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=a.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=a.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=a.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=a.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=a.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=a.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/a.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=a.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},A.prototype.layout=function(){var o=l.DEFAULT_CREATE_BENDS_AS_NEEDED;return o&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},A.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(r.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var u=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(I){return u.has(I)});this.graphManager.setAllNodesToApplyGravitation(p)}}else{var o=this.getFlatForest();if(o.length>0)this.positionNodesRadially(o);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var u=new Set(this.getAllNodes()),p=this.nodesWithGravity.filter(function(E){return u.has(E)});this.graphManager.setAllNodesToApplyGravitation(p),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},A.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%a.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var o=new Set(this.getAllNodes()),u=this.nodesWithGravity.filter(function(m){return o.has(m)});this.graphManager.setAllNodesToApplyGravitation(u),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=a.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var p=!this.isTreeGrowing&&!this.isGrowthFinished,E=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(p,E),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},A.prototype.getPositionsData=function(){for(var o=this.graphManager.getAllNodes(),u={},p=0;p<o.length;p++){var E=o[p].rect,m=o[p].id;u[m]={id:m,x:E.getCenterX(),y:E.getCenterY(),w:E.width,h:E.height}}return u},A.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var o=!1;if(a.ANIMATE==="during")this.emit("layoutstarted");else{for(;!o;)o=this.tick();this.graphManager.updateBounds()}},A.prototype.calculateNodesToApplyGravitationTo=function(){var o=[],u,p=this.graphManager.getGraphs(),E=p.length,m;for(m=0;m<E;m++)u=p[m],u.updateConnected(),u.isConnected||(o=o.concat(u.getNodes()));return o},A.prototype.createBendpoints=function(){var o=[];o=o.concat(this.graphManager.getAllEdges());var u=new Set,p;for(p=0;p<o.length;p++){var E=o[p];if(!u.has(E)){var m=E.getSource(),I=E.getTarget();if(m==I)E.getBendpoints().push(new d),E.getBendpoints().push(new d),this.createDummyNodesForBendpoints(E),u.add(E);else{var w=[];if(w=w.concat(m.getEdgeListToNode(I)),w=w.concat(I.getEdgeListToNode(m)),!u.has(w[0])){if(w.length>1){var S;for(S=0;S<w.length;S++){var X=w[S];X.getBendpoints().push(new d),this.createDummyNodesForBendpoints(X)}}w.forEach(function($){u.add($)})}}}if(u.size==o.length)break}},A.prototype.positionNodesRadially=function(o){for(var u=new f(0,0),p=Math.ceil(Math.sqrt(o.length)),E=0,m=0,I=0,w=new d(0,0),S=0;S<o.length;S++){S%p==0&&(I=0,m=E,S!=0&&(m+=r.DEFAULT_COMPONENT_SEPERATION),E=0);var X=o[S],$=D.findCenterOfTree(X);u.x=I,u.y=m,w=A.radialLayout(X,$,u),w.y>E&&(E=Math.floor(w.y)),I=Math.floor(w.x+r.DEFAULT_COMPONENT_SEPERATION)}this.transform(new d(l.WORLD_CENTER_X-w.x/2,l.WORLD_CENTER_Y-w.y/2))},A.radialLayout=function(o,u,p){var E=Math.max(this.maxDiagonalInTree(o),r.DEFAULT_RADIAL_SEPARATION);A.branchRadialLayout(u,null,0,359,0,E);var m=T.calculateBounds(o),I=new x;I.setDeviceOrgX(m.getMinX()),I.setDeviceOrgY(m.getMinY()),I.setWorldOrgX(p.x),I.setWorldOrgY(p.y);for(var w=0;w<o.length;w++){var S=o[w];S.transform(I)}var X=new d(m.getMaxX(),m.getMaxY());return I.inverseTransformPoint(X)},A.branchRadialLayout=function(o,u,p,E,m,I){var w=(E-p+1)/2;w<0&&(w+=180);var S=(w+p)%360,X=S*L.TWO_PI/360,$=Math.cos(X),J=m*Math.cos(X),tt=m*Math.sin(X);o.setCenter(J,tt);var U=[];U=U.concat(o.getEdges());var B=U.length;u!=null&&B--;for(var K=0,Z=U.length,O,R=o.getEdgesBetween(u);R.length>1;){var v=R[0];R.splice(0,1);var M=U.indexOf(v);M>=0&&U.splice(M,1),Z--,B--}u!=null?O=(U.indexOf(R[0])+1)%Z:O=0;for(var G=Math.abs(E-p)/B,C=O;K!=B;C=++C%Z){var H=U[C].getOtherEnd(o);if(H!=u){var Q=(p+K*G)%360,et=(Q+G)%360;A.branchRadialLayout(H,o,Q,et,m+I,I),K++}}},A.maxDiagonalInTree=function(o){for(var u=y.MIN_VALUE,p=0;p<o.length;p++){var E=o[p],m=E.getDiagonal();m>u&&(u=m)}return u},A.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},A.prototype.groupZeroDegreeMembers=function(){var o=this,u={};this.memberGroups={},this.idToDummyNode={};for(var p=[],E=this.graphManager.getAllNodes(),m=0;m<E.length;m++){var I=E[m],w=I.getParent();this.getNodeDegreeWithChildren(I)===0&&(w.id==null||!this.getToBeTiled(w))&&p.push(I)}for(var m=0;m<p.length;m++){var I=p[m],S=I.getParent().id;typeof u[S]=="undefined"&&(u[S]=[]),u[S]=u[S].concat(I)}Object.keys(u).forEach(function(X){if(u[X].length>1){var $="DummyCompound_"+X;o.memberGroups[$]=u[X];var J=u[X][0].getParent(),tt=new n(o.graphManager);tt.id=$,tt.paddingLeft=J.paddingLeft||0,tt.paddingRight=J.paddingRight||0,tt.paddingBottom=J.paddingBottom||0,tt.paddingTop=J.paddingTop||0,o.idToDummyNode[$]=tt;var U=o.getGraphManager().add(o.newGraph(),tt),B=J.getChild();B.add(tt);for(var K=0;K<u[X].length;K++){var Z=u[X][K];B.remove(Z),U.add(Z)}}})},A.prototype.clearCompounds=function(){var o={},u={};this.performDFSOnCompounds();for(var p=0;p<this.compoundOrder.length;p++)u[this.compoundOrder[p].id]=this.compoundOrder[p],o[this.compoundOrder[p].id]=[].concat(this.compoundOrder[p].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[p].getChild()),this.compoundOrder[p].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(o,u)},A.prototype.clearZeroDegreeMembers=function(){var o=this,u=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(p){var E=o.idToDummyNode[p];u[p]=o.tileNodes(o.memberGroups[p],E.paddingLeft+E.paddingRight),E.rect.width=u[p].width,E.rect.height=u[p].height})},A.prototype.repopulateCompounds=function(){for(var o=this.compoundOrder.length-1;o>=0;o--){var u=this.compoundOrder[o],p=u.id,E=u.paddingLeft,m=u.paddingTop;this.adjustLocations(this.tiledMemberPack[p],u.rect.x,u.rect.y,E,m)}},A.prototype.repopulateZeroDegreeMembers=function(){var o=this,u=this.tiledZeroDegreePack;Object.keys(u).forEach(function(p){var E=o.idToDummyNode[p],m=E.paddingLeft,I=E.paddingTop;o.adjustLocations(u[p],E.rect.x,E.rect.y,m,I)})},A.prototype.getToBeTiled=function(o){var u=o.id;if(this.toBeTiled[u]!=null)return this.toBeTiled[u];var p=o.getChild();if(p==null)return this.toBeTiled[u]=!1,!1;for(var E=p.getNodes(),m=0;m<E.length;m++){var I=E[m];if(this.getNodeDegree(I)>0)return this.toBeTiled[u]=!1,!1;if(I.getChild()==null){this.toBeTiled[I.id]=!1;continue}if(!this.getToBeTiled(I))return this.toBeTiled[u]=!1,!1}return this.toBeTiled[u]=!0,!0},A.prototype.getNodeDegree=function(o){for(var u=o.id,p=o.getEdges(),E=0,m=0;m<p.length;m++){var I=p[m];I.getSource().id!==I.getTarget().id&&(E=E+1)}return E},A.prototype.getNodeDegreeWithChildren=function(o){var u=this.getNodeDegree(o);if(o.getChild()==null)return u;for(var p=o.getChild().getNodes(),E=0;E<p.length;E++){var m=p[E];u+=this.getNodeDegreeWithChildren(m)}return u},A.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},A.prototype.fillCompexOrderByDFS=function(o){for(var u=0;u<o.length;u++){var p=o[u];p.getChild()!=null&&this.fillCompexOrderByDFS(p.getChild().getNodes()),this.getToBeTiled(p)&&this.compoundOrder.push(p)}},A.prototype.adjustLocations=function(o,u,p,E,m){u+=E,p+=m;for(var I=u,w=0;w<o.rows.length;w++){var S=o.rows[w];u=I;for(var X=0,$=0;$<S.length;$++){var J=S[$];J.rect.x=u,J.rect.y=p,u+=J.rect.width+o.horizontalPadding,J.rect.height>X&&(X=J.rect.height)}p+=X+o.verticalPadding}},A.prototype.tileCompoundMembers=function(o,u){var p=this;this.tiledMemberPack=[],Object.keys(o).forEach(function(E){var m=u[E];p.tiledMemberPack[E]=p.tileNodes(o[E],m.paddingLeft+m.paddingRight),m.rect.width=p.tiledMemberPack[E].width,m.rect.height=p.tiledMemberPack[E].height})},A.prototype.tileNodes=function(o,u){var p=r.TILING_PADDING_VERTICAL,E=r.TILING_PADDING_HORIZONTAL,m={rows:[],rowWidth:[],rowHeight:[],width:0,height:u,verticalPadding:p,horizontalPadding:E};o.sort(function(S,X){return S.rect.width*S.rect.height>X.rect.width*X.rect.height?-1:S.rect.width*S.rect.height<X.rect.width*X.rect.height?1:0});for(var I=0;I<o.length;I++){var w=o[I];m.rows.length==0?this.insertNodeToRow(m,w,0,u):this.canAddHorizontal(m,w.rect.width,w.rect.height)?this.insertNodeToRow(m,w,this.getShortestRowIndex(m),u):this.insertNodeToRow(m,w,m.rows.length,u),this.shiftToLastRow(m)}return m},A.prototype.insertNodeToRow=function(o,u,p,E){var m=E;if(p==o.rows.length){var I=[];o.rows.push(I),o.rowWidth.push(m),o.rowHeight.push(0)}var w=o.rowWidth[p]+u.rect.width;o.rows[p].length>0&&(w+=o.horizontalPadding),o.rowWidth[p]=w,o.width<w&&(o.width=w);var S=u.rect.height;p>0&&(S+=o.verticalPadding);var X=0;S>o.rowHeight[p]&&(X=o.rowHeight[p],o.rowHeight[p]=S,X=o.rowHeight[p]-X),o.height+=X,o.rows[p].push(u)},A.prototype.getShortestRowIndex=function(o){for(var u=-1,p=Number.MAX_VALUE,E=0;E<o.rows.length;E++)o.rowWidth[E]<p&&(u=E,p=o.rowWidth[E]);return u},A.prototype.getLongestRowIndex=function(o){for(var u=-1,p=Number.MIN_VALUE,E=0;E<o.rows.length;E++)o.rowWidth[E]>p&&(u=E,p=o.rowWidth[E]);return u},A.prototype.canAddHorizontal=function(o,u,p){var E=this.getShortestRowIndex(o);if(E<0)return!0;var m=o.rowWidth[E];if(m+o.horizontalPadding+u<=o.width)return!0;var I=0;o.rowHeight[E]<p&&E>0&&(I=p+o.verticalPadding-o.rowHeight[E]);var w;o.width-m>=u+o.horizontalPadding?w=(o.height+I)/(m+u+o.horizontalPadding):w=(o.height+I)/o.width,I=p+o.verticalPadding;var S;return o.width<u?S=(o.height+I)/u:S=(o.height+I)/o.width,S<1&&(S=1/S),w<1&&(w=1/w),w<S},A.prototype.shiftToLastRow=function(o){var u=this.getLongestRowIndex(o),p=o.rowWidth.length-1,E=o.rows[u],m=E[E.length-1],I=m.width+o.horizontalPadding;if(o.width-o.rowWidth[p]>I&&u!=p){E.splice(-1,1),o.rows[p].push(m),o.rowWidth[u]=o.rowWidth[u]-I,o.rowWidth[p]=o.rowWidth[p]+I,o.width=o.rowWidth[instance.getLongestRowIndex(o)];for(var w=Number.MIN_VALUE,S=0;S<E.length;S++)E[S].height>w&&(w=E[S].height);u>0&&(w+=o.verticalPadding);var X=o.rowHeight[u]+o.rowHeight[p];o.rowHeight[u]=w,o.rowHeight[p]<m.height+o.verticalPadding&&(o.rowHeight[p]=m.height+o.verticalPadding);var $=o.rowHeight[u]+o.rowHeight[p];o.height+=$-X,this.shiftToLastRow(o)}},A.prototype.tilingPreLayout=function(){r.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},A.prototype.tilingPostLayout=function(){r.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},A.prototype.reduceTrees=function(){for(var o=[],u=!0,p;u;){var E=this.graphManager.getAllNodes(),m=[];u=!1;for(var I=0;I<E.length;I++)p=E[I],p.getEdges().length==1&&!p.getEdges()[0].isInterGraph&&p.getChild()==null&&(m.push([p,p.getEdges()[0],p.getOwner()]),u=!0);if(u==!0){for(var w=[],S=0;S<m.length;S++)m[S][0].getEdges().length==1&&(w.push(m[S]),m[S][0].getOwner().remove(m[S][0]));o.push(w),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=o},A.prototype.growTree=function(o){for(var u=o.length,p=o[u-1],E,m=0;m<p.length;m++)E=p[m],this.findPlaceforPrunedNode(E),E[2].add(E[0]),E[2].add(E[1],E[1].source,E[1].target);o.splice(o.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},A.prototype.findPlaceforPrunedNode=function(o){var u,p,E=o[0];E==o[1].source?p=o[1].target:p=o[1].source;var m=p.startX,I=p.finishX,w=p.startY,S=p.finishY,X=0,$=0,J=0,tt=0,U=[X,J,$,tt];if(w>0)for(var B=m;B<=I;B++)U[0]+=this.grid[B][w-1].length+this.grid[B][w].length-1;if(I<this.grid.length-1)for(var B=w;B<=S;B++)U[1]+=this.grid[I+1][B].length+this.grid[I][B].length-1;if(S<this.grid[0].length-1)for(var B=m;B<=I;B++)U[2]+=this.grid[B][S+1].length+this.grid[B][S].length-1;if(m>0)for(var B=w;B<=S;B++)U[3]+=this.grid[m-1][B].length+this.grid[m][B].length-1;for(var K=y.MAX_VALUE,Z,O,R=0;R<U.length;R++)U[R]<K?(K=U[R],Z=1,O=R):U[R]==K&&Z++;if(Z==3&&K==0)U[0]==0&&U[1]==0&&U[2]==0?u=1:U[0]==0&&U[1]==0&&U[3]==0?u=0:U[0]==0&&U[2]==0&&U[3]==0?u=3:U[1]==0&&U[2]==0&&U[3]==0&&(u=2);else if(Z==2&&K==0){var v=Math.floor(Math.random()*2);U[0]==0&&U[1]==0?v==0?u=0:u=1:U[0]==0&&U[2]==0?v==0?u=0:u=2:U[0]==0&&U[3]==0?v==0?u=0:u=3:U[1]==0&&U[2]==0?v==0?u=1:u=2:U[1]==0&&U[3]==0?v==0?u=1:u=3:v==0?u=2:u=3}else if(Z==4&&K==0){var v=Math.floor(Math.random()*4);u=v}else u=O;u==0?E.setCenter(p.getCenterX(),p.getCenterY()-p.getHeight()/2-a.DEFAULT_EDGE_LENGTH-E.getHeight()/2):u==1?E.setCenter(p.getCenterX()+p.getWidth()/2+a.DEFAULT_EDGE_LENGTH+E.getWidth()/2,p.getCenterY()):u==2?E.setCenter(p.getCenterX(),p.getCenterY()+p.getHeight()/2+a.DEFAULT_EDGE_LENGTH+E.getHeight()/2):E.setCenter(p.getCenterX()-p.getWidth()/2-a.DEFAULT_EDGE_LENGTH-E.getWidth()/2,p.getCenterY())},c.exports=A},function(c,s,t){"use strict";var e={};e.layoutBase=t(0),e.CoSEConstants=t(1),e.CoSEEdge=t(2),e.CoSEGraph=t(3),e.CoSEGraphManager=t(4),e.CoSELayout=t(6),e.CoSENode=t(5),c.exports=e}])})},67249:function(ut,P,k){(function(c,s){ut.exports=s(k(42270))})(this,function(N){return function(c){var s={};function t(e){if(s[e])return s[e].exports;var i=s[e]={i:e,l:!1,exports:{}};return c[e].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=c,t.c=s,t.i=function(e){return e},t.d=function(e,i,h){t.o(e,i)||Object.defineProperty(e,i,{configurable:!1,enumerable:!0,get:h})},t.n=function(e){var i=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(i,"a",i),i},t.o=function(e,i){return Object.prototype.hasOwnProperty.call(e,i)},t.p="",t(t.s=1)}([function(c,s){c.exports=N},function(c,s,t){"use strict";var e=t(0).layoutBase.LayoutConstants,i=t(0).layoutBase.FDLayoutConstants,h=t(0).CoSEConstants,n=t(0).CoSELayout,g=t(0).CoSENode,r=t(0).layoutBase.PointD,a=t(0).layoutBase.DimensionD,l={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function f(L,T){var x={};for(var A in L)x[A]=L[A];for(var A in T)x[A]=T[A];return x}function d(L){this.options=f(l,L),D(this.options)}var D=function(T){T.nodeRepulsion!=null&&(h.DEFAULT_REPULSION_STRENGTH=i.DEFAULT_REPULSION_STRENGTH=T.nodeRepulsion),T.idealEdgeLength!=null&&(h.DEFAULT_EDGE_LENGTH=i.DEFAULT_EDGE_LENGTH=T.idealEdgeLength),T.edgeElasticity!=null&&(h.DEFAULT_SPRING_STRENGTH=i.DEFAULT_SPRING_STRENGTH=T.edgeElasticity),T.nestingFactor!=null&&(h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=T.nestingFactor),T.gravity!=null&&(h.DEFAULT_GRAVITY_STRENGTH=i.DEFAULT_GRAVITY_STRENGTH=T.gravity),T.numIter!=null&&(h.MAX_ITERATIONS=i.MAX_ITERATIONS=T.numIter),T.gravityRange!=null&&(h.DEFAULT_GRAVITY_RANGE_FACTOR=i.DEFAULT_GRAVITY_RANGE_FACTOR=T.gravityRange),T.gravityCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=T.gravityCompound),T.gravityRangeCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=T.gravityRangeCompound),T.initialEnergyOnIncremental!=null&&(h.DEFAULT_COOLING_FACTOR_INCREMENTAL=i.DEFAULT_COOLING_FACTOR_INCREMENTAL=T.initialEnergyOnIncremental),T.quality=="draft"?e.QUALITY=0:T.quality=="proof"?e.QUALITY=2:e.QUALITY=1,h.NODE_DIMENSIONS_INCLUDE_LABELS=i.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=T.nodeDimensionsIncludeLabels,h.DEFAULT_INCREMENTAL=i.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=!T.randomize,h.ANIMATE=i.ANIMATE=e.ANIMATE=T.animate,h.TILE=T.tile,h.TILING_PADDING_VERTICAL=typeof T.tilingPaddingVertical=="function"?T.tilingPaddingVertical.call():T.tilingPaddingVertical,h.TILING_PADDING_HORIZONTAL=typeof T.tilingPaddingHorizontal=="function"?T.tilingPaddingHorizontal.call():T.tilingPaddingHorizontal};d.prototype.run=function(){var L,T,x=this.options,A=this.idToLNode={},F=this.layout=new n,o=this;o.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var u=F.newGraphManager();this.gm=u;var p=this.options.eles.nodes(),E=this.options.eles.edges();this.root=u.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(p),F);for(var m=0;m<E.length;m++){var I=E[m],w=this.idToLNode[I.data("source")],S=this.idToLNode[I.data("target")];if(w!==S&&w.getEdgesBetween(S).length==0){var X=u.add(F.newEdge(),w,S);X.id=I.id()}}var $=function(U,B){typeof U=="number"&&(U=B);var K=U.data("id"),Z=o.idToLNode[K];return{x:Z.getRect().getCenterX(),y:Z.getRect().getCenterY()}},J=function tt(){for(var U=function(){x.fit&&x.cy.fit(x.eles,x.padding),L||(L=!0,o.cy.one("layoutready",x.ready),o.cy.trigger({type:"layoutready",layout:o}))},B=o.options.refresh,K,Z=0;Z<B&&!K;Z++)K=o.stopped||o.layout.tick();if(K){F.checkLayoutSuccess()&&!F.isSubLayout&&F.doPostLayout(),F.tilingPostLayout&&F.tilingPostLayout(),F.isLayoutFinished=!0,o.options.eles.nodes().positions($),U(),o.cy.one("layoutstop",o.options.stop),o.cy.trigger({type:"layoutstop",layout:o}),T&&cancelAnimationFrame(T),L=!1;return}var O=o.layout.getPositionsData();x.eles.nodes().positions(function(R,v){if(typeof R=="number"&&(R=v),!R.isParent()){for(var M=R.id(),G=O[M],C=R;G==null&&(G=O[C.data("parent")]||O["DummyCompound_"+C.data("parent")],O[M]=G,C=C.parent()[0],C!=null););return G!=null?{x:G.x,y:G.y}:{x:R.position("x"),y:R.position("y")}}}),U(),T=requestAnimationFrame(tt)};return F.addListener("layoutstarted",function(){o.options.animate==="during"&&(T=requestAnimationFrame(J))}),F.runLayout(),this.options.animate!=="during"&&(o.options.eles.nodes().not(":parent").layoutPositions(o,o.options,$),L=!1),this},d.prototype.getTopMostNodes=function(L){for(var T={},x=0;x<L.length;x++)T[L[x].id()]=!0;var A=L.filter(function(F,o){typeof F=="number"&&(F=o);for(var u=F.parent()[0];u!=null;){if(T[u.id()])return!1;u=u.parent()[0]}return!0});return A},d.prototype.processChildrenList=function(L,T,x){for(var A=T.length,F=0;F<A;F++){var o=T[F],u=o.children(),p,E=o.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(o.outerWidth()!=null&&o.outerHeight()!=null?p=L.add(new g(x.graphManager,new r(o.position("x")-E.w/2,o.position("y")-E.h/2),new a(parseFloat(E.w),parseFloat(E.h)))):p=L.add(new g(this.graphManager)),p.id=o.data("id"),p.paddingLeft=parseInt(o.css("padding")),p.paddingTop=parseInt(o.css("padding")),p.paddingRight=parseInt(o.css("padding")),p.paddingBottom=parseInt(o.css("padding")),this.options.nodeDimensionsIncludeLabels&&o.isParent()){var m=o.boundingBox({includeLabels:!0,includeNodes:!1}).w,I=o.boundingBox({includeLabels:!0,includeNodes:!1}).h,w=o.css("text-halign");p.labelWidth=m,p.labelHeight=I,p.labelPos=w}if(this.idToLNode[o.data("id")]=p,isNaN(p.rect.x)&&(p.rect.x=0),isNaN(p.rect.y)&&(p.rect.y=0),u!=null&&u.length>0){var S;S=x.getGraphManager().add(x.newGraph(),p),this.processChildrenList(S,u,x)}}},d.prototype.stop=function(){return this.stopped=!0,this};var y=function(T){T("layout","cose-bilkent",d)};typeof cytoscape!="undefined"&&y(cytoscape),c.exports=y}])})},43221:function(ut){(function(k,N){ut.exports=N()})(this,function(){return function(P){var k={};function N(c){if(k[c])return k[c].exports;var s=k[c]={i:c,l:!1,exports:{}};return P[c].call(s.exports,s,s.exports,N),s.l=!0,s.exports}return N.m=P,N.c=k,N.i=function(c){return c},N.d=function(c,s,t){N.o(c,s)||Object.defineProperty(c,s,{configurable:!1,enumerable:!0,get:t})},N.n=function(c){var s=c&&c.__esModule?function(){return c.default}:function(){return c};return N.d(s,"a",s),s},N.o=function(c,s){return Object.prototype.hasOwnProperty.call(c,s)},N.p="",N(N.s=26)}([function(P,k,N){"use strict";function c(){}c.QUALITY=1,c.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,c.DEFAULT_INCREMENTAL=!1,c.DEFAULT_ANIMATION_ON_LAYOUT=!0,c.DEFAULT_ANIMATION_DURING_LAYOUT=!1,c.DEFAULT_ANIMATION_PERIOD=50,c.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,c.DEFAULT_GRAPH_MARGIN=15,c.NODE_DIMENSIONS_INCLUDE_LABELS=!1,c.SIMPLE_NODE_SIZE=40,c.SIMPLE_NODE_HALF_SIZE=c.SIMPLE_NODE_SIZE/2,c.EMPTY_COMPOUND_NODE_SIZE=40,c.MIN_EDGE_LENGTH=1,c.WORLD_BOUNDARY=1e6,c.INITIAL_WORLD_BOUNDARY=c.WORLD_BOUNDARY/1e3,c.WORLD_CENTER_X=1200,c.WORLD_CENTER_Y=900,P.exports=c},function(P,k,N){"use strict";var c=N(2),s=N(8),t=N(9);function e(h,n,g){c.call(this,g),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=g,this.bendpoints=[],this.source=h,this.target=n}e.prototype=Object.create(c.prototype);for(var i in c)e[i]=c[i];e.prototype.getSource=function(){return this.source},e.prototype.getTarget=function(){return this.target},e.prototype.isInterGraph=function(){return this.isInterGraph},e.prototype.getLength=function(){return this.length},e.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},e.prototype.getBendpoints=function(){return this.bendpoints},e.prototype.getLca=function(){return this.lca},e.prototype.getSourceInLca=function(){return this.sourceInLca},e.prototype.getTargetInLca=function(){return this.targetInLca},e.prototype.getOtherEnd=function(h){if(this.source===h)return this.target;if(this.target===h)return this.source;throw"Node is not incident with this edge"},e.prototype.getOtherEndInGraph=function(h,n){for(var g=this.getOtherEnd(h),r=n.getGraphManager().getRoot();;){if(g.getOwner()==n)return g;if(g.getOwner()==r)break;g=g.getOwner().getParent()}return null},e.prototype.updateLength=function(){var h=new Array(4);this.isOverlapingSourceAndTarget=s.getIntersection(this.target.getRect(),this.source.getRect(),h),this.isOverlapingSourceAndTarget||(this.lengthX=h[0]-h[2],this.lengthY=h[1]-h[3],Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},e.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=t.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=t.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},P.exports=e},function(P,k,N){"use strict";function c(s){this.vGraphObject=s}P.exports=c},function(P,k,N){"use strict";var c=N(2),s=N(10),t=N(13),e=N(0),i=N(16),h=N(4);function n(r,a,l,f){l==null&&f==null&&(f=a),c.call(this,f),r.graphManager!=null&&(r=r.graphManager),this.estimatedSize=s.MIN_VALUE,this.inclusionTreeDepth=s.MAX_VALUE,this.vGraphObject=f,this.edges=[],this.graphManager=r,l!=null&&a!=null?this.rect=new t(a.x,a.y,l.width,l.height):this.rect=new t}n.prototype=Object.create(c.prototype);for(var g in c)n[g]=c[g];n.prototype.getEdges=function(){return this.edges},n.prototype.getChild=function(){return this.child},n.prototype.getOwner=function(){return this.owner},n.prototype.getWidth=function(){return this.rect.width},n.prototype.setWidth=function(r){this.rect.width=r},n.prototype.getHeight=function(){return this.rect.height},n.prototype.setHeight=function(r){this.rect.height=r},n.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},n.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},n.prototype.getCenter=function(){return new h(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},n.prototype.getLocation=function(){return new h(this.rect.x,this.rect.y)},n.prototype.getRect=function(){return this.rect},n.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},n.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},n.prototype.setRect=function(r,a){this.rect.x=r.x,this.rect.y=r.y,this.rect.width=a.width,this.rect.height=a.height},n.prototype.setCenter=function(r,a){this.rect.x=r-this.rect.width/2,this.rect.y=a-this.rect.height/2},n.prototype.setLocation=function(r,a){this.rect.x=r,this.rect.y=a},n.prototype.moveBy=function(r,a){this.rect.x+=r,this.rect.y+=a},n.prototype.getEdgeListToNode=function(r){var a=[],l,f=this;return f.edges.forEach(function(d){if(d.target==r){if(d.source!=f)throw"Incorrect edge source!";a.push(d)}}),a},n.prototype.getEdgesBetween=function(r){var a=[],l,f=this;return f.edges.forEach(function(d){if(!(d.source==f||d.target==f))throw"Incorrect edge source and/or target";(d.target==r||d.source==r)&&a.push(d)}),a},n.prototype.getNeighborsList=function(){var r=new Set,a=this;return a.edges.forEach(function(l){if(l.source==a)r.add(l.target);else{if(l.target!=a)throw"Incorrect incidency!";r.add(l.source)}}),r},n.prototype.withChildren=function(){var r=new Set,a,l;if(r.add(this),this.child!=null)for(var f=this.child.getNodes(),d=0;d<f.length;d++)a=f[d],l=a.withChildren(),l.forEach(function(D){r.add(D)});return r},n.prototype.getNoOfChildren=function(){var r=0,a;if(this.child==null)r=1;else for(var l=this.child.getNodes(),f=0;f<l.length;f++)a=l[f],r+=a.getNoOfChildren();return r==0&&(r=1),r},n.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},n.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},n.prototype.scatter=function(){var r,a,l=-e.INITIAL_WORLD_BOUNDARY,f=e.INITIAL_WORLD_BOUNDARY;r=e.WORLD_CENTER_X+i.nextDouble()*(f-l)+l;var d=-e.INITIAL_WORLD_BOUNDARY,D=e.INITIAL_WORLD_BOUNDARY;a=e.WORLD_CENTER_Y+i.nextDouble()*(D-d)+d,this.rect.x=r,this.rect.y=a},n.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var r=this.getChild();if(r.updateBounds(!0),this.rect.x=r.getLeft(),this.rect.y=r.getTop(),this.setWidth(r.getRight()-r.getLeft()),this.setHeight(r.getBottom()-r.getTop()),e.NODE_DIMENSIONS_INCLUDE_LABELS){var a=r.getRight()-r.getLeft(),l=r.getBottom()-r.getTop();this.labelWidth>a&&(this.rect.x-=(this.labelWidth-a)/2,this.setWidth(this.labelWidth)),this.labelHeight>l&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-l)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-l),this.setHeight(this.labelHeight))}}},n.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==s.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},n.prototype.transform=function(r){var a=this.rect.x;a>e.WORLD_BOUNDARY?a=e.WORLD_BOUNDARY:a<-e.WORLD_BOUNDARY&&(a=-e.WORLD_BOUNDARY);var l=this.rect.y;l>e.WORLD_BOUNDARY?l=e.WORLD_BOUNDARY:l<-e.WORLD_BOUNDARY&&(l=-e.WORLD_BOUNDARY);var f=new h(a,l),d=r.inverseTransformPoint(f);this.setLocation(d.x,d.y)},n.prototype.getLeft=function(){return this.rect.x},n.prototype.getRight=function(){return this.rect.x+this.rect.width},n.prototype.getTop=function(){return this.rect.y},n.prototype.getBottom=function(){return this.rect.y+this.rect.height},n.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},P.exports=n},function(P,k,N){"use strict";function c(s,t){s==null&&t==null?(this.x=0,this.y=0):(this.x=s,this.y=t)}c.prototype.getX=function(){return this.x},c.prototype.getY=function(){return this.y},c.prototype.setX=function(s){this.x=s},c.prototype.setY=function(s){this.y=s},c.prototype.getDifference=function(s){return new DimensionD(this.x-s.x,this.y-s.y)},c.prototype.getCopy=function(){return new c(this.x,this.y)},c.prototype.translate=function(s){return this.x+=s.width,this.y+=s.height,this},P.exports=c},function(P,k,N){"use strict";var c=N(2),s=N(10),t=N(0),e=N(6),i=N(3),h=N(1),n=N(13),g=N(12),r=N(11);function a(f,d,D){c.call(this,D),this.estimatedSize=s.MIN_VALUE,this.margin=t.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=f,d!=null&&d instanceof e?this.graphManager=d:d!=null&&d instanceof Layout&&(this.graphManager=d.graphManager)}a.prototype=Object.create(c.prototype);for(var l in c)a[l]=c[l];a.prototype.getNodes=function(){return this.nodes},a.prototype.getEdges=function(){return this.edges},a.prototype.getGraphManager=function(){return this.graphManager},a.prototype.getParent=function(){return this.parent},a.prototype.getLeft=function(){return this.left},a.prototype.getRight=function(){return this.right},a.prototype.getTop=function(){return this.top},a.prototype.getBottom=function(){return this.bottom},a.prototype.isConnected=function(){return this.isConnected},a.prototype.add=function(f,d,D){if(d==null&&D==null){var y=f;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(y)>-1)throw"Node already in graph!";return y.owner=this,this.getNodes().push(y),y}else{var L=f;if(!(this.getNodes().indexOf(d)>-1&&this.getNodes().indexOf(D)>-1))throw"Source or target not in graph!";if(!(d.owner==D.owner&&d.owner==this))throw"Both owners must be this graph!";return d.owner!=D.owner?null:(L.source=d,L.target=D,L.isInterGraph=!1,this.getEdges().push(L),d.edges.push(L),D!=d&&D.edges.push(L),L)}},a.prototype.remove=function(f){var d=f;if(f instanceof i){if(d==null)throw"Node is null!";if(!(d.owner!=null&&d.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var D=d.edges.slice(),y,L=D.length,T=0;T<L;T++)y=D[T],y.isInterGraph?this.graphManager.remove(y):y.source.owner.remove(y);var x=this.nodes.indexOf(d);if(x==-1)throw"Node not in owner node list!";this.nodes.splice(x,1)}else if(f instanceof h){var y=f;if(y==null)throw"Edge is null!";if(!(y.source!=null&&y.target!=null))throw"Source and/or target is null!";if(!(y.source.owner!=null&&y.target.owner!=null&&y.source.owner==this&&y.target.owner==this))throw"Source and/or target owner is invalid!";var A=y.source.edges.indexOf(y),F=y.target.edges.indexOf(y);if(!(A>-1&&F>-1))throw"Source and/or target doesn't know this edge!";y.source.edges.splice(A,1),y.target!=y.source&&y.target.edges.splice(F,1);var x=y.source.owner.getEdges().indexOf(y);if(x==-1)throw"Not in owner's edge list!";y.source.owner.getEdges().splice(x,1)}},a.prototype.updateLeftTop=function(){for(var f=s.MAX_VALUE,d=s.MAX_VALUE,D,y,L,T=this.getNodes(),x=T.length,A=0;A<x;A++){var F=T[A];D=F.getTop(),y=F.getLeft(),f>D&&(f=D),d>y&&(d=y)}return f==s.MAX_VALUE?null:(T[0].getParent().paddingLeft!=null?L=T[0].getParent().paddingLeft:L=this.margin,this.left=d-L,this.top=f-L,new g(this.left,this.top))},a.prototype.updateBounds=function(f){for(var d=s.MAX_VALUE,D=-s.MAX_VALUE,y=s.MAX_VALUE,L=-s.MAX_VALUE,T,x,A,F,o,u=this.nodes,p=u.length,E=0;E<p;E++){var m=u[E];f&&m.child!=null&&m.updateBounds(),T=m.getLeft(),x=m.getRight(),A=m.getTop(),F=m.getBottom(),d>T&&(d=T),D<x&&(D=x),y>A&&(y=A),L<F&&(L=F)}var I=new n(d,y,D-d,L-y);d==s.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),u[0].getParent().paddingLeft!=null?o=u[0].getParent().paddingLeft:o=this.margin,this.left=I.x-o,this.right=I.x+I.width+o,this.top=I.y-o,this.bottom=I.y+I.height+o},a.calculateBounds=function(f){for(var d=s.MAX_VALUE,D=-s.MAX_VALUE,y=s.MAX_VALUE,L=-s.MAX_VALUE,T,x,A,F,o=f.length,u=0;u<o;u++){var p=f[u];T=p.getLeft(),x=p.getRight(),A=p.getTop(),F=p.getBottom(),d>T&&(d=T),D<x&&(D=x),y>A&&(y=A),L<F&&(L=F)}var E=new n(d,y,D-d,L-y);return E},a.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){for(var f=0,d=this.nodes,D=d.length,y=0;y<D;y++){var L=d[y];f+=L.calcEstimatedSize()}return f==0?this.estimatedSize=t.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=f/Math.sqrt(this.nodes.length),this.estimatedSize},a.prototype.updateConnected=function(){var f=this;if(this.nodes.length==0){this.isConnected=!0;return}var d=new r,D=new Set,y=this.nodes[0],L,T,x=y.withChildren();for(x.forEach(function(E){d.push(E),D.add(E)});d.length!==0;){y=d.shift(),L=y.getEdges();for(var A=L.length,F=0;F<A;F++){var o=L[F];if(T=o.getOtherEndInGraph(y,this),T!=null&&!D.has(T)){var u=T.withChildren();u.forEach(function(E){d.push(E),D.add(E)})}}}if(this.isConnected=!1,D.size>=this.nodes.length){var p=0;D.forEach(function(E){E.owner==f&&p++}),p==this.nodes.length&&(this.isConnected=!0)}},P.exports=a},function(P,k,N){"use strict";var c,s=N(1);function t(e){c=N(5),this.layout=e,this.graphs=[],this.edges=[]}t.prototype.addRoot=function(){var e=this.layout.newGraph(),i=this.layout.newNode(null),h=this.add(e,i);return this.setRootGraph(h),this.rootGraph},t.prototype.add=function(e,i,h,n,g){if(h==null&&n==null&&g==null){if(e==null)throw"Graph is null!";if(i==null)throw"Parent node is null!";if(this.graphs.indexOf(e)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(e),e.parent!=null)throw"Already has a parent!";if(i.child!=null)throw"Already has a child!";return e.parent=i,i.child=e,e}else{g=h,n=i,h=e;var r=n.getOwner(),a=g.getOwner();if(!(r!=null&&r.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(a!=null&&a.getGraphManager()==this))throw"Target not in this graph mgr!";if(r==a)return h.isInterGraph=!1,r.add(h,n,g);if(h.isInterGraph=!0,h.source=n,h.target=g,this.edges.indexOf(h)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(h),!(h.source!=null&&h.target!=null))throw"Edge source and/or target is null!";if(!(h.source.edges.indexOf(h)==-1&&h.target.edges.indexOf(h)==-1))throw"Edge already in source and/or target incidency list!";return h.source.edges.push(h),h.target.edges.push(h),h}},t.prototype.remove=function(e){if(e instanceof c){var i=e;if(i.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(i==this.rootGraph||i.parent!=null&&i.parent.graphManager==this))throw"Invalid parent node!";var h=[];h=h.concat(i.getEdges());for(var n,g=h.length,r=0;r<g;r++)n=h[r],i.remove(n);var a=[];a=a.concat(i.getNodes());var l;g=a.length;for(var r=0;r<g;r++)l=a[r],i.remove(l);i==this.rootGraph&&this.setRootGraph(null);var f=this.graphs.indexOf(i);this.graphs.splice(f,1),i.parent=null}else if(e instanceof s){if(n=e,n==null)throw"Edge is null!";if(!n.isInterGraph)throw"Not an inter-graph edge!";if(!(n.source!=null&&n.target!=null))throw"Source and/or target is null!";if(!(n.source.edges.indexOf(n)!=-1&&n.target.edges.indexOf(n)!=-1))throw"Source and/or target doesn't know this edge!";var f=n.source.edges.indexOf(n);if(n.source.edges.splice(f,1),f=n.target.edges.indexOf(n),n.target.edges.splice(f,1),!(n.source.owner!=null&&n.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(n.source.owner.getGraphManager().edges.indexOf(n)==-1)throw"Not in owner graph manager's edge list!";var f=n.source.owner.getGraphManager().edges.indexOf(n);n.source.owner.getGraphManager().edges.splice(f,1)}},t.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},t.prototype.getGraphs=function(){return this.graphs},t.prototype.getAllNodes=function(){if(this.allNodes==null){for(var e=[],i=this.getGraphs(),h=i.length,n=0;n<h;n++)e=e.concat(i[n].getNodes());this.allNodes=e}return this.allNodes},t.prototype.resetAllNodes=function(){this.allNodes=null},t.prototype.resetAllEdges=function(){this.allEdges=null},t.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},t.prototype.getAllEdges=function(){if(this.allEdges==null){for(var e=[],i=this.getGraphs(),h=i.length,n=0;n<i.length;n++)e=e.concat(i[n].getEdges());e=e.concat(this.edges),this.allEdges=e}return this.allEdges},t.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},t.prototype.setAllNodesToApplyGravitation=function(e){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=e},t.prototype.getRoot=function(){return this.rootGraph},t.prototype.setRootGraph=function(e){if(e.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=e,e.parent==null&&(e.parent=this.layout.newNode("Root node"))},t.prototype.getLayout=function(){return this.layout},t.prototype.isOneAncestorOfOther=function(e,i){if(!(e!=null&&i!=null))throw"assert failed";if(e==i)return!0;var h=e.getOwner(),n;do{if(n=h.getParent(),n==null)break;if(n==i)return!0;if(h=n.getOwner(),h==null)break}while(!0);h=i.getOwner();do{if(n=h.getParent(),n==null)break;if(n==e)return!0;if(h=n.getOwner(),h==null)break}while(!0);return!1},t.prototype.calcLowestCommonAncestors=function(){for(var e,i,h,n,g,r=this.getAllEdges(),a=r.length,l=0;l<a;l++){if(e=r[l],i=e.source,h=e.target,e.lca=null,e.sourceInLca=i,e.targetInLca=h,i==h){e.lca=i.getOwner();continue}for(n=i.getOwner();e.lca==null;){for(e.targetInLca=h,g=h.getOwner();e.lca==null;){if(g==n){e.lca=g;break}if(g==this.rootGraph)break;if(e.lca!=null)throw"assert failed";e.targetInLca=g.getParent(),g=e.targetInLca.getOwner()}if(n==this.rootGraph)break;e.lca==null&&(e.sourceInLca=n.getParent(),n=e.sourceInLca.getOwner())}if(e.lca==null)throw"assert failed"}},t.prototype.calcLowestCommonAncestor=function(e,i){if(e==i)return e.getOwner();var h=e.getOwner();do{if(h==null)break;var n=i.getOwner();do{if(n==null)break;if(n==h)return n;n=n.getParent().getOwner()}while(!0);h=h.getParent().getOwner()}while(!0);return h},t.prototype.calcInclusionTreeDepths=function(e,i){e==null&&i==null&&(e=this.rootGraph,i=1);for(var h,n=e.getNodes(),g=n.length,r=0;r<g;r++)h=n[r],h.inclusionTreeDepth=i,h.child!=null&&this.calcInclusionTreeDepths(h.child,i+1)},t.prototype.includesInvalidEdge=function(){for(var e,i=this.edges.length,h=0;h<i;h++)if(e=this.edges[h],this.isOneAncestorOfOther(e.source,e.target))return!0;return!1},P.exports=t},function(P,k,N){"use strict";var c=N(0);function s(){}for(var t in c)s[t]=c[t];s.MAX_ITERATIONS=2500,s.DEFAULT_EDGE_LENGTH=50,s.DEFAULT_SPRING_STRENGTH=.45,s.DEFAULT_REPULSION_STRENGTH=4500,s.DEFAULT_GRAVITY_STRENGTH=.4,s.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,s.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,s.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,s.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,s.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,s.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,s.COOLING_ADAPTATION_FACTOR=.33,s.ADAPTATION_LOWER_NODE_LIMIT=1e3,s.ADAPTATION_UPPER_NODE_LIMIT=5e3,s.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,s.MAX_NODE_DISPLACEMENT=s.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,s.MIN_REPULSION_DIST=s.DEFAULT_EDGE_LENGTH/10,s.CONVERGENCE_CHECK_PERIOD=100,s.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,s.MIN_EDGE_LENGTH=1,s.GRID_CALCULATION_CHECK_PERIOD=10,P.exports=s},function(P,k,N){"use strict";var c=N(12);function s(){}s.calcSeparationAmount=function(t,e,i,h){if(!t.intersects(e))throw"assert failed";var n=new Array(2);this.decideDirectionsForOverlappingNodes(t,e,n),i[0]=Math.min(t.getRight(),e.getRight())-Math.max(t.x,e.x),i[1]=Math.min(t.getBottom(),e.getBottom())-Math.max(t.y,e.y),t.getX()<=e.getX()&&t.getRight()>=e.getRight()?i[0]+=Math.min(e.getX()-t.getX(),t.getRight()-e.getRight()):e.getX()<=t.getX()&&e.getRight()>=t.getRight()&&(i[0]+=Math.min(t.getX()-e.getX(),e.getRight()-t.getRight())),t.getY()<=e.getY()&&t.getBottom()>=e.getBottom()?i[1]+=Math.min(e.getY()-t.getY(),t.getBottom()-e.getBottom()):e.getY()<=t.getY()&&e.getBottom()>=t.getBottom()&&(i[1]+=Math.min(t.getY()-e.getY(),e.getBottom()-t.getBottom()));var g=Math.abs((e.getCenterY()-t.getCenterY())/(e.getCenterX()-t.getCenterX()));e.getCenterY()===t.getCenterY()&&e.getCenterX()===t.getCenterX()&&(g=1);var r=g*i[0],a=i[1]/g;i[0]<a?a=i[0]:r=i[1],i[0]=-1*n[0]*(a/2+h),i[1]=-1*n[1]*(r/2+h)},s.decideDirectionsForOverlappingNodes=function(t,e,i){t.getCenterX()<e.getCenterX()?i[0]=-1:i[0]=1,t.getCenterY()<e.getCenterY()?i[1]=-1:i[1]=1},s.getIntersection2=function(t,e,i){var h=t.getCenterX(),n=t.getCenterY(),g=e.getCenterX(),r=e.getCenterY();if(t.intersects(e))return i[0]=h,i[1]=n,i[2]=g,i[3]=r,!0;var a=t.getX(),l=t.getY(),f=t.getRight(),d=t.getX(),D=t.getBottom(),y=t.getRight(),L=t.getWidthHalf(),T=t.getHeightHalf(),x=e.getX(),A=e.getY(),F=e.getRight(),o=e.getX(),u=e.getBottom(),p=e.getRight(),E=e.getWidthHalf(),m=e.getHeightHalf(),I=!1,w=!1;if(h===g){if(n>r)return i[0]=h,i[1]=l,i[2]=g,i[3]=u,!1;if(n<r)return i[0]=h,i[1]=D,i[2]=g,i[3]=A,!1}else if(n===r){if(h>g)return i[0]=a,i[1]=n,i[2]=F,i[3]=r,!1;if(h<g)return i[0]=f,i[1]=n,i[2]=x,i[3]=r,!1}else{var S=t.height/t.width,X=e.height/e.width,$=(r-n)/(g-h),J=void 0,tt=void 0,U=void 0,B=void 0,K=void 0,Z=void 0;if(-S===$?h>g?(i[0]=d,i[1]=D,I=!0):(i[0]=f,i[1]=l,I=!0):S===$&&(h>g?(i[0]=a,i[1]=l,I=!0):(i[0]=y,i[1]=D,I=!0)),-X===$?g>h?(i[2]=o,i[3]=u,w=!0):(i[2]=F,i[3]=A,w=!0):X===$&&(g>h?(i[2]=x,i[3]=A,w=!0):(i[2]=p,i[3]=u,w=!0)),I&&w)return!1;if(h>g?n>r?(J=this.getCardinalDirection(S,$,4),tt=this.getCardinalDirection(X,$,2)):(J=this.getCardinalDirection(-S,$,3),tt=this.getCardinalDirection(-X,$,1)):n>r?(J=this.getCardinalDirection(-S,$,1),tt=this.getCardinalDirection(-X,$,3)):(J=this.getCardinalDirection(S,$,2),tt=this.getCardinalDirection(X,$,4)),!I)switch(J){case 1:B=l,U=h+-T/$,i[0]=U,i[1]=B;break;case 2:U=y,B=n+L*$,i[0]=U,i[1]=B;break;case 3:B=D,U=h+T/$,i[0]=U,i[1]=B;break;case 4:U=d,B=n+-L*$,i[0]=U,i[1]=B;break}if(!w)switch(tt){case 1:Z=A,K=g+-m/$,i[2]=K,i[3]=Z;break;case 2:K=p,Z=r+E*$,i[2]=K,i[3]=Z;break;case 3:Z=u,K=g+m/$,i[2]=K,i[3]=Z;break;case 4:K=o,Z=r+-E*$,i[2]=K,i[3]=Z;break}}return!1},s.getCardinalDirection=function(t,e,i){return t>e?i:1+i%4},s.getIntersection=function(t,e,i,h){if(h==null)return this.getIntersection2(t,e,i);var n=t.x,g=t.y,r=e.x,a=e.y,l=i.x,f=i.y,d=h.x,D=h.y,y=void 0,L=void 0,T=void 0,x=void 0,A=void 0,F=void 0,o=void 0,u=void 0,p=void 0;return T=a-g,A=n-r,o=r*g-n*a,x=D-f,F=l-d,u=d*f-l*D,p=T*F-x*A,p===0?null:(y=(A*u-F*o)/p,L=(x*o-T*u)/p,new c(y,L))},s.angleOfVector=function(t,e,i,h){var n=void 0;return t!==i?(n=Math.atan((h-e)/(i-t)),i<t?n+=Math.PI:h<e&&(n+=this.TWO_PI)):h<e?n=this.ONE_AND_HALF_PI:n=this.HALF_PI,n},s.doIntersect=function(t,e,i,h){var n=t.x,g=t.y,r=e.x,a=e.y,l=i.x,f=i.y,d=h.x,D=h.y,y=(r-n)*(D-f)-(d-l)*(a-g);if(y===0)return!1;var L=((D-f)*(d-n)+(l-d)*(D-g))/y,T=((g-a)*(d-n)+(r-n)*(D-g))/y;return 0<L&&L<1&&0<T&&T<1},s.HALF_PI=.5*Math.PI,s.ONE_AND_HALF_PI=1.5*Math.PI,s.TWO_PI=2*Math.PI,s.THREE_PI=3*Math.PI,P.exports=s},function(P,k,N){"use strict";function c(){}c.sign=function(s){return s>0?1:s<0?-1:0},c.floor=function(s){return s<0?Math.ceil(s):Math.floor(s)},c.ceil=function(s){return s<0?Math.floor(s):Math.ceil(s)},P.exports=c},function(P,k,N){"use strict";function c(){}c.MAX_VALUE=2147483647,c.MIN_VALUE=-2147483648,P.exports=c},function(P,k,N){"use strict";var c=function(){function n(g,r){for(var a=0;a<r.length;a++){var l=r[a];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(g,l.key,l)}}return function(g,r,a){return r&&n(g.prototype,r),a&&n(g,a),g}}();function s(n,g){if(!(n instanceof g))throw new TypeError("Cannot call a class as a function")}var t=function(g){return{value:g,next:null,prev:null}},e=function(g,r,a,l){return g!==null?g.next=r:l.head=r,a!==null?a.prev=r:l.tail=r,r.prev=g,r.next=a,l.length++,r},i=function(g,r){var a=g.prev,l=g.next;return a!==null?a.next=l:r.head=l,l!==null?l.prev=a:r.tail=a,g.prev=g.next=null,r.length--,g},h=function(){function n(g){var r=this;s(this,n),this.length=0,this.head=null,this.tail=null,g!=null&&g.forEach(function(a){return r.push(a)})}return c(n,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(r,a){return e(a.prev,t(r),a,this)}},{key:"insertAfter",value:function(r,a){return e(a,t(r),a.next,this)}},{key:"insertNodeBefore",value:function(r,a){return e(a.prev,r,a,this)}},{key:"insertNodeAfter",value:function(r,a){return e(a,r,a.next,this)}},{key:"push",value:function(r){return e(this.tail,t(r),null,this)}},{key:"unshift",value:function(r){return e(null,t(r),this.head,this)}},{key:"remove",value:function(r){return i(r,this)}},{key:"pop",value:function(){return i(this.tail,this).value}},{key:"popNode",value:function(){return i(this.tail,this)}},{key:"shift",value:function(){return i(this.head,this).value}},{key:"shiftNode",value:function(){return i(this.head,this)}},{key:"get_object_at",value:function(r){if(r<=this.length()){for(var a=1,l=this.head;a<r;)l=l.next,a++;return l.value}}},{key:"set_object_at",value:function(r,a){if(r<=this.length()){for(var l=1,f=this.head;l<r;)f=f.next,l++;f.value=a}}}]),n}();P.exports=h},function(P,k,N){"use strict";function c(s,t,e){this.x=null,this.y=null,s==null&&t==null&&e==null?(this.x=0,this.y=0):typeof s=="number"&&typeof t=="number"&&e==null?(this.x=s,this.y=t):s.constructor.name=="Point"&&t==null&&e==null&&(e=s,this.x=e.x,this.y=e.y)}c.prototype.getX=function(){return this.x},c.prototype.getY=function(){return this.y},c.prototype.getLocation=function(){return new c(this.x,this.y)},c.prototype.setLocation=function(s,t,e){s.constructor.name=="Point"&&t==null&&e==null?(e=s,this.setLocation(e.x,e.y)):typeof s=="number"&&typeof t=="number"&&e==null&&(parseInt(s)==s&&parseInt(t)==t?this.move(s,t):(this.x=Math.floor(s+.5),this.y=Math.floor(t+.5)))},c.prototype.move=function(s,t){this.x=s,this.y=t},c.prototype.translate=function(s,t){this.x+=s,this.y+=t},c.prototype.equals=function(s){if(s.constructor.name=="Point"){var t=s;return this.x==t.x&&this.y==t.y}return this==s},c.prototype.toString=function(){return new c().constructor.name+"[x="+this.x+",y="+this.y+"]"},P.exports=c},function(P,k,N){"use strict";function c(s,t,e,i){this.x=0,this.y=0,this.width=0,this.height=0,s!=null&&t!=null&&e!=null&&i!=null&&(this.x=s,this.y=t,this.width=e,this.height=i)}c.prototype.getX=function(){return this.x},c.prototype.setX=function(s){this.x=s},c.prototype.getY=function(){return this.y},c.prototype.setY=function(s){this.y=s},c.prototype.getWidth=function(){return this.width},c.prototype.setWidth=function(s){this.width=s},c.prototype.getHeight=function(){return this.height},c.prototype.setHeight=function(s){this.height=s},c.prototype.getRight=function(){return this.x+this.width},c.prototype.getBottom=function(){return this.y+this.height},c.prototype.intersects=function(s){return!(this.getRight()<s.x||this.getBottom()<s.y||s.getRight()<this.x||s.getBottom()<this.y)},c.prototype.getCenterX=function(){return this.x+this.width/2},c.prototype.getMinX=function(){return this.getX()},c.prototype.getMaxX=function(){return this.getX()+this.width},c.prototype.getCenterY=function(){return this.y+this.height/2},c.prototype.getMinY=function(){return this.getY()},c.prototype.getMaxY=function(){return this.getY()+this.height},c.prototype.getWidthHalf=function(){return this.width/2},c.prototype.getHeightHalf=function(){return this.height/2},P.exports=c},function(P,k,N){"use strict";var c=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function s(){}s.lastID=0,s.createID=function(t){return s.isPrimitive(t)?t:(t.uniqueID!=null||(t.uniqueID=s.getString(),s.lastID++),t.uniqueID)},s.getString=function(t){return t==null&&(t=s.lastID),"Object#"+t},s.isPrimitive=function(t){var e=typeof t=="undefined"?"undefined":c(t);return t==null||e!="object"&&e!="function"},P.exports=s},function(P,k,N){"use strict";function c(l){if(Array.isArray(l)){for(var f=0,d=Array(l.length);f<l.length;f++)d[f]=l[f];return d}else return Array.from(l)}var s=N(0),t=N(6),e=N(3),i=N(1),h=N(5),n=N(4),g=N(17),r=N(27);function a(l){r.call(this),this.layoutQuality=s.QUALITY,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=s.DEFAULT_INCREMENTAL,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new t(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,l!=null&&(this.isRemoteUse=l)}a.RANDOM_SEED=1,a.prototype=Object.create(r.prototype),a.prototype.getGraphManager=function(){return this.graphManager},a.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},a.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},a.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},a.prototype.newGraphManager=function(){var l=new t(this);return this.graphManager=l,l},a.prototype.newGraph=function(l){return new h(null,this.graphManager,l)},a.prototype.newNode=function(l){return new e(this.graphManager,l)},a.prototype.newEdge=function(l){return new i(null,null,l)},a.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},a.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var l;return this.checkLayoutSuccess()?l=!1:l=this.layout(),s.ANIMATE==="during"?!1:(l&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,l)},a.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},a.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var l,f=this.graphManager.getAllEdges(),d=0;d<f.length;d++)l=f[d];for(var D,y=this.graphManager.getRoot().getNodes(),d=0;d<y.length;d++)D=y[d];this.update(this.graphManager.getRoot())}},a.prototype.update=function(l){if(l==null)this.update2();else if(l instanceof e){var f=l;if(f.getChild()!=null)for(var d=f.getChild().getNodes(),D=0;D<d.length;D++)update(d[D]);if(f.vGraphObject!=null){var y=f.vGraphObject;y.update(f)}}else if(l instanceof i){var L=l;if(L.vGraphObject!=null){var T=L.vGraphObject;T.update(L)}}else if(l instanceof h){var x=l;if(x.vGraphObject!=null){var A=x.vGraphObject;A.update(x)}}},a.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=s.QUALITY,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=s.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},a.prototype.transform=function(l){if(l==null)this.transform(new n(0,0));else{var f=new g,d=this.graphManager.getRoot().updateLeftTop();if(d!=null){f.setWorldOrgX(l.x),f.setWorldOrgY(l.y),f.setDeviceOrgX(d.x),f.setDeviceOrgY(d.y);for(var D=this.getAllNodes(),y,L=0;L<D.length;L++)y=D[L],y.transform(f)}}},a.prototype.positionNodesRandomly=function(l){if(l==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var f,d,D=l.getNodes(),y=0;y<D.length;y++)f=D[y],d=f.getChild(),d==null||d.getNodes().length==0?f.scatter():(this.positionNodesRandomly(d),f.updateBounds())},a.prototype.getFlatForest=function(){for(var l=[],f=!0,d=this.graphManager.getRoot().getNodes(),D=!0,y=0;y<d.length;y++)d[y].getChild()!=null&&(D=!1);if(!D)return l;var L=new Set,T=[],x=new Map,A=[];for(A=A.concat(d);A.length>0&&f;){for(T.push(A[0]);T.length>0&&f;){var F=T[0];T.splice(0,1),L.add(F);for(var o=F.getEdges(),y=0;y<o.length;y++){var u=o[y].getOtherEnd(F);if(x.get(F)!=u)if(!L.has(u))T.push(u),x.set(u,F);else{f=!1;break}}}if(!f)l=[];else{var p=[].concat(c(L));l.push(p);for(var y=0;y<p.length;y++){var E=p[y],m=A.indexOf(E);m>-1&&A.splice(m,1)}L=new Set,x=new Map}}return l},a.prototype.createDummyNodesForBendpoints=function(l){for(var f=[],d=l.source,D=this.graphManager.calcLowestCommonAncestor(l.source,l.target),y=0;y<l.bendpoints.length;y++){var L=this.newNode(null);L.setRect(new Point(0,0),new Dimension(1,1)),D.add(L);var T=this.newEdge(null);this.graphManager.add(T,d,L),f.add(L),d=L}var T=this.newEdge(null);return this.graphManager.add(T,d,l.target),this.edgeToDummyNodes.set(l,f),l.isInterGraph()?this.graphManager.remove(l):D.remove(l),f},a.prototype.createBendpointsFromDummyNodes=function(){var l=[];l=l.concat(this.graphManager.getAllEdges()),l=[].concat(c(this.edgeToDummyNodes.keys())).concat(l);for(var f=0;f<l.length;f++){var d=l[f];if(d.bendpoints.length>0){for(var D=this.edgeToDummyNodes.get(d),y=0;y<D.length;y++){var L=D[y],T=new n(L.getCenterX(),L.getCenterY()),x=d.bendpoints.get(y);x.x=T.x,x.y=T.y,L.getOwner().remove(L)}this.graphManager.add(d,d.source,d.target)}}},a.transform=function(l,f,d,D){if(d!=null&&D!=null){var y=f;if(l<=50){var L=f/d;y-=(f-L)/50*(50-l)}else{var T=f*D;y+=(T-f)/50*(l-50)}return y}else{var x,A;return l<=50?(x=9*f/500,A=f/10):(x=9*f/50,A=-8*f),x*l+A}},a.findCenterOfTree=function(l){var f=[];f=f.concat(l);var d=[],D=new Map,y=!1,L=null;(f.length==1||f.length==2)&&(y=!0,L=f[0]);for(var T=0;T<f.length;T++){var x=f[T],A=x.getNeighborsList().size;D.set(x,x.getNeighborsList().size),A==1&&d.push(x)}var F=[];for(F=F.concat(d);!y;){var o=[];o=o.concat(F),F=[];for(var T=0;T<f.length;T++){var x=f[T],u=f.indexOf(x);u>=0&&f.splice(u,1);var p=x.getNeighborsList();p.forEach(function(I){if(d.indexOf(I)<0){var w=D.get(I),S=w-1;S==1&&F.push(I),D.set(I,S)}})}d=d.concat(F),(f.length==1||f.length==2)&&(y=!0,L=f[0])}return L},a.prototype.setGraphManager=function(l){this.graphManager=l},P.exports=a},function(P,k,N){"use strict";function c(){}c.seed=1,c.x=0,c.nextDouble=function(){return c.x=Math.sin(c.seed++)*1e4,c.x-Math.floor(c.x)},P.exports=c},function(P,k,N){"use strict";var c=N(4);function s(t,e){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}s.prototype.getWorldOrgX=function(){return this.lworldOrgX},s.prototype.setWorldOrgX=function(t){this.lworldOrgX=t},s.prototype.getWorldOrgY=function(){return this.lworldOrgY},s.prototype.setWorldOrgY=function(t){this.lworldOrgY=t},s.prototype.getWorldExtX=function(){return this.lworldExtX},s.prototype.setWorldExtX=function(t){this.lworldExtX=t},s.prototype.getWorldExtY=function(){return this.lworldExtY},s.prototype.setWorldExtY=function(t){this.lworldExtY=t},s.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},s.prototype.setDeviceOrgX=function(t){this.ldeviceOrgX=t},s.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},s.prototype.setDeviceOrgY=function(t){this.ldeviceOrgY=t},s.prototype.getDeviceExtX=function(){return this.ldeviceExtX},s.prototype.setDeviceExtX=function(t){this.ldeviceExtX=t},s.prototype.getDeviceExtY=function(){return this.ldeviceExtY},s.prototype.setDeviceExtY=function(t){this.ldeviceExtY=t},s.prototype.transformX=function(t){var e=0,i=this.lworldExtX;return i!=0&&(e=this.ldeviceOrgX+(t-this.lworldOrgX)*this.ldeviceExtX/i),e},s.prototype.transformY=function(t){var e=0,i=this.lworldExtY;return i!=0&&(e=this.ldeviceOrgY+(t-this.lworldOrgY)*this.ldeviceExtY/i),e},s.prototype.inverseTransformX=function(t){var e=0,i=this.ldeviceExtX;return i!=0&&(e=this.lworldOrgX+(t-this.ldeviceOrgX)*this.lworldExtX/i),e},s.prototype.inverseTransformY=function(t){var e=0,i=this.ldeviceExtY;return i!=0&&(e=this.lworldOrgY+(t-this.ldeviceOrgY)*this.lworldExtY/i),e},s.prototype.inverseTransformPoint=function(t){var e=new c(this.inverseTransformX(t.x),this.inverseTransformY(t.y));return e},P.exports=s},function(P,k,N){"use strict";function c(r){if(Array.isArray(r)){for(var a=0,l=Array(r.length);a<r.length;a++)l[a]=r[a];return l}else return Array.from(r)}var s=N(15),t=N(7),e=N(0),i=N(8),h=N(9);function n(){s.call(this),this.useSmartIdealEdgeLengthCalculation=t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=t.DEFAULT_EDGE_LENGTH,this.springConstant=t.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=t.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=t.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=t.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=t.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*t.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=t.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=t.MAX_ITERATIONS}n.prototype=Object.create(s.prototype);for(var g in s)n[g]=s[g];n.prototype.initParameters=function(){s.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},n.prototype.calcIdealEdgeLengths=function(){for(var r,a,l,f,d,D,y=this.getGraphManager().getAllEdges(),L=0;L<y.length;L++)r=y[L],r.idealLength=this.idealEdgeLength,r.isInterGraph&&(l=r.getSource(),f=r.getTarget(),d=r.getSourceInLca().getEstimatedSize(),D=r.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(r.idealLength+=d+D-2*e.SIMPLE_NODE_SIZE),a=r.getLca().getInclusionTreeDepth(),r.idealLength+=t.DEFAULT_EDGE_LENGTH*t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(l.getInclusionTreeDepth()+f.getInclusionTreeDepth()-2*a))},n.prototype.initSpringEmbedder=function(){var r=this.getAllNodes().length;this.incremental?(r>t.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*t.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(r-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-t.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT_INCREMENTAL):(r>t.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(t.COOLING_ADAPTATION_FACTOR,1-(r-t.ADAPTATION_LOWER_NODE_LIMIT)/(t.ADAPTATION_UPPER_NODE_LIMIT-t.ADAPTATION_LOWER_NODE_LIMIT)*(1-t.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=t.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},n.prototype.calcSpringForces=function(){for(var r=this.getAllEdges(),a,l=0;l<r.length;l++)a=r[l],this.calcSpringForce(a,a.idealLength)},n.prototype.calcRepulsionForces=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l,f,d,D,y=this.getAllNodes(),L;if(this.useFRGridVariant)for(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&r&&this.updateGrid(),L=new Set,l=0;l<y.length;l++)d=y[l],this.calculateRepulsionForceOfANode(d,L,r,a),L.add(d);else for(l=0;l<y.length;l++)for(d=y[l],f=l+1;f<y.length;f++)D=y[f],d.getOwner()==D.getOwner()&&this.calcRepulsionForce(d,D)},n.prototype.calcGravitationalForces=function(){for(var r,a=this.getAllNodesToApplyGravitation(),l=0;l<a.length;l++)r=a[l],this.calcGravitationalForce(r)},n.prototype.moveNodes=function(){for(var r=this.getAllNodes(),a,l=0;l<r.length;l++)a=r[l],a.move()},n.prototype.calcSpringForce=function(r,a){var l=r.getSource(),f=r.getTarget(),d,D,y,L;if(this.uniformLeafNodeSizes&&l.getChild()==null&&f.getChild()==null)r.updateLengthSimple();else if(r.updateLength(),r.isOverlapingSourceAndTarget)return;d=r.getLength(),d!=0&&(D=this.springConstant*(d-a),y=D*(r.lengthX/d),L=D*(r.lengthY/d),l.springForceX+=y,l.springForceY+=L,f.springForceX-=y,f.springForceY-=L)},n.prototype.calcRepulsionForce=function(r,a){var l=r.getRect(),f=a.getRect(),d=new Array(2),D=new Array(4),y,L,T,x,A,F,o;if(l.intersects(f)){i.calcSeparationAmount(l,f,d,t.DEFAULT_EDGE_LENGTH/2),F=2*d[0],o=2*d[1];var u=r.noOfChildren*a.noOfChildren/(r.noOfChildren+a.noOfChildren);r.repulsionForceX-=u*F,r.repulsionForceY-=u*o,a.repulsionForceX+=u*F,a.repulsionForceY+=u*o}else this.uniformLeafNodeSizes&&r.getChild()==null&&a.getChild()==null?(y=f.getCenterX()-l.getCenterX(),L=f.getCenterY()-l.getCenterY()):(i.getIntersection(l,f,D),y=D[2]-D[0],L=D[3]-D[1]),Math.abs(y)<t.MIN_REPULSION_DIST&&(y=h.sign(y)*t.MIN_REPULSION_DIST),Math.abs(L)<t.MIN_REPULSION_DIST&&(L=h.sign(L)*t.MIN_REPULSION_DIST),T=y*y+L*L,x=Math.sqrt(T),A=this.repulsionConstant*r.noOfChildren*a.noOfChildren/T,F=A*y/x,o=A*L/x,r.repulsionForceX-=F,r.repulsionForceY-=o,a.repulsionForceX+=F,a.repulsionForceY+=o},n.prototype.calcGravitationalForce=function(r){var a,l,f,d,D,y,L,T;a=r.getOwner(),l=(a.getRight()+a.getLeft())/2,f=(a.getTop()+a.getBottom())/2,d=r.getCenterX()-l,D=r.getCenterY()-f,y=Math.abs(d)+r.getWidth()/2,L=Math.abs(D)+r.getHeight()/2,r.getOwner()==this.graphManager.getRoot()?(T=a.getEstimatedSize()*this.gravityRangeFactor,(y>T||L>T)&&(r.gravitationForceX=-this.gravityConstant*d,r.gravitationForceY=-this.gravityConstant*D)):(T=a.getEstimatedSize()*this.compoundGravityRangeFactor,(y>T||L>T)&&(r.gravitationForceX=-this.gravityConstant*d*this.compoundGravityConstant,r.gravitationForceY=-this.gravityConstant*D*this.compoundGravityConstant))},n.prototype.isConverged=function(){var r,a=!1;return this.totalIterations>this.maxIterations/3&&(a=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),r=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,r||a},n.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},n.prototype.calcNoOfChildrenForAllNodes=function(){for(var r,a=this.graphManager.getAllNodes(),l=0;l<a.length;l++)r=a[l],r.noOfChildren=r.getNoOfChildren()},n.prototype.calcGrid=function(r){var a=0,l=0;a=parseInt(Math.ceil((r.getRight()-r.getLeft())/this.repulsionRange)),l=parseInt(Math.ceil((r.getBottom()-r.getTop())/this.repulsionRange));for(var f=new Array(a),d=0;d<a;d++)f[d]=new Array(l);for(var d=0;d<a;d++)for(var D=0;D<l;D++)f[d][D]=new Array;return f},n.prototype.addNodeToGrid=function(r,a,l){var f=0,d=0,D=0,y=0;f=parseInt(Math.floor((r.getRect().x-a)/this.repulsionRange)),d=parseInt(Math.floor((r.getRect().width+r.getRect().x-a)/this.repulsionRange)),D=parseInt(Math.floor((r.getRect().y-l)/this.repulsionRange)),y=parseInt(Math.floor((r.getRect().height+r.getRect().y-l)/this.repulsionRange));for(var L=f;L<=d;L++)for(var T=D;T<=y;T++)this.grid[L][T].push(r),r.setGridCoordinates(f,d,D,y)},n.prototype.updateGrid=function(){var r,a,l=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),r=0;r<l.length;r++)a=l[r],this.addNodeToGrid(a,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},n.prototype.calculateRepulsionForceOfANode=function(r,a,l,f){if(this.totalIterations%t.GRID_CALCULATION_CHECK_PERIOD==1&&l||f){var d=new Set;r.surrounding=new Array;for(var D,y=this.grid,L=r.startX-1;L<r.finishX+2;L++)for(var T=r.startY-1;T<r.finishY+2;T++)if(!(L<0||T<0||L>=y.length||T>=y[0].length)){for(var x=0;x<y[L][T].length;x++)if(D=y[L][T][x],!(r.getOwner()!=D.getOwner()||r==D)&&!a.has(D)&&!d.has(D)){var A=Math.abs(r.getCenterX()-D.getCenterX())-(r.getWidth()/2+D.getWidth()/2),F=Math.abs(r.getCenterY()-D.getCenterY())-(r.getHeight()/2+D.getHeight()/2);A<=this.repulsionRange&&F<=this.repulsionRange&&d.add(D)}}r.surrounding=[].concat(c(d))}for(L=0;L<r.surrounding.length;L++)this.calcRepulsionForce(r,r.surrounding[L])},n.prototype.calcRepulsionRange=function(){return 0},P.exports=n},function(P,k,N){"use strict";var c=N(1),s=N(7);function t(i,h,n){c.call(this,i,h,n),this.idealLength=s.DEFAULT_EDGE_LENGTH}t.prototype=Object.create(c.prototype);for(var e in c)t[e]=c[e];P.exports=t},function(P,k,N){"use strict";var c=N(3);function s(e,i,h,n){c.call(this,e,i,h,n),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}s.prototype=Object.create(c.prototype);for(var t in c)s[t]=c[t];s.prototype.setGridCoordinates=function(e,i,h,n){this.startX=e,this.finishX=i,this.startY=h,this.finishY=n},P.exports=s},function(P,k,N){"use strict";function c(s,t){this.width=0,this.height=0,s!==null&&t!==null&&(this.height=t,this.width=s)}c.prototype.getWidth=function(){return this.width},c.prototype.setWidth=function(s){this.width=s},c.prototype.getHeight=function(){return this.height},c.prototype.setHeight=function(s){this.height=s},P.exports=c},function(P,k,N){"use strict";var c=N(14);function s(){this.map={},this.keys=[]}s.prototype.put=function(t,e){var i=c.createID(t);this.contains(i)||(this.map[i]=e,this.keys.push(t))},s.prototype.contains=function(t){var e=c.createID(t);return this.map[t]!=null},s.prototype.get=function(t){var e=c.createID(t);return this.map[e]},s.prototype.keySet=function(){return this.keys},P.exports=s},function(P,k,N){"use strict";var c=N(14);function s(){this.set={}}s.prototype.add=function(t){var e=c.createID(t);this.contains(e)||(this.set[e]=t)},s.prototype.remove=function(t){delete this.set[c.createID(t)]},s.prototype.clear=function(){this.set={}},s.prototype.contains=function(t){return this.set[c.createID(t)]==t},s.prototype.isEmpty=function(){return this.size()===0},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAllTo=function(t){for(var e=Object.keys(this.set),i=e.length,h=0;h<i;h++)t.push(this.set[e[h]])},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAll=function(t){for(var e=t.length,i=0;i<e;i++){var h=t[i];this.add(h)}},P.exports=s},function(P,k,N){"use strict";var c=function(){function i(h,n){for(var g=0;g<n.length;g++){var r=n[g];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(h,r.key,r)}}return function(h,n,g){return n&&i(h.prototype,n),g&&i(h,g),h}}();function s(i,h){if(!(i instanceof h))throw new TypeError("Cannot call a class as a function")}var t=N(11),e=function(){function i(h,n){s(this,i),(n!==null||n!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var g=void 0;h instanceof t?g=h.size():g=h.length,this._quicksort(h,0,g-1)}return c(i,[{key:"_quicksort",value:function(n,g,r){if(g<r){var a=this._partition(n,g,r);this._quicksort(n,g,a),this._quicksort(n,a+1,r)}}},{key:"_partition",value:function(n,g,r){for(var a=this._get(n,g),l=g,f=r;;){for(;this.compareFunction(a,this._get(n,f));)f--;for(;this.compareFunction(this._get(n,l),a);)l++;if(l<f)this._swap(n,l,f),l++,f--;else return f}}},{key:"_get",value:function(n,g){return n instanceof t?n.get_object_at(g):n[g]}},{key:"_set",value:function(n,g,r){n instanceof t?n.set_object_at(g,r):n[g]=r}},{key:"_swap",value:function(n,g,r){var a=this._get(n,g);this._set(n,g,this._get(n,r)),this._set(n,r,a)}},{key:"_defaultCompareFunction",value:function(n,g){return g>n}}]),i}();P.exports=e},function(P,k,N){"use strict";var c=function(){function e(i,h){for(var n=0;n<h.length;n++){var g=h[n];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(i,g.key,g)}}return function(i,h,n){return h&&e(i.prototype,h),n&&e(i,n),i}}();function s(e,i){if(!(e instanceof i))throw new TypeError("Cannot call a class as a function")}var t=function(){function e(i,h){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;s(this,e),this.sequence1=i,this.sequence2=h,this.match_score=n,this.mismatch_penalty=g,this.gap_penalty=r,this.iMax=i.length+1,this.jMax=h.length+1,this.grid=new Array(this.iMax);for(var a=0;a<this.iMax;a++){this.grid[a]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.grid[a][l]=0}this.tracebackGrid=new Array(this.iMax);for(var f=0;f<this.iMax;f++){this.tracebackGrid[f]=new Array(this.jMax);for(var d=0;d<this.jMax;d++)this.tracebackGrid[f][d]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return c(e,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var h=1;h<this.jMax;h++)this.grid[0][h]=this.grid[0][h-1]+this.gap_penalty,this.tracebackGrid[0][h]=[!1,!1,!0];for(var n=1;n<this.iMax;n++)this.grid[n][0]=this.grid[n-1][0]+this.gap_penalty,this.tracebackGrid[n][0]=[!1,!0,!1];for(var g=1;g<this.iMax;g++)for(var r=1;r<this.jMax;r++){var a=void 0;this.sequence1[g-1]===this.sequence2[r-1]?a=this.grid[g-1][r-1]+this.match_score:a=this.grid[g-1][r-1]+this.mismatch_penalty;var l=this.grid[g-1][r]+this.gap_penalty,f=this.grid[g][r-1]+this.gap_penalty,d=[a,l,f],D=this.arrayAllMaxIndexes(d);this.grid[g][r]=d[D[0]],this.tracebackGrid[g][r]=[D.includes(0),D.includes(1),D.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var h=[];for(h.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});h[0];){var n=h[0],g=this.tracebackGrid[n.pos[0]][n.pos[1]];g[0]&&h.push({pos:[n.pos[0]-1,n.pos[1]-1],seq1:this.sequence1[n.pos[0]-1]+n.seq1,seq2:this.sequence2[n.pos[1]-1]+n.seq2}),g[1]&&h.push({pos:[n.pos[0]-1,n.pos[1]],seq1:this.sequence1[n.pos[0]-1]+n.seq1,seq2:"-"+n.seq2}),g[2]&&h.push({pos:[n.pos[0],n.pos[1]-1],seq1:"-"+n.seq1,seq2:this.sequence2[n.pos[1]-1]+n.seq2}),n.pos[0]===0&&n.pos[1]===0&&this.alignments.push({sequence1:n.seq1,sequence2:n.seq2}),h.shift()}return this.alignments}},{key:"getAllIndexes",value:function(h,n){for(var g=[],r=-1;(r=h.indexOf(n,r+1))!==-1;)g.push(r);return g}},{key:"arrayAllMaxIndexes",value:function(h){return this.getAllIndexes(h,Math.max.apply(null,h))}}]),e}();P.exports=t},function(P,k,N){"use strict";var c=function(){};c.FDLayout=N(18),c.FDLayoutConstants=N(7),c.FDLayoutEdge=N(19),c.FDLayoutNode=N(20),c.DimensionD=N(21),c.HashMap=N(22),c.HashSet=N(23),c.IGeometry=N(8),c.IMath=N(9),c.Integer=N(10),c.Point=N(12),c.PointD=N(4),c.RandomSeed=N(16),c.RectangleD=N(13),c.Transform=N(17),c.UniqueIDGeneretor=N(14),c.Quicksort=N(24),c.LinkedList=N(11),c.LGraphObject=N(2),c.LGraph=N(5),c.LEdge=N(1),c.LGraphManager=N(6),c.LNode=N(3),c.Layout=N(15),c.LayoutConstants=N(0),c.NeedlemanWunsch=N(25),P.exports=c},function(P,k,N){"use strict";function c(){this.listeners=[]}var s=c.prototype;s.addListener=function(t,e){this.listeners.push({event:t,callback:e})},s.removeListener=function(t,e){for(var i=this.listeners.length;i>=0;i--){var h=this.listeners[i];h.event===t&&h.callback===e&&this.listeners.splice(i,1)}},s.emit=function(t,e){for(var i=0;i<this.listeners.length;i++){var h=this.listeners[i];t===h.event&&h.callback(e)}},P.exports=c}])})},55061:function(ut,P,k){"use strict";var Z;k.d(P,{diagram:function(){return K}});var N=k(70919),c=k(50854),s=k(44133),t=k(29134),e=k(73058),i=k(67249),h=k(69471),n=k(65440),g=k(12724),r=k(5959),a=function(){var O=(0,t.eW)(function(ft,Y,b,W){for(b=b||{},W=ft.length;W--;b[ft[W]]=Y);return b},"o"),R=[1,4],v=[1,13],M=[1,12],G=[1,15],C=[1,16],H=[1,20],Q=[1,19],et=[6,7,8],it=[1,26],nt=[1,24],lt=[1,25],q=[6,7,11],pt=[1,6,13,15,16,19,22],at=[1,33],ct=[1,34],yt=[1,6,7,11,13,15,16,19,22],At={trace:(0,t.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:(0,t.eW)(function(Y,b,W,V,z,_,Lt){var j=_.length-1;switch(z){case 6:case 7:return V;case 8:V.getLogger().trace("Stop NL ");break;case 9:V.getLogger().trace("Stop EOF ");break;case 11:V.getLogger().trace("Stop NL2 ");break;case 12:V.getLogger().trace("Stop EOF2 ");break;case 15:V.getLogger().info("Node: ",_[j].id),V.addNode(_[j-1].length,_[j].id,_[j].descr,_[j].type);break;case 16:V.getLogger().trace("Icon: ",_[j]),V.decorateNode({icon:_[j]});break;case 17:case 21:V.decorateNode({class:_[j]});break;case 18:V.getLogger().trace("SPACELIST");break;case 19:V.getLogger().trace("Node: ",_[j].id),V.addNode(0,_[j].id,_[j].descr,_[j].type);break;case 20:V.decorateNode({icon:_[j]});break;case 25:V.getLogger().trace("node found ..",_[j-2]),this.$={id:_[j-1],descr:_[j-1],type:V.getType(_[j-2],_[j])};break;case 26:this.$={id:_[j],descr:_[j],type:V.nodeType.DEFAULT};break;case 27:V.getLogger().trace("node found ..",_[j-3]),this.$={id:_[j-3],descr:_[j-1],type:V.getType(_[j-2],_[j])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:R},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:R},{6:v,7:[1,10],9:9,12:11,13:M,14:14,15:G,16:C,17:17,18:18,19:H,22:Q},O(et,[2,3]),{1:[2,2]},O(et,[2,4]),O(et,[2,5]),{1:[2,6],6:v,12:21,13:M,14:14,15:G,16:C,17:17,18:18,19:H,22:Q},{6:v,9:22,12:11,13:M,14:14,15:G,16:C,17:17,18:18,19:H,22:Q},{6:it,7:nt,10:23,11:lt},O(q,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:H,22:Q}),O(q,[2,18]),O(q,[2,19]),O(q,[2,20]),O(q,[2,21]),O(q,[2,23]),O(q,[2,24]),O(q,[2,26],{19:[1,30]}),{20:[1,31]},{6:it,7:nt,10:32,11:lt},{1:[2,7],6:v,12:21,13:M,14:14,15:G,16:C,17:17,18:18,19:H,22:Q},O(pt,[2,14],{7:at,11:ct}),O(yt,[2,8]),O(yt,[2,9]),O(yt,[2,10]),O(q,[2,15]),O(q,[2,16]),O(q,[2,17]),{20:[1,35]},{21:[1,36]},O(pt,[2,13],{7:at,11:ct}),O(yt,[2,11]),O(yt,[2,12]),{21:[1,37]},O(q,[2,25]),O(q,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:(0,t.eW)(function(Y,b){if(b.recoverable)this.trace(Y);else{var W=new Error(Y);throw W.hash=b,W}},"parseError"),parse:(0,t.eW)(function(Y){var b=this,W=[0],V=[],z=[null],_=[],Lt=this.table,j="",Dt=0,xt=0,St=0,Ut=2,Gt=1,_t=_.slice.call(arguments,1),rt=Object.create(this.lexer),dt={yy:{}};for(var It in this.yy)Object.prototype.hasOwnProperty.call(this.yy,It)&&(dt.yy[It]=this.yy[It]);rt.setInput(Y,dt.yy),dt.yy.lexer=rt,dt.yy.parser=this,typeof rt.yylloc=="undefined"&&(rt.yylloc={});var Ct=rt.yylloc;_.push(Ct);var Yt=rt.options&&rt.options.ranges;typeof dt.yy.parseError=="function"?this.parseError=dt.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function kt(ot){W.length=W.length-2*ot,z.length=z.length-ot,_.length=_.length-ot}(0,t.eW)(kt,"popStack");function Ft(){var ot;return ot=V.pop()||rt.lex()||Gt,typeof ot!="number"&&(ot instanceof Array&&(V=ot,ot=V.pop()),ot=b.symbols_[ot]||ot),ot}(0,t.eW)(Ft,"lex");for(var st,Rt,vt,ht,Xt,Mt,Et={},Nt,gt,bt,mt;;){if(vt=W[W.length-1],this.defaultActions[vt]?ht=this.defaultActions[vt]:((st===null||typeof st=="undefined")&&(st=Ft()),ht=Lt[vt]&&Lt[vt][st]),typeof ht=="undefined"||!ht.length||!ht[0]){var wt="";mt=[];for(Nt in Lt[vt])this.terminals_[Nt]&&Nt>Ut&&mt.push("'"+this.terminals_[Nt]+"'");rt.showPosition?wt="Parse error on line "+(Dt+1)+`:
`+rt.showPosition()+`
Expecting `+mt.join(", ")+", got '"+(this.terminals_[st]||st)+"'":wt="Parse error on line "+(Dt+1)+": Unexpected "+(st==Gt?"end of input":"'"+(this.terminals_[st]||st)+"'"),this.parseError(wt,{text:rt.match,token:this.terminals_[st]||st,line:rt.yylineno,loc:Ct,expected:mt})}if(ht[0]instanceof Array&&ht.length>1)throw new Error("Parse Error: multiple actions possible at state: "+vt+", token: "+st);switch(ht[0]){case 1:W.push(st),z.push(rt.yytext),_.push(rt.yylloc),W.push(ht[1]),st=null,Rt?(st=Rt,Rt=null):(xt=rt.yyleng,j=rt.yytext,Dt=rt.yylineno,Ct=rt.yylloc,St>0&&St--);break;case 2:if(gt=this.productions_[ht[1]][1],Et.$=z[z.length-gt],Et._$={first_line:_[_.length-(gt||1)].first_line,last_line:_[_.length-1].last_line,first_column:_[_.length-(gt||1)].first_column,last_column:_[_.length-1].last_column},Yt&&(Et._$.range=[_[_.length-(gt||1)].range[0],_[_.length-1].range[1]]),Mt=this.performAction.apply(Et,[j,xt,Dt,dt.yy,ht[1],z,_].concat(_t)),typeof Mt!="undefined")return Mt;gt&&(W=W.slice(0,-1*gt*2),z=z.slice(0,-1*gt),_=_.slice(0,-1*gt)),W.push(this.productions_[ht[1]][0]),z.push(Et.$),_.push(Et._$),bt=Lt[W[W.length-2]][W[W.length-1]],W.push(bt);break;case 3:return!0}}return!0},"parse")},Pt=function(){var ft={EOF:1,parseError:(0,t.eW)(function(b,W){if(this.yy.parser)this.yy.parser.parseError(b,W);else throw new Error(b)},"parseError"),setInput:(0,t.eW)(function(Y,b){return this.yy=b||this.yy||{},this._input=Y,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,t.eW)(function(){var Y=this._input[0];this.yytext+=Y,this.yyleng++,this.offset++,this.match+=Y,this.matched+=Y;var b=Y.match(/(?:\r\n?|\n).*/g);return b?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),Y},"input"),unput:(0,t.eW)(function(Y){var b=Y.length,W=Y.split(/(?:\r\n?|\n)/g);this._input=Y+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-b),this.offset-=b;var V=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),W.length-1&&(this.yylineno-=W.length-1);var z=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:W?(W.length===V.length?this.yylloc.first_column:0)+V[V.length-W.length].length-W[0].length:this.yylloc.first_column-b},this.options.ranges&&(this.yylloc.range=[z[0],z[0]+this.yyleng-b]),this.yyleng=this.yytext.length,this},"unput"),more:(0,t.eW)(function(){return this._more=!0,this},"more"),reject:(0,t.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,t.eW)(function(Y){this.unput(this.match.slice(Y))},"less"),pastInput:(0,t.eW)(function(){var Y=this.matched.substr(0,this.matched.length-this.match.length);return(Y.length>20?"...":"")+Y.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,t.eW)(function(){var Y=this.match;return Y.length<20&&(Y+=this._input.substr(0,20-Y.length)),(Y.substr(0,20)+(Y.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,t.eW)(function(){var Y=this.pastInput(),b=new Array(Y.length+1).join("-");return Y+this.upcomingInput()+`
`+b+"^"},"showPosition"),test_match:(0,t.eW)(function(Y,b){var W,V,z;if(this.options.backtrack_lexer&&(z={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(z.yylloc.range=this.yylloc.range.slice(0))),V=Y[0].match(/(?:\r\n?|\n).*/g),V&&(this.yylineno+=V.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:V?V[V.length-1].length-V[V.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+Y[0].length},this.yytext+=Y[0],this.match+=Y[0],this.matches=Y,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(Y[0].length),this.matched+=Y[0],W=this.performAction.call(this,this.yy,this,b,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),W)return W;if(this._backtrack){for(var _ in z)this[_]=z[_];return!1}return!1},"test_match"),next:(0,t.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var Y,b,W,V;this._more||(this.yytext="",this.match="");for(var z=this._currentRules(),_=0;_<z.length;_++)if(W=this._input.match(this.rules[z[_]]),W&&(!b||W[0].length>b[0].length)){if(b=W,V=_,this.options.backtrack_lexer){if(Y=this.test_match(W,z[_]),Y!==!1)return Y;if(this._backtrack){b=!1;continue}else return!1}else if(!this.options.flex)break}return b?(Y=this.test_match(b,z[V]),Y!==!1?Y:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,t.eW)(function(){var b=this.next();return b||this.lex()},"lex"),begin:(0,t.eW)(function(b){this.conditionStack.push(b)},"begin"),popState:(0,t.eW)(function(){var b=this.conditionStack.length-1;return b>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,t.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,t.eW)(function(b){return b=this.conditionStack.length-1-Math.abs(b||0),b>=0?this.conditionStack[b]:"INITIAL"},"topState"),pushState:(0,t.eW)(function(b){this.begin(b)},"pushState"),stateStackSize:(0,t.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,t.eW)(function(b,W,V,z){var _=z;switch(V){case 0:return b.getLogger().trace("Found comment",W.yytext),6;break;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;break;case 4:this.popState();break;case 5:b.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return b.getLogger().trace("SPACELINE"),6;break;case 7:return 7;case 8:return 15;case 9:b.getLogger().trace("end icon"),this.popState();break;case 10:return b.getLogger().trace("Exploding node"),this.begin("NODE"),19;break;case 11:return b.getLogger().trace("Cloud"),this.begin("NODE"),19;break;case 12:return b.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;break;case 13:return b.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;break;case 14:return this.begin("NODE"),19;break;case 15:return this.begin("NODE"),19;break;case 16:return this.begin("NODE"),19;break;case 17:return this.begin("NODE"),19;break;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:b.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return b.getLogger().trace("description:",W.yytext),"NODE_DESCR";break;case 26:this.popState();break;case 27:return this.popState(),b.getLogger().trace("node end ))"),"NODE_DEND";break;case 28:return this.popState(),b.getLogger().trace("node end )"),"NODE_DEND";break;case 29:return this.popState(),b.getLogger().trace("node end ...",W.yytext),"NODE_DEND";break;case 30:return this.popState(),b.getLogger().trace("node end (("),"NODE_DEND";break;case 31:return this.popState(),b.getLogger().trace("node end (-"),"NODE_DEND";break;case 32:return this.popState(),b.getLogger().trace("node end (-"),"NODE_DEND";break;case 33:return this.popState(),b.getLogger().trace("node end (("),"NODE_DEND";break;case 34:return this.popState(),b.getLogger().trace("node end (("),"NODE_DEND";break;case 35:return b.getLogger().trace("Long description:",W.yytext),20;break;case 36:return b.getLogger().trace("Long description:",W.yytext),20;break}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return ft}();At.lexer=Pt;function Tt(){this.yy={}}return(0,t.eW)(Tt,"Parser"),Tt.prototype=At,At.Parser=Tt,new Tt}();a.parser=a;var l=a,f={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},d=(Z=class{constructor(){this.nodes=[],this.count=0,this.elements={},this.getLogger=this.getLogger.bind(this),this.nodeType=f,this.clear(),this.getType=this.getType.bind(this),this.getMindmap=this.getMindmap.bind(this),this.getElementById=this.getElementById.bind(this),this.getParent=this.getParent.bind(this),this.getMindmap=this.getMindmap.bind(this),this.addNode=this.addNode.bind(this),this.decorateNode=this.decorateNode.bind(this)}clear(){this.nodes=[],this.count=0,this.elements={}}getParent(R){for(let v=this.nodes.length-1;v>=0;v--)if(this.nodes[v].level<R)return this.nodes[v];return null}getMindmap(){return this.nodes.length>0?this.nodes[0]:null}addNode(R,v,M,G){var it,nt,lt,q;t.cM.info("addNode",R,v,M,G);const C=(0,t.nV)();let H=(nt=(it=C.mindmap)==null?void 0:it.padding)!=null?nt:t.vZ.mindmap.padding;switch(G){case this.nodeType.ROUNDED_RECT:case this.nodeType.RECT:case this.nodeType.HEXAGON:H*=2;break}const Q={id:this.count++,nodeId:(0,t.oO)(v,C),level:R,descr:(0,t.oO)(M,C),type:G,children:[],width:(q=(lt=C.mindmap)==null?void 0:lt.maxNodeWidth)!=null?q:t.vZ.mindmap.maxNodeWidth,padding:H},et=this.getParent(R);if(et)et.children.push(Q),this.nodes.push(Q);else if(this.nodes.length===0)this.nodes.push(Q);else throw new Error(`There can be only one root. No parent could be found for ("${Q.descr}")`)}getType(R,v){switch(t.cM.debug("In get type",R,v),R){case"[":return this.nodeType.RECT;case"(":return v===")"?this.nodeType.ROUNDED_RECT:this.nodeType.CLOUD;case"((":return this.nodeType.CIRCLE;case")":return this.nodeType.CLOUD;case"))":return this.nodeType.BANG;case"{{":return this.nodeType.HEXAGON;default:return this.nodeType.DEFAULT}}setElementForId(R,v){this.elements[R]=v}getElementById(R){return this.elements[R]}decorateNode(R){if(!R)return;const v=(0,t.nV)(),M=this.nodes[this.nodes.length-1];R.icon&&(M.icon=(0,t.oO)(R.icon,v)),R.class&&(M.class=(0,t.oO)(R.class,v))}type2Str(R){switch(R){case this.nodeType.DEFAULT:return"no-border";case this.nodeType.RECT:return"rect";case this.nodeType.ROUNDED_RECT:return"rounded-rect";case this.nodeType.CIRCLE:return"circle";case this.nodeType.CLOUD:return"cloud";case this.nodeType.BANG:return"bang";case this.nodeType.HEXAGON:return"hexgon";default:return"no-border"}}getLogger(){return t.cM}},(0,t.eW)(Z,"MindmapDB"),Z),D=12,y=(0,t.eW)(function(O,R,v,M){R.append("path").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("d",`M0 ${v.height-5} v${-v.height+2*5} q0,-5 5,-5 h${v.width-2*5} q5,0 5,5 v${v.height-5} H0 Z`),R.append("line").attr("class","node-line-"+M).attr("x1",0).attr("y1",v.height).attr("x2",v.width).attr("y2",v.height)},"defaultBkg"),L=(0,t.eW)(function(O,R,v){R.append("rect").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("height",v.height).attr("width",v.width)},"rectBkg"),T=(0,t.eW)(function(O,R,v){const M=v.width,G=v.height,C=.15*M,H=.25*M,Q=.35*M,et=.2*M;R.append("path").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("d",`M0 0 a${C},${C} 0 0,1 ${M*.25},${-1*M*.1}
      a${Q},${Q} 1 0,1 ${M*.4},${-1*M*.1}
      a${H},${H} 1 0,1 ${M*.35},${1*M*.2}

      a${C},${C} 1 0,1 ${M*.15},${1*G*.35}
      a${et},${et} 1 0,1 ${-1*M*.15},${1*G*.65}

      a${H},${C} 1 0,1 ${-1*M*.25},${M*.15}
      a${Q},${Q} 1 0,1 ${-1*M*.5},0
      a${C},${C} 1 0,1 ${-1*M*.25},${-1*M*.15}

      a${C},${C} 1 0,1 ${-1*M*.1},${-1*G*.35}
      a${et},${et} 1 0,1 ${M*.1},${-1*G*.65}

    H0 V0 Z`)},"cloudBkg"),x=(0,t.eW)(function(O,R,v){const M=v.width,G=v.height,C=.15*M;R.append("path").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("d",`M0 0 a${C},${C} 1 0,0 ${M*.25},${-1*G*.1}
      a${C},${C} 1 0,0 ${M*.25},0
      a${C},${C} 1 0,0 ${M*.25},0
      a${C},${C} 1 0,0 ${M*.25},${1*G*.1}

      a${C},${C} 1 0,0 ${M*.15},${1*G*.33}
      a${C*.8},${C*.8} 1 0,0 0,${1*G*.34}
      a${C},${C} 1 0,0 ${-1*M*.15},${1*G*.33}

      a${C},${C} 1 0,0 ${-1*M*.25},${G*.15}
      a${C},${C} 1 0,0 ${-1*M*.25},0
      a${C},${C} 1 0,0 ${-1*M*.25},0
      a${C},${C} 1 0,0 ${-1*M*.25},${-1*G*.15}

      a${C},${C} 1 0,0 ${-1*M*.1},${-1*G*.33}
      a${C*.8},${C*.8} 1 0,0 0,${-1*G*.34}
      a${C},${C} 1 0,0 ${M*.1},${-1*G*.33}

    H0 V0 Z`)},"bangBkg"),A=(0,t.eW)(function(O,R,v){R.append("circle").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("r",v.width/2)},"circleBkg");function F(O,R,v,M,G){return O.insert("polygon",":first-child").attr("points",M.map(function(C){return C.x+","+C.y}).join(" ")).attr("transform","translate("+(G.width-R)/2+", "+v+")")}(0,t.eW)(F,"insertPolygonShape");var o=(0,t.eW)(function(O,R,v){const M=v.height,C=M/4,H=v.width-v.padding+2*C,Q=[{x:C,y:0},{x:H-C,y:0},{x:H,y:-M/2},{x:H-C,y:-M},{x:C,y:-M},{x:0,y:-M/2}];F(R,H,M,Q,v)},"hexagonBkg"),u=(0,t.eW)(function(O,R,v){R.append("rect").attr("id","node-"+v.id).attr("class","node-bkg node-"+O.type2Str(v.type)).attr("height",v.height).attr("rx",v.padding).attr("ry",v.padding).attr("width",v.width)},"roundedRectBkg"),p=(0,t.eW)(function(O,R,v,M,G){return Ot(this,null,function*(){const C=G.htmlLabels,H=M%(D-1),Q=R.append("g");v.section=H;let et="section-"+H;H<0&&(et+=" section-root"),Q.attr("class",(v.class?v.class+" ":"")+"mindmap-node "+et);const it=Q.append("g"),nt=Q.append("g"),lt=v.descr.replace(/(<br\/*>)/g,`
`);yield(0,N.rw)(nt,lt,{useHtmlLabels:C,width:v.width,classes:"mindmap-node-label"},G),C||nt.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const q=nt.node().getBBox(),[pt]=(0,c.VG)(G.fontSize);if(v.height=q.height+pt*1.1*.5+v.padding,v.width=q.width+2*v.padding,v.icon)if(v.type===O.nodeType.CIRCLE)v.height+=50,v.width+=50,Q.append("foreignObject").attr("height","50px").attr("width",v.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+H+" "+v.icon),nt.attr("transform","translate("+v.width/2+", "+(v.height/2-1.5*v.padding)+")");else{v.width+=50;const at=v.height;v.height=Math.max(at,60);const ct=Math.abs(v.height-at);Q.append("foreignObject").attr("width","60px").attr("height",v.height).attr("style","text-align: center;margin-top:"+ct/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+H+" "+v.icon),nt.attr("transform","translate("+(25+v.width/2)+", "+(ct/2+v.padding/2)+")")}else if(C){const at=(v.width-q.width)/2,ct=(v.height-q.height)/2;nt.attr("transform","translate("+at+", "+ct+")")}else{const at=v.width/2,ct=v.padding/2;nt.attr("transform","translate("+at+", "+ct+")")}switch(v.type){case O.nodeType.DEFAULT:y(O,it,v,H);break;case O.nodeType.ROUNDED_RECT:u(O,it,v,H);break;case O.nodeType.RECT:L(O,it,v,H);break;case O.nodeType.CIRCLE:it.attr("transform","translate("+v.width/2+", "+ +v.height/2+")"),A(O,it,v,H);break;case O.nodeType.CLOUD:T(O,it,v,H);break;case O.nodeType.BANG:x(O,it,v,H);break;case O.nodeType.HEXAGON:o(O,it,v,H);break}return O.setElementForId(v.id,Q),v.height})},"drawNode"),E=(0,t.eW)(function(O,R){const v=O.getElementById(R.id),M=R.x||0,G=R.y||0;v.attr("transform","translate("+M+","+G+")")},"positionNode");e.Z.use(i);function m(O,R,v,M,G){return Ot(this,null,function*(){yield p(O,R,v,M,G),v.children&&(yield Promise.all(v.children.map((C,H)=>m(O,R,C,M<0?H:M,G))))})}(0,t.eW)(m,"drawNodes");function I(O,R){R.edges().map((v,M)=>{const G=v.data();if(v[0]._private.bodyBounds){const C=v[0]._private.rscratch;t.cM.trace("Edge: ",M,G),O.insert("path").attr("d",`M ${C.startX},${C.startY} L ${C.midX},${C.midY} L${C.endX},${C.endY} `).attr("class","edge section-edge-"+G.section+" edge-depth-"+G.depth)}})}(0,t.eW)(I,"drawEdges");function w(O,R,v,M){R.add({group:"nodes",data:{id:O.id.toString(),labelText:O.descr,height:O.height,width:O.width,level:M,nodeId:O.id,padding:O.padding,type:O.type},position:{x:O.x,y:O.y}}),O.children&&O.children.forEach(G=>{w(G,R,v,M+1),R.add({group:"edges",data:{id:`${O.id}_${G.id}`,source:O.id,target:G.id,depth:M,section:G.section}})})}(0,t.eW)(w,"addNodes");function S(O,R){return new Promise(v=>{const M=(0,h.Ys)("body").append("div").attr("id","cy").attr("style","display:none"),G=(0,e.Z)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});M.remove(),w(O,G,R,0),G.nodes().forEach(function(C){C.layoutDimensions=()=>{const H=C.data();return{w:H.width,h:H.height}}}),G.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),G.ready(C=>{t.cM.info("Ready",C),v(G)})})}(0,t.eW)(S,"layoutMindmap");function X(O,R){R.nodes().map((v,M)=>{const G=v.data();G.x=v.position().x,G.y=v.position().y,E(O,G);const C=O.getElementById(G.nodeId);t.cM.info("id:",M,"Position: (",v.position().x,", ",v.position().y,")",G),C.attr("transform",`translate(${v.position().x-G.width/2}, ${v.position().y-G.height/2})`),C.attr("attr",`apa-${M})`)})}(0,t.eW)(X,"positionNodes");var $=(0,t.eW)((O,R,v,M)=>Ot(this,null,function*(){var lt,q,pt,at;t.cM.debug(`Rendering mindmap diagram
`+O);const G=M.db,C=G.getMindmap();if(!C)return;const H=(0,t.nV)();H.htmlLabels=!1;const Q=(0,s.P)(R),et=Q.append("g");et.attr("class","mindmap-edges");const it=Q.append("g");it.attr("class","mindmap-nodes"),yield m(G,it,C,-1,H);const nt=yield S(C,H);I(et,nt),X(G,nt),(0,t.j7)(void 0,Q,(q=(lt=H.mindmap)==null?void 0:lt.padding)!=null?q:t.vZ.mindmap.padding,(at=(pt=H.mindmap)==null?void 0:pt.useMaxWidth)!=null?at:t.vZ.mindmap.useMaxWidth)}),"draw"),J={draw:$},tt=(0,t.eW)(O=>{let R="";for(let v=0;v<O.THEME_COLOR_LIMIT;v++)O["lineColor"+v]=O["lineColor"+v]||O["cScaleInv"+v],(0,n.Z)(O["lineColor"+v])?O["lineColor"+v]=(0,g.Z)(O["lineColor"+v],20):O["lineColor"+v]=(0,r.Z)(O["lineColor"+v],20);for(let v=0;v<O.THEME_COLOR_LIMIT;v++){const M=""+(17-3*v);R+=`
    .section-${v-1} rect, .section-${v-1} path, .section-${v-1} circle, .section-${v-1} polygon, .section-${v-1} path  {
      fill: ${O["cScale"+v]};
    }
    .section-${v-1} text {
     fill: ${O["cScaleLabel"+v]};
    }
    .node-icon-${v-1} {
      font-size: 40px;
      color: ${O["cScaleLabel"+v]};
    }
    .section-edge-${v-1}{
      stroke: ${O["cScale"+v]};
    }
    .edge-depth-${v-1}{
      stroke-width: ${M};
    }
    .section-${v-1} line {
      stroke: ${O["cScaleInv"+v]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return R},"genSections"),U=(0,t.eW)(O=>`
  .edge {
    stroke-width: 3;
  }
  ${tt(O)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${O.git0};
  }
  .section-root text {
    fill: ${O.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),B=U,K={get db(){return new d},renderer:J,parser:l,styles:B}}}]);
}());