!(function(){"use strict";var rt=(Ae,Q,R)=>new Promise((q,x)=>{var K=g=>{try{N(R.next(g))}catch(V){x(V)}},G=g=>{try{N(R.throw(g))}catch(V){x(V)}},N=g=>g.done?q(g.value):Promise.resolve(g.value).then(K,G);N((R=R.apply(Ae,Q)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[544],{3027:function(Ae,Q,R){R.d(Q,{q:function(){return K}});var q=R(29134),x=R(69471),K=(0,q.eW)((G,N)=>{let g;return N==="sandbox"&&(g=(0,x.Ys)("#i"+G)),(N==="sandbox"?(0,x.Ys)(g.nodes()[0].contentDocument.body):(0,x.Ys)("body")).select(`[id="${G}"]`)},"getDiagramElement")},8031:function(Ae,Q,R){R.d(Q,{j:function(){return x}});var q=R(29134),x=(0,q.eW)((N,g,V,j)=>{N.attr("class",V);const{width:le,height:Ce,x:r,y:ue}=K(N,g);(0,q.v2)(N,Ce,le,j);const ve=G(r,ue,le,Ce,g);N.attr("viewBox",ve),q.cM.debug(`viewBox configured: ${ve} with padding: ${g}`)},"setupViewPortForSVG"),K=(0,q.eW)((N,g)=>{var j;const V=((j=N.node())==null?void 0:j.getBBox())||{width:0,height:0,x:0,y:0};return{width:V.width+g*2,height:V.height+g*2,x:V.x,y:V.y}},"calculateDimensionsWithPadding"),G=(0,q.eW)((N,g,V,j,le)=>`${N-le} ${g-le} ${V} ${j}`,"createViewBox")},544:function(Ae,Q,R){var ce;R.d(Q,{diagram:function(){return ot}});var q=R(3027),x=R(8031),K=R(23064),G=R(78933),N=R(61150),g=R(52387),V=R(50538),j=R(57956),le=R(70919),Ce=R(50854),r=R(29134),ue=function(){var e=(0,r.eW)(function(H,i,n,c){for(n=n||{},c=H.length;c--;n[H[c]]=i);return n},"o"),l=[1,3],u=[1,4],h=[1,5],_=[1,6],o=[5,6,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],f=[1,22],b=[2,7],S=[1,26],A=[1,27],E=[1,28],m=[1,29],I=[1,33],C=[1,34],L=[1,35],P=[1,36],w=[1,37],W=[1,38],F=[1,24],U=[1,31],B=[1,32],$=[1,30],y=[1,39],k=[1,40],p=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,77,89,90],z=[1,61],oe=[89,90],$e=[5,8,9,11,13,21,22,23,24,27,29,41,42,43,44,45,46,54,61,63,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Ve=[27,29],Ye=[1,70],Ke=[1,71],je=[1,72],He=[1,73],Qe=[1,74],Ge=[1,75],ze=[1,76],Ee=[1,83],X=[1,80],_e=[1,84],me=[1,85],fe=[1,86],de=[1,87],Re=[1,88],be=[1,89],pe=[1,90],ye=[1,91],ke=[1,92],Me=[5,8,9,11,13,21,22,23,24,27,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],J=[63,64],Xe=[1,101],Je=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,76,77,89,90],D=[5,8,9,11,13,21,22,23,24,41,42,43,44,45,46,54,72,74,75,76,77,80,81,82,83,84,85,86,87,88,89,90],Z=[1,110],ee=[1,106],te=[1,107],se=[1,108],ie=[1,109],re=[1,111],ge=[1,116],Se=[1,117],Ie=[1,114],Te=[1,115],Le={trace:(0,r.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,directive:4,NEWLINE:5,RD:6,diagram:7,EOF:8,acc_title:9,acc_title_value:10,acc_descr:11,acc_descr_value:12,acc_descr_multiline_value:13,requirementDef:14,elementDef:15,relationshipDef:16,direction:17,styleStatement:18,classDefStatement:19,classStatement:20,direction_tb:21,direction_bt:22,direction_rl:23,direction_lr:24,requirementType:25,requirementName:26,STRUCT_START:27,requirementBody:28,STYLE_SEPARATOR:29,idList:30,ID:31,COLONSEP:32,id:33,TEXT:34,text:35,RISK:36,riskLevel:37,VERIFYMTHD:38,verifyType:39,STRUCT_STOP:40,REQUIREMENT:41,FUNCTIONAL_REQUIREMENT:42,INTERFACE_REQUIREMENT:43,PERFORMANCE_REQUIREMENT:44,PHYSICAL_REQUIREMENT:45,DESIGN_CONSTRAINT:46,LOW_RISK:47,MED_RISK:48,HIGH_RISK:49,VERIFY_ANALYSIS:50,VERIFY_DEMONSTRATION:51,VERIFY_INSPECTION:52,VERIFY_TEST:53,ELEMENT:54,elementName:55,elementBody:56,TYPE:57,type:58,DOCREF:59,ref:60,END_ARROW_L:61,relationship:62,LINE:63,END_ARROW_R:64,CONTAINS:65,COPIES:66,DERIVES:67,SATISFIES:68,VERIFIES:69,REFINES:70,TRACES:71,CLASSDEF:72,stylesOpt:73,CLASS:74,ALPHA:75,COMMA:76,STYLE:77,style:78,styleComponent:79,NUM:80,COLON:81,UNIT:82,SPACE:83,BRKT:84,PCT:85,MINUS:86,LABEL:87,SEMICOLON:88,unqString:89,qString:90,$accept:0,$end:1},terminals_:{2:"error",5:"NEWLINE",6:"RD",8:"EOF",9:"acc_title",10:"acc_title_value",11:"acc_descr",12:"acc_descr_value",13:"acc_descr_multiline_value",21:"direction_tb",22:"direction_bt",23:"direction_rl",24:"direction_lr",27:"STRUCT_START",29:"STYLE_SEPARATOR",31:"ID",32:"COLONSEP",34:"TEXT",36:"RISK",38:"VERIFYMTHD",40:"STRUCT_STOP",41:"REQUIREMENT",42:"FUNCTIONAL_REQUIREMENT",43:"INTERFACE_REQUIREMENT",44:"PERFORMANCE_REQUIREMENT",45:"PHYSICAL_REQUIREMENT",46:"DESIGN_CONSTRAINT",47:"LOW_RISK",48:"MED_RISK",49:"HIGH_RISK",50:"VERIFY_ANALYSIS",51:"VERIFY_DEMONSTRATION",52:"VERIFY_INSPECTION",53:"VERIFY_TEST",54:"ELEMENT",57:"TYPE",59:"DOCREF",61:"END_ARROW_L",63:"LINE",64:"END_ARROW_R",65:"CONTAINS",66:"COPIES",67:"DERIVES",68:"SATISFIES",69:"VERIFIES",70:"REFINES",71:"TRACES",72:"CLASSDEF",74:"CLASS",75:"ALPHA",76:"COMMA",77:"STYLE",80:"NUM",81:"COLON",82:"UNIT",83:"SPACE",84:"BRKT",85:"PCT",86:"MINUS",87:"LABEL",88:"SEMICOLON",89:"unqString",90:"qString"},productions_:[0,[3,3],[3,2],[3,4],[4,2],[4,2],[4,1],[7,0],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[17,1],[17,1],[17,1],[17,1],[14,5],[14,7],[28,5],[28,5],[28,5],[28,5],[28,2],[28,1],[25,1],[25,1],[25,1],[25,1],[25,1],[25,1],[37,1],[37,1],[37,1],[39,1],[39,1],[39,1],[39,1],[15,5],[15,7],[56,5],[56,5],[56,2],[56,1],[16,5],[16,5],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[62,1],[19,3],[20,3],[20,3],[30,1],[30,3],[30,1],[30,3],[18,3],[73,1],[73,3],[78,1],[78,2],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[79,1],[26,1],[26,1],[33,1],[33,1],[35,1],[35,1],[55,1],[55,1],[58,1],[58,1],[60,1],[60,1]],performAction:(0,r.eW)(function(i,n,c,s,d,t,Ne){var a=t.length-1;switch(d){case 4:this.$=t[a].trim(),s.setAccTitle(this.$);break;case 5:case 6:this.$=t[a].trim(),s.setAccDescription(this.$);break;case 7:this.$=[];break;case 17:s.setDirection("TB");break;case 18:s.setDirection("BT");break;case 19:s.setDirection("RL");break;case 20:s.setDirection("LR");break;case 21:s.addRequirement(t[a-3],t[a-4]);break;case 22:s.addRequirement(t[a-5],t[a-6]),s.setClass([t[a-5]],t[a-3]);break;case 23:s.setNewReqId(t[a-2]);break;case 24:s.setNewReqText(t[a-2]);break;case 25:s.setNewReqRisk(t[a-2]);break;case 26:s.setNewReqVerifyMethod(t[a-2]);break;case 29:this.$=s.RequirementType.REQUIREMENT;break;case 30:this.$=s.RequirementType.FUNCTIONAL_REQUIREMENT;break;case 31:this.$=s.RequirementType.INTERFACE_REQUIREMENT;break;case 32:this.$=s.RequirementType.PERFORMANCE_REQUIREMENT;break;case 33:this.$=s.RequirementType.PHYSICAL_REQUIREMENT;break;case 34:this.$=s.RequirementType.DESIGN_CONSTRAINT;break;case 35:this.$=s.RiskLevel.LOW_RISK;break;case 36:this.$=s.RiskLevel.MED_RISK;break;case 37:this.$=s.RiskLevel.HIGH_RISK;break;case 38:this.$=s.VerifyType.VERIFY_ANALYSIS;break;case 39:this.$=s.VerifyType.VERIFY_DEMONSTRATION;break;case 40:this.$=s.VerifyType.VERIFY_INSPECTION;break;case 41:this.$=s.VerifyType.VERIFY_TEST;break;case 42:s.addElement(t[a-3]);break;case 43:s.addElement(t[a-5]),s.setClass([t[a-5]],t[a-3]);break;case 44:s.setNewElementType(t[a-2]);break;case 45:s.setNewElementDocRef(t[a-2]);break;case 48:s.addRelationship(t[a-2],t[a],t[a-4]);break;case 49:s.addRelationship(t[a-2],t[a-4],t[a]);break;case 50:this.$=s.Relationships.CONTAINS;break;case 51:this.$=s.Relationships.COPIES;break;case 52:this.$=s.Relationships.DERIVES;break;case 53:this.$=s.Relationships.SATISFIES;break;case 54:this.$=s.Relationships.VERIFIES;break;case 55:this.$=s.Relationships.REFINES;break;case 56:this.$=s.Relationships.TRACES;break;case 57:this.$=t[a-2],s.defineClass(t[a-1],t[a]);break;case 58:s.setClass(t[a-1],t[a]);break;case 59:s.setClass([t[a-2]],t[a]);break;case 60:case 62:this.$=[t[a]];break;case 61:case 63:this.$=t[a-2].concat([t[a]]);break;case 64:this.$=t[a-2],s.setCssStyle(t[a-1],t[a]);break;case 65:this.$=[t[a]];break;case 66:t[a-2].push(t[a]),this.$=t[a-2];break;case 68:this.$=t[a-1]+t[a];break}},"anonymous"),table:[{3:1,4:2,6:l,9:u,11:h,13:_},{1:[3]},{3:8,4:2,5:[1,7],6:l,9:u,11:h,13:_},{5:[1,9]},{10:[1,10]},{12:[1,11]},e(o,[2,6]),{3:12,4:2,6:l,9:u,11:h,13:_},{1:[2,2]},{4:17,5:f,7:13,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},e(o,[2,4]),e(o,[2,5]),{1:[2,1]},{8:[1,41]},{4:17,5:f,7:42,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:43,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:44,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:45,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:46,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:47,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:48,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:49,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{4:17,5:f,7:50,8:b,9:u,11:h,13:_,14:14,15:15,16:16,17:18,18:19,19:20,20:21,21:S,22:A,23:E,24:m,25:23,33:25,41:I,42:C,43:L,44:P,45:w,46:W,54:F,72:U,74:B,77:$,89:y,90:k},{26:51,89:[1,52],90:[1,53]},{55:54,89:[1,55],90:[1,56]},{29:[1,59],61:[1,57],63:[1,58]},e(p,[2,17]),e(p,[2,18]),e(p,[2,19]),e(p,[2,20]),{30:60,33:62,75:z,89:y,90:k},{30:63,33:62,75:z,89:y,90:k},{30:64,33:62,75:z,89:y,90:k},e(oe,[2,29]),e(oe,[2,30]),e(oe,[2,31]),e(oe,[2,32]),e(oe,[2,33]),e(oe,[2,34]),e($e,[2,81]),e($e,[2,82]),{1:[2,3]},{8:[2,8]},{8:[2,9]},{8:[2,10]},{8:[2,11]},{8:[2,12]},{8:[2,13]},{8:[2,14]},{8:[2,15]},{8:[2,16]},{27:[1,65],29:[1,66]},e(Ve,[2,79]),e(Ve,[2,80]),{27:[1,67],29:[1,68]},e(Ve,[2,85]),e(Ve,[2,86]),{62:69,65:Ye,66:Ke,67:je,68:He,69:Qe,70:Ge,71:ze},{62:77,65:Ye,66:Ke,67:je,68:He,69:Qe,70:Ge,71:ze},{30:78,33:62,75:z,89:y,90:k},{73:79,75:Ee,76:X,78:81,79:82,80:_e,81:me,82:fe,83:de,84:Re,85:be,86:pe,87:ye,88:ke},e(Me,[2,60]),e(Me,[2,62]),{73:93,75:Ee,76:X,78:81,79:82,80:_e,81:me,82:fe,83:de,84:Re,85:be,86:pe,87:ye,88:ke},{30:94,33:62,75:z,76:X,89:y,90:k},{5:[1,95]},{30:96,33:62,75:z,89:y,90:k},{5:[1,97]},{30:98,33:62,75:z,89:y,90:k},{63:[1,99]},e(J,[2,50]),e(J,[2,51]),e(J,[2,52]),e(J,[2,53]),e(J,[2,54]),e(J,[2,55]),e(J,[2,56]),{64:[1,100]},e(p,[2,59],{76:X}),e(p,[2,64],{76:Xe}),{33:103,75:[1,102],89:y,90:k},e(Je,[2,65],{79:104,75:Ee,80:_e,81:me,82:fe,83:de,84:Re,85:be,86:pe,87:ye,88:ke}),e(D,[2,67]),e(D,[2,69]),e(D,[2,70]),e(D,[2,71]),e(D,[2,72]),e(D,[2,73]),e(D,[2,74]),e(D,[2,75]),e(D,[2,76]),e(D,[2,77]),e(D,[2,78]),e(p,[2,57],{76:Xe}),e(p,[2,58],{76:X}),{5:Z,28:105,31:ee,34:te,36:se,38:ie,40:re},{27:[1,112],76:X},{5:ge,40:Se,56:113,57:Ie,59:Te},{27:[1,118],76:X},{33:119,89:y,90:k},{33:120,89:y,90:k},{75:Ee,78:121,79:82,80:_e,81:me,82:fe,83:de,84:Re,85:be,86:pe,87:ye,88:ke},e(Me,[2,61]),e(Me,[2,63]),e(D,[2,68]),e(p,[2,21]),{32:[1,122]},{32:[1,123]},{32:[1,124]},{32:[1,125]},{5:Z,28:126,31:ee,34:te,36:se,38:ie,40:re},e(p,[2,28]),{5:[1,127]},e(p,[2,42]),{32:[1,128]},{32:[1,129]},{5:ge,40:Se,56:130,57:Ie,59:Te},e(p,[2,47]),{5:[1,131]},e(p,[2,48]),e(p,[2,49]),e(Je,[2,66],{79:104,75:Ee,80:_e,81:me,82:fe,83:de,84:Re,85:be,86:pe,87:ye,88:ke}),{33:132,89:y,90:k},{35:133,89:[1,134],90:[1,135]},{37:136,47:[1,137],48:[1,138],49:[1,139]},{39:140,50:[1,141],51:[1,142],52:[1,143],53:[1,144]},e(p,[2,27]),{5:Z,28:145,31:ee,34:te,36:se,38:ie,40:re},{58:146,89:[1,147],90:[1,148]},{60:149,89:[1,150],90:[1,151]},e(p,[2,46]),{5:ge,40:Se,56:152,57:Ie,59:Te},{5:[1,153]},{5:[1,154]},{5:[2,83]},{5:[2,84]},{5:[1,155]},{5:[2,35]},{5:[2,36]},{5:[2,37]},{5:[1,156]},{5:[2,38]},{5:[2,39]},{5:[2,40]},{5:[2,41]},e(p,[2,22]),{5:[1,157]},{5:[2,87]},{5:[2,88]},{5:[1,158]},{5:[2,89]},{5:[2,90]},e(p,[2,43]),{5:Z,28:159,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:160,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:161,31:ee,34:te,36:se,38:ie,40:re},{5:Z,28:162,31:ee,34:te,36:se,38:ie,40:re},{5:ge,40:Se,56:163,57:Ie,59:Te},{5:ge,40:Se,56:164,57:Ie,59:Te},e(p,[2,23]),e(p,[2,24]),e(p,[2,25]),e(p,[2,26]),e(p,[2,44]),e(p,[2,45])],defaultActions:{8:[2,2],12:[2,1],41:[2,3],42:[2,8],43:[2,9],44:[2,10],45:[2,11],46:[2,12],47:[2,13],48:[2,14],49:[2,15],50:[2,16],134:[2,83],135:[2,84],137:[2,35],138:[2,36],139:[2,37],141:[2,38],142:[2,39],143:[2,40],144:[2,41],147:[2,87],148:[2,88],150:[2,89],151:[2,90]},parseError:(0,r.eW)(function(i,n){if(n.recoverable)this.trace(i);else{var c=new Error(i);throw c.hash=n,c}},"parseError"),parse:(0,r.eW)(function(i){var n=this,c=[0],s=[],d=[null],t=[],Ne=this.table,a="",Oe=0,Ze=0,et=0,ut=2,tt=1,Et=t.slice.call(arguments,1),T=Object.create(this.lexer),ne={yy:{}};for(var Pe in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Pe)&&(ne.yy[Pe]=this.yy[Pe]);T.setInput(i,ne.yy),ne.yy.lexer=T,ne.yy.parser=this,typeof T.yylloc=="undefined"&&(T.yylloc={});var we=T.yylloc;t.push(we);var _t=T.options&&T.options.ranges;typeof ne.yy.parseError=="function"?this.parseError=ne.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function mt(M){c.length=c.length-2*M,d.length=d.length-M,t.length=t.length-M}(0,r.eW)(mt,"popStack");function st(){var M;return M=s.pop()||T.lex()||tt,typeof M!="number"&&(M instanceof Array&&(s=M,M=s.pop()),M=n.symbols_[M]||M),M}(0,r.eW)(st,"lex");for(var v,We,ae,O,ft,Fe,he={},qe,Y,it,xe;;){if(ae=c[c.length-1],this.defaultActions[ae]?O=this.defaultActions[ae]:((v===null||typeof v=="undefined")&&(v=st()),O=Ne[ae]&&Ne[ae][v]),typeof O=="undefined"||!O.length||!O[0]){var Ue="";xe=[];for(qe in Ne[ae])this.terminals_[qe]&&qe>ut&&xe.push("'"+this.terminals_[qe]+"'");T.showPosition?Ue="Parse error on line "+(Oe+1)+`:
`+T.showPosition()+`
Expecting `+xe.join(", ")+", got '"+(this.terminals_[v]||v)+"'":Ue="Parse error on line "+(Oe+1)+": Unexpected "+(v==tt?"end of input":"'"+(this.terminals_[v]||v)+"'"),this.parseError(Ue,{text:T.match,token:this.terminals_[v]||v,line:T.yylineno,loc:we,expected:xe})}if(O[0]instanceof Array&&O.length>1)throw new Error("Parse Error: multiple actions possible at state: "+ae+", token: "+v);switch(O[0]){case 1:c.push(v),d.push(T.yytext),t.push(T.yylloc),c.push(O[1]),v=null,We?(v=We,We=null):(Ze=T.yyleng,a=T.yytext,Oe=T.yylineno,we=T.yylloc,et>0&&et--);break;case 2:if(Y=this.productions_[O[1]][1],he.$=d[d.length-Y],he._$={first_line:t[t.length-(Y||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(Y||1)].first_column,last_column:t[t.length-1].last_column},_t&&(he._$.range=[t[t.length-(Y||1)].range[0],t[t.length-1].range[1]]),Fe=this.performAction.apply(he,[a,Ze,Oe,ne.yy,O[1],d,t].concat(Et)),typeof Fe!="undefined")return Fe;Y&&(c=c.slice(0,-1*Y*2),d=d.slice(0,-1*Y),t=t.slice(0,-1*Y)),c.push(this.productions_[O[1]][0]),d.push(he.$),t.push(he._$),it=Ne[c[c.length-2]][c[c.length-1]],c.push(it);break;case 3:return!0}}return!0},"parse")},ht=function(){var H={EOF:1,parseError:(0,r.eW)(function(n,c){if(this.yy.parser)this.yy.parser.parseError(n,c);else throw new Error(n)},"parseError"),setInput:(0,r.eW)(function(i,n){return this.yy=n||this.yy||{},this._input=i,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,r.eW)(function(){var i=this._input[0];this.yytext+=i,this.yyleng++,this.offset++,this.match+=i,this.matched+=i;var n=i.match(/(?:\r\n?|\n).*/g);return n?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),i},"input"),unput:(0,r.eW)(function(i){var n=i.length,c=i.split(/(?:\r\n?|\n)/g);this._input=i+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-n),this.offset-=n;var s=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),c.length-1&&(this.yylineno-=c.length-1);var d=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:c?(c.length===s.length?this.yylloc.first_column:0)+s[s.length-c.length].length-c[0].length:this.yylloc.first_column-n},this.options.ranges&&(this.yylloc.range=[d[0],d[0]+this.yyleng-n]),this.yyleng=this.yytext.length,this},"unput"),more:(0,r.eW)(function(){return this._more=!0,this},"more"),reject:(0,r.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,r.eW)(function(i){this.unput(this.match.slice(i))},"less"),pastInput:(0,r.eW)(function(){var i=this.matched.substr(0,this.matched.length-this.match.length);return(i.length>20?"...":"")+i.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,r.eW)(function(){var i=this.match;return i.length<20&&(i+=this._input.substr(0,20-i.length)),(i.substr(0,20)+(i.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,r.eW)(function(){var i=this.pastInput(),n=new Array(i.length+1).join("-");return i+this.upcomingInput()+`
`+n+"^"},"showPosition"),test_match:(0,r.eW)(function(i,n){var c,s,d;if(this.options.backtrack_lexer&&(d={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(d.yylloc.range=this.yylloc.range.slice(0))),s=i[0].match(/(?:\r\n?|\n).*/g),s&&(this.yylineno+=s.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:s?s[s.length-1].length-s[s.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+i[0].length},this.yytext+=i[0],this.match+=i[0],this.matches=i,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(i[0].length),this.matched+=i[0],c=this.performAction.call(this,this.yy,this,n,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),c)return c;if(this._backtrack){for(var t in d)this[t]=d[t];return!1}return!1},"test_match"),next:(0,r.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var i,n,c,s;this._more||(this.yytext="",this.match="");for(var d=this._currentRules(),t=0;t<d.length;t++)if(c=this._input.match(this.rules[d[t]]),c&&(!n||c[0].length>n[0].length)){if(n=c,s=t,this.options.backtrack_lexer){if(i=this.test_match(c,d[t]),i!==!1)return i;if(this._backtrack){n=!1;continue}else return!1}else if(!this.options.flex)break}return n?(i=this.test_match(n,d[s]),i!==!1?i:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,r.eW)(function(){var n=this.next();return n||this.lex()},"lex"),begin:(0,r.eW)(function(n){this.conditionStack.push(n)},"begin"),popState:(0,r.eW)(function(){var n=this.conditionStack.length-1;return n>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,r.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,r.eW)(function(n){return n=this.conditionStack.length-1-Math.abs(n||0),n>=0?this.conditionStack[n]:"INITIAL"},"topState"),pushState:(0,r.eW)(function(n){this.begin(n)},"pushState"),stateStackSize:(0,r.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,r.eW)(function(n,c,s,d){var t=d;switch(s){case 0:return"title";case 1:return this.begin("acc_title"),9;break;case 2:return this.popState(),"acc_title_value";break;case 3:return this.begin("acc_descr"),11;break;case 4:return this.popState(),"acc_descr_value";break;case 5:this.begin("acc_descr_multiline");break;case 6:this.popState();break;case 7:return"acc_descr_multiline_value";case 8:return 21;case 9:return 22;case 10:return 23;case 11:return 24;case 12:return 5;case 13:break;case 14:break;case 15:break;case 16:return 8;case 17:return 6;case 18:return 27;case 19:return 40;case 20:return 29;case 21:return 32;case 22:return 31;case 23:return 34;case 24:return 36;case 25:return 38;case 26:return 41;case 27:return 42;case 28:return 43;case 29:return 44;case 30:return 45;case 31:return 46;case 32:return 47;case 33:return 48;case 34:return 49;case 35:return 50;case 36:return 51;case 37:return 52;case 38:return 53;case 39:return 54;case 40:return 65;case 41:return 66;case 42:return 67;case 43:return 68;case 44:return 69;case 45:return 70;case 46:return 71;case 47:return 57;case 48:return 59;case 49:return this.begin("style"),77;break;case 50:return 75;case 51:return 81;case 52:return 88;case 53:return"PERCENT";case 54:return 86;case 55:return 84;case 56:break;case 57:this.begin("string");break;case 58:this.popState();break;case 59:return this.begin("style"),72;break;case 60:return this.begin("style"),74;break;case 61:return 61;case 62:return 64;case 63:return 63;case 64:this.begin("string");break;case 65:this.popState();break;case 66:return"qString";case 67:return c.yytext=c.yytext.trim(),89;break;case 68:return 75;case 69:return 80;case 70:return 76}},"anonymous"),rules:[/^(?:title\s[^#\n;]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:(\r?\n)+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:%[^\n]*)/i,/^(?:$)/i,/^(?:requirementDiagram\b)/i,/^(?:\{)/i,/^(?:\})/i,/^(?::{3})/i,/^(?::)/i,/^(?:id\b)/i,/^(?:text\b)/i,/^(?:risk\b)/i,/^(?:verifyMethod\b)/i,/^(?:requirement\b)/i,/^(?:functionalRequirement\b)/i,/^(?:interfaceRequirement\b)/i,/^(?:performanceRequirement\b)/i,/^(?:physicalRequirement\b)/i,/^(?:designConstraint\b)/i,/^(?:low\b)/i,/^(?:medium\b)/i,/^(?:high\b)/i,/^(?:analysis\b)/i,/^(?:demonstration\b)/i,/^(?:inspection\b)/i,/^(?:test\b)/i,/^(?:element\b)/i,/^(?:contains\b)/i,/^(?:copies\b)/i,/^(?:derives\b)/i,/^(?:satisfies\b)/i,/^(?:verifies\b)/i,/^(?:refines\b)/i,/^(?:traces\b)/i,/^(?:type\b)/i,/^(?:docref\b)/i,/^(?:style\b)/i,/^(?:\w+)/i,/^(?::)/i,/^(?:;)/i,/^(?:%)/i,/^(?:-)/i,/^(?:#)/i,/^(?: )/i,/^(?:["])/i,/^(?:\n)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:<-)/i,/^(?:->)/i,/^(?:-)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:[\w][^:,\r\n\{\<\>\-\=]*)/i,/^(?:\w+)/i,/^(?:[0-9]+)/i,/^(?:,)/i],conditions:{acc_descr_multiline:{rules:[6,7,68,69,70],inclusive:!1},acc_descr:{rules:[4,68,69,70],inclusive:!1},acc_title:{rules:[2,68,69,70],inclusive:!1},style:{rules:[50,51,52,53,54,55,56,57,58,68,69,70],inclusive:!1},unqString:{rules:[68,69,70],inclusive:!1},token:{rules:[68,69,70],inclusive:!1},string:{rules:[65,66,68,69,70],inclusive:!1},INITIAL:{rules:[0,1,3,5,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,59,60,61,62,63,64,67,68,69,70],inclusive:!0}}};return H}();Le.lexer=ht;function De(){this.yy={}}return(0,r.eW)(De,"Parser"),De.prototype=Le,Le.Parser=De,new De}();ue.parser=ue;var ve=ue,nt=(ce=class{constructor(){this.relations=[],this.latestRequirement=this.getInitialRequirement(),this.requirements=new Map,this.latestElement=this.getInitialElement(),this.elements=new Map,this.classes=new Map,this.direction="TB",this.RequirementType={REQUIREMENT:"Requirement",FUNCTIONAL_REQUIREMENT:"Functional Requirement",INTERFACE_REQUIREMENT:"Interface Requirement",PERFORMANCE_REQUIREMENT:"Performance Requirement",PHYSICAL_REQUIREMENT:"Physical Requirement",DESIGN_CONSTRAINT:"Design Constraint"},this.RiskLevel={LOW_RISK:"Low",MED_RISK:"Medium",HIGH_RISK:"High"},this.VerifyType={VERIFY_ANALYSIS:"Analysis",VERIFY_DEMONSTRATION:"Demonstration",VERIFY_INSPECTION:"Inspection",VERIFY_TEST:"Test"},this.Relationships={CONTAINS:"contains",COPIES:"copies",DERIVES:"derives",SATISFIES:"satisfies",VERIFIES:"verifies",REFINES:"refines",TRACES:"traces"},this.setAccTitle=r.GN,this.getAccTitle=r.eu,this.setAccDescription=r.U$,this.getAccDescription=r.Mx,this.setDiagramTitle=r.g2,this.getDiagramTitle=r.Kr,this.getConfig=(0,r.eW)(()=>(0,r.nV)().requirement,"getConfig"),this.clear(),this.setDirection=this.setDirection.bind(this),this.addRequirement=this.addRequirement.bind(this),this.setNewReqId=this.setNewReqId.bind(this),this.setNewReqRisk=this.setNewReqRisk.bind(this),this.setNewReqText=this.setNewReqText.bind(this),this.setNewReqVerifyMethod=this.setNewReqVerifyMethod.bind(this),this.addElement=this.addElement.bind(this),this.setNewElementType=this.setNewElementType.bind(this),this.setNewElementDocRef=this.setNewElementDocRef.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setCssStyle=this.setCssStyle.bind(this),this.setClass=this.setClass.bind(this),this.defineClass=this.defineClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}getDirection(){return this.direction}setDirection(l){this.direction=l}resetLatestRequirement(){this.latestRequirement=this.getInitialRequirement()}resetLatestElement(){this.latestElement=this.getInitialElement()}getInitialRequirement(){return{requirementId:"",text:"",risk:"",verifyMethod:"",name:"",type:"",cssStyles:[],classes:["default"]}}getInitialElement(){return{name:"",type:"",docRef:"",cssStyles:[],classes:["default"]}}addRequirement(l,u){return this.requirements.has(l)||this.requirements.set(l,{name:l,type:u,requirementId:this.latestRequirement.requirementId,text:this.latestRequirement.text,risk:this.latestRequirement.risk,verifyMethod:this.latestRequirement.verifyMethod,cssStyles:[],classes:["default"]}),this.resetLatestRequirement(),this.requirements.get(l)}getRequirements(){return this.requirements}setNewReqId(l){this.latestRequirement!==void 0&&(this.latestRequirement.requirementId=l)}setNewReqText(l){this.latestRequirement!==void 0&&(this.latestRequirement.text=l)}setNewReqRisk(l){this.latestRequirement!==void 0&&(this.latestRequirement.risk=l)}setNewReqVerifyMethod(l){this.latestRequirement!==void 0&&(this.latestRequirement.verifyMethod=l)}addElement(l){return this.elements.has(l)||(this.elements.set(l,{name:l,type:this.latestElement.type,docRef:this.latestElement.docRef,cssStyles:[],classes:["default"]}),r.cM.info("Added new element: ",l)),this.resetLatestElement(),this.elements.get(l)}getElements(){return this.elements}setNewElementType(l){this.latestElement!==void 0&&(this.latestElement.type=l)}setNewElementDocRef(l){this.latestElement!==void 0&&(this.latestElement.docRef=l)}addRelationship(l,u,h){this.relations.push({type:l,src:u,dst:h})}getRelationships(){return this.relations}clear(){this.relations=[],this.resetLatestRequirement(),this.requirements=new Map,this.resetLatestElement(),this.elements=new Map,this.classes=new Map,(0,r.ZH)()}setCssStyle(l,u){var h;for(const _ of l){const o=(h=this.requirements.get(_))!=null?h:this.elements.get(_);if(!u||!o)return;for(const f of u)f.includes(",")?o.cssStyles.push(...f.split(",")):o.cssStyles.push(f)}}setClass(l,u){var h,_;for(const o of l){const f=(h=this.requirements.get(o))!=null?h:this.elements.get(o);if(f)for(const b of u){f.classes.push(b);const S=(_=this.classes.get(b))==null?void 0:_.styles;S&&f.cssStyles.push(...S)}}}defineClass(l,u){for(const h of l){let _=this.classes.get(h);_===void 0&&(_={id:h,styles:[],textStyles:[]},this.classes.set(h,_)),u&&u.forEach(function(o){if(/color/.exec(o)){const f=o.replace("fill","bgFill");_.textStyles.push(f)}_.styles.push(o)}),this.requirements.forEach(o=>{o.classes.includes(h)&&o.cssStyles.push(...u.flatMap(f=>f.split(",")))}),this.elements.forEach(o=>{o.classes.includes(h)&&o.cssStyles.push(...u.flatMap(f=>f.split(",")))})}}getClasses(){return this.classes}getData(){var _,o,f,b,S,A;const l=(0,r.nV)(),u=[],h=[];for(const E of this.requirements.values()){const m=E;m.id=E.name,m.cssStyles=E.cssStyles,m.cssClasses=E.classes.join(" "),m.shape="requirementBox",m.look=l.look,u.push(m)}for(const E of this.elements.values()){const m=E;m.shape="requirementBox",m.look=l.look,m.id=E.name,m.cssStyles=E.cssStyles,m.cssClasses=E.classes.join(" "),u.push(m)}for(const E of this.relations){let m=0;const I=E.type===this.Relationships.CONTAINS,C={id:`${E.src}-${E.dst}-${m}`,start:(f=(_=this.requirements.get(E.src))==null?void 0:_.name)!=null?f:(o=this.elements.get(E.src))==null?void 0:o.name,end:(A=(b=this.requirements.get(E.dst))==null?void 0:b.name)!=null?A:(S=this.elements.get(E.dst))==null?void 0:S.name,label:`&lt;&lt;${E.type}&gt;&gt;`,classes:"relationshipLine",style:["fill:none",I?"":"stroke-dasharray: 10,7"],labelpos:"c",thickness:"normal",type:"normal",pattern:I?"normal":"dashed",arrowTypeStart:I?"requirement_contains":"",arrowTypeEnd:I?"":"requirement_arrow",look:l.look};h.push(C),m++}return{nodes:u,edges:h,other:{},config:l,direction:this.getDirection()}}},(0,r.eW)(ce,"RequirementDB"),ce),at=(0,r.eW)(e=>`

  marker {
    fill: ${e.relationColor};
    stroke: ${e.relationColor};
  }

  marker.cross {
    stroke: ${e.lineColor};
  }

  svg {
    font-family: ${e.fontFamily};
    font-size: ${e.fontSize};
  }

  .reqBox {
    fill: ${e.requirementBackground};
    fill-opacity: 1.0;
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  
  .reqTitle, .reqLabel{
    fill:  ${e.requirementTextColor};
  }
  .reqLabelBox {
    fill: ${e.relationLabelBackground};
    fill-opacity: 1.0;
  }

  .req-title-line {
    stroke: ${e.requirementBorderColor};
    stroke-width: ${e.requirementBorderSize};
  }
  .relationshipLine {
    stroke: ${e.relationColor};
    stroke-width: 1;
  }
  .relationshipLabel {
    fill: ${e.relationLabelColor};
  }
  .divider {
    stroke: ${e.nodeBorder};
    stroke-width: 1;
  }
  .label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .label text,span {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }
  .labelBkg {
    background-color: ${e.edgeLabelBackground};
  }

`,"getStyles"),lt=at,Be={};(0,r.r2)(Be,{draw:()=>ct});var ct=(0,r.eW)(function(e,l,u,h){return rt(this,null,function*(){var E,m,I,C;r.cM.info("REF0:"),r.cM.info("Drawing requirement diagram (unified)",l);const{securityLevel:_,state:o,layout:f}=(0,r.nV)(),b=h.db.getData(),S=(0,q.q)(l,_);b.type=h.type,b.layoutAlgorithm=(0,K._b)(f),b.nodeSpacing=(E=o==null?void 0:o.nodeSpacing)!=null?E:50,b.rankSpacing=(m=o==null?void 0:o.rankSpacing)!=null?m:50,b.markers=["requirement_contains","requirement_arrow"],b.diagramId=l,yield(0,K.sY)(b,S);const A=8;Ce.w8.insertTitle(S,"requirementDiagramTitleText",(I=o==null?void 0:o.titleTopMargin)!=null?I:25,h.db.getDiagramTitle()),(0,x.j)(S,A,"requirementDiagram",(C=o==null?void 0:o.useMaxWidth)!=null?C:!0)})},"draw"),ot={parser:ve,get db(){return new nt},renderer:Be,styles:lt}}}]);
}());