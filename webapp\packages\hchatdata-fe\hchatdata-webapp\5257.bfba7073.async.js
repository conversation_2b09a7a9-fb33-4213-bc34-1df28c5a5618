!(function(){"use strict";var Kt=(G,D,_)=>new Promise((O,S)=>{var R=f=>{try{k(_.next(f))}catch(m){S(m)}},v=f=>{try{k(_.throw(f))}catch(m){S(m)}},k=f=>f.done?O(f.value):Promise.resolve(f.value).then(R,v);k((_=_.apply(G,D)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5257],{74523:function(G,D,_){var O=_(50834),S=_(38456);const R=(v,k)=>O.Z.lang.round(S.Z.parse(v)[k]);D.Z=R},3027:function(G,D,_){_.d(D,{q:function(){return R}});var O=_(29134),S=_(69471),R=(0,O.eW)((v,k)=>{let f;return k==="sandbox"&&(f=(0,S.Ys)("#i"+v)),(k==="sandbox"?(0,S.Ys)(f.nodes()[0].contentDocument.body):(0,S.Ys)("body")).select(`[id="${v}"]`)},"getDiagramElement")},8031:function(G,D,_){_.d(D,{j:function(){return S}});var O=_(29134),S=(0,O.eW)((k,f,m,M)=>{k.attr("class",m);const{width:w,height:Z,x:a,y:ft}=R(k,f);(0,O.v2)(k,Z,w,M);const at=v(a,ft,w,Z,f);k.attr("viewBox",at),O.cM.debug(`viewBox configured: ${at} with padding: ${f}`)},"setupViewPortForSVG"),R=(0,O.eW)((k,f)=>{var M;const m=((M=k.node())==null?void 0:M.getBBox())||{width:0,height:0,x:0,y:0};return{width:m.width+f*2,height:m.height+f*2,x:m.x,y:m.y}},"calculateDimensionsWithPadding"),v=(0,O.eW)((k,f,m,M,w)=>`${k-w} ${f-w} ${m} ${M}`,"createViewBox")},75257:function(G,D,_){var Y;_.d(D,{diagram:function(){return Qt}});var O=_(3027),S=_(8031),R=_(23064),v=_(78933),k=_(61150),f=_(52387),m=_(50538),M=_(57956),w=_(70919),Z=_(50854),a=_(29134),ft=_(69471),at=_(74523),Ut=_(85712),pt=function(){var s=(0,a.eW)(function(B,n,c,o){for(c=c||{},o=B.length;o--;c[B[o]]=n);return c},"o"),i=[6,8,10,22,24,26,28,33,34,35,36,37,40,43,44,50],h=[1,10],d=[1,11],u=[1,12],l=[1,13],y=[1,20],p=[1,21],I=[1,22],z=[1,23],ct=[1,24],N=[1,19],X=[1,25],H=[1,26],F=[1,18],Q=[1,33],K=[1,34],J=[1,35],kt=[1,36],Et=[1,37],vt=[6,8,10,13,15,17,20,21,22,24,26,28,33,34,35,36,37,40,43,44,50,63,64,65,66,67],x=[1,42],P=[1,43],q=[1,52],$=[40,50,68,69],tt=[1,63],et=[1,61],W=[1,58],st=[1,62],it=[1,64],ot=[6,8,10,13,17,22,24,26,28,33,34,35,36,37,40,41,42,43,44,48,49,50,63,64,65,66,67],It=[63,64,65,66,67],Ct=[1,81],Mt=[1,80],Nt=[1,78],xt=[1,79],Pt=[6,10,42,47],U=[6,10,13,41,42,47,48,49],lt=[1,89],ht=[1,88],ut=[1,87],rt=[19,56],Wt=[1,98],Bt=[1,97],gt=[19,56,58,60],mt={trace:(0,a.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,ER_DIAGRAM:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,entityName:11,relSpec:12,COLON:13,role:14,STYLE_SEPARATOR:15,idList:16,BLOCK_START:17,attributes:18,BLOCK_STOP:19,SQS:20,SQE:21,title:22,title_value:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,direction:29,classDefStatement:30,classStatement:31,styleStatement:32,direction_tb:33,direction_bt:34,direction_rl:35,direction_lr:36,CLASSDEF:37,stylesOpt:38,separator:39,UNICODE_TEXT:40,STYLE_TEXT:41,COMMA:42,CLASS:43,STYLE:44,style:45,styleComponent:46,SEMI:47,NUM:48,BRKT:49,ENTITY_NAME:50,attribute:51,attributeType:52,attributeName:53,attributeKeyTypeList:54,attributeComment:55,ATTRIBUTE_WORD:56,attributeKeyType:57,",":58,ATTRIBUTE_KEY:59,COMMENT:60,cardinality:61,relType:62,ZERO_OR_ONE:63,ZERO_OR_MORE:64,ONE_OR_MORE:65,ONLY_ONE:66,MD_PARENT:67,NON_IDENTIFYING:68,IDENTIFYING:69,WORD:70,$accept:0,$end:1},terminals_:{2:"error",4:"ER_DIAGRAM",6:"EOF",8:"SPACE",10:"NEWLINE",13:"COLON",15:"STYLE_SEPARATOR",17:"BLOCK_START",19:"BLOCK_STOP",20:"SQS",21:"SQE",22:"title",23:"title_value",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"direction_tb",34:"direction_bt",35:"direction_rl",36:"direction_lr",37:"CLASSDEF",40:"UNICODE_TEXT",41:"STYLE_TEXT",42:"COMMA",43:"CLASS",44:"STYLE",47:"SEMI",48:"NUM",49:"BRKT",50:"ENTITY_NAME",56:"ATTRIBUTE_WORD",58:",",59:"ATTRIBUTE_KEY",60:"COMMENT",63:"ZERO_OR_ONE",64:"ZERO_OR_MORE",65:"ONE_OR_MORE",66:"ONLY_ONE",67:"MD_PARENT",68:"NON_IDENTIFYING",69:"IDENTIFYING",70:"WORD"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,5],[9,9],[9,7],[9,7],[9,4],[9,6],[9,3],[9,5],[9,1],[9,3],[9,7],[9,9],[9,6],[9,8],[9,4],[9,6],[9,2],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[9,1],[29,1],[29,1],[29,1],[29,1],[30,4],[16,1],[16,1],[16,3],[16,3],[31,3],[32,4],[38,1],[38,3],[45,1],[45,2],[39,1],[39,1],[39,1],[46,1],[46,1],[46,1],[46,1],[11,1],[11,1],[18,1],[18,2],[51,2],[51,3],[51,3],[51,4],[52,1],[53,1],[54,1],[54,3],[57,1],[55,1],[12,3],[61,1],[61,1],[61,1],[61,1],[61,1],[62,1],[62,1],[14,1],[14,1],[14,1]],performAction:(0,a.eW)(function(n,c,o,r,b,t,nt){var e=t.length-1;switch(b){case 1:break;case 2:this.$=[];break;case 3:t[e-1].push(t[e]),this.$=t[e-1];break;case 4:case 5:this.$=t[e];break;case 6:case 7:this.$=[];break;case 8:r.addEntity(t[e-4]),r.addEntity(t[e-2]),r.addRelationship(t[e-4],t[e],t[e-2],t[e-3]);break;case 9:r.addEntity(t[e-8]),r.addEntity(t[e-4]),r.addRelationship(t[e-8],t[e],t[e-4],t[e-5]),r.setClass([t[e-8]],t[e-6]),r.setClass([t[e-4]],t[e-2]);break;case 10:r.addEntity(t[e-6]),r.addEntity(t[e-2]),r.addRelationship(t[e-6],t[e],t[e-2],t[e-3]),r.setClass([t[e-6]],t[e-4]);break;case 11:r.addEntity(t[e-6]),r.addEntity(t[e-4]),r.addRelationship(t[e-6],t[e],t[e-4],t[e-5]),r.setClass([t[e-4]],t[e-2]);break;case 12:r.addEntity(t[e-3]),r.addAttributes(t[e-3],t[e-1]);break;case 13:r.addEntity(t[e-5]),r.addAttributes(t[e-5],t[e-1]),r.setClass([t[e-5]],t[e-3]);break;case 14:r.addEntity(t[e-2]);break;case 15:r.addEntity(t[e-4]),r.setClass([t[e-4]],t[e-2]);break;case 16:r.addEntity(t[e]);break;case 17:r.addEntity(t[e-2]),r.setClass([t[e-2]],t[e]);break;case 18:r.addEntity(t[e-6],t[e-4]),r.addAttributes(t[e-6],t[e-1]);break;case 19:r.addEntity(t[e-8],t[e-6]),r.addAttributes(t[e-8],t[e-1]),r.setClass([t[e-8]],t[e-3]);break;case 20:r.addEntity(t[e-5],t[e-3]);break;case 21:r.addEntity(t[e-7],t[e-5]),r.setClass([t[e-7]],t[e-2]);break;case 22:r.addEntity(t[e-3],t[e-1]);break;case 23:r.addEntity(t[e-5],t[e-3]),r.setClass([t[e-5]],t[e]);break;case 24:case 25:this.$=t[e].trim(),r.setAccTitle(this.$);break;case 26:case 27:this.$=t[e].trim(),r.setAccDescription(this.$);break;case 32:r.setDirection("TB");break;case 33:r.setDirection("BT");break;case 34:r.setDirection("RL");break;case 35:r.setDirection("LR");break;case 36:this.$=t[e-3],r.addClass(t[e-2],t[e-1]);break;case 37:case 38:case 56:case 64:this.$=[t[e]];break;case 39:case 40:this.$=t[e-2].concat([t[e]]);break;case 41:this.$=t[e-2],r.setClass(t[e-1],t[e]);break;case 42:this.$=t[e-3],r.addCssStyles(t[e-2],t[e-1]);break;case 43:this.$=[t[e]];break;case 44:t[e-2].push(t[e]),this.$=t[e-2];break;case 46:this.$=t[e-1]+t[e];break;case 54:case 76:case 77:this.$=t[e].replace(/"/g,"");break;case 55:case 78:this.$=t[e];break;case 57:t[e].push(t[e-1]),this.$=t[e];break;case 58:this.$={type:t[e-1],name:t[e]};break;case 59:this.$={type:t[e-2],name:t[e-1],keys:t[e]};break;case 60:this.$={type:t[e-2],name:t[e-1],comment:t[e]};break;case 61:this.$={type:t[e-3],name:t[e-2],keys:t[e-1],comment:t[e]};break;case 62:case 63:case 66:this.$=t[e];break;case 65:t[e-2].push(t[e]),this.$=t[e-2];break;case 67:this.$=t[e].replace(/"/g,"");break;case 68:this.$={cardA:t[e],relType:t[e-1],cardB:t[e-2]};break;case 69:this.$=r.Cardinality.ZERO_OR_ONE;break;case 70:this.$=r.Cardinality.ZERO_OR_MORE;break;case 71:this.$=r.Cardinality.ONE_OR_MORE;break;case 72:this.$=r.Cardinality.ONLY_ONE;break;case 73:this.$=r.Cardinality.MD_PARENT;break;case 74:this.$=r.Identification.NON_IDENTIFYING;break;case 75:this.$=r.Identification.IDENTIFYING;break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},s(i,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:9,22:h,24:d,26:u,28:l,29:14,30:15,31:16,32:17,33:y,34:p,35:I,36:z,37:ct,40:N,43:X,44:H,50:F},s(i,[2,7],{1:[2,1]}),s(i,[2,3]),{9:27,11:9,22:h,24:d,26:u,28:l,29:14,30:15,31:16,32:17,33:y,34:p,35:I,36:z,37:ct,40:N,43:X,44:H,50:F},s(i,[2,5]),s(i,[2,6]),s(i,[2,16],{12:28,61:32,15:[1,29],17:[1,30],20:[1,31],63:Q,64:K,65:J,66:kt,67:Et}),{23:[1,38]},{25:[1,39]},{27:[1,40]},s(i,[2,27]),s(i,[2,28]),s(i,[2,29]),s(i,[2,30]),s(i,[2,31]),s(vt,[2,54]),s(vt,[2,55]),s(i,[2,32]),s(i,[2,33]),s(i,[2,34]),s(i,[2,35]),{16:41,40:x,41:P},{16:44,40:x,41:P},{16:45,40:x,41:P},s(i,[2,4]),{11:46,40:N,50:F},{16:47,40:x,41:P},{18:48,19:[1,49],51:50,52:51,56:q},{11:53,40:N,50:F},{62:54,68:[1,55],69:[1,56]},s($,[2,69]),s($,[2,70]),s($,[2,71]),s($,[2,72]),s($,[2,73]),s(i,[2,24]),s(i,[2,25]),s(i,[2,26]),{13:tt,38:57,41:et,42:W,45:59,46:60,48:st,49:it},s(ot,[2,37]),s(ot,[2,38]),{16:65,40:x,41:P,42:W},{13:tt,38:66,41:et,42:W,45:59,46:60,48:st,49:it},{13:[1,67],15:[1,68]},s(i,[2,17],{61:32,12:69,17:[1,70],42:W,63:Q,64:K,65:J,66:kt,67:Et}),{19:[1,71]},s(i,[2,14]),{18:72,19:[2,56],51:50,52:51,56:q},{53:73,56:[1,74]},{56:[2,62]},{21:[1,75]},{61:76,63:Q,64:K,65:J,66:kt,67:Et},s(It,[2,74]),s(It,[2,75]),{6:Ct,10:Mt,39:77,42:Nt,47:xt},{40:[1,82],41:[1,83]},s(Pt,[2,43],{46:84,13:tt,41:et,48:st,49:it}),s(U,[2,45]),s(U,[2,50]),s(U,[2,51]),s(U,[2,52]),s(U,[2,53]),s(i,[2,41],{42:W}),{6:Ct,10:Mt,39:85,42:Nt,47:xt},{14:86,40:lt,50:ht,70:ut},{16:90,40:x,41:P},{11:91,40:N,50:F},{18:92,19:[1,93],51:50,52:51,56:q},s(i,[2,12]),{19:[2,57]},s(rt,[2,58],{54:94,55:95,57:96,59:Wt,60:Bt}),s([19,56,59,60],[2,63]),s(i,[2,22],{15:[1,100],17:[1,99]}),s([40,50],[2,68]),s(i,[2,36]),{13:tt,41:et,45:101,46:60,48:st,49:it},s(i,[2,47]),s(i,[2,48]),s(i,[2,49]),s(ot,[2,39]),s(ot,[2,40]),s(U,[2,46]),s(i,[2,42]),s(i,[2,8]),s(i,[2,76]),s(i,[2,77]),s(i,[2,78]),{13:[1,102],42:W},{13:[1,104],15:[1,103]},{19:[1,105]},s(i,[2,15]),s(rt,[2,59],{55:106,58:[1,107],60:Bt}),s(rt,[2,60]),s(gt,[2,64]),s(rt,[2,67]),s(gt,[2,66]),{18:108,19:[1,109],51:50,52:51,56:q},{16:110,40:x,41:P},s(Pt,[2,44],{46:84,13:tt,41:et,48:st,49:it}),{14:111,40:lt,50:ht,70:ut},{16:112,40:x,41:P},{14:113,40:lt,50:ht,70:ut},s(i,[2,13]),s(rt,[2,61]),{57:114,59:Wt},{19:[1,115]},s(i,[2,20]),s(i,[2,23],{17:[1,116],42:W}),s(i,[2,11]),{13:[1,117],42:W},s(i,[2,10]),s(gt,[2,65]),s(i,[2,18]),{18:118,19:[1,119],51:50,52:51,56:q},{14:120,40:lt,50:ht,70:ut},{19:[1,121]},s(i,[2,21]),s(i,[2,9]),s(i,[2,19])],defaultActions:{52:[2,62],72:[2,57]},parseError:(0,a.eW)(function(n,c){if(c.recoverable)this.trace(n);else{var o=new Error(n);throw o.hash=c,o}},"parseError"),parse:(0,a.eW)(function(n){var c=this,o=[0],r=[],b=[null],t=[],nt=this.table,e="",dt=0,Lt=0,Vt=0,qt=2,wt=1,$t=t.slice.call(arguments,1),E=Object.create(this.lexer),L={yy:{}};for(var Ot in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Ot)&&(L.yy[Ot]=this.yy[Ot]);E.setInput(n,L.yy),L.yy.lexer=E,L.yy.parser=this,typeof E.yylloc=="undefined"&&(E.yylloc={});var Tt=E.yylloc;t.push(Tt);var te=E.options&&E.options.ranges;typeof L.yy.parseError=="function"?this.parseError=L.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function ee(T){o.length=o.length-2*T,b.length=b.length-T,t.length=t.length-T}(0,a.eW)(ee,"popStack");function Yt(){var T;return T=r.pop()||E.lex()||wt,typeof T!="number"&&(T instanceof Array&&(r=T,T=r.pop()),T=c.symbols_[T]||T),T}(0,a.eW)(Yt,"lex");for(var g,St,V,A,se,At,j={},bt,C,Ft,yt;;){if(V=o[o.length-1],this.defaultActions[V]?A=this.defaultActions[V]:((g===null||typeof g=="undefined")&&(g=Yt()),A=nt[V]&&nt[V][g]),typeof A=="undefined"||!A.length||!A[0]){var Rt="";yt=[];for(bt in nt[V])this.terminals_[bt]&&bt>qt&&yt.push("'"+this.terminals_[bt]+"'");E.showPosition?Rt="Parse error on line "+(dt+1)+`:
`+E.showPosition()+`
Expecting `+yt.join(", ")+", got '"+(this.terminals_[g]||g)+"'":Rt="Parse error on line "+(dt+1)+": Unexpected "+(g==wt?"end of input":"'"+(this.terminals_[g]||g)+"'"),this.parseError(Rt,{text:E.match,token:this.terminals_[g]||g,line:E.yylineno,loc:Tt,expected:yt})}if(A[0]instanceof Array&&A.length>1)throw new Error("Parse Error: multiple actions possible at state: "+V+", token: "+g);switch(A[0]){case 1:o.push(g),b.push(E.yytext),t.push(E.yylloc),o.push(A[1]),g=null,St?(g=St,St=null):(Lt=E.yyleng,e=E.yytext,dt=E.yylineno,Tt=E.yylloc,Vt>0&&Vt--);break;case 2:if(C=this.productions_[A[1]][1],j.$=b[b.length-C],j._$={first_line:t[t.length-(C||1)].first_line,last_line:t[t.length-1].last_line,first_column:t[t.length-(C||1)].first_column,last_column:t[t.length-1].last_column},te&&(j._$.range=[t[t.length-(C||1)].range[0],t[t.length-1].range[1]]),At=this.performAction.apply(j,[e,Lt,dt,L.yy,A[1],b,t].concat($t)),typeof At!="undefined")return At;C&&(o=o.slice(0,-1*C*2),b=b.slice(0,-1*C),t=t.slice(0,-1*C)),o.push(this.productions_[A[1]][0]),b.push(j.$),t.push(j._$),Ft=nt[o[o.length-2]][o[o.length-1]],o.push(Ft);break;case 3:return!0}}return!0},"parse")},Jt=function(){var B={EOF:1,parseError:(0,a.eW)(function(c,o){if(this.yy.parser)this.yy.parser.parseError(c,o);else throw new Error(c)},"parseError"),setInput:(0,a.eW)(function(n,c){return this.yy=c||this.yy||{},this._input=n,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,a.eW)(function(){var n=this._input[0];this.yytext+=n,this.yyleng++,this.offset++,this.match+=n,this.matched+=n;var c=n.match(/(?:\r\n?|\n).*/g);return c?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),n},"input"),unput:(0,a.eW)(function(n){var c=n.length,o=n.split(/(?:\r\n?|\n)/g);this._input=n+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c),this.offset-=c;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),o.length-1&&(this.yylineno-=o.length-1);var b=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:o?(o.length===r.length?this.yylloc.first_column:0)+r[r.length-o.length].length-o[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[b[0],b[0]+this.yyleng-c]),this.yyleng=this.yytext.length,this},"unput"),more:(0,a.eW)(function(){return this._more=!0,this},"more"),reject:(0,a.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,a.eW)(function(n){this.unput(this.match.slice(n))},"less"),pastInput:(0,a.eW)(function(){var n=this.matched.substr(0,this.matched.length-this.match.length);return(n.length>20?"...":"")+n.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,a.eW)(function(){var n=this.match;return n.length<20&&(n+=this._input.substr(0,20-n.length)),(n.substr(0,20)+(n.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,a.eW)(function(){var n=this.pastInput(),c=new Array(n.length+1).join("-");return n+this.upcomingInput()+`
`+c+"^"},"showPosition"),test_match:(0,a.eW)(function(n,c){var o,r,b;if(this.options.backtrack_lexer&&(b={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(b.yylloc.range=this.yylloc.range.slice(0))),r=n[0].match(/(?:\r\n?|\n).*/g),r&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+n[0].length},this.yytext+=n[0],this.match+=n[0],this.matches=n,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(n[0].length),this.matched+=n[0],o=this.performAction.call(this,this.yy,this,c,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o)return o;if(this._backtrack){for(var t in b)this[t]=b[t];return!1}return!1},"test_match"),next:(0,a.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var n,c,o,r;this._more||(this.yytext="",this.match="");for(var b=this._currentRules(),t=0;t<b.length;t++)if(o=this._input.match(this.rules[b[t]]),o&&(!c||o[0].length>c[0].length)){if(c=o,r=t,this.options.backtrack_lexer){if(n=this.test_match(o,b[t]),n!==!1)return n;if(this._backtrack){c=!1;continue}else return!1}else if(!this.options.flex)break}return c?(n=this.test_match(c,b[r]),n!==!1?n:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,a.eW)(function(){var c=this.next();return c||this.lex()},"lex"),begin:(0,a.eW)(function(c){this.conditionStack.push(c)},"begin"),popState:(0,a.eW)(function(){var c=this.conditionStack.length-1;return c>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,a.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,a.eW)(function(c){return c=this.conditionStack.length-1-Math.abs(c||0),c>=0?this.conditionStack[c]:"INITIAL"},"topState"),pushState:(0,a.eW)(function(c){this.begin(c)},"pushState"),stateStackSize:(0,a.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,a.eW)(function(c,o,r,b){var t=b;switch(r){case 0:return this.begin("acc_title"),24;break;case 1:return this.popState(),"acc_title_value";break;case 2:return this.begin("acc_descr"),26;break;case 3:return this.popState(),"acc_descr_value";break;case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return 33;case 8:return 34;case 9:return 35;case 10:return 36;case 11:return 10;case 12:break;case 13:return 8;case 14:return 50;case 15:return 70;case 16:return 4;case 17:return this.begin("block"),17;break;case 18:return 49;case 19:return 49;case 20:return 42;case 21:return 15;case 22:return 13;case 23:break;case 24:return 59;case 25:return 56;case 26:return 56;case 27:return 60;case 28:break;case 29:return this.popState(),19;break;case 30:return o.yytext[0];case 31:return 20;case 32:return 21;case 33:return this.begin("style"),44;break;case 34:return this.popState(),10;break;case 35:break;case 36:return 13;case 37:return 42;case 38:return 49;case 39:return this.begin("style"),37;break;case 40:return 43;case 41:return 63;case 42:return 65;case 43:return 65;case 44:return 65;case 45:return 63;case 46:return 63;case 47:return 64;case 48:return 64;case 49:return 64;case 50:return 64;case 51:return 64;case 52:return 65;case 53:return 64;case 54:return 65;case 55:return 66;case 56:return 66;case 57:return 66;case 58:return 66;case 59:return 63;case 60:return 64;case 61:return 65;case 62:return 67;case 63:return 68;case 64:return 69;case 65:return 69;case 66:return 68;case 67:return 68;case 68:return 68;case 69:return 41;case 70:return 47;case 71:return 40;case 72:return 48;case 73:return o.yytext[0];case 74:return 6}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:.*direction\s+TB[^\n]*)/i,/^(?:.*direction\s+BT[^\n]*)/i,/^(?:.*direction\s+RL[^\n]*)/i,/^(?:.*direction\s+LR[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:[\s]+)/i,/^(?:"[^"%\r\n\v\b\\]+")/i,/^(?:"[^"]*")/i,/^(?:erDiagram\b)/i,/^(?:\{)/i,/^(?:#)/i,/^(?:#)/i,/^(?:,)/i,/^(?::::)/i,/^(?::)/i,/^(?:\s+)/i,/^(?:\b((?:PK)|(?:FK)|(?:UK))\b)/i,/^(?:([^\s]*)[~].*[~]([^\s]*))/i,/^(?:([\*A-Za-z_\u00C0-\uFFFF][A-Za-z0-9\-\_\[\]\(\)\u00C0-\uFFFF\*]*))/i,/^(?:"[^"]*")/i,/^(?:[\n]+)/i,/^(?:\})/i,/^(?:.)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:style\b)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?::)/i,/^(?:,)/i,/^(?:#)/i,/^(?:classDef\b)/i,/^(?:class\b)/i,/^(?:one or zero\b)/i,/^(?:one or more\b)/i,/^(?:one or many\b)/i,/^(?:1\+)/i,/^(?:\|o\b)/i,/^(?:zero or one\b)/i,/^(?:zero or more\b)/i,/^(?:zero or many\b)/i,/^(?:0\+)/i,/^(?:\}o\b)/i,/^(?:many\(0\))/i,/^(?:many\(1\))/i,/^(?:many\b)/i,/^(?:\}\|)/i,/^(?:one\b)/i,/^(?:only one\b)/i,/^(?:1\b)/i,/^(?:\|\|)/i,/^(?:o\|)/i,/^(?:o\{)/i,/^(?:\|\{)/i,/^(?:\s*u\b)/i,/^(?:\.\.)/i,/^(?:--)/i,/^(?:to\b)/i,/^(?:optionally to\b)/i,/^(?:\.-)/i,/^(?:-\.)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:;)/i,/^(?:([^\x00-\x7F]|\w|-|\*)+)/i,/^(?:[0-9])/i,/^(?:.)/i,/^(?:$)/i],conditions:{style:{rules:[34,35,36,37,38,69,70],inclusive:!1},acc_descr_multiline:{rules:[5,6],inclusive:!1},acc_descr:{rules:[3],inclusive:!1},acc_title:{rules:[1],inclusive:!1},block:{rules:[23,24,25,26,27,28,29,30],inclusive:!1},INITIAL:{rules:[0,2,4,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,31,32,33,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,71,72,73,74],inclusive:!0}}};return B}();mt.lexer=Jt;function _t(){this.yy={}}return(0,a.eW)(_t,"Parser"),_t.prototype=mt,mt.Parser=_t,new _t}();pt.parser=pt;var jt=pt,Gt=(Y=class{constructor(){this.entities=new Map,this.relationships=[],this.classes=new Map,this.direction="TB",this.Cardinality={ZERO_OR_ONE:"ZERO_OR_ONE",ZERO_OR_MORE:"ZERO_OR_MORE",ONE_OR_MORE:"ONE_OR_MORE",ONLY_ONE:"ONLY_ONE",MD_PARENT:"MD_PARENT"},this.Identification={NON_IDENTIFYING:"NON_IDENTIFYING",IDENTIFYING:"IDENTIFYING"},this.setAccTitle=a.GN,this.getAccTitle=a.eu,this.setAccDescription=a.U$,this.getAccDescription=a.Mx,this.setDiagramTitle=a.g2,this.getDiagramTitle=a.Kr,this.getConfig=(0,a.eW)(()=>(0,a.nV)().er,"getConfig"),this.clear(),this.addEntity=this.addEntity.bind(this),this.addAttributes=this.addAttributes.bind(this),this.addRelationship=this.addRelationship.bind(this),this.setDirection=this.setDirection.bind(this),this.addCssStyles=this.addCssStyles.bind(this),this.addClass=this.addClass.bind(this),this.setClass=this.setClass.bind(this),this.setAccTitle=this.setAccTitle.bind(this),this.setAccDescription=this.setAccDescription.bind(this)}addEntity(i,h=""){var d,u;return this.entities.has(i)?!((u=this.entities.get(i))!=null&&u.alias)&&h&&(this.entities.get(i).alias=h,a.cM.info(`Add alias '${h}' to entity '${i}'`)):(this.entities.set(i,{id:`entity-${i}-${this.entities.size}`,label:i,attributes:[],alias:h,shape:"erBox",look:(d=(0,a.nV)().look)!=null?d:"default",cssClasses:"default",cssStyles:[]}),a.cM.info("Added new entity :",i)),this.entities.get(i)}getEntity(i){return this.entities.get(i)}getEntities(){return this.entities}getClasses(){return this.classes}addAttributes(i,h){const d=this.addEntity(i);let u;for(u=h.length-1;u>=0;u--)h[u].keys||(h[u].keys=[]),h[u].comment||(h[u].comment=""),d.attributes.push(h[u]),a.cM.debug("Added attribute ",h[u].name)}addRelationship(i,h,d,u){const l=this.entities.get(i),y=this.entities.get(d);if(!l||!y)return;const p={entityA:l.id,roleA:h,entityB:y.id,relSpec:u};this.relationships.push(p),a.cM.debug("Added new relationship :",p)}getRelationships(){return this.relationships}getDirection(){return this.direction}setDirection(i){this.direction=i}getCompiledStyles(i){var d,u;let h=[];for(const l of i){const y=this.classes.get(l);y!=null&&y.styles&&(h=[...h,...(d=y.styles)!=null?d:[]].map(p=>p.trim())),y!=null&&y.textStyles&&(h=[...h,...(u=y.textStyles)!=null?u:[]].map(p=>p.trim()))}return h}addCssStyles(i,h){for(const d of i){const u=this.entities.get(d);if(!h||!u)return;for(const l of h)u.cssStyles.push(l)}}addClass(i,h){i.forEach(d=>{let u=this.classes.get(d);u===void 0&&(u={id:d,styles:[],textStyles:[]},this.classes.set(d,u)),h&&h.forEach(function(l){if(/color/.exec(l)){const y=l.replace("fill","bgFill");u.textStyles.push(y)}u.styles.push(l)})})}setClass(i,h){for(const d of i){const u=this.entities.get(d);if(u)for(const l of h)u.cssClasses+=" "+l}}clear(){this.entities=new Map,this.classes=new Map,this.relationships=[],(0,a.ZH)()}getData(){const i=[],h=[],d=(0,a.nV)();for(const l of this.entities.keys()){const y=this.entities.get(l);y&&(y.cssCompiledStyles=this.getCompiledStyles(y.cssClasses.split(" ")),i.push(y))}let u=0;for(const l of this.relationships){const y={id:(0,Z.Ln)(l.entityA,l.entityB,{prefix:"id",counter:u++}),type:"normal",curve:"basis",start:l.entityA,end:l.entityB,label:l.roleA,labelpos:"c",thickness:"normal",classes:"relationshipLine",arrowTypeStart:l.relSpec.cardB.toLowerCase(),arrowTypeEnd:l.relSpec.cardA.toLowerCase(),pattern:l.relSpec.relType=="IDENTIFYING"?"solid":"dashed",look:d.look};h.push(y)}return{nodes:i,edges:h,other:{},config:d,direction:"TB"}}},(0,a.eW)(Y,"ErDB"),Y),Dt={};(0,a.r2)(Dt,{draw:()=>Zt});var Zt=(0,a.eW)(function(s,i,h,d){return Kt(this,null,function*(){var N,X;a.cM.info("REF0:"),a.cM.info("Drawing er diagram (unified)",i);const{securityLevel:u,er:l,layout:y}=(0,a.nV)(),p=d.db.getData(),I=(0,O.q)(i,u);p.type=d.type,p.layoutAlgorithm=(0,R._b)(y),p.config.flowchart.nodeSpacing=(l==null?void 0:l.nodeSpacing)||140,p.config.flowchart.rankSpacing=(l==null?void 0:l.rankSpacing)||80,p.direction=d.db.getDirection(),p.markers=["only_one","zero_or_one","one_or_more","zero_or_more"],p.diagramId=i,yield(0,R.sY)(p,I),p.layoutAlgorithm==="elk"&&I.select(".edges").lower();const z=I.selectAll('[id*="-background"]');Array.from(z).length>0&&z.each(function(){const H=(0,ft.Ys)(this),Q=H.attr("id").replace("-background",""),K=I.select(`#${CSS.escape(Q)}`);if(!K.empty()){const J=K.attr("transform");H.attr("transform",J)}});const ct=8;Z.w8.insertTitle(I,"erDiagramTitleText",(N=l==null?void 0:l.titleTopMargin)!=null?N:25,d.db.getDiagramTitle()),(0,S.j)(I,ct,"erDiagram",(X=l==null?void 0:l.useMaxWidth)!=null?X:!0)})},"draw"),zt=(0,a.eW)((s,i)=>{const h=at.Z,d=h(s,"r"),u=h(s,"g"),l=h(s,"b");return Ut.Z(d,u,l,i)},"fade"),Xt=(0,a.eW)(s=>`
  .entityBox {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
  }

  .relationshipLabelBox {
    fill: ${s.tertiaryColor};
    opacity: 0.7;
    background-color: ${s.tertiaryColor};
      rect {
        opacity: 0.5;
      }
  }

  .labelBkg {
    background-color: ${zt(s.tertiaryColor,.5)};
  }

  .edgeLabel .label {
    fill: ${s.nodeBorder};
    font-size: 14px;
  }

  .label {
    font-family: ${s.fontFamily};
    color: ${s.nodeTextColor||s.textColor};
  }

  .edge-pattern-dashed {
    stroke-dasharray: 8,8;
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon
  {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
    stroke-width: 1px;
  }

  .relationshipLine {
    stroke: ${s.lineColor};
    stroke-width: 1;
    fill: none;
  }

  .marker {
    fill: none !important;
    stroke: ${s.lineColor} !important;
    stroke-width: 1;
  }
`,"getStyles"),Ht=Xt,Qt={parser:jt,get db(){return new Gt},renderer:Dt,styles:Ht}}}]);
}());