"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5468],{86490:function(Ut,kt,H){H.d(kt,{diagram:function(){return jt}});var i=H(29134),R=H(69471),_t=H(65440),bt=H(12724),vt=H(5959),Y=function(){var n=(0,i.eW)(function(f,s,a,h){for(a=a||{},h=f.length;h--;a[f[h]]=s);return a},"o"),t=[6,8,10,11,12,14,16,17,20,21],e=[1,9],l=[1,10],r=[1,11],d=[1,12],c=[1,13],g=[1,16],m=[1,17],p={trace:(0,i.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,timeline:4,document:5,EOF:6,line:7,SPACE:8,statement:9,NEWLINE:10,title:11,acc_title:12,acc_title_value:13,acc_descr:14,acc_descr_value:15,acc_descr_multiline_value:16,section:17,period_statement:18,event_statement:19,period:20,event:21,$accept:0,$end:1},terminals_:{2:"error",4:"timeline",6:"EOF",8:"SPACE",10:"NEWLINE",11:"title",12:"acc_title",13:"acc_title_value",14:"acc_descr",15:"acc_descr_value",16:"acc_descr_multiline_value",17:"section",20:"period",21:"event"},productions_:[0,[3,3],[5,0],[5,2],[7,2],[7,1],[7,1],[7,1],[9,1],[9,2],[9,2],[9,1],[9,1],[9,1],[9,1],[18,1],[19,1]],performAction:(0,i.eW)(function(s,a,h,u,y,o,W){var k=o.length-1;switch(y){case 1:return o[k-1];case 2:this.$=[];break;case 3:o[k-1].push(o[k]),this.$=o[k-1];break;case 4:case 5:this.$=o[k];break;case 6:case 7:this.$=[];break;case 8:u.getCommonDb().setDiagramTitle(o[k].substr(6)),this.$=o[k].substr(6);break;case 9:this.$=o[k].trim(),u.getCommonDb().setAccTitle(this.$);break;case 10:case 11:this.$=o[k].trim(),u.getCommonDb().setAccDescription(this.$);break;case 12:u.addSection(o[k].substr(8)),this.$=o[k].substr(8);break;case 15:u.addTask(o[k],0,""),this.$=o[k];break;case 16:u.addEvent(o[k].substr(2)),this.$=o[k];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},n(t,[2,2],{5:3}),{6:[1,4],7:5,8:[1,6],9:7,10:[1,8],11:e,12:l,14:r,16:d,17:c,18:14,19:15,20:g,21:m},n(t,[2,7],{1:[2,1]}),n(t,[2,3]),{9:18,11:e,12:l,14:r,16:d,17:c,18:14,19:15,20:g,21:m},n(t,[2,5]),n(t,[2,6]),n(t,[2,8]),{13:[1,19]},{15:[1,20]},n(t,[2,11]),n(t,[2,12]),n(t,[2,13]),n(t,[2,14]),n(t,[2,15]),n(t,[2,16]),n(t,[2,4]),n(t,[2,9]),n(t,[2,10])],defaultActions:{},parseError:(0,i.eW)(function(s,a){if(a.recoverable)this.trace(s);else{var h=new Error(s);throw h.hash=a,h}},"parseError"),parse:(0,i.eW)(function(s){var a=this,h=[0],u=[],y=[null],o=[],W=this.table,k="",N=0,$=0,F=0,mt=2,U=1,G=o.slice.call(arguments,1),_=Object.create(this.lexer),M={yy:{}};for(var z in this.yy)Object.prototype.hasOwnProperty.call(this.yy,z)&&(M.yy[z]=this.yy[z]);_.setInput(s,M.yy),M.yy.lexer=_,M.yy.parser=this,typeof _.yylloc=="undefined"&&(_.yylloc={});var V=_.yylloc;o.push(V);var A=_.options&&_.options.ranges;typeof M.yy.parseError=="function"?this.parseError=M.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function w(S){h.length=h.length-2*S,y.length=y.length-S,o.length=o.length-S}(0,i.eW)(w,"popStack");function I(){var S;return S=u.pop()||_.lex()||U,typeof S!="number"&&(S instanceof Array&&(u=S,S=u.pop()),S=a.symbols_[S]||S),S}(0,i.eW)(I,"lex");for(var b,P,T,E,Zt,tt,j={},J,L,xt,Q;;){if(T=h[h.length-1],this.defaultActions[T]?E=this.defaultActions[T]:((b===null||typeof b=="undefined")&&(b=I()),E=W[T]&&W[T][b]),typeof E=="undefined"||!E.length||!E[0]){var et="";Q=[];for(J in W[T])this.terminals_[J]&&J>mt&&Q.push("'"+this.terminals_[J]+"'");_.showPosition?et="Parse error on line "+(N+1)+`:
`+_.showPosition()+`
Expecting `+Q.join(", ")+", got '"+(this.terminals_[b]||b)+"'":et="Parse error on line "+(N+1)+": Unexpected "+(b==U?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(et,{text:_.match,token:this.terminals_[b]||b,line:_.yylineno,loc:V,expected:Q})}if(E[0]instanceof Array&&E.length>1)throw new Error("Parse Error: multiple actions possible at state: "+T+", token: "+b);switch(E[0]){case 1:h.push(b),y.push(_.yytext),o.push(_.yylloc),h.push(E[1]),b=null,P?(b=P,P=null):($=_.yyleng,k=_.yytext,N=_.yylineno,V=_.yylloc,F>0&&F--);break;case 2:if(L=this.productions_[E[1]][1],j.$=y[y.length-L],j._$={first_line:o[o.length-(L||1)].first_line,last_line:o[o.length-1].last_line,first_column:o[o.length-(L||1)].first_column,last_column:o[o.length-1].last_column},A&&(j._$.range=[o[o.length-(L||1)].range[0],o[o.length-1].range[1]]),tt=this.performAction.apply(j,[k,$,N,M.yy,E[1],y,o].concat(G)),typeof tt!="undefined")return tt;L&&(h=h.slice(0,-1*L*2),y=y.slice(0,-1*L),o=o.slice(0,-1*L)),h.push(this.productions_[E[1]][0]),y.push(j.$),o.push(j._$),xt=W[h[h.length-2]][h[h.length-1]],h.push(xt);break;case 3:return!0}}return!0},"parse")},x=function(){var f={EOF:1,parseError:(0,i.eW)(function(a,h){if(this.yy.parser)this.yy.parser.parseError(a,h);else throw new Error(a)},"parseError"),setInput:(0,i.eW)(function(s,a){return this.yy=a||this.yy||{},this._input=s,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,i.eW)(function(){var s=this._input[0];this.yytext+=s,this.yyleng++,this.offset++,this.match+=s,this.matched+=s;var a=s.match(/(?:\r\n?|\n).*/g);return a?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),s},"input"),unput:(0,i.eW)(function(s){var a=s.length,h=s.split(/(?:\r\n?|\n)/g);this._input=s+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-a),this.offset-=a;var u=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),h.length-1&&(this.yylineno-=h.length-1);var y=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:h?(h.length===u.length?this.yylloc.first_column:0)+u[u.length-h.length].length-h[0].length:this.yylloc.first_column-a},this.options.ranges&&(this.yylloc.range=[y[0],y[0]+this.yyleng-a]),this.yyleng=this.yytext.length,this},"unput"),more:(0,i.eW)(function(){return this._more=!0,this},"more"),reject:(0,i.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,i.eW)(function(s){this.unput(this.match.slice(s))},"less"),pastInput:(0,i.eW)(function(){var s=this.matched.substr(0,this.matched.length-this.match.length);return(s.length>20?"...":"")+s.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,i.eW)(function(){var s=this.match;return s.length<20&&(s+=this._input.substr(0,20-s.length)),(s.substr(0,20)+(s.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,i.eW)(function(){var s=this.pastInput(),a=new Array(s.length+1).join("-");return s+this.upcomingInput()+`
`+a+"^"},"showPosition"),test_match:(0,i.eW)(function(s,a){var h,u,y;if(this.options.backtrack_lexer&&(y={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(y.yylloc.range=this.yylloc.range.slice(0))),u=s[0].match(/(?:\r\n?|\n).*/g),u&&(this.yylineno+=u.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:u?u[u.length-1].length-u[u.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+s[0].length},this.yytext+=s[0],this.match+=s[0],this.matches=s,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(s[0].length),this.matched+=s[0],h=this.performAction.call(this,this.yy,this,a,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),h)return h;if(this._backtrack){for(var o in y)this[o]=y[o];return!1}return!1},"test_match"),next:(0,i.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var s,a,h,u;this._more||(this.yytext="",this.match="");for(var y=this._currentRules(),o=0;o<y.length;o++)if(h=this._input.match(this.rules[y[o]]),h&&(!a||h[0].length>a[0].length)){if(a=h,u=o,this.options.backtrack_lexer){if(s=this.test_match(h,y[o]),s!==!1)return s;if(this._backtrack){a=!1;continue}else return!1}else if(!this.options.flex)break}return a?(s=this.test_match(a,y[u]),s!==!1?s:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,i.eW)(function(){var a=this.next();return a||this.lex()},"lex"),begin:(0,i.eW)(function(a){this.conditionStack.push(a)},"begin"),popState:(0,i.eW)(function(){var a=this.conditionStack.length-1;return a>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,i.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,i.eW)(function(a){return a=this.conditionStack.length-1-Math.abs(a||0),a>=0?this.conditionStack[a]:"INITIAL"},"topState"),pushState:(0,i.eW)(function(a){this.begin(a)},"pushState"),stateStackSize:(0,i.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,i.eW)(function(a,h,u,y){var o=y;switch(u){case 0:break;case 1:break;case 2:return 10;case 3:break;case 4:break;case 5:return 4;case 6:return 11;case 7:return this.begin("acc_title"),12;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.begin("acc_descr"),14;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.begin("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 17;case 15:return 21;case 16:return 20;case 17:return 6;case 18:return"INVALID"}},"anonymous"),rules:[/^(?:%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:[\n]+)/i,/^(?:\s+)/i,/^(?:#[^\n]*)/i,/^(?:timeline\b)/i,/^(?:title\s[^\n]+)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:[\}])/i,/^(?:[^\}]*)/i,/^(?:section\s[^:\n]+)/i,/^(?::\s(?:[^:\n]|:(?!\s))+)/i,/^(?:[^#:\n]+)/i,/^(?:$)/i,/^(?:.)/i],conditions:{acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,9,11,14,15,16,17,18],inclusive:!0}}};return f}();p.lexer=x;function v(){this.yy={}}return(0,i.eW)(v,"Parser"),v.prototype=p,p.Parser=v,new v}();Y.parser=Y;var wt=Y,nt={};(0,i.r2)(nt,{addEvent:()=>ht,addSection:()=>at,addTask:()=>ct,addTaskOrg:()=>dt,clear:()=>st,default:()=>Wt,getCommonDb:()=>it,getSections:()=>lt,getTasks:()=>ot});var O="",rt=0,X=[],Z=[],B=[],it=(0,i.eW)(()=>i.LJ,"getCommonDb"),st=(0,i.eW)(function(){X.length=0,Z.length=0,O="",B.length=0,(0,i.ZH)()},"clear"),at=(0,i.eW)(function(n){O=n,X.push(n)},"addSection"),lt=(0,i.eW)(function(){return X},"getSections"),ot=(0,i.eW)(function(){let n=ut();const t=100;let e=0;for(;!n&&e<t;)n=ut(),e++;return Z.push(...B),Z},"getTasks"),ct=(0,i.eW)(function(n,t,e){const l={id:rt++,section:O,type:O,task:n,score:t||0,events:e?[e]:[]};B.push(l)},"addTask"),ht=(0,i.eW)(function(n){B.find(e=>e.id===rt-1).events.push(n)},"addEvent"),dt=(0,i.eW)(function(n){const t={section:O,type:O,description:n,task:n,classes:[]};Z.push(t)},"addTaskOrg"),ut=(0,i.eW)(function(){const n=(0,i.eW)(function(e){return B[e].processed},"compileTask");let t=!0;for(const[e,l]of B.entries())n(e),t=t&&l.processed;return t},"compileTasks"),Wt={clear:st,getCommonDb:it,addSection:at,getSections:lt,getTasks:ot,addTask:ct,addTaskOrg:dt,addEvent:ht},Et=12,K=(0,i.eW)(function(n,t){const e=n.append("rect");return e.attr("x",t.x),e.attr("y",t.y),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("width",t.width),e.attr("height",t.height),e.attr("rx",t.rx),e.attr("ry",t.ry),t.class!==void 0&&e.attr("class",t.class),e},"drawRect"),St=(0,i.eW)(function(n,t){const l=n.append("circle").attr("cx",t.cx).attr("cy",t.cy).attr("class","face").attr("r",15).attr("stroke-width",2).attr("overflow","visible"),r=n.append("g");r.append("circle").attr("cx",t.cx-15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666"),r.append("circle").attr("cx",t.cx+15/3).attr("cy",t.cy-15/3).attr("r",1.5).attr("stroke-width",2).attr("fill","#666").attr("stroke","#666");function d(m){const p=(0,R.Nb1)().startAngle(Math.PI/2).endAngle(3*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+2)+")")}(0,i.eW)(d,"smile");function c(m){const p=(0,R.Nb1)().startAngle(3*Math.PI/2).endAngle(5*(Math.PI/2)).innerRadius(7.5).outerRadius(6.8181818181818175);m.append("path").attr("class","mouth").attr("d",p).attr("transform","translate("+t.cx+","+(t.cy+7)+")")}(0,i.eW)(c,"sad");function g(m){m.append("line").attr("class","mouth").attr("stroke",2).attr("x1",t.cx-5).attr("y1",t.cy+7).attr("x2",t.cx+5).attr("y2",t.cy+7).attr("class","mouth").attr("stroke-width","1px").attr("stroke","#666")}return(0,i.eW)(g,"ambivalent"),t.score>3?d(r):t.score<3?c(r):g(r),l},"drawFace"),Tt=(0,i.eW)(function(n,t){const e=n.append("circle");return e.attr("cx",t.cx),e.attr("cy",t.cy),e.attr("class","actor-"+t.pos),e.attr("fill",t.fill),e.attr("stroke",t.stroke),e.attr("r",t.r),e.class!==void 0&&e.attr("class",e.class),t.title!==void 0&&e.append("title").text(t.title),e},"drawCircle"),pt=(0,i.eW)(function(n,t){const e=t.text.replace(/<br\s*\/?>/gi," "),l=n.append("text");l.attr("x",t.x),l.attr("y",t.y),l.attr("class","legend"),l.style("text-anchor",t.anchor),t.class!==void 0&&l.attr("class",t.class);const r=l.append("tspan");return r.attr("x",t.x+t.textMargin*2),r.text(e),l},"drawText"),Mt=(0,i.eW)(function(n,t){function e(r,d,c,g,m){return r+","+d+" "+(r+c)+","+d+" "+(r+c)+","+(d+g-m)+" "+(r+c-m*1.2)+","+(d+g)+" "+r+","+(d+g)}(0,i.eW)(e,"genPoints");const l=n.append("polygon");l.attr("points",e(t.x,t.y,50,20,7)),l.attr("class","labelBox"),t.y=t.y+t.labelMargin,t.x=t.x+.5*t.labelMargin,pt(n,t)},"drawLabel"),It=(0,i.eW)(function(n,t,e){const l=n.append("g"),r=q();r.x=t.x,r.y=t.y,r.fill=t.fill,r.width=e.width,r.height=e.height,r.class="journey-section section-type-"+t.num,r.rx=3,r.ry=3,K(l,r),gt(e)(t.text,l,r.x,r.y,r.width,r.height,{class:"journey-section section-type-"+t.num},e,t.colour)},"drawSection"),yt=-1,Nt=(0,i.eW)(function(n,t,e){const l=t.x+e.width/2,r=n.append("g");yt++;const d=300+5*30;r.append("line").attr("id","task"+yt).attr("x1",l).attr("y1",t.y).attr("x2",l).attr("y2",d).attr("class","task-line").attr("stroke-width","1px").attr("stroke-dasharray","4 2").attr("stroke","#666"),St(r,{cx:l,cy:300+(5-t.score)*30,score:t.score});const c=q();c.x=t.x,c.y=t.y,c.fill=t.fill,c.width=e.width,c.height=e.height,c.class="task task-type-"+t.num,c.rx=3,c.ry=3,K(r,c),gt(e)(t.task,r,c.x,c.y,c.width,c.height,{class:"task"},e,t.colour)},"drawTask"),Pt=(0,i.eW)(function(n,t){K(n,{x:t.startx,y:t.starty,width:t.stopx-t.startx,height:t.stopy-t.starty,fill:t.fill,class:"rect"}).lower()},"drawBackgroundRect"),Lt=(0,i.eW)(function(){return{x:0,y:0,fill:void 0,"text-anchor":"start",width:100,height:100,textMargin:0,rx:0,ry:0}},"getTextObj"),q=(0,i.eW)(function(){return{x:0,y:0,width:100,anchor:"start",height:100,rx:0,ry:0}},"getNoteRect"),gt=function(){function n(r,d,c,g,m,p,x,v){const f=d.append("text").attr("x",c+m/2).attr("y",g+p/2+5).style("font-color",v).style("text-anchor","middle").text(r);l(f,x)}(0,i.eW)(n,"byText");function t(r,d,c,g,m,p,x,v,f){const{taskFontSize:s,taskFontFamily:a}=v,h=r.split(/<br\s*\/?>/gi);for(let u=0;u<h.length;u++){const y=u*s-s*(h.length-1)/2,o=d.append("text").attr("x",c+m/2).attr("y",g).attr("fill",f).style("text-anchor","middle").style("font-size",s).style("font-family",a);o.append("tspan").attr("x",c+m/2).attr("dy",y).text(h[u]),o.attr("y",g+p/2).attr("dominant-baseline","central").attr("alignment-baseline","central"),l(o,x)}}(0,i.eW)(t,"byTspan");function e(r,d,c,g,m,p,x,v){const f=d.append("switch"),a=f.append("foreignObject").attr("x",c).attr("y",g).attr("width",m).attr("height",p).attr("position","fixed").append("xhtml:div").style("display","table").style("height","100%").style("width","100%");a.append("div").attr("class","label").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(r),t(r,f,c,g,m,p,x,v),l(a,x)}(0,i.eW)(e,"byFo");function l(r,d){for(const c in d)c in d&&r.attr(c,d[c])}return(0,i.eW)(l,"_setTextAttrs"),function(r){return r.textPlacement==="fo"?e:r.textPlacement==="old"?n:t}}(),At=(0,i.eW)(function(n){n.append("defs").append("marker").attr("id","arrowhead").attr("refX",5).attr("refY",2).attr("markerWidth",6).attr("markerHeight",4).attr("orient","auto").append("path").attr("d","M 0,0 V 4 L6,2 Z")},"initGraphics");function D(n,t){n.each(function(){var e=(0,R.Ys)(this),l=e.text().split(/(\s+|<br>)/).reverse(),r,d=[],c=1.1,g=e.attr("y"),m=parseFloat(e.attr("dy")),p=e.text(null).append("tspan").attr("x",0).attr("y",g).attr("dy",m+"em");for(let x=0;x<l.length;x++)r=l[l.length-1-x],d.push(r),p.text(d.join(" ").trim()),(p.node().getComputedTextLength()>t||r==="<br>")&&(d.pop(),p.text(d.join(" ").trim()),r==="<br>"?d=[""]:d=[r],p=e.append("tspan").attr("x",0).attr("y",g).attr("dy",c+"em").text(r))})}(0,i.eW)(D,"wrap");var Ct=(0,i.eW)(function(n,t,e,l){var v;const r=e%Et-1,d=n.append("g");t.section=r,d.attr("class",(t.class?t.class+" ":"")+"timeline-node "+("section-"+r));const c=d.append("g"),g=d.append("g"),p=g.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,t.width).node().getBBox(),x=(v=l.fontSize)!=null&&v.replace?l.fontSize.replace("px",""):l.fontSize;return t.height=p.height+x*1.1*.5+t.padding,t.height=Math.max(t.height,t.maxHeight),t.width=t.width+2*t.padding,g.attr("transform","translate("+t.width/2+", "+t.padding/2+")"),Ht(c,t,r,l),t},"drawNode"),$t=(0,i.eW)(function(n,t,e){var g;const l=n.append("g"),d=l.append("text").text(t.descr).attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle").call(D,t.width).node().getBBox(),c=(g=e.fontSize)!=null&&g.replace?e.fontSize.replace("px",""):e.fontSize;return l.remove(),d.height+c*1.1*.5+t.padding},"getVirtualNodeHeight"),Ht=(0,i.eW)(function(n,t,e){n.append("path").attr("id","node-"+t.id).attr("class","node-bkg node-"+t.type).attr("d",`M0 ${t.height-5} v${-t.height+2*5} q0,-5 5,-5 h${t.width-2*5} q5,0 5,5 v${t.height-5} H0 Z`),n.append("line").attr("class","node-line-"+e).attr("x1",0).attr("y1",t.height).attr("x2",t.width).attr("y2",t.height)},"defaultBkg"),C={drawRect:K,drawCircle:Tt,drawSection:It,drawText:pt,drawLabel:Mt,drawTask:Nt,drawBackgroundRect:Pt,getTextObj:Lt,getNoteRect:q,initGraphics:At,drawNode:Ct,getVirtualNodeHeight:$t},Rt=(0,i.eW)(function(n,t,e,l){var U,G,_,M,z,V;const r=(0,i.nV)(),d=(G=(U=r.timeline)==null?void 0:U.leftMargin)!=null?G:50;i.cM.debug("timeline",l.db);const c=r.securityLevel;let g;c==="sandbox"&&(g=(0,R.Ys)("#i"+t));const p=(c==="sandbox"?(0,R.Ys)(g.nodes()[0].contentDocument.body):(0,R.Ys)("body")).select("#"+t);p.append("g");const x=l.db.getTasks(),v=l.db.getCommonDb().getDiagramTitle();i.cM.debug("task",x),C.initGraphics(p);const f=l.db.getSections();i.cM.debug("sections",f);let s=0,a=0,h=0,u=0,y=50+d,o=50;u=50;let W=0,k=!0;f.forEach(function(A){const w={number:W,descr:A,section:W,width:150,padding:20,maxHeight:s},I=C.getVirtualNodeHeight(p,w,r);i.cM.debug("sectionHeight before draw",I),s=Math.max(s,I+20)});let N=0,$=0;i.cM.debug("tasks.length",x.length);for(const[A,w]of x.entries()){const I={number:A,descr:w,section:w.section,width:150,padding:20,maxHeight:a},b=C.getVirtualNodeHeight(p,I,r);i.cM.debug("taskHeight before draw",b),a=Math.max(a,b+20),N=Math.max(N,w.events.length);let P=0;for(const T of w.events){const E={descr:T,section:w.section,number:w.section,width:150,padding:20,maxHeight:50};P+=C.getVirtualNodeHeight(p,E,r)}w.events.length>0&&(P+=(w.events.length-1)*10),$=Math.max($,P)}i.cM.debug("maxSectionHeight before draw",s),i.cM.debug("maxTaskHeight before draw",a),f&&f.length>0?f.forEach(A=>{const w=x.filter(T=>T.section===A),I={number:W,descr:A,section:W,width:200*Math.max(w.length,1)-50,padding:20,maxHeight:s};i.cM.debug("sectionNode",I);const b=p.append("g"),P=C.drawNode(b,I,W,r);i.cM.debug("sectionNode output",P),b.attr("transform",`translate(${y}, ${u})`),o+=s+50,w.length>0&&ft(p,w,W,y,o,a,r,N,$,s,!1),y+=200*Math.max(w.length,1),o=u,W++}):(k=!1,ft(p,x,W,y,o,a,r,N,$,s,!0));const F=p.node().getBBox();i.cM.debug("bounds",F),v&&p.append("text").text(v).attr("x",F.width/2-d).attr("font-size","4ex").attr("font-weight","bold").attr("y",20),h=k?s+a+150:a+100,p.append("g").attr("class","lineWrapper").append("line").attr("x1",d).attr("y1",h).attr("x2",F.width+3*d).attr("y2",h).attr("stroke-width",4).attr("stroke","black").attr("marker-end","url(#arrowhead)"),(0,i.j7)(void 0,p,(M=(_=r.timeline)==null?void 0:_.padding)!=null?M:50,(V=(z=r.timeline)==null?void 0:z.useMaxWidth)!=null?V:!1)},"draw"),ft=(0,i.eW)(function(n,t,e,l,r,d,c,g,m,p,x){var v;for(const f of t){const s={descr:f.task,section:e,number:e,width:150,padding:20,maxHeight:d};i.cM.debug("taskNode",s);const a=n.append("g").attr("class","taskWrapper"),u=C.drawNode(a,s,e,c).height;if(i.cM.debug("taskHeight after draw",u),a.attr("transform",`translate(${l}, ${r})`),d=Math.max(d,u),f.events){const y=n.append("g").attr("class","lineWrapper");let o=d;r+=100,o=o+Ot(n,f.events,e,l,r,c),r-=100,y.append("line").attr("x1",l+190/2).attr("y1",r+d).attr("x2",l+190/2).attr("y2",r+d+100+m+100).attr("stroke-width",2).attr("stroke","black").attr("marker-end","url(#arrowhead)").attr("stroke-dasharray","5,5")}l=l+200,x&&!((v=c.timeline)!=null&&v.disableMulticolor)&&e++}r=r-10},"drawTasks"),Ot=(0,i.eW)(function(n,t,e,l,r,d){let c=0;const g=r;r=r+100;for(const m of t){const p={descr:m,section:e,number:e,width:150,padding:20,maxHeight:50};i.cM.debug("eventNode",p);const x=n.append("g").attr("class","eventWrapper"),f=C.drawNode(x,p,e,d).height;c=c+f,x.attr("transform",`translate(${l}, ${r})`),r=r+10+f}return r=g,c},"drawEvents"),Bt={setConf:(0,i.eW)(()=>{},"setConf"),draw:Rt},Ft=(0,i.eW)(n=>{let t="";for(let e=0;e<n.THEME_COLOR_LIMIT;e++)n["lineColor"+e]=n["lineColor"+e]||n["cScaleInv"+e],(0,_t.Z)(n["lineColor"+e])?n["lineColor"+e]=(0,bt.Z)(n["lineColor"+e],20):n["lineColor"+e]=(0,vt.Z)(n["lineColor"+e],20);for(let e=0;e<n.THEME_COLOR_LIMIT;e++){const l=""+(17-3*e);t+=`
    .section-${e-1} rect, .section-${e-1} path, .section-${e-1} circle, .section-${e-1} path  {
      fill: ${n["cScale"+e]};
    }
    .section-${e-1} text {
     fill: ${n["cScaleLabel"+e]};
    }
    .node-icon-${e-1} {
      font-size: 40px;
      color: ${n["cScaleLabel"+e]};
    }
    .section-edge-${e-1}{
      stroke: ${n["cScale"+e]};
    }
    .edge-depth-${e-1}{
      stroke-width: ${l};
    }
    .section-${e-1} line {
      stroke: ${n["cScaleInv"+e]} ;
      stroke-width: 3;
    }

    .lineWrapper line{
      stroke: ${n["cScaleLabel"+e]} ;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return t},"genSections"),zt=(0,i.eW)(n=>`
  .edge {
    stroke-width: 3;
  }
  ${Ft(n)}
  .section-root rect, .section-root path, .section-root circle  {
    fill: ${n.git0};
  }
  .section-root text {
    fill: ${n.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .eventWrapper  {
   filter: brightness(120%);
  }
`,"getStyles"),Vt=zt,jt={db:nt,renderer:Bt,parser:wt,styles:Vt}}}]);
