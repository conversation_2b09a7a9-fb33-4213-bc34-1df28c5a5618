"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[7840],{47840:function(K,L,c){c.d(L,{bK:function(){return Xt}});var d=c(24285),P=c(9694),E=0;function y(e){var n=++E;return(0,P.Z)(e)+n}var C=y,x=c(61606),N=c(42060),g=c(857),R=Math.ceil,Y=Math.max;function F(e,n,r,t){for(var i=-1,a=Y(R((n-e)/(r||1)),0),o=Array(a);a--;)o[t?a:++i]=e,e+=r;return o}var U=F,W=c(14026),k=c(53689);function B(e){return function(n,r,t){return t&&typeof t!="number"&&(0,W.Z)(n,r,t)&&(r=t=void 0),n=(0,k.Z)(n),r===void 0?(r=n,n=0):r=(0,k.Z)(r),t=t===void 0?n<r?1:-1:(0,k.Z)(t),U(n,r,t,e)}}var V=B,M=V(),D=M,A=c(56561);class m{constructor(){var n={};n._next=n._prev=n,this._sentinel=n}dequeue(){var n=this._sentinel,r=n._prev;if(r!==n)return G(r),r}enqueue(n){var r=this._sentinel;n._prev&&n._next&&G(n),n._next=r._next,r._next._prev=n,r._next=n,n._prev=r}toString(){for(var n=[],r=this._sentinel,t=r._prev;t!==r;)n.push(JSON.stringify(t,X)),t=t._prev;return"["+n.join(", ")+"]"}}function G(e){e._prev._next=e._next,e._next._prev=e._prev,delete e._next,delete e._prev}function X(e,n){if(e!=="_next"&&e!=="_prev")return n}var ve=x.Z(1);function re(e,n){if(e.nodeCount()<=1)return[];var r=v(e,n||ve),t=z(r.graph,r.buckets,r.zeroIdx);return N.Z(g.Z(t,function(i){return e.outEdges(i.v,i.w)}))}function z(e,n,r){for(var t=[],i=n[n.length-1],a=n[0],o;e.nodeCount();){for(;o=a.dequeue();)h(e,n,r,o);for(;o=i.dequeue();)h(e,n,r,o);if(e.nodeCount()){for(var s=n.length-2;s>0;--s)if(o=n[s].dequeue(),o){t=t.concat(h(e,n,r,o,!0));break}}}return t}function h(e,n,r,t,i){var a=i?[]:void 0;return d.Z(e.inEdges(t.v),function(o){var s=e.edge(o),u=e.node(o.v);i&&a.push({v:o.v,w:o.w}),u.out-=s,Z(n,r,u)}),d.Z(e.outEdges(t.v),function(o){var s=e.edge(o),u=o.w,f=e.node(u);f.in-=s,Z(n,r,f)}),e.removeNode(t.v),a}function v(e,n){var r=new A.k,t=0,i=0;d.Z(e.nodes(),function(s){r.setNode(s,{v:s,in:0,out:0})}),d.Z(e.edges(),function(s){var u=r.edge(s.v,s.w)||0,f=n(s),l=u+f;r.setEdge(s.v,s.w,l),i=Math.max(i,r.node(s.v).out+=f),t=Math.max(t,r.node(s.w).in+=f)});var a=D(i+t+3).map(function(){return new m}),o=t+1;return d.Z(r.nodes(),function(s){Z(a,o,r.node(s))}),{graph:r,buckets:a,zeroIdx:o}}function Z(e,n,r){r.out?r.in?e[r.out-r.in+n].enqueue(r):e[e.length-1].enqueue(r):e[0].enqueue(r)}function w(e){var n=e.graph().acyclicer==="greedy"?re(e,r(e)):I(e);d.Z(n,function(t){var i=e.edge(t);e.removeEdge(t),i.forwardName=t.name,i.reversed=!0,e.setEdge(t.w,t.v,i,C("rev"))});function r(t){return function(i){return t.edge(i).weight}}}function I(e){var n=[],r={},t={};function i(a){Object.prototype.hasOwnProperty.call(t,a)||(t[a]=!0,r[a]=!0,d.Z(e.outEdges(a),function(o){Object.prototype.hasOwnProperty.call(r,o.w)?n.push(o):i(o.w)}),delete r[a])}return d.Z(e.nodes(),i),n}function T(e){d.Z(e.edges(),function(n){var r=e.edge(n);if(r.reversed){e.removeEdge(n);var t=r.forwardName;delete r.reversed,delete r.forwardName,e.setEdge(n.w,n.v,r,t)}})}var S=c(85830),$=c(70648),vn=c(8922);function pn(e,n){return(0,$.Z)(e,n,function(r,t){return(0,vn.Z)(e,t)})}var mn=pn,Zn=c(46262),gn=c(20359);function bn(e){return(0,gn.Z)((0,Zn.Z)(e,void 0,N.Z),e+"")}var wn=bn,En=wn(function(e,n){return e==null?{}:mn(e,n)}),de=En,_n=c(35565),Ce=c(71818);function yn(e,n){return e>n}var On=yn,Ne=c(35272);function Pn(e){return e&&e.length?(0,Ce.Z)(e,Ne.Z,On):void 0}var Q=Pn,ue=c(7683),xn=c(81186),Ie=c(98612),pe=c(18458);function Ln(e,n){var r={};return n=(0,pe.Z)(n,3),(0,Ie.Z)(e,function(t,i,a){(0,xn.Z)(r,i,n(t,i,a))}),r}var fe=Ln,J=c(80155),te=c(93422),Me=c(67876),Cn=c(47627),Nn=function(){return Cn.Z.Date.now()},Re=Nn;function ee(e,n,r,t){var i;do i=C(t);while(e.hasNode(i));return r.dummy=n,e.setNode(i,r),i}function In(e){var n=new A.k().setGraph(e.graph());return d.Z(e.nodes(),function(r){n.setNode(r,e.node(r))}),d.Z(e.edges(),function(r){var t=n.edge(r.v,r.w)||{weight:0,minlen:1},i=e.edge(r);n.setEdge(r.v,r.w,{weight:t.weight+i.weight,minlen:Math.max(t.minlen,i.minlen)})}),n}function Te(e){var n=new A.k({multigraph:e.isMultigraph()}).setGraph(e.graph());return d.Z(e.nodes(),function(r){e.children(r).length||n.setNode(r,e.node(r))}),d.Z(e.edges(),function(r){n.setEdge(r,e.edge(r))}),n}function bi(e){var n=_.map(e.nodes(),function(r){var t={};return _.forEach(e.outEdges(r),function(i){t[i.w]=(t[i.w]||0)+e.edge(i).weight}),t});return _.zipObject(e.nodes(),n)}function wi(e){var n=_.map(e.nodes(),function(r){var t={};return _.forEach(e.inEdges(r),function(i){t[i.v]=(t[i.v]||0)+e.edge(i).weight}),t});return _.zipObject(e.nodes(),n)}function je(e,n){var r=e.x,t=e.y,i=n.x-r,a=n.y-t,o=e.width/2,s=e.height/2;if(!i&&!a)throw new Error("Not possible to find intersection inside of the rectangle");var u,f;return Math.abs(a)*o>Math.abs(i)*s?(a<0&&(s=-s),u=s*i/a,f=s):(i<0&&(o=-o),u=o,f=o*a/i),{x:r+u,y:t+f}}function he(e){var n=g.Z(D(De(e)+1),function(){return[]});return d.Z(e.nodes(),function(r){var t=e.node(r),i=t.rank;J.Z(i)||(n[i][t.order]=r)}),n}function Mn(e){var n=te.Z(g.Z(e.nodes(),function(r){return e.node(r).rank}));d.Z(e.nodes(),function(r){var t=e.node(r);Me.Z(t,"rank")&&(t.rank-=n)})}function Rn(e){var n=te.Z(g.Z(e.nodes(),function(a){return e.node(a).rank})),r=[];d.Z(e.nodes(),function(a){var o=e.node(a).rank-n;r[o]||(r[o]=[]),r[o].push(a)});var t=0,i=e.graph().nodeRankFactor;d.Z(r,function(a,o){J.Z(a)&&o%i!==0?--t:t&&d.Z(a,function(s){e.node(s).rank+=t})})}function ke(e,n,r,t){var i={width:0,height:0};return arguments.length>=4&&(i.rank=r,i.order=t),ee(e,"border",i,n)}function De(e){return Q(g.Z(e.nodes(),function(n){var r=e.node(n).rank;if(!J.Z(r))return r}))}function Tn(e,n){var r={lhs:[],rhs:[]};return d.Z(e,function(t){n(t)?r.lhs.push(t):r.rhs.push(t)}),r}function jn(e,n){var r=Re();try{return n()}finally{console.log(e+" time: "+(Re()-r)+"ms")}}function kn(e,n){return n()}function Dn(e){function n(r){var t=e.children(r),i=e.node(r);if(t.length&&d.Z(t,n),Object.prototype.hasOwnProperty.call(i,"minRank")){i.borderLeft=[],i.borderRight=[];for(var a=i.minRank,o=i.maxRank+1;a<o;++a)Ae(e,"borderLeft","_bl",r,i,a),Ae(e,"borderRight","_br",r,i,a)}}d.Z(e.children(),n)}function Ae(e,n,r,t,i,a){var o={width:0,height:0,rank:a,borderType:n},s=i[n][a-1],u=ee(e,"border",o,r);i[n][a]=u,e.setParent(u,t),s&&e.setEdge(s,u,{weight:1})}function An(e){var n=e.graph().rankdir.toLowerCase();(n==="lr"||n==="rl")&&Se(e)}function Sn(e){var n=e.graph().rankdir.toLowerCase();(n==="bt"||n==="rl")&&Fn(e),(n==="lr"||n==="rl")&&(Bn(e),Se(e))}function Se(e){d.Z(e.nodes(),function(n){Fe(e.node(n))}),d.Z(e.edges(),function(n){Fe(e.edge(n))})}function Fe(e){var n=e.width;e.width=e.height,e.height=n}function Fn(e){d.Z(e.nodes(),function(n){me(e.node(n))}),d.Z(e.edges(),function(n){var r=e.edge(n);d.Z(r.points,me),Object.prototype.hasOwnProperty.call(r,"y")&&me(r)})}function me(e){e.y=-e.y}function Bn(e){d.Z(e.nodes(),function(n){Ze(e.node(n))}),d.Z(e.edges(),function(n){var r=e.edge(n);d.Z(r.points,Ze),Object.prototype.hasOwnProperty.call(r,"x")&&Ze(r)})}function Ze(e){var n=e.x;e.x=e.y,e.y=n}function Wn(e){e.graph().dummyChains=[],d.Z(e.edges(),function(n){Un(e,n)})}function Un(e,n){var r=n.v,t=e.node(r).rank,i=n.w,a=e.node(i).rank,o=n.name,s=e.edge(n),u=s.labelRank;if(a!==t+1){e.removeEdge(n);var f=void 0,l,p;for(p=0,++t;t<a;++p,++t)s.points=[],f={width:0,height:0,edgeLabel:s,edgeObj:n,rank:t},l=ee(e,"edge",f,"_d"),t===u&&(f.width=s.width,f.height=s.height,f.dummy="edge-label",f.labelpos=s.labelpos),e.setEdge(r,l,{weight:s.weight},o),p===0&&e.graph().dummyChains.push(l),r=l;e.setEdge(r,i,{weight:s.weight},o)}}function Vn(e){d.Z(e.graph().dummyChains,function(n){var r=e.node(n),t=r.edgeLabel,i;for(e.setEdge(r.edgeObj,t);r.dummy;)i=e.successors(n)[0],e.removeNode(n),t.points.push({x:r.x,y:r.y}),r.dummy==="edge-label"&&(t.x=r.x,t.y=r.y,t.width=r.width,t.height=r.height),n=i,r=e.node(n)})}var Gn=c(65052);function Kn(e,n){return e&&e.length?(0,Ce.Z)(e,(0,pe.Z)(n,2),Gn.Z):void 0}var ge=Kn;function be(e){var n={};function r(t){var i=e.node(t);if(Object.prototype.hasOwnProperty.call(n,t))return i.rank;n[t]=!0;var a=te.Z(g.Z(e.outEdges(t),function(o){return r(o.w)-e.edge(o).minlen}));return(a===Number.POSITIVE_INFINITY||a===void 0||a===null)&&(a=0),i.rank=a}d.Z(e.sources(),r)}function ie(e,n){return e.node(n.w).rank-e.node(n.v).rank-e.edge(n).minlen}function Be(e){var n=new A.k({directed:!1}),r=e.nodes()[0],t=e.nodeCount();n.setNode(r,{});for(var i,a;Yn(n,e)<t;)i=zn(n,e),a=n.hasNode(i.v)?ie(e,i):-ie(e,i),Hn(n,e,a);return n}function Yn(e,n){function r(t){d.Z(n.nodeEdges(t),function(i){var a=i.v,o=t===a?i.w:a;!e.hasNode(o)&&!ie(n,i)&&(e.setNode(o,{}),e.setEdge(t,o,{}),r(o))})}return d.Z(e.nodes(),r),e.nodeCount()}function zn(e,n){return ge(n.edges(),function(r){if(e.hasNode(r.v)!==e.hasNode(r.w))return ie(n,r)})}function Hn(e,n,r){d.Z(e.nodes(),function(t){n.node(t).rank+=r})}var we=c(89045),ae=c(97175),Xn=x.Z(1);function Ei(e,n,r,t){return Jn(e,String(n),r||Xn,t||function(i){return e.outEdges(i)})}function Jn(e,n,r,t){var i={},a=new PriorityQueue,o,s,u=function(f){var l=f.v!==o?f.v:f.w,p=i[l],b=r(f),O=s.distance+b;if(b<0)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+f+" Weight: "+b);O<p.distance&&(p.distance=O,p.predecessor=o,a.decrease(l,O))};for(e.nodes().forEach(function(f){var l=f===n?0:Number.POSITIVE_INFINITY;i[f]={distance:l},a.add(f,l)});a.size()>0&&(o=a.removeMin(),s=i[o],s.distance!==Number.POSITIVE_INFINITY);)t(o).forEach(u);return i}function _i(e,n,r){return _.transform(e.nodes(),function(t,i){t[i]=dijkstra(e,i,n,r)},{})}var $n=x.Z(1);function yi(e,n,r){return Qn(e,n||$n,r||function(t){return e.outEdges(t)})}function Qn(e,n,r){var t={},i=e.nodes();return i.forEach(function(a){t[a]={},t[a][a]={distance:0},i.forEach(function(o){a!==o&&(t[a][o]={distance:Number.POSITIVE_INFINITY})}),r(a).forEach(function(o){var s=o.v===a?o.w:o.v,u=n(o);t[a][s]={distance:u,predecessor:a}})}),i.forEach(function(a){var o=t[a];i.forEach(function(s){var u=t[s];i.forEach(function(f){var l=u[a],p=o[f],b=u[f],O=l.distance+p.distance;O<b.distance&&(b.distance=O,b.predecessor=p.predecessor)})})}),t}var qn=c(30498),er=c(78308),nr=c(13932),rr=c(56288),tr=c(64814),ir=(0,tr.Z)("length"),ar=ir,or=c(62126),We="\\ud800-\\udfff",sr="\\u0300-\\u036f",dr="\\ufe20-\\ufe2f",ur="\\u20d0-\\u20ff",fr=sr+dr+ur,hr="\\ufe0e\\ufe0f",cr="["+We+"]",Ee="["+fr+"]",_e="\\ud83c[\\udffb-\\udfff]",lr="(?:"+Ee+"|"+_e+")",Ue="[^"+We+"]",Ve="(?:\\ud83c[\\udde6-\\uddff]){2}",Ge="[\\ud800-\\udbff][\\udc00-\\udfff]",vr="\\u200d",Ke=lr+"?",Ye="["+hr+"]?",pr="(?:"+vr+"(?:"+[Ue,Ve,Ge].join("|")+")"+Ye+Ke+")*",mr=Ye+Ke+pr,Zr="(?:"+[Ue+Ee+"?",Ee,Ve,Ge,cr].join("|")+")",ze=RegExp(_e+"(?="+_e+")|"+Zr+mr,"g");function gr(e){for(var n=ze.lastIndex=0;ze.test(e);)++n;return n}var br=gr;function wr(e){return(0,or.Z)(e)?br(e):ar(e)}var Er=wr,_r="[object Map]",yr="[object Set]";function Or(e){if(e==null)return 0;if((0,nr.Z)(e))return(0,rr.Z)(e)?Er(e):e.length;var n=(0,er.Z)(e);return n==_r||n==yr?e.size:(0,qn.Z)(e).length}var Pr=Or;xr.CycleException=ce;function xr(e){var n={},r={},t=[];function i(a){if(Object.prototype.hasOwnProperty.call(r,a))throw new ce;Object.prototype.hasOwnProperty.call(n,a)||(r[a]=!0,n[a]=!0,d.Z(e.predecessors(a),i),delete r[a],t.push(a))}if(d.Z(e.sinks(),i),Pr(n)!==e.nodeCount())throw new ce;return t}function ce(){}ce.prototype=new Error;function Oi(e){try{topsort(e)}catch(n){if(n instanceof CycleException)return!1;throw n}return!0}var He=c(83788);function Xe(e,n,r){He.Z(n)||(n=[n]);var t=(e.isDirected()?e.successors:e.neighbors).bind(e),i=[],a={};return d.Z(n,function(o){if(!e.hasNode(o))throw new Error("Graph does not have node: "+o);Je(e,o,r==="post",a,t,i)}),i}function Je(e,n,r,t,i,a){Object.prototype.hasOwnProperty.call(t,n)||(t[n]=!0,r||a.push(n),d.Z(i(n),function(o){Je(e,o,r,t,i,a)}),r&&a.push(n))}function Lr(e,n){return Xe(e,n,"post")}function Cr(e,n){return Xe(e,n,"pre")}var Pi=c(55133);function xi(e,n){var r=new Graph,t={},i=new PriorityQueue,a;function o(u){var f=u.v===a?u.w:u.v,l=i.priority(f);if(l!==void 0){var p=n(u);p<l&&(t[f]=a,i.decrease(f,p))}}if(e.nodeCount()===0)return r;_.each(e.nodes(),function(u){i.add(u,Number.POSITIVE_INFINITY),r.setNode(u)}),i.decrease(e.nodes()[0],0);for(var s=!1;i.size()>0;){if(a=i.removeMin(),Object.prototype.hasOwnProperty.call(t,a))r.setEdge(a,t[a]);else{if(s)throw new Error("Input graph is not connected: "+e);s=!0}e.nodeEdges(a).forEach(o)}return r}q.initLowLimValues=Oe,q.initCutValues=ye,q.calcCutValue=$e,q.leaveEdge=qe,q.enterEdge=en,q.exchangeEdges=nn;function q(e){e=In(e),be(e);var n=Be(e);Oe(n),ye(n,e);for(var r,t;r=qe(n);)t=en(n,e,r),nn(n,e,r,t)}function ye(e,n){var r=Lr(e,e.nodes());r=r.slice(0,r.length-1),d.Z(r,function(t){Nr(e,n,t)})}function Nr(e,n,r){var t=e.node(r),i=t.parent;e.edge(r,i).cutvalue=$e(e,n,r)}function $e(e,n,r){var t=e.node(r),i=t.parent,a=!0,o=n.edge(r,i),s=0;return o||(a=!1,o=n.edge(i,r)),s=o.weight,d.Z(n.nodeEdges(r),function(u){var f=u.v===r,l=f?u.w:u.v;if(l!==i){var p=f===a,b=n.edge(u).weight;if(s+=p?b:-b,Mr(e,r,l)){var O=e.edge(r,l).cutvalue;s+=p?-O:O}}}),s}function Oe(e,n){arguments.length<2&&(n=e.nodes()[0]),Qe(e,{},1,n)}function Qe(e,n,r,t,i){var a=r,o=e.node(t);return n[t]=!0,d.Z(e.neighbors(t),function(s){Object.prototype.hasOwnProperty.call(n,s)||(r=Qe(e,n,r,s,t))}),o.low=a,o.lim=r++,i?o.parent=i:delete o.parent,r}function qe(e){return we.Z(e.edges(),function(n){return e.edge(n).cutvalue<0})}function en(e,n,r){var t=r.v,i=r.w;n.hasEdge(t,i)||(t=r.w,i=r.v);var a=e.node(t),o=e.node(i),s=a,u=!1;a.lim>o.lim&&(s=o,u=!0);var f=ae.Z(n.edges(),function(l){return u===rn(e,e.node(l.v),s)&&u!==rn(e,e.node(l.w),s)});return ge(f,function(l){return ie(n,l)})}function nn(e,n,r,t){var i=r.v,a=r.w;e.removeEdge(i,a),e.setEdge(t.v,t.w,{}),Oe(e),ye(e,n),Ir(e,n)}function Ir(e,n){var r=we.Z(e.nodes(),function(i){return!n.node(i).parent}),t=Cr(e,r);t=t.slice(1),d.Z(t,function(i){var a=e.node(i).parent,o=n.edge(i,a),s=!1;o||(o=n.edge(a,i),s=!0),n.node(i).rank=n.node(a).rank+(s?o.minlen:-o.minlen)})}function Mr(e,n,r){return e.hasEdge(n,r)}function rn(e,n,r){return r.low<=n.lim&&n.lim<=r.lim}function Rr(e){switch(e.graph().ranker){case"network-simplex":tn(e);break;case"tight-tree":jr(e);break;case"longest-path":Tr(e);break;default:tn(e)}}var Tr=be;function jr(e){be(e),Be(e)}function tn(e){q(e)}var ne=c(10541),le=c(6613);function kr(e){var n=ee(e,"root",{},"_root"),r=Dr(e),t=Q(ne.Z(r))-1,i=2*t+1;e.graph().nestingRoot=n,d.Z(e.edges(),function(o){e.edge(o).minlen*=i});var a=Ar(e)+1;d.Z(e.children(),function(o){an(e,n,i,a,t,r,o)}),e.graph().nodeRankFactor=i}function an(e,n,r,t,i,a,o){var s=e.children(o);if(!s.length){o!==n&&e.setEdge(n,o,{weight:0,minlen:r});return}var u=ke(e,"_bt"),f=ke(e,"_bb"),l=e.node(o);e.setParent(u,o),l.borderTop=u,e.setParent(f,o),l.borderBottom=f,d.Z(s,function(p){an(e,n,r,t,i,a,p);var b=e.node(p),O=b.borderTop?b.borderTop:p,j=b.borderBottom?b.borderBottom:p,H=b.borderTop?t:2*t,se=O!==j?1:i-a[o]+1;e.setEdge(u,O,{weight:H,minlen:se,nestingEdge:!0}),e.setEdge(j,f,{weight:H,minlen:se,nestingEdge:!0})}),e.parent(o)||e.setEdge(n,u,{weight:0,minlen:i+a[o]})}function Dr(e){var n={};function r(t,i){var a=e.children(t);a&&a.length&&d.Z(a,function(o){r(o,i+1)}),n[t]=i}return d.Z(e.children(),function(t){r(t,1)}),n}function Ar(e){return le.Z(e.edges(),function(n,r){return n+e.edge(r).weight},0)}function Sr(e){var n=e.graph();e.removeNode(n.nestingRoot),delete n.nestingRoot,d.Z(e.edges(),function(r){var t=e.edge(r);t.nestingEdge&&e.removeEdge(r)})}var Fr=c(68194),Br=1,Wr=4;function Ur(e){return(0,Fr.Z)(e,Br|Wr)}var Vr=Ur;function Gr(e,n,r){var t={},i;d.Z(r,function(a){for(var o=e.parent(a),s,u;o;){if(s=e.parent(o),s?(u=t[s],t[s]=o):(u=i,i=o),u&&u!==o){n.setEdge(u,o);return}o=s}})}function Kr(e,n,r){var t=Yr(e),i=new A.k({compound:!0}).setGraph({root:t}).setDefaultNodeLabel(function(a){return e.node(a)});return d.Z(e.nodes(),function(a){var o=e.node(a),s=e.parent(a);(o.rank===n||o.minRank<=n&&n<=o.maxRank)&&(i.setNode(a),i.setParent(a,s||t),d.Z(e[r](a),function(u){var f=u.v===a?u.w:u.v,l=i.edge(f,a),p=J.Z(l)?0:l.weight;i.setEdge(f,a,{weight:e.edge(u).weight+p})}),Object.prototype.hasOwnProperty.call(o,"minRank")&&i.setNode(a,{borderLeft:o.borderLeft[n],borderRight:o.borderRight[n]}))}),i}function Yr(e){for(var n;e.hasNode(n=C("_root")););return n}var zr=c(23996);function Hr(e,n,r){for(var t=-1,i=e.length,a=n.length,o={};++t<i;){var s=t<a?n[t]:void 0;r(o,e[t],s)}return o}var Xr=Hr;function Jr(e,n){return Xr(e||[],n||[],zr.Z)}var $r=Jr,Qr=c(3148),Pe=c(3271),qr=c(51327),et=c(46623);function nt(e,n){var r=e.length;for(e.sort(n);r--;)e[r]=e[r].value;return e}var rt=nt,tt=c(70544),on=c(80718);function it(e,n){if(e!==n){var r=e!==void 0,t=e===null,i=e===e,a=(0,on.Z)(e),o=n!==void 0,s=n===null,u=n===n,f=(0,on.Z)(n);if(!s&&!f&&!a&&e>n||a&&o&&u&&!s&&!f||t&&o&&u||!r&&u||!i)return 1;if(!t&&!a&&!f&&e<n||f&&r&&i&&!t&&!a||s&&r&&i||!o&&i||!u)return-1}return 0}var at=it;function ot(e,n,r){for(var t=-1,i=e.criteria,a=n.criteria,o=i.length,s=r.length;++t<o;){var u=at(i[t],a[t]);if(u){if(t>=s)return u;var f=r[t];return u*(f=="desc"?-1:1)}}return e.index-n.index}var st=ot;function dt(e,n,r){n.length?n=(0,Pe.Z)(n,function(a){return(0,He.Z)(a)?function(o){return(0,qr.Z)(o,a.length===1?a[0]:a)}:a}):n=[Ne.Z];var t=-1;n=(0,Pe.Z)(n,(0,tt.Z)(pe.Z));var i=(0,et.Z)(e,function(a,o,s){var u=(0,Pe.Z)(n,function(f){return f(a)});return{criteria:u,index:++t,value:a}});return rt(i,function(a,o){return st(a,o,r)})}var ut=dt,ft=c(93092),ht=(0,ft.Z)(function(e,n){if(e==null)return[];var r=n.length;return r>1&&(0,W.Z)(e,n[0],n[1])?n=[]:r>2&&(0,W.Z)(n[0],n[1],n[2])&&(n=[n[0]]),ut(e,(0,Qr.Z)(n,1),[])}),oe=ht;function ct(e,n){for(var r=0,t=1;t<n.length;++t)r+=lt(e,n[t-1],n[t]);return r}function lt(e,n,r){for(var t=$r(r,g.Z(r,function(f,l){return l})),i=N.Z(g.Z(n,function(f){return oe(g.Z(e.outEdges(f),function(l){return{pos:t[l.w],weight:e.edge(l).weight}}),"pos")})),a=1;a<r.length;)a<<=1;var o=2*a-1;a-=1;var s=g.Z(new Array(o),function(){return 0}),u=0;return d.Z(i.forEach(function(f){var l=f.pos+a;s[l]+=f.weight;for(var p=0;l>0;)l%2&&(p+=s[l+1]),l=l-1>>1,s[l]+=f.weight;u+=f.weight*p})),u}function vt(e){var n={},r=ae.Z(e.nodes(),function(s){return!e.children(s).length}),t=Q(g.Z(r,function(s){return e.node(s).rank})),i=g.Z(D(t+1),function(){return[]});function a(s){if(!Me.Z(n,s)){n[s]=!0;var u=e.node(s);i[u.rank].push(s),d.Z(e.successors(s),a)}}var o=oe(r,function(s){return e.node(s).rank});return d.Z(o,a),i}function pt(e,n){return g.Z(n,function(r){var t=e.inEdges(r);if(t.length){var i=le.Z(t,function(a,o){var s=e.edge(o),u=e.node(o.v);return{sum:a.sum+s.weight*u.order,weight:a.weight+s.weight}},{sum:0,weight:0});return{v:r,barycenter:i.sum/i.weight,weight:i.weight}}else return{v:r}})}function mt(e,n){var r={};d.Z(e,function(i,a){var o=r[i.v]={indegree:0,in:[],out:[],vs:[i.v],i:a};J.Z(i.barycenter)||(o.barycenter=i.barycenter,o.weight=i.weight)}),d.Z(n.edges(),function(i){var a=r[i.v],o=r[i.w];!J.Z(a)&&!J.Z(o)&&(o.indegree++,a.out.push(r[i.w]))});var t=ae.Z(r,function(i){return!i.indegree});return Zt(t)}function Zt(e){var n=[];function r(a){return function(o){o.merged||(J.Z(o.barycenter)||J.Z(a.barycenter)||o.barycenter>=a.barycenter)&&gt(a,o)}}function t(a){return function(o){o.in.push(a),--o.indegree===0&&e.push(o)}}for(;e.length;){var i=e.pop();n.push(i),d.Z(i.in.reverse(),r(i)),d.Z(i.out,t(i))}return g.Z(ae.Z(n,function(a){return!a.merged}),function(a){return de(a,["vs","i","barycenter","weight"])})}function gt(e,n){var r=0,t=0;e.weight&&(r+=e.barycenter*e.weight,t+=e.weight),n.weight&&(r+=n.barycenter*n.weight,t+=n.weight),e.vs=n.vs.concat(e.vs),e.barycenter=r/t,e.weight=t,e.i=Math.min(n.i,e.i),n.merged=!0}function bt(e,n){var r=Tn(e,function(l){return Object.prototype.hasOwnProperty.call(l,"barycenter")}),t=r.lhs,i=oe(r.rhs,function(l){return-l.i}),a=[],o=0,s=0,u=0;t.sort(wt(!!n)),u=sn(a,i,u),d.Z(t,function(l){u+=l.vs.length,a.push(l.vs),o+=l.barycenter*l.weight,s+=l.weight,u=sn(a,i,u)});var f={vs:N.Z(a)};return s&&(f.barycenter=o/s,f.weight=s),f}function sn(e,n,r){for(var t;n.length&&(t=ue.Z(n)).i<=r;)n.pop(),e.push(t.vs),r++;return r}function wt(e){return function(n,r){return n.barycenter<r.barycenter?-1:n.barycenter>r.barycenter?1:e?r.i-n.i:n.i-r.i}}function dn(e,n,r,t){var i=e.children(n),a=e.node(n),o=a?a.borderLeft:void 0,s=a?a.borderRight:void 0,u={};o&&(i=ae.Z(i,function(j){return j!==o&&j!==s}));var f=pt(e,i);d.Z(f,function(j){if(e.children(j.v).length){var H=dn(e,j.v,r,t);u[j.v]=H,Object.prototype.hasOwnProperty.call(H,"barycenter")&&_t(j,H)}});var l=mt(f,r);Et(l,u);var p=bt(l,t);if(o&&(p.vs=N.Z([o,p.vs,s]),e.predecessors(o).length)){var b=e.node(e.predecessors(o)[0]),O=e.node(e.predecessors(s)[0]);Object.prototype.hasOwnProperty.call(p,"barycenter")||(p.barycenter=0,p.weight=0),p.barycenter=(p.barycenter*p.weight+b.order+O.order)/(p.weight+2),p.weight+=2}return p}function Et(e,n){d.Z(e,function(r){r.vs=N.Z(r.vs.map(function(t){return n[t]?n[t].vs:t}))})}function _t(e,n){J.Z(e.barycenter)?(e.barycenter=n.barycenter,e.weight=n.weight):(e.barycenter=(e.barycenter*e.weight+n.barycenter*n.weight)/(e.weight+n.weight),e.weight+=n.weight)}function yt(e){var n=De(e),r=un(e,D(1,n+1),"inEdges"),t=un(e,D(n-1,-1,-1),"outEdges"),i=vt(e);fn(e,i);for(var a=Number.POSITIVE_INFINITY,o,s=0,u=0;u<4;++s,++u){Ot(s%2?r:t,s%4>=2),i=he(e);var f=ct(e,i);f<a&&(u=0,o=Vr(i),a=f)}fn(e,o)}function un(e,n,r){return g.Z(n,function(t){return Kr(e,t,r)})}function Ot(e,n){var r=new A.k;d.Z(e,function(t){var i=t.graph().root,a=dn(t,i,r,n);d.Z(a.vs,function(o,s){t.node(o).order=s}),Gr(t,r,a.vs)})}function fn(e,n){d.Z(n,function(r){d.Z(r,function(t,i){e.node(t).order=i})})}function Pt(e){var n=Lt(e);d.Z(e.graph().dummyChains,function(r){for(var t=e.node(r),i=t.edgeObj,a=xt(e,n,i.v,i.w),o=a.path,s=a.lca,u=0,f=o[u],l=!0;r!==i.w;){if(t=e.node(r),l){for(;(f=o[u])!==s&&e.node(f).maxRank<t.rank;)u++;f===s&&(l=!1)}if(!l){for(;u<o.length-1&&e.node(f=o[u+1]).minRank<=t.rank;)u++;f=o[u]}e.setParent(r,f),r=e.successors(r)[0]}})}function xt(e,n,r,t){var i=[],a=[],o=Math.min(n[r].low,n[t].low),s=Math.max(n[r].lim,n[t].lim),u,f;u=r;do u=e.parent(u),i.push(u);while(u&&(n[u].low>o||s>n[u].lim));for(f=u,u=t;(u=e.parent(u))!==f;)a.push(u);return{path:i.concat(a.reverse()),lca:f}}function Lt(e){var n={},r=0;function t(i){var a=r;d.Z(e.children(i),t),n[i]={low:a,lim:r++}}return d.Z(e.children(),t),n}var hn=c(89037);function Ct(e,n){return e&&(0,Ie.Z)(e,(0,hn.Z)(n))}var Nt=Ct,It=c(49152),Mt=c(44399);function Rt(e,n){return e==null?e:(0,It.Z)(e,(0,hn.Z)(n),Mt.Z)}var Tt=Rt;function jt(e,n){var r={};function t(i,a){var o=0,s=0,u=i.length,f=ue.Z(a);return d.Z(a,function(l,p){var b=Dt(e,l),O=b?e.node(b).order:u;(b||l===f)&&(d.Z(a.slice(s,p+1),function(j){d.Z(e.predecessors(j),function(H){var se=e.node(H),ln=se.order;(ln<o||O<ln)&&!(se.dummy&&e.node(j).dummy)&&cn(r,H,j)})}),s=p+1,o=O)}),a}return le.Z(n,t),r}function kt(e,n){var r={};function t(a,o,s,u,f){var l;d.Z(D(o,s),function(p){l=a[p],e.node(l).dummy&&d.Z(e.predecessors(l),function(b){var O=e.node(b);O.dummy&&(O.order<u||O.order>f)&&cn(r,b,l)})})}function i(a,o){var s=-1,u,f=0;return d.Z(o,function(l,p){if(e.node(l).dummy==="border"){var b=e.predecessors(l);b.length&&(u=e.node(b[0]).order,t(o,f,p,s,u),f=p,s=u)}t(o,f,o.length,u,a.length)}),o}return le.Z(n,i),r}function Dt(e,n){if(e.node(n).dummy)return we.Z(e.predecessors(n),function(r){return e.node(r).dummy})}function cn(e,n,r){if(n>r){var t=n;n=r,r=t}var i=e[n];i||(e[n]=i={}),i[r]=!0}function At(e,n,r){if(n>r){var t=n;n=r,r=t}return!!e[n]&&Object.prototype.hasOwnProperty.call(e[n],r)}function St(e,n,r,t){var i={},a={},o={};return d.Z(n,function(s){d.Z(s,function(u,f){i[u]=u,a[u]=u,o[u]=f})}),d.Z(n,function(s){var u=-1;d.Z(s,function(f){var l=t(f);if(l.length){l=oe(l,function(H){return o[H]});for(var p=(l.length-1)/2,b=Math.floor(p),O=Math.ceil(p);b<=O;++b){var j=l[b];a[f]===f&&u<o[j]&&!At(r,f,j)&&(a[j]=f,a[f]=i[f]=i[j],u=o[j])}}})}),{root:i,align:a}}function Ft(e,n,r,t,i){var a={},o=Bt(e,n,r,i),s=i?"borderLeft":"borderRight";function u(p,b){for(var O=o.nodes(),j=O.pop(),H={};j;)H[j]?p(j):(H[j]=!0,O.push(j),O=O.concat(b(j))),j=O.pop()}function f(p){a[p]=o.inEdges(p).reduce(function(b,O){return Math.max(b,a[O.v]+o.edge(O))},0)}function l(p){var b=o.outEdges(p).reduce(function(j,H){return Math.min(j,a[H.w]-o.edge(H))},Number.POSITIVE_INFINITY),O=e.node(p);b!==Number.POSITIVE_INFINITY&&O.borderType!==s&&(a[p]=Math.max(a[p],b))}return u(f,o.predecessors.bind(o)),u(l,o.successors.bind(o)),d.Z(t,function(p){a[p]=a[r[p]]}),a}function Bt(e,n,r,t){var i=new A.k,a=e.graph(),o=Kt(a.nodesep,a.edgesep,t);return d.Z(n,function(s){var u;d.Z(s,function(f){var l=r[f];if(i.setNode(l),u){var p=r[u],b=i.edge(p,l);i.setEdge(p,l,Math.max(o(e,f,u),b||0))}u=f})}),i}function Wt(e,n){return ge(ne.Z(n),function(r){var t=Number.NEGATIVE_INFINITY,i=Number.POSITIVE_INFINITY;return Tt(r,function(a,o){var s=Yt(e,o)/2;t=Math.max(a+s,t),i=Math.min(a-s,i)}),t-i})}function Ut(e,n){var r=ne.Z(n),t=te.Z(r),i=Q(r);d.Z(["u","d"],function(a){d.Z(["l","r"],function(o){var s=a+o,u=e[s],f;if(u!==n){var l=ne.Z(u);f=o==="l"?t-te.Z(l):i-Q(l),f&&(e[s]=fe(u,function(p){return p+f}))}})})}function Vt(e,n){return fe(e.ul,function(r,t){if(n)return e[n.toLowerCase()][t];var i=oe(g.Z(e,t));return(i[1]+i[2])/2})}function Gt(e){var n=he(e),r=S.Z(jt(e,n),kt(e,n)),t={},i;d.Z(["u","d"],function(o){i=o==="u"?n:ne.Z(n).reverse(),d.Z(["l","r"],function(s){s==="r"&&(i=g.Z(i,function(p){return ne.Z(p).reverse()}));var u=(o==="u"?e.predecessors:e.successors).bind(e),f=St(e,i,r,u),l=Ft(e,i,f.root,f.align,s==="r");s==="r"&&(l=fe(l,function(p){return-p})),t[o+s]=l})});var a=Wt(e,t);return Ut(t,a),Vt(t,e.graph().align)}function Kt(e,n,r){return function(t,i,a){var o=t.node(i),s=t.node(a),u=0,f;if(u+=o.width/2,Object.prototype.hasOwnProperty.call(o,"labelpos"))switch(o.labelpos.toLowerCase()){case"l":f=-o.width/2;break;case"r":f=o.width/2;break}if(f&&(u+=r?f:-f),f=0,u+=(o.dummy?n:e)/2,u+=(s.dummy?n:e)/2,u+=s.width/2,Object.prototype.hasOwnProperty.call(s,"labelpos"))switch(s.labelpos.toLowerCase()){case"l":f=s.width/2;break;case"r":f=-s.width/2;break}return f&&(u+=r?f:-f),f=0,u}}function Yt(e,n){return e.node(n).width}function zt(e){e=Te(e),Ht(e),Nt(Gt(e),function(n,r){e.node(r).x=n})}function Ht(e){var n=he(e),r=e.graph().ranksep,t=0;d.Z(n,function(i){var a=Q(g.Z(i,function(o){return e.node(o).height}));d.Z(i,function(o){e.node(o).y=t+a/2}),t+=a+r})}function Xt(e,n){var r=n&&n.debugTiming?jn:kn;r("layout",()=>{var t=r("  buildLayoutGraph",()=>oi(e));r("  runLayout",()=>Jt(t,r)),r("  updateInputGraph",()=>$t(e,t))})}function Jt(e,n){n("    makeSpaceForEdgeLabels",()=>si(e)),n("    removeSelfEdges",()=>mi(e)),n("    acyclic",()=>w(e)),n("    nestingGraph.run",()=>kr(e)),n("    rank",()=>Rr(Te(e))),n("    injectEdgeLabelProxies",()=>di(e)),n("    removeEmptyRanks",()=>Rn(e)),n("    nestingGraph.cleanup",()=>Sr(e)),n("    normalizeRanks",()=>Mn(e)),n("    assignRankMinMax",()=>ui(e)),n("    removeEdgeLabelProxies",()=>fi(e)),n("    normalize.run",()=>Wn(e)),n("    parentDummyChains",()=>Pt(e)),n("    addBorderSegments",()=>Dn(e)),n("    order",()=>yt(e)),n("    insertSelfEdges",()=>Zi(e)),n("    adjustCoordinateSystem",()=>An(e)),n("    position",()=>zt(e)),n("    positionSelfEdges",()=>gi(e)),n("    removeBorderNodes",()=>pi(e)),n("    normalize.undo",()=>Vn(e)),n("    fixupEdgeLabelCoords",()=>li(e)),n("    undoCoordinateSystem",()=>Sn(e)),n("    translateGraph",()=>hi(e)),n("    assignNodeIntersects",()=>ci(e)),n("    reversePoints",()=>vi(e)),n("    acyclic.undo",()=>T(e))}function $t(e,n){d.Z(e.nodes(),function(r){var t=e.node(r),i=n.node(r);t&&(t.x=i.x,t.y=i.y,n.children(r).length&&(t.width=i.width,t.height=i.height))}),d.Z(e.edges(),function(r){var t=e.edge(r),i=n.edge(r);t.points=i.points,Object.prototype.hasOwnProperty.call(i,"x")&&(t.x=i.x,t.y=i.y)}),e.graph().width=n.graph().width,e.graph().height=n.graph().height}var Qt=["nodesep","edgesep","ranksep","marginx","marginy"],qt={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},ei=["acyclicer","ranker","rankdir","align"],ni=["width","height"],ri={width:0,height:0},ti=["minlen","weight","width","height","labeloffset"],ii={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},ai=["labelpos"];function oi(e){var n=new A.k({multigraph:!0,compound:!0}),r=Le(e.graph());return n.setGraph(S.Z({},qt,xe(r,Qt),de(r,ei))),d.Z(e.nodes(),function(t){var i=Le(e.node(t));n.setNode(t,_n.Z(xe(i,ni),ri)),n.setParent(t,e.parent(t))}),d.Z(e.edges(),function(t){var i=Le(e.edge(t));n.setEdge(t,S.Z({},ii,xe(i,ti),de(i,ai)))}),n}function si(e){var n=e.graph();n.ranksep/=2,d.Z(e.edges(),function(r){var t=e.edge(r);t.minlen*=2,t.labelpos.toLowerCase()!=="c"&&(n.rankdir==="TB"||n.rankdir==="BT"?t.width+=t.labeloffset:t.height+=t.labeloffset)})}function di(e){d.Z(e.edges(),function(n){var r=e.edge(n);if(r.width&&r.height){var t=e.node(n.v),i=e.node(n.w),a={rank:(i.rank-t.rank)/2+t.rank,e:n};ee(e,"edge-proxy",a,"_ep")}})}function ui(e){var n=0;d.Z(e.nodes(),function(r){var t=e.node(r);t.borderTop&&(t.minRank=e.node(t.borderTop).rank,t.maxRank=e.node(t.borderBottom).rank,n=Q(n,t.maxRank))}),e.graph().maxRank=n}function fi(e){d.Z(e.nodes(),function(n){var r=e.node(n);r.dummy==="edge-proxy"&&(e.edge(r.e).labelRank=r.rank,e.removeNode(n))})}function hi(e){var n=Number.POSITIVE_INFINITY,r=0,t=Number.POSITIVE_INFINITY,i=0,a=e.graph(),o=a.marginx||0,s=a.marginy||0;function u(f){var l=f.x,p=f.y,b=f.width,O=f.height;n=Math.min(n,l-b/2),r=Math.max(r,l+b/2),t=Math.min(t,p-O/2),i=Math.max(i,p+O/2)}d.Z(e.nodes(),function(f){u(e.node(f))}),d.Z(e.edges(),function(f){var l=e.edge(f);Object.prototype.hasOwnProperty.call(l,"x")&&u(l)}),n-=o,t-=s,d.Z(e.nodes(),function(f){var l=e.node(f);l.x-=n,l.y-=t}),d.Z(e.edges(),function(f){var l=e.edge(f);d.Z(l.points,function(p){p.x-=n,p.y-=t}),Object.prototype.hasOwnProperty.call(l,"x")&&(l.x-=n),Object.prototype.hasOwnProperty.call(l,"y")&&(l.y-=t)}),a.width=r-n+o,a.height=i-t+s}function ci(e){d.Z(e.edges(),function(n){var r=e.edge(n),t=e.node(n.v),i=e.node(n.w),a,o;r.points?(a=r.points[0],o=r.points[r.points.length-1]):(r.points=[],a=i,o=t),r.points.unshift(je(t,a)),r.points.push(je(i,o))})}function li(e){d.Z(e.edges(),function(n){var r=e.edge(n);if(Object.prototype.hasOwnProperty.call(r,"x"))switch((r.labelpos==="l"||r.labelpos==="r")&&(r.width-=r.labeloffset),r.labelpos){case"l":r.x-=r.width/2+r.labeloffset;break;case"r":r.x+=r.width/2+r.labeloffset;break}})}function vi(e){d.Z(e.edges(),function(n){var r=e.edge(n);r.reversed&&r.points.reverse()})}function pi(e){d.Z(e.nodes(),function(n){if(e.children(n).length){var r=e.node(n),t=e.node(r.borderTop),i=e.node(r.borderBottom),a=e.node(ue.Z(r.borderLeft)),o=e.node(ue.Z(r.borderRight));r.width=Math.abs(o.x-a.x),r.height=Math.abs(i.y-t.y),r.x=a.x+r.width/2,r.y=t.y+r.height/2}}),d.Z(e.nodes(),function(n){e.node(n).dummy==="border"&&e.removeNode(n)})}function mi(e){d.Z(e.edges(),function(n){if(n.v===n.w){var r=e.node(n.v);r.selfEdges||(r.selfEdges=[]),r.selfEdges.push({e:n,label:e.edge(n)}),e.removeEdge(n)}})}function Zi(e){var n=he(e);d.Z(n,function(r){var t=0;d.Z(r,function(i,a){var o=e.node(i);o.order=a+t,d.Z(o.selfEdges,function(s){ee(e,"selfedge",{width:s.label.width,height:s.label.height,rank:o.rank,order:a+ ++t,e:s.e,label:s.label},"_se")}),delete o.selfEdges})})}function gi(e){d.Z(e.nodes(),function(n){var r=e.node(n);if(r.dummy==="selfedge"){var t=e.node(r.e.v),i=t.x+t.width/2,a=t.y,o=r.x-i,s=t.height/2;e.setEdge(r.e,r.label),e.removeNode(n),r.label.points=[{x:i+2*o/3,y:a-s},{x:i+5*o/6,y:a-s},{x:i+o,y:a},{x:i+5*o/6,y:a+s},{x:i+2*o/3,y:a+s}],r.label.x=r.x,r.label.y=r.y}})}function xe(e,n){return fe(de(e,n),Number)}function Le(e){var n={};return d.Z(e,function(r,t){n[t.toLowerCase()]=r}),n}},55133:function(K,L,c){c.d(L,{k:function(){return A}});var d=c(61606),P=c(89477),E=c(92769),y=c(97175),C=c(67127),x=c(24285),N=c(80155),g=c(3148),R=c(93092),Y=c(57031),F=c(45408),U=(0,R.Z)(function(z){return(0,Y.Z)((0,g.Z)(z,1,F.Z,!0))}),W=U,k=c(10541),B=c(6613),V="\0",M="\0",D="";class A{constructor(h={}){this._isDirected=Object.prototype.hasOwnProperty.call(h,"directed")?h.directed:!0,this._isMultigraph=Object.prototype.hasOwnProperty.call(h,"multigraph")?h.multigraph:!1,this._isCompound=Object.prototype.hasOwnProperty.call(h,"compound")?h.compound:!1,this._label=void 0,this._defaultNodeLabelFn=d.Z(void 0),this._defaultEdgeLabelFn=d.Z(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[M]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(h){return this._label=h,this}graph(){return this._label}setDefaultNodeLabel(h){return P.Z(h)||(h=d.Z(h)),this._defaultNodeLabelFn=h,this}nodeCount(){return this._nodeCount}nodes(){return E.Z(this._nodes)}sources(){var h=this;return y.Z(this.nodes(),function(v){return C.Z(h._in[v])})}sinks(){var h=this;return y.Z(this.nodes(),function(v){return C.Z(h._out[v])})}setNodes(h,v){var Z=arguments,w=this;return x.Z(h,function(I){Z.length>1?w.setNode(I,v):w.setNode(I)}),this}setNode(h,v){return Object.prototype.hasOwnProperty.call(this._nodes,h)?(arguments.length>1&&(this._nodes[h]=v),this):(this._nodes[h]=arguments.length>1?v:this._defaultNodeLabelFn(h),this._isCompound&&(this._parent[h]=M,this._children[h]={},this._children[M][h]=!0),this._in[h]={},this._preds[h]={},this._out[h]={},this._sucs[h]={},++this._nodeCount,this)}node(h){return this._nodes[h]}hasNode(h){return Object.prototype.hasOwnProperty.call(this._nodes,h)}removeNode(h){if(Object.prototype.hasOwnProperty.call(this._nodes,h)){var v=Z=>this.removeEdge(this._edgeObjs[Z]);delete this._nodes[h],this._isCompound&&(this._removeFromParentsChildList(h),delete this._parent[h],x.Z(this.children(h),Z=>{this.setParent(Z)}),delete this._children[h]),x.Z(E.Z(this._in[h]),v),delete this._in[h],delete this._preds[h],x.Z(E.Z(this._out[h]),v),delete this._out[h],delete this._sucs[h],--this._nodeCount}return this}setParent(h,v){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(N.Z(v))v=M;else{v+="";for(var Z=v;!N.Z(Z);Z=this.parent(Z))if(Z===h)throw new Error("Setting "+v+" as parent of "+h+" would create a cycle");this.setNode(v)}return this.setNode(h),this._removeFromParentsChildList(h),this._parent[h]=v,this._children[v][h]=!0,this}_removeFromParentsChildList(h){delete this._children[this._parent[h]][h]}parent(h){if(this._isCompound){var v=this._parent[h];if(v!==M)return v}}children(h){if(N.Z(h)&&(h=M),this._isCompound){var v=this._children[h];if(v)return E.Z(v)}else{if(h===M)return this.nodes();if(this.hasNode(h))return[]}}predecessors(h){var v=this._preds[h];if(v)return E.Z(v)}successors(h){var v=this._sucs[h];if(v)return E.Z(v)}neighbors(h){var v=this.predecessors(h);if(v)return W(v,this.successors(h))}isLeaf(h){var v;return this.isDirected()?v=this.successors(h):v=this.neighbors(h),v.length===0}filterNodes(h){var v=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});v.setGraph(this.graph());var Z=this;x.Z(this._nodes,function(T,S){h(S)&&v.setNode(S,T)}),x.Z(this._edgeObjs,function(T){v.hasNode(T.v)&&v.hasNode(T.w)&&v.setEdge(T,Z.edge(T))});var w={};function I(T){var S=Z.parent(T);return S===void 0||v.hasNode(S)?(w[T]=S,S):S in w?w[S]:I(S)}return this._isCompound&&x.Z(v.nodes(),function(T){v.setParent(T,I(T))}),v}setDefaultEdgeLabel(h){return P.Z(h)||(h=d.Z(h)),this._defaultEdgeLabelFn=h,this}edgeCount(){return this._edgeCount}edges(){return k.Z(this._edgeObjs)}setPath(h,v){var Z=this,w=arguments;return B.Z(h,function(I,T){return w.length>1?Z.setEdge(I,T,v):Z.setEdge(I,T),T}),this}setEdge(){var h,v,Z,w,I=!1,T=arguments[0];typeof T=="object"&&T!==null&&"v"in T?(h=T.v,v=T.w,Z=T.name,arguments.length===2&&(w=arguments[1],I=!0)):(h=T,v=arguments[1],Z=arguments[3],arguments.length>2&&(w=arguments[2],I=!0)),h=""+h,v=""+v,N.Z(Z)||(Z=""+Z);var S=X(this._isDirected,h,v,Z);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,S))return I&&(this._edgeLabels[S]=w),this;if(!N.Z(Z)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(h),this.setNode(v),this._edgeLabels[S]=I?w:this._defaultEdgeLabelFn(h,v,Z);var $=ve(this._isDirected,h,v,Z);return h=$.v,v=$.w,Object.freeze($),this._edgeObjs[S]=$,m(this._preds[v],h),m(this._sucs[h],v),this._in[v][S]=$,this._out[h][S]=$,this._edgeCount++,this}edge(h,v,Z){var w=arguments.length===1?re(this._isDirected,arguments[0]):X(this._isDirected,h,v,Z);return this._edgeLabels[w]}hasEdge(h,v,Z){var w=arguments.length===1?re(this._isDirected,arguments[0]):X(this._isDirected,h,v,Z);return Object.prototype.hasOwnProperty.call(this._edgeLabels,w)}removeEdge(h,v,Z){var w=arguments.length===1?re(this._isDirected,arguments[0]):X(this._isDirected,h,v,Z),I=this._edgeObjs[w];return I&&(h=I.v,v=I.w,delete this._edgeLabels[w],delete this._edgeObjs[w],G(this._preds[v],h),G(this._sucs[h],v),delete this._in[v][w],delete this._out[h][w],this._edgeCount--),this}inEdges(h,v){var Z=this._in[h];if(Z){var w=k.Z(Z);return v?y.Z(w,function(I){return I.v===v}):w}}outEdges(h,v){var Z=this._out[h];if(Z){var w=k.Z(Z);return v?y.Z(w,function(I){return I.w===v}):w}}nodeEdges(h,v){var Z=this.inEdges(h,v);if(Z)return Z.concat(this.outEdges(h,v))}}A.prototype._nodeCount=0,A.prototype._edgeCount=0;function m(z,h){z[h]?z[h]++:z[h]=1}function G(z,h){--z[h]||delete z[h]}function X(z,h,v,Z){var w=""+h,I=""+v;if(!z&&w>I){var T=w;w=I,I=T}return w+D+I+D+(N.Z(Z)?V:Z)}function ve(z,h,v,Z){var w=""+h,I=""+v;if(!z&&w>I){var T=w;w=I,I=T}var S={v:w,w:I};return Z&&(S.name=Z),S}function re(z,h){return X(z,h.v,h.w,h.name)}},56561:function(K,L,c){c.d(L,{k:function(){return d.k}});var d=c(55133);const P="2.1.9-pre"},71818:function(K,L,c){var d=c(80718);function P(E,y,C){for(var x=-1,N=E.length;++x<N;){var g=E[x],R=y(g);if(R!=null&&(Y===void 0?R===R&&!(0,d.Z)(R):C(R,Y)))var Y=R,F=g}return F}L.Z=P},65052:function(K,L){function c(d,P){return d<P}L.Z=c},46623:function(K,L,c){var d=c(61397),P=c(13932);function E(y,C){var x=-1,N=(0,P.Z)(y)?Array(y.length):[];return(0,d.Z)(y,function(g,R,Y){N[++x]=C(g,R,Y)}),N}L.Z=E},70648:function(K,L,c){c.d(L,{Z:function(){return Y}});var d=c(51327),P=c(23996),E=c(86255),y=c(25281),C=c(7247),x=c(65931);function N(F,U,W,k){if(!(0,C.Z)(F))return F;U=(0,E.Z)(U,F);for(var B=-1,V=U.length,M=V-1,D=F;D!=null&&++B<V;){var A=(0,x.Z)(U[B]),m=W;if(A==="__proto__"||A==="constructor"||A==="prototype")return F;if(B!=M){var G=D[A];m=k?k(G,A,D):void 0,m===void 0&&(m=(0,C.Z)(G)?G:(0,y.Z)(U[B+1])?[]:{})}(0,P.Z)(D,A,m),D=D[A]}return F}var g=N;function R(F,U,W){for(var k=-1,B=U.length,V={};++k<B;){var M=U[k],D=(0,d.Z)(F,M);W(D,M)&&g(V,(0,E.Z)(M,F),D)}return V}var Y=R},62126:function(K,L){var c="\\ud800-\\udfff",d="\\u0300-\\u036f",P="\\ufe20-\\ufe2f",E="\\u20d0-\\u20ff",y=d+P+E,C="\\ufe0e\\ufe0f",x="\\u200d",N=RegExp("["+x+c+y+C+"]");function g(R){return N.test(R)}L.Z=g},35565:function(K,L,c){var d=c(93092),P=c(28782),E=c(14026),y=c(44399),C=Object.prototype,x=C.hasOwnProperty,N=(0,d.Z)(function(g,R){g=Object(g);var Y=-1,F=R.length,U=F>2?R[2]:void 0;for(U&&(0,E.Z)(R[0],R[1],U)&&(F=1);++Y<F;)for(var W=R[Y],k=(0,y.Z)(W),B=-1,V=k.length;++B<V;){var M=k[B],D=g[M];(D===void 0||(0,P.Z)(D,C[M])&&!x.call(g,M))&&(g[M]=W[M])}return g});L.Z=N},89045:function(K,L,c){c.d(L,{Z:function(){return U}});var d=c(18458),P=c(13932),E=c(92769);function y(W){return function(k,B,V){var M=Object(k);if(!(0,P.Z)(k)){var D=(0,d.Z)(B,3);k=(0,E.Z)(k),B=function(m){return D(M[m],m,M)}}var A=W(k,B,V);return A>-1?M[D?k[A]:A]:void 0}}var C=y,x=c(55799),N=c(42013),g=Math.max;function R(W,k,B){var V=W==null?0:W.length;if(!V)return-1;var M=B==null?0:(0,N.Z)(B);return M<0&&(M=g(V+M,0)),(0,x.Z)(W,(0,d.Z)(k,3),M)}var Y=R,F=C(Y),U=F},42060:function(K,L,c){var d=c(3148);function P(E){var y=E==null?0:E.length;return y?(0,d.Z)(E,1):[]}L.Z=P},67876:function(K,L,c){c.d(L,{Z:function(){return N}});var d=Object.prototype,P=d.hasOwnProperty;function E(g,R){return g!=null&&P.call(g,R)}var y=E,C=c(20534);function x(g,R){return g!=null&&(0,C.Z)(g,R,y)}var N=x},56288:function(K,L,c){var d=c(71395),P=c(83788),E=c(54764),y="[object String]";function C(x){return typeof x=="string"||!(0,P.Z)(x)&&(0,E.Z)(x)&&(0,d.Z)(x)==y}L.Z=C},7683:function(K,L){function c(d){var P=d==null?0:d.length;return P?d[P-1]:void 0}L.Z=c},857:function(K,L,c){var d=c(3271),P=c(18458),E=c(46623),y=c(83788);function C(x,N){var g=(0,y.Z)(x)?d.Z:E.Z;return g(x,(0,P.Z)(N,3))}L.Z=C},93422:function(K,L,c){var d=c(71818),P=c(65052),E=c(35272);function y(C){return C&&C.length?(0,d.Z)(C,E.Z,P.Z):void 0}L.Z=y},53689:function(K,L,c){c.d(L,{Z:function(){return A}});var d=/\s/;function P(m){for(var G=m.length;G--&&d.test(m.charAt(G)););return G}var E=P,y=/^\s+/;function C(m){return m&&m.slice(0,E(m)+1).replace(y,"")}var x=C,N=c(7247),g=c(80718),R=NaN,Y=/^[-+]0x[0-9a-f]+$/i,F=/^0b[01]+$/i,U=/^0o[0-7]+$/i,W=parseInt;function k(m){if(typeof m=="number")return m;if((0,g.Z)(m))return R;if((0,N.Z)(m)){var G=typeof m.valueOf=="function"?m.valueOf():m;m=(0,N.Z)(G)?G+"":G}if(typeof m!="string")return m===0?m:+m;m=x(m);var X=F.test(m);return X||U.test(m)?W(m.slice(2),X?2:8):Y.test(m)?R:+m}var B=k,V=1/0,M=17976931348623157e292;function D(m){if(!m)return m===0?m:0;if(m=B(m),m===V||m===-V){var G=m<0?-1:1;return G*M}return m===m?m:0}var A=D},42013:function(K,L,c){var d=c(53689);function P(E){var y=(0,d.Z)(E),C=y%1;return y===y?C?y-C:y:0}L.Z=P}}]);
