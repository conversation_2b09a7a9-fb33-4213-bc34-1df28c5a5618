"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[2315],{2315:function(le,_t,X){var G,K,C;X.d(_t,{diagram:function(){return ae}});var g=X(29134),H=X(69471),bt=X(86222),U=X.n(bt);function ht(t,n){var o;if(n===void 0){var h=U()(t),d;try{for(h.s();!(d=h.n()).done;){var u=d.value;u!=null&&(o>u||o===void 0&&u>=u)&&(o=u)}}catch(l){h.e(l)}finally{h.f()}}else{var p=-1,y=U()(t),r;try{for(y.s();!(r=y.n()).done;){var s=r.value;(s=n(s,++p,t))!=null&&(o>s||o===void 0&&s>=s)&&(o=s)}}catch(l){y.e(l)}finally{y.f()}}return o}function St(t){return t.target.depth}function wt(t){return t.depth}function Lt(t,n){return n-1-t.height}function ft(t,n){return t.sourceLinks.length?t.depth:n-1}function Et(t){return t.targetLinks.length?t.depth:t.sourceLinks.length?ht(t.sourceLinks,St)-1:0}function it(t,n){var o=0;if(n===void 0){var h=U()(t),d;try{for(h.s();!(d=h.n()).done;){var u=d.value;(u=+u)&&(o+=u)}}catch(l){h.e(l)}finally{h.f()}}else{var p=-1,y=U()(t),r;try{for(y.s();!(r=y.n()).done;){var s=r.value;(s=+n(s,++p,t))&&(o+=s)}}catch(l){y.e(l)}finally{y.f()}}return o}function yt(t,n){var o;if(n===void 0){var h=U()(t),d;try{for(h.s();!(d=h.n()).done;){var u=d.value;u!=null&&(o<u||o===void 0&&u>=u)&&(o=u)}}catch(l){h.e(l)}finally{h.f()}}else{var p=-1,y=U()(t),r;try{for(y.s();!(r=y.n()).done;){var s=r.value;(s=n(s,++p,t))!=null&&(o<s||o===void 0&&s>=s)&&(o=s)}}catch(l){y.e(l)}finally{y.f()}}return o}function Z(t){return function(){return t}}function dt(t,n){return J(t.source,n.source)||t.index-n.index}function gt(t,n){return J(t.target,n.target)||t.index-n.index}function J(t,n){return t.y0-n.y0}function rt(t){return t.value}function Wt(t){return t.index}function At(t){return t.nodes}function Tt(t){return t.links}function pt(t,n){const o=t.get(n);if(!o)throw new Error("missing: "+n);return o}function kt({nodes:t}){for(const n of t){let o=n.y0,h=o;for(const d of n.sourceLinks)d.y0=o+d.width/2,o+=d.width;for(const d of n.targetLinks)d.y1=h+d.width/2,h+=d.width}}function Mt(){let t=0,n=0,o=1,h=1,d=24,u=8,p,y=Wt,r=ft,s,l,x=At,v=Tt,k=6;function _(){const e={nodes:x.apply(null,arguments),links:v.apply(null,arguments)};return W(e),E(e),A(e),I(e),O(e),kt(e),e}_.update=function(e){return kt(e),e},_.nodeId=function(e){return arguments.length?(y=typeof e=="function"?e:Z(e),_):y},_.nodeAlign=function(e){return arguments.length?(r=typeof e=="function"?e:Z(e),_):r},_.nodeSort=function(e){return arguments.length?(s=e,_):s},_.nodeWidth=function(e){return arguments.length?(d=+e,_):d},_.nodePadding=function(e){return arguments.length?(u=p=+e,_):u},_.nodes=function(e){return arguments.length?(x=typeof e=="function"?e:Z(e),_):x},_.links=function(e){return arguments.length?(v=typeof e=="function"?e:Z(e),_):v},_.linkSort=function(e){return arguments.length?(l=e,_):l},_.size=function(e){return arguments.length?(t=n=0,o=+e[0],h=+e[1],_):[o-t,h-n]},_.extent=function(e){return arguments.length?(t=+e[0][0],o=+e[1][0],n=+e[0][1],h=+e[1][1],_):[[t,n],[o,h]]},_.iterations=function(e){return arguments.length?(k=+e,_):k};function W({nodes:e,links:c}){for(const[i,a]of e.entries())a.index=i,a.sourceLinks=[],a.targetLinks=[];const f=new Map(e.map((i,a)=>[y(i,a,e),i]));for(const[i,a]of c.entries()){a.index=i;let{source:m,target:b}=a;typeof m!="object"&&(m=a.source=pt(f,m)),typeof b!="object"&&(b=a.target=pt(f,b)),m.sourceLinks.push(a),b.targetLinks.push(a)}if(l!=null)for(const{sourceLinks:i,targetLinks:a}of e)i.sort(l),a.sort(l)}function E({nodes:e}){for(const c of e)c.value=c.fixedValue===void 0?Math.max(it(c.sourceLinks,rt),it(c.targetLinks,rt)):c.fixedValue}function A({nodes:e}){const c=e.length;let f=new Set(e),i=new Set,a=0;for(;f.size;){for(const m of f){m.depth=a;for(const{target:b}of m.sourceLinks)i.add(b)}if(++a>c)throw new Error("circular link");f=i,i=new Set}}function I({nodes:e}){const c=e.length;let f=new Set(e),i=new Set,a=0;for(;f.size;){for(const m of f){m.height=a;for(const{source:b}of m.targetLinks)i.add(b)}if(++a>c)throw new Error("circular link");f=i,i=new Set}}function D({nodes:e}){const c=yt(e,a=>a.depth)+1,f=(o-t-d)/(c-1),i=new Array(c);for(const a of e){const m=Math.max(0,Math.min(c-1,Math.floor(r.call(null,a,c))));a.layer=m,a.x0=t+m*f,a.x1=a.x0+d,i[m]?i[m].push(a):i[m]=[a]}if(s)for(const a of i)a.sort(s);return i}function j(e){const c=ht(e,f=>(h-n-(f.length-1)*p)/it(f,rt));for(const f of e){let i=n;for(const a of f){a.y0=i,a.y1=i+a.value*c,i=a.y1+p;for(const m of a.sourceLinks)m.width=m.value*c}i=(h-i+p)/(f.length+1);for(let a=0;a<f.length;++a){const m=f[a];m.y0+=i*(a+1),m.y1+=i*(a+1)}Y(f)}}function O(e){const c=D(e);p=Math.min(u,(h-n)/(yt(c,f=>f.length)-1)),j(c);for(let f=0;f<k;++f){const i=Math.pow(.99,f),a=Math.max(1-i,(f+1)/k);T(c,i,a),S(c,i,a)}}function S(e,c,f){for(let i=1,a=e.length;i<a;++i){const m=e[i];for(const b of m){let $=0,N=0;for(const{source:L,value:ut}of b.targetLinks){let Q=ut*(b.layer-L.layer);$+=w(L,b)*Q,N+=Q}if(!(N>0))continue;let z=($/N-b.y0)*c;b.y0+=z,b.y1+=z,B(b)}s===void 0&&m.sort(J),P(m,f)}}function T(e,c,f){for(let i=e.length,a=i-2;a>=0;--a){const m=e[a];for(const b of m){let $=0,N=0;for(const{target:L,value:ut}of b.sourceLinks){let Q=ut*(L.layer-b.layer);$+=R(b,L)*Q,N+=Q}if(!(N>0))continue;let z=($/N-b.y0)*c;b.y0+=z,b.y1+=z,B(b)}s===void 0&&m.sort(J),P(m,f)}}function P(e,c){const f=e.length>>1,i=e[f];F(e,i.y0-p,f-1,c),M(e,i.y1+p,f+1,c),F(e,h,e.length-1,c),M(e,n,0,c)}function M(e,c,f,i){for(;f<e.length;++f){const a=e[f],m=(c-a.y0)*i;m>1e-6&&(a.y0+=m,a.y1+=m),c=a.y1+p}}function F(e,c,f,i){for(;f>=0;--f){const a=e[f],m=(a.y1-c)*i;m>1e-6&&(a.y0-=m,a.y1-=m),c=a.y0-p}}function B({sourceLinks:e,targetLinks:c}){if(l===void 0){for(const{source:{sourceLinks:f}}of c)f.sort(gt);for(const{target:{targetLinks:f}}of e)f.sort(dt)}}function Y(e){if(l===void 0)for(const{sourceLinks:c,targetLinks:f}of e)c.sort(gt),f.sort(dt)}function w(e,c){let f=e.y0-(e.sourceLinks.length-1)*p/2;for(const{target:i,width:a}of e.sourceLinks){if(i===c)break;f+=a+p}for(const{source:i,width:a}of c.targetLinks){if(i===e)break;f-=a}return f}function R(e,c){let f=c.y0-(c.targetLinks.length-1)*p/2;for(const{source:i,width:a}of c.targetLinks){if(i===e)break;f+=a+p}for(const{target:i,width:a}of e.sourceLinks){if(i===c)break;f-=a}return f}return _}var st=Math.PI,ot=2*st,V=1e-6,Nt=ot-V;function at(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function mt(){return new at}at.prototype=mt.prototype={constructor:at,moveTo:function(t,n){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)},closePath:function(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,n){this._+="L"+(this._x1=+t)+","+(this._y1=+n)},quadraticCurveTo:function(t,n,o,h){this._+="Q"+ +t+","+ +n+","+(this._x1=+o)+","+(this._y1=+h)},bezierCurveTo:function(t,n,o,h,d,u){this._+="C"+ +t+","+ +n+","+ +o+","+ +h+","+(this._x1=+d)+","+(this._y1=+u)},arcTo:function(t,n,o,h,d){t=+t,n=+n,o=+o,h=+h,d=+d;var u=this._x1,p=this._y1,y=o-t,r=h-n,s=u-t,l=p-n,x=s*s+l*l;if(d<0)throw new Error("negative radius: "+d);if(this._x1===null)this._+="M"+(this._x1=t)+","+(this._y1=n);else if(x>V)if(!(Math.abs(l*y-r*s)>V)||!d)this._+="L"+(this._x1=t)+","+(this._y1=n);else{var v=o-u,k=h-p,_=y*y+r*r,W=v*v+k*k,E=Math.sqrt(_),A=Math.sqrt(x),I=d*Math.tan((st-Math.acos((_+x-W)/(2*E*A)))/2),D=I/A,j=I/E;Math.abs(D-1)>V&&(this._+="L"+(t+D*s)+","+(n+D*l)),this._+="A"+d+","+d+",0,0,"+ +(l*v>s*k)+","+(this._x1=t+j*y)+","+(this._y1=n+j*r)}},arc:function(t,n,o,h,d,u){t=+t,n=+n,o=+o,u=!!u;var p=o*Math.cos(h),y=o*Math.sin(h),r=t+p,s=n+y,l=1^u,x=u?h-d:d-h;if(o<0)throw new Error("negative radius: "+o);this._x1===null?this._+="M"+r+","+s:(Math.abs(this._x1-r)>V||Math.abs(this._y1-s)>V)&&(this._+="L"+r+","+s),o&&(x<0&&(x=x%ot+ot),x>Nt?this._+="A"+o+","+o+",0,1,"+l+","+(t-p)+","+(n-y)+"A"+o+","+o+",0,1,"+l+","+(this._x1=r)+","+(this._y1=s):x>V&&(this._+="A"+o+","+o+",0,"+ +(x>=st)+","+l+","+(this._x1=t+o*Math.cos(d))+","+(this._y1=n+o*Math.sin(d))))},rect:function(t,n,o,h){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)+"h"+ +o+"v"+ +h+"h"+-o+"Z"},toString:function(){return this._}};var It=mt,Pt=Array.prototype.slice;function xt(t){return function(){return t}}function Ct(t){return t[0]}function Ot(t){return t[1]}function zt(t){return t.source}function Dt(t){return t.target}function lt(t){var n=zt,o=Dt,h=Ct,d=Ot,u=null;function p(){var y,r=Pt.call(arguments),s=n.apply(this,r),l=o.apply(this,r);if(u||(u=y=It()),t(u,+h.apply(this,(r[0]=s,r)),+d.apply(this,r),+h.apply(this,(r[0]=l,r)),+d.apply(this,r)),y)return u=null,y+""||null}return p.source=function(y){return arguments.length?(n=y,p):n},p.target=function(y){return arguments.length?(o=y,p):o},p.x=function(y){return arguments.length?(h=typeof y=="function"?y:xt(+y),p):h},p.y=function(y){return arguments.length?(d=typeof y=="function"?y:xt(+y),p):d},p.context=function(y){return arguments.length?(u=y==null?null:y,p):u},p}function jt(t,n,o,h,d){t.moveTo(n,o),t.bezierCurveTo(n=(n+h)/2,o,n,d,h,d)}function Rt(t,n,o,h,d){t.moveTo(n,o),t.bezierCurveTo(n,o=(o+d)/2,h,o,h,d)}function $t(t,n,o,h,d){var u=pointRadial(n,o),p=pointRadial(n,o=(o+d)/2),y=pointRadial(h,o),r=pointRadial(h,d);t.moveTo(u[0],u[1]),t.bezierCurveTo(p[0],p[1],y[0],y[1],r[0],r[1])}function Vt(){return lt(jt)}function ce(){return lt(Rt)}function ue(){var t=lt($t);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}function Ft(t){return[t.source.x1,t.y0]}function Bt(t){return[t.target.x0,t.y1]}function Yt(){return Vt().source(Ft).target(Bt)}var ct=function(){var t=(0,g.eW)(function(y,r,s,l){for(s=s||{},l=y.length;l--;s[y[l]]=r);return s},"o"),n=[1,9],o=[1,10],h=[1,5,10,12],d={trace:(0,g.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,SANKEY:4,NEWLINE:5,csv:6,opt_eof:7,record:8,csv_tail:9,EOF:10,"field[source]":11,COMMA:12,"field[target]":13,"field[value]":14,field:15,escaped:16,non_escaped:17,DQUOTE:18,ESCAPED_TEXT:19,NON_ESCAPED_TEXT:20,$accept:0,$end:1},terminals_:{2:"error",4:"SANKEY",5:"NEWLINE",10:"EOF",11:"field[source]",12:"COMMA",13:"field[target]",14:"field[value]",18:"DQUOTE",19:"ESCAPED_TEXT",20:"NON_ESCAPED_TEXT"},productions_:[0,[3,4],[6,2],[9,2],[9,0],[7,1],[7,0],[8,5],[15,1],[15,1],[16,3],[17,1]],performAction:(0,g.eW)(function(r,s,l,x,v,k,_){var W=k.length-1;switch(v){case 7:const E=x.findOrCreateNode(k[W-4].trim().replaceAll('""','"')),A=x.findOrCreateNode(k[W-2].trim().replaceAll('""','"')),I=parseFloat(k[W].trim());x.addLink(E,A,I);break;case 8:case 9:case 11:this.$=k[W];break;case 10:this.$=k[W-1];break}},"anonymous"),table:[{3:1,4:[1,2]},{1:[3]},{5:[1,3]},{6:4,8:5,15:6,16:7,17:8,18:n,20:o},{1:[2,6],7:11,10:[1,12]},t(o,[2,4],{9:13,5:[1,14]}),{12:[1,15]},t(h,[2,8]),t(h,[2,9]),{19:[1,16]},t(h,[2,11]),{1:[2,1]},{1:[2,5]},t(o,[2,2]),{6:17,8:5,15:6,16:7,17:8,18:n,20:o},{15:18,16:7,17:8,18:n,20:o},{18:[1,19]},t(o,[2,3]),{12:[1,20]},t(h,[2,10]),{15:21,16:7,17:8,18:n,20:o},t([1,5,10],[2,7])],defaultActions:{11:[2,1],12:[2,5]},parseError:(0,g.eW)(function(r,s){if(s.recoverable)this.trace(r);else{var l=new Error(r);throw l.hash=s,l}},"parseError"),parse:(0,g.eW)(function(r){var s=this,l=[0],x=[],v=[null],k=[],_=this.table,W="",E=0,A=0,I=0,D=2,j=1,O=k.slice.call(arguments,1),S=Object.create(this.lexer),T={yy:{}};for(var P in this.yy)Object.prototype.hasOwnProperty.call(this.yy,P)&&(T.yy[P]=this.yy[P]);S.setInput(r,T.yy),T.yy.lexer=S,T.yy.parser=this,typeof S.yylloc=="undefined"&&(S.yylloc={});var M=S.yylloc;k.push(M);var F=S.options&&S.options.ranges;typeof T.yy.parseError=="function"?this.parseError=T.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function B(L){l.length=l.length-2*L,v.length=v.length-L,k.length=k.length-L}(0,g.eW)(B,"popStack");function Y(){var L;return L=x.pop()||S.lex()||j,typeof L!="number"&&(L instanceof Array&&(x=L,L=x.pop()),L=s.symbols_[L]||L),L}(0,g.eW)(Y,"lex");for(var w,R,e,c,f,i,a={},m,b,$,N;;){if(e=l[l.length-1],this.defaultActions[e]?c=this.defaultActions[e]:((w===null||typeof w=="undefined")&&(w=Y()),c=_[e]&&_[e][w]),typeof c=="undefined"||!c.length||!c[0]){var z="";N=[];for(m in _[e])this.terminals_[m]&&m>D&&N.push("'"+this.terminals_[m]+"'");S.showPosition?z="Parse error on line "+(E+1)+`:
`+S.showPosition()+`
Expecting `+N.join(", ")+", got '"+(this.terminals_[w]||w)+"'":z="Parse error on line "+(E+1)+": Unexpected "+(w==j?"end of input":"'"+(this.terminals_[w]||w)+"'"),this.parseError(z,{text:S.match,token:this.terminals_[w]||w,line:S.yylineno,loc:M,expected:N})}if(c[0]instanceof Array&&c.length>1)throw new Error("Parse Error: multiple actions possible at state: "+e+", token: "+w);switch(c[0]){case 1:l.push(w),v.push(S.yytext),k.push(S.yylloc),l.push(c[1]),w=null,R?(w=R,R=null):(A=S.yyleng,W=S.yytext,E=S.yylineno,M=S.yylloc,I>0&&I--);break;case 2:if(b=this.productions_[c[1]][1],a.$=v[v.length-b],a._$={first_line:k[k.length-(b||1)].first_line,last_line:k[k.length-1].last_line,first_column:k[k.length-(b||1)].first_column,last_column:k[k.length-1].last_column},F&&(a._$.range=[k[k.length-(b||1)].range[0],k[k.length-1].range[1]]),i=this.performAction.apply(a,[W,A,E,T.yy,c[1],v,k].concat(O)),typeof i!="undefined")return i;b&&(l=l.slice(0,-1*b*2),v=v.slice(0,-1*b),k=k.slice(0,-1*b)),l.push(this.productions_[c[1]][0]),v.push(a.$),k.push(a._$),$=_[l[l.length-2]][l[l.length-1]],l.push($);break;case 3:return!0}}return!0},"parse")},u=function(){var y={EOF:1,parseError:(0,g.eW)(function(s,l){if(this.yy.parser)this.yy.parser.parseError(s,l);else throw new Error(s)},"parseError"),setInput:(0,g.eW)(function(r,s){return this.yy=s||this.yy||{},this._input=r,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,g.eW)(function(){var r=this._input[0];this.yytext+=r,this.yyleng++,this.offset++,this.match+=r,this.matched+=r;var s=r.match(/(?:\r\n?|\n).*/g);return s?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),r},"input"),unput:(0,g.eW)(function(r){var s=r.length,l=r.split(/(?:\r\n?|\n)/g);this._input=r+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-s),this.offset-=s;var x=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),l.length-1&&(this.yylineno-=l.length-1);var v=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:l?(l.length===x.length?this.yylloc.first_column:0)+x[x.length-l.length].length-l[0].length:this.yylloc.first_column-s},this.options.ranges&&(this.yylloc.range=[v[0],v[0]+this.yyleng-s]),this.yyleng=this.yytext.length,this},"unput"),more:(0,g.eW)(function(){return this._more=!0,this},"more"),reject:(0,g.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,g.eW)(function(r){this.unput(this.match.slice(r))},"less"),pastInput:(0,g.eW)(function(){var r=this.matched.substr(0,this.matched.length-this.match.length);return(r.length>20?"...":"")+r.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,g.eW)(function(){var r=this.match;return r.length<20&&(r+=this._input.substr(0,20-r.length)),(r.substr(0,20)+(r.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,g.eW)(function(){var r=this.pastInput(),s=new Array(r.length+1).join("-");return r+this.upcomingInput()+`
`+s+"^"},"showPosition"),test_match:(0,g.eW)(function(r,s){var l,x,v;if(this.options.backtrack_lexer&&(v={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(v.yylloc.range=this.yylloc.range.slice(0))),x=r[0].match(/(?:\r\n?|\n).*/g),x&&(this.yylineno+=x.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:x?x[x.length-1].length-x[x.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+r[0].length},this.yytext+=r[0],this.match+=r[0],this.matches=r,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(r[0].length),this.matched+=r[0],l=this.performAction.call(this,this.yy,this,s,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),l)return l;if(this._backtrack){for(var k in v)this[k]=v[k];return!1}return!1},"test_match"),next:(0,g.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var r,s,l,x;this._more||(this.yytext="",this.match="");for(var v=this._currentRules(),k=0;k<v.length;k++)if(l=this._input.match(this.rules[v[k]]),l&&(!s||l[0].length>s[0].length)){if(s=l,x=k,this.options.backtrack_lexer){if(r=this.test_match(l,v[k]),r!==!1)return r;if(this._backtrack){s=!1;continue}else return!1}else if(!this.options.flex)break}return s?(r=this.test_match(s,v[x]),r!==!1?r:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,g.eW)(function(){var s=this.next();return s||this.lex()},"lex"),begin:(0,g.eW)(function(s){this.conditionStack.push(s)},"begin"),popState:(0,g.eW)(function(){var s=this.conditionStack.length-1;return s>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,g.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,g.eW)(function(s){return s=this.conditionStack.length-1-Math.abs(s||0),s>=0?this.conditionStack[s]:"INITIAL"},"topState"),pushState:(0,g.eW)(function(s){this.begin(s)},"pushState"),stateStackSize:(0,g.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,g.eW)(function(s,l,x,v){var k=v;switch(x){case 0:return this.pushState("csv"),4;break;case 1:return 10;case 2:return 5;case 3:return 12;case 4:return this.pushState("escaped_text"),18;break;case 5:return 20;case 6:return this.popState("escaped_text"),18;break;case 7:return 19}},"anonymous"),rules:[/^(?:sankey-beta\b)/i,/^(?:$)/i,/^(?:((\u000D\u000A)|(\u000A)))/i,/^(?:(\u002C))/i,/^(?:(\u0022))/i,/^(?:([\u0020-\u0021\u0023-\u002B\u002D-\u007E])*)/i,/^(?:(\u0022)(?!(\u0022)))/i,/^(?:(([\u0020-\u0021\u0023-\u002B\u002D-\u007E])|(\u002C)|(\u000D)|(\u000A)|(\u0022)(\u0022))*)/i],conditions:{csv:{rules:[1,2,3,4,5,6,7],inclusive:!1},escaped_text:{rules:[6,7],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7],inclusive:!0}}};return y}();d.lexer=u;function p(){this.yy={}}return(0,g.eW)(p,"Parser"),p.prototype=d,d.Parser=p,new p}();ct.parser=ct;var q=ct,tt=[],et=[],nt=new Map,Ht=(0,g.eW)(()=>{tt=[],et=[],nt=new Map,(0,g.ZH)()},"clear"),Ut=(G=class{constructor(n,o,h=0){this.source=n,this.target=o,this.value=h}},(0,g.eW)(G,"SankeyLink"),G),Gt=(0,g.eW)((t,n,o)=>{tt.push(new Ut(t,n,o))},"addLink"),Kt=(K=class{constructor(n){this.ID=n}},(0,g.eW)(K,"SankeyNode"),K),Xt=(0,g.eW)(t=>{t=g.SY.sanitizeText(t,(0,g.nV)());let n=nt.get(t);return n===void 0&&(n=new Kt(t),nt.set(t,n),et.push(n)),n},"findOrCreateNode"),Qt=(0,g.eW)(()=>et,"getNodes"),Zt=(0,g.eW)(()=>tt,"getLinks"),Jt=(0,g.eW)(()=>({nodes:et.map(t=>({id:t.ID})),links:tt.map(t=>({source:t.source.ID,target:t.target.ID,value:t.value}))}),"getGraph"),qt={nodesMap:nt,getConfig:(0,g.eW)(()=>(0,g.nV)().sankey,"getConfig"),getNodes:Qt,getLinks:Zt,getGraph:Jt,addLink:Gt,findOrCreateNode:Xt,getAccTitle:g.eu,setAccTitle:g.GN,getAccDescription:g.Mx,setAccDescription:g.U$,getDiagramTitle:g.Kr,setDiagramTitle:g.g2,clear:Ht},vt=(C=class{static next(n){return new C(n+ ++C.count)}constructor(n){this.id=n,this.href=`#${n}`}toString(){return"url("+this.href+")"}},(0,g.eW)(C,"Uid"),C.count=0,C),te={left:wt,right:Lt,center:Et,justify:ft},ee=(0,g.eW)(function(t,n,o,h){var F,B,Y,w,R,e,c,f;const{securityLevel:d,sankey:u}=(0,g.nV)(),p=g.Fy.sankey;let y;d==="sandbox"&&(y=(0,H.Ys)("#i"+n));const r=d==="sandbox"?(0,H.Ys)(y.nodes()[0].contentDocument.body):(0,H.Ys)("body"),s=d==="sandbox"?r.select(`[id="${n}"]`):(0,H.Ys)(`[id="${n}"]`),l=(F=u==null?void 0:u.width)!=null?F:p.width,x=(B=u==null?void 0:u.height)!=null?B:p.width,v=(Y=u==null?void 0:u.useMaxWidth)!=null?Y:p.useMaxWidth,k=(w=u==null?void 0:u.nodeAlignment)!=null?w:p.nodeAlignment,_=(R=u==null?void 0:u.prefix)!=null?R:p.prefix,W=(e=u==null?void 0:u.suffix)!=null?e:p.suffix,E=(c=u==null?void 0:u.showValues)!=null?c:p.showValues,A=h.db.getGraph(),I=te[k];Mt().nodeId(i=>i.id).nodeWidth(10).nodePadding(10+(E?15:0)).nodeAlign(I).extent([[0,0],[l,x]])(A);const O=(0,H.PKp)(H.K2I);s.append("g").attr("class","nodes").selectAll(".node").data(A.nodes).join("g").attr("class","node").attr("id",i=>(i.uid=vt.next("node-")).id).attr("transform",function(i){return"translate("+i.x0+","+i.y0+")"}).attr("x",i=>i.x0).attr("y",i=>i.y0).append("rect").attr("height",i=>i.y1-i.y0).attr("width",i=>i.x1-i.x0).attr("fill",i=>O(i.id));const S=(0,g.eW)(({id:i,value:a})=>E?`${i}
${_}${Math.round(a*100)/100}${W}`:i,"getText");s.append("g").attr("class","node-labels").attr("font-size",14).selectAll("text").data(A.nodes).join("text").attr("x",i=>i.x0<l/2?i.x1+6:i.x0-6).attr("y",i=>(i.y1+i.y0)/2).attr("dy",`${E?"0":"0.35"}em`).attr("text-anchor",i=>i.x0<l/2?"start":"end").text(S);const T=s.append("g").attr("class","links").attr("fill","none").attr("stroke-opacity",.5).selectAll(".link").data(A.links).join("g").attr("class","link").style("mix-blend-mode","multiply"),P=(f=u==null?void 0:u.linkColor)!=null?f:"gradient";if(P==="gradient"){const i=T.append("linearGradient").attr("id",a=>(a.uid=vt.next("linearGradient-")).id).attr("gradientUnits","userSpaceOnUse").attr("x1",a=>a.source.x1).attr("x2",a=>a.target.x0);i.append("stop").attr("offset","0%").attr("stop-color",a=>O(a.source.id)),i.append("stop").attr("offset","100%").attr("stop-color",a=>O(a.target.id))}let M;switch(P){case"gradient":M=(0,g.eW)(i=>i.uid,"coloring");break;case"source":M=(0,g.eW)(i=>O(i.source.id),"coloring");break;case"target":M=(0,g.eW)(i=>O(i.target.id),"coloring");break;default:M=P}T.append("path").attr("d",Yt()).attr("stroke",M).attr("stroke-width",i=>Math.max(1,i.width)),(0,g.j7)(void 0,s,0,v)},"draw"),ne={draw:ee},ie=(0,g.eW)(t=>t.replaceAll(/^[^\S\n\r]+|[^\S\n\r]+$/g,"").replaceAll(/([\n\r])+/g,`
`).trim(),"prepareTextForParsing"),re=(0,g.eW)(t=>`.label {
      font-family: ${t.fontFamily};
    }`,"getStyles"),se=re,oe=q.parse.bind(q);q.parse=t=>oe(ie(t));var ae={styles:se,parser:q,db:qt,renderer:ne}}}]);
