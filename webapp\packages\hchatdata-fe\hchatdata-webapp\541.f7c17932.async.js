!(function(){"use strict";var Pe=Object.defineProperty;var re=Object.getOwnPropertySymbols;var Le=Object.prototype.hasOwnProperty,Te=Object.prototype.propertyIsEnumerable;var ce=(v,u,o)=>u in v?Pe(v,u,{enumerable:!0,configurable:!0,writable:!0,value:o}):v[u]=o,ae=(v,u)=>{for(var o in u||(u={}))Le.call(u,o)&&ce(v,o,u[o]);if(re)for(var o of re(u))Te.call(u,o)&&ce(v,o,u[o]);return v};var ie=(v,u,o)=>new Promise((w,g)=>{var b=a=>{try{f(o.next(a))}catch(m){g(m)}},y=a=>{try{f(o.throw(a))}catch(m){g(m)}},f=a=>a.done?w(a.value):Promise.resolve(a.value).then(b,y);f((o=o.apply(v,u)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[541],{96547:function(v,u,o){o.d(u,{A:function(){return g}});var w=o(29134);function g(b,y){var f,a,m;b.accDescr&&((f=y.setAccDescription)==null||f.call(y,b.accDescr)),b.accTitle&&((a=y.setAccTitle)==null||a.call(y,b.accTitle)),b.title&&((m=y.setDiagramTitle)==null||m.call(y,b.title))}(0,w.eW)(g,"populateCommonDb")},8031:function(v,u,o){o.d(u,{j:function(){return g}});var w=o(29134),g=(0,w.eW)((f,a,m,d)=>{f.attr("class",m);const{width:$,height:z,x:ee,y:te}=b(f,a);(0,w.v2)(f,z,$,d);const O=y(ee,te,$,z,a);f.attr("viewBox",O),w.cM.debug(`viewBox configured: ${O} with padding: ${a}`)},"setupViewPortForSVG"),b=(0,w.eW)((f,a)=>{var d;const m=((d=f.node())==null?void 0:d.getBBox())||{width:0,height:0,x:0,y:0};return{width:m.width+a*2,height:m.height+a*2,x:m.x,y:m.y}},"calculateDimensionsWithPadding"),y=(0,w.eW)((f,a,m,d,$)=>`${f-$} ${a-$} ${m} ${d}`,"createViewBox")},20541:function(v,u,o){var B;o.d(u,{diagram:function(){return ge}});var w=o(8031),g=o(57956),b=o(96547),y=o(50854),f=o(44133),a=o(29134),m=o(38663),d=o(69471),$=(B=class{constructor(){this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.setAccTitle=a.GN,this.getAccTitle=a.eu,this.setDiagramTitle=a.g2,this.getDiagramTitle=a.Kr,this.getAccDescription=a.Mx,this.setAccDescription=a.U$}getNodes(){return this.nodes}getConfig(){var n;const s=a.vZ,c=(0,a.iE)();return(0,y.Rb)(ae(ae({},s.treemap),(n=c.treemap)!=null?n:{}))}addNode(s,c){var n;this.nodes.push(s),this.levels.set(s,c),c===0&&(this.outerNodes.push(s),(n=this.root)!=null||(this.root=s))}getRoot(){return{name:"",children:this.outerNodes}}addClass(s,c){var i;const n=(i=this.classes.get(s))!=null?i:{id:s,styles:[],textStyles:[]},p=c.replace(/\\,/g,"\xA7\xA7\xA7").replace(/,/g,";").replace(/§§§/g,",").split(";");p&&p.forEach(L=>{(0,g.Fh)(L)&&(n!=null&&n.textStyles?n.textStyles.push(L):n.textStyles=[L]),n!=null&&n.styles?n.styles.push(L):n.styles=[L]}),this.classes.set(s,n)}getClasses(){return this.classes}getStylesForClass(s){var c,n;return(n=(c=this.classes.get(s))==null?void 0:c.styles)!=null?n:[]}clear(){(0,a.ZH)(),this.nodes=[],this.levels=new Map,this.outerNodes=[],this.classes=new Map,this.root=void 0}},(0,a.eW)(B,"TreeMapDB"),B);function z(S){if(!S.length)return[];const s=[],c=[];return S.forEach(n=>{const p={name:n.name,children:n.type==="Leaf"?void 0:[]};for(p.classSelector=n==null?void 0:n.classSelector,n!=null&&n.cssCompiledStyles&&(p.cssCompiledStyles=[n.cssCompiledStyles]),n.type==="Leaf"&&n.value!==void 0&&(p.value=n.value);c.length>0&&c[c.length-1].level>=n.level;)c.pop();if(c.length===0)s.push(p);else{const i=c[c.length-1].node;i.children?i.children.push(p):i.children=[p]}n.type!=="Leaf"&&c.push({node:p,level:n.level})}),s}(0,a.eW)(z,"buildHierarchy");var ee=(0,a.eW)((S,s)=>{var i,L,N,U;(0,b.A)(S,s);const c=[];for(const l of(i=S.TreemapRows)!=null?i:[])l.$type==="ClassDefStatement"&&s.addClass((L=l.className)!=null?L:"",(N=l.styleText)!=null?N:"");for(const l of(U=S.TreemapRows)!=null?U:[]){const x=l.item;if(!x)continue;const C=l.indent?parseInt(l.indent):0,X=te(x),H=x.classSelector?s.getStylesForClass(x.classSelector):[],G=H.length>0?H.join(";"):void 0,Z={level:C,name:X,type:x.$type,value:x.value,classSelector:x.classSelector,cssCompiledStyles:G};c.push(Z)}const n=z(c),p=(0,a.eW)((l,x)=>{for(const C of l)s.addNode(C,x),C.children&&C.children.length>0&&p(C.children,x+1)},"addNodesRecursively");p(n,0)},"populate"),te=(0,a.eW)(S=>S.name?String(S.name):"","getItemName"),O={parser:{yy:void 0},parse:(0,a.eW)(S=>ie(this,null,function*(){var s;try{const c=m.Qc,n=yield c("treemap",S);a.cM.debug("Treemap AST:",n);const p=(s=O.parser)==null?void 0:s.yy;if(!(p instanceof $))throw new Error("parser.parser?.yy was not a TreemapDB. This is due to a bug within Mermaid, please report this issue at https://github.com/mermaid-js/mermaid/issues.");ee(n,p)}catch(c){throw a.cM.error("Error parsing treemap:",c),c}}),"parse")},de=10,k=10,I=25,he=(0,a.eW)((S,s,c,n)=>{var ne,oe;const p=n.db,i=p.getConfig(),L=(ne=i.padding)!=null?ne:de,N=p.getDiagramTitle(),U=p.getRoot(),{themeVariables:l}=(0,a.iE)();if(!U)return;const x=N?30:0,C=(0,f.P)(s),X=i.nodeWidth?i.nodeWidth*k:960,H=i.nodeHeight?i.nodeHeight*k:500,G=X,Z=H+x;C.attr("viewBox",`0 0 ${G} ${Z}`),(0,a.v2)(C,Z,G,i.useMaxWidth);let A;try{const e=i.valueFormat||",";if(e==="$0,0")A=(0,a.eW)(t=>"$"+(0,d.WUZ)(",")(t),"valueFormat");else if(e.startsWith("$")&&e.includes(",")){const t=/\.\d+/.exec(e),r=t?t[0]:"";A=(0,a.eW)(_=>"$"+(0,d.WUZ)(","+r)(_),"valueFormat")}else if(e.startsWith("$")){const t=e.substring(1);A=(0,a.eW)(r=>"$"+(0,d.WUZ)(t||"")(r),"valueFormat")}else A=(0,d.WUZ)(e)}catch(e){a.cM.error("Error creating format function:",e),A=(0,d.WUZ)(",")}const K=(0,d.PKp)().range(["transparent",l.cScale0,l.cScale1,l.cScale2,l.cScale3,l.cScale4,l.cScale5,l.cScale6,l.cScale7,l.cScale8,l.cScale9,l.cScale10,l.cScale11]),Se=(0,d.PKp)().range(["transparent",l.cScalePeer0,l.cScalePeer1,l.cScalePeer2,l.cScalePeer3,l.cScalePeer4,l.cScalePeer5,l.cScalePeer6,l.cScalePeer7,l.cScalePeer8,l.cScalePeer9,l.cScalePeer10,l.cScalePeer11]),J=(0,d.PKp)().range([l.cScaleLabel0,l.cScaleLabel1,l.cScaleLabel2,l.cScaleLabel3,l.cScaleLabel4,l.cScaleLabel5,l.cScaleLabel6,l.cScaleLabel7,l.cScaleLabel8,l.cScaleLabel9,l.cScaleLabel10,l.cScaleLabel11]);N&&C.append("text").attr("x",G/2).attr("y",x/2).attr("class","treemapTitle").attr("text-anchor","middle").attr("dominant-baseline","middle").text(N);const le=C.append("g").attr("transform",`translate(0, ${x})`).attr("class","treemapContainer"),xe=(0,d.bT9)(U).sum(e=>{var t;return(t=e.value)!=null?t:0}).sort((e,t)=>{var r,_;return((r=t.value)!=null?r:0)-((_=e.value)!=null?_:0)}),se=(0,d.pNI)().size([X,H]).paddingTop(e=>e.children&&e.children.length>0?I+k:0).paddingInner(L).paddingLeft(e=>e.children&&e.children.length>0?k:0).paddingRight(e=>e.children&&e.children.length>0?k:0).paddingBottom(e=>e.children&&e.children.length>0?k:0).round(!0)(xe),_e=se.descendants().filter(e=>e.children&&e.children.length>0),j=le.selectAll(".treemapSection").data(_e).enter().append("g").attr("class","treemapSection").attr("transform",e=>`translate(${e.x0},${e.y0})`);j.append("rect").attr("width",e=>e.x1-e.x0).attr("height",I).attr("class","treemapSectionHeader").attr("fill","none").attr("fill-opacity",.6).attr("stroke-width",.6).attr("style",e=>e.depth===0?"display: none;":""),j.append("clipPath").attr("id",(e,t)=>`clip-section-${s}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-12)).attr("height",I),j.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class",(e,t)=>`treemapSection section${t}`).attr("fill",e=>K(e.data.name)).attr("fill-opacity",.6).attr("stroke",e=>Se(e.data.name)).attr("stroke-width",2).attr("stroke-opacity",.4).attr("style",e=>{if(e.depth===0)return"display: none;";const t=(0,g.UG)({cssCompiledStyles:e.data.cssCompiledStyles});return t.nodeStyles+";"+t.borderStyles.join(";")}),j.append("text").attr("class","treemapSectionLabel").attr("x",6).attr("y",I/2).attr("dominant-baseline","middle").text(e=>e.depth===0?"":e.data.name).attr("font-weight","bold").attr("style",e=>{if(e.depth===0)return"display: none;";const t="dominant-baseline: middle; font-size: 12px; fill:"+J(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",r=(0,g.UG)({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")}).each(function(e){if(e.depth===0)return;const t=(0,d.Ys)(this),r=e.data.name;t.text(r);const _=e.x1-e.x0,E=6;let M;i.showValues!==!1&&e.value?M=_-10-30-10-E:M=_-E-6;const T=Math.max(15,M),h=t.node();if(h.getComputedTextLength()>T){const P="...";let W=r;for(;W.length>0;){if(W=r.substring(0,W.length-1),W.length===0){t.text(P),h.getComputedTextLength()>T&&t.text("");break}if(t.text(W+P),h.getComputedTextLength()<=T)break}}}),i.showValues!==!1&&j.append("text").attr("class","treemapSectionValue").attr("x",e=>e.x1-e.x0-10).attr("y",I/2).attr("text-anchor","end").attr("dominant-baseline","middle").text(e=>e.value?A(e.value):"").attr("font-style","italic").attr("style",e=>{if(e.depth===0)return"display: none;";const t="text-anchor: end; dominant-baseline: middle; font-size: 10px; fill:"+J(e.data.name)+"; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;",r=(0,g.UG)({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")});const ve=se.leaves(),Q=le.selectAll(".treemapLeafGroup").data(ve).enter().append("g").attr("class",(e,t)=>`treemapNode treemapLeafGroup leaf${t}${e.data.classSelector?` ${e.data.classSelector}`:""}x`).attr("transform",e=>`translate(${e.x0},${e.y0})`);Q.append("rect").attr("width",e=>e.x1-e.x0).attr("height",e=>e.y1-e.y0).attr("class","treemapLeaf").attr("fill",e=>e.parent?K(e.parent.data.name):K(e.data.name)).attr("style",e=>(0,g.UG)({cssCompiledStyles:e.data.cssCompiledStyles}).nodeStyles).attr("fill-opacity",.3).attr("stroke",e=>e.parent?K(e.parent.data.name):K(e.data.name)).attr("stroke-width",3),Q.append("clipPath").attr("id",(e,t)=>`clip-${s}-${t}`).append("rect").attr("width",e=>Math.max(0,e.x1-e.x0-4)).attr("height",e=>Math.max(0,e.y1-e.y0-4)),Q.append("text").attr("class","treemapLabel").attr("x",e=>(e.x1-e.x0)/2).attr("y",e=>(e.y1-e.y0)/2).attr("style",e=>{const t="text-anchor: middle; dominant-baseline: middle; font-size: 38px;fill:"+J(e.data.name)+";",r=(0,g.UG)({cssCompiledStyles:e.data.cssCompiledStyles});return t+r.labelStyles.replace("color:","fill:")}).attr("clip-path",(e,t)=>`url(#clip-${s}-${t})`).text(e=>e.data.name).each(function(e){const t=(0,d.Ys)(this),r=e.x1-e.x0,_=e.y1-e.y0,E=t.node(),M=4,R=r-2*M,T=_-2*M;if(R<10||T<10){t.style("display","none");return}let h=parseInt(t.style("font-size"),10);const F=8,P=28,W=.6,D=6,Y=2;for(;E.getComputedTextLength()>R&&h>F;)h--,t.style("font-size",`${h}px`);let V=Math.max(D,Math.min(P,Math.round(h*W))),q=h+Y+V;for(;q>T&&h>F&&(h--,V=Math.max(D,Math.min(P,Math.round(h*W))),!(V<D&&h===F));)t.style("font-size",`${h}px`),q=h+Y+V,V<=D&&q>T;t.style("font-size",`${h}px`),(E.getComputedTextLength()>R||h<F||T<h)&&t.style("display","none")}),i.showValues!==!1&&Q.append("text").attr("class","treemapValue").attr("x",t=>(t.x1-t.x0)/2).attr("y",function(t){return(t.y1-t.y0)/2}).attr("style",t=>{const r="text-anchor: middle; dominant-baseline: hanging; font-size: 28px;fill:"+J(t.data.name)+";",_=(0,g.UG)({cssCompiledStyles:t.data.cssCompiledStyles});return r+_.labelStyles.replace("color:","fill:")}).attr("clip-path",(t,r)=>`url(#clip-${s}-${r})`).text(t=>t.value?A(t.value):"").each(function(t){const r=(0,d.Ys)(this),_=this.parentNode;if(!_){r.style("display","none");return}const E=(0,d.Ys)(_).select(".treemapLabel");if(E.empty()||E.style("display")==="none"){r.style("display","none");return}const M=parseFloat(E.style("font-size")),R=28,T=.6,h=6,F=2,P=Math.max(h,Math.min(R,Math.round(M*T)));r.style("font-size",`${P}px`);const D=(t.y1-t.y0)/2+M/2+F;r.attr("y",D);const Y=t.x1-t.x0,Ce=t.y1-t.y0-4,we=Y-2*4;r.node().getComputedTextLength()>we||D+P>Ce||P<h?r.style("display","none"):r.style("display",null)});const be=(oe=i.diagramPadding)!=null?oe:8;(0,w.j)(C,be,"flowchart",(i==null?void 0:i.useMaxWidth)||!1)},"draw"),pe=(0,a.eW)(function(S,s){return s.db.getClasses()},"getClasses"),ue={draw:he,getClasses:pe},fe={sectionStrokeColor:"black",sectionStrokeWidth:"1",sectionFillColor:"#efefef",leafStrokeColor:"black",leafStrokeWidth:"1",leafFillColor:"#efefef",labelColor:"black",labelFontSize:"12px",valueFontSize:"10px",valueColor:"black",titleColor:"black",titleFontSize:"14px"},me=(0,a.eW)(({treemap:S}={})=>{const s=(0,y.Rb)(fe,S);return`
  .treemapNode.section {
    stroke: ${s.sectionStrokeColor};
    stroke-width: ${s.sectionStrokeWidth};
    fill: ${s.sectionFillColor};
  }
  .treemapNode.leaf {
    stroke: ${s.leafStrokeColor};
    stroke-width: ${s.leafStrokeWidth};
    fill: ${s.leafFillColor};
  }
  .treemapLabel {
    fill: ${s.labelColor};
    font-size: ${s.labelFontSize};
  }
  .treemapValue {
    fill: ${s.valueColor};
    font-size: ${s.valueFontSize};
  }
  .treemapTitle {
    fill: ${s.titleColor};
    font-size: ${s.titleFontSize};
  }
  `},"getStyles"),ye=me,ge={parser:O,get db(){return new $},renderer:ue,styles:ye}}}]);
}());