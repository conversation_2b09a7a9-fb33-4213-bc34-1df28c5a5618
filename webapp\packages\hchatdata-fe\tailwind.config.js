/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    // 包含genie-ui的源码路径，以便在开发环境中正确扫描类名
    "../genie-ui/src/**/*.{js,ts,jsx,tsx}",
    // 也包含chat-sdk的路径
    "../chat-sdk/src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#4040ff',
        'primary-hover': '#656cff',
        // 确保标准颜色可用
        red: {
          500: '#ef4444',
        },
        gray: {
          200: '#e5e7eb',
          600: '#4b5563',
        },
        blue: {
          500: '#3b82f6',
        },
      },
      spacing: {
        '4': '4px',
        '6': '6px',
        '8': '8px',
        '12': '12px',
        '16': '16px',
        '20': '20px',
        '24': '24px',
        '32': '32px',
        '40': '40px',
        '48': '48px',
        '64': '64px',
        '80': '80px',
        '100': '100px',
        '120': '120px',
        '640': '640px',
      },
      fontSize: {
        '12': '12px',
        '14': '14px',
        '16': '16px',
        '18': '18px',
        '20': '20px',
        '24': '24px',
      },
      lineHeight: {
        '20': '20px',
        '24': '24px',
        '28': '28px',
        '32': '32px',
      }
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false, // 禁用 Tailwind 的基础样式重置，避免与 antd 冲突
    // 确保 border 相关的核心插件启用
    borderColor: true,
    borderStyle: true,
    borderWidth: true,
    borderRadius: true,
  }
};
