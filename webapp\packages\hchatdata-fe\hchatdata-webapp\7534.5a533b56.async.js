!(function(){var ye=Object.defineProperty,Ee=Object.defineProperties;var me=Object.getOwnPropertyDescriptors;var ve=Object.getOwnPropertySymbols;var Te=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable;var de=(Xt,it,st)=>it in Xt?ye(Xt,it,{enumerable:!0,configurable:!0,writable:!0,value:st}):Xt[it]=st,ie=(Xt,it)=>{for(var st in it||(it={}))Te.call(it,st)&&de(Xt,st,it[st]);if(ve)for(var st of ve(it))Ne.call(it,st)&&de(Xt,st,it[st]);return Xt},ne=(Xt,it)=>Ee(Xt,me(it));var ee=(Xt,it,st)=>new Promise((D,E)=>{var s=t=>{try{i(st.next(t))}catch(y){E(y)}},o=t=>{try{i(st.throw(t))}catch(y){E(y)}},i=t=>t.done?D(t.value):Promise.resolve(t.value).then(s,o);i((st=st.apply(Xt,it)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[7534],{49160:function(Xt,it,st){(function(E,s){Xt.exports=s(st(65084))})(this,function(D){return(()=>{"use strict";var E={45:(t,y,r)=>{var d={};d.layoutBase=r(551),d.CoSEConstants=r(806),d.CoSEEdge=r(767),d.CoSEGraph=r(880),d.CoSEGraphManager=r(578),d.CoSELayout=r(765),d.CoSENode=r(991),d.ConstraintHandler=r(902),t.exports=d},806:(t,y,r)=>{var d=r(551).FDLayoutConstants;function e(){}for(var a in d)e[a]=d[a];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=d.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,e.ENFORCE_CONSTRAINTS=!0,e.APPLY_LAYOUT=!0,e.RELAX_MOVEMENT_ON_CONSTRAINTS=!0,e.TREE_REDUCTION_ON_INCREMENTAL=!0,e.PURE_INCREMENTAL=e.DEFAULT_INCREMENTAL,t.exports=e},767:(t,y,r)=>{var d=r(551).FDLayoutEdge;function e(l,u,c){d.call(this,l,u,c)}e.prototype=Object.create(d.prototype);for(var a in d)e[a]=d[a];t.exports=e},880:(t,y,r)=>{var d=r(551).LGraph;function e(l,u,c){d.call(this,l,u,c)}e.prototype=Object.create(d.prototype);for(var a in d)e[a]=d[a];t.exports=e},578:(t,y,r)=>{var d=r(551).LGraphManager;function e(l){d.call(this,l)}e.prototype=Object.create(d.prototype);for(var a in d)e[a]=d[a];t.exports=e},765:(t,y,r)=>{var d=r(551).FDLayout,e=r(578),a=r(880),l=r(991),u=r(767),c=r(806),T=r(902),h=r(551).FDLayoutConstants,A=r(551).LayoutConstants,N=r(551).Point,L=r(551).PointD,x=r(551).DimensionD,_=r(551).Layout,I=r(551).Integer,nt=r(551).IGeometry,dt=r(551).LGraph,ut=r(551).Transform,Nt=r(551).LinkedList;function U(){d.call(this),this.toBeTiled={},this.constraints={}}U.prototype=Object.create(d.prototype);for(var Z in d)U[Z]=d[Z];U.prototype.newGraphManager=function(){var n=new e(this);return this.graphManager=n,n},U.prototype.newGraph=function(n){return new a(null,this.graphManager,n)},U.prototype.newNode=function(n){return new l(this.graphManager,n)},U.prototype.newEdge=function(n){return new u(null,null,n)},U.prototype.initParameters=function(){d.prototype.initParameters.call(this,arguments),this.isSubLayout||(c.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=c.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=c.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=h.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=h.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=h.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1)},U.prototype.initSpringEmbedder=function(){d.prototype.initSpringEmbedder.call(this),this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/h.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=.04,this.coolingAdjuster=1},U.prototype.layout=function(){var n=A.DEFAULT_CREATE_BENDS_AS_NEEDED;return n&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},U.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(c.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var v=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(R){return v.has(R)});this.graphManager.setAllNodesToApplyGravitation(f)}}else{var n=this.getFlatForest();if(n.length>0)this.positionNodesRadially(n);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var v=new Set(this.getAllNodes()),f=this.nodesWithGravity.filter(function(m){return v.has(m)});this.graphManager.setAllNodesToApplyGravitation(f),this.positionNodesRandomly()}}return Object.keys(this.constraints).length>0&&(T.handleConstraints(this),this.initConstraintVariables()),this.initSpringEmbedder(),c.APPLY_LAYOUT&&this.runSpringEmbedder(),!0},U.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%h.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var n=new Set(this.getAllNodes()),v=this.nodesWithGravity.filter(function(p){return n.has(p)});this.graphManager.setAllNodesToApplyGravitation(v),this.graphManager.updateBounds(),this.updateGrid(),c.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2:this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),c.PURE_INCREMENTAL?this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL/2*((100-this.afterGrowthIterations)/100):this.coolingFactor=h.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var f=!this.isTreeGrowing&&!this.isGrowthFinished,m=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(f,m),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},U.prototype.getPositionsData=function(){for(var n=this.graphManager.getAllNodes(),v={},f=0;f<n.length;f++){var m=n[f].rect,p=n[f].id;v[p]={id:p,x:m.getCenterX(),y:m.getCenterY(),w:m.width,h:m.height}}return v},U.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var n=!1;if(h.ANIMATE==="during")this.emit("layoutstarted");else{for(;!n;)n=this.tick();this.graphManager.updateBounds()}},U.prototype.moveNodes=function(){for(var n=this.getAllNodes(),v,f=0;f<n.length;f++)v=n[f],v.calculateDisplacement();Object.keys(this.constraints).length>0&&this.updateDisplacements();for(var f=0;f<n.length;f++)v=n[f],v.move()},U.prototype.initConstraintVariables=function(){var n=this;this.idToNodeMap=new Map,this.fixedNodeSet=new Set;for(var v=this.graphManager.getAllNodes(),f=0;f<v.length;f++){var m=v[f];this.idToNodeMap.set(m.id,m)}var p=function w(B){for(var k=B.getChild().getNodes(),J,Et=0,at=0;at<k.length;at++)J=k[at],J.getChild()==null?n.fixedNodeSet.has(J.id)&&(Et+=100):Et+=w(J);return Et};if(this.constraints.fixedNodeConstraint){this.constraints.fixedNodeConstraint.forEach(function(k){n.fixedNodeSet.add(k.nodeId)});for(var v=this.graphManager.getAllNodes(),m,f=0;f<v.length;f++)if(m=v[f],m.getChild()!=null){var R=p(m);R>0&&(m.fixedNodeWeight=R)}}if(this.constraints.relativePlacementConstraint){var O=new Map,H=new Map;if(this.dummyToNodeForVerticalAlignment=new Map,this.dummyToNodeForHorizontalAlignment=new Map,this.fixedNodesOnHorizontal=new Set,this.fixedNodesOnVertical=new Set,this.fixedNodeSet.forEach(function(w){n.fixedNodesOnHorizontal.add(w),n.fixedNodesOnVertical.add(w)}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var X=this.constraints.alignmentConstraint.vertical,f=0;f<X.length;f++)this.dummyToNodeForVerticalAlignment.set("dummy"+f,[]),X[f].forEach(function(B){O.set(B,"dummy"+f),n.dummyToNodeForVerticalAlignment.get("dummy"+f).push(B),n.fixedNodeSet.has(B)&&n.fixedNodesOnHorizontal.add("dummy"+f)});if(this.constraints.alignmentConstraint.horizontal)for(var Y=this.constraints.alignmentConstraint.horizontal,f=0;f<Y.length;f++)this.dummyToNodeForHorizontalAlignment.set("dummy"+f,[]),Y[f].forEach(function(B){H.set(B,"dummy"+f),n.dummyToNodeForHorizontalAlignment.get("dummy"+f).push(B),n.fixedNodeSet.has(B)&&n.fixedNodesOnVertical.add("dummy"+f)})}if(c.RELAX_MOVEMENT_ON_CONSTRAINTS)this.shuffle=function(w){var B,k,J;for(J=w.length-1;J>=2*w.length/3;J--)B=Math.floor(Math.random()*(J+1)),k=w[J],w[J]=w[B],w[B]=k;return w},this.nodesInRelativeHorizontal=[],this.nodesInRelativeVertical=[],this.nodeToRelativeConstraintMapHorizontal=new Map,this.nodeToRelativeConstraintMapVertical=new Map,this.nodeToTempPositionMapHorizontal=new Map,this.nodeToTempPositionMapVertical=new Map,this.constraints.relativePlacementConstraint.forEach(function(w){if(w.left){var B=O.has(w.left)?O.get(w.left):w.left,k=O.has(w.right)?O.get(w.right):w.right;n.nodesInRelativeHorizontal.includes(B)||(n.nodesInRelativeHorizontal.push(B),n.nodeToRelativeConstraintMapHorizontal.set(B,[]),n.dummyToNodeForVerticalAlignment.has(B)?n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(B)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(B,n.idToNodeMap.get(B).getCenterX())),n.nodesInRelativeHorizontal.includes(k)||(n.nodesInRelativeHorizontal.push(k),n.nodeToRelativeConstraintMapHorizontal.set(k,[]),n.dummyToNodeForVerticalAlignment.has(k)?n.nodeToTempPositionMapHorizontal.set(k,n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(k)[0]).getCenterX()):n.nodeToTempPositionMapHorizontal.set(k,n.idToNodeMap.get(k).getCenterX())),n.nodeToRelativeConstraintMapHorizontal.get(B).push({right:k,gap:w.gap}),n.nodeToRelativeConstraintMapHorizontal.get(k).push({left:B,gap:w.gap})}else{var J=H.has(w.top)?H.get(w.top):w.top,Et=H.has(w.bottom)?H.get(w.bottom):w.bottom;n.nodesInRelativeVertical.includes(J)||(n.nodesInRelativeVertical.push(J),n.nodeToRelativeConstraintMapVertical.set(J,[]),n.dummyToNodeForHorizontalAlignment.has(J)?n.nodeToTempPositionMapVertical.set(J,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(J)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(J,n.idToNodeMap.get(J).getCenterY())),n.nodesInRelativeVertical.includes(Et)||(n.nodesInRelativeVertical.push(Et),n.nodeToRelativeConstraintMapVertical.set(Et,[]),n.dummyToNodeForHorizontalAlignment.has(Et)?n.nodeToTempPositionMapVertical.set(Et,n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(Et)[0]).getCenterY()):n.nodeToTempPositionMapVertical.set(Et,n.idToNodeMap.get(Et).getCenterY())),n.nodeToRelativeConstraintMapVertical.get(J).push({bottom:Et,gap:w.gap}),n.nodeToRelativeConstraintMapVertical.get(Et).push({top:J,gap:w.gap})}});else{var rt=new Map,ht=new Map;this.constraints.relativePlacementConstraint.forEach(function(w){if(w.left){var B=O.has(w.left)?O.get(w.left):w.left,k=O.has(w.right)?O.get(w.right):w.right;rt.has(B)?rt.get(B).push(k):rt.set(B,[k]),rt.has(k)?rt.get(k).push(B):rt.set(k,[B])}else{var J=H.has(w.top)?H.get(w.top):w.top,Et=H.has(w.bottom)?H.get(w.bottom):w.bottom;ht.has(J)?ht.get(J).push(Et):ht.set(J,[Et]),ht.has(Et)?ht.get(Et).push(J):ht.set(Et,[J])}});var z=function(B,k){var J=[],Et=[],at=new Nt,bt=new Set,At=0;return B.forEach(function(Vt,Gt){if(!bt.has(Gt)){J[At]=[],Et[At]=!1;var pt=Gt;for(at.push(pt),bt.add(pt),J[At].push(pt);at.length!=0;){pt=at.shift(),k.has(pt)&&(Et[At]=!0);var gt=B.get(pt);gt.forEach(function(It){bt.has(It)||(at.push(It),bt.add(It),J[At].push(It))})}At++}}),{components:J,isFixed:Et}},lt=z(rt,n.fixedNodesOnHorizontal);this.componentsOnHorizontal=lt.components,this.fixedComponentsOnHorizontal=lt.isFixed;var tt=z(ht,n.fixedNodesOnVertical);this.componentsOnVertical=tt.components,this.fixedComponentsOnVertical=tt.isFixed}}},U.prototype.updateDisplacements=function(){var n=this;if(this.constraints.fixedNodeConstraint&&this.constraints.fixedNodeConstraint.forEach(function(tt){var w=n.idToNodeMap.get(tt.nodeId);w.displacementX=0,w.displacementY=0}),this.constraints.alignmentConstraint){if(this.constraints.alignmentConstraint.vertical)for(var v=this.constraints.alignmentConstraint.vertical,f=0;f<v.length;f++){for(var m=0,p=0;p<v[f].length;p++){if(this.fixedNodeSet.has(v[f][p])){m=0;break}m+=this.idToNodeMap.get(v[f][p]).displacementX}for(var R=m/v[f].length,p=0;p<v[f].length;p++)this.idToNodeMap.get(v[f][p]).displacementX=R}if(this.constraints.alignmentConstraint.horizontal)for(var O=this.constraints.alignmentConstraint.horizontal,f=0;f<O.length;f++){for(var H=0,p=0;p<O[f].length;p++){if(this.fixedNodeSet.has(O[f][p])){H=0;break}H+=this.idToNodeMap.get(O[f][p]).displacementY}for(var X=H/O[f].length,p=0;p<O[f].length;p++)this.idToNodeMap.get(O[f][p]).displacementY=X}}if(this.constraints.relativePlacementConstraint)if(c.RELAX_MOVEMENT_ON_CONSTRAINTS)this.totalIterations%10==0&&(this.shuffle(this.nodesInRelativeHorizontal),this.shuffle(this.nodesInRelativeVertical)),this.nodesInRelativeHorizontal.forEach(function(tt){if(!n.fixedNodesOnHorizontal.has(tt)){var w=0;n.dummyToNodeForVerticalAlignment.has(tt)?w=n.idToNodeMap.get(n.dummyToNodeForVerticalAlignment.get(tt)[0]).displacementX:w=n.idToNodeMap.get(tt).displacementX,n.nodeToRelativeConstraintMapHorizontal.get(tt).forEach(function(B){if(B.right){var k=n.nodeToTempPositionMapHorizontal.get(B.right)-n.nodeToTempPositionMapHorizontal.get(tt)-w;k<B.gap&&(w-=B.gap-k)}else{var k=n.nodeToTempPositionMapHorizontal.get(tt)-n.nodeToTempPositionMapHorizontal.get(B.left)+w;k<B.gap&&(w+=B.gap-k)}}),n.nodeToTempPositionMapHorizontal.set(tt,n.nodeToTempPositionMapHorizontal.get(tt)+w),n.dummyToNodeForVerticalAlignment.has(tt)?n.dummyToNodeForVerticalAlignment.get(tt).forEach(function(B){n.idToNodeMap.get(B).displacementX=w}):n.idToNodeMap.get(tt).displacementX=w}}),this.nodesInRelativeVertical.forEach(function(tt){if(!n.fixedNodesOnHorizontal.has(tt)){var w=0;n.dummyToNodeForHorizontalAlignment.has(tt)?w=n.idToNodeMap.get(n.dummyToNodeForHorizontalAlignment.get(tt)[0]).displacementY:w=n.idToNodeMap.get(tt).displacementY,n.nodeToRelativeConstraintMapVertical.get(tt).forEach(function(B){if(B.bottom){var k=n.nodeToTempPositionMapVertical.get(B.bottom)-n.nodeToTempPositionMapVertical.get(tt)-w;k<B.gap&&(w-=B.gap-k)}else{var k=n.nodeToTempPositionMapVertical.get(tt)-n.nodeToTempPositionMapVertical.get(B.top)+w;k<B.gap&&(w+=B.gap-k)}}),n.nodeToTempPositionMapVertical.set(tt,n.nodeToTempPositionMapVertical.get(tt)+w),n.dummyToNodeForHorizontalAlignment.has(tt)?n.dummyToNodeForHorizontalAlignment.get(tt).forEach(function(B){n.idToNodeMap.get(B).displacementY=w}):n.idToNodeMap.get(tt).displacementY=w}});else{for(var f=0;f<this.componentsOnHorizontal.length;f++){var Y=this.componentsOnHorizontal[f];if(this.fixedComponentsOnHorizontal[f])for(var p=0;p<Y.length;p++)this.dummyToNodeForVerticalAlignment.has(Y[p])?this.dummyToNodeForVerticalAlignment.get(Y[p]).forEach(function(B){n.idToNodeMap.get(B).displacementX=0}):this.idToNodeMap.get(Y[p]).displacementX=0;else{for(var rt=0,ht=0,p=0;p<Y.length;p++)if(this.dummyToNodeForVerticalAlignment.has(Y[p])){var z=this.dummyToNodeForVerticalAlignment.get(Y[p]);rt+=z.length*this.idToNodeMap.get(z[0]).displacementX,ht+=z.length}else rt+=this.idToNodeMap.get(Y[p]).displacementX,ht++;for(var lt=rt/ht,p=0;p<Y.length;p++)this.dummyToNodeForVerticalAlignment.has(Y[p])?this.dummyToNodeForVerticalAlignment.get(Y[p]).forEach(function(B){n.idToNodeMap.get(B).displacementX=lt}):this.idToNodeMap.get(Y[p]).displacementX=lt}}for(var f=0;f<this.componentsOnVertical.length;f++){var Y=this.componentsOnVertical[f];if(this.fixedComponentsOnVertical[f])for(var p=0;p<Y.length;p++)this.dummyToNodeForHorizontalAlignment.has(Y[p])?this.dummyToNodeForHorizontalAlignment.get(Y[p]).forEach(function(k){n.idToNodeMap.get(k).displacementY=0}):this.idToNodeMap.get(Y[p]).displacementY=0;else{for(var rt=0,ht=0,p=0;p<Y.length;p++)if(this.dummyToNodeForHorizontalAlignment.has(Y[p])){var z=this.dummyToNodeForHorizontalAlignment.get(Y[p]);rt+=z.length*this.idToNodeMap.get(z[0]).displacementY,ht+=z.length}else rt+=this.idToNodeMap.get(Y[p]).displacementY,ht++;for(var lt=rt/ht,p=0;p<Y.length;p++)this.dummyToNodeForHorizontalAlignment.has(Y[p])?this.dummyToNodeForHorizontalAlignment.get(Y[p]).forEach(function(at){n.idToNodeMap.get(at).displacementY=lt}):this.idToNodeMap.get(Y[p]).displacementY=lt}}}},U.prototype.calculateNodesToApplyGravitationTo=function(){var n=[],v,f=this.graphManager.getGraphs(),m=f.length,p;for(p=0;p<m;p++)v=f[p],v.updateConnected(),v.isConnected||(n=n.concat(v.getNodes()));return n},U.prototype.createBendpoints=function(){var n=[];n=n.concat(this.graphManager.getAllEdges());var v=new Set,f;for(f=0;f<n.length;f++){var m=n[f];if(!v.has(m)){var p=m.getSource(),R=m.getTarget();if(p==R)m.getBendpoints().push(new L),m.getBendpoints().push(new L),this.createDummyNodesForBendpoints(m),v.add(m);else{var O=[];if(O=O.concat(p.getEdgeListToNode(R)),O=O.concat(R.getEdgeListToNode(p)),!v.has(O[0])){if(O.length>1){var H;for(H=0;H<O.length;H++){var X=O[H];X.getBendpoints().push(new L),this.createDummyNodesForBendpoints(X)}}O.forEach(function(Y){v.add(Y)})}}}if(v.size==n.length)break}},U.prototype.positionNodesRadially=function(n){for(var v=new N(0,0),f=Math.ceil(Math.sqrt(n.length)),m=0,p=0,R=0,O=new L(0,0),H=0;H<n.length;H++){H%f==0&&(R=0,p=m,H!=0&&(p+=c.DEFAULT_COMPONENT_SEPERATION),m=0);var X=n[H],Y=_.findCenterOfTree(X);v.x=R,v.y=p,O=U.radialLayout(X,Y,v),O.y>m&&(m=Math.floor(O.y)),R=Math.floor(O.x+c.DEFAULT_COMPONENT_SEPERATION)}this.transform(new L(A.WORLD_CENTER_X-O.x/2,A.WORLD_CENTER_Y-O.y/2))},U.radialLayout=function(n,v,f){var m=Math.max(this.maxDiagonalInTree(n),c.DEFAULT_RADIAL_SEPARATION);U.branchRadialLayout(v,null,0,359,0,m);var p=dt.calculateBounds(n),R=new ut;R.setDeviceOrgX(p.getMinX()),R.setDeviceOrgY(p.getMinY()),R.setWorldOrgX(f.x),R.setWorldOrgY(f.y);for(var O=0;O<n.length;O++){var H=n[O];H.transform(R)}var X=new L(p.getMaxX(),p.getMaxY());return R.inverseTransformPoint(X)},U.branchRadialLayout=function(n,v,f,m,p,R){var O=(m-f+1)/2;O<0&&(O+=180);var H=(O+f)%360,X=H*nt.TWO_PI/360,Y=Math.cos(X),rt=p*Math.cos(X),ht=p*Math.sin(X);n.setCenter(rt,ht);var z=[];z=z.concat(n.getEdges());var lt=z.length;v!=null&&lt--;for(var tt=0,w=z.length,B,k=n.getEdgesBetween(v);k.length>1;){var J=k[0];k.splice(0,1);var Et=z.indexOf(J);Et>=0&&z.splice(Et,1),w--,lt--}v!=null?B=(z.indexOf(k[0])+1)%w:B=0;for(var at=Math.abs(m-f)/lt,bt=B;tt!=lt;bt=++bt%w){var At=z[bt].getOtherEnd(n);if(At!=v){var Vt=(f+tt*at)%360,Gt=(Vt+at)%360;U.branchRadialLayout(At,n,Vt,Gt,p+R,R),tt++}}},U.maxDiagonalInTree=function(n){for(var v=I.MIN_VALUE,f=0;f<n.length;f++){var m=n[f],p=m.getDiagonal();p>v&&(v=p)}return v},U.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},U.prototype.groupZeroDegreeMembers=function(){var n=this,v={};this.memberGroups={},this.idToDummyNode={};for(var f=[],m=this.graphManager.getAllNodes(),p=0;p<m.length;p++){var R=m[p],O=R.getParent();this.getNodeDegreeWithChildren(R)===0&&(O.id==null||!this.getToBeTiled(O))&&f.push(R)}for(var p=0;p<f.length;p++){var R=f[p],H=R.getParent().id;typeof v[H]=="undefined"&&(v[H]=[]),v[H]=v[H].concat(R)}Object.keys(v).forEach(function(X){if(v[X].length>1){var Y="DummyCompound_"+X;n.memberGroups[Y]=v[X];var rt=v[X][0].getParent(),ht=new l(n.graphManager);ht.id=Y,ht.paddingLeft=rt.paddingLeft||0,ht.paddingRight=rt.paddingRight||0,ht.paddingBottom=rt.paddingBottom||0,ht.paddingTop=rt.paddingTop||0,n.idToDummyNode[Y]=ht;var z=n.getGraphManager().add(n.newGraph(),ht),lt=rt.getChild();lt.add(ht);for(var tt=0;tt<v[X].length;tt++){var w=v[X][tt];lt.remove(w),z.add(w)}}})},U.prototype.clearCompounds=function(){var n={},v={};this.performDFSOnCompounds();for(var f=0;f<this.compoundOrder.length;f++)v[this.compoundOrder[f].id]=this.compoundOrder[f],n[this.compoundOrder[f].id]=[].concat(this.compoundOrder[f].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[f].getChild()),this.compoundOrder[f].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(n,v)},U.prototype.clearZeroDegreeMembers=function(){var n=this,v=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(f){var m=n.idToDummyNode[f];if(v[f]=n.tileNodes(n.memberGroups[f],m.paddingLeft+m.paddingRight),m.rect.width=v[f].width,m.rect.height=v[f].height,m.setCenter(v[f].centerX,v[f].centerY),m.labelMarginLeft=0,m.labelMarginTop=0,c.NODE_DIMENSIONS_INCLUDE_LABELS){var p=m.rect.width,R=m.rect.height;m.labelWidth&&(m.labelPosHorizontal=="left"?(m.rect.x-=m.labelWidth,m.setWidth(p+m.labelWidth),m.labelMarginLeft=m.labelWidth):m.labelPosHorizontal=="center"&&m.labelWidth>p?(m.rect.x-=(m.labelWidth-p)/2,m.setWidth(m.labelWidth),m.labelMarginLeft=(m.labelWidth-p)/2):m.labelPosHorizontal=="right"&&m.setWidth(p+m.labelWidth)),m.labelHeight&&(m.labelPosVertical=="top"?(m.rect.y-=m.labelHeight,m.setHeight(R+m.labelHeight),m.labelMarginTop=m.labelHeight):m.labelPosVertical=="center"&&m.labelHeight>R?(m.rect.y-=(m.labelHeight-R)/2,m.setHeight(m.labelHeight),m.labelMarginTop=(m.labelHeight-R)/2):m.labelPosVertical=="bottom"&&m.setHeight(R+m.labelHeight))}})},U.prototype.repopulateCompounds=function(){for(var n=this.compoundOrder.length-1;n>=0;n--){var v=this.compoundOrder[n],f=v.id,m=v.paddingLeft,p=v.paddingTop,R=v.labelMarginLeft,O=v.labelMarginTop;this.adjustLocations(this.tiledMemberPack[f],v.rect.x,v.rect.y,m,p,R,O)}},U.prototype.repopulateZeroDegreeMembers=function(){var n=this,v=this.tiledZeroDegreePack;Object.keys(v).forEach(function(f){var m=n.idToDummyNode[f],p=m.paddingLeft,R=m.paddingTop,O=m.labelMarginLeft,H=m.labelMarginTop;n.adjustLocations(v[f],m.rect.x,m.rect.y,p,R,O,H)})},U.prototype.getToBeTiled=function(n){var v=n.id;if(this.toBeTiled[v]!=null)return this.toBeTiled[v];var f=n.getChild();if(f==null)return this.toBeTiled[v]=!1,!1;for(var m=f.getNodes(),p=0;p<m.length;p++){var R=m[p];if(this.getNodeDegree(R)>0)return this.toBeTiled[v]=!1,!1;if(R.getChild()==null){this.toBeTiled[R.id]=!1;continue}if(!this.getToBeTiled(R))return this.toBeTiled[v]=!1,!1}return this.toBeTiled[v]=!0,!0},U.prototype.getNodeDegree=function(n){for(var v=n.id,f=n.getEdges(),m=0,p=0;p<f.length;p++){var R=f[p];R.getSource().id!==R.getTarget().id&&(m=m+1)}return m},U.prototype.getNodeDegreeWithChildren=function(n){var v=this.getNodeDegree(n);if(n.getChild()==null)return v;for(var f=n.getChild().getNodes(),m=0;m<f.length;m++){var p=f[m];v+=this.getNodeDegreeWithChildren(p)}return v},U.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},U.prototype.fillCompexOrderByDFS=function(n){for(var v=0;v<n.length;v++){var f=n[v];f.getChild()!=null&&this.fillCompexOrderByDFS(f.getChild().getNodes()),this.getToBeTiled(f)&&this.compoundOrder.push(f)}},U.prototype.adjustLocations=function(n,v,f,m,p,R,O){v+=m+R,f+=p+O;for(var H=v,X=0;X<n.rows.length;X++){var Y=n.rows[X];v=H;for(var rt=0,ht=0;ht<Y.length;ht++){var z=Y[ht];z.rect.x=v,z.rect.y=f,v+=z.rect.width+n.horizontalPadding,z.rect.height>rt&&(rt=z.rect.height)}f+=rt+n.verticalPadding}},U.prototype.tileCompoundMembers=function(n,v){var f=this;this.tiledMemberPack=[],Object.keys(n).forEach(function(m){var p=v[m];if(f.tiledMemberPack[m]=f.tileNodes(n[m],p.paddingLeft+p.paddingRight),p.rect.width=f.tiledMemberPack[m].width,p.rect.height=f.tiledMemberPack[m].height,p.setCenter(f.tiledMemberPack[m].centerX,f.tiledMemberPack[m].centerY),p.labelMarginLeft=0,p.labelMarginTop=0,c.NODE_DIMENSIONS_INCLUDE_LABELS){var R=p.rect.width,O=p.rect.height;p.labelWidth&&(p.labelPosHorizontal=="left"?(p.rect.x-=p.labelWidth,p.setWidth(R+p.labelWidth),p.labelMarginLeft=p.labelWidth):p.labelPosHorizontal=="center"&&p.labelWidth>R?(p.rect.x-=(p.labelWidth-R)/2,p.setWidth(p.labelWidth),p.labelMarginLeft=(p.labelWidth-R)/2):p.labelPosHorizontal=="right"&&p.setWidth(R+p.labelWidth)),p.labelHeight&&(p.labelPosVertical=="top"?(p.rect.y-=p.labelHeight,p.setHeight(O+p.labelHeight),p.labelMarginTop=p.labelHeight):p.labelPosVertical=="center"&&p.labelHeight>O?(p.rect.y-=(p.labelHeight-O)/2,p.setHeight(p.labelHeight),p.labelMarginTop=(p.labelHeight-O)/2):p.labelPosVertical=="bottom"&&p.setHeight(O+p.labelHeight))}})},U.prototype.tileNodes=function(n,v){var f=this.tileNodesByFavoringDim(n,v,!0),m=this.tileNodesByFavoringDim(n,v,!1),p=this.getOrgRatio(f),R=this.getOrgRatio(m),O;return R<p?O=m:O=f,O},U.prototype.getOrgRatio=function(n){var v=n.width,f=n.height,m=v/f;return m<1&&(m=1/m),m},U.prototype.calcIdealRowWidth=function(n,v){var f=c.TILING_PADDING_VERTICAL,m=c.TILING_PADDING_HORIZONTAL,p=n.length,R=0,O=0,H=0;n.forEach(function(tt){R+=tt.getWidth(),O+=tt.getHeight(),tt.getWidth()>H&&(H=tt.getWidth())});var X=R/p,Y=O/p,rt=Math.pow(f-m,2)+4*(X+m)*(Y+f)*p,ht=(m-f+Math.sqrt(rt))/(2*(X+m)),z;v?(z=Math.ceil(ht),z==ht&&z++):z=Math.floor(ht);var lt=z*(X+m)-m;return H>lt&&(lt=H),lt+=m*2,lt},U.prototype.tileNodesByFavoringDim=function(n,v,f){var m=c.TILING_PADDING_VERTICAL,p=c.TILING_PADDING_HORIZONTAL,R=c.TILING_COMPARE_BY,O={rows:[],rowWidth:[],rowHeight:[],width:0,height:v,verticalPadding:m,horizontalPadding:p,centerX:0,centerY:0};R&&(O.idealRowWidth=this.calcIdealRowWidth(n,f));var H=function(w){return w.rect.width*w.rect.height},X=function(w,B){return H(B)-H(w)};n.sort(function(tt,w){var B=X;return O.idealRowWidth?(B=R,B(tt.id,w.id)):B(tt,w)});for(var Y=0,rt=0,ht=0;ht<n.length;ht++){var z=n[ht];Y+=z.getCenterX(),rt+=z.getCenterY()}O.centerX=Y/n.length,O.centerY=rt/n.length;for(var ht=0;ht<n.length;ht++){var z=n[ht];if(O.rows.length==0)this.insertNodeToRow(O,z,0,v);else if(this.canAddHorizontal(O,z.rect.width,z.rect.height)){var lt=O.rows.length-1;O.idealRowWidth||(lt=this.getShortestRowIndex(O)),this.insertNodeToRow(O,z,lt,v)}else this.insertNodeToRow(O,z,O.rows.length,v);this.shiftToLastRow(O)}return O},U.prototype.insertNodeToRow=function(n,v,f,m){var p=m;if(f==n.rows.length){var R=[];n.rows.push(R),n.rowWidth.push(p),n.rowHeight.push(0)}var O=n.rowWidth[f]+v.rect.width;n.rows[f].length>0&&(O+=n.horizontalPadding),n.rowWidth[f]=O,n.width<O&&(n.width=O);var H=v.rect.height;f>0&&(H+=n.verticalPadding);var X=0;H>n.rowHeight[f]&&(X=n.rowHeight[f],n.rowHeight[f]=H,X=n.rowHeight[f]-X),n.height+=X,n.rows[f].push(v)},U.prototype.getShortestRowIndex=function(n){for(var v=-1,f=Number.MAX_VALUE,m=0;m<n.rows.length;m++)n.rowWidth[m]<f&&(v=m,f=n.rowWidth[m]);return v},U.prototype.getLongestRowIndex=function(n){for(var v=-1,f=Number.MIN_VALUE,m=0;m<n.rows.length;m++)n.rowWidth[m]>f&&(v=m,f=n.rowWidth[m]);return v},U.prototype.canAddHorizontal=function(n,v,f){if(n.idealRowWidth){var m=n.rows.length-1,p=n.rowWidth[m];return p+v+n.horizontalPadding<=n.idealRowWidth}var R=this.getShortestRowIndex(n);if(R<0)return!0;var O=n.rowWidth[R];if(O+n.horizontalPadding+v<=n.width)return!0;var H=0;n.rowHeight[R]<f&&R>0&&(H=f+n.verticalPadding-n.rowHeight[R]);var X;n.width-O>=v+n.horizontalPadding?X=(n.height+H)/(O+v+n.horizontalPadding):X=(n.height+H)/n.width,H=f+n.verticalPadding;var Y;return n.width<v?Y=(n.height+H)/v:Y=(n.height+H)/n.width,Y<1&&(Y=1/Y),X<1&&(X=1/X),X<Y},U.prototype.shiftToLastRow=function(n){var v=this.getLongestRowIndex(n),f=n.rowWidth.length-1,m=n.rows[v],p=m[m.length-1],R=p.width+n.horizontalPadding;if(n.width-n.rowWidth[f]>R&&v!=f){m.splice(-1,1),n.rows[f].push(p),n.rowWidth[v]=n.rowWidth[v]-R,n.rowWidth[f]=n.rowWidth[f]+R,n.width=n.rowWidth[instance.getLongestRowIndex(n)];for(var O=Number.MIN_VALUE,H=0;H<m.length;H++)m[H].height>O&&(O=m[H].height);v>0&&(O+=n.verticalPadding);var X=n.rowHeight[v]+n.rowHeight[f];n.rowHeight[v]=O,n.rowHeight[f]<p.height+n.verticalPadding&&(n.rowHeight[f]=p.height+n.verticalPadding);var Y=n.rowHeight[v]+n.rowHeight[f];n.height+=Y-X,this.shiftToLastRow(n)}},U.prototype.tilingPreLayout=function(){c.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},U.prototype.tilingPostLayout=function(){c.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},U.prototype.reduceTrees=function(){for(var n=[],v=!0,f;v;){var m=this.graphManager.getAllNodes(),p=[];v=!1;for(var R=0;R<m.length;R++)if(f=m[R],f.getEdges().length==1&&!f.getEdges()[0].isInterGraph&&f.getChild()==null){if(c.PURE_INCREMENTAL){var O=f.getEdges()[0].getOtherEnd(f),H=new x(f.getCenterX()-O.getCenterX(),f.getCenterY()-O.getCenterY());p.push([f,f.getEdges()[0],f.getOwner(),H])}else p.push([f,f.getEdges()[0],f.getOwner()]);v=!0}if(v==!0){for(var X=[],Y=0;Y<p.length;Y++)p[Y][0].getEdges().length==1&&(X.push(p[Y]),p[Y][0].getOwner().remove(p[Y][0]));n.push(X),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=n},U.prototype.growTree=function(n){for(var v=n.length,f=n[v-1],m,p=0;p<f.length;p++)m=f[p],this.findPlaceforPrunedNode(m),m[2].add(m[0]),m[2].add(m[1],m[1].source,m[1].target);n.splice(n.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},U.prototype.findPlaceforPrunedNode=function(n){var v,f,m=n[0];if(m==n[1].source?f=n[1].target:f=n[1].source,c.PURE_INCREMENTAL)m.setCenter(f.getCenterX()+n[3].getWidth(),f.getCenterY()+n[3].getHeight());else{var p=f.startX,R=f.finishX,O=f.startY,H=f.finishY,X=0,Y=0,rt=0,ht=0,z=[X,rt,Y,ht];if(O>0)for(var lt=p;lt<=R;lt++)z[0]+=this.grid[lt][O-1].length+this.grid[lt][O].length-1;if(R<this.grid.length-1)for(var lt=O;lt<=H;lt++)z[1]+=this.grid[R+1][lt].length+this.grid[R][lt].length-1;if(H<this.grid[0].length-1)for(var lt=p;lt<=R;lt++)z[2]+=this.grid[lt][H+1].length+this.grid[lt][H].length-1;if(p>0)for(var lt=O;lt<=H;lt++)z[3]+=this.grid[p-1][lt].length+this.grid[p][lt].length-1;for(var tt=I.MAX_VALUE,w,B,k=0;k<z.length;k++)z[k]<tt?(tt=z[k],w=1,B=k):z[k]==tt&&w++;if(w==3&&tt==0)z[0]==0&&z[1]==0&&z[2]==0?v=1:z[0]==0&&z[1]==0&&z[3]==0?v=0:z[0]==0&&z[2]==0&&z[3]==0?v=3:z[1]==0&&z[2]==0&&z[3]==0&&(v=2);else if(w==2&&tt==0){var J=Math.floor(Math.random()*2);z[0]==0&&z[1]==0?J==0?v=0:v=1:z[0]==0&&z[2]==0?J==0?v=0:v=2:z[0]==0&&z[3]==0?J==0?v=0:v=3:z[1]==0&&z[2]==0?J==0?v=1:v=2:z[1]==0&&z[3]==0?J==0?v=1:v=3:J==0?v=2:v=3}else if(w==4&&tt==0){var J=Math.floor(Math.random()*4);v=J}else v=B;v==0?m.setCenter(f.getCenterX(),f.getCenterY()-f.getHeight()/2-h.DEFAULT_EDGE_LENGTH-m.getHeight()/2):v==1?m.setCenter(f.getCenterX()+f.getWidth()/2+h.DEFAULT_EDGE_LENGTH+m.getWidth()/2,f.getCenterY()):v==2?m.setCenter(f.getCenterX(),f.getCenterY()+f.getHeight()/2+h.DEFAULT_EDGE_LENGTH+m.getHeight()/2):m.setCenter(f.getCenterX()-f.getWidth()/2-h.DEFAULT_EDGE_LENGTH-m.getWidth()/2,f.getCenterY())}},t.exports=U},991:(t,y,r)=>{var d=r(551).FDLayoutNode,e=r(551).IMath;function a(u,c,T,h){d.call(this,u,c,T,h)}a.prototype=Object.create(d.prototype);for(var l in d)a[l]=d[l];a.prototype.calculateDisplacement=function(){var u=this.graphManager.getLayout();this.getChild()!=null&&this.fixedNodeWeight?(this.displacementX+=u.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.fixedNodeWeight,this.displacementY+=u.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.fixedNodeWeight):(this.displacementX+=u.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY+=u.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren),Math.abs(this.displacementX)>u.coolingFactor*u.maxNodeDisplacement&&(this.displacementX=u.coolingFactor*u.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>u.coolingFactor*u.maxNodeDisplacement&&(this.displacementY=u.coolingFactor*u.maxNodeDisplacement*e.sign(this.displacementY)),this.child&&this.child.getNodes().length>0&&this.propogateDisplacementToChildren(this.displacementX,this.displacementY)},a.prototype.propogateDisplacementToChildren=function(u,c){for(var T=this.getChild().getNodes(),h,A=0;A<T.length;A++)h=T[A],h.getChild()==null?(h.displacementX+=u,h.displacementY+=c):h.propogateDisplacementToChildren(u,c)},a.prototype.move=function(){var u=this.graphManager.getLayout();(this.child==null||this.child.getNodes().length==0)&&(this.moveBy(this.displacementX,this.displacementY),u.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY)),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},a.prototype.setPred1=function(u){this.pred1=u},a.prototype.getPred1=function(){return pred1},a.prototype.getPred2=function(){return pred2},a.prototype.setNext=function(u){this.next=u},a.prototype.getNext=function(){return next},a.prototype.setProcessed=function(u){this.processed=u},a.prototype.isProcessed=function(){return processed},t.exports=a},902:(t,y,r)=>{function d(T){if(Array.isArray(T)){for(var h=0,A=Array(T.length);h<T.length;h++)A[h]=T[h];return A}else return Array.from(T)}var e=r(806),a=r(551).LinkedList,l=r(551).Matrix,u=r(551).SVD;function c(){}c.handleConstraints=function(T){var h={};h.fixedNodeConstraint=T.constraints.fixedNodeConstraint,h.alignmentConstraint=T.constraints.alignmentConstraint,h.relativePlacementConstraint=T.constraints.relativePlacementConstraint;for(var A=new Map,N=new Map,L=[],x=[],_=T.getAllNodes(),I=0,nt=0;nt<_.length;nt++){var dt=_[nt];dt.getChild()==null&&(N.set(dt.id,I++),L.push(dt.getCenterX()),x.push(dt.getCenterY()),A.set(dt.id,dt))}h.relativePlacementConstraint&&h.relativePlacementConstraint.forEach(function($){!$.gap&&$.gap!=0&&($.left?$.gap=e.DEFAULT_EDGE_LENGTH+A.get($.left).getWidth()/2+A.get($.right).getWidth()/2:$.gap=e.DEFAULT_EDGE_LENGTH+A.get($.top).getHeight()/2+A.get($.bottom).getHeight()/2)});var ut=function(q,g){return{x:q.x-g.x,y:q.y-g.y}},Nt=function(q){var g=0,M=0;return q.forEach(function(C){g+=L[N.get(C)],M+=x[N.get(C)]}),{x:g/q.size,y:M/q.size}},U=function(q,g,M,C,S){function W(et,b){var ft=new Set(et),Q=!0,ot=!1,mt=void 0;try{for(var ct=b[Symbol.iterator](),yt;!(Q=(yt=ct.next()).done);Q=!0){var Rt=yt.value;ft.add(Rt)}}catch(Tt){ot=!0,mt=Tt}finally{try{!Q&&ct.return&&ct.return()}finally{if(ot)throw mt}}return ft}var j=new Map;q.forEach(function(et,b){j.set(b,0)}),q.forEach(function(et,b){et.forEach(function(ft){j.set(ft.id,j.get(ft.id)+1)})});var F=new Map,P=new Map,K=new a;j.forEach(function(et,b){et==0?(K.push(b),M||(g=="horizontal"?F.set(b,N.has(b)?L[N.get(b)]:C.get(b)):F.set(b,N.has(b)?x[N.get(b)]:C.get(b)))):F.set(b,Number.NEGATIVE_INFINITY),M&&P.set(b,new Set([b]))}),M&&S.forEach(function(et){var b=[];if(et.forEach(function(ot){M.has(ot)&&b.push(ot)}),b.length>0){var ft=0;b.forEach(function(ot){g=="horizontal"?(F.set(ot,N.has(ot)?L[N.get(ot)]:C.get(ot)),ft+=F.get(ot)):(F.set(ot,N.has(ot)?x[N.get(ot)]:C.get(ot)),ft+=F.get(ot))}),ft=ft/b.length,et.forEach(function(ot){M.has(ot)||F.set(ot,ft)})}else{var Q=0;et.forEach(function(ot){g=="horizontal"?Q+=N.has(ot)?L[N.get(ot)]:C.get(ot):Q+=N.has(ot)?x[N.get(ot)]:C.get(ot)}),Q=Q/et.length,et.forEach(function(ot){F.set(ot,Q)})}});for(var G=function(){var b=K.shift(),ft=q.get(b);ft.forEach(function(Q){if(F.get(Q.id)<F.get(b)+Q.gap)if(M&&M.has(Q.id)){var ot=void 0;if(g=="horizontal"?ot=N.has(Q.id)?L[N.get(Q.id)]:C.get(Q.id):ot=N.has(Q.id)?x[N.get(Q.id)]:C.get(Q.id),F.set(Q.id,ot),ot<F.get(b)+Q.gap){var mt=F.get(b)+Q.gap-ot;P.get(b).forEach(function(ct){F.set(ct,F.get(ct)-mt)})}}else F.set(Q.id,F.get(b)+Q.gap);j.set(Q.id,j.get(Q.id)-1),j.get(Q.id)==0&&K.push(Q.id),M&&P.set(Q.id,W(P.get(b),P.get(Q.id)))})};K.length!=0;)G();if(M){var V=new Set;q.forEach(function(et,b){et.length==0&&V.add(b)});var vt=[];P.forEach(function(et,b){if(V.has(b)){var ft=!1,Q=!0,ot=!1,mt=void 0;try{for(var ct=et[Symbol.iterator](),yt;!(Q=(yt=ct.next()).done);Q=!0){var Rt=yt.value;M.has(Rt)&&(ft=!0)}}catch(wt){ot=!0,mt=wt}finally{try{!Q&&ct.return&&ct.return()}finally{if(ot)throw mt}}if(!ft){var Tt=!1,Dt=void 0;vt.forEach(function(wt,Ut){wt.has([].concat(d(et))[0])&&(Tt=!0,Dt=Ut)}),Tt?et.forEach(function(wt){vt[Dt].add(wt)}):vt.push(new Set(et))}}}),vt.forEach(function(et,b){var ft=Number.POSITIVE_INFINITY,Q=Number.POSITIVE_INFINITY,ot=Number.NEGATIVE_INFINITY,mt=Number.NEGATIVE_INFINITY,ct=!0,yt=!1,Rt=void 0;try{for(var Tt=et[Symbol.iterator](),Dt;!(ct=(Dt=Tt.next()).done);ct=!0){var wt=Dt.value,Ut=void 0;g=="horizontal"?Ut=N.has(wt)?L[N.get(wt)]:C.get(wt):Ut=N.has(wt)?x[N.get(wt)]:C.get(wt);var kt=F.get(wt);Ut<ft&&(ft=Ut),Ut>ot&&(ot=Ut),kt<Q&&(Q=kt),kt>mt&&(mt=kt)}}catch(_t){yt=!0,Rt=_t}finally{try{!ct&&Tt.return&&Tt.return()}finally{if(yt)throw Rt}}var $t=(ft+ot)/2-(Q+mt)/2,Yt=!0,Zt=!1,Kt=void 0;try{for(var qt=et[Symbol.iterator](),re;!(Yt=(re=qt.next()).done);Yt=!0){var jt=re.value;F.set(jt,F.get(jt)+$t)}}catch(_t){Zt=!0,Kt=_t}finally{try{!Yt&&qt.return&&qt.return()}finally{if(Zt)throw Kt}}})}return F},Z=function(q){var g=0,M=0,C=0,S=0;if(q.forEach(function(P){P.left?L[N.get(P.left)]-L[N.get(P.right)]>=0?g++:M++:x[N.get(P.top)]-x[N.get(P.bottom)]>=0?C++:S++}),g>M&&C>S)for(var W=0;W<N.size;W++)L[W]=-1*L[W],x[W]=-1*x[W];else if(g>M)for(var j=0;j<N.size;j++)L[j]=-1*L[j];else if(C>S)for(var F=0;F<N.size;F++)x[F]=-1*x[F]},n=function(q){var g=[],M=new a,C=new Set,S=0;return q.forEach(function(W,j){if(!C.has(j)){g[S]=[];var F=j;for(M.push(F),C.add(F),g[S].push(F);M.length!=0;){F=M.shift();var P=q.get(F);P.forEach(function(K){C.has(K.id)||(M.push(K.id),C.add(K.id),g[S].push(K.id))})}S++}}),g},v=function(q){var g=new Map;return q.forEach(function(M,C){g.set(C,[])}),q.forEach(function(M,C){M.forEach(function(S){g.get(C).push(S),g.get(S.id).push({id:C,gap:S.gap,direction:S.direction})})}),g},f=function(q){var g=new Map;return q.forEach(function(M,C){g.set(C,[])}),q.forEach(function(M,C){M.forEach(function(S){g.get(S.id).push({id:C,gap:S.gap,direction:S.direction})})}),g},m=[],p=[],R=!1,O=!1,H=new Set,X=new Map,Y=new Map,rt=[];if(h.fixedNodeConstraint&&h.fixedNodeConstraint.forEach(function($){H.add($.nodeId)}),h.relativePlacementConstraint&&(h.relativePlacementConstraint.forEach(function($){$.left?(X.has($.left)?X.get($.left).push({id:$.right,gap:$.gap,direction:"horizontal"}):X.set($.left,[{id:$.right,gap:$.gap,direction:"horizontal"}]),X.has($.right)||X.set($.right,[])):(X.has($.top)?X.get($.top).push({id:$.bottom,gap:$.gap,direction:"vertical"}):X.set($.top,[{id:$.bottom,gap:$.gap,direction:"vertical"}]),X.has($.bottom)||X.set($.bottom,[]))}),Y=v(X),rt=n(Y)),e.TRANSFORM_ON_CONSTRAINT_HANDLING){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>1)h.fixedNodeConstraint.forEach(function($,q){m[q]=[$.position.x,$.position.y],p[q]=[L[N.get($.nodeId)],x[N.get($.nodeId)]]}),R=!0;else if(h.alignmentConstraint)(function(){var $=0;if(h.alignmentConstraint.vertical){for(var q=h.alignmentConstraint.vertical,g=function(F){var P=new Set;q[F].forEach(function(V){P.add(V)});var K=new Set([].concat(d(P)).filter(function(V){return H.has(V)})),G=void 0;K.size>0?G=L[N.get(K.values().next().value)]:G=Nt(P).x,q[F].forEach(function(V){m[$]=[G,x[N.get(V)]],p[$]=[L[N.get(V)],x[N.get(V)]],$++})},M=0;M<q.length;M++)g(M);R=!0}if(h.alignmentConstraint.horizontal){for(var C=h.alignmentConstraint.horizontal,S=function(F){var P=new Set;C[F].forEach(function(V){P.add(V)});var K=new Set([].concat(d(P)).filter(function(V){return H.has(V)})),G=void 0;K.size>0?G=L[N.get(K.values().next().value)]:G=Nt(P).y,C[F].forEach(function(V){m[$]=[L[N.get(V)],G],p[$]=[L[N.get(V)],x[N.get(V)]],$++})},W=0;W<C.length;W++)S(W);R=!0}h.relativePlacementConstraint&&(O=!0)})();else if(h.relativePlacementConstraint){for(var ht=0,z=0,lt=0;lt<rt.length;lt++)rt[lt].length>ht&&(ht=rt[lt].length,z=lt);if(ht<Y.size/2)Z(h.relativePlacementConstraint),R=!1,O=!1;else{var tt=new Map,w=new Map,B=[];rt[z].forEach(function($){X.get($).forEach(function(q){q.direction=="horizontal"?(tt.has($)?tt.get($).push(q):tt.set($,[q]),tt.has(q.id)||tt.set(q.id,[]),B.push({left:$,right:q.id})):(w.has($)?w.get($).push(q):w.set($,[q]),w.has(q.id)||w.set(q.id,[]),B.push({top:$,bottom:q.id}))})}),Z(B),O=!1;var k=U(tt,"horizontal"),J=U(w,"vertical");rt[z].forEach(function($,q){p[q]=[L[N.get($)],x[N.get($)]],m[q]=[],k.has($)?m[q][0]=k.get($):m[q][0]=L[N.get($)],J.has($)?m[q][1]=J.get($):m[q][1]=x[N.get($)]}),R=!0}}if(R){for(var Et=void 0,at=l.transpose(m),bt=l.transpose(p),At=0;At<at.length;At++)at[At]=l.multGamma(at[At]),bt[At]=l.multGamma(bt[At]);var Vt=l.multMat(at,l.transpose(bt)),Gt=u.svd(Vt);Et=l.multMat(Gt.V,l.transpose(Gt.U));for(var pt=0;pt<N.size;pt++){var gt=[L[pt],x[pt]],It=[Et[0][0],Et[1][0]],xt=[Et[0][1],Et[1][1]];L[pt]=l.dotProduct(gt,It),x[pt]=l.dotProduct(gt,xt)}O&&Z(h.relativePlacementConstraint)}}if(e.ENFORCE_CONSTRAINTS){if(h.fixedNodeConstraint&&h.fixedNodeConstraint.length>0){var Mt={x:0,y:0};h.fixedNodeConstraint.forEach(function($,q){var g={x:L[N.get($.nodeId)],y:x[N.get($.nodeId)]},M=$.position,C=ut(M,g);Mt.x+=C.x,Mt.y+=C.y}),Mt.x/=h.fixedNodeConstraint.length,Mt.y/=h.fixedNodeConstraint.length,L.forEach(function($,q){L[q]+=Mt.x}),x.forEach(function($,q){x[q]+=Mt.y}),h.fixedNodeConstraint.forEach(function($){L[N.get($.nodeId)]=$.position.x,x[N.get($.nodeId)]=$.position.y})}if(h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var Ot=h.alignmentConstraint.vertical,Ct=function(q){var g=new Set;Ot[q].forEach(function(S){g.add(S)});var M=new Set([].concat(d(g)).filter(function(S){return H.has(S)})),C=void 0;M.size>0?C=L[N.get(M.values().next().value)]:C=Nt(g).x,g.forEach(function(S){H.has(S)||(L[N.get(S)]=C)})},St=0;St<Ot.length;St++)Ct(St);if(h.alignmentConstraint.horizontal)for(var zt=h.alignmentConstraint.horizontal,Qt=function(q){var g=new Set;zt[q].forEach(function(S){g.add(S)});var M=new Set([].concat(d(g)).filter(function(S){return H.has(S)})),C=void 0;M.size>0?C=x[N.get(M.values().next().value)]:C=Nt(g).y,g.forEach(function(S){H.has(S)||(x[N.get(S)]=C)})},Pt=0;Pt<zt.length;Pt++)Qt(Pt)}h.relativePlacementConstraint&&function(){var $=new Map,q=new Map,g=new Map,M=new Map,C=new Map,S=new Map,W=new Set,j=new Set;if(H.forEach(function(Bt){W.add(Bt),j.add(Bt)}),h.alignmentConstraint){if(h.alignmentConstraint.vertical)for(var F=h.alignmentConstraint.vertical,P=function(Lt){g.set("dummy"+Lt,[]),F[Lt].forEach(function(Ft){$.set(Ft,"dummy"+Lt),g.get("dummy"+Lt).push(Ft),H.has(Ft)&&W.add("dummy"+Lt)}),C.set("dummy"+Lt,L[N.get(F[Lt][0])])},K=0;K<F.length;K++)P(K);if(h.alignmentConstraint.horizontal)for(var G=h.alignmentConstraint.horizontal,V=function(Lt){M.set("dummy"+Lt,[]),G[Lt].forEach(function(Ft){q.set(Ft,"dummy"+Lt),M.get("dummy"+Lt).push(Ft),H.has(Ft)&&j.add("dummy"+Lt)}),S.set("dummy"+Lt,x[N.get(G[Lt][0])])},vt=0;vt<G.length;vt++)V(vt)}var et=new Map,b=new Map,ft=function(Lt){X.get(Lt).forEach(function(Ft){var te=void 0,Jt=void 0;Ft.direction=="horizontal"?(te=$.get(Lt)?$.get(Lt):Lt,$.get(Ft.id)?Jt={id:$.get(Ft.id),gap:Ft.gap,direction:Ft.direction}:Jt=Ft,et.has(te)?et.get(te).push(Jt):et.set(te,[Jt]),et.has(Jt.id)||et.set(Jt.id,[])):(te=q.get(Lt)?q.get(Lt):Lt,q.get(Ft.id)?Jt={id:q.get(Ft.id),gap:Ft.gap,direction:Ft.direction}:Jt=Ft,b.has(te)?b.get(te).push(Jt):b.set(te,[Jt]),b.has(Jt.id)||b.set(Jt.id,[]))})},Q=!0,ot=!1,mt=void 0;try{for(var ct=X.keys()[Symbol.iterator](),yt;!(Q=(yt=ct.next()).done);Q=!0){var Rt=yt.value;ft(Rt)}}catch(Bt){ot=!0,mt=Bt}finally{try{!Q&&ct.return&&ct.return()}finally{if(ot)throw mt}}var Tt=v(et),Dt=v(b),wt=n(Tt),Ut=n(Dt),kt=f(et),$t=f(b),Yt=[],Zt=[];wt.forEach(function(Bt,Lt){Yt[Lt]=[],Bt.forEach(function(Ft){kt.get(Ft).length==0&&Yt[Lt].push(Ft)})}),Ut.forEach(function(Bt,Lt){Zt[Lt]=[],Bt.forEach(function(Ft){$t.get(Ft).length==0&&Zt[Lt].push(Ft)})});var Kt=U(et,"horizontal",W,C,Yt),qt=U(b,"vertical",j,S,Zt),re=function(Lt){g.get(Lt)?g.get(Lt).forEach(function(Ft){L[N.get(Ft)]=Kt.get(Lt)}):L[N.get(Lt)]=Kt.get(Lt)},jt=!0,_t=!1,ae=void 0;try{for(var oe=Kt.keys()[Symbol.iterator](),fe;!(jt=(fe=oe.next()).done);jt=!0){var se=fe.value;re(se)}}catch(Bt){_t=!0,ae=Bt}finally{try{!jt&&oe.return&&oe.return()}finally{if(_t)throw ae}}var pe=function(Lt){M.get(Lt)?M.get(Lt).forEach(function(Ft){x[N.get(Ft)]=qt.get(Lt)}):x[N.get(Lt)]=qt.get(Lt)},he=!0,ce=!1,ue=void 0;try{for(var le=qt.keys()[Symbol.iterator](),ge;!(he=(ge=le.next()).done);he=!0){var se=ge.value;pe(se)}}catch(Bt){ce=!0,ue=Bt}finally{try{!he&&le.return&&le.return()}finally{if(ce)throw ue}}}()}for(var Wt=0;Wt<_.length;Wt++){var Ht=_[Wt];Ht.getChild()==null&&Ht.setCenter(L[N.get(Ht.id)],x[N.get(Ht.id)])}},t.exports=c},551:t=>{t.exports=D}},s={};function o(t){var y=s[t];if(y!==void 0)return y.exports;var r=s[t]={exports:{}};return E[t](r,r.exports,o),r.exports}var i=o(45);return i})()})},28514:function(Xt,it,st){(function(E,s){Xt.exports=s(st(49160))})(this,function(D){return(()=>{"use strict";var E={658:t=>{t.exports=Object.assign!=null?Object.assign.bind(Object):function(y){for(var r=arguments.length,d=Array(r>1?r-1:0),e=1;e<r;e++)d[e-1]=arguments[e];return d.forEach(function(a){Object.keys(a).forEach(function(l){return y[l]=a[l]})}),y}},548:(t,y,r)=>{var d=function(){function l(u,c){var T=[],h=!0,A=!1,N=void 0;try{for(var L=u[Symbol.iterator](),x;!(h=(x=L.next()).done)&&(T.push(x.value),!(c&&T.length===c));h=!0);}catch(_){A=!0,N=_}finally{try{!h&&L.return&&L.return()}finally{if(A)throw N}}return T}return function(u,c){if(Array.isArray(u))return u;if(Symbol.iterator in Object(u))return l(u,c);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),e=r(140).layoutBase.LinkedList,a={};a.getTopMostNodes=function(l){for(var u={},c=0;c<l.length;c++)u[l[c].id()]=!0;var T=l.filter(function(h,A){typeof h=="number"&&(h=A);for(var N=h.parent()[0];N!=null;){if(u[N.id()])return!1;N=N.parent()[0]}return!0});return T},a.connectComponents=function(l,u,c,T){var h=new e,A=new Set,N=[],L=void 0,x=void 0,_=void 0,I=!1,nt=1,dt=[],ut=[],Nt=function(){var Z=l.collection();ut.push(Z);var n=c[0],v=l.collection();v.merge(n).merge(n.descendants().intersection(u)),N.push(n),v.forEach(function(p){h.push(p),A.add(p),Z.merge(p)});for(var f=function(){n=h.shift();var R=l.collection();n.neighborhood().nodes().forEach(function(Y){u.intersection(n.edgesWith(Y)).length>0&&R.merge(Y)});for(var O=0;O<R.length;O++){var H=R[O];if(L=c.intersection(H.union(H.ancestors())),L!=null&&!A.has(L[0])){var X=L.union(L.descendants());X.forEach(function(Y){h.push(Y),A.add(Y),Z.merge(Y),c.has(Y)&&N.push(Y)})}}};h.length!=0;)f();if(Z.forEach(function(p){u.intersection(p.connectedEdges()).forEach(function(R){Z.has(R.source())&&Z.has(R.target())&&Z.merge(R)})}),N.length==c.length&&(I=!0),!I||I&&nt>1){x=N[0],_=x.connectedEdges().length,N.forEach(function(p){p.connectedEdges().length<_&&(_=p.connectedEdges().length,x=p)}),dt.push(x.id());var m=l.collection();m.merge(N[0]),N.forEach(function(p){m.merge(p)}),N=[],c=c.difference(m),nt++}};do Nt();while(!I);return T&&dt.length>0&&T.set("dummy"+(T.size+1),dt),ut},a.relocateComponent=function(l,u,c){if(!c.fixedNodeConstraint){var T=Number.POSITIVE_INFINITY,h=Number.NEGATIVE_INFINITY,A=Number.POSITIVE_INFINITY,N=Number.NEGATIVE_INFINITY;if(c.quality=="draft"){var L=!0,x=!1,_=void 0;try{for(var I=u.nodeIndexes[Symbol.iterator](),nt;!(L=(nt=I.next()).done);L=!0){var dt=nt.value,ut=d(dt,2),Nt=ut[0],U=ut[1],Z=c.cy.getElementById(Nt);if(Z){var n=Z.boundingBox(),v=u.xCoords[U]-n.w/2,f=u.xCoords[U]+n.w/2,m=u.yCoords[U]-n.h/2,p=u.yCoords[U]+n.h/2;v<T&&(T=v),f>h&&(h=f),m<A&&(A=m),p>N&&(N=p)}}}catch(Y){x=!0,_=Y}finally{try{!L&&I.return&&I.return()}finally{if(x)throw _}}var R=l.x-(h+T)/2,O=l.y-(N+A)/2;u.xCoords=u.xCoords.map(function(Y){return Y+R}),u.yCoords=u.yCoords.map(function(Y){return Y+O})}else{Object.keys(u).forEach(function(Y){var rt=u[Y],ht=rt.getRect().x,z=rt.getRect().x+rt.getRect().width,lt=rt.getRect().y,tt=rt.getRect().y+rt.getRect().height;ht<T&&(T=ht),z>h&&(h=z),lt<A&&(A=lt),tt>N&&(N=tt)});var H=l.x-(h+T)/2,X=l.y-(N+A)/2;Object.keys(u).forEach(function(Y){var rt=u[Y];rt.setCenter(rt.getCenterX()+H,rt.getCenterY()+X)})}}},a.calcBoundingBox=function(l,u,c,T){for(var h=Number.MAX_SAFE_INTEGER,A=Number.MIN_SAFE_INTEGER,N=Number.MAX_SAFE_INTEGER,L=Number.MIN_SAFE_INTEGER,x=void 0,_=void 0,I=void 0,nt=void 0,dt=l.descendants().not(":parent"),ut=dt.length,Nt=0;Nt<ut;Nt++){var U=dt[Nt];x=u[T.get(U.id())]-U.width()/2,_=u[T.get(U.id())]+U.width()/2,I=c[T.get(U.id())]-U.height()/2,nt=c[T.get(U.id())]+U.height()/2,h>x&&(h=x),A<_&&(A=_),N>I&&(N=I),L<nt&&(L=nt)}var Z={};return Z.topLeftX=h,Z.topLeftY=N,Z.width=A-h,Z.height=L-N,Z},a.calcParentsWithoutChildren=function(l,u){var c=l.collection();return u.nodes(":parent").forEach(function(T){var h=!1;T.children().forEach(function(A){A.css("display")!="none"&&(h=!0)}),h||c.merge(T)}),c},t.exports=a},816:(t,y,r)=>{var d=r(548),e=r(140).CoSELayout,a=r(140).CoSENode,l=r(140).layoutBase.PointD,u=r(140).layoutBase.DimensionD,c=r(140).layoutBase.LayoutConstants,T=r(140).layoutBase.FDLayoutConstants,h=r(140).CoSEConstants,A=function(L,x){var _=L.cy,I=L.eles,nt=I.nodes(),dt=I.edges(),ut=void 0,Nt=void 0,U=void 0,Z={};L.randomize&&(ut=x.nodeIndexes,Nt=x.xCoords,U=x.yCoords);var n=function(Y){return typeof Y=="function"},v=function(Y,rt){return n(Y)?Y(rt):Y},f=d.calcParentsWithoutChildren(_,I),m=function X(Y,rt,ht,z){for(var lt=rt.length,tt=0;tt<lt;tt++){var w=rt[tt],B=null;w.intersection(f).length==0&&(B=w.children());var k=void 0,J=w.layoutDimensions({nodeDimensionsIncludeLabels:z.nodeDimensionsIncludeLabels});if(w.outerWidth()!=null&&w.outerHeight()!=null)if(z.randomize)if(!w.isParent())k=Y.add(new a(ht.graphManager,new l(Nt[ut.get(w.id())]-J.w/2,U[ut.get(w.id())]-J.h/2),new u(parseFloat(J.w),parseFloat(J.h))));else{var Et=d.calcBoundingBox(w,Nt,U,ut);w.intersection(f).length==0?k=Y.add(new a(ht.graphManager,new l(Et.topLeftX,Et.topLeftY),new u(Et.width,Et.height))):k=Y.add(new a(ht.graphManager,new l(Et.topLeftX,Et.topLeftY),new u(parseFloat(J.w),parseFloat(J.h))))}else k=Y.add(new a(ht.graphManager,new l(w.position("x")-J.w/2,w.position("y")-J.h/2),new u(parseFloat(J.w),parseFloat(J.h))));else k=Y.add(new a(this.graphManager));if(k.id=w.data("id"),k.nodeRepulsion=v(z.nodeRepulsion,w),k.paddingLeft=parseInt(w.css("padding")),k.paddingTop=parseInt(w.css("padding")),k.paddingRight=parseInt(w.css("padding")),k.paddingBottom=parseInt(w.css("padding")),z.nodeDimensionsIncludeLabels&&(k.labelWidth=w.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).w,k.labelHeight=w.boundingBox({includeLabels:!0,includeNodes:!1,includeOverlays:!1}).h,k.labelPosVertical=w.css("text-valign"),k.labelPosHorizontal=w.css("text-halign")),Z[w.data("id")]=k,isNaN(k.rect.x)&&(k.rect.x=0),isNaN(k.rect.y)&&(k.rect.y=0),B!=null&&B.length>0){var at=void 0;at=ht.getGraphManager().add(ht.newGraph(),k),X(at,B,ht,z)}}},p=function(Y,rt,ht){for(var z=0,lt=0,tt=0;tt<ht.length;tt++){var w=ht[tt],B=Z[w.data("source")],k=Z[w.data("target")];if(B&&k&&B!==k&&B.getEdgesBetween(k).length==0){var J=rt.add(Y.newEdge(),B,k);J.id=w.id(),J.idealLength=v(L.idealEdgeLength,w),J.edgeElasticity=v(L.edgeElasticity,w),z+=J.idealLength,lt++}}L.idealEdgeLength!=null&&(lt>0?h.DEFAULT_EDGE_LENGTH=T.DEFAULT_EDGE_LENGTH=z/lt:n(L.idealEdgeLength)?h.DEFAULT_EDGE_LENGTH=T.DEFAULT_EDGE_LENGTH=50:h.DEFAULT_EDGE_LENGTH=T.DEFAULT_EDGE_LENGTH=L.idealEdgeLength,h.MIN_REPULSION_DIST=T.MIN_REPULSION_DIST=T.DEFAULT_EDGE_LENGTH/10,h.DEFAULT_RADIAL_SEPARATION=T.DEFAULT_EDGE_LENGTH)},R=function(Y,rt){rt.fixedNodeConstraint&&(Y.constraints.fixedNodeConstraint=rt.fixedNodeConstraint),rt.alignmentConstraint&&(Y.constraints.alignmentConstraint=rt.alignmentConstraint),rt.relativePlacementConstraint&&(Y.constraints.relativePlacementConstraint=rt.relativePlacementConstraint)};L.nestingFactor!=null&&(h.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=T.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=L.nestingFactor),L.gravity!=null&&(h.DEFAULT_GRAVITY_STRENGTH=T.DEFAULT_GRAVITY_STRENGTH=L.gravity),L.numIter!=null&&(h.MAX_ITERATIONS=T.MAX_ITERATIONS=L.numIter),L.gravityRange!=null&&(h.DEFAULT_GRAVITY_RANGE_FACTOR=T.DEFAULT_GRAVITY_RANGE_FACTOR=L.gravityRange),L.gravityCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_STRENGTH=T.DEFAULT_COMPOUND_GRAVITY_STRENGTH=L.gravityCompound),L.gravityRangeCompound!=null&&(h.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=T.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=L.gravityRangeCompound),L.initialEnergyOnIncremental!=null&&(h.DEFAULT_COOLING_FACTOR_INCREMENTAL=T.DEFAULT_COOLING_FACTOR_INCREMENTAL=L.initialEnergyOnIncremental),L.tilingCompareBy!=null&&(h.TILING_COMPARE_BY=L.tilingCompareBy),L.quality=="proof"?c.QUALITY=2:c.QUALITY=0,h.NODE_DIMENSIONS_INCLUDE_LABELS=T.NODE_DIMENSIONS_INCLUDE_LABELS=c.NODE_DIMENSIONS_INCLUDE_LABELS=L.nodeDimensionsIncludeLabels,h.DEFAULT_INCREMENTAL=T.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=!L.randomize,h.ANIMATE=T.ANIMATE=c.ANIMATE=L.animate,h.TILE=L.tile,h.TILING_PADDING_VERTICAL=typeof L.tilingPaddingVertical=="function"?L.tilingPaddingVertical.call():L.tilingPaddingVertical,h.TILING_PADDING_HORIZONTAL=typeof L.tilingPaddingHorizontal=="function"?L.tilingPaddingHorizontal.call():L.tilingPaddingHorizontal,h.DEFAULT_INCREMENTAL=T.DEFAULT_INCREMENTAL=c.DEFAULT_INCREMENTAL=!0,h.PURE_INCREMENTAL=!L.randomize,c.DEFAULT_UNIFORM_LEAF_NODE_SIZES=L.uniformNodeDimensions,L.step=="transformed"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!1),L.step=="enforced"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!1),L.step=="cose"&&(h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!1,h.APPLY_LAYOUT=!0),L.step=="all"&&(L.randomize?h.TRANSFORM_ON_CONSTRAINT_HANDLING=!0:h.TRANSFORM_ON_CONSTRAINT_HANDLING=!1,h.ENFORCE_CONSTRAINTS=!0,h.APPLY_LAYOUT=!0),L.fixedNodeConstraint||L.alignmentConstraint||L.relativePlacementConstraint?h.TREE_REDUCTION_ON_INCREMENTAL=!1:h.TREE_REDUCTION_ON_INCREMENTAL=!0;var O=new e,H=O.newGraphManager();return m(H.addRoot(),d.getTopMostNodes(nt),O,L),p(O,H,dt),R(O,L),O.runLayout(),Z};t.exports={coseLayout:A}},212:(t,y,r)=>{var d=function(){function L(x,_){for(var I=0;I<_.length;I++){var nt=_[I];nt.enumerable=nt.enumerable||!1,nt.configurable=!0,"value"in nt&&(nt.writable=!0),Object.defineProperty(x,nt.key,nt)}}return function(x,_,I){return _&&L(x.prototype,_),I&&L(x,I),x}}();function e(L,x){if(!(L instanceof x))throw new TypeError("Cannot call a class as a function")}var a=r(658),l=r(548),u=r(657),c=u.spectralLayout,T=r(816),h=T.coseLayout,A=Object.freeze({quality:"default",randomize:!0,animate:!0,animationDuration:1e3,animationEasing:void 0,fit:!0,padding:30,nodeDimensionsIncludeLabels:!1,uniformNodeDimensions:!1,packComponents:!0,step:"all",samplingType:!0,sampleSize:25,nodeSeparation:75,piTol:1e-7,nodeRepulsion:function(x){return 4500},idealEdgeLength:function(x){return 50},edgeElasticity:function(x){return .45},nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,tilingCompareBy:void 0,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.3,fixedNodeConstraint:void 0,alignmentConstraint:void 0,relativePlacementConstraint:void 0,ready:function(){},stop:function(){}}),N=function(){function L(x){e(this,L),this.options=a({},A,x)}return d(L,[{key:"run",value:function(){var _=this,I=this.options,nt=I.cy,dt=I.eles,ut=[],Nt=void 0,U=void 0,Z=[],n=void 0,v=[];I.fixedNodeConstraint&&(!Array.isArray(I.fixedNodeConstraint)||I.fixedNodeConstraint.length==0)&&(I.fixedNodeConstraint=void 0),I.alignmentConstraint&&(I.alignmentConstraint.vertical&&(!Array.isArray(I.alignmentConstraint.vertical)||I.alignmentConstraint.vertical.length==0)&&(I.alignmentConstraint.vertical=void 0),I.alignmentConstraint.horizontal&&(!Array.isArray(I.alignmentConstraint.horizontal)||I.alignmentConstraint.horizontal.length==0)&&(I.alignmentConstraint.horizontal=void 0)),I.relativePlacementConstraint&&(!Array.isArray(I.relativePlacementConstraint)||I.relativePlacementConstraint.length==0)&&(I.relativePlacementConstraint=void 0);var f=I.fixedNodeConstraint||I.alignmentConstraint||I.relativePlacementConstraint;f&&(I.tile=!1,I.packComponents=!1);var m=void 0,p=!1;if(nt.layoutUtilities&&I.packComponents&&(m=nt.layoutUtilities("get"),m||(m=nt.layoutUtilities()),p=!0),dt.nodes().length>0)if(p){var H=l.getTopMostNodes(I.eles.nodes());if(n=l.connectComponents(nt,I.eles,H),n.forEach(function(pt){var gt=pt.boundingBox();v.push({x:gt.x1+gt.w/2,y:gt.y1+gt.h/2})}),I.randomize&&n.forEach(function(pt){I.eles=pt,ut.push(c(I))}),I.quality=="default"||I.quality=="proof"){var X=nt.collection();if(I.tile){var Y=new Map,rt=[],ht=[],z=0,lt={nodeIndexes:Y,xCoords:rt,yCoords:ht},tt=[];if(n.forEach(function(pt,gt){pt.edges().length==0&&(pt.nodes().forEach(function(It,xt){X.merge(pt.nodes()[xt]),It.isParent()||(lt.nodeIndexes.set(pt.nodes()[xt].id(),z++),lt.xCoords.push(pt.nodes()[0].position().x),lt.yCoords.push(pt.nodes()[0].position().y))}),tt.push(gt))}),X.length>1){var w=X.boundingBox();v.push({x:w.x1+w.w/2,y:w.y1+w.h/2}),n.push(X),ut.push(lt);for(var B=tt.length-1;B>=0;B--)n.splice(tt[B],1),ut.splice(tt[B],1),v.splice(tt[B],1)}}n.forEach(function(pt,gt){I.eles=pt,Z.push(h(I,ut[gt])),l.relocateComponent(v[gt],Z[gt],I)})}else n.forEach(function(pt,gt){l.relocateComponent(v[gt],ut[gt],I)});var k=new Set;if(n.length>1){var J=[],Et=dt.filter(function(pt){return pt.css("display")=="none"});n.forEach(function(pt,gt){var It=void 0;if(I.quality=="draft"&&(It=ut[gt].nodeIndexes),pt.nodes().not(Et).length>0){var xt={};xt.edges=[],xt.nodes=[];var Mt=void 0;pt.nodes().not(Et).forEach(function(Ot){if(I.quality=="draft")if(!Ot.isParent())Mt=It.get(Ot.id()),xt.nodes.push({x:ut[gt].xCoords[Mt]-Ot.boundingbox().w/2,y:ut[gt].yCoords[Mt]-Ot.boundingbox().h/2,width:Ot.boundingbox().w,height:Ot.boundingbox().h});else{var Ct=l.calcBoundingBox(Ot,ut[gt].xCoords,ut[gt].yCoords,It);xt.nodes.push({x:Ct.topLeftX,y:Ct.topLeftY,width:Ct.width,height:Ct.height})}else Z[gt][Ot.id()]&&xt.nodes.push({x:Z[gt][Ot.id()].getLeft(),y:Z[gt][Ot.id()].getTop(),width:Z[gt][Ot.id()].getWidth(),height:Z[gt][Ot.id()].getHeight()})}),pt.edges().forEach(function(Ot){var Ct=Ot.source(),St=Ot.target();if(Ct.css("display")!="none"&&St.css("display")!="none")if(I.quality=="draft"){var zt=It.get(Ct.id()),Qt=It.get(St.id()),Pt=[],Wt=[];if(Ct.isParent()){var Ht=l.calcBoundingBox(Ct,ut[gt].xCoords,ut[gt].yCoords,It);Pt.push(Ht.topLeftX+Ht.width/2),Pt.push(Ht.topLeftY+Ht.height/2)}else Pt.push(ut[gt].xCoords[zt]),Pt.push(ut[gt].yCoords[zt]);if(St.isParent()){var $=l.calcBoundingBox(St,ut[gt].xCoords,ut[gt].yCoords,It);Wt.push($.topLeftX+$.width/2),Wt.push($.topLeftY+$.height/2)}else Wt.push(ut[gt].xCoords[Qt]),Wt.push(ut[gt].yCoords[Qt]);xt.edges.push({startX:Pt[0],startY:Pt[1],endX:Wt[0],endY:Wt[1]})}else Z[gt][Ct.id()]&&Z[gt][St.id()]&&xt.edges.push({startX:Z[gt][Ct.id()].getCenterX(),startY:Z[gt][Ct.id()].getCenterY(),endX:Z[gt][St.id()].getCenterX(),endY:Z[gt][St.id()].getCenterY()})}),xt.nodes.length>0&&(J.push(xt),k.add(gt))}});var at=m.packComponents(J,I.randomize).shifts;if(I.quality=="draft")ut.forEach(function(pt,gt){var It=pt.xCoords.map(function(Mt){return Mt+at[gt].dx}),xt=pt.yCoords.map(function(Mt){return Mt+at[gt].dy});pt.xCoords=It,pt.yCoords=xt});else{var bt=0;k.forEach(function(pt){Object.keys(Z[pt]).forEach(function(gt){var It=Z[pt][gt];It.setCenter(It.getCenterX()+at[bt].dx,It.getCenterY()+at[bt].dy)}),bt++})}}}else{var R=I.eles.boundingBox();if(v.push({x:R.x1+R.w/2,y:R.y1+R.h/2}),I.randomize){var O=c(I);ut.push(O)}I.quality=="default"||I.quality=="proof"?(Z.push(h(I,ut[0])),l.relocateComponent(v[0],Z[0],I)):l.relocateComponent(v[0],ut[0],I)}var At=function(gt,It){if(I.quality=="default"||I.quality=="proof"){typeof gt=="number"&&(gt=It);var xt=void 0,Mt=void 0,Ot=gt.data("id");return Z.forEach(function(St){Ot in St&&(xt={x:St[Ot].getRect().getCenterX(),y:St[Ot].getRect().getCenterY()},Mt=St[Ot])}),I.nodeDimensionsIncludeLabels&&(Mt.labelWidth&&(Mt.labelPosHorizontal=="left"?xt.x+=Mt.labelWidth/2:Mt.labelPosHorizontal=="right"&&(xt.x-=Mt.labelWidth/2)),Mt.labelHeight&&(Mt.labelPosVertical=="top"?xt.y+=Mt.labelHeight/2:Mt.labelPosVertical=="bottom"&&(xt.y-=Mt.labelHeight/2))),xt==null&&(xt={x:gt.position("x"),y:gt.position("y")}),{x:xt.x,y:xt.y}}else{var Ct=void 0;return ut.forEach(function(St){var zt=St.nodeIndexes.get(gt.id());zt!=null&&(Ct={x:St.xCoords[zt],y:St.yCoords[zt]})}),Ct==null&&(Ct={x:gt.position("x"),y:gt.position("y")}),{x:Ct.x,y:Ct.y}}};if(I.quality=="default"||I.quality=="proof"||I.randomize){var Vt=l.calcParentsWithoutChildren(nt,dt),Gt=dt.filter(function(pt){return pt.css("display")=="none"});I.eles=dt.not(Gt),dt.nodes().not(":parent").not(Gt).layoutPositions(_,I,At),Vt.length>0&&Vt.forEach(function(pt){pt.position(At(pt))})}else console.log("If randomize option is set to false, then quality option must be 'default' or 'proof'.")}}]),L}();t.exports=N},657:(t,y,r)=>{var d=r(548),e=r(140).layoutBase.Matrix,a=r(140).layoutBase.SVD,l=function(c){var T=c.cy,h=c.eles,A=h.nodes(),N=h.nodes(":parent"),L=new Map,x=new Map,_=new Map,I=[],nt=[],dt=[],ut=[],Nt=[],U=[],Z=[],n=[],v=void 0,f=void 0,m=1e8,p=1e-9,R=c.piTol,O=c.samplingType,H=c.nodeSeparation,X=void 0,Y=function(){for(var g=0,M=0,C=!1;M<X;){g=Math.floor(Math.random()*f),C=!1;for(var S=0;S<M;S++)if(ut[S]==g){C=!0;break}if(!C)ut[M]=g,M++;else continue}},rt=function(g,M,C){for(var S=[],W=0,j=0,F=0,P=void 0,K=[],G=0,V=1,vt=0;vt<f;vt++)K[vt]=m;for(S[j]=g,K[g]=0;j>=W;){F=S[W++];for(var et=I[F],b=0;b<et.length;b++)P=x.get(et[b]),K[P]==m&&(K[P]=K[F]+1,S[++j]=P);U[F][M]=K[F]*H}if(C){for(var ft=0;ft<f;ft++)U[ft][M]<Nt[ft]&&(Nt[ft]=U[ft][M]);for(var Q=0;Q<f;Q++)Nt[Q]>G&&(G=Nt[Q],V=Q)}return V},ht=function(g){var M=void 0;if(g){M=Math.floor(Math.random()*f),v=M;for(var S=0;S<f;S++)Nt[S]=m;for(var W=0;W<X;W++)ut[W]=M,M=rt(M,W,g)}else{Y();for(var C=0;C<X;C++)rt(ut[C],C,g,!1)}for(var j=0;j<f;j++)for(var F=0;F<X;F++)U[j][F]*=U[j][F];for(var P=0;P<X;P++)Z[P]=[];for(var K=0;K<X;K++)for(var G=0;G<X;G++)Z[K][G]=U[ut[G]][K]},z=function(){for(var g=a.svd(Z),M=g.S,C=g.U,S=g.V,W=M[0]*M[0]*M[0],j=[],F=0;F<X;F++){j[F]=[];for(var P=0;P<X;P++)j[F][P]=0,F==P&&(j[F][P]=M[F]/(M[F]*M[F]+W/(M[F]*M[F])))}n=e.multMat(e.multMat(S,j),e.transpose(C))},lt=function(){for(var g=void 0,M=void 0,C=[],S=[],W=[],j=[],F=0;F<f;F++)C[F]=Math.random(),S[F]=Math.random();C=e.normalize(C),S=e.normalize(S);for(var P=0,K=p,G=p,V=void 0;;){P++;for(var vt=0;vt<f;vt++)W[vt]=C[vt];if(C=e.multGamma(e.multL(e.multGamma(W),U,n)),g=e.dotProduct(W,C),C=e.normalize(C),K=e.dotProduct(W,C),V=Math.abs(K/G),V<=1+R&&V>=1)break;G=K}for(var et=0;et<f;et++)W[et]=C[et];for(P=0,G=p;;){P++;for(var b=0;b<f;b++)j[b]=S[b];if(j=e.minusOp(j,e.multCons(W,e.dotProduct(W,j))),S=e.multGamma(e.multL(e.multGamma(j),U,n)),M=e.dotProduct(j,S),S=e.normalize(S),K=e.dotProduct(j,S),V=Math.abs(K/G),V<=1+R&&V>=1)break;G=K}for(var ft=0;ft<f;ft++)j[ft]=S[ft];nt=e.multCons(W,Math.sqrt(Math.abs(g))),dt=e.multCons(j,Math.sqrt(Math.abs(M)))};d.connectComponents(T,h,d.getTopMostNodes(A),L),N.forEach(function(q){d.connectComponents(T,h,d.getTopMostNodes(q.descendants().intersection(h)),L)});for(var tt=0,w=0;w<A.length;w++)A[w].isParent()||x.set(A[w].id(),tt++);var B=!0,k=!1,J=void 0;try{for(var Et=L.keys()[Symbol.iterator](),at;!(B=(at=Et.next()).done);B=!0){var bt=at.value;x.set(bt,tt++)}}catch(q){k=!0,J=q}finally{try{!B&&Et.return&&Et.return()}finally{if(k)throw J}}for(var At=0;At<x.size;At++)I[At]=[];N.forEach(function(q){for(var g=q.children().intersection(h);g.nodes(":childless").length==0;)g=g.nodes()[0].children().intersection(h);var M=0,C=g.nodes(":childless")[0].connectedEdges().length;g.nodes(":childless").forEach(function(S,W){S.connectedEdges().length<C&&(C=S.connectedEdges().length,M=W)}),_.set(q.id(),g.nodes(":childless")[M].id())}),A.forEach(function(q){var g=void 0;q.isParent()?g=x.get(_.get(q.id())):g=x.get(q.id()),q.neighborhood().nodes().forEach(function(M){h.intersection(q.edgesWith(M)).length>0&&(M.isParent()?I[g].push(_.get(M.id())):I[g].push(M.id()))})});var Vt=function(g){var M=x.get(g),C=void 0;L.get(g).forEach(function(S){T.getElementById(S).isParent()?C=_.get(S):C=S,I[M].push(C),I[x.get(C)].push(g)})},Gt=!0,pt=!1,gt=void 0;try{for(var It=L.keys()[Symbol.iterator](),xt;!(Gt=(xt=It.next()).done);Gt=!0){var Mt=xt.value;Vt(Mt)}}catch(q){pt=!0,gt=q}finally{try{!Gt&&It.return&&It.return()}finally{if(pt)throw gt}}f=x.size;var Ot=void 0;if(f>2){X=f<c.sampleSize?f:c.sampleSize;for(var Ct=0;Ct<f;Ct++)U[Ct]=[];for(var St=0;St<X;St++)n[St]=[];return c.quality=="draft"||c.step=="all"?(ht(O),z(),lt(),Ot={nodeIndexes:x,xCoords:nt,yCoords:dt}):(x.forEach(function(q,g){nt.push(T.getElementById(g).position("x")),dt.push(T.getElementById(g).position("y"))}),Ot={nodeIndexes:x,xCoords:nt,yCoords:dt}),Ot}else{var zt=x.keys(),Qt=T.getElementById(zt.next().value),Pt=Qt.position(),Wt=Qt.outerWidth();if(nt.push(Pt.x),dt.push(Pt.y),f==2){var Ht=T.getElementById(zt.next().value),$=Ht.outerWidth();nt.push(Pt.x+Wt/2+$/2+c.idealEdgeLength),dt.push(Pt.y)}return Ot={nodeIndexes:x,xCoords:nt,yCoords:dt},Ot}};t.exports={spectralLayout:l}},579:(t,y,r)=>{var d=r(212),e=function(l){l&&l("layout","fcose",d)};typeof cytoscape!="undefined"&&e(cytoscape),t.exports=e},140:t=>{t.exports=D}},s={};function o(t){var y=s[t];if(y!==void 0)return y.exports;var r=s[t]={exports:{}};return E[t](r,r.exports,o),r.exports}var i=o(579);return i})()})},65084:function(Xt){(function(st,D){Xt.exports=D()})(this,function(){return function(it){var st={};function D(E){if(st[E])return st[E].exports;var s=st[E]={i:E,l:!1,exports:{}};return it[E].call(s.exports,s,s.exports,D),s.l=!0,s.exports}return D.m=it,D.c=st,D.i=function(E){return E},D.d=function(E,s,o){D.o(E,s)||Object.defineProperty(E,s,{configurable:!1,enumerable:!0,get:o})},D.n=function(E){var s=E&&E.__esModule?function(){return E.default}:function(){return E};return D.d(s,"a",s),s},D.o=function(E,s){return Object.prototype.hasOwnProperty.call(E,s)},D.p="",D(D.s=28)}([function(it,st,D){"use strict";function E(){}E.QUALITY=1,E.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,E.DEFAULT_INCREMENTAL=!1,E.DEFAULT_ANIMATION_ON_LAYOUT=!0,E.DEFAULT_ANIMATION_DURING_LAYOUT=!1,E.DEFAULT_ANIMATION_PERIOD=50,E.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,E.DEFAULT_GRAPH_MARGIN=15,E.NODE_DIMENSIONS_INCLUDE_LABELS=!1,E.SIMPLE_NODE_SIZE=40,E.SIMPLE_NODE_HALF_SIZE=E.SIMPLE_NODE_SIZE/2,E.EMPTY_COMPOUND_NODE_SIZE=40,E.MIN_EDGE_LENGTH=1,E.WORLD_BOUNDARY=1e6,E.INITIAL_WORLD_BOUNDARY=E.WORLD_BOUNDARY/1e3,E.WORLD_CENTER_X=1200,E.WORLD_CENTER_Y=900,it.exports=E},function(it,st,D){"use strict";var E=D(2),s=D(8),o=D(9);function i(y,r,d){E.call(this,d),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=d,this.bendpoints=[],this.source=y,this.target=r}i.prototype=Object.create(E.prototype);for(var t in E)i[t]=E[t];i.prototype.getSource=function(){return this.source},i.prototype.getTarget=function(){return this.target},i.prototype.isInterGraph=function(){return this.isInterGraph},i.prototype.getLength=function(){return this.length},i.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},i.prototype.getBendpoints=function(){return this.bendpoints},i.prototype.getLca=function(){return this.lca},i.prototype.getSourceInLca=function(){return this.sourceInLca},i.prototype.getTargetInLca=function(){return this.targetInLca},i.prototype.getOtherEnd=function(y){if(this.source===y)return this.target;if(this.target===y)return this.source;throw"Node is not incident with this edge"},i.prototype.getOtherEndInGraph=function(y,r){for(var d=this.getOtherEnd(y),e=r.getGraphManager().getRoot();;){if(d.getOwner()==r)return d;if(d.getOwner()==e)break;d=d.getOwner().getParent()}return null},i.prototype.updateLength=function(){var y=new Array(4);this.isOverlapingSourceAndTarget=s.getIntersection(this.target.getRect(),this.source.getRect(),y),this.isOverlapingSourceAndTarget||(this.lengthX=y[0]-y[2],this.lengthY=y[1]-y[3],Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},i.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=o.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=o.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},it.exports=i},function(it,st,D){"use strict";function E(s){this.vGraphObject=s}it.exports=E},function(it,st,D){"use strict";var E=D(2),s=D(10),o=D(13),i=D(0),t=D(16),y=D(5);function r(e,a,l,u){l==null&&u==null&&(u=a),E.call(this,u),e.graphManager!=null&&(e=e.graphManager),this.estimatedSize=s.MIN_VALUE,this.inclusionTreeDepth=s.MAX_VALUE,this.vGraphObject=u,this.edges=[],this.graphManager=e,l!=null&&a!=null?this.rect=new o(a.x,a.y,l.width,l.height):this.rect=new o}r.prototype=Object.create(E.prototype);for(var d in E)r[d]=E[d];r.prototype.getEdges=function(){return this.edges},r.prototype.getChild=function(){return this.child},r.prototype.getOwner=function(){return this.owner},r.prototype.getWidth=function(){return this.rect.width},r.prototype.setWidth=function(e){this.rect.width=e},r.prototype.getHeight=function(){return this.rect.height},r.prototype.setHeight=function(e){this.rect.height=e},r.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},r.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},r.prototype.getCenter=function(){return new y(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},r.prototype.getLocation=function(){return new y(this.rect.x,this.rect.y)},r.prototype.getRect=function(){return this.rect},r.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},r.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},r.prototype.setRect=function(e,a){this.rect.x=e.x,this.rect.y=e.y,this.rect.width=a.width,this.rect.height=a.height},r.prototype.setCenter=function(e,a){this.rect.x=e-this.rect.width/2,this.rect.y=a-this.rect.height/2},r.prototype.setLocation=function(e,a){this.rect.x=e,this.rect.y=a},r.prototype.moveBy=function(e,a){this.rect.x+=e,this.rect.y+=a},r.prototype.getEdgeListToNode=function(e){var a=[],l,u=this;return u.edges.forEach(function(c){if(c.target==e){if(c.source!=u)throw"Incorrect edge source!";a.push(c)}}),a},r.prototype.getEdgesBetween=function(e){var a=[],l,u=this;return u.edges.forEach(function(c){if(!(c.source==u||c.target==u))throw"Incorrect edge source and/or target";(c.target==e||c.source==e)&&a.push(c)}),a},r.prototype.getNeighborsList=function(){var e=new Set,a=this;return a.edges.forEach(function(l){if(l.source==a)e.add(l.target);else{if(l.target!=a)throw"Incorrect incidency!";e.add(l.source)}}),e},r.prototype.withChildren=function(){var e=new Set,a,l;if(e.add(this),this.child!=null)for(var u=this.child.getNodes(),c=0;c<u.length;c++)a=u[c],l=a.withChildren(),l.forEach(function(T){e.add(T)});return e},r.prototype.getNoOfChildren=function(){var e=0,a;if(this.child==null)e=1;else for(var l=this.child.getNodes(),u=0;u<l.length;u++)a=l[u],e+=a.getNoOfChildren();return e==0&&(e=1),e},r.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},r.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},r.prototype.scatter=function(){var e,a,l=-i.INITIAL_WORLD_BOUNDARY,u=i.INITIAL_WORLD_BOUNDARY;e=i.WORLD_CENTER_X+t.nextDouble()*(u-l)+l;var c=-i.INITIAL_WORLD_BOUNDARY,T=i.INITIAL_WORLD_BOUNDARY;a=i.WORLD_CENTER_Y+t.nextDouble()*(T-c)+c,this.rect.x=e,this.rect.y=a},r.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var e=this.getChild();if(e.updateBounds(!0),this.rect.x=e.getLeft(),this.rect.y=e.getTop(),this.setWidth(e.getRight()-e.getLeft()),this.setHeight(e.getBottom()-e.getTop()),i.NODE_DIMENSIONS_INCLUDE_LABELS){var a=e.getRight()-e.getLeft(),l=e.getBottom()-e.getTop();this.labelWidth&&(this.labelPosHorizontal=="left"?(this.rect.x-=this.labelWidth,this.setWidth(a+this.labelWidth)):this.labelPosHorizontal=="center"&&this.labelWidth>a?(this.rect.x-=(this.labelWidth-a)/2,this.setWidth(this.labelWidth)):this.labelPosHorizontal=="right"&&this.setWidth(a+this.labelWidth)),this.labelHeight&&(this.labelPosVertical=="top"?(this.rect.y-=this.labelHeight,this.setHeight(l+this.labelHeight)):this.labelPosVertical=="center"&&this.labelHeight>l?(this.rect.y-=(this.labelHeight-l)/2,this.setHeight(this.labelHeight)):this.labelPosVertical=="bottom"&&this.setHeight(l+this.labelHeight))}}},r.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==s.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},r.prototype.transform=function(e){var a=this.rect.x;a>i.WORLD_BOUNDARY?a=i.WORLD_BOUNDARY:a<-i.WORLD_BOUNDARY&&(a=-i.WORLD_BOUNDARY);var l=this.rect.y;l>i.WORLD_BOUNDARY?l=i.WORLD_BOUNDARY:l<-i.WORLD_BOUNDARY&&(l=-i.WORLD_BOUNDARY);var u=new y(a,l),c=e.inverseTransformPoint(u);this.setLocation(c.x,c.y)},r.prototype.getLeft=function(){return this.rect.x},r.prototype.getRight=function(){return this.rect.x+this.rect.width},r.prototype.getTop=function(){return this.rect.y},r.prototype.getBottom=function(){return this.rect.y+this.rect.height},r.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},it.exports=r},function(it,st,D){"use strict";var E=D(0);function s(){}for(var o in E)s[o]=E[o];s.MAX_ITERATIONS=2500,s.DEFAULT_EDGE_LENGTH=50,s.DEFAULT_SPRING_STRENGTH=.45,s.DEFAULT_REPULSION_STRENGTH=4500,s.DEFAULT_GRAVITY_STRENGTH=.4,s.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,s.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,s.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,s.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,s.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,s.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,s.COOLING_ADAPTATION_FACTOR=.33,s.ADAPTATION_LOWER_NODE_LIMIT=1e3,s.ADAPTATION_UPPER_NODE_LIMIT=5e3,s.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,s.MAX_NODE_DISPLACEMENT=s.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,s.MIN_REPULSION_DIST=s.DEFAULT_EDGE_LENGTH/10,s.CONVERGENCE_CHECK_PERIOD=100,s.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,s.MIN_EDGE_LENGTH=1,s.GRID_CALCULATION_CHECK_PERIOD=10,it.exports=s},function(it,st,D){"use strict";function E(s,o){s==null&&o==null?(this.x=0,this.y=0):(this.x=s,this.y=o)}E.prototype.getX=function(){return this.x},E.prototype.getY=function(){return this.y},E.prototype.setX=function(s){this.x=s},E.prototype.setY=function(s){this.y=s},E.prototype.getDifference=function(s){return new DimensionD(this.x-s.x,this.y-s.y)},E.prototype.getCopy=function(){return new E(this.x,this.y)},E.prototype.translate=function(s){return this.x+=s.width,this.y+=s.height,this},it.exports=E},function(it,st,D){"use strict";var E=D(2),s=D(10),o=D(0),i=D(7),t=D(3),y=D(1),r=D(13),d=D(12),e=D(11);function a(u,c,T){E.call(this,T),this.estimatedSize=s.MIN_VALUE,this.margin=o.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=u,c!=null&&c instanceof i?this.graphManager=c:c!=null&&c instanceof Layout&&(this.graphManager=c.graphManager)}a.prototype=Object.create(E.prototype);for(var l in E)a[l]=E[l];a.prototype.getNodes=function(){return this.nodes},a.prototype.getEdges=function(){return this.edges},a.prototype.getGraphManager=function(){return this.graphManager},a.prototype.getParent=function(){return this.parent},a.prototype.getLeft=function(){return this.left},a.prototype.getRight=function(){return this.right},a.prototype.getTop=function(){return this.top},a.prototype.getBottom=function(){return this.bottom},a.prototype.isConnected=function(){return this.isConnected},a.prototype.add=function(u,c,T){if(c==null&&T==null){var h=u;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(h)>-1)throw"Node already in graph!";return h.owner=this,this.getNodes().push(h),h}else{var A=u;if(!(this.getNodes().indexOf(c)>-1&&this.getNodes().indexOf(T)>-1))throw"Source or target not in graph!";if(!(c.owner==T.owner&&c.owner==this))throw"Both owners must be this graph!";return c.owner!=T.owner?null:(A.source=c,A.target=T,A.isInterGraph=!1,this.getEdges().push(A),c.edges.push(A),T!=c&&T.edges.push(A),A)}},a.prototype.remove=function(u){var c=u;if(u instanceof t){if(c==null)throw"Node is null!";if(!(c.owner!=null&&c.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var T=c.edges.slice(),h,A=T.length,N=0;N<A;N++)h=T[N],h.isInterGraph?this.graphManager.remove(h):h.source.owner.remove(h);var L=this.nodes.indexOf(c);if(L==-1)throw"Node not in owner node list!";this.nodes.splice(L,1)}else if(u instanceof y){var h=u;if(h==null)throw"Edge is null!";if(!(h.source!=null&&h.target!=null))throw"Source and/or target is null!";if(!(h.source.owner!=null&&h.target.owner!=null&&h.source.owner==this&&h.target.owner==this))throw"Source and/or target owner is invalid!";var x=h.source.edges.indexOf(h),_=h.target.edges.indexOf(h);if(!(x>-1&&_>-1))throw"Source and/or target doesn't know this edge!";h.source.edges.splice(x,1),h.target!=h.source&&h.target.edges.splice(_,1);var L=h.source.owner.getEdges().indexOf(h);if(L==-1)throw"Not in owner's edge list!";h.source.owner.getEdges().splice(L,1)}},a.prototype.updateLeftTop=function(){for(var u=s.MAX_VALUE,c=s.MAX_VALUE,T,h,A,N=this.getNodes(),L=N.length,x=0;x<L;x++){var _=N[x];T=_.getTop(),h=_.getLeft(),u>T&&(u=T),c>h&&(c=h)}return u==s.MAX_VALUE?null:(N[0].getParent().paddingLeft!=null?A=N[0].getParent().paddingLeft:A=this.margin,this.left=c-A,this.top=u-A,new d(this.left,this.top))},a.prototype.updateBounds=function(u){for(var c=s.MAX_VALUE,T=-s.MAX_VALUE,h=s.MAX_VALUE,A=-s.MAX_VALUE,N,L,x,_,I,nt=this.nodes,dt=nt.length,ut=0;ut<dt;ut++){var Nt=nt[ut];u&&Nt.child!=null&&Nt.updateBounds(),N=Nt.getLeft(),L=Nt.getRight(),x=Nt.getTop(),_=Nt.getBottom(),c>N&&(c=N),T<L&&(T=L),h>x&&(h=x),A<_&&(A=_)}var U=new r(c,h,T-c,A-h);c==s.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),nt[0].getParent().paddingLeft!=null?I=nt[0].getParent().paddingLeft:I=this.margin,this.left=U.x-I,this.right=U.x+U.width+I,this.top=U.y-I,this.bottom=U.y+U.height+I},a.calculateBounds=function(u){for(var c=s.MAX_VALUE,T=-s.MAX_VALUE,h=s.MAX_VALUE,A=-s.MAX_VALUE,N,L,x,_,I=u.length,nt=0;nt<I;nt++){var dt=u[nt];N=dt.getLeft(),L=dt.getRight(),x=dt.getTop(),_=dt.getBottom(),c>N&&(c=N),T<L&&(T=L),h>x&&(h=x),A<_&&(A=_)}var ut=new r(c,h,T-c,A-h);return ut},a.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==s.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){for(var u=0,c=this.nodes,T=c.length,h=0;h<T;h++){var A=c[h];u+=A.calcEstimatedSize()}return u==0?this.estimatedSize=o.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=u/Math.sqrt(this.nodes.length),this.estimatedSize},a.prototype.updateConnected=function(){var u=this;if(this.nodes.length==0){this.isConnected=!0;return}var c=new e,T=new Set,h=this.nodes[0],A,N,L=h.withChildren();for(L.forEach(function(ut){c.push(ut),T.add(ut)});c.length!==0;){h=c.shift(),A=h.getEdges();for(var x=A.length,_=0;_<x;_++){var I=A[_];if(N=I.getOtherEndInGraph(h,this),N!=null&&!T.has(N)){var nt=N.withChildren();nt.forEach(function(ut){c.push(ut),T.add(ut)})}}}if(this.isConnected=!1,T.size>=this.nodes.length){var dt=0;T.forEach(function(ut){ut.owner==u&&dt++}),dt==this.nodes.length&&(this.isConnected=!0)}},it.exports=a},function(it,st,D){"use strict";var E,s=D(1);function o(i){E=D(6),this.layout=i,this.graphs=[],this.edges=[]}o.prototype.addRoot=function(){var i=this.layout.newGraph(),t=this.layout.newNode(null),y=this.add(i,t);return this.setRootGraph(y),this.rootGraph},o.prototype.add=function(i,t,y,r,d){if(y==null&&r==null&&d==null){if(i==null)throw"Graph is null!";if(t==null)throw"Parent node is null!";if(this.graphs.indexOf(i)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(i),i.parent!=null)throw"Already has a parent!";if(t.child!=null)throw"Already has a child!";return i.parent=t,t.child=i,i}else{d=y,r=t,y=i;var e=r.getOwner(),a=d.getOwner();if(!(e!=null&&e.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(a!=null&&a.getGraphManager()==this))throw"Target not in this graph mgr!";if(e==a)return y.isInterGraph=!1,e.add(y,r,d);if(y.isInterGraph=!0,y.source=r,y.target=d,this.edges.indexOf(y)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(y),!(y.source!=null&&y.target!=null))throw"Edge source and/or target is null!";if(!(y.source.edges.indexOf(y)==-1&&y.target.edges.indexOf(y)==-1))throw"Edge already in source and/or target incidency list!";return y.source.edges.push(y),y.target.edges.push(y),y}},o.prototype.remove=function(i){if(i instanceof E){var t=i;if(t.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(t==this.rootGraph||t.parent!=null&&t.parent.graphManager==this))throw"Invalid parent node!";var y=[];y=y.concat(t.getEdges());for(var r,d=y.length,e=0;e<d;e++)r=y[e],t.remove(r);var a=[];a=a.concat(t.getNodes());var l;d=a.length;for(var e=0;e<d;e++)l=a[e],t.remove(l);t==this.rootGraph&&this.setRootGraph(null);var u=this.graphs.indexOf(t);this.graphs.splice(u,1),t.parent=null}else if(i instanceof s){if(r=i,r==null)throw"Edge is null!";if(!r.isInterGraph)throw"Not an inter-graph edge!";if(!(r.source!=null&&r.target!=null))throw"Source and/or target is null!";if(!(r.source.edges.indexOf(r)!=-1&&r.target.edges.indexOf(r)!=-1))throw"Source and/or target doesn't know this edge!";var u=r.source.edges.indexOf(r);if(r.source.edges.splice(u,1),u=r.target.edges.indexOf(r),r.target.edges.splice(u,1),!(r.source.owner!=null&&r.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(r.source.owner.getGraphManager().edges.indexOf(r)==-1)throw"Not in owner graph manager's edge list!";var u=r.source.owner.getGraphManager().edges.indexOf(r);r.source.owner.getGraphManager().edges.splice(u,1)}},o.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},o.prototype.getGraphs=function(){return this.graphs},o.prototype.getAllNodes=function(){if(this.allNodes==null){for(var i=[],t=this.getGraphs(),y=t.length,r=0;r<y;r++)i=i.concat(t[r].getNodes());this.allNodes=i}return this.allNodes},o.prototype.resetAllNodes=function(){this.allNodes=null},o.prototype.resetAllEdges=function(){this.allEdges=null},o.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},o.prototype.getAllEdges=function(){if(this.allEdges==null){for(var i=[],t=this.getGraphs(),y=t.length,r=0;r<t.length;r++)i=i.concat(t[r].getEdges());i=i.concat(this.edges),this.allEdges=i}return this.allEdges},o.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},o.prototype.setAllNodesToApplyGravitation=function(i){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=i},o.prototype.getRoot=function(){return this.rootGraph},o.prototype.setRootGraph=function(i){if(i.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=i,i.parent==null&&(i.parent=this.layout.newNode("Root node"))},o.prototype.getLayout=function(){return this.layout},o.prototype.isOneAncestorOfOther=function(i,t){if(!(i!=null&&t!=null))throw"assert failed";if(i==t)return!0;var y=i.getOwner(),r;do{if(r=y.getParent(),r==null)break;if(r==t)return!0;if(y=r.getOwner(),y==null)break}while(!0);y=t.getOwner();do{if(r=y.getParent(),r==null)break;if(r==i)return!0;if(y=r.getOwner(),y==null)break}while(!0);return!1},o.prototype.calcLowestCommonAncestors=function(){for(var i,t,y,r,d,e=this.getAllEdges(),a=e.length,l=0;l<a;l++){if(i=e[l],t=i.source,y=i.target,i.lca=null,i.sourceInLca=t,i.targetInLca=y,t==y){i.lca=t.getOwner();continue}for(r=t.getOwner();i.lca==null;){for(i.targetInLca=y,d=y.getOwner();i.lca==null;){if(d==r){i.lca=d;break}if(d==this.rootGraph)break;if(i.lca!=null)throw"assert failed";i.targetInLca=d.getParent(),d=i.targetInLca.getOwner()}if(r==this.rootGraph)break;i.lca==null&&(i.sourceInLca=r.getParent(),r=i.sourceInLca.getOwner())}if(i.lca==null)throw"assert failed"}},o.prototype.calcLowestCommonAncestor=function(i,t){if(i==t)return i.getOwner();var y=i.getOwner();do{if(y==null)break;var r=t.getOwner();do{if(r==null)break;if(r==y)return r;r=r.getParent().getOwner()}while(!0);y=y.getParent().getOwner()}while(!0);return y},o.prototype.calcInclusionTreeDepths=function(i,t){i==null&&t==null&&(i=this.rootGraph,t=1);for(var y,r=i.getNodes(),d=r.length,e=0;e<d;e++)y=r[e],y.inclusionTreeDepth=t,y.child!=null&&this.calcInclusionTreeDepths(y.child,t+1)},o.prototype.includesInvalidEdge=function(){for(var i,t=[],y=this.edges.length,r=0;r<y;r++)i=this.edges[r],this.isOneAncestorOfOther(i.source,i.target)&&t.push(i);for(var r=0;r<t.length;r++)this.remove(t[r]);return!1},it.exports=o},function(it,st,D){"use strict";var E=D(12);function s(){}s.calcSeparationAmount=function(o,i,t,y){if(!o.intersects(i))throw"assert failed";var r=new Array(2);this.decideDirectionsForOverlappingNodes(o,i,r),t[0]=Math.min(o.getRight(),i.getRight())-Math.max(o.x,i.x),t[1]=Math.min(o.getBottom(),i.getBottom())-Math.max(o.y,i.y),o.getX()<=i.getX()&&o.getRight()>=i.getRight()?t[0]+=Math.min(i.getX()-o.getX(),o.getRight()-i.getRight()):i.getX()<=o.getX()&&i.getRight()>=o.getRight()&&(t[0]+=Math.min(o.getX()-i.getX(),i.getRight()-o.getRight())),o.getY()<=i.getY()&&o.getBottom()>=i.getBottom()?t[1]+=Math.min(i.getY()-o.getY(),o.getBottom()-i.getBottom()):i.getY()<=o.getY()&&i.getBottom()>=o.getBottom()&&(t[1]+=Math.min(o.getY()-i.getY(),i.getBottom()-o.getBottom()));var d=Math.abs((i.getCenterY()-o.getCenterY())/(i.getCenterX()-o.getCenterX()));i.getCenterY()===o.getCenterY()&&i.getCenterX()===o.getCenterX()&&(d=1);var e=d*t[0],a=t[1]/d;t[0]<a?a=t[0]:e=t[1],t[0]=-1*r[0]*(a/2+y),t[1]=-1*r[1]*(e/2+y)},s.decideDirectionsForOverlappingNodes=function(o,i,t){o.getCenterX()<i.getCenterX()?t[0]=-1:t[0]=1,o.getCenterY()<i.getCenterY()?t[1]=-1:t[1]=1},s.getIntersection2=function(o,i,t){var y=o.getCenterX(),r=o.getCenterY(),d=i.getCenterX(),e=i.getCenterY();if(o.intersects(i))return t[0]=y,t[1]=r,t[2]=d,t[3]=e,!0;var a=o.getX(),l=o.getY(),u=o.getRight(),c=o.getX(),T=o.getBottom(),h=o.getRight(),A=o.getWidthHalf(),N=o.getHeightHalf(),L=i.getX(),x=i.getY(),_=i.getRight(),I=i.getX(),nt=i.getBottom(),dt=i.getRight(),ut=i.getWidthHalf(),Nt=i.getHeightHalf(),U=!1,Z=!1;if(y===d){if(r>e)return t[0]=y,t[1]=l,t[2]=d,t[3]=nt,!1;if(r<e)return t[0]=y,t[1]=T,t[2]=d,t[3]=x,!1}else if(r===e){if(y>d)return t[0]=a,t[1]=r,t[2]=_,t[3]=e,!1;if(y<d)return t[0]=u,t[1]=r,t[2]=L,t[3]=e,!1}else{var n=o.height/o.width,v=i.height/i.width,f=(e-r)/(d-y),m=void 0,p=void 0,R=void 0,O=void 0,H=void 0,X=void 0;if(-n===f?y>d?(t[0]=c,t[1]=T,U=!0):(t[0]=u,t[1]=l,U=!0):n===f&&(y>d?(t[0]=a,t[1]=l,U=!0):(t[0]=h,t[1]=T,U=!0)),-v===f?d>y?(t[2]=I,t[3]=nt,Z=!0):(t[2]=_,t[3]=x,Z=!0):v===f&&(d>y?(t[2]=L,t[3]=x,Z=!0):(t[2]=dt,t[3]=nt,Z=!0)),U&&Z)return!1;if(y>d?r>e?(m=this.getCardinalDirection(n,f,4),p=this.getCardinalDirection(v,f,2)):(m=this.getCardinalDirection(-n,f,3),p=this.getCardinalDirection(-v,f,1)):r>e?(m=this.getCardinalDirection(-n,f,1),p=this.getCardinalDirection(-v,f,3)):(m=this.getCardinalDirection(n,f,2),p=this.getCardinalDirection(v,f,4)),!U)switch(m){case 1:O=l,R=y+-N/f,t[0]=R,t[1]=O;break;case 2:R=h,O=r+A*f,t[0]=R,t[1]=O;break;case 3:O=T,R=y+N/f,t[0]=R,t[1]=O;break;case 4:R=c,O=r+-A*f,t[0]=R,t[1]=O;break}if(!Z)switch(p){case 1:X=x,H=d+-Nt/f,t[2]=H,t[3]=X;break;case 2:H=dt,X=e+ut*f,t[2]=H,t[3]=X;break;case 3:X=nt,H=d+Nt/f,t[2]=H,t[3]=X;break;case 4:H=I,X=e+-ut*f,t[2]=H,t[3]=X;break}}return!1},s.getCardinalDirection=function(o,i,t){return o>i?t:1+t%4},s.getIntersection=function(o,i,t,y){if(y==null)return this.getIntersection2(o,i,t);var r=o.x,d=o.y,e=i.x,a=i.y,l=t.x,u=t.y,c=y.x,T=y.y,h=void 0,A=void 0,N=void 0,L=void 0,x=void 0,_=void 0,I=void 0,nt=void 0,dt=void 0;return N=a-d,x=r-e,I=e*d-r*a,L=T-u,_=l-c,nt=c*u-l*T,dt=N*_-L*x,dt===0?null:(h=(x*nt-_*I)/dt,A=(L*I-N*nt)/dt,new E(h,A))},s.angleOfVector=function(o,i,t,y){var r=void 0;return o!==t?(r=Math.atan((y-i)/(t-o)),t<o?r+=Math.PI:y<i&&(r+=this.TWO_PI)):y<i?r=this.ONE_AND_HALF_PI:r=this.HALF_PI,r},s.doIntersect=function(o,i,t,y){var r=o.x,d=o.y,e=i.x,a=i.y,l=t.x,u=t.y,c=y.x,T=y.y,h=(e-r)*(T-u)-(c-l)*(a-d);if(h===0)return!1;var A=((T-u)*(c-r)+(l-c)*(T-d))/h,N=((d-a)*(c-r)+(e-r)*(T-d))/h;return 0<A&&A<1&&0<N&&N<1},s.findCircleLineIntersections=function(o,i,t,y,r,d,e){var a=(t-o)*(t-o)+(y-i)*(y-i),l=2*((o-r)*(t-o)+(i-d)*(y-i)),u=(o-r)*(o-r)+(i-d)*(i-d)-e*e,c=l*l-4*a*u;if(c>=0){var T=(-l+Math.sqrt(l*l-4*a*u))/(2*a),h=(-l-Math.sqrt(l*l-4*a*u))/(2*a),A=null;return T>=0&&T<=1?[T]:h>=0&&h<=1?[h]:A}else return null},s.HALF_PI=.5*Math.PI,s.ONE_AND_HALF_PI=1.5*Math.PI,s.TWO_PI=2*Math.PI,s.THREE_PI=3*Math.PI,it.exports=s},function(it,st,D){"use strict";function E(){}E.sign=function(s){return s>0?1:s<0?-1:0},E.floor=function(s){return s<0?Math.ceil(s):Math.floor(s)},E.ceil=function(s){return s<0?Math.floor(s):Math.ceil(s)},it.exports=E},function(it,st,D){"use strict";function E(){}E.MAX_VALUE=2147483647,E.MIN_VALUE=-2147483648,it.exports=E},function(it,st,D){"use strict";var E=function(){function r(d,e){for(var a=0;a<e.length;a++){var l=e[a];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(d,l.key,l)}}return function(d,e,a){return e&&r(d.prototype,e),a&&r(d,a),d}}();function s(r,d){if(!(r instanceof d))throw new TypeError("Cannot call a class as a function")}var o=function(d){return{value:d,next:null,prev:null}},i=function(d,e,a,l){return d!==null?d.next=e:l.head=e,a!==null?a.prev=e:l.tail=e,e.prev=d,e.next=a,l.length++,e},t=function(d,e){var a=d.prev,l=d.next;return a!==null?a.next=l:e.head=l,l!==null?l.prev=a:e.tail=a,d.prev=d.next=null,e.length--,d},y=function(){function r(d){var e=this;s(this,r),this.length=0,this.head=null,this.tail=null,d!=null&&d.forEach(function(a){return e.push(a)})}return E(r,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(e,a){return i(a.prev,o(e),a,this)}},{key:"insertAfter",value:function(e,a){return i(a,o(e),a.next,this)}},{key:"insertNodeBefore",value:function(e,a){return i(a.prev,e,a,this)}},{key:"insertNodeAfter",value:function(e,a){return i(a,e,a.next,this)}},{key:"push",value:function(e){return i(this.tail,o(e),null,this)}},{key:"unshift",value:function(e){return i(null,o(e),this.head,this)}},{key:"remove",value:function(e){return t(e,this)}},{key:"pop",value:function(){return t(this.tail,this).value}},{key:"popNode",value:function(){return t(this.tail,this)}},{key:"shift",value:function(){return t(this.head,this).value}},{key:"shiftNode",value:function(){return t(this.head,this)}},{key:"get_object_at",value:function(e){if(e<=this.length()){for(var a=1,l=this.head;a<e;)l=l.next,a++;return l.value}}},{key:"set_object_at",value:function(e,a){if(e<=this.length()){for(var l=1,u=this.head;l<e;)u=u.next,l++;u.value=a}}}]),r}();it.exports=y},function(it,st,D){"use strict";function E(s,o,i){this.x=null,this.y=null,s==null&&o==null&&i==null?(this.x=0,this.y=0):typeof s=="number"&&typeof o=="number"&&i==null?(this.x=s,this.y=o):s.constructor.name=="Point"&&o==null&&i==null&&(i=s,this.x=i.x,this.y=i.y)}E.prototype.getX=function(){return this.x},E.prototype.getY=function(){return this.y},E.prototype.getLocation=function(){return new E(this.x,this.y)},E.prototype.setLocation=function(s,o,i){s.constructor.name=="Point"&&o==null&&i==null?(i=s,this.setLocation(i.x,i.y)):typeof s=="number"&&typeof o=="number"&&i==null&&(parseInt(s)==s&&parseInt(o)==o?this.move(s,o):(this.x=Math.floor(s+.5),this.y=Math.floor(o+.5)))},E.prototype.move=function(s,o){this.x=s,this.y=o},E.prototype.translate=function(s,o){this.x+=s,this.y+=o},E.prototype.equals=function(s){if(s.constructor.name=="Point"){var o=s;return this.x==o.x&&this.y==o.y}return this==s},E.prototype.toString=function(){return new E().constructor.name+"[x="+this.x+",y="+this.y+"]"},it.exports=E},function(it,st,D){"use strict";function E(s,o,i,t){this.x=0,this.y=0,this.width=0,this.height=0,s!=null&&o!=null&&i!=null&&t!=null&&(this.x=s,this.y=o,this.width=i,this.height=t)}E.prototype.getX=function(){return this.x},E.prototype.setX=function(s){this.x=s},E.prototype.getY=function(){return this.y},E.prototype.setY=function(s){this.y=s},E.prototype.getWidth=function(){return this.width},E.prototype.setWidth=function(s){this.width=s},E.prototype.getHeight=function(){return this.height},E.prototype.setHeight=function(s){this.height=s},E.prototype.getRight=function(){return this.x+this.width},E.prototype.getBottom=function(){return this.y+this.height},E.prototype.intersects=function(s){return!(this.getRight()<s.x||this.getBottom()<s.y||s.getRight()<this.x||s.getBottom()<this.y)},E.prototype.getCenterX=function(){return this.x+this.width/2},E.prototype.getMinX=function(){return this.getX()},E.prototype.getMaxX=function(){return this.getX()+this.width},E.prototype.getCenterY=function(){return this.y+this.height/2},E.prototype.getMinY=function(){return this.getY()},E.prototype.getMaxY=function(){return this.getY()+this.height},E.prototype.getWidthHalf=function(){return this.width/2},E.prototype.getHeightHalf=function(){return this.height/2},it.exports=E},function(it,st,D){"use strict";var E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o};function s(){}s.lastID=0,s.createID=function(o){return s.isPrimitive(o)?o:(o.uniqueID!=null||(o.uniqueID=s.getString(),s.lastID++),o.uniqueID)},s.getString=function(o){return o==null&&(o=s.lastID),"Object#"+o},s.isPrimitive=function(o){var i=typeof o=="undefined"?"undefined":E(o);return o==null||i!="object"&&i!="function"},it.exports=s},function(it,st,D){"use strict";function E(l){if(Array.isArray(l)){for(var u=0,c=Array(l.length);u<l.length;u++)c[u]=l[u];return c}else return Array.from(l)}var s=D(0),o=D(7),i=D(3),t=D(1),y=D(6),r=D(5),d=D(17),e=D(29);function a(l){e.call(this),this.layoutQuality=s.QUALITY,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=s.DEFAULT_INCREMENTAL,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new o(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,l!=null&&(this.isRemoteUse=l)}a.RANDOM_SEED=1,a.prototype=Object.create(e.prototype),a.prototype.getGraphManager=function(){return this.graphManager},a.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},a.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},a.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},a.prototype.newGraphManager=function(){var l=new o(this);return this.graphManager=l,l},a.prototype.newGraph=function(l){return new y(null,this.graphManager,l)},a.prototype.newNode=function(l){return new i(this.graphManager,l)},a.prototype.newEdge=function(l){return new t(null,null,l)},a.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},a.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var l;return this.checkLayoutSuccess()?l=!1:l=this.layout(),s.ANIMATE==="during"?!1:(l&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,l)},a.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},a.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var l,u=this.graphManager.getAllEdges(),c=0;c<u.length;c++)l=u[c];for(var T,h=this.graphManager.getRoot().getNodes(),c=0;c<h.length;c++)T=h[c];this.update(this.graphManager.getRoot())}},a.prototype.update=function(l){if(l==null)this.update2();else if(l instanceof i){var u=l;if(u.getChild()!=null)for(var c=u.getChild().getNodes(),T=0;T<c.length;T++)update(c[T]);if(u.vGraphObject!=null){var h=u.vGraphObject;h.update(u)}}else if(l instanceof t){var A=l;if(A.vGraphObject!=null){var N=A.vGraphObject;N.update(A)}}else if(l instanceof y){var L=l;if(L.vGraphObject!=null){var x=L.vGraphObject;x.update(L)}}},a.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=s.QUALITY,this.animationDuringLayout=s.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=s.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=s.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=s.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=s.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=s.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},a.prototype.transform=function(l){if(l==null)this.transform(new r(0,0));else{var u=new d,c=this.graphManager.getRoot().updateLeftTop();if(c!=null){u.setWorldOrgX(l.x),u.setWorldOrgY(l.y),u.setDeviceOrgX(c.x),u.setDeviceOrgY(c.y);for(var T=this.getAllNodes(),h,A=0;A<T.length;A++)h=T[A],h.transform(u)}}},a.prototype.positionNodesRandomly=function(l){if(l==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var u,c,T=l.getNodes(),h=0;h<T.length;h++)u=T[h],c=u.getChild(),c==null||c.getNodes().length==0?u.scatter():(this.positionNodesRandomly(c),u.updateBounds())},a.prototype.getFlatForest=function(){for(var l=[],u=!0,c=this.graphManager.getRoot().getNodes(),T=!0,h=0;h<c.length;h++)c[h].getChild()!=null&&(T=!1);if(!T)return l;var A=new Set,N=[],L=new Map,x=[];for(x=x.concat(c);x.length>0&&u;){for(N.push(x[0]);N.length>0&&u;){var _=N[0];N.splice(0,1),A.add(_);for(var I=_.getEdges(),h=0;h<I.length;h++){var nt=I[h].getOtherEnd(_);if(L.get(_)!=nt)if(!A.has(nt))N.push(nt),L.set(nt,_);else{u=!1;break}}}if(!u)l=[];else{var dt=[].concat(E(A));l.push(dt);for(var h=0;h<dt.length;h++){var ut=dt[h],Nt=x.indexOf(ut);Nt>-1&&x.splice(Nt,1)}A=new Set,L=new Map}}return l},a.prototype.createDummyNodesForBendpoints=function(l){for(var u=[],c=l.source,T=this.graphManager.calcLowestCommonAncestor(l.source,l.target),h=0;h<l.bendpoints.length;h++){var A=this.newNode(null);A.setRect(new Point(0,0),new Dimension(1,1)),T.add(A);var N=this.newEdge(null);this.graphManager.add(N,c,A),u.add(A),c=A}var N=this.newEdge(null);return this.graphManager.add(N,c,l.target),this.edgeToDummyNodes.set(l,u),l.isInterGraph()?this.graphManager.remove(l):T.remove(l),u},a.prototype.createBendpointsFromDummyNodes=function(){var l=[];l=l.concat(this.graphManager.getAllEdges()),l=[].concat(E(this.edgeToDummyNodes.keys())).concat(l);for(var u=0;u<l.length;u++){var c=l[u];if(c.bendpoints.length>0){for(var T=this.edgeToDummyNodes.get(c),h=0;h<T.length;h++){var A=T[h],N=new r(A.getCenterX(),A.getCenterY()),L=c.bendpoints.get(h);L.x=N.x,L.y=N.y,A.getOwner().remove(A)}this.graphManager.add(c,c.source,c.target)}}},a.transform=function(l,u,c,T){if(c!=null&&T!=null){var h=u;if(l<=50){var A=u/c;h-=(u-A)/50*(50-l)}else{var N=u*T;h+=(N-u)/50*(l-50)}return h}else{var L,x;return l<=50?(L=9*u/500,x=u/10):(L=9*u/50,x=-8*u),L*l+x}},a.findCenterOfTree=function(l){var u=[];u=u.concat(l);var c=[],T=new Map,h=!1,A=null;(u.length==1||u.length==2)&&(h=!0,A=u[0]);for(var N=0;N<u.length;N++){var L=u[N],x=L.getNeighborsList().size;T.set(L,L.getNeighborsList().size),x==1&&c.push(L)}var _=[];for(_=_.concat(c);!h;){var I=[];I=I.concat(_),_=[];for(var N=0;N<u.length;N++){var L=u[N],nt=u.indexOf(L);nt>=0&&u.splice(nt,1);var dt=L.getNeighborsList();dt.forEach(function(U){if(c.indexOf(U)<0){var Z=T.get(U),n=Z-1;n==1&&_.push(U),T.set(U,n)}})}c=c.concat(_),(u.length==1||u.length==2)&&(h=!0,A=u[0])}return A},a.prototype.setGraphManager=function(l){this.graphManager=l},it.exports=a},function(it,st,D){"use strict";function E(){}E.seed=1,E.x=0,E.nextDouble=function(){return E.x=Math.sin(E.seed++)*1e4,E.x-Math.floor(E.x)},it.exports=E},function(it,st,D){"use strict";var E=D(5);function s(o,i){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}s.prototype.getWorldOrgX=function(){return this.lworldOrgX},s.prototype.setWorldOrgX=function(o){this.lworldOrgX=o},s.prototype.getWorldOrgY=function(){return this.lworldOrgY},s.prototype.setWorldOrgY=function(o){this.lworldOrgY=o},s.prototype.getWorldExtX=function(){return this.lworldExtX},s.prototype.setWorldExtX=function(o){this.lworldExtX=o},s.prototype.getWorldExtY=function(){return this.lworldExtY},s.prototype.setWorldExtY=function(o){this.lworldExtY=o},s.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},s.prototype.setDeviceOrgX=function(o){this.ldeviceOrgX=o},s.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},s.prototype.setDeviceOrgY=function(o){this.ldeviceOrgY=o},s.prototype.getDeviceExtX=function(){return this.ldeviceExtX},s.prototype.setDeviceExtX=function(o){this.ldeviceExtX=o},s.prototype.getDeviceExtY=function(){return this.ldeviceExtY},s.prototype.setDeviceExtY=function(o){this.ldeviceExtY=o},s.prototype.transformX=function(o){var i=0,t=this.lworldExtX;return t!=0&&(i=this.ldeviceOrgX+(o-this.lworldOrgX)*this.ldeviceExtX/t),i},s.prototype.transformY=function(o){var i=0,t=this.lworldExtY;return t!=0&&(i=this.ldeviceOrgY+(o-this.lworldOrgY)*this.ldeviceExtY/t),i},s.prototype.inverseTransformX=function(o){var i=0,t=this.ldeviceExtX;return t!=0&&(i=this.lworldOrgX+(o-this.ldeviceOrgX)*this.lworldExtX/t),i},s.prototype.inverseTransformY=function(o){var i=0,t=this.ldeviceExtY;return t!=0&&(i=this.lworldOrgY+(o-this.ldeviceOrgY)*this.lworldExtY/t),i},s.prototype.inverseTransformPoint=function(o){var i=new E(this.inverseTransformX(o.x),this.inverseTransformY(o.y));return i},it.exports=s},function(it,st,D){"use strict";function E(e){if(Array.isArray(e)){for(var a=0,l=Array(e.length);a<e.length;a++)l[a]=e[a];return l}else return Array.from(e)}var s=D(15),o=D(4),i=D(0),t=D(8),y=D(9);function r(){s.call(this),this.useSmartIdealEdgeLengthCalculation=o.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.gravityConstant=o.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=o.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=o.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=o.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=o.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=o.MAX_ITERATIONS}r.prototype=Object.create(s.prototype);for(var d in s)r[d]=s[d];r.prototype.initParameters=function(){s.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=o.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},r.prototype.calcIdealEdgeLengths=function(){for(var e,a,l,u,c,T,h,A=this.getGraphManager().getAllEdges(),N=0;N<A.length;N++)e=A[N],a=e.idealLength,e.isInterGraph&&(u=e.getSource(),c=e.getTarget(),T=e.getSourceInLca().getEstimatedSize(),h=e.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(e.idealLength+=T+h-2*i.SIMPLE_NODE_SIZE),l=e.getLca().getInclusionTreeDepth(),e.idealLength+=a*o.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(u.getInclusionTreeDepth()+c.getInclusionTreeDepth()-2*l))},r.prototype.initSpringEmbedder=function(){var e=this.getAllNodes().length;this.incremental?(e>o.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*o.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(e-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-o.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT_INCREMENTAL):(e>o.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(o.COOLING_ADAPTATION_FACTOR,1-(e-o.ADAPTATION_LOWER_NODE_LIMIT)/(o.ADAPTATION_UPPER_NODE_LIMIT-o.ADAPTATION_LOWER_NODE_LIMIT)*(1-o.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=o.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.displacementThresholdPerNode=3*o.DEFAULT_EDGE_LENGTH/100,this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},r.prototype.calcSpringForces=function(){for(var e=this.getAllEdges(),a,l=0;l<e.length;l++)a=e[l],this.calcSpringForce(a,a.idealLength)},r.prototype.calcRepulsionForces=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l,u,c,T,h=this.getAllNodes(),A;if(this.useFRGridVariant)for(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&e&&this.updateGrid(),A=new Set,l=0;l<h.length;l++)c=h[l],this.calculateRepulsionForceOfANode(c,A,e,a),A.add(c);else for(l=0;l<h.length;l++)for(c=h[l],u=l+1;u<h.length;u++)T=h[u],c.getOwner()==T.getOwner()&&this.calcRepulsionForce(c,T)},r.prototype.calcGravitationalForces=function(){for(var e,a=this.getAllNodesToApplyGravitation(),l=0;l<a.length;l++)e=a[l],this.calcGravitationalForce(e)},r.prototype.moveNodes=function(){for(var e=this.getAllNodes(),a,l=0;l<e.length;l++)a=e[l],a.move()},r.prototype.calcSpringForce=function(e,a){var l=e.getSource(),u=e.getTarget(),c,T,h,A;if(this.uniformLeafNodeSizes&&l.getChild()==null&&u.getChild()==null)e.updateLengthSimple();else if(e.updateLength(),e.isOverlapingSourceAndTarget)return;c=e.getLength(),c!=0&&(T=e.edgeElasticity*(c-a),h=T*(e.lengthX/c),A=T*(e.lengthY/c),l.springForceX+=h,l.springForceY+=A,u.springForceX-=h,u.springForceY-=A)},r.prototype.calcRepulsionForce=function(e,a){var l=e.getRect(),u=a.getRect(),c=new Array(2),T=new Array(4),h,A,N,L,x,_,I;if(l.intersects(u)){t.calcSeparationAmount(l,u,c,o.DEFAULT_EDGE_LENGTH/2),_=2*c[0],I=2*c[1];var nt=e.noOfChildren*a.noOfChildren/(e.noOfChildren+a.noOfChildren);e.repulsionForceX-=nt*_,e.repulsionForceY-=nt*I,a.repulsionForceX+=nt*_,a.repulsionForceY+=nt*I}else this.uniformLeafNodeSizes&&e.getChild()==null&&a.getChild()==null?(h=u.getCenterX()-l.getCenterX(),A=u.getCenterY()-l.getCenterY()):(t.getIntersection(l,u,T),h=T[2]-T[0],A=T[3]-T[1]),Math.abs(h)<o.MIN_REPULSION_DIST&&(h=y.sign(h)*o.MIN_REPULSION_DIST),Math.abs(A)<o.MIN_REPULSION_DIST&&(A=y.sign(A)*o.MIN_REPULSION_DIST),N=h*h+A*A,L=Math.sqrt(N),x=(e.nodeRepulsion/2+a.nodeRepulsion/2)*e.noOfChildren*a.noOfChildren/N,_=x*h/L,I=x*A/L,e.repulsionForceX-=_,e.repulsionForceY-=I,a.repulsionForceX+=_,a.repulsionForceY+=I},r.prototype.calcGravitationalForce=function(e){var a,l,u,c,T,h,A,N;a=e.getOwner(),l=(a.getRight()+a.getLeft())/2,u=(a.getTop()+a.getBottom())/2,c=e.getCenterX()-l,T=e.getCenterY()-u,h=Math.abs(c)+e.getWidth()/2,A=Math.abs(T)+e.getHeight()/2,e.getOwner()==this.graphManager.getRoot()?(N=a.getEstimatedSize()*this.gravityRangeFactor,(h>N||A>N)&&(e.gravitationForceX=-this.gravityConstant*c,e.gravitationForceY=-this.gravityConstant*T)):(N=a.getEstimatedSize()*this.compoundGravityRangeFactor,(h>N||A>N)&&(e.gravitationForceX=-this.gravityConstant*c*this.compoundGravityConstant,e.gravitationForceY=-this.gravityConstant*T*this.compoundGravityConstant))},r.prototype.isConverged=function(){var e,a=!1;return this.totalIterations>this.maxIterations/3&&(a=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),e=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,e||a},r.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},r.prototype.calcNoOfChildrenForAllNodes=function(){for(var e,a=this.graphManager.getAllNodes(),l=0;l<a.length;l++)e=a[l],e.noOfChildren=e.getNoOfChildren()},r.prototype.calcGrid=function(e){var a=0,l=0;a=parseInt(Math.ceil((e.getRight()-e.getLeft())/this.repulsionRange)),l=parseInt(Math.ceil((e.getBottom()-e.getTop())/this.repulsionRange));for(var u=new Array(a),c=0;c<a;c++)u[c]=new Array(l);for(var c=0;c<a;c++)for(var T=0;T<l;T++)u[c][T]=new Array;return u},r.prototype.addNodeToGrid=function(e,a,l){var u=0,c=0,T=0,h=0;u=parseInt(Math.floor((e.getRect().x-a)/this.repulsionRange)),c=parseInt(Math.floor((e.getRect().width+e.getRect().x-a)/this.repulsionRange)),T=parseInt(Math.floor((e.getRect().y-l)/this.repulsionRange)),h=parseInt(Math.floor((e.getRect().height+e.getRect().y-l)/this.repulsionRange));for(var A=u;A<=c;A++)for(var N=T;N<=h;N++)this.grid[A][N].push(e),e.setGridCoordinates(u,c,T,h)},r.prototype.updateGrid=function(){var e,a,l=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),e=0;e<l.length;e++)a=l[e],this.addNodeToGrid(a,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},r.prototype.calculateRepulsionForceOfANode=function(e,a,l,u){if(this.totalIterations%o.GRID_CALCULATION_CHECK_PERIOD==1&&l||u){var c=new Set;e.surrounding=new Array;for(var T,h=this.grid,A=e.startX-1;A<e.finishX+2;A++)for(var N=e.startY-1;N<e.finishY+2;N++)if(!(A<0||N<0||A>=h.length||N>=h[0].length)){for(var L=0;L<h[A][N].length;L++)if(T=h[A][N][L],!(e.getOwner()!=T.getOwner()||e==T)&&!a.has(T)&&!c.has(T)){var x=Math.abs(e.getCenterX()-T.getCenterX())-(e.getWidth()/2+T.getWidth()/2),_=Math.abs(e.getCenterY()-T.getCenterY())-(e.getHeight()/2+T.getHeight()/2);x<=this.repulsionRange&&_<=this.repulsionRange&&c.add(T)}}e.surrounding=[].concat(E(c))}for(A=0;A<e.surrounding.length;A++)this.calcRepulsionForce(e,e.surrounding[A])},r.prototype.calcRepulsionRange=function(){return 0},it.exports=r},function(it,st,D){"use strict";var E=D(1),s=D(4);function o(t,y,r){E.call(this,t,y,r),this.idealLength=s.DEFAULT_EDGE_LENGTH,this.edgeElasticity=s.DEFAULT_SPRING_STRENGTH}o.prototype=Object.create(E.prototype);for(var i in E)o[i]=E[i];it.exports=o},function(it,st,D){"use strict";var E=D(3),s=D(4);function o(t,y,r,d){E.call(this,t,y,r,d),this.nodeRepulsion=s.DEFAULT_REPULSION_STRENGTH,this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}o.prototype=Object.create(E.prototype);for(var i in E)o[i]=E[i];o.prototype.setGridCoordinates=function(t,y,r,d){this.startX=t,this.finishX=y,this.startY=r,this.finishY=d},it.exports=o},function(it,st,D){"use strict";function E(s,o){this.width=0,this.height=0,s!==null&&o!==null&&(this.height=o,this.width=s)}E.prototype.getWidth=function(){return this.width},E.prototype.setWidth=function(s){this.width=s},E.prototype.getHeight=function(){return this.height},E.prototype.setHeight=function(s){this.height=s},it.exports=E},function(it,st,D){"use strict";var E=D(14);function s(){this.map={},this.keys=[]}s.prototype.put=function(o,i){var t=E.createID(o);this.contains(t)||(this.map[t]=i,this.keys.push(o))},s.prototype.contains=function(o){var i=E.createID(o);return this.map[o]!=null},s.prototype.get=function(o){var i=E.createID(o);return this.map[i]},s.prototype.keySet=function(){return this.keys},it.exports=s},function(it,st,D){"use strict";var E=D(14);function s(){this.set={}}s.prototype.add=function(o){var i=E.createID(o);this.contains(i)||(this.set[i]=o)},s.prototype.remove=function(o){delete this.set[E.createID(o)]},s.prototype.clear=function(){this.set={}},s.prototype.contains=function(o){return this.set[E.createID(o)]==o},s.prototype.isEmpty=function(){return this.size()===0},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAllTo=function(o){for(var i=Object.keys(this.set),t=i.length,y=0;y<t;y++)o.push(this.set[i[y]])},s.prototype.size=function(){return Object.keys(this.set).length},s.prototype.addAll=function(o){for(var i=o.length,t=0;t<i;t++){var y=o[t];this.add(y)}},it.exports=s},function(it,st,D){"use strict";function E(){}E.multMat=function(s,o){for(var i=[],t=0;t<s.length;t++){i[t]=[];for(var y=0;y<o[0].length;y++){i[t][y]=0;for(var r=0;r<s[0].length;r++)i[t][y]+=s[t][r]*o[r][y]}}return i},E.transpose=function(s){for(var o=[],i=0;i<s[0].length;i++){o[i]=[];for(var t=0;t<s.length;t++)o[i][t]=s[t][i]}return o},E.multCons=function(s,o){for(var i=[],t=0;t<s.length;t++)i[t]=s[t]*o;return i},E.minusOp=function(s,o){for(var i=[],t=0;t<s.length;t++)i[t]=s[t]-o[t];return i},E.dotProduct=function(s,o){for(var i=0,t=0;t<s.length;t++)i+=s[t]*o[t];return i},E.mag=function(s){return Math.sqrt(this.dotProduct(s,s))},E.normalize=function(s){for(var o=[],i=this.mag(s),t=0;t<s.length;t++)o[t]=s[t]/i;return o},E.multGamma=function(s){for(var o=[],i=0,t=0;t<s.length;t++)i+=s[t];i*=-1/s.length;for(var y=0;y<s.length;y++)o[y]=i+s[y];return o},E.multL=function(s,o,i){for(var t=[],y=[],r=[],d=0;d<o[0].length;d++){for(var e=0,a=0;a<o.length;a++)e+=-.5*o[a][d]*s[a];y[d]=e}for(var l=0;l<i.length;l++){for(var u=0,c=0;c<i.length;c++)u+=i[l][c]*y[c];r[l]=u}for(var T=0;T<o.length;T++){for(var h=0,A=0;A<o[0].length;A++)h+=o[T][A]*r[A];t[T]=h}return t},it.exports=E},function(it,st,D){"use strict";var E=function(){function t(y,r){for(var d=0;d<r.length;d++){var e=r[d];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(y,e.key,e)}}return function(y,r,d){return r&&t(y.prototype,r),d&&t(y,d),y}}();function s(t,y){if(!(t instanceof y))throw new TypeError("Cannot call a class as a function")}var o=D(11),i=function(){function t(y,r){s(this,t),(r!==null||r!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var d=void 0;y instanceof o?d=y.size():d=y.length,this._quicksort(y,0,d-1)}return E(t,[{key:"_quicksort",value:function(r,d,e){if(d<e){var a=this._partition(r,d,e);this._quicksort(r,d,a),this._quicksort(r,a+1,e)}}},{key:"_partition",value:function(r,d,e){for(var a=this._get(r,d),l=d,u=e;;){for(;this.compareFunction(a,this._get(r,u));)u--;for(;this.compareFunction(this._get(r,l),a);)l++;if(l<u)this._swap(r,l,u),l++,u--;else return u}}},{key:"_get",value:function(r,d){return r instanceof o?r.get_object_at(d):r[d]}},{key:"_set",value:function(r,d,e){r instanceof o?r.set_object_at(d,e):r[d]=e}},{key:"_swap",value:function(r,d,e){var a=this._get(r,d);this._set(r,d,this._get(r,e)),this._set(r,e,a)}},{key:"_defaultCompareFunction",value:function(r,d){return d>r}}]),t}();it.exports=i},function(it,st,D){"use strict";function E(){}E.svd=function(s){this.U=null,this.V=null,this.s=null,this.m=0,this.n=0,this.m=s.length,this.n=s[0].length;var o=Math.min(this.m,this.n);this.s=function(ct){for(var yt=[];ct-- >0;)yt.push(0);return yt}(Math.min(this.m+1,this.n)),this.U=function(ct){var yt=function Rt(Tt){if(Tt.length==0)return 0;for(var Dt=[],wt=0;wt<Tt[0];wt++)Dt.push(Rt(Tt.slice(1)));return Dt};return yt(ct)}([this.m,o]),this.V=function(ct){var yt=function Rt(Tt){if(Tt.length==0)return 0;for(var Dt=[],wt=0;wt<Tt[0];wt++)Dt.push(Rt(Tt.slice(1)));return Dt};return yt(ct)}([this.n,this.n]);for(var i=function(ct){for(var yt=[];ct-- >0;)yt.push(0);return yt}(this.n),t=function(ct){for(var yt=[];ct-- >0;)yt.push(0);return yt}(this.m),y=!0,r=!0,d=Math.min(this.m-1,this.n),e=Math.max(0,Math.min(this.n-2,this.m)),a=0;a<Math.max(d,e);a++){if(a<d){this.s[a]=0;for(var l=a;l<this.m;l++)this.s[a]=E.hypot(this.s[a],s[l][a]);if(this.s[a]!==0){s[a][a]<0&&(this.s[a]=-this.s[a]);for(var u=a;u<this.m;u++)s[u][a]/=this.s[a];s[a][a]+=1}this.s[a]=-this.s[a]}for(var c=a+1;c<this.n;c++){if(function(ct,yt){return ct&&yt}(a<d,this.s[a]!==0)){for(var T=0,h=a;h<this.m;h++)T+=s[h][a]*s[h][c];T=-T/s[a][a];for(var A=a;A<this.m;A++)s[A][c]+=T*s[A][a]}i[c]=s[a][c]}if(function(ct,yt){return ct&&yt}(y,a<d))for(var N=a;N<this.m;N++)this.U[N][a]=s[N][a];if(a<e){i[a]=0;for(var L=a+1;L<this.n;L++)i[a]=E.hypot(i[a],i[L]);if(i[a]!==0){i[a+1]<0&&(i[a]=-i[a]);for(var x=a+1;x<this.n;x++)i[x]/=i[a];i[a+1]+=1}if(i[a]=-i[a],function(ct,yt){return ct&&yt}(a+1<this.m,i[a]!==0)){for(var _=a+1;_<this.m;_++)t[_]=0;for(var I=a+1;I<this.n;I++)for(var nt=a+1;nt<this.m;nt++)t[nt]+=i[I]*s[nt][I];for(var dt=a+1;dt<this.n;dt++)for(var ut=-i[dt]/i[a+1],Nt=a+1;Nt<this.m;Nt++)s[Nt][dt]+=ut*t[Nt]}if(r)for(var U=a+1;U<this.n;U++)this.V[U][a]=i[U]}}var Z=Math.min(this.n,this.m+1);if(d<this.n&&(this.s[d]=s[d][d]),this.m<Z&&(this.s[Z-1]=0),e+1<Z&&(i[e]=s[e][Z-1]),i[Z-1]=0,y){for(var n=d;n<o;n++){for(var v=0;v<this.m;v++)this.U[v][n]=0;this.U[n][n]=1}for(var f=d-1;f>=0;f--)if(this.s[f]!==0){for(var m=f+1;m<o;m++){for(var p=0,R=f;R<this.m;R++)p+=this.U[R][f]*this.U[R][m];p=-p/this.U[f][f];for(var O=f;O<this.m;O++)this.U[O][m]+=p*this.U[O][f]}for(var H=f;H<this.m;H++)this.U[H][f]=-this.U[H][f];this.U[f][f]=1+this.U[f][f];for(var X=0;X<f-1;X++)this.U[X][f]=0}else{for(var Y=0;Y<this.m;Y++)this.U[Y][f]=0;this.U[f][f]=1}}if(r)for(var rt=this.n-1;rt>=0;rt--){if(function(ct,yt){return ct&&yt}(rt<e,i[rt]!==0))for(var ht=rt+1;ht<o;ht++){for(var z=0,lt=rt+1;lt<this.n;lt++)z+=this.V[lt][rt]*this.V[lt][ht];z=-z/this.V[rt+1][rt];for(var tt=rt+1;tt<this.n;tt++)this.V[tt][ht]+=z*this.V[tt][rt]}for(var w=0;w<this.n;w++)this.V[w][rt]=0;this.V[rt][rt]=1}for(var B=Z-1,k=0,J=Math.pow(2,-52),Et=Math.pow(2,-966);Z>0;){var at=void 0,bt=void 0;for(at=Z-2;at>=-1&&at!==-1;at--)if(Math.abs(i[at])<=Et+J*(Math.abs(this.s[at])+Math.abs(this.s[at+1]))){i[at]=0;break}if(at===Z-2)bt=4;else{var At=void 0;for(At=Z-1;At>=at&&At!==at;At--){var Vt=(At!==Z?Math.abs(i[At]):0)+(At!==at+1?Math.abs(i[At-1]):0);if(Math.abs(this.s[At])<=Et+J*Vt){this.s[At]=0;break}}At===at?bt=3:At===Z-1?bt=1:(bt=2,at=At)}switch(at++,bt){case 1:{var Gt=i[Z-2];i[Z-2]=0;for(var pt=Z-2;pt>=at;pt--){var gt=E.hypot(this.s[pt],Gt),It=this.s[pt]/gt,xt=Gt/gt;if(this.s[pt]=gt,pt!==at&&(Gt=-xt*i[pt-1],i[pt-1]=It*i[pt-1]),r)for(var Mt=0;Mt<this.n;Mt++)gt=It*this.V[Mt][pt]+xt*this.V[Mt][Z-1],this.V[Mt][Z-1]=-xt*this.V[Mt][pt]+It*this.V[Mt][Z-1],this.V[Mt][pt]=gt}}break;case 2:{var Ot=i[at-1];i[at-1]=0;for(var Ct=at;Ct<Z;Ct++){var St=E.hypot(this.s[Ct],Ot),zt=this.s[Ct]/St,Qt=Ot/St;if(this.s[Ct]=St,Ot=-Qt*i[Ct],i[Ct]=zt*i[Ct],y)for(var Pt=0;Pt<this.m;Pt++)St=zt*this.U[Pt][Ct]+Qt*this.U[Pt][at-1],this.U[Pt][at-1]=-Qt*this.U[Pt][Ct]+zt*this.U[Pt][at-1],this.U[Pt][Ct]=St}}break;case 3:{var Wt=Math.max(Math.max(Math.max(Math.max(Math.abs(this.s[Z-1]),Math.abs(this.s[Z-2])),Math.abs(i[Z-2])),Math.abs(this.s[at])),Math.abs(i[at])),Ht=this.s[Z-1]/Wt,$=this.s[Z-2]/Wt,q=i[Z-2]/Wt,g=this.s[at]/Wt,M=i[at]/Wt,C=(($+Ht)*($-Ht)+q*q)/2,S=Ht*q*(Ht*q),W=0;(function(ct,yt){return ct||yt})(C!==0,S!==0)&&(W=Math.sqrt(C*C+S),C<0&&(W=-W),W=S/(C+W));for(var j=(g+Ht)*(g-Ht)+W,F=g*M,P=at;P<Z-1;P++){var K=E.hypot(j,F),G=j/K,V=F/K;if(P!==at&&(i[P-1]=K),j=G*this.s[P]+V*i[P],i[P]=G*i[P]-V*this.s[P],F=V*this.s[P+1],this.s[P+1]=G*this.s[P+1],r)for(var vt=0;vt<this.n;vt++)K=G*this.V[vt][P]+V*this.V[vt][P+1],this.V[vt][P+1]=-V*this.V[vt][P]+G*this.V[vt][P+1],this.V[vt][P]=K;if(K=E.hypot(j,F),G=j/K,V=F/K,this.s[P]=K,j=G*i[P]+V*this.s[P+1],this.s[P+1]=-V*i[P]+G*this.s[P+1],F=V*i[P+1],i[P+1]=G*i[P+1],y&&P<this.m-1)for(var et=0;et<this.m;et++)K=G*this.U[et][P]+V*this.U[et][P+1],this.U[et][P+1]=-V*this.U[et][P]+G*this.U[et][P+1],this.U[et][P]=K}i[Z-2]=j,k=k+1}break;case 4:{if(this.s[at]<=0&&(this.s[at]=this.s[at]<0?-this.s[at]:0,r))for(var b=0;b<=B;b++)this.V[b][at]=-this.V[b][at];for(;at<B&&!(this.s[at]>=this.s[at+1]);){var ft=this.s[at];if(this.s[at]=this.s[at+1],this.s[at+1]=ft,r&&at<this.n-1)for(var Q=0;Q<this.n;Q++)ft=this.V[Q][at+1],this.V[Q][at+1]=this.V[Q][at],this.V[Q][at]=ft;if(y&&at<this.m-1)for(var ot=0;ot<this.m;ot++)ft=this.U[ot][at+1],this.U[ot][at+1]=this.U[ot][at],this.U[ot][at]=ft;at++}k=0,Z--}break}}var mt={U:this.U,V:this.V,S:this.s};return mt},E.hypot=function(s,o){var i=void 0;return Math.abs(s)>Math.abs(o)?(i=o/s,i=Math.abs(s)*Math.sqrt(1+i*i)):o!=0?(i=s/o,i=Math.abs(o)*Math.sqrt(1+i*i)):i=0,i},it.exports=E},function(it,st,D){"use strict";var E=function(){function i(t,y){for(var r=0;r<y.length;r++){var d=y[r];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(t,d.key,d)}}return function(t,y,r){return y&&i(t.prototype,y),r&&i(t,r),t}}();function s(i,t){if(!(i instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function i(t,y){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,d=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,e=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;s(this,i),this.sequence1=t,this.sequence2=y,this.match_score=r,this.mismatch_penalty=d,this.gap_penalty=e,this.iMax=t.length+1,this.jMax=y.length+1,this.grid=new Array(this.iMax);for(var a=0;a<this.iMax;a++){this.grid[a]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.grid[a][l]=0}this.tracebackGrid=new Array(this.iMax);for(var u=0;u<this.iMax;u++){this.tracebackGrid[u]=new Array(this.jMax);for(var c=0;c<this.jMax;c++)this.tracebackGrid[u][c]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return E(i,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var y=1;y<this.jMax;y++)this.grid[0][y]=this.grid[0][y-1]+this.gap_penalty,this.tracebackGrid[0][y]=[!1,!1,!0];for(var r=1;r<this.iMax;r++)this.grid[r][0]=this.grid[r-1][0]+this.gap_penalty,this.tracebackGrid[r][0]=[!1,!0,!1];for(var d=1;d<this.iMax;d++)for(var e=1;e<this.jMax;e++){var a=void 0;this.sequence1[d-1]===this.sequence2[e-1]?a=this.grid[d-1][e-1]+this.match_score:a=this.grid[d-1][e-1]+this.mismatch_penalty;var l=this.grid[d-1][e]+this.gap_penalty,u=this.grid[d][e-1]+this.gap_penalty,c=[a,l,u],T=this.arrayAllMaxIndexes(c);this.grid[d][e]=c[T[0]],this.tracebackGrid[d][e]=[T.includes(0),T.includes(1),T.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var y=[];for(y.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});y[0];){var r=y[0],d=this.tracebackGrid[r.pos[0]][r.pos[1]];d[0]&&y.push({pos:[r.pos[0]-1,r.pos[1]-1],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2}),d[1]&&y.push({pos:[r.pos[0]-1,r.pos[1]],seq1:this.sequence1[r.pos[0]-1]+r.seq1,seq2:"-"+r.seq2}),d[2]&&y.push({pos:[r.pos[0],r.pos[1]-1],seq1:"-"+r.seq1,seq2:this.sequence2[r.pos[1]-1]+r.seq2}),r.pos[0]===0&&r.pos[1]===0&&this.alignments.push({sequence1:r.seq1,sequence2:r.seq2}),y.shift()}return this.alignments}},{key:"getAllIndexes",value:function(y,r){for(var d=[],e=-1;(e=y.indexOf(r,e+1))!==-1;)d.push(e);return d}},{key:"arrayAllMaxIndexes",value:function(y){return this.getAllIndexes(y,Math.max.apply(null,y))}}]),i}();it.exports=o},function(it,st,D){"use strict";var E=function(){};E.FDLayout=D(18),E.FDLayoutConstants=D(4),E.FDLayoutEdge=D(19),E.FDLayoutNode=D(20),E.DimensionD=D(21),E.HashMap=D(22),E.HashSet=D(23),E.IGeometry=D(8),E.IMath=D(9),E.Integer=D(10),E.Point=D(12),E.PointD=D(5),E.RandomSeed=D(16),E.RectangleD=D(13),E.Transform=D(17),E.UniqueIDGeneretor=D(14),E.Quicksort=D(25),E.LinkedList=D(11),E.LGraphObject=D(2),E.LGraph=D(6),E.LEdge=D(1),E.LGraphManager=D(7),E.LNode=D(3),E.Layout=D(15),E.LayoutConstants=D(0),E.NeedlemanWunsch=D(27),E.Matrix=D(24),E.SVD=D(26),it.exports=E},function(it,st,D){"use strict";function E(){this.listeners=[]}var s=E.prototype;s.addListener=function(o,i){this.listeners.push({event:o,callback:i})},s.removeListener=function(o,i){for(var t=this.listeners.length;t>=0;t--){var y=this.listeners[t];y.event===o&&y.callback===i&&this.listeners.splice(t,1)}},s.emit=function(o,i){for(var t=0;t<this.listeners.length;t++){var y=this.listeners[t];o===y.event&&y.callback(i)}},it.exports=E}])})},87534:function(Xt,it,st){"use strict";st.d(it,{diagram:function(){return q}});var D=st(70919),E=st(96547),s=st(78605),o=st(50854),i=st(44133),t=st(29134),y=st(38663),r=st(73058),d=st(28514),e=st(69471),a={L:"left",R:"right",T:"top",B:"bottom"},l={L:(0,t.eW)(g=>`${g},${g/2} 0,${g} 0,0`,"L"),R:(0,t.eW)(g=>`0,${g/2} ${g},0 ${g},${g}`,"R"),T:(0,t.eW)(g=>`0,0 ${g},0 ${g/2},${g}`,"T"),B:(0,t.eW)(g=>`${g/2},0 ${g},${g} 0,${g}`,"B")},u={L:(0,t.eW)((g,M)=>g-M+2,"L"),R:(0,t.eW)((g,M)=>g-2,"R"),T:(0,t.eW)((g,M)=>g-M+2,"T"),B:(0,t.eW)((g,M)=>g-2,"B")},c=(0,t.eW)(function(g){return h(g)?g==="L"?"R":"L":g==="T"?"B":"T"},"getOppositeArchitectureDirection"),T=(0,t.eW)(function(g){const M=g;return M==="L"||M==="R"||M==="T"||M==="B"},"isArchitectureDirection"),h=(0,t.eW)(function(g){const M=g;return M==="L"||M==="R"},"isArchitectureDirectionX"),A=(0,t.eW)(function(g){const M=g;return M==="T"||M==="B"},"isArchitectureDirectionY"),N=(0,t.eW)(function(g,M){const C=h(g)&&A(M),S=A(g)&&h(M);return C||S},"isArchitectureDirectionXY"),L=(0,t.eW)(function(g){const M=g[0],C=g[1],S=h(M)&&A(C),W=A(M)&&h(C);return S||W},"isArchitecturePairXY"),x=(0,t.eW)(function(g){return g!=="LL"&&g!=="RR"&&g!=="TT"&&g!=="BB"},"isValidArchitectureDirectionPair"),_=(0,t.eW)(function(g,M){const C=`${g}${M}`;return x(C)?C:void 0},"getArchitectureDirectionPair"),I=(0,t.eW)(function([g,M],C){const S=C[0],W=C[1];return h(S)?A(W)?[g+(S==="L"?-1:1),M+(W==="T"?1:-1)]:[g+(S==="L"?-1:1),M]:h(W)?[g+(W==="L"?1:-1),M+(S==="T"?1:-1)]:[g,M+(S==="T"?1:-1)]},"shiftPositionByArchitectureDirectionPair"),nt=(0,t.eW)(function(g){return g==="LT"||g==="TL"?[1,1]:g==="BL"||g==="LB"?[1,-1]:g==="BR"||g==="RB"?[-1,-1]:[-1,1]},"getArchitectureDirectionXYFactors"),dt=(0,t.eW)(function(g,M){return N(g,M)?"bend":h(g)?"horizontal":"vertical"},"getArchitectureDirectionAlignment"),ut=(0,t.eW)(function(g){return g.type==="service"},"isArchitectureService"),Nt=(0,t.eW)(function(g){return g.type==="junction"},"isArchitectureJunction"),U=(0,t.eW)(g=>g.data(),"edgeData"),Z=(0,t.eW)(g=>g.data(),"nodeData"),n=t.vZ.architecture,v=new s.A(()=>({nodes:{},groups:{},edges:[],registeredIds:{},config:n,dataStructures:void 0,elements:{}})),f=(0,t.eW)(()=>{v.reset(),(0,t.ZH)()},"clear"),m=(0,t.eW)(function({id:g,icon:M,in:C,title:S,iconText:W}){if(v.records.registeredIds[g]!==void 0)throw new Error(`The service id [${g}] is already in use by another ${v.records.registeredIds[g]}`);if(C!==void 0){if(g===C)throw new Error(`The service [${g}] cannot be placed within itself`);if(v.records.registeredIds[C]===void 0)throw new Error(`The service [${g}]'s parent does not exist. Please make sure the parent is created before this service`);if(v.records.registeredIds[C]==="node")throw new Error(`The service [${g}]'s parent is not a group`)}v.records.registeredIds[g]="node",v.records.nodes[g]={id:g,type:"service",icon:M,iconText:W,title:S,edges:[],in:C}},"addService"),p=(0,t.eW)(()=>Object.values(v.records.nodes).filter(ut),"getServices"),R=(0,t.eW)(function({id:g,in:M}){v.records.registeredIds[g]="node",v.records.nodes[g]={id:g,type:"junction",edges:[],in:M}},"addJunction"),O=(0,t.eW)(()=>Object.values(v.records.nodes).filter(Nt),"getJunctions"),H=(0,t.eW)(()=>Object.values(v.records.nodes),"getNodes"),X=(0,t.eW)(g=>v.records.nodes[g],"getNode"),Y=(0,t.eW)(function({id:g,icon:M,in:C,title:S}){if(v.records.registeredIds[g]!==void 0)throw new Error(`The group id [${g}] is already in use by another ${v.records.registeredIds[g]}`);if(C!==void 0){if(g===C)throw new Error(`The group [${g}] cannot be placed within itself`);if(v.records.registeredIds[C]===void 0)throw new Error(`The group [${g}]'s parent does not exist. Please make sure the parent is created before this group`);if(v.records.registeredIds[C]==="node")throw new Error(`The group [${g}]'s parent is not a group`)}v.records.registeredIds[g]="group",v.records.groups[g]={id:g,icon:M,title:S,in:C}},"addGroup"),rt=(0,t.eW)(()=>Object.values(v.records.groups),"getGroups"),ht=(0,t.eW)(function({lhsId:g,rhsId:M,lhsDir:C,rhsDir:S,lhsInto:W,rhsInto:j,lhsGroup:F,rhsGroup:P,title:K}){if(!T(C))throw new Error(`Invalid direction given for left hand side of edge ${g}--${M}. Expected (L,R,T,B) got ${C}`);if(!T(S))throw new Error(`Invalid direction given for right hand side of edge ${g}--${M}. Expected (L,R,T,B) got ${S}`);if(v.records.nodes[g]===void 0&&v.records.groups[g]===void 0)throw new Error(`The left-hand id [${g}] does not yet exist. Please create the service/group before declaring an edge to it.`);if(v.records.nodes[M]===void 0&&v.records.groups[g]===void 0)throw new Error(`The right-hand id [${M}] does not yet exist. Please create the service/group before declaring an edge to it.`);const G=v.records.nodes[g].in,V=v.records.nodes[M].in;if(F&&G&&V&&G==V)throw new Error(`The left-hand id [${g}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);if(P&&G&&V&&G==V)throw new Error(`The right-hand id [${M}] is modified to traverse the group boundary, but the edge does not pass through two groups.`);const vt={lhsId:g,lhsDir:C,lhsInto:W,lhsGroup:F,rhsId:M,rhsDir:S,rhsInto:j,rhsGroup:P,title:K};v.records.edges.push(vt),v.records.nodes[g]&&v.records.nodes[M]&&(v.records.nodes[g].edges.push(v.records.edges[v.records.edges.length-1]),v.records.nodes[M].edges.push(v.records.edges[v.records.edges.length-1]))},"addEdge"),z=(0,t.eW)(()=>v.records.edges,"getEdges"),lt=(0,t.eW)(()=>{if(v.records.dataStructures===void 0){const g={},M=Object.entries(v.records.nodes).reduce((P,[K,G])=>(P[K]=G.edges.reduce((V,vt)=>{var ft,Q,ot,mt;const et=(ft=X(vt.lhsId))==null?void 0:ft.in,b=(Q=X(vt.rhsId))==null?void 0:Q.in;if(et&&b&&et!==b){const ct=dt(vt.lhsDir,vt.rhsDir);ct!=="bend"&&((ot=g[et])!=null||(g[et]={}),g[et][b]=ct,(mt=g[b])!=null||(g[b]={}),g[b][et]=ct)}if(vt.lhsId===K){const ct=_(vt.lhsDir,vt.rhsDir);ct&&(V[ct]=vt.rhsId)}else{const ct=_(vt.rhsDir,vt.lhsDir);ct&&(V[ct]=vt.lhsId)}return V},{}),P),{}),C=Object.keys(M)[0],S={[C]:1},W=Object.keys(M).reduce((P,K)=>K===C?P:ne(ie({},P),{[K]:1}),{}),j=(0,t.eW)(P=>{const K={[P]:[0,0]},G=[P];for(;G.length>0;){const V=G.shift();if(V){S[V]=1,delete W[V];const vt=M[V],[et,b]=K[V];Object.entries(vt).forEach(([ft,Q])=>{S[Q]||(K[Q]=I([et,b],ft),G.push(Q))})}}return K},"BFS"),F=[j(C)];for(;Object.keys(W).length>0;)F.push(j(Object.keys(W)[0]));v.records.dataStructures={adjList:M,spatialMaps:F,groupAlignments:g}}return v.records.dataStructures},"getDataStructures"),tt=(0,t.eW)((g,M)=>{v.records.elements[g]=M},"setElementForId"),w=(0,t.eW)(g=>v.records.elements[g],"getElementById"),B=(0,t.eW)(()=>(0,o.Rb)(ie(ie({},n),(0,t.iE)().architecture)),"getConfig"),k={clear:f,setDiagramTitle:t.g2,getDiagramTitle:t.Kr,setAccTitle:t.GN,getAccTitle:t.eu,setAccDescription:t.U$,getAccDescription:t.Mx,getConfig:B,addService:m,getServices:p,addJunction:R,getJunctions:O,getNodes:H,getNode:X,addGroup:Y,getGroups:rt,addEdge:ht,getEdges:z,setElementForId:tt,getElementById:w,getDataStructures:lt};function J(g){return B()[g]}(0,t.eW)(J,"getConfigField");var Et=(0,t.eW)((g,M)=>{(0,E.A)(g,M),g.groups.map(M.addGroup),g.services.map(C=>M.addService(ne(ie({},C),{type:"service"}))),g.junctions.map(C=>M.addJunction(ne(ie({},C),{type:"junction"}))),g.edges.map(M.addEdge)},"populateDb"),at={parse:(0,t.eW)(g=>ee(this,null,function*(){const M=yield(0,y.Qc)("architecture",g);t.cM.debug(M),Et(M,k)}),"parse")},bt=(0,t.eW)(g=>`
  .edge {
    stroke-width: ${g.archEdgeWidth};
    stroke: ${g.archEdgeColor};
    fill: none;
  }

  .arrow {
    fill: ${g.archEdgeArrowColor};
  }

  .node-bkg {
    fill: none;
    stroke: ${g.archGroupBorderColor};
    stroke-width: ${g.archGroupBorderWidth};
    stroke-dasharray: 8;
  }
  .node-icon-text {
    display: flex; 
    align-items: center;
  }
  
  .node-icon-text > div {
    color: #fff;
    margin: 1px;
    height: fit-content;
    text-align: center;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
`,"getStyles"),At=bt,Vt=(0,t.eW)(g=>`<g><rect width="80" height="80" style="fill: #087ebf; stroke-width: 0px;"/>${g}</g>`,"wrapIcon"),Gt={prefix:"mermaid-architecture",height:80,width:80,icons:{database:{body:Vt('<path id="b" data-name="4" d="m20,57.86c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="c" data-name="3" d="m20,45.95c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path id="d" data-name="2" d="m20,34.05c0,3.94,8.95,7.14,20,7.14s20-3.2,20-7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse id="e" data-name="1" cx="40" cy="22.14" rx="20" ry="7.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="20" y1="57.86" x2="20" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="60" y1="57.86" x2="60" y2="22.14" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},server:{body:Vt('<rect x="17.5" y="17.5" width="45" height="45" rx="2" ry="2" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="32.5" x2="62.5" y2="32.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="47.5" x2="62.5" y2="47.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><g><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,25c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,40c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: #fff; stroke-width: 0px;"/><path d="m56.25,55c0,.27-.45.5-1,.5h-10.5c-.55,0-1-.23-1-.5s.45-.5,1-.5h10.5c.55,0,1,.23,1,.5Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="25" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="40" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g><g><circle cx="32.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="27.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/><circle cx="22.5" cy="55" r=".75" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10;"/></g>')},disk:{body:Vt('<rect x="20" y="15" width="40" height="50" rx="1" ry="1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="19.17" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="24" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="56" cy="60.83" rx=".8" ry=".83" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="14" ry="14.58" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><ellipse cx="40" cy="33.75" rx="4" ry="4.17" style="fill: #fff; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m37.51,42.52l-4.83,13.22c-.26.71-1.1,1.02-1.76.64l-4.18-2.42c-.66-.38-.81-1.26-.33-1.84l9.01-10.8c.88-1.05,2.56-.08,2.09,1.2Z" style="fill: #fff; stroke-width: 0px;"/>')},internet:{body:Vt('<circle cx="40" cy="40" r="22.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="40" y1="17.5" x2="40" y2="62.5" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="17.5" y1="40" x2="62.5" y2="40" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m39.99,17.51c-15.28,11.1-15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><path d="m40.01,17.51c15.28,11.1,15.28,33.88,0,44.98" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="30.1" x2="60.25" y2="30.1" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/><line x1="19.75" y1="49.9" x2="60.25" y2="49.9" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},cloud:{body:Vt('<path d="m65,47.5c0,2.76-2.24,5-5,5H20c-2.76,0-5-2.24-5-5,0-1.87,1.03-3.51,2.56-4.36-.04-.21-.06-.42-.06-.64,0-2.6,2.48-4.74,5.65-4.97,1.65-4.51,6.34-7.76,11.85-7.76.86,0,1.69.08,2.5.23,2.09-1.57,4.69-2.5,7.5-2.5,6.1,0,11.19,4.38,12.28,10.17,2.14.56,3.72,2.51,3.72,4.83,0,.03,0,.07-.01.1,2.29.46,4.01,2.48,4.01,4.9Z" style="fill: none; stroke: #fff; stroke-miterlimit: 10; stroke-width: 2px;"/>')},unknown:D.cN,blank:{body:Vt("")}}},pt=(0,t.eW)(function(g,M){return ee(this,null,function*(){const C=J("padding"),S=J("iconSize"),W=S/2,j=S/6,F=j/2;yield Promise.all(M.edges().map(P=>ee(this,null,function*(){var Ut,kt;const{source:K,sourceDir:G,sourceArrow:V,sourceGroup:vt,target:et,targetDir:b,targetArrow:ft,targetGroup:Q,label:ot}=U(P);let{x:mt,y:ct}=P[0].sourceEndpoint();const{x:yt,y:Rt}=P[0].midpoint();let{x:Tt,y:Dt}=P[0].targetEndpoint();const wt=C+4;if(vt&&(h(G)?mt+=G==="L"?-wt:wt:ct+=G==="T"?-wt:wt+18),Q&&(h(b)?Tt+=b==="L"?-wt:wt:Dt+=b==="T"?-wt:wt+18),!vt&&((Ut=k.getNode(K))==null?void 0:Ut.type)==="junction"&&(h(G)?mt+=G==="L"?W:-W:ct+=G==="T"?W:-W),!Q&&((kt=k.getNode(et))==null?void 0:kt.type)==="junction"&&(h(b)?Tt+=b==="L"?W:-W:Dt+=b==="T"?W:-W),P[0]._private.rscratch){const $t=g.insert("g");if($t.insert("path").attr("d",`M ${mt},${ct} L ${yt},${Rt} L${Tt},${Dt} `).attr("class","edge"),V){const Yt=h(G)?u[G](mt,j):mt-F,Zt=A(G)?u[G](ct,j):ct-F;$t.insert("polygon").attr("points",l[G](j)).attr("transform",`translate(${Yt},${Zt})`).attr("class","arrow")}if(ft){const Yt=h(b)?u[b](Tt,j):Tt-F,Zt=A(b)?u[b](Dt,j):Dt-F;$t.insert("polygon").attr("points",l[b](j)).attr("transform",`translate(${Yt},${Zt})`).attr("class","arrow")}if(ot){const Yt=N(G,b)?"XY":h(G)?"X":"Y";let Zt=0;Yt==="X"?Zt=Math.abs(mt-Tt):Yt==="Y"?Zt=Math.abs(ct-Dt)/1.5:Zt=Math.abs(mt-Tt)/2;const Kt=$t.append("g");if(yield(0,D.rw)(Kt,ot,{useHtmlLabels:!1,width:Zt,classes:"architecture-service-label"},(0,t.nV)()),Kt.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),Yt==="X")Kt.attr("transform","translate("+yt+", "+Rt+")");else if(Yt==="Y")Kt.attr("transform","translate("+yt+", "+Rt+") rotate(-90)");else if(Yt==="XY"){const qt=_(G,b);if(qt&&L(qt)){const re=Kt.node().getBoundingClientRect(),[jt,_t]=nt(qt);Kt.attr("dominant-baseline","auto").attr("transform",`rotate(${-1*jt*_t*45})`);const ae=Kt.node().getBoundingClientRect();Kt.attr("transform",`
                translate(${yt}, ${Rt-re.height/2})
                translate(${jt*ae.width/2}, ${_t*ae.height/2})
                rotate(${-1*jt*_t*45}, 0, ${re.height/2})
              `)}}}}})))})},"drawEdges"),gt=(0,t.eW)(function(g,M){return ee(this,null,function*(){const S=J("padding")*.75,W=J("fontSize"),F=J("iconSize")/2;yield Promise.all(M.nodes().map(P=>ee(this,null,function*(){const K=Z(P);if(K.type==="group"){const{h:G,w:V,x1:vt,y1:et}=P.boundingBox();g.append("rect").attr("x",vt+F).attr("y",et+F).attr("width",V).attr("height",G).attr("class","node-bkg");const b=g.append("g");let ft=vt,Q=et;if(K.icon){const ot=b.append("g");ot.html(`<g>${yield(0,D.s4)(K.icon,{height:S,width:S,fallbackPrefix:Gt.prefix})}</g>`),ot.attr("transform","translate("+(ft+F+1)+", "+(Q+F+1)+")"),ft+=S,Q+=W/2-1-2}if(K.label){const ot=b.append("g");yield(0,D.rw)(ot,K.label,{useHtmlLabels:!1,width:V,classes:"architecture-service-label"},(0,t.nV)()),ot.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","start").attr("text-anchor","start"),ot.attr("transform","translate("+(ft+F+4)+", "+(Q+F+2)+")")}}})))})},"drawGroups"),It=(0,t.eW)(function(g,M,C){return ee(this,null,function*(){var S;for(const W of C){const j=M.append("g"),F=J("iconSize");if(W.title){const V=j.append("g");yield(0,D.rw)(V,W.title,{useHtmlLabels:!1,width:F*1.5,classes:"architecture-service-label"},(0,t.nV)()),V.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle"),V.attr("transform","translate("+F/2+", "+F+")")}const P=j.append("g");if(W.icon)P.html(`<g>${yield(0,D.s4)(W.icon,{height:F,width:F,fallbackPrefix:Gt.prefix})}</g>`);else if(W.iconText){P.html(`<g>${yield(0,D.s4)("blank",{height:F,width:F,fallbackPrefix:Gt.prefix})}</g>`);const et=P.append("g").append("foreignObject").attr("width",F).attr("height",F).append("div").attr("class","node-icon-text").attr("style",`height: ${F}px;`).append("div").html(W.iconText),b=(S=parseInt(window.getComputedStyle(et.node(),null).getPropertyValue("font-size").replace(/\D/g,"")))!=null?S:16;et.attr("style",`-webkit-line-clamp: ${Math.floor((F-2)/b)};`)}else P.append("path").attr("class","node-bkg").attr("id","node-"+W.id).attr("d",`M0 ${F} v${-F} q0,-5 5,-5 h${F} q5,0 5,5 v${F} H0 Z`);j.attr("class","architecture-service");const{width:K,height:G}=j._groups[0][0].getBBox();W.width=K,W.height=G,g.setElementForId(W.id,j)}return 0})},"drawServices"),xt=(0,t.eW)(function(g,M,C){C.forEach(S=>{const W=M.append("g"),j=J("iconSize");W.append("g").append("rect").attr("id","node-"+S.id).attr("fill-opacity","0").attr("width",j).attr("height",j),W.attr("class","architecture-junction");const{width:P,height:K}=W._groups[0][0].getBBox();W.width=P,W.height=K,g.setElementForId(S.id,W)})},"drawJunctions");(0,D.ef)([{name:Gt.prefix,icons:Gt}]),r.Z.use(d);function Mt(g,M){g.forEach(C=>{M.add({group:"nodes",data:{type:"service",id:C.id,icon:C.icon,label:C.title,parent:C.in,width:J("iconSize"),height:J("iconSize")},classes:"node-service"})})}(0,t.eW)(Mt,"addServices");function Ot(g,M){g.forEach(C=>{M.add({group:"nodes",data:{type:"junction",id:C.id,parent:C.in,width:J("iconSize"),height:J("iconSize")},classes:"node-junction"})})}(0,t.eW)(Ot,"addJunctions");function Ct(g,M){M.nodes().map(C=>{const S=Z(C);if(S.type==="group")return;S.x=C.position().x,S.y=C.position().y,g.getElementById(S.id).attr("transform","translate("+(S.x||0)+","+(S.y||0)+")")})}(0,t.eW)(Ct,"positionNodes");function St(g,M){g.forEach(C=>{M.add({group:"nodes",data:{type:"group",id:C.id,icon:C.icon,label:C.title,parent:C.in},classes:"node-group"})})}(0,t.eW)(St,"addGroups");function zt(g,M){g.forEach(C=>{const{lhsId:S,rhsId:W,lhsInto:j,lhsGroup:F,rhsInto:P,lhsDir:K,rhsDir:G,rhsGroup:V,title:vt}=C,et=N(C.lhsDir,C.rhsDir)?"segments":"straight",b={id:`${S}-${W}`,label:vt,source:S,sourceDir:K,sourceArrow:j,sourceGroup:F,sourceEndpoint:K==="L"?"0 50%":K==="R"?"100% 50%":K==="T"?"50% 0":"50% 100%",target:W,targetDir:G,targetArrow:P,targetGroup:V,targetEndpoint:G==="L"?"0 50%":G==="R"?"100% 50%":G==="T"?"50% 0":"50% 100%"};M.add({group:"edges",data:b,classes:et})})}(0,t.eW)(zt,"addEdges");function Qt(g,M,C){const S=(0,t.eW)((P,K)=>Object.entries(P).reduce((G,[V,vt])=>{var ft,Q,ot;let et=0;const b=Object.entries(vt);if(b.length===1)return G[V]=b[0][1],G;for(let mt=0;mt<b.length-1;mt++)for(let ct=mt+1;ct<b.length;ct++){const[yt,Rt]=b[mt],[Tt,Dt]=b[ct];if(((ft=C[yt])==null?void 0:ft[Tt])===K)(Q=G[V])!=null||(G[V]=[]),G[V]=[...G[V],...Rt,...Dt];else if(yt==="default"||Tt==="default")(ot=G[V])!=null||(G[V]=[]),G[V]=[...G[V],...Rt,...Dt];else{const Ut=`${V}-${et++}`;G[Ut]=Rt;const kt=`${V}-${et++}`;G[kt]=Dt}}return G},{}),"flattenAlignments"),W=M.map(P=>{const K={},G={};return Object.entries(P).forEach(([V,[vt,et]])=>{var ft,Q,ot,mt,ct,yt,Rt,Tt;const b=(Q=(ft=g.getNode(V))==null?void 0:ft.in)!=null?Q:"default";(ot=K[et])!=null||(K[et]={}),(ct=(mt=K[et])[b])!=null||(mt[b]=[]),K[et][b].push(V),(yt=G[vt])!=null||(G[vt]={}),(Tt=(Rt=G[vt])[b])!=null||(Rt[b]=[]),G[vt][b].push(V)}),{horiz:Object.values(S(K,"horizontal")).filter(V=>V.length>1),vert:Object.values(S(G,"vertical")).filter(V=>V.length>1)}}),[j,F]=W.reduce(([P,K],{horiz:G,vert:V})=>[[...P,...G],[...K,...V]],[[],[]]);return{horizontal:j,vertical:F}}(0,t.eW)(Qt,"getAlignments");function Pt(g){const M=[],C=(0,t.eW)(W=>`${W[0]},${W[1]}`,"posToStr"),S=(0,t.eW)(W=>W.split(",").map(j=>parseInt(j)),"strToPos");return g.forEach(W=>{const j=Object.fromEntries(Object.entries(W).map(([G,V])=>[C(V),G])),F=[C([0,0])],P={},K={L:[-1,0],R:[1,0],T:[0,1],B:[0,-1]};for(;F.length>0;){const G=F.shift();if(G){P[G]=1;const V=j[G];if(V){const vt=S(G);Object.entries(K).forEach(([et,b])=>{const ft=C([vt[0]+b[0],vt[1]+b[1]]),Q=j[ft];Q&&!P[ft]&&(F.push(ft),M.push({[a[et]]:Q,[a[c(et)]]:V,gap:1.5*J("iconSize")}))})}}}}),M}(0,t.eW)(Pt,"getRelativeConstraints");function Wt(g,M,C,S,W,{spatialMaps:j,groupAlignments:F}){return new Promise(P=>{const K=(0,e.Ys)("body").append("div").attr("id","cy").attr("style","display:none"),G=(0,r.Z)({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"straight",label:"data(label)","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"edge.segments",style:{"curve-style":"segments","segment-weights":"0","segment-distances":[.5],"edge-distances":"endpoints","source-endpoint":"data(sourceEndpoint)","target-endpoint":"data(targetEndpoint)"}},{selector:"node",style:{"compound-sizing-wrt-labels":"include"}},{selector:"node[label]",style:{"text-valign":"bottom","text-halign":"center","font-size":`${J("fontSize")}px`}},{selector:".node-service",style:{label:"data(label)",width:"data(width)",height:"data(height)"}},{selector:".node-junction",style:{width:"data(width)",height:"data(height)"}},{selector:".node-group",style:{padding:`${J("padding")}px`}}],layout:{name:"grid",boundingBox:{x1:0,x2:100,y1:0,y2:100}}});K.remove(),St(C,G),Mt(g,G),Ot(M,G),zt(S,G);const V=Qt(W,j,F),vt=Pt(j),et=G.layout({name:"fcose",quality:"proof",styleEnabled:!1,animate:!1,nodeDimensionsIncludeLabels:!1,idealEdgeLength(b){const[ft,Q]=b.connectedNodes(),{parent:ot}=Z(ft),{parent:mt}=Z(Q);return ot===mt?1.5*J("iconSize"):.5*J("iconSize")},edgeElasticity(b){const[ft,Q]=b.connectedNodes(),{parent:ot}=Z(ft),{parent:mt}=Z(Q);return ot===mt?.45:.001},alignmentConstraint:V,relativePlacementConstraint:vt});et.one("layoutstop",()=>{var ft;function b(Q,ot,mt,ct){let yt,Rt;const{x:Tt,y:Dt}=Q,{x:wt,y:Ut}=ot;Rt=(ct-Dt+(Tt-mt)*(Dt-Ut)/(Tt-wt))/Math.sqrt(1+Math.pow((Dt-Ut)/(Tt-wt),2)),yt=Math.sqrt(Math.pow(ct-Dt,2)+Math.pow(mt-Tt,2)-Math.pow(Rt,2));const kt=Math.sqrt(Math.pow(wt-Tt,2)+Math.pow(Ut-Dt,2));yt=yt/kt;let $t=(wt-Tt)*(ct-Dt)-(Ut-Dt)*(mt-Tt);switch(!0){case $t>=0:$t=1;break;case $t<0:$t=-1;break}let Yt=(wt-Tt)*(mt-Tt)+(Ut-Dt)*(ct-Dt);switch(!0){case Yt>=0:Yt=1;break;case Yt<0:Yt=-1;break}return Rt=Math.abs(Rt)*$t,yt=yt*Yt,{distances:Rt,weights:yt}}(0,t.eW)(b,"getSegmentWeights"),G.startBatch();for(const Q of Object.values(G.edges()))if((ft=Q.data)!=null&&ft.call(Q)){const{x:ot,y:mt}=Q.source().position(),{x:ct,y:yt}=Q.target().position();if(ot!==ct&&mt!==yt){const Rt=Q.sourceEndpoint(),Tt=Q.targetEndpoint(),{sourceDir:Dt}=U(Q),[wt,Ut]=A(Dt)?[Rt.x,Tt.y]:[Tt.x,Rt.y],{weights:kt,distances:$t}=b(Rt,Tt,wt,Ut);Q.style("segment-distances",$t),Q.style("segment-weights",kt)}}G.endBatch(),et.run()}),et.run(),G.ready(b=>{t.cM.info("Ready",b),P(G)})})}(0,t.eW)(Wt,"layoutArchitecture");var Ht=(0,t.eW)((g,M,C,S)=>ee(this,null,function*(){const W=S.db,j=W.getServices(),F=W.getJunctions(),P=W.getGroups(),K=W.getEdges(),G=W.getDataStructures(),V=(0,i.P)(M),vt=V.append("g");vt.attr("class","architecture-edges");const et=V.append("g");et.attr("class","architecture-services");const b=V.append("g");b.attr("class","architecture-groups"),yield It(W,et,j),xt(W,et,F);const ft=yield Wt(j,F,P,K,W,G);yield pt(vt,ft),yield gt(b,ft),Ct(W,ft),(0,t.j7)(void 0,V,J("padding"),J("useMaxWidth"))}),"draw"),$={draw:Ht},q={parser:at,db:k,renderer:$,styles:At}},96547:function(Xt,it,st){"use strict";st.d(it,{A:function(){return E}});var D=st(29134);function E(s,o){var i,t,y;s.accDescr&&((i=o.setAccDescription)==null||i.call(o,s.accDescr)),s.accTitle&&((t=o.setAccTitle)==null||t.call(o,s.accTitle)),s.title&&((y=o.setDiagramTitle)==null||y.call(o,s.title))}(0,D.eW)(E,"populateCommonDb")},78605:function(Xt,it,st){"use strict";var s;st.d(it,{A:function(){return E}});var D=st(29134),E=(s=class{constructor(i){this.init=i,this.records=this.init()}reset(){this.records=this.init()}},(0,D.eW)(s,"ImperativeState"),s)}}]);
}());