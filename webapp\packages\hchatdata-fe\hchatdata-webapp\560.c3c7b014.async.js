!(function(){"use strict";var E=(i,s,a)=>new Promise((d,n)=>{var t=r=>{try{e(a.next(r))}catch(_){n(_)}},o=r=>{try{e(a.throw(r))}catch(_){n(_)}},e=r=>r.done?d(r.value):Promise.resolve(r.value).then(t,o);e((a=a.apply(i,s)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[560],{50560:function(i,s,a){a.d(s,{diagram:function(){return P}});var d=a(29298),n=a(44133),t=a(29134),o=a(38663),e={parse:(0,t.eW)(v=>E(this,null,function*(){const c=yield(0,o.Qc)("info",v);t.cM.debug(c)}),"parse")},r={version:d.X.version+""},_=(0,t.eW)(()=>r.version,"getVersion"),g={getVersion:_},h=(0,t.eW)((v,c,m)=>{t.cM.debug(`rendering info diagram
`+v);const u=(0,n.P)(c);(0,t.v2)(u,100,400,!0),u.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${m}`)},"draw"),p={draw:h},P={parser:e,db:g,renderer:p}}}]);
}());