"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6433],{51308:function(dt,Te,n){n.d(Te,{Z:function(){return Pe}});var ye=n(10947),D=n(2206),te={title:"title___V3aE9",subTitleContainer:"subTitleContainer___gJqJ1"},s=n(31549),fe=ye.Z.Paragraph,ce=function(W){var Q=W.title,ae=W.subTitle,B=W.subTitleEditable,l=B===void 0?!1:B,_=W.onSubTitleChange;return(0,s.jsxs)(D.Z,{direction:"vertical",size:2,style:{width:"100%"},children:[(0,s.jsx)("div",{className:te.title,children:Q}),(0,s.jsx)("div",{className:te.subTitleContainer,children:l?(0,s.jsx)(fe,{editable:{onChange:function(r){_==null||_(r)}},children:ae||"\u6DFB\u52A0\u63CF\u8FF0"}):ae&&(0,s.jsx)("span",{style:{fontSize:"12px",color:"#7b809a"},children:ae})})]})},Pe=ce},63032:function(dt,Te,n){n.d(Te,{Z:function(){return _}});var ye=n(73193),D=n.n(ye),te=n(10154),s=n.n(te),fe=n(84176),ce=n.n(fe),Pe=n(44194),f=n(51865),W=n.n(f),Q={standardFormRow:"standardFormRow___aOMw5",label:"label____K708",content:"content___V6RWR",standardFormRowLast:"standardFormRowLast___TdGkw",standardFormRowBlock:"standardFormRowBlock___ZJQv5",standardFormRowGrid:"standardFormRowGrid___ggJzD"},ae=n(31549),B=["title","children","last","block","grid","titleClassName"],l=function(r){var o=r.title,at=r.children,$e=r.last,Ie=r.block,Ge=r.grid,Me=r.titleClassName,Ae=ce()(r,B),L=W()(Q.standardFormRow,s()(s()(s()({},Q.standardFormRowBlock,Ie),Q.standardFormRowLast,$e),Q.standardFormRowGrid,Ge)),Xe=W()(Q.label,Me);return(0,ae.jsxs)("div",D()(D()({className:L},Ae),{},{children:[o&&(0,ae.jsx)("div",{className:Xe,children:(0,ae.jsx)("span",{children:o})}),(0,ae.jsx)("div",{className:Q.content,children:at})]}))},_=l},66781:function(dt,Te,n){n.r(Te),n.d(Te,{default:function(){return on}});var ye=n(90819),D=n.n(ye),te=n(73193),s=n.n(te),fe=n(89933),ce=n.n(fe),Pe=n(45332),f=n.n(Pe),W=n(7477),Q=n(8887),ae=n(34284),B=n(2206),l=n(44194),_=n(9113),Ue=n(20221),r=n(64941),o=n(47712),at=n(76711),$e=n.n(at),Ie=n(20263),Ge=n(69367),Me=n(60654),Ae=n(26574),L=n(16156),Xe=n(48893),_e=n(61107),ut=n(37137),gt=n(95457),Ne=n(94202),Ot=n(57285),Oe=n(7982),be=n(39378),Ce=n(84025),T=n.n(Ce),e=n(31549),nt=function(i){var a=i.title,c=i.tip,d=i.data,u=i.fields,m=i.loading,A=i.isPer,j=i.isPercent,h=i.dateFieldName,p=h===void 0?"sys_imp_date":h,M=i.rowNumber,w=M===void 0?0:M,C=i.groupByDimensionFieldName,O=i.dateFormat,U=i.height,x=i.renderType,R=i.decimalPlaces,b=i.onDownload,X=(0,l.useRef)(),E=(0,l.useState)(),z=f()(E,2),q=z[0],ie=z[1],le=(0,l.useCallback)(function(){var $;q?($=q,x==="clear"&&$.clear()):($=Oe.S1(X.current),ie($));var ge=Array.from(new Set(d.map(function(v){return T()("".concat(p&&v[p])).format(O!=null?O:"YYYY-MM-DD")}).sort(function(v,S){return T()(v).valueOf()-T()(S).valueOf()}))),G=function(){if(C){var S=(0,be.groupBy)(d,C),Y=Object.keys(S).map(function(F){var pe=S[F],K=pe.reduce(function(oe,lt){return oe[lt[p]]=s()({},lt),oe},{}),se=ge.reduce(function(oe,lt){var et=K[lt];if(et){var De;oe.push(et[u==null||(De=u[0])===null||De===void 0?void 0:De.column])}else oe.push(0);return oe},[]);return{type:"line",name:F,symbol:"circle",smooth:!0,sortNum:(0,be.sum)(se),data:se}});return w?Y.sort(function(F,pe){return pe.sortNum-F.sortNum}).slice(0,w):Y}var y=u.map(function(F){var pe={type:"line",name:F.name,symbol:"circle",showSymbol:d.length===1,smooth:!0,data:d.reduce(function(K,se){var oe=se[F.column];return oe&&K.push(oe),K},[])};return pe});return y},N=G();$.setOption({legend:{left:0,top:0,icon:"rect",itemWidth:15,itemHeight:5,selected:u.reduce(function(v,S){return S.selected===!1&&(v[S.name]=!1),v},{})},xAxis:{type:"category",axisTick:{alignWithLabel:!0,lineStyle:{color:gt.Xh}},axisLine:{lineStyle:{color:gt.Xh}},axisLabel:{showMaxLabel:!0,color:"#999"},data:ge},yAxis:{type:"value",splitLine:{lineStyle:{opacity:.3}},axisLabel:{formatter:function(S){return S===0?0:A?"".concat((0,Ne.uN)(S,R!=null?R:0),"%"):j?(0,Ne.T2)(S,R!=null?R:0):(0,Ne.M)(S)}}},tooltip:{trigger:"axis",formatter:function(S){var Y=S[0],y=S.map(function(F){return'<div style="margin-top: 3px;">'.concat(F.marker,' <span style="display: inline-block; width: 100px; margin-right: 5px;">').concat(F.seriesName,'</span><span style="display: inline-block; width: 120px; text-align: right; font-weight: 500;">').concat(F.value===""?"-":A?"".concat((0,Ne.uN)(F.value,R!=null?R:2),"%"):j?(0,Ne.T2)(F.value,R!=null?R:2):(0,Ne.M)(F.value),"</span></div>")}).join("");return"".concat(Y.name,"<br />").concat(y)}},grid:{left:"1%",right:"4%",bottom:"3%",top:U&&U<300?45:60,containLabel:!0},series:N}),$.resize()},[d,u,q,A,j,p,R,x,w,C]);return(0,l.useEffect)(function(){m||le()},[le,m,d]),(0,e.jsxs)("div",{className:r.Z.trendChart,children:[a&&(0,e.jsxs)("div",{className:r.Z.top,children:[(0,e.jsx)("div",{className:r.Z.title,children:a}),b&&(0,e.jsx)(L.Z,{title:"\u4E0B\u8F7D",children:(0,e.jsx)(ae.ZP,{shape:"circle",className:r.Z.downloadBtn,onClick:b,children:(0,e.jsx)(_e.Z,{})})})]}),(0,e.jsx)(Ot.Z,{className:r.Z.chart,style:{height:U,display:m?"table":"none"},paragraph:{rows:U&&U>300?10:6}}),(0,e.jsx)("div",{className:r.Z.chart,style:{height:U,display:m?"none":"block"},ref:X})]})},qe=nt,xe=n(17258),He=n(10162),yt=n(84176),Je=n.n(yt),Et=n(58184),At=n(31063),ht=n(77837),rt=n.n(ht),St=n(45563),Ct=n.n(St),pt=["autoInit","fetchOptions","debounceTimeout","formatPropsValue","formatFetchOptionsParams","formatOptions","disabledSearch"],jt=Ae.default.Option,Rt=(0,l.forwardRef)(function(I,i){var a=I.autoInit,c=a===void 0?!1:a,d=I.fetchOptions,u=I.debounceTimeout,m=u===void 0?500:u,A=I.formatPropsValue,j=I.formatFetchOptionsParams,h=I.formatOptions,p=I.disabledSearch,M=p===void 0?!1:p,w=Je()(I,pt),C=s()({},w),O=C.ctx,U=C.filterOption;Ct()(A)&&(C.value=A(C.value));var x=(0,l.useState)(!1),R=f()(x,2),b=R[0],X=R[1],E=(0,l.useState)(C.options||C.source||[]),z=f()(E,2),q=z[0],ie=z[1];(0,l.useImperativeHandle)(i,function(){return{emitSearch:function(N){$(N,!0)}}}),(0,l.useEffect)(function(){c&&$("",!0)},[]),(0,l.useEffect)(function(){ie(C.source||[])},[C.source]);var le=(0,l.useRef)(0),$=function(N,v){if(ie([]),!M&&!(!v&&!N)){le.current+=1;var S=le.current;X(!0);var Y=j?j(N,O):[N];d.apply(null,Y).then(function(y){if(!(S!==le.current||!Array.isArray(y))){var F=y;h&&Ct()(h)&&(F=h(y,O)),F=U&&Array.isArray(F)?U==null?void 0:U(F,O):F,ie(F),X(!1)}})}},ge=(0,l.useMemo)(function(){return rt()($,m,{trailing:!0})},[d,m]);return(0,e.jsx)(Ae.default,s()(s()({style:{minWidth:"100px"},showSearch:!0,allowClear:!0,mode:"multiple",onSearch:ge},C),{},{filterOption:!1,notFoundContent:b?(0,e.jsx)(Et.Z,{size:"small"}):(0,e.jsx)(At.Z,{image:At.Z.PRESENTED_IMAGE_SIMPLE}),loading:b,children:q.map(function(G){return(0,e.jsx)(jt,{value:G.value,children:G.text||G.label},G.value)})}))}),Ht=Rt,Ve=n(57605),It=Ie.Z.Item,Qt=function(i){var a=i.dimensionOptions,c=i.value,d=i.periodDate,u=i.afterSolt,m=i.onChange,A=Ie.Z.useForm(),j=f()(A,1),h=j[0],p=(0,l.useRef)(),M=(0,l.useRef)({}),w=(0,l.useState)({operator:Ve.lf.IN}),C=f()(w,2),O=C[0],U=C[1];(0,l.useEffect)(function(){c&&(h.setFieldsValue(c),U(c))},[c]),(0,l.useEffect)(function(){if(O.dimensionBizName){var b;M.current={dimensionBizName:O.dimensionBizName},(b=p.current)===null||b===void 0||b.emitSearch(""),h.setFieldValue("dimensionValue",void 0),U(s()(s()({},O),{},{dimensionValue:void 0}))}},[O.dimensionBizName]);var x=function(){var b=ce()(D()().mark(function X(E){var z,q,ie,le,$,ge;return D()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:if((z=M.current)!==null&&z!==void 0&&z.dimensionBizName){N.next=2;break}return N.abrupt("return");case 2:if(q=M.current.dimensionBizName,ie=a.find(function(v){return v.value===q}),ie){N.next=6;break}return N.abrupt("return");case 6:return N.next=8,(0,_.z$)(s()(s()({},M.current),{},{value:E,modelId:ie.modelId,limit:50},d!=null&&d.startDate?{dateInfo:{dateMode:"BETWEEN",startDate:d.startDate,endDate:d.endDate}}:{}));case 8:if(le=N.sent,$=le.code,ge=le.data,!($===200&&Array.isArray(ge==null?void 0:ge.resultList))){N.next=13;break}return N.abrupt("return",ge.resultList.slice(0,50).map(function(v){return{value:v[q],label:v[q]}}));case 13:return N.abrupt("return",[]);case 14:case"end":return N.stop()}},X)}));return function(E){return b.apply(this,arguments)}}(),R=[Ve.lf.IN];return(0,e.jsx)(Ie.Z,{layout:"inline",form:h,colon:!1,initialValues:s()({},O),onValuesChange:function(X,E){var z=E.operator,q=E.dimensionValue;if(R.includes(z)&&(0,be.isString)(q)){var ie=[q];U(s()(s()({},E),{},{dimensionValue:ie})),h.setFieldValue("dimensionValue",ie);return}if(!R.includes(z)&&Array.isArray(q)){var le=q[0];U(s()(s()({},E),{},{dimensionValue:le})),h.setFieldValue("dimensionValue",le);return}U(E)},children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(It,{name:"dimensionBizName",noStyle:!0,children:(0,e.jsx)(Ae.default,{style:{minWidth:150},options:a,showSearch:!0,filterOption:function(X,E){var z;return((z=E==null?void 0:E.label)!==null&&z!==void 0?z:"").toLowerCase().includes(X.toLowerCase())},allowClear:!0,placeholder:"\u8BF7\u9009\u62E9\u7B5B\u9009\u7EF4\u5EA6"})}),(0,e.jsx)(xe.Z,{color:"processing",style:{margin:0,padding:0,height:32},children:(0,e.jsx)(It,{name:"operator",noStyle:!0,children:(0,e.jsx)(Ae.default,{style:{minWidth:72},bordered:!1,options:Object.values(Ve.lf).map(function(b){return{value:b,label:b}})})})}),(0,e.jsx)(It,{name:"dimensionValue",noStyle:!0,children:(0,e.jsx)(Ht,{placeholder:O.dimensionBizName?"\u8BF7\u8F93\u5165\u7EF4\u5EA6\u503C\u641C\u7D22":"\u8BF7\u5148\u9009\u62E9\u4E00\u4E2A\u7EF4\u5EA6",ref:p,style:{minWidth:150},maxTagCount:3,mode:R.includes(O.operator)?"multiple":void 0,fetchOptions:x})}),(0,e.jsx)(L.Z,{title:"\u6DFB\u52A0\u7B5B\u9009\u6761\u4EF6",children:(0,e.jsx)(ae.ZP,{type:"primary",icon:(0,e.jsx)(He.Z,{}),disabled:!(O.dimensionBizName&&(0,Ne.gP)(O.dimensionValue)),onClick:function(){var X=h.getFieldsValue();m==null||m(X)}})}),u]})})},_t=Qt,Be=function(i){var a=i.dimensionOptions,c=i.modelId,d=i.periodDate,u=i.afterSolt,m=i.value,A=i.onChange,j=(0,l.useState)([]),h=f()(j,2),p=h[0],M=h[1],w=(0,l.useState)({}),C=f()(w,2),O=C[0],U=C[1];return(0,l.useEffect)(function(){if(Array.isArray(a)){var x=a.reduce(function(R,b){return R[b.value]={dimensionBizName:"".concat(b.value),dimensionName:b.label},R},{});U(x)}},[a]),(0,e.jsxs)("div",{children:[(0,e.jsx)(_t,{modelId:c,dimensionOptions:a,periodDate:d,onChange:function(R){var b=[].concat($e()(p),[R]);M(b),A==null||A(b)},afterSolt:u}),(0,e.jsx)(B.Z,{size:8,wrap:!0,style:{marginTop:10},children:p.map(function(x,R){var b,X=x.dimensionBizName,E=x.dimensionValue,z=x.operator;return(0,e.jsxs)(B.Z,{children:[(0,e.jsxs)(xe.Z,{color:"blue",style:{padding:5},closable:!0,onClose:function(){var ie=$e()(p);ie.splice(R,1),M(ie),A==null||A(ie)},children:[(0,e.jsxs)("span",{style:{marginRight:5},children:[O==null||(b=O[X])===null||b===void 0?void 0:b.dimensionName,"[",z,"]:"]}),(0,e.jsx)(xe.Z,{color:"purple",children:Array.isArray(E)?E.join("\u3001"):E})]}),R!==p.length-1&&(0,e.jsx)(xe.Z,{color:"blue",style:{marginRight:10},children:(0,e.jsx)("span",{style:{height:32,display:"flex",alignItems:"center"},children:"AND"})})]},"".concat(X,"-").concat(E,"-").concat(z,"-").concat(Math.random()))})})]})},J=Be,ne=n(88372),me=n(93485),ue=n(15154),je=n(46504),re={popverOverlayContent:"popverOverlayContent___Z9Nyg",dateProCard:"dateProCard___WOkFx",dateTimeShowInput:"dateTimeShowInput___CYb4r",advancedSettingItemText:"advancedSettingItemText___qaD8D",dateAdvancedSettingContent:"dateAdvancedSettingContent___Celiz",dateShortCutSettingContent:"dateShortCutSettingContent___Rnz5A","ant-tag-checkable":"ant-tag-checkable___O1jPo","tag-value-box":"tag-value-box___iOgbe"},t=n(42520),qt=n(10154),k=n.n(qt),Dt=k()(k()(k()({},t.NU.DAY,t.wZ.DAY),t.NU.WEEK,t.wZ.WEEK),t.NU.MONTH,t.wZ.MONTH),Fe=k()(k()(k()(k()({},t.wZ.DAY,"\u5929"),t.wZ.WEEK,"\u5468"),t.wZ.MONTH,"\u6708"),t.wZ.YEAR,"\u5E74"),Le=k()(k()(k()(k()({},t.wZ.DAY,"\u5F53\u5929"),t.wZ.WEEK,"\u672C\u5468"),t.wZ.MONTH,"\u672C\u6708"),t.wZ.YEAR,"\u4ECA\u5E74"),ct=k()(k()(k()(k()({},t.qK.PERDAY,"\u6BCF\u65E5"),t.qK.PERWEEK,"\u6BCF\u5468(\u5468\u4E00)"),t.qK.PERMONTH,"\u6BCF\u6708(01\u65E5)"),t.qK.PERYEAR,"\u6BCF\u5E74(01\u670801\u65E5)"),Mt=[{label:"\u5929",value:t.NU.DAY,toolTips:"\u5C55\u793A\u6BCF\u5929\u6570\u636E"},{label:"\u5468",value:t.NU.WEEK,toolTips:"\u4EC5\u5C55\u793A\u6BCF\u5468\u65E5\u6570\u636E"},{label:"\u6708",value:t.NU.MONTH,toolTips:"\u4EC5\u5C55\u793A\u6BCF\u6708\u6700\u540E\u4E00\u5929\u6570\u636E"}],it="\u6700\u8FD11\u5929",ea=function(i,a){return{dateSettingType:"DYNAMIC",dynamicParams:{includesCurrentPeriod:!0,dateRangeType:a,dynamicAdvancedConfigType:"last",dateSettingType:t.aN.DYNAMIC,shortCutId:"last".concat(i).concat(t.yV[a]),number:i,periodType:a}}},da=[{id:"last7Days",text:"\u6700\u8FD17\u5929",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.DAY,initData:{shortCutId:"last7Days",number:7,periodType:t.wZ.DAY,includesCurrentPeriod:!1}},{id:"last15Days",text:"\u6700\u8FD115\u5929",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.DAY,initData:{shortCutId:"last15Days",number:15,periodType:t.wZ.DAY,includesCurrentPeriod:!1}},{id:"last30Days",text:"\u6700\u8FD130\u5929",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.DAY,initData:{shortCutId:"last30Days",number:30,periodType:t.wZ.DAY,includesCurrentPeriod:!1}},{id:"last4Weeks",text:"\u6700\u8FD14\u5468",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.WEEK,initData:{shortCutId:"last4Weeks",number:4,periodType:t.wZ.WEEK,includesCurrentPeriod:!1}},{id:"last8Weeks",text:"\u6700\u8FD18\u5468",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.WEEK,initData:{shortCutId:"last8Weeks",number:8,periodType:t.wZ.WEEK,includesCurrentPeriod:!1}},{id:"last12Weeks",text:"\u6700\u8FD112\u5468",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.WEEK,initData:{shortCutId:"last12Weeks",number:12,periodType:t.wZ.WEEK,includesCurrentPeriod:!1}},{id:"last6Months",text:"\u6700\u8FD16\u4E2A\u6708",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.MONTH,initData:{shortCutId:"last6Months",number:6,periodType:t.wZ.MONTH,includesCurrentPeriod:!1}},{id:"last12Months",text:"\u6700\u8FD112\u4E2A\u6708",advancedConfigType:t.Bg.LAST,dateRangeType:t.NU.MONTH,initData:{shortCutId:"last12Months",number:12,periodType:t.wZ.MONTH,includesCurrentPeriod:!1}}],Vt=function(i,a){var c=a?i-1:i,d=a?0:1;return{startDateNumber:c,endDateNumber:d}},ua=function(i){var a=i.number,c=i.periodType,d=i.latestDateMap,u=i.includesCurrentPeriod,m=u===void 0?!1:u,A=Vt(a,m),j=A.startDateNumber,h=A.endDateNumber;switch(c){case t.wZ.DAY:{var p=m?void 0:(d==null?void 0:d.maxPartition)||void 0,M={startDateNumber:j,endDateNumber:h};return p&&(M=Vt(a,!0)),[T()(p).subtract(M.startDateNumber,"days").startOf("days"),T()(p).subtract(M.endDateNumber,"days").endOf("days")]}case t.wZ.WEEK:{var w=m?void 0:(d==null?void 0:d.maxPartition)||void 0,C={startDateNumber:j,endDateNumber:h};return w&&(C=Vt(a,!0)),[T()(w).subtract(C.startDateNumber,"week").startOf("week"),T()(w).subtract(C.endDateNumber,"week").endOf("week")]}case t.wZ.MONTH:{var O=m?void 0:(d==null?void 0:d.maxPartition)||void 0,U={startDateNumber:j,endDateNumber:h};return O&&(U=Vt(a,!0)),[T()(O).subtract(U.startDateNumber,"month").startOf("month"),T()(O).subtract(U.endDateNumber,"month").endOf("month")]}case t.wZ.YEAR:return[T()().subtract(j,"year").startOf("year"),T()().subtract(h,"year").endOf("year")];default:return[]}},ca=function(i){var a=i.number,c=i.periodType,d=a;switch(c){case t.wZ.DAY:return[T()().subtract(d,"days").startOf("days"),T()().subtract(d,"days").endOf("days")];case t.wZ.WEEK:return[T()().subtract(d,"week").startOf("week"),T()().subtract(d,"week").endOf("week")];case t.wZ.MONTH:return[T()().subtract(d,"month").startOf("month"),T()().subtract(d,"month").endOf("month")];case t.wZ.YEAR:return[T()().subtract(d,"year").startOf("year"),T()().subtract(d,"year").endOf("year")];default:return[]}},ma=function(i){var a=i.perPeriodType;switch(a){case t.qK.PERDAY:return[T()().startOf("days"),T()()];case t.qK.PERWEEK:return[T()().startOf("week"),T()()];case t.qK.PERMONTH:return[T()().startOf("month"),T()()];case t.qK.PERYEAR:return[T()().startOf("year"),T()()];default:return[]}},va=function(i){var a=i.date;return[T()(a),T()()]},fa=function(i){return i?[T()(i),T()(i)]:(console.warn("\u6700\u65B0\u6807\u7B7E\u66F4\u65B0\u65E5\u671F\u4E0D\u5B58\u5728"),[T()().subtract(1,"week"),T()().subtract(1,"week")])},Bt=k()(k()(k()(k()(k()({},t.Bg.LATEST,fa),t.Bg.LAST,ua),t.Bg.HISTORY,ca),t.Bg.FROM_DATE_PERIOD,ma),t.Bg.FROM_DATE,va),ga=function(i){return i&&Array.isArray(i)?i.map(function(a){var c;return(a==null||(c=a.format)===null||c===void 0?void 0:c.call(a,"YYYY-MM-DD"))||""}):i},Ra=function(i,a){var c=i.dynamicAdvancedConfigType,d=i.number,u=i.periodType,m=i.includesCurrentPeriod,A=i.perPeriodType,j=i.date,h=i.dateRangeType,p={dateMode:t.Ug.RANGE,dateRangeType:h||t.NU.DAY},M=[];switch(c){case t.Bg.LATEST:{var w=a==null?void 0:a.maxPartition;M=fa(w);break}case t.Bg.LAST:{M=ua({number:d,periodType:u,latestDateMap:a,includesCurrentPeriod:m});break}case t.Bg.HISTORY:{M=ca({number:d,periodType:u});break}case t.Bg.FROM_DATE_PERIOD:M=ma({perPeriodType:A});break;case t.Bg.FROM_DATE:M=va({date:j});break;default:break}return s()(s()({},p),{},{dateRange:ga(M)})},Ia=function(i){var a=i.dateSettingType,c=i.latestDateMap,d=c===void 0?{}:c,u=i.dynamicParams,m=u===void 0?{}:u,A=i.staticParams,j=A===void 0?{}:A;return a===t.aN.DYNAMIC?Ra(m,d):j},ha=function(i,a,c){var d=i.number,u=i.periodType,m=i.includesCurrentPeriod,A=i.perPeriodType,j=i.date,h=[],p="";switch(a){case t.Bg.LATEST:{var M=c.maxPartition;p=it,h=Bt[t.Bg.LATEST](M);break}case t.Bg.LAST:h=Bt[t.Bg.LAST]({number:d,periodType:u,latestDateMap:c,includesCurrentPeriod:m}),p="\u6700\u8FD1".concat(d).concat(Fe[u])+"".concat(m?"(\u5305\u542B".concat(Le[u],")"):"");break;case t.Bg.HISTORY:h=Bt[t.Bg.HISTORY]({number:d,periodType:u}),p="\u8FC7\u53BB\u7B2C".concat(d).concat(Fe[u]);break;case t.Bg.FROM_DATE_PERIOD:h=Bt[t.Bg.FROM_DATE_PERIOD]({perPeriodType:A}),p="\u81EA\u4ECE".concat(ct[A],"00:00:00\u81F3\u6B64\u523B");break;case t.Bg.FROM_DATE:h=Bt[t.Bg.FROM_DATE]({date:j}),p="".concat(j,"\u81F3\u6B64\u523B");break;default:h=[],p=""}return{dateRangeString:ga(h),dateRangeStringDesc:p}};function pa(I){var i=I.dates,a=I.dateRangeType,c=I.latestDateMap,d=I.isDateRangeChange,u=i;Array.isArray(u)||(u=[u]);var m=t.A7[a],A=u.map(function(j,h){switch(m){case t.Dw.DATE:return c!=null&&c.maxPartition&&!d?c.maxPartition:j.format("YYYY-MM-DD");case t.Dw.WEEK:return h===0?j.startOf("week").subtract(d?0:1,"week").format("YYYY-MM-DD"):j.endOf("week").subtract(d?0:1,"week").format("YYYY-MM-DD");case t.Dw.MONTH:return h===0?j.startOf("month").subtract(d?0:1,"month").format("YYYY-MM-DD"):j.endOf("month").subtract(d?0:1,"month").format("YYYY-MM-DD");default:return c!=null&&c.maxPartition&&!d?c==null?void 0:c.maxPartition:j.format("YYYY-MM-DD")}});return A}var Da=function(i,a){var c=T()(i).format("w"),d=T()(a).format("w");return"".concat(i,"(").concat(c,"\u5468)\u81F3").concat(a,"(").concat(d,"\u5468)")},Ta=function(i,a){var c=T()(i).format("YYYY-MM"),d=T()(a).format("YYYY-MM");return"".concat(c,"\u81F3").concat(d)},ya=n(82096),Ma=n(28977),Ee=n.n(Ma),ba=ya.default.RangePicker,Fa=function(i){var a=i.initialValues,c=i.dateRangeTypeProps,d=i.currentDateSettingType,u=d===void 0?t.aN.STATIC:d,m=i.onDateRangeChange,A=(0,l.useState)({maxPartition:Ee()().format("YYYY-MM-DD")}),j=f()(A,2),h=j[0],p=j[1],M=(0,l.useState)(function(){return{dateSettingType:t.aN.STATIC,dateMode:(a==null?void 0:a.dateMode)||t.Ug.RANGE,dateRangeType:(a==null?void 0:a.dateRangeType)||c||t.NU.DAY,dateRange:(a==null?void 0:a.dateRange)||[],dateMultiple:(a==null?void 0:a.dateMultiple)||[]}}),w=f()(M,2),C=w[0],O=w[1],U=(0,l.useState)([]),x=f()(U,2),R=x[0],b=x[1],X=(0,l.useState)(function(){return c?t.A7[c]:C.dateRangeType?t.A7[C.dateRangeType]:t.Dw.DATE}),E=f()(X,2),z=E[0],q=E[1];(0,l.useEffect)(function(){le()},[]),(0,l.useEffect)(function(){u===t.aN.STATIC&&ie(c),q(t.A7[c])},[c,h]);var ie=function(){var G=ce()(D()().mark(function N(v){return D()().wrap(function(Y){for(;;)switch(Y.prev=Y.next){case 0:if(v){Y.next=2;break}return Y.abrupt("return");case 2:O(s()(s()({},C),{},{dateRangeType:v})),ge(R,v,C.dateMode);case 4:case"end":return Y.stop()}},N)}));return function(v){return G.apply(this,arguments)}}(),le=function(){var N=(a==null?void 0:a.dateRange)||[],v=f()(N,2),S=v[0],Y=v[1],y=h.maxPartition,F=[Ee()(),Ee()()];S&&Y&&(F=[Ee()(S),Ee()(Y)]),R.length===0&&!(S&&Y)&&y&&(F=[Ee()(y),Ee()(y)]),b(F)},$=function(N){var v=s()(s()(s()({},C),N),{},{dateRangeStringDesc:""}),S=v.dateRange,Y=v.dateMode;if(Y===t.Ug.RANGE){var y=f()(S,2),F=y[0],pe=y[1];F&&pe&&(v.dateRangeStringDesc="".concat(F,"\u81F3").concat(pe),v.dateMultiple=[])}Y===t.Ug.LIST&&(v.dateRangeStringDesc="\u65E5\u671F\u591A\u9009",v.dateRange=[]),O(v),m(v.dateRange,v)},ge=function(N,v,S,Y){if(!N){$({dateRange:[]});return}var y=pa({dates:N,dateRangeType:v,latestDateMap:h,isDateRangeChange:Y});y[0]&&y[1]&&(b([Ee()(y[0]),Ee()(y[1])]),$({dateMode:S,dateRangeType:v,dateRange:y}))};return(0,e.jsx)(B.Z,{children:(0,e.jsx)(ba,{style:{paddingBottom:5},value:R,onChange:function(N){b(N);var v=pa({dates:N,latestDateMap:h,isDateRangeChange:!0,dateRangeType:c||C.dateRangeType});$({dateRange:v,dateRangeType:c||C.dateRangeType})},allowClear:!0,picker:z})})},Za=Fa,Pa=n(32693),Sa=n(76400),Ca=n(10947),kt=n(34044),xa=n(37069),Oa=n(55838),Ba=xe.Z.CheckableTag,La=Sa.Z.Panel,Ya=Ca.Z.Link,Wa=Ae.default.Option,Ua=(0,l.forwardRef)(function(I,i){var a=I.initialValues,c=I.dateRangeTypeProps,d=I.onDateRangeChange,u=I.onAdvanceSettingCollapsedChange,m=I.onShortCutClick,A=I.onDateRangeStringAndDescChange,j=I.disabledAdvanceSetting,h=j===void 0?!1:j;(0,l.useImperativeHandle)(i,function(){return{dynamicDateUpdateAdvancedPanelFormData:function(){$&&oe(v[$],$,!0)}}});var p=function(){return!(a!=null&&a.dateSettingType)||a!=null&&a.shortCutId?[]:["1"]},M=(0,l.useState)(c),w=f()(M,2),C=w[0],O=w[1];(0,l.useEffect)(function(){U(c)},[c]),(0,l.useEffect)(function(){(a==null?void 0:a.dateSettingType)===t.aN.DYNAMIC&&Yt(c)},[C]);var U=function(g){var V=v[t.Bg.LAST],de=v[t.Bg.HISTORY];switch(g){case t.NU.DAY:S(s()(s()({},v),{},k()(k()({},t.Bg.LAST,s()(s()({},V),{},{periodType:t.wZ.DAY})),t.Bg.HISTORY,s()(s()({},de),{},{periodType:t.wZ.DAY}))));break;case t.NU.WEEK:S(s()(s()({},v),{},k()(k()({},t.Bg.LAST,s()(s()({},V),{},{periodType:t.wZ.WEEK})),t.Bg.HISTORY,s()(s()({},de),{},{periodType:t.wZ.WEEK}))));break;case t.NU.MONTH:S(s()(s()({},v),{},k()(k()({},t.Bg.LAST,s()(s()({},V),{},{periodType:t.wZ.MONTH})),t.Bg.HISTORY,s()(s()({},de),{},{periodType:t.wZ.MONTH}))));break;default:break}O(g),q([])},x=function(){return a==null?void 0:a.dynamicAdvancedConfigType},R=function(){var g=k()(k()(k()(k()({},t.Bg.LAST,{number:1,periodType:Dt[c],includesCurrentPeriod:!1}),t.Bg.HISTORY,{number:1,periodType:Dt[c]}),t.Bg.FROM_DATE_PERIOD,{perPeriodType:t.qK.PERDAY}),t.Bg.FROM_DATE,{date:Ee()()});if(v&&(g=s()({},v)),a!=null&&a.dynamicAdvancedConfigType){var V=a.dynamicAdvancedConfigType,de=g[V];if(!de)return g;var ve=Object.keys(de).reduce(function(Ze,Ke){return s()(s()({},Ze),{},k()({},Ke,a[Ke]))},{});g[V]=ve}return g},b=function(){return(a==null?void 0:a.shortCutId)||""},X=(0,l.useState)(p()),E=f()(X,2),z=E[0],q=E[1],ie=(0,l.useState)(x()),le=f()(ie,2),$=le[0],ge=le[1],G=(0,l.useState)(R()),N=f()(G,2),v=N[0],S=N[1],Y=(0,l.useState)(b()),y=f()(Y,2),F=y[0],pe=y[1],K=function(){q(p()),ge(x()),S(R()),pe(b())};(0,l.useEffect)(function(){(a==null?void 0:a.dateSettingType)===t.aN.DYNAMIC&&K()},[a]);var se=function(g,V){d(g,s()(s()({},V),{},{dateSettingType:t.aN.DYNAMIC}))},oe=function(g,V){var de=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,ve=s()(s()(s()({},v[V]),g),{},{dateRangeType:c,dynamicAdvancedConfigType:V}),Ze=ha(ve,V,{maxPartition:Ee()().format("YYYY-MM-DD")}),Ke=Ze.dateRangeString,ot=Ze.dateRangeStringDesc;ve.dateRangeStringDesc=ot,A==null||A({dateRangeString:Ke,dateRangeStringDesc:ot}),de&&se(Ke,ve),S(s()(s()({},v),{},k()({},V,ve)))},lt=function(g,V){switch(g){case t.wZ.DAY:return![t.NU.DAY].includes(V);case t.wZ.WEEK:return![t.NU.DAY,t.NU.WEEK].includes(V);case t.wZ.MONTH:return!1;case t.wZ.YEAR:return!1;default:return!1}},et=function(g){var V=(0,Ne.fw)(Fe),de=V.reduce(function(ve,Ze){var Ke=Ze.value,ot=Ze.label,vt=lt(Ke,g);return vt||ve.push((0,e.jsx)(Wa,{value:Ke,children:ot},Ke)),ve},[]);return de},De=function(g){return g!==$},Yt=function(g){var V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,de=da.filter(function(Ze){return Ze.dateRangeType===g}),ve=de[0];ve&&bt(ve,V)},bt=function(g){var V=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,de=g.id,ve=g.advancedConfigType,Ze=g.initData;pe(de),ge(ve),oe(Ze,ve,V),V&&(m==null||m(de))},ke=function(){pe("")};return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{className:re.dateShortCutSettingContent,children:(0,e.jsx)(Ge.Z,{children:da.map(function(P){var g=P.id,V=P.text,de=P.dateRangeType;if(de===c)return(0,e.jsx)(Me.Z,{children:(0,e.jsx)(Ba,{className:re["ant-tag-checkable"],checked:F===g,onChange:function(){bt(P)},children:(0,e.jsx)("div",{className:re["tag-value-box"],children:V})},"row-col-tag-".concat(g))},"row-col-".concat(g))})})}),!h&&(0,e.jsx)("div",{className:re.dateAdvancedSettingContent,children:(0,e.jsx)(Sa.Z,{activeKey:z,onChange:function(g){if(q(g),g.length===0){u==null||u(!1);return}u==null||u(!0)},bordered:!1,ghost:!0,expandIconPosition:"right",children:(0,e.jsxs)(La,{header:"",extra:(0,e.jsx)(B.Z,{children:(0,e.jsx)(Ya,{children:"\u9AD8\u7EA7\u8BBE\u7F6E"})}),children:[(0,e.jsx)("div",{children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)("div",{style:{color:"rgba(0, 0, 0, 0.85)"},children:"\u52A8\u6001\u65F6\u95F4"}),(0,e.jsx)(L.Z,{title:"\u65E5\u671F\u968F\u7740\u65F6\u95F4\u63A8\u79FB\u800C\u66F4\u65B0\u3002 \u82E5\u57281\u67081\u65E5\u8BBE\u7F6E\u67E5\u8BE2\u65E5\u671F\u4E3A\u201C\u4ECA\u5929\u201D\uFF0C \u5219\u7B2C\u4E8C\u5929\u7684\u67E5\u8BE2\u65E5\u671F\u4E3A1\u67082\u65E5\u3002",children:(0,e.jsx)(Pa.Z,{})})]})}),(0,e.jsx)(kt.ZP.Group,{onChange:function(g){var V=g.target.value;ge(V),oe(v[V],V),ke()},value:$,children:(0,e.jsxs)(B.Z,{direction:"vertical",children:[(0,e.jsx)(kt.ZP,{value:t.Bg.LAST,children:(0,e.jsxs)(B.Z,{size:10,children:[(0,e.jsx)("span",{className:re.advancedSettingItemText,children:"\u6700\u8FD1"}),(0,e.jsx)(xa.Z,{style:{width:120},placeholder:"\u8BF7\u8F93\u5165\u6570\u5B57",min:1,disabled:De(t.Bg.LAST),value:v[t.Bg.LAST].number,onChange:function(g){oe({number:g},t.Bg.LAST),ke()}}),(0,e.jsx)(Ae.default,{style:{width:120},disabled:De(t.Bg.LAST),value:v[t.Bg.LAST].periodType,onClick:function(g){g.preventDefault(),g.stopPropagation()},onChange:function(g){oe({periodType:g},t.Bg.LAST),ke()},children:et(c)}),(0,e.jsxs)(Oa.Z,{disabled:De(t.Bg.LAST),checked:v[t.Bg.LAST].includesCurrentPeriod,onChange:function(g){var V=g.target.checked;oe({includesCurrentPeriod:V},t.Bg.LAST),ke()},children:["\u5305\u542B",Le[v[t.Bg.LAST].periodType]]})]})}),(0,e.jsx)(kt.ZP,{value:t.Bg.HISTORY,children:(0,e.jsxs)(B.Z,{size:10,children:[(0,e.jsx)("span",{className:re.advancedSettingItemText,children:"\u8FC7\u53BB\u7B2C"}),(0,e.jsx)(xa.Z,{style:{width:120},placeholder:"\u8BF7\u8F93\u5165\u6570\u5B57",min:1,disabled:De(t.Bg.HISTORY),value:v[t.Bg.HISTORY].number,onChange:function(g){oe({number:g},t.Bg.HISTORY),ke()}}),(0,e.jsx)(Ae.default,{style:{width:120},disabled:De(t.Bg.HISTORY),value:v[t.Bg.HISTORY].periodType,onClick:function(g){g.preventDefault(),g.stopPropagation()},onChange:function(g){oe({periodType:g},t.Bg.HISTORY),ke()},children:et(c)})]})}),(0,e.jsx)(kt.ZP,{value:t.Bg.FROM_DATE,children:(0,e.jsxs)(B.Z,{size:10,children:[(0,e.jsx)("span",{className:re.advancedSettingItemText,children:"\u81EA\u4ECE"}),(0,e.jsx)(ya.default,{disabled:De(t.Bg.FROM_DATE),value:Ee()(v[t.Bg.FROM_DATE].date),disabledDate:function(g){return g&&g>Ee()().endOf("day")},picker:t.A7[c],onChange:function(g,V){if(g){var de=t.A7[c];de===t.Dw.WEEK&&g.startOf("week").format("YYYY-MM-DD"),de===t.Dw.MONTH&&g.startOf("month").format("YYYY-MM-DD"),oe({date:g},t.Bg.FROM_DATE),ke()}}}),"\u81F3\u6B64\u523B"]})})]})})]},"1")})})]})}),wa=Ua,Lt=n(7595),Ha=xe.Z.CheckableTag,Va=function(i){var a,c,d,u=i.disabledAdvanceSetting,m=i.initialValues,A=i.showCurrentDataRangeString,j=A===void 0?!0:A,h=i.onDateRangeChange,p=i.onDateRangeTypeChange,M=i.onInit,w=(0,l.useRef)({}),C=function(Z){var H={shortCutId:"last7Days",dateRangeType:t.NU.DAY,dynamicAdvancedConfigType:t.Bg.LATEST,dateRangeStringDesc:it,number:7,dateSettingType:t.aN.DYNAMIC};switch(Z){case t.NU.DAY:return H;case t.NU.WEEK:return{shortCutId:"last4Weeks",dateRangeType:t.NU.WEEK,dynamicAdvancedConfigType:t.Bg.LAST,dateRangeStringDesc:"\u6700\u8FD14\u5468",dateSettingType:t.aN.DYNAMIC,includesCurrentPeriod:!1,number:4,periodType:"WEEK"};case t.NU.MONTH:return{shortCutId:"last6Months",dateRangeType:t.NU.MONTH,dynamicAdvancedConfigType:t.Bg.LAST,dateRangeStringDesc:"\u6700\u8FD16\u6708",includesCurrentPeriod:!1,number:6,periodType:"MONTH",dateSettingType:t.aN.DYNAMIC};default:return H}},O=(0,l.useState)((m==null||(a=m.dynamicParams)===null||a===void 0?void 0:a.dateRangeType)||(m==null||(c=m.staticParams)===null||c===void 0?void 0:c.dateRangeType)||t.NU.DAY),U=f()(O,2),x=U[0],R=U[1],b={dateSettingType:t.aN.STATIC,dateMode:t.Ug.RANGE,dateRangeType:t.NU.DAY,dateRange:[],dateMultiple:[],dateRangeStringDesc:""},X=(0,l.useState)({maxPartition:Ee()().format("YYYY-MM-DD")}),E=f()(X,2),z=E[0],q=E[1],ie=(0,l.useState)(function(){return m?Ia(m):{}}),le=f()(ie,1),$=le[0],ge=(0,l.useState)(!1),G=f()(ge,2),N=G[0],v=G[1],S=(0,l.useState)(function(){return(m==null?void 0:m.staticParams)||{}}),Y=f()(S,2),y=Y[0],F=Y[1],pe=(0,l.useState)(function(){return(m==null?void 0:m.dynamicParams)||{}}),K=f()(pe,2),se=K[0],oe=K[1],lt=(0,l.useState)((m==null||(d=m.staticParams)===null||d===void 0?void 0:d.dateMode)||t.Ug.RANGE),et=f()(lt,2),De=et[0],Yt=et[1],bt=(0,l.useState)((m==null?void 0:m.dateSettingType)||t.aN.STATIC),ke=f()(bt,2),P=ke[0],g=ke[1],V=(0,l.useState)(function(){return $.dateRange||[]}),de=f()(V,2),ve=de[0],Ze=de[1],Ke=(0,l.useState)(function(){return Kt()}),ot=f()(Ke,2),vt=ot[0],na=ot[1];function Kt(){var ee=f()(ve,2),Z=ee[0],H=ee[1];if(P===t.aN.DYNAMIC)return Z&&H?x===t.NU.WEEK?Da(Z,H):x===t.NU.MONTH?Ta(Z,H):"".concat(Z,"\u81F3").concat(H):"";if(P===t.aN.STATIC){var Re=y.dateMode,he=y.dateMultiple;if(Re===t.Ug.RANGE)return Z&&H?x===t.NU.WEEK?Da(Z,H):x===t.NU.MONTH?Ta(Z,H):"".concat(Z," \u81F3 ").concat(H):"";if(Re===t.Ug.LIST)return he.join(",")}}(0,l.useEffect)(function(){na(Kt())},[y,se,ve]);var zt=function(Z){Z&&(Yt(Z),Z===t.Ug.LIST&&R(t.NU.DAY))},Wt=function(Z,H){var Re=H.dateRangeStringDesc,he=H.dateSettingType,ze=H.dateMode;zt(ze),Se(Re),g(he),Array.isArray(Z)&&Ze(Z);var We={latestDateMap:z,dateSettingType:he,dynamicParams:{},staticParams:{}};he===t.aN.DYNAMIC&&(We=s()(s()({},We),{},{dateSettingType:he,dynamicParams:H,staticParams:{}})),he===t.aN.STATIC&&(We=s()(s()({},We),{},{dateSettingType:he,dynamicParams:{},staticParams:H})),oe(s()({},We.dynamicParams)),F(s()({},We.staticParams)),h(Z,We)},$t=function(Z){if(P===t.aN.DYNAMIC){var H=ha(se,se.dynamicAdvancedConfigType,z),Re=H.dateRangeStringDesc;return Re}if(P===t.aN.STATIC){var he=y.dateMode,ze=ve||[],We=f()(ze,2),wt=We[0],xt=We[1];if(he===t.Ug.RANGE)return wt&&xt?"".concat(wt," \u81F3 ").concat(xt):"";if(he===t.Ug.LIST)return"\u65E5\u671F\u591A\u9009"}var Ft=Z||[],Nt=f()(Ft,2),Zt=Nt[0],Pt=Nt[1];return Zt&&Pt?"".concat(Zt," \u81F3 ").concat(Pt):""},Ut=(0,l.useState)(function(){return $t($.dateRange)}),Qe=f()(Ut,2),we=Qe[0],Se=Qe[1],Ye=function(Z){var H=Z.latestDateMap;if(!m){var Re=C(x),he=H.maxPartition,ze=[he,he],We=s()(s()({},Re),{},{dateRange:ze});Wt(ze,We)}};(0,l.useEffect)(function(){Ye({latestDateMap:z})},[m]),(0,l.useEffect)(function(){var ee=$.dateRange;Se($t(ee))},[$]);var tt=(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(Lt.Z,{className:re.dateProCard,title:(0,e.jsx)(B.Z,{children:"\u65F6\u95F4\u7C92\u5EA6"}),children:(0,e.jsx)("div",{className:re.dateShortCutSettingContent,children:(0,e.jsx)(Ge.Z,{children:Mt.map(function(ee){var Z=ee.value,H=ee.label,Re=ee.toolTips;if(!(De===t.Ug.LIST&&Z!==t.NU.DAY))return(0,e.jsx)(Me.Z,{children:(0,e.jsx)(L.Z,{title:Re,children:(0,e.jsx)(Ha,{className:re["ant-tag-checkable"],checked:x===Z,onChange:function(){R(Z),p==null||p(Z)},children:(0,e.jsx)("div",{className:re["tag-value-box"],children:H})},"row-col-tag-".concat(Z))})},"row-col-".concat(Z))})})})}),(0,e.jsx)(Lt.Z,{className:re.dateProCard,title:"\u5FEB\u6377\u9009\u9879",children:(0,e.jsx)(wa,{ref:w,disabledAdvanceSetting:u,initialValues:se,dateRangeTypeProps:x,onDateRangeChange:Wt,onDateRangeStringAndDescChange:function(Z){var H=Z.dateRangeString;Ze(H)},onShortCutClick:function(){v(!1)}})}),(0,e.jsx)(Lt.Z,{className:re.dateProCard,title:(0,e.jsx)(B.Z,{children:"\u9759\u6001\u65F6\u95F4"}),children:(0,e.jsx)(Za,{currentDateSettingType:P,initialValues:y,dateRangeTypeProps:x,onDateRangeChange:Wt})}),(0,e.jsxs)("div",{style:{display:"flex",borderTop:"1px solid #eee",paddingTop:"10px"},children:[(0,e.jsxs)(B.Z,{style:{fontSize:12,marginRight:20},children:[(0,e.jsx)("div",{style:{width:60},children:"\u5DF2\u9009\u65F6\u95F4\uFF1A"}),(0,e.jsx)("div",{children:vt})]}),(0,e.jsxs)(B.Z,{style:{marginLeft:"auto"},children:[(0,e.jsx)(ae.ZP,{type:"primary",onClick:function(){P===t.aN.DYNAMIC&&w.current.dynamicDateUpdateAdvancedPanelFormData(),v(!1)},children:"\u786E \u8BA4"}),(0,e.jsx)(ae.ZP,{onClick:function(){v(!1)},children:"\u53D6 \u6D88"})]})]})]});return(0,e.jsxs)(B.Z,{direction:"vertical",children:[(0,e.jsx)(ue.Z,{content:tt,destroyTooltipOnHide:!1,open:N,trigger:"click",onOpenChange:function(Z){if(v(Z),!Z&&P===t.aN.DYNAMIC){var H=se.dateRangeType;H&&H!==x&&R(H)}},overlayClassName:re.popverOverlayContent,placement:"left",children:(0,e.jsx)(je.default,{className:re.dateTimeShowInput,value:we,placeholder:"\u8BF7\u9009\u62E9\u65E5\u671F\u65F6\u95F4",prefix:(0,e.jsx)(ne.Z,{}),readOnly:!0,style:{width:280},suffix:(0,e.jsx)(L.Z,{title:"".concat(vt),children:(0,e.jsx)(me.Z,{style:{}})})})}),j&&!(P===t.aN.STATIC&&De===t.Ug.RANGE&&x===t.NU.DAY)&&(0,e.jsxs)("div",{style:{color:"#0e73ff"},children:["\u5F53\u524D\u65F6\u95F4: ",vt]})]})},ka=Va,ta=n(63032),Na=n(61338),Ka=function(i){var a=i.columnConfig,c=i.dataSource,d=i.dateFieldName,u=d===void 0?"sys_imp_date":d,m=i.metricFieldName,A=i.loading,j=A===void 0?!1:A,h=(0,l.useState)([]),p=f()(h,2),M=p[0],w=p[1];return(0,l.useEffect)(function(){if(Array.isArray(a)){var C=a.map(function(O){var U=O.name,x=O.nameEn;return x===u?{title:"\u65E5\u671F",dataIndex:x,key:x,width:120,fixed:"left",defaultSortOrder:"descend",sorter:function(b,X){return T()(b[x]).valueOf()-T()(X[x]).valueOf()}}:x===m?{title:U,dataIndex:x,key:x,sortDirections:["descend"],sorter:function(b,X){return b[x]-X[x]}}:{title:U,key:x,dataIndex:x}});w(C)}},[a]),(0,e.jsx)("div",{style:{height:"100%"},children:(0,e.jsx)(Na.Z,{columns:M,dataSource:c,scroll:{x:200,y:700},loading:j,onChange:function(){}})})},za=Ka,mt=n(97180),aa=Ie.Z.Item,$a=function(i){var a,c,d,u,m=i.metircData,A=i.relationDimensionOptions,j=i.dimensionList,h=(0,l.useRef)([]),p=(0,l.useState)([]),M=f()(p,2),w=M[0],C=M[1],O=(0,l.useState)(!1),U=f()(O,2),x=U[0],R=U[1],b=(0,l.useState)(),X=f()(b,2),E=X[0],z=X[1],q=(0,l.useState)(""),ie=f()(q,2),le=ie[0],$=ie[1],ge=(0,l.useState)(!1),G=f()(ge,2),N=G[0],v=G[1],S=(0,l.useState)({}),Y=f()(S,2),y=Y[0],F=Y[1],pe=(0,l.useState)(!0),K=f()(pe,2),se=K[0],oe=K[1],lt=(0,l.useState)({startDate:Ee()().subtract(6,"days").format("YYYY-MM-DD"),endDate:Ee()().format("YYYY-MM-DD"),dateField:mt.hu[t.NU.DAY],period:t.NU.DAY}),et=f()(lt,2),De=et[0],Yt=et[1],bt=(0,l.useState)(5),ke=f()(bt,2),P=ke[0],g=ke[1],V=(0,l.useState)([]),de=f()(V,2),ve=de[0],Ze=de[1],Ke=(0,l.useState)(!1),ot=f()(Ke,2),vt=ot[0],na=ot[1],Kt=(0,l.useState)(),zt=f()(Kt,2),Wt=zt[0],$t=zt[1],Ut=function(){var Qe=ce()(D()().mark(function we(){var Se,Ye,tt,ee,Z,H,Re,he,ze,We,wt,xt,Ft,Nt,Zt,Pt,Gt,Xt,ra,ia,sa,la,oa=arguments;return D()().wrap(function(ft){for(;;)switch(ft.prev=ft.next){case 0:if(Se=oa.length>0&&oa[0]!==void 0?oa[0]:{download:!1},Ye=Se.download,tt=Se.dimensionGroup,ee=tt===void 0?[]:tt,Z=Se.dimensionFilters,H=Z===void 0?[]:Z,Ye?v(!0):R(!0),m){ft.next=5;break}return ft.abrupt("return");case 5:return Re=m.bizName,he=m.id,ze=m.domainId,We=m.name,h.current=[{name:We,column:Re}],wt=j.reduce(function(Tt,Jt){return ee.includes(Jt.bizName)&&Tt.push(Jt.id),Tt},[]),ft.next=10,(0,_.$8)({domainId:ze,metricIds:[he],dimensionIds:wt,filters:H,period:De.period,dateField:De.dateField,startDate:De.startDate,endDate:De.endDate,download:Ye,isTransform:vt});case 10:if(xt=ft.sent,!Ye){ft.next=14;break}return v(!1),ft.abrupt("return");case 14:Ft=xt.code,Nt=xt.data,Zt=xt.msg,R(!1),Ft===200?(Pt=Nt.resultList,Gt=Nt.columns,Xt=Nt.queryAuthorization,Ze(Gt),ra=Xt==null?void 0:Xt.message,ra&&$(ra),ia=Gt.find(function(Tt){return Tt.nameEn===Re}),ia&&z(ia),sa=Gt.find(function(Tt){return Tt.type==="DATE"}),sa?(la=sa.nameEn,C($e()(Pt).sort(function(Tt,Jt){return Tt[la].localeCompare(Jt[la])}))):C(Pt),oe(!1),ee[ee.length-1]&&$t(ee[ee.length-1])):(Ft===401||Ft===400?$(Zt):W.ZP.error(Zt),oe(!0),C([]),z(void 0));case 17:case"end":return ft.stop()}},we)}));return function(){return Qe.apply(this,arguments)}}();return(0,l.useEffect)(function(){m!=null&&m.id&&Ut(s()({},y))},[m]),(0,e.jsxs)("div",{className:r.Z.metricTrendSection,children:[(m==null?void 0:m.containsPartitionDimensions)!==!1&&(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("div",{className:r.Z.sectionBox,children:(0,e.jsxs)(Ge.Z,{style:{padding:"10px 10px 0px 10px"},children:[(0,e.jsx)(Me.Z,{flex:"1 1 200px",children:(0,e.jsxs)(Ie.Z,{layout:"inline",colon:!1,onValuesChange:function(we,Se){we.key},children:[(0,e.jsx)(ta.Z,{title:"\u65E5\u671F\u533A\u95F4:",children:(0,e.jsx)(aa,{name:"metricDate",children:(0,e.jsx)(ka,{initialValues:ea(7,t.NU.DAY),showCurrentDataRangeString:!1,onDateRangeChange:function(we,Se){var Ye=f()(we,2),tt=Ye[0],ee=Ye[1],Z=Se.dateSettingType,H=Se.dynamicParams,Re=Se.staticParams,he=mt.hu[t.NU.DAY],ze=t.NU.DAY;t.aN.DYNAMIC===Z&&(he=mt.hu[H.dateRangeType],ze=H.dateRangeType),t.aN.STATIC===Z&&(he=mt.hu[Re.dateRangeType],ze=Re.dateRangeType),Yt({startDate:tt,endDate:ee,dateField:he,period:ze})},disabledAdvanceSetting:!0})})},"metricDate"),(0,e.jsx)(ta.Z,{title:"\u7EF4\u5EA6\u4E0B\u94BB:",children:(0,e.jsx)(aa,{name:"dimensionSelected",children:(0,e.jsx)(Ae.default,{style:{minWidth:150,maxWidth:200},options:A,showSearch:!0,filterOption:function(we,Se){var Ye;return((Ye=Se==null?void 0:Se.label)!==null&&Ye!==void 0?Ye:"").toLowerCase().includes(we.toLowerCase())},mode:"multiple",placeholder:"\u8BF7\u9009\u62E9\u4E0B\u94BB\u7EF4\u5EA6",onChange:function(we){var Se=s()(s()({},y),{},{dimensionGroup:we||[]});F(Se)}})})},"dimensionSelected"),(0,e.jsx)(ta.Z,{title:"\u7EF4\u5EA6\u7B5B\u9009:",children:(0,e.jsx)(aa,{name:"dimensionFilter",children:(0,e.jsx)(J,{modelId:(m==null?void 0:m.modelId)||0,dimensionOptions:A,periodDate:De,onChange:function(we){var Se=we.map(function(tt){var ee=tt.dimensionBizName,Z=tt.dimensionValue,H=tt.operator;return{bizName:ee,value:Z,operator:H}}),Ye=s()(s()({},y),{},{dimensionFilters:Se});F(Ye)},afterSolt:(0,e.jsx)(ae.ZP,{type:"primary",icon:(0,e.jsx)(Xe.Z,{}),size:"middle",loading:x,onClick:function(){Ut(s()({},y))},children:"\u67E5 \u8BE2"})})})},"dimensionFilter")]})}),(0,e.jsx)(Me.Z,{flex:"0 1"})]})}),(0,e.jsx)("div",{className:r.Z.sectionBox,children:(0,e.jsx)(Lt.Z,{size:"small",title:(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("span",{children:"\u6570\u636E\u8D8B\u52BF"}),le&&(0,e.jsx)("div",{style:{color:"#d46b08"},children:le})]}),children:(0,e.jsx)(qe,{data:w,isPer:(E==null?void 0:E.dataFormatType)==="percent"&&(E==null||(a=E.dataFormat)===null||a===void 0?void 0:a.needMultiply100)===!1,isPercent:(E==null?void 0:E.dataFormatType)==="percent"&&(E==null||(c=E.dataFormat)===null||c===void 0?void 0:c.needMultiply100)===!0,rowNumber:P,fields:h.current,loading:x,dateFieldName:De.dateField,groupByDimensionFieldName:Wt,height:350,renderType:"clear",decimalPlaces:(E==null||(d=E.dataFormat)===null||d===void 0?void 0:d.decimalPlaces)||2})})})]}),(0,e.jsx)("div",{className:r.Z.sectionBox,style:{paddingBottom:0},children:(0,e.jsx)(Lt.Z,{size:"small",title:"\u6570\u636E\u660E\u7EC6",collapsible:!0,extra:(0,e.jsxs)(B.Z.Compact,{block:!0,children:[(0,e.jsx)(ae.ZP,{size:"middle",type:"primary",loading:N,disabled:se,onClick:function(){Ut(s()({download:!0},y))},children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(_e.Z,{}),"\u4E0B \u8F7D"]})},"download"),(0,e.jsx)(L.Z,{title:"\u5F00\u542F\u8F6C\u7F6E",children:(0,e.jsx)(ae.ZP,{size:"middle",type:vt?"primary":"default",icon:(0,e.jsx)(ut.Z,{}),onClick:function(){na(!vt)}})})]}),children:(0,e.jsx)("div",{style:{minHeight:"450px"},children:(0,e.jsx)(za,{loading:x,columnConfig:ve,dataSource:w,dateFieldName:De.dateField,metricFieldName:(u=h.current)===null||u===void 0||(u=u[0])===null||u===void 0?void 0:u.column})})})})]})},Ga=$a,st=n(49877),Ea=n(56430),Xa=je.default.TextArea,Ja=function(i){var a=i.metircData,c=(0,l.useState)(),d=f()(c,2),u=d[0],m=d[1],A=[{dataIndex:"name",title:"\u6307\u6807\u540D\u79F0"},{dataIndex:"bizName",title:"\u82F1\u6587\u540D\u79F0"}],j=[{dataIndex:"fieldName",title:"\u5B57\u6BB5\u540D\u79F0"},{dataIndex:"dataType",title:"\u5B57\u6BB5\u7C7B\u578B"}],h=[{dataIndex:"bizName",title:"\u5EA6\u91CF\u540D\u79F0",tooltip:"\u7531\u6A21\u578B\u540D\u79F0_\u5B57\u6BB5\u540D\u79F0\u62FC\u63A5\u800C\u6765"},{dataIndex:"constraint",title:"\u9650\u5B9A\u6761\u4EF6",width:250,tooltip:'\u8BE5\u9650\u5B9A\u6761\u4EF6\u7528\u4E8E\u5728\u8BA1\u7B97\u6307\u6807\u65F6\u9650\u5B9A\u53E3\u5F84\uFF0C\u4F5C\u7528\u4E8E\u5EA6\u91CF\uFF0C\u6240\u7528\u4E8E\u8FC7\u6EE4\u7684\u7EF4\u5EA6\u5FC5\u987B\u5728\u521B\u5EFA\u6A21\u578B\u7684\u65F6\u5019\u88AB\u6807\u8BB0\u4E3A\u65E5\u671F\u6216\u8005\u7EF4\u5EA6\uFF0C\u4E0D\u9700\u8981\u52A0where\u5173\u952E\u5B57\u3002\u6BD4\u5982\uFF1A\u7EF4\u5EA6A="\u503C1" and \u7EF4\u5EA6B="\u503C2"',render:function(M,w){var C=w.constraint;return C?(0,e.jsx)(Xa,{readOnly:!0,value:C}):"--"}},{dataIndex:"agg",title:"\u805A\u5408\u51FD\u6570",width:80,render:function(M){return M||"--"}}];return(0,l.useEffect)(function(){if(a){var p=a.metricDefineType,M=a.metricDefineByFieldParams,w=a.metricDefineByMeasureParams,C=a.metricDefineByMetricParams;switch(p){case mt.DD.FIELD:m({name:"\u6309\u5B57\u6BB5",listName:"\u5B57\u6BB5\u5217\u8868",expr:M.expr,list:M.fields,columns:j});break;case mt.DD.MEASURE:m({name:"\u6309\u5EA6\u91CF",listName:"\u5EA6\u91CF\u5217\u8868",expr:w.expr,list:w.measures,columns:h});break;case mt.DD.METRIC:m({name:"\u6309\u6307\u6807",listName:"\u6307\u6807\u5217\u8868",expr:C.expr,list:C.metrics,columns:A});break;default:break}}},[a]),(0,e.jsx)("div",{className:r.Z.sectionBox,style:{padding:"10px 20px"},children:(0,e.jsxs)("div",{className:r.Z.metricBasicInfo,children:[(0,e.jsx)(st.Z,{justify:"space-between",align:"center",children:(0,e.jsx)("p",{className:r.Z.caliberSubTitle,children:"\u6307\u6807\u4FE1\u606F"})}),(0,e.jsxs)(st.Z,{wrap:"wrap",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u6307\u6807ID\uFF1A"}),a==null?void 0:a.id]}),(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u4E2D\u6587\u540D\uFF1A"}),a==null?void 0:a.name]})]}),(0,e.jsx)(st.Z,{wrap:"wrap",children:(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u82F1\u6587\u540D\uFF1A"}),a==null?void 0:a.bizName]})}),(0,e.jsx)(st.Z,{wrap:"wrap",children:(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u522B\u540D\uFF1A"}),(a==null?void 0:a.alias)&&a.alias.split(",").map(function(p){return(0,e.jsx)(xe.Z,{color:"blue",children:p},p)})]})}),(0,e.jsx)(st.Z,{wrap:"wrap",children:(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u5206\u7C7B\uFF1A"}),Array.isArray(a==null?void 0:a.classifications)&&a.classifications.map(function(p){return(0,e.jsx)(xe.Z,{color:"blue",children:p},p)})]})}),(0,e.jsx)(Ea.Z,{}),(0,e.jsx)(st.Z,{justify:"space-between",align:"center",children:(0,e.jsx)("p",{className:r.Z.caliberSubTitle,children:"\u6A21\u578B\u4FE1\u606F"})}),(0,e.jsxs)(st.Z,{wrap:"wrap",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u6A21\u578B\u540D\uFF1A"}),a==null?void 0:a.modelName]}),(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u6A21\u578BID\uFF1A"})," ",a==null?void 0:a.modelId]})]}),(0,e.jsx)(st.Z,{wrap:"wrap",children:(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u6A21\u578B\u82F1\u6587\u540D\uFF1A"}),a==null?void 0:a.modelBizName]})}),(0,e.jsx)(Ea.Z,{}),(0,e.jsx)(st.Z,{justify:"space-between",align:"center",children:(0,e.jsx)("p",{className:r.Z.caliberSubTitle,children:"\u5B9A\u4E49\u4FE1\u606F"})}),(0,e.jsxs)(st.Z,{wrap:"wrap",children:[(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u5B9A\u4E49\u7C7B\u578B\uFF1A"}),u==null?void 0:u.name]}),(0,e.jsxs)("div",{children:[(0,e.jsx)("span",{className:r.Z.label,children:"\u8868\u8FBE\u5F0F\uFF1A"}),u==null?void 0:u.expr]})]}),(0,e.jsxs)("div",{className:r.Z.label,style:{marginBottom:10},children:[u==null?void 0:u.listName,"\uFF1A"]}),(0,e.jsx)(Na.Z,{className:r.Z.defineDataTable,columns:u==null?void 0:u.columns,dataSource:u==null?void 0:u.list,size:"small",pagination:!1})]})})},Qa=Ja,_a=n(7819),Aa=n(57591),ja=n(11090),qa=n(9835),en=n(27995),tn=n(74310),an=n(60996),nn=Ca.Z.Text,rn=function(i){var a=i.metircData,c=i.relationDimensionOptions,d=i.onDimensionRelationBtnClick;return(0,e.jsxs)("div",{className:r.Z.metricInfoSider,children:[(0,e.jsxs)("div",{className:r.Z.title,children:[(0,e.jsx)("div",{className:r.Z.name,children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(an.Z,{indicatorId:a==null?void 0:a.id,initState:a==null?void 0:a.isCollect}),a==null?void 0:a.name,(a==null?void 0:a.hasAdminRes)&&(0,e.jsx)("span",{className:r.Z.gotoMetricListIcon,onClick:function(){window.open("".concat(Aa.sb,"model/").concat(a.domainId,"/").concat(a.modelId,"/"))},children:(0,e.jsx)(L.Z,{title:"\u524D\u5F80\u6240\u5C5E\u6A21\u578B\u6307\u6807\u5217\u8868",children:(0,e.jsx)(ja.Z,{})})})]})}),(a==null?void 0:a.bizName)&&(0,e.jsx)("div",{className:r.Z.bizName,children:a.bizName})]}),(0,e.jsxs)("div",{className:r.Z.sectionContainer,children:[(0,e.jsx)("hr",{className:r.Z.hr}),(0,e.jsxs)("div",{className:r.Z.section,children:[(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u654F\u611F\u5EA6: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:(a==null?void 0:a.sensitiveLevel)!==void 0&&(0,e.jsx)("span",{children:(0,e.jsx)(xe.Z,{color:mt.D$[a.sensitiveLevel],children:mt.Ze[a.sensitiveLevel]})})})]}),(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u6240\u5C5E\u6A21\u578B: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(xe.Z,{icon:(0,e.jsx)(qa.Z,{}),color:"#3b5999",children:(a==null?void 0:a.modelName)||"\u6A21\u578B\u540D\u4E3A\u7A7A"}),(a==null?void 0:a.hasAdminRes)&&(0,e.jsx)("span",{className:r.Z.gotoMetricListIcon,onClick:function(){window.open("".concat(Aa.sb,"model/").concat(a.domainId,"/0/overview"))},children:(0,e.jsx)(L.Z,{title:"\u524D\u5F80\u6A21\u578B\u8BBE\u7F6E\u9875",children:(0,e.jsx)(ja.Z,{})})})]})})]}),(0,Ne.gP)(a==null?void 0:a.tags)&&(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u522B\u540D: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:(0,e.jsx)(B.Z,{size:2,wrap:!0,children:(0,be.isString)(a==null?void 0:a.alias)&&(a==null?void 0:a.alias.split(",").map(function(u){return(0,e.jsx)(xe.Z,{color:"#eee",style:{borderRadius:44,maxWidth:90,minWidth:40,backgroundColor:"rgba(18, 31, 67, 0.04)"},children:(0,e.jsx)(nn,{style:{maxWidth:80,color:"rgb(95, 116, 141)",textAlign:"center",fontSize:12},ellipsis:{tooltip:u},children:u})},u)}))})})]}),(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u63CF\u8FF0: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:a==null?void 0:a.description})]})]}),(0,e.jsx)("hr",{className:r.Z.hr}),(0,e.jsxs)("div",{className:r.Z.section,children:[(0,e.jsx)("div",{className:r.Z.sectionTitleBox,children:(0,e.jsx)("span",{className:r.Z.sectionTitle,children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(en.Z,{}),"\u521B\u5EFA\u4FE1\u606F"]})})}),(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u521B\u5EFA\u4EBA: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:a==null?void 0:a.createdBy})]}),(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u521B\u5EFA\u65F6\u95F4: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:a!=null&&a.createdAt?Ee()(a==null?void 0:a.createdAt).format("YYYY-MM-DD HH:mm:ss"):""})]}),(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u66F4\u65B0\u65F6\u95F4: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:a!=null&&a.createdAt?Ee()(a==null?void 0:a.updatedAt).format("YYYY-MM-DD HH:mm:ss"):""})]})]}),(0,e.jsx)("hr",{className:r.Z.hr}),(0,e.jsxs)("div",{className:r.Z.section,children:[(0,e.jsx)("div",{className:r.Z.sectionTitleBox,children:(0,e.jsx)("span",{className:r.Z.sectionTitle,children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(tn.Z,{}),"\u5E94\u7528\u4FE1\u606F"]})})}),(0,Ne.gP)(a==null?void 0:a.classifications)&&(0,e.jsxs)("div",{className:r.Z.item,children:[(0,e.jsx)("span",{className:r.Z.itemLable,children:"\u5206\u7C7B: "}),(0,e.jsx)("span",{className:r.Z.itemValue,children:(0,e.jsx)(B.Z,{size:2,wrap:!0,children:a==null?void 0:a.classifications.map(function(u){return(0,e.jsx)(xe.Z,{color:"blue",children:u},u)})})})]})]}),(0,e.jsx)("div",{className:r.Z.ctrlBox,children:(0,e.jsx)("ul",{className:r.Z.ctrlList,children:(0,e.jsx)(L.Z,{title:"\u914D\u7F6E\u4E0B\u94BB\u7EF4\u5EA6\u540E\uFF0C\u5C06\u53EF\u4EE5\u5728\u6307\u6807\u5361\u4E2D\u8FDB\u884C\u4E0B\u94BB",children:(0,e.jsxs)("li",{style:{display:"block"},onClick:function(){d==null||d()},children:[(0,e.jsxs)(B.Z,{style:{width:"100%"},children:[(0,e.jsx)("div",{className:r.Z.subTitle,children:"\u4E0B\u94BB\u7EF4\u5EA6"}),(0,e.jsx)("span",{className:r.Z.ctrlItemIcon,children:(0,e.jsx)(He.Z,{})})]}),(0,Ne.gP)(c)&&(0,e.jsx)("div",{style:{marginLeft:0,marginTop:20},children:(0,e.jsx)(B.Z,{size:5,wrap:!0,children:c.map(function(u){return(0,e.jsx)(xe.Z,{color:"blue",style:{marginRight:0},children:u.label},u.value)})})})]})})})})]})]})},sn=rn,ln=function(){var i=(0,Ue.useParams)(),a=i.metricId,c=(0,l.useState)(!1),d=f()(c,2),u=d[0],m=d[1],A=(0,l.useState)(),j=f()(A,2),h=j[0],p=j[1],M=(0,l.useState)([]),w=f()(M,2),C=w[0],O=w[1],U=(0,l.useState)([]),x=f()(U,2),R=x[0],b=x[1],X=(0,l.useState)([]),E=f()(X,2),z=E[0],q=E[1];(0,l.useEffect)(function(){ie(a),le(a)},[a]);var ie=function(){var G=ce()(D()().mark(function N(v){var S,Y,y,F;return D()().wrap(function(K){for(;;)switch(K.prev=K.next){case 0:if(v){K.next=2;break}return K.abrupt("return");case 2:return K.next=4,(0,_.Zu)(v);case 4:if(S=K.sent,Y=S.code,y=S.data,F=S.msg,Y!==200){K.next=11;break}return p(s()({},y)),K.abrupt("return");case 11:W.ZP.error(F);case 12:case"end":return K.stop()}},N)}));return function(v){return G.apply(this,arguments)}}(),le=function(){var G=ce()(D()().mark(function N(v){var S,Y,y,F,pe;return D()().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return se.next=2,(0,_.BZ)(v);case 2:if(S=se.sent,Y=S.code,y=S.data,F=S.msg,!(Y===200&&Array.isArray(y))){se.next=13;break}return b(y),pe=y.map(function(oe){return oe.dimensionId}),$(pe),se.abrupt("return",y);case 13:O([]),q([]);case 15:return Y!==200&&W.ZP.error(F),se.abrupt("return",[]);case 17:case"end":return se.stop()}},N)}));return function(v){return G.apply(this,arguments)}}(),$=function(){var G=ce()(D()().mark(function N(v){var S,Y,y,F;return D()().wrap(function(K){for(;;)switch(K.prev=K.next){case 0:if(Array.isArray(v)&&v.length>0){K.next=3;break}return q([]),K.abrupt("return");case 3:return K.next=5,(0,_.dt)({ids:v});case 5:if(S=K.sent,Y=S.code,y=S.data,F=S.msg,!(Y===200&&Array.isArray(y==null?void 0:y.list))){K.next=13;break}return O(y.list),q(y.list.map(function(se){return{label:se.name,value:se.bizName,modelId:se.modelId}})),K.abrupt("return",y.list);case 13:return W.ZP.error(F),K.abrupt("return",[]);case 15:case"end":return K.stop()}},N)}));return function(v){return G.apply(this,arguments)}}(),ge=[{key:"metricCaliberInput",label:"\u57FA\u7840\u4FE1\u606F",children:(0,e.jsx)(Qa,{metircData:h})},{key:"metricTrend",label:"\u6307\u6807\u63A2\u7D22",children:(0,e.jsx)(Ga,{metircData:h,relationDimensionOptions:z,dimensionList:C})}];return(0,e.jsx)(e.Fragment,{children:(0,e.jsxs)("div",{className:r.Z.metricDetailWrapper,children:[(0,e.jsxs)("div",{className:r.Z.metricDetail,children:[(0,e.jsx)("div",{className:r.Z.tabContainer,children:(0,e.jsx)(Q.Z,{defaultActiveKey:"metricCaliberInput",items:ge,tabBarExtraContent:{right:(0,e.jsx)(ae.ZP,{size:"middle",type:"link",onClick:function(){Ue.history.push("/metric/market")},children:(0,e.jsxs)(B.Z,{children:[(0,e.jsx)(o.Z,{}),"\u8FD4\u56DE\u5217\u8868\u9875"]})},"backListBtn")},size:"large",className:r.Z.metricDetailTab})}),(0,e.jsx)("div",{className:r.Z.siderContainer,children:(0,e.jsx)(sn,{relationDimensionOptions:z,metircData:h,onDimensionRelationBtnClick:function(){m(!0)}})})]}),(0,e.jsx)(_a.Z,{metricItem:h,relationsInitialValue:R,open:u,onCancel:function(){m(!1)},onSubmit:function(N){ie(a),le(a),m(!1)}})]})})},on=ln},7819:function(dt,Te,n){n.d(Te,{Z:function(){return Ne}});var ye=n(90819),D=n.n(ye),te=n(73193),s=n.n(te),fe=n(89933),ce=n.n(fe),Pe=n(45332),f=n.n(Pe),W=n(44194),Q=n(7477),ae=n(34284),B=n(15783),l=n(55838),_=n(40096),Ue=n(16156),r=n(61338),o=n(5271),at=n.n(o),$e=n(79812),Ie=n(36983),Ge=n(20034),Me=n(9113),Ae=n(57605),L=n(31549),Xe=function(Oe){var be=Oe.metricItem,Ce=Oe.relationsInitialValue,T=Oe.onChange,e=(0,W.useState)([]),nt=f()(e,2),qe=nt[0],xe=nt[1],He=(0,W.useState)({}),yt=f()(He,2),Je=yt[0],Et=yt[1],At=(0,W.useState)([]),ht=f()(At,2),rt=ht[0],St=ht[1],Ct=(0,W.useState)([]),pt=f()(Ct,2),jt=pt[0],Rt=pt[1];(0,W.useEffect)(function(){Ht()},[be,Ce]);var Ht=function(){var Be=ce()(D()().mark(function J(){var ne,me,ue,je;return D()().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,(0,Me.sJ)(be==null?void 0:be.modelId);case 2:ne=t.sent,me=ne.code,ue=ne.data,je=ne.msg,me===200&&Array.isArray(ue)?St(ue):Q.ZP.error(je);case 7:case"end":return t.stop()}},J)}));return function(){return Be.apply(this,arguments)}}();(0,W.useEffect)(function(){var Be=rt.map(function(J){var ne,me=Ae.Kr.DIMENSION,ue=J.id;return s()(s()({},J),{},{transType:me,disabled:(ne=Je[ue])===null||ne===void 0?void 0:ne.inheritedFromModel,key:"".concat(ue)})});Rt(Be)},[Je,rt]),(0,W.useEffect)(function(){if(Array.isArray(Ce)){var Be=Ce.map(function(ne){return"".concat(ne.dimensionId)}),J=Ce.reduce(function(ne,me){var ue=me.dimensionId;return ne[ue]=s()({},me),ne},{});Et(J),xe(Be)}},[Ce]);var Ve=function(J,ne){var me=J.id,ue=s()({},Je),je=ue[me];je?ue[me]=s()(s()({},je),ne):ue[me]=s()({},ne),Et(ue),It(qe,ue)},It=function(J,ne){var me=J.reduce(function(ue,je){var re=ne[je];if(re){if(re.inheritedFromModel===!0&&!re.necessary)return ue;ue.push(re)}else ue.push({dimensionId:Number(je),necessary:!1,inheritedFromModel:!1});return ue},[]);T==null||T(me)},Qt=[{dataIndex:"name",title:"\u540D\u79F0"},{dataIndex:"transType",width:80,title:"\u7C7B\u578B",render:function(J){return(0,L.jsx)($e.Z,{type:J})}},{dataIndex:"y",title:(0,L.jsx)(Ie.Z,{title:"\u662F\u5426\u7ED1\u5B9A",tooltips:"\u82E5\u52FE\u9009\u7ED1\u5B9A\uFF0C\u5219\u5728\u67E5\u8BE2\u8BE5\u6307\u6807\u6570\u636E\u65F6\u5FC5\u987B\u7ED3\u5408\u8BE5\u7EF4\u5EA6\u8FDB\u884C\u67E5\u8BE2"}),width:120,render:function(J,ne){var me,ue=ne.transType,je=ne.id;return ue===Ae.Kr.DIMENSION?(0,L.jsx)(l.Z,{checked:(me=Je[je])===null||me===void 0?void 0:me.necessary,onChange:function(t){Ve(ne,{dimensionId:je,necessary:t.target.checked})},onClick:function(t){t.stopPropagation()}}):(0,L.jsx)(L.Fragment,{})}}],_t=[{dataIndex:"name",title:"\u540D\u79F0"},{dataIndex:"transType",title:"\u7C7B\u578B",render:function(J){return(0,L.jsx)($e.Z,{type:J})}}];return(0,L.jsx)(L.Fragment,{children:(0,L.jsx)(_.Z,{showSearch:!0,titles:["\u672A\u5173\u8054\u7EF4\u5EA6","\u5DF2\u5173\u8054\u7EF4\u5EA6"],dataSource:jt,listStyle:{width:500,height:600},filterOption:function(J,ne){var me=ne.name;return!!me.includes(J)},targetKeys:qe,onChange:function(J){xe(J),It(J,Je)},children:function(J){var ne=J.direction,me=J.filteredItems,ue=J.onItemSelectAll,je=J.onItemSelect,re=J.selectedKeys,t=J.disabled,qt=ne==="left"?_t:Qt,k={getCheckboxProps:function(Fe){return{disabled:t||Fe.disabled}},onSelectAll:function(Fe,Le){var ct=Le.filter(function(it){return!it.disabled}).map(function(it){var ea=it.key;return ea}),Mt=Fe?at()(ct,re):at()(re,ct);ue(Mt,Fe)},onSelect:function(Fe,Le){var ct=Fe.key;je(ct,Le)},selectedRowKeys:re,renderCell:function(Fe,Le,ct,Mt){var it;return((it=Je[Le.id])===null||it===void 0?void 0:it.inheritedFromModel)===!0?(0,L.jsx)(Ue.Z,{title:"\u6765\u81EA\u6A21\u578B\u9ED8\u8BA4\u8BBE\u7F6E\u7EF4\u5EA6",children:(0,L.jsx)(Ge.Z,{style:{color:"#0958d9"}})}):Mt}};return(0,L.jsx)(r.Z,{rowSelection:k,columns:qt,dataSource:me,size:"small",rowClassName:function(Fe){var Le;return(Le=Je[Fe.id])!==null&&Le!==void 0&&Le.inheritedFromModel?"inherit-from-model-row":""},pagination:!1,scroll:{y:450},onRow:function(Fe){var Le=Fe.key,ct=Fe.disabled;return{onClick:function(){ct||t||je(Le,!re.includes(Le))}}}})}})})},_e=Xe,ut=n(51308),gt=function(Oe){var be=Oe.open,Ce=Oe.metricItem,T=Oe.relationsInitialValue,e=Oe.onCancel,nt=Oe.onSubmit,qe=Oe.onRefreshRelationData,xe=(0,W.useState)([]),He=f()(xe,2),yt=He[0],Je=He[1];(0,W.useEffect)(function(){Array.isArray(T)&&Je(T)},[T]);var Et=function(){var ht=ce()(D()().mark(function rt(St){var Ct,pt,jt,Rt;return D()().wrap(function(Ve){for(;;)switch(Ve.prev=Ve.next){case 0:return Ct=s()(s()({},Ce),{},{relateDimension:s()(s()({},(Ce==null?void 0:Ce.relateDimension)||{}),{},{drillDownDimensions:St})}),Ve.next=3,(0,Me.Xk)(Ct);case 3:if(pt=Ve.sent,jt=pt.code,Rt=pt.msg,jt!==200){Ve.next=10;break}return nt(St),qe==null||qe(),Ve.abrupt("return");case 10:Q.ZP.error(Rt);case 11:case"end":return Ve.stop()}},rt)}));return function(St){return ht.apply(this,arguments)}}(),At=function(){return(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(ae.ZP,{onClick:e,children:"\u53D6\u6D88"}),(0,L.jsx)(ae.ZP,{type:"primary",onClick:function(){Ce!=null&&Ce.id?Et(yt):nt(yt)},children:"\u5B8C\u6210"})]})};return(0,L.jsx)(L.Fragment,{children:(0,L.jsx)(B.Z,{width:1200,destroyOnHidden:!0,title:(0,L.jsx)(ut.Z,{title:"\u7EF4\u5EA6\u5173\u8054",subTitle:"\u6CE8\u610F\uFF1A\u5B8C\u6210\u6307\u6807\u4FE1\u606F\u66F4\u65B0\u540E\uFF0C\u7EF4\u5EA6\u5173\u8054\u914D\u7F6E\u4FE1\u606F\u624D\u4F1A\u88AB\u4FDD\u5B58"}),maskClosable:!1,open:be,footer:At(),onCancel:e,children:(0,L.jsx)("div",{style:{display:"flex",justifyContent:"center"},children:(0,L.jsx)(_e,{metricItem:Ce,relationsInitialValue:T,onChange:function(rt){Je(rt)}})})})})},Ne=gt},60996:function(dt,Te,n){n.d(Te,{Z:function(){return at}});var ye=n(90819),D=n.n(ye),te=n(89933),s=n.n(te),fe=n(45332),ce=n.n(fe),Pe=n(7477),f=n(16156),W=n(44194),Q=n(9113),ae=n(12322),B=n(48965),l={collectDashboard:"collectDashboard___o1pSS",dashboardCollected:"dashboardCollected___bLBw0"},_=n(31549),Ue=function(Ie){var Ge=Ie.star,Me=Ge===void 0?!1:Ge,Ae=Ie.onToggleCollect,L=(0,W.useState)(Me),Xe=ce()(L,2),_e=Xe[0],ut=Xe[1];return(0,W.useEffect)(function(){ut(Me)},[Me]),(0,_.jsx)("div",{className:"".concat(l.collectDashboard," ").concat(_e===!0?"dashboardCollected":""),onClick:function(Ne){Ne.stopPropagation(),ut(!_e),Ae(!_e)},children:_e===!1?(0,_.jsx)(ae.Z,{}):(0,_.jsx)(B.Z,{style:{color:"#eac54f"}})})},r=Ue,o=function(Ie){var Ge=Ie.indicatorId,Me=Ie.type,Ae=Me===void 0?"metric":Me,L=Ie.initState,Xe=L===void 0?!1:L,_e=(0,W.useState)(Xe),ut=ce()(_e,2),gt=ut[0],Ne=ut[1];(0,W.useEffect)(function(){Ne(Xe)},[Xe]);var Ot=function(){var Oe=s()(D()().mark(function be(Ce,T){var e,nt,qe;return D()().wrap(function(He){for(;;)switch(He.prev=He.next){case 0:return He.next=2,(0,Q.Pl)({id:Ce,type:Ae,state:T});case 2:e=He.sent,nt=e.code,qe=e.msg,nt===200?Ne(T):Pe.ZP.error(qe);case 6:case"end":return He.stop()}},be)}));return function(Ce,T){return Oe.apply(this,arguments)}}();return(0,_.jsx)(f.Z,{title:"".concat(gt?"\u53D6\u6D88":"\u52A0\u5165","\u6536\u85CF"),children:(0,_.jsx)("div",{children:(0,_.jsx)(r,{star:gt,onToggleCollect:function(be){Ot(Ge,be)}})})})},at=o},36983:function(dt,Te,n){var ye=n(73193),D=n.n(ye),te=n(84176),s=n.n(te),fe=n(2206),ce=n(16156),Pe=n(44194),f=n(15722),W=n(31549),Q=["title","tooltips"],ae=function(l){var _=l.title,Ue=l.tooltips,r=s()(l,Q);return(0,W.jsx)(W.Fragment,{children:(0,W.jsxs)(fe.Z,{children:[(0,W.jsx)("span",{children:_}),(0,W.jsx)(ce.Z,D()(D()({title:Ue},r),{},{children:(0,W.jsx)(f.Z,{})}))]})})};Te.Z=ae},79812:function(dt,Te,n){var ye=n(17258),D=n(44194),te=n(57605),s=n(31549),fe=function(Pe){var f=Pe.type;return(0,s.jsx)(s.Fragment,{children:f===te.Z.DIMENSION?(0,s.jsx)(ye.Z,{color:"blue",children:"\u7EF4\u5EA6"}):f===te.Z.METRIC?(0,s.jsx)(ye.Z,{color:"orange",children:"\u6307\u6807"}):f===te.Z.DATASOURCE?(0,s.jsx)(ye.Z,{color:"green",children:"\u6A21\u578B"}):f===te.Z.TAG?(0,s.jsx)(ye.Z,{color:"green",children:"\u6807\u7B7E"}):(0,s.jsx)(s.Fragment,{})})};Te.Z=fe},97180:function(dt,Te,n){n.d(Te,{D$:function(){return W},DD:function(){return l},Ze:function(){return Pe},hu:function(){return ae},iw:function(){return Q},sz:function(){return _},uc:function(){return ce},x1:function(){return Ue}});var ye=n(10154),D=n.n(ye),te=n(57605),s=n(42520),fe=function(r){return r[r.LOW=0]="LOW",r[r.MID=1]="MID",r[r.HIGH=2]="HIGH",r}({}),ce=[{label:"\u666E\u901A",value:fe.LOW},{label:"\u91CD\u8981",value:fe.MID},{label:"\u6838\u5FC3",value:fe.HIGH}],Pe=ce.reduce(function(r,o){var at=o.label,$e=o.value;return r[$e]=at,r},{}),f={1:"\u662F",0:"\u5426"},W=D()(D()(D()({},fe.LOW,"default"),fe.MID,"orange"),fe.HIGH,"volcano"),Q=D()(D()(D()({},te.Z.DATASOURCE,{label:"\u6A21\u578B",value:te.Z.DATASOURCE,color:"cyan"}),te.Z.DIMENSION,{label:"\u7EF4\u5EA6",value:te.Z.DIMENSION,color:"blue"}),te.Z.METRIC,{label:"\u6307\u6807",value:te.Z.METRIC,color:"orange"}),ae=D()(D()(D()({},s.NU.DAY,"sys_imp_date"),s.NU.WEEK,"sys_imp_week"),s.NU.MONTH,"sys_imp_month"),B={sys_imp_date:s.NU.DAY,sys_imp_week:s.NU.WEEK,sys_imp_month:s.NU.MONTH},l=function(r){return r.FIELD="FIELD",r.MEASURE="MEASURE",r.METRIC="METRIC",r}({}),_=function(r){return r.FIELD="FIELD",r.DIMENSION="DIMENSION",r.METRIC="METRIC",r}({}),Ue=D()(D()(D()({},_.FIELD,"\u5B57\u6BB5"),_.DIMENSION,"\u7EF4\u5EA6"),_.METRIC,"\u6307\u6807")},57605:function(dt,Te,n){n.d(Te,{C3:function(){return Q},E0:function(){return ae},Kr:function(){return s},MU:function(){return Ue},Mq:function(){return l},Or:function(){return _},Z:function(){return fe},cX:function(){return B},fs:function(){return te},lf:function(){return W},nG:function(){return r},sB:function(){return f},v8:function(){return Pe}});var ye=n(10154),D=n.n(ye),te=function(o){return o.TAG="TAG",o.METRIC="METRIC",o}({}),s=function(o){return o.DIMENSION="DIMENSION",o.METRIC="METRIC",o.TAG="TAG",o}({}),fe=function(o){return o.DATASOURCE="DATASOURCE",o.DIMENSION="DIMENSION",o.METRIC="METRIC",o.TAG="TAG",o}({}),ce=function(o){return o.ATOMIC="\u539F\u5B50\u6307\u6807",o.DERIVED="\u884D\u751F\u6307\u6807",o}({}),Pe=function(o){return o.initial="--",o.error="\u9519\u8BEF",o.pending="\u7B49\u5F85",o.running="\u6B63\u5728\u6267\u884C",o.success="\u6210\u529F",o.unknown="\u672A\u77E5",o}({}),f=function(o){return o[o.UNKNOWN=-1]="UNKNOWN",o[o.INITIALIZED=0]="INITIALIZED",o[o.ONLINE=1]="ONLINE",o[o.OFFLINE=2]="OFFLINE",o[o.DELETED=3]="DELETED",o[o.UNAVAILABLE=4]="UNAVAILABLE",o}({}),W=function(o){return o.EQUAL="=",o.IN="IN",o.LIKE="LIKE",o}({}),Q=function(o){return o.DIMENSION="DIMENSION",o.TAG="TAG",o.METRIC="METRIC",o.DOMAIN="DOMAIN",o.ENTITY="ENTITY",o.VIEW="VIEW",o.MODEL="MODEL",o.UNKNOWN="UNKNOWN",o}({}),ae=D()(D()(D()(D()(D()(D()(D()(D()({},Q.DIMENSION,"\u7EF4\u5EA6"),Q.TAG,"\u6807\u7B7E"),Q.METRIC,"\u6307\u6807"),Q.DOMAIN,"\u57DF"),Q.ENTITY,"\u5B9E\u4F53"),Q.VIEW,"\u89C6\u56FE"),Q.MODEL,"\u6A21\u578B"),Q.UNKNOWN,"\u672A\u77E5"),B=function(o){return o.ONLINE="ONLINE",o.OFFLINE="OFFLINE",o.DELETED="DELETED",o.INITIALIZED="INITIALIZED",o.UNAVAILABLE="UNAVAILABLE",o.UNKNOWN="UNKNOWN",o}({}),l=D()(D()({},te.TAG,"detailTypeDefaultConfig"),te.METRIC,"aggregateTypeDefaultConfig"),_=function(o){return o.LAST="LAST",o.RECENT="RECENT",o.CURRENT="CURRENT",o}({}),Ue=function(o){return o.DAY="DAY",o.WEEK="WEEK",o.MONTH="MONTH",o.YEAR="YEAR",o}({}),r=function(o){return o.BLACK_LIST="blackList",o.WHITE_LIST="whiteList",o.RULE_LIST="ruleList",o}({})},64941:function(dt,Te){Te.Z={metricWrapper:"metricWrapper___wwgLL",metricFilterWrapper:"metricFilterWrapper____fq3X",metricTable:"metricTable___cGqhC",table:"table___SzbAN",metricBasicInfo:"metricBasicInfo___RAR2R",label:"label___WmAOz",caliberTitle:"caliberTitle___zbjIL",caliberSubTitle:"caliberSubTitle___o98BP",defineDataTable:"defineDataTable___gGj91",caliberDesc:"caliberDesc___BZ87n",searchBox:"searchBox___IxgjW",searchInput:"searchInput___XC9xj",overviewExtraContainer:"overviewExtraContainer___DS9uf",extraWrapper:"extraWrapper___rhaLf",extraStatistic:"extraStatistic___d7F3I",extraTitle:"extraTitle___lb_dw",extraValue:"extraValue___AWxHT",metricEditWrapper:"metricEditWrapper___OVAsk",metricDetailTab:"metricDetailTab___PP0ur",metricDetail:"metricDetail___alfgr",tabContainer:"tabContainer___FFoO4",metricInfoContent:"metricInfoContent___dMpls",title:"title___fpZ1P",siderContainer:"siderContainer___wgqBl",metricDetailWrapper:"metricDetailWrapper___eIWoI",sectionBox:"sectionBox___A7S5_",metricInfoSider:"metricInfoSider___akvJt",createTitle:"createTitle___w5Vf1",gotoMetricListIcon:"gotoMetricListIcon___uEtH5",name:"name___nnlNE",bizName:"bizName___zATXh",desc:"desc___JuUdI",subTitle:"subTitle___zya5g",sectionContainer:"sectionContainer___yVkzz",section:"section___vePzi",sectionTitleBox:"sectionTitleBox___Jh_GT",sectionTitle:"sectionTitle___LGxWF",item:"item___txXyB",itemLable:"itemLable___N2ZQj",itemValue:"itemValue___nVhDP",hr:"hr___S7gRj",ctrlBox:"ctrlBox___jYK7q",ctrlList:"ctrlList___PrIhf",ctrlItemIcon:"ctrlItemIcon___IVUOy",styles:"styles___cWI79",ctrlItemLable:"ctrlItemLable___zhFOm",settingList:"settingList___lVgZm",active:"active___Hi32t",icon:"icon___oDucA",content:"content___xc_h5",text:"text___Tb9jb"}}}]);
