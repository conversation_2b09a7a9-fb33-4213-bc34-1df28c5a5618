"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[378],{52465:function(rt,ie,n){n.r(ie),n.d(ie,{default:function(){return ve}});var v=n(73193),I=n.n(v),q=n(90819),F=n.n(q),ce=n(89933),B=n.n(ce),$=n(45332),C=n.n($),z=n(94202),de=n(10162),w=n(46504),Oe=n(7477),Me=n(17258),Se=n(47786),_=n(26574),ee=n(34284),ye=n(61338),$e=n(84025),we=n.n($e),h=n(44194),te={WEB_PAGE:"Web\u9875\u9762",WEB_SERVICE:"Web\u670D\u52A1"},lt={EMBEDDING_RECALL:"\u5411\u91CF\u53EC\u56DE",FUNCTION_CALL:"\u51FD\u6570\u8C03\u7528"},ut={WIDGET:"blue",DASHBOARD:"volcano",URL:"purple",TAG:"cyan"},Ue=n(10154),ze=n.n(Ue),fe=n(89957),Ge=n.n(fe),He=n(76711),U=n.n(He),me=n(20263),Qe=n(15783),e=n(45826),d=n(34044),g=n(37069),u=n(87411),c=function(D){return D.WEB_PAGE="WEB_PAGE",D.WEB_SERVICE="WEB_SERVICE",D.NL2SQL_LLM="NL2SQL_LLM",D}({}),A=function(D){return D.EMBEDDING_RECALL="EMBEDDING_RECALL",D.FUNCTION_CALL="FUNCTION_CALL",D}({}),y=function(D){return D.CUSTOM="CUSTOM",D.SEMANTIC="SEMANTIC",D.FORWARD="FORWARD",D}({}),i={pluginManage:"pluginManage___CHe6K",filterSection:"filterSection___ZOaTd",filterItem:"filterItem___RpZPX",filterItemTitle:"filterItemTitle___ztN_k",filterItemControl:"filterItemControl___ZxrU1",pluginList:"pluginList___dinmX",titleBar:"titleBar___XBOy1",title:"title___V0Wwv",modelColumn:"modelColumn___dI2x7",operator:"operator___s3ORQ",paramsSection:"paramsSection___LxqCq",filterRow:"filterRow___quci2",filterParamName:"filterParamName___F2v91",filterParamValueField:"filterParamValueField___C4l8J",questionExample:"questionExample___kSZrf"},V=n(12820),Q=n(39378),t=n(31549),T=me.Z.Item,G=w.default.TextArea,Fe=function(X){var p=X.detail,J=X.onSubmit,je=X.onCancel,L=(0,h.useState)([]),ae=C()(L,2),ne=ae[0],De=ae[1],Ne=(0,h.useState)({}),Y=C()(Ne,2),K=Y[0],ge=Y[1],he=(0,h.useState)(!1),re=C()(he,2),le=re[0],W=re[1],Ie=(0,h.useState)(),b=C()(Ie,2),ue=b[0],k=b[1],Xe=(0,h.useState)(),Te=C()(Xe,2),Le=Te[0],Ze=Te[1],Je=(0,h.useState)([]),Pe=C()(Je,2),Re=Pe[0],Be=Pe[1],Ye=(0,h.useState)([]),Ae=C()(Ye,2),oe=Ae[0],se=Ae[1],Ke=(0,h.useState)([]),be=C()(Ke,2),P=be[0],O=be[1],ke=(0,h.useState)([]),H=C()(ke,2),qe=H[0],Ve=H[1],_e=me.Z.useForm(),et=C()(_e,1),Ce=et[0],We=function(){var o=B()(F()().mark(function r(){var a,l;return F()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:return x.next=2,(0,u.fZ)();case 2:a=x.sent,l=(0,z.Dr)(a.data,function(f){var j;f.title=f.name,f.value=f.type==="DOMAIN"?"DOMAIN_".concat(f.id):f.id,f.checkable=f.type==="DATASET"||f.type==="DOMAIN"&&((j=f.children)===null||j===void 0?void 0:j.length)>0}),De([{title:"\u9ED8\u8BA4",value:-1,type:"DATASET"}].concat(U()(l)));case 5:case"end":return x.stop()}},r)}));return function(){return o.apply(this,arguments)}}();(0,h.useEffect)(function(){We()},[]);var tt=function(){var o=B()(F()().mark(function r(a){var l,S;return F()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return l=a.filter(function(j){return!!j.dataSetId}).map(function(j){return j.dataSetId}),f.next=3,Promise.all(l.map(function(j){return(0,u.Z7)(j)}));case 3:S=f.sent,ge(l.reduce(function(j,pe,R){return j[pe]=S[R].data.dimensions,j},{}));case 5:case"end":return f.stop()}},r)}));return function(a){return o.apply(this,arguments)}}();(0,h.useEffect)(function(){if(p){var o,r,a=p.config||{},l=a.paramOptions,S=l==null||(o=l.find(function(R){return R.paramType==="FORWARD"&&R.key==="height"}))===null||o===void 0?void 0:o.value;if(Ce.setFieldsValue(I()(I()({},p),{},{url:(r=p.config)===null||r===void 0?void 0:r.url,height:S})),Ve(p.dataSetList||[]),(l==null?void 0:l.length)>0){var x=l.filter(function(R){return R.paramType!==y.FORWARD});O(x),tt(x)}k(p.type);var f=JSON.parse(p.parseModeConfig||"{}");Ze(f.name);var j=f.parameters||{},pe=j.properties;Be(pe?Object.keys(pe).map(function(R,nt){return{id:"".concat(nt),name:R,type:pe[R].type,description:pe[R].description}}):[]),se(f.examples?f.examples.map(function(R,nt){return{id:nt,question:R}}):[])}},[p]);var at={labelCol:{span:4},wrapperCol:{span:20}},m=function(r){return{name:Le,description:r,parameters:{type:"object",properties:Re.filter(function(a){var l;return!!((l=a.name)!==null&&l!==void 0&&l.trim())}).reduce(function(a,l){return a[l.name||""]={type:l.type,description:l.description},a},{}),required:Re.filter(function(a){var l;return!!((l=a.name)!==null&&l!==void 0&&l.trim())}).map(function(a){return a.name})},examples:oe.filter(function(a){var l;return!!((l=a.question)!==null&&l!==void 0&&l.trim())}).map(function(a){return a.question})}},s=function(){var o=B()(F()().mark(function r(){var a,l,S;return F()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return f.next=2,Ce.validateFields();case 2:return a=f.sent,W(!0),l=(0,Q.isArray)(P)?P==null?void 0:P.filter(function(j){return Ge()(j)==="object"&&(j.paramType!==null||j.value!=null)}):[],l=l.concat([{paramType:y.FORWARD,key:"height",value:a.height||void 0}]),S={url:a.url,paramOptions:l},f.next=9,(0,u.iS)(I()(I()({},a),{},{id:p==null?void 0:p.id,modelList:(0,Q.isArray)(a.modelList)?a.modelList:[a.modelList],config:JSON.stringify(S),parseModeConfig:JSON.stringify(m(a.pattern))}));case 9:W(!1),J(a),Oe.ZP.success(p!=null&&p.id?"\u7F16\u8F91\u6210\u529F":"\u65B0\u5EFA\u6210\u529F");case 12:case"end":return f.stop()}},r)}));return function(){return o.apply(this,arguments)}}(),N=function(){var o=B()(F()().mark(function r(a){var l;return F()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:if(!K[a]){x.next=2;break}return x.abrupt("return");case 2:return x.next=4,(0,u.Z7)(a);case 4:l=x.sent,ge(I()(I()({},K),{},ze()({},a,l.data.dimensions)));case 6:case"end":return x.stop()}},r)}));return function(a){return o.apply(this,arguments)}}(),M=function(){var r=[];return(0,z.Dr)(ne,function(a){a.type==="DATASET"&&qe.includes(a.id)&&r.push(a)}),r},E=function(r){r.dataSetList&&Ve(r.dataSetList)};return(0,t.jsx)(Qe.Z,{open:!0,title:p?"\u7F16\u8F91\u63D2\u4EF6":"\u65B0\u5EFA\u63D2\u4EF6",width:920,confirmLoading:le,onOk:s,onCancel:je,children:(0,t.jsxs)(me.Z,I()(I()({},at),{},{form:Ce,style:{maxWidth:820},onValuesChange:E,children:[(0,t.jsx)(T,{name:"dataSetList",label:"\u6570\u636E\u96C6",children:(0,t.jsx)(e.Z,{treeData:ne,placeholder:"\u8BF7\u9009\u62E9\u6570\u636E\u96C6",multiple:!0,treeCheckable:!0,allowClear:!0})}),(0,t.jsx)(T,{name:"name",label:"\u63D2\u4EF6\u540D\u79F0",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u63D2\u4EF6\u540D\u79F0"}],children:(0,t.jsx)(w.default,{placeholder:"\u8BF7\u8F93\u5165\u63D2\u4EF6\u540D\u79F0",allowClear:!0})}),(0,t.jsx)(T,{name:"type",label:"\u63D2\u4EF6\u7C7B\u578B",rules:[{required:!0,message:"\u8BF7\u9009\u62E9\u63D2\u4EF6\u7C7B\u578B"}],children:(0,t.jsx)(_.default,{placeholder:"\u8BF7\u9009\u62E9\u63D2\u4EF6\u7C7B\u578B",options:Object.keys(te).map(function(o){return{label:te[o],value:o}}),onChange:function(r){k(r),r===c.NL2SQL_LLM&&(Ce.setFieldsValue({parseMode:A.FUNCTION_CALL}),Be([{id:(0,z.Vj)(),name:"query_text",type:"string",description:"\u7528\u6237\u7684\u539F\u59CB\u81EA\u7136\u8BED\u8A00\u67E5\u8BE2"}]))}})}),(0,t.jsx)(T,{label:"\u51FD\u6570\u540D\u79F0",children:(0,t.jsx)(w.default,{value:Le,onChange:function(r){Ze(r.target.value)},placeholder:"\u8BF7\u8F93\u5165\u51FD\u6570\u540D\u79F0\uFF0C\u53EA\u80FD\u5305\u542B\u56E0\u4E3A\u5B57\u6BCD\u548C\u4E0B\u5212\u7EBF",allowClear:!0})}),(0,t.jsx)(T,{name:"pattern",label:"\u51FD\u6570\u63CF\u8FF0",children:(0,t.jsx)(G,{placeholder:"\u8BF7\u8F93\u5165\u51FD\u6570\u63CF\u8FF0\uFF0C\u591A\u4E2A\u63CF\u8FF0\u6362\u884C\u5206\u9694",allowClear:!0})}),(0,t.jsx)(T,{name:"exampleQuestions",label:"\u793A\u4F8B\u95EE\u9898",children:(0,t.jsxs)("div",{className:i.paramsSection,children:[oe.map(function(o){var r=o.id,a=o.question;return(0,t.jsxs)("div",{className:i.filterRow,children:[(0,t.jsx)(w.default,{placeholder:"\u793A\u4F8B\u95EE\u9898",value:a,className:i.questionExample,onChange:function(S){o.question=S.target.value,se(U()(oe))},allowClear:!0}),(0,t.jsx)(V.Z,{onClick:function(){se(oe.filter(function(S){return S.id!==r}))}})]},r)}),(0,t.jsxs)(ee.ZP,{onClick:function(){se([].concat(U()(oe),[{id:(0,z.Vj)()}]))},children:[(0,t.jsx)(de.Z,{}),"\u65B0\u589E\u793A\u4F8B\u95EE\u9898"]})]})}),(ue===c.WEB_PAGE||ue===c.WEB_SERVICE)&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(T,{name:"url",label:"\u5730\u5740",rules:[{required:!0,message:"\u8BF7\u8F93\u5165\u5730\u5740"}],children:(0,t.jsx)(w.default,{placeholder:"\u8BF7\u8F93\u5165\u5730\u5740",allowClear:!0})}),(0,t.jsx)(T,{name:"params",label:"\u51FD\u6570\u53C2\u6570",children:(0,t.jsxs)("div",{className:i.paramsSection,children:[P.map(function(o){return(0,t.jsxs)("div",{className:i.filterRow,children:[(0,t.jsx)(w.default,{placeholder:"\u53C2\u6570\u540D\u79F0",value:o.key,className:i.filterParamName,onChange:function(a){o.key=a.target.value,O(U()(P))},allowClear:!0}),(0,t.jsxs)(d.ZP.Group,{onChange:function(a){o.paramType=a.target.value,O(U()(P))},value:o.paramType,children:[(0,t.jsx)(d.ZP,{value:y.SEMANTIC,children:"\u7EF4\u5EA6"}),(0,t.jsx)(d.ZP,{value:y.CUSTOM,children:"\u81EA\u5B9A\u4E49"})]}),o.paramType===y.CUSTOM&&(0,t.jsx)(w.default,{placeholder:"\u8BF7\u8F93\u5165",value:o.value,className:i.filterParamValueField,onChange:function(a){o.value=a.target.value,O(U()(P))},allowClear:!0}),o.paramType===y.SEMANTIC&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(_.default,{placeholder:"\u6570\u636E\u96C6",options:M().map(function(r){return{label:r.name,value:r.id}}),showSearch:!0,filterOption:function(a,l){var S;return((S=l==null?void 0:l.label)!==null&&S!==void 0?S:"").toLowerCase().includes(a.toLowerCase())},className:i.filterParamName,allowClear:!0,value:o.dataSetId,onChange:function(a){o.dataSetId=a,O(U()(P)),N(a)}}),(0,t.jsx)(_.default,{placeholder:"\u8BF7\u9009\u62E9\u7EF4\u5EA6\uFF0C\u9700\u5148\u9009\u62E9\u6570\u636E\u96C6",options:(K[o.dataSetId]||[]).map(function(r){return{label:r.name,value:"".concat(r.id)}}),showSearch:!0,className:i.filterParamValueField,filterOption:function(a,l){var S;return((S=l==null?void 0:l.label)!==null&&S!==void 0?S:"").toLowerCase().includes(a.toLowerCase())},allowClear:!0,value:o.elementId,onChange:function(a){o.elementId=a,O(U()(P))}})]}),(0,t.jsx)(V.Z,{onClick:function(){O(P.filter(function(a){return a.id!==o.id}))}})]},o.id)}),(0,t.jsxs)(ee.ZP,{onClick:function(){O([].concat(U()(P),[{id:(0,z.Vj)(),key:void 0,value:void 0}]))},children:[(0,t.jsx)(de.Z,{}),"\u65B0\u589E\u53C2\u6570"]})]})})]}),(0,t.jsx)(T,{name:"height",label:"\u9AD8\u5EA6",children:(0,t.jsx)(g.Z,{placeholder:"\u5355\u4F4Dpx"})})]}))})},Ee=Fe,Z=w.default.Search,xe=function(){var X=(0,h.useState)(),p=C()(X,2),J=p[0],je=p[1],L=(0,h.useState)(),ae=C()(L,2),ne=ae[0],De=ae[1],Ne=(0,h.useState)(),Y=C()(Ne,2),K=Y[0],ge=Y[1],he=(0,h.useState)(),re=C()(he,2),le=re[0],W=re[1],Ie=(0,h.useState)([]),b=C()(Ie,2),ue=b[0],k=b[1],Xe=(0,h.useState)([]),Te=C()(Xe,2),Le=Te[0],Ze=Te[1],Je=(0,h.useState)(!1),Pe=C()(Je,2),Re=Pe[0],Be=Pe[1],Ye=(0,h.useState)(),Ae=C()(Ye,2),oe=Ae[0],se=Ae[1],Ke=(0,h.useState)(!1),be=C()(Ke,2),P=be[0],O=be[1],ke=function(){var m=B()(F()().mark(function s(){var N;return F()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return E.next=2,(0,u.fZ)();case 2:N=E.sent,Ze((0,z.QT)(N.data));case 4:case"end":return E.stop()}},s)}));return function(){return m.apply(this,arguments)}}(),H=function(){var m=B()(F()().mark(function s(N){var M,E;return F()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return Be(!0),r.next=3,(0,u.UV)(I()({name:J,type:ne,pattern:K,model:le},N||{}));case 3:E=r.sent,Be(!1),k(((M=E.data)===null||M===void 0?void 0:M.map(function(a){return I()(I()({},a),{},{config:JSON.parse(a.config||"{}")})}))||[]);case 6:case"end":return r.stop()}},s)}));return function(N){return m.apply(this,arguments)}}();(0,h.useEffect)(function(){ke(),H()},[]);var qe=function(s){se(s),O(!0)},Ve=function(){var m=B()(F()().mark(function s(N){return F()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return E.next=2,(0,u.f$)(N.id);case 2:Oe.ZP.success("\u63D2\u4EF6\u5220\u9664\u6210\u529F"),H();case 4:case"end":return E.stop()}},s)}));return function(N){return m.apply(this,arguments)}}(),_e=[{title:"\u63D2\u4EF6\u540D\u79F0",dataIndex:"name",key:"name"},{title:"\u6570\u636E\u96C6",dataIndex:"dataSetList",key:"dataSetList",width:200,render:function(s){return s!=null&&s.includes(-1)?"\u9ED8\u8BA4":s?(0,t.jsx)("div",{className:i.modelColumn,children:s.map(function(N){var M,E=(M=Le.find(function(o){return o.id===N}))===null||M===void 0?void 0:M.name;return E?(0,t.jsx)(Me.Z,{children:E},N):null})}):"-"}},{title:"\u63D2\u4EF6\u7C7B\u578B",dataIndex:"type",key:"type",render:function(s){return(0,t.jsx)(Me.Z,{color:s===c.WEB_PAGE?"blue":"cyan",children:te[s]})}},{title:"\u51FD\u6570\u63CF\u8FF0",dataIndex:"pattern",key:"pattern",width:450},{title:"\u66F4\u65B0\u4EBA",dataIndex:"updatedBy",key:"updatedBy",render:function(s){return s||"-"}},{title:"\u66F4\u65B0\u65F6\u95F4",dataIndex:"updatedAt",key:"updatedAt",render:function(s){return s?we()(s).format("YYYY-MM-DD HH:mm"):"-"}},{title:"\u64CD\u4F5C",dataIndex:"x",key:"x",render:function(s,N){return(0,t.jsxs)("div",{className:i.operator,children:[(0,t.jsx)("a",{onClick:function(){qe(N)},children:"\u7F16\u8F91"}),(0,t.jsx)(Se.Z,{title:"\u786E\u5B9A\u5220\u9664\u5417\uFF1F",onConfirm:function(){Ve(N)},children:(0,t.jsx)("a",{children:"\u5220\u9664"})})]})}}],et=function(s){W(s),H({model:s})},Ce=function(s){De(s),H({type:s})},We=function(){H()},tt=function(){se(void 0),O(!0)},at=function(){O(!1),H()};return(0,t.jsxs)("div",{className:i.pluginManage,children:[(0,t.jsxs)("div",{className:i.filterSection,children:[(0,t.jsxs)("div",{className:i.filterItem,children:[(0,t.jsx)("div",{className:i.filterItemTitle,children:"\u4E3B\u9898\u57DF"}),(0,t.jsx)(_.default,{className:i.filterItemControl,placeholder:"\u8BF7\u9009\u62E9\u4E3B\u9898\u57DF",options:Le.map(function(m){return{label:m.name,value:m.id}}),value:le,allowClear:!0,onChange:et})]}),(0,t.jsxs)("div",{className:i.filterItem,children:[(0,t.jsx)("div",{className:i.filterItemTitle,children:"\u63D2\u4EF6\u540D\u79F0"}),(0,t.jsx)(Z,{className:i.filterItemControl,placeholder:"\u8BF7\u8F93\u5165\u63D2\u4EF6\u540D\u79F0",value:J,onChange:function(s){je(s.target.value)},onSearch:We})]}),(0,t.jsxs)("div",{className:i.filterItem,children:[(0,t.jsx)("div",{className:i.filterItemTitle,children:"\u51FD\u6570\u63CF\u8FF0"}),(0,t.jsx)(Z,{className:i.filterItemControl,placeholder:"\u8BF7\u8F93\u5165\u51FD\u6570\u63CF\u8FF0",value:K,onChange:function(s){ge(s.target.value)},onSearch:We})]}),(0,t.jsxs)("div",{className:i.filterItem,children:[(0,t.jsx)("div",{className:i.filterItemTitle,children:"\u63D2\u4EF6\u7C7B\u578B"}),(0,t.jsx)(_.default,{className:i.filterItemControl,placeholder:"\u8BF7\u9009\u62E9\u63D2\u4EF6\u7C7B\u578B",options:Object.keys(te).map(function(m){return{label:te[m],value:m}}),value:ne,allowClear:!0,onChange:Ce})]})]}),(0,t.jsxs)("div",{className:i.pluginList,children:[(0,t.jsxs)("div",{className:i.titleBar,children:[(0,t.jsx)("div",{className:i.title,children:"\u63D2\u4EF6\u5217\u8868"}),(0,t.jsxs)(ee.ZP,{type:"primary",onClick:tt,children:[(0,t.jsx)(de.Z,{}),"\u65B0\u5EFA\u63D2\u4EF6"]})]}),(0,t.jsx)(ye.Z,{columns:_e,dataSource:ue,size:"small",pagination:{defaultPageSize:20},loading:Re})]}),P&&(0,t.jsx)(Ee,{detail:oe,onSubmit:at,onCancel:function(){O(!1)}})]})},ve=xe},87411:function(rt,ie,n){n.d(ie,{UV:function(){return q},Z7:function(){return B},f$:function(){return F},fZ:function(){return ce},iS:function(){return I}});var v=n(20221);function I($){return(0,v.request)("/api/chat/plugin",{method:$.id?"PUT":"POST",data:$})}function q($){return(0,v.request)("/api/chat/plugin/query",{method:"POST",data:$})}function F($){return(0,v.request)("/api/chat/plugin/".concat($),{method:"DELETE"})}function ce(){return(0,v.request)("/api/chat/conf/getDomainDataSetTree",{method:"GET"})}function B($){return(0,v.request)("/api/chat/conf/getDataSetSchema/".concat($),{method:"GET"})}},17258:function(rt,ie,n){n.d(ie,{Z:function(){return Qe}});var v=n(44194),I=n(51865),q=n.n(I),F=n(34573),ce=n(9695),B=n(71841),$=n(11778),C=n(26833),z=n(47506),de=n(40044),w=n(87471),Oe=n(19107),Me=n(77167),Se=n(88370);const _=e=>{const{paddingXXS:d,lineWidth:g,tagPaddingHorizontal:u,componentCls:c,calc:A}=e,y=A(u).sub(g).equal(),i=A(d).sub(g).equal();return{[c]:Object.assign(Object.assign({},(0,Oe.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:y,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,de.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${c}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${c}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${c}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${c}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:y}}),[`${c}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},ee=e=>{const{lineWidth:d,fontSizeIcon:g,calc:u}=e,c=e.fontSizeSM;return(0,Me.mergeToken)(e,{tagFontSize:c,tagLineHeight:(0,de.unit)(u(e.lineHeightSM).mul(c).equal()),tagIconSize:u(g).sub(u(d).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},ye=e=>({defaultBg:new w.FastColor(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var $e=(0,Se.I$)("Tag",e=>{const d=ee(e);return _(d)},ye),we=function(e,d){var g={};for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&d.indexOf(u)<0&&(g[u]=e[u]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(e);c<u.length;c++)d.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(e,u[c])&&(g[u[c]]=e[u[c]]);return g},te=v.forwardRef((e,d)=>{const{prefixCls:g,style:u,className:c,checked:A,onChange:y,onClick:i}=e,V=we(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:Q,tag:t}=v.useContext(z.E_),T=ve=>{y==null||y(!A),i==null||i(ve)},G=Q("tag",g),[Fe,Ee,Z]=$e(G),xe=q()(G,`${G}-checkable`,{[`${G}-checkable-checked`]:A},t==null?void 0:t.className,c,Ee,Z);return Fe(v.createElement("span",Object.assign({},V,{ref:d,style:Object.assign(Object.assign({},u),t==null?void 0:t.style),className:xe,onClick:T})))}),lt=n(14325);const ut=e=>(0,lt.Z)(e,(d,{textColor:g,lightBorderColor:u,lightColor:c,darkColor:A})=>({[`${e.componentCls}${e.componentCls}-${d}`]:{color:g,background:c,borderColor:u,"&-inverse":{color:e.colorTextLightSolid,background:A,borderColor:A},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}));var Ue=(0,Se.bk)(["Tag","preset"],e=>{const d=ee(e);return ut(d)},ye);function ze(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const fe=(e,d,g)=>{const u=ze(g);return{[`${e.componentCls}${e.componentCls}-${d}`]:{color:e[`color${g}`],background:e[`color${u}Bg`],borderColor:e[`color${u}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var Ge=(0,Se.bk)(["Tag","status"],e=>{const d=ee(e);return[fe(d,"success","Success"),fe(d,"processing","Info"),fe(d,"error","Error"),fe(d,"warning","Warning")]},ye),He=function(e,d){var g={};for(var u in e)Object.prototype.hasOwnProperty.call(e,u)&&d.indexOf(u)<0&&(g[u]=e[u]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,u=Object.getOwnPropertySymbols(e);c<u.length;c++)d.indexOf(u[c])<0&&Object.prototype.propertyIsEnumerable.call(e,u[c])&&(g[u[c]]=e[u[c]]);return g};const me=v.forwardRef((e,d)=>{const{prefixCls:g,className:u,rootClassName:c,style:A,children:y,icon:i,color:V,onClose:Q,bordered:t=!0,visible:T}=e,G=He(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:Fe,direction:Ee,tag:Z}=v.useContext(z.E_),[xe,ve]=v.useState(!0),D=(0,F.Z)(G,["closeIcon","closable"]);v.useEffect(()=>{T!==void 0&&ve(T)},[T]);const X=(0,ce.o2)(V),p=(0,ce.yT)(V),J=X||p,je=Object.assign(Object.assign({backgroundColor:V&&!J?V:void 0},Z==null?void 0:Z.style),A),L=Fe("tag",g),[ae,ne,De]=$e(L),Ne=q()(L,Z==null?void 0:Z.className,{[`${L}-${V}`]:J,[`${L}-has-color`]:V&&!J,[`${L}-hidden`]:!xe,[`${L}-rtl`]:Ee==="rtl",[`${L}-borderless`]:!t},u,c,ne,De),Y=W=>{W.stopPropagation(),Q==null||Q(W),!W.defaultPrevented&&ve(!1)},[,K]=(0,B.Z)((0,B.w)(e),(0,B.w)(Z),{closable:!1,closeIconRender:W=>{const Ie=v.createElement("span",{className:`${L}-close-icon`,onClick:Y},W);return(0,$.wm)(W,Ie,b=>({onClick:ue=>{var k;(k=b==null?void 0:b.onClick)===null||k===void 0||k.call(b,ue),Y(ue)},className:q()(b==null?void 0:b.className,`${L}-close-icon`)}))}}),ge=typeof G.onClick=="function"||y&&y.type==="a",he=i||null,re=he?v.createElement(v.Fragment,null,he,y&&v.createElement("span",null,y)):y,le=v.createElement("span",Object.assign({},D,{ref:d,className:Ne,style:je}),re,K,X&&v.createElement(Ue,{key:"preset",prefixCls:L}),p&&v.createElement(Ge,{key:"status",prefixCls:L}));return ae(ge?v.createElement(C.Z,{component:"Tag"},le):le)});me.CheckableTag=te;var Qe=me}}]);
