!(function(){"use strict";var ke=(te,G,_)=>new Promise((F,P)=>{var K=T=>{try{J(_.next(T))}catch(Q){P(Q)}},ue=T=>{try{J(_.throw(T))}catch(Q){P(Q)}},J=T=>T.done?F(T.value):Promise.resolve(T.value).then(K,ue);J((_=_.apply(te,G)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[2336],{70982:function(te,G,_){_.d(G,{G:function(){return P}});var F=_(29134),P=(0,F.eW)(()=>`
  /* Font Awesome icon styling - consolidated */
  .label-icon {
    display: inline-block;
    height: 1em;
    overflow: visible;
    vertical-align: -0.125em;
  }
  
  .node .label-icon path {
    fill: currentColor;
    stroke: revert;
    stroke-width: revert;
  }
`,"getIconStyles")},62336:function(te,G,_){_.d(G,{diagram:function(){return Ue}});var F=_(70982),P=_(29367),K=_(52387),ue=_(50538),J=_(57956),T=_(70919),Q=_(50854),Ee=_(44133),i=_(29134),me=_(65440),ge=_(12724),de=_(5959),ne=function(){var e=(0,i.eW)(function(p,s,t,o){for(t=t||{},o=p.length;o--;t[p[o]]=s);return t},"o"),d=[1,4],f=[1,13],a=[1,12],k=[1,15],S=[1,16],u=[1,20],y=[1,19],N=[6,7,8],g=[1,26],I=[1,24],x=[1,25],E=[6,7,11],W=[1,31],r=[6,7,11,24],V=[1,6,13,16,17,20,23],H=[1,35],Z=[1,36],R=[1,6,7,11,13,16,17,20,23],Y=[1,38],U={trace:(0,i.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:(0,i.eW)(function(s,t,o,c,h,n,C){var l=n.length-1;switch(h){case 6:case 7:return c;case 8:c.getLogger().trace("Stop NL ");break;case 9:c.getLogger().trace("Stop EOF ");break;case 11:c.getLogger().trace("Stop NL2 ");break;case 12:c.getLogger().trace("Stop EOF2 ");break;case 15:c.getLogger().info("Node: ",n[l-1].id),c.addNode(n[l-2].length,n[l-1].id,n[l-1].descr,n[l-1].type,n[l]);break;case 16:c.getLogger().info("Node: ",n[l].id),c.addNode(n[l-1].length,n[l].id,n[l].descr,n[l].type);break;case 17:c.getLogger().trace("Icon: ",n[l]),c.decorateNode({icon:n[l]});break;case 18:case 23:c.decorateNode({class:n[l]});break;case 19:c.getLogger().trace("SPACELIST");break;case 20:c.getLogger().trace("Node: ",n[l-1].id),c.addNode(0,n[l-1].id,n[l-1].descr,n[l-1].type,n[l]);break;case 21:c.getLogger().trace("Node: ",n[l].id),c.addNode(0,n[l].id,n[l].descr,n[l].type);break;case 22:c.decorateNode({icon:n[l]});break;case 27:c.getLogger().trace("node found ..",n[l-2]),this.$={id:n[l-1],descr:n[l-1],type:c.getType(n[l-2],n[l])};break;case 28:this.$={id:n[l],descr:n[l],type:0};break;case 29:c.getLogger().trace("node found ..",n[l-3]),this.$={id:n[l-3],descr:n[l-1],type:c.getType(n[l-2],n[l])};break;case 30:this.$=n[l-1]+n[l];break;case 31:this.$=n[l];break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:d},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:d},{6:f,7:[1,10],9:9,12:11,13:a,14:14,16:k,17:S,18:17,19:18,20:u,23:y},e(N,[2,3]),{1:[2,2]},e(N,[2,4]),e(N,[2,5]),{1:[2,6],6:f,12:21,13:a,14:14,16:k,17:S,18:17,19:18,20:u,23:y},{6:f,9:22,12:11,13:a,14:14,16:k,17:S,18:17,19:18,20:u,23:y},{6:g,7:I,10:23,11:x},e(E,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:u,23:y}),e(E,[2,19]),e(E,[2,21],{15:30,24:W}),e(E,[2,22]),e(E,[2,23]),e(r,[2,25]),e(r,[2,26]),e(r,[2,28],{20:[1,32]}),{21:[1,33]},{6:g,7:I,10:34,11:x},{1:[2,7],6:f,12:21,13:a,14:14,16:k,17:S,18:17,19:18,20:u,23:y},e(V,[2,14],{7:H,11:Z}),e(R,[2,8]),e(R,[2,9]),e(R,[2,10]),e(E,[2,16],{15:37,24:W}),e(E,[2,17]),e(E,[2,18]),e(E,[2,20],{24:Y}),e(r,[2,31]),{21:[1,39]},{22:[1,40]},e(V,[2,13],{7:H,11:Z}),e(R,[2,11]),e(R,[2,12]),e(E,[2,15],{24:Y}),e(r,[2,30]),{22:[1,41]},e(r,[2,27]),e(r,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:(0,i.eW)(function(s,t){if(t.recoverable)this.trace(s);else{var o=new Error(s);throw o.hash=t,o}},"parseError"),parse:(0,i.eW)(function(s){var t=this,o=[0],c=[],h=[null],n=[],C=this.table,l="",X=0,z=0,fe=0,je=2,_e=1,Ge=n.slice.call(arguments,1),m=Object.create(this.lexer),M={yy:{}};for(var ae in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ae)&&(M.yy[ae]=this.yy[ae]);m.setInput(s,M.yy),M.yy.lexer=m,M.yy.parser=this,typeof m.yylloc=="undefined"&&(m.yylloc={});var oe=m.yylloc;n.push(oe);var Fe=m.options&&m.options.ranges;typeof M.yy.parseError=="function"?this.parseError=M.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Ke(D){o.length=o.length-2*D,h.length=h.length-D,n.length=n.length-D}(0,i.eW)(Ke,"popStack");function ye(){var D;return D=c.pop()||m.lex()||_e,typeof D!="number"&&(D instanceof Array&&(c=D,D=c.pop()),D=t.symbols_[D]||D),D}(0,i.eW)(ye,"lex");for(var v,le,B,O,He,ce,j={},$,A,be,ee;;){if(B=o[o.length-1],this.defaultActions[B]?O=this.defaultActions[B]:((v===null||typeof v=="undefined")&&(v=ye()),O=C[B]&&C[B][v]),typeof O=="undefined"||!O.length||!O[0]){var he="";ee=[];for($ in C[B])this.terminals_[$]&&$>je&&ee.push("'"+this.terminals_[$]+"'");m.showPosition?he="Parse error on line "+(X+1)+`:
`+m.showPosition()+`
Expecting `+ee.join(", ")+", got '"+(this.terminals_[v]||v)+"'":he="Parse error on line "+(X+1)+": Unexpected "+(v==_e?"end of input":"'"+(this.terminals_[v]||v)+"'"),this.parseError(he,{text:m.match,token:this.terminals_[v]||v,line:m.yylineno,loc:oe,expected:ee})}if(O[0]instanceof Array&&O.length>1)throw new Error("Parse Error: multiple actions possible at state: "+B+", token: "+v);switch(O[0]){case 1:o.push(v),h.push(m.yytext),n.push(m.yylloc),o.push(O[1]),v=null,le?(v=le,le=null):(z=m.yyleng,l=m.yytext,X=m.yylineno,oe=m.yylloc,fe>0&&fe--);break;case 2:if(A=this.productions_[O[1]][1],j.$=h[h.length-A],j._$={first_line:n[n.length-(A||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(A||1)].first_column,last_column:n[n.length-1].last_column},Fe&&(j._$.range=[n[n.length-(A||1)].range[0],n[n.length-1].range[1]]),ce=this.performAction.apply(j,[l,z,X,M.yy,O[1],h,n].concat(Ge)),typeof ce!="undefined")return ce;A&&(o=o.slice(0,-1*A*2),h=h.slice(0,-1*A),n=n.slice(0,-1*A)),o.push(this.productions_[O[1]][0]),h.push(j.$),n.push(j._$),be=C[o[o.length-2]][o[o.length-1]],o.push(be);break;case 3:return!0}}return!0},"parse")},q=function(){var p={EOF:1,parseError:(0,i.eW)(function(t,o){if(this.yy.parser)this.yy.parser.parseError(t,o);else throw new Error(t)},"parseError"),setInput:(0,i.eW)(function(s,t){return this.yy=t||this.yy||{},this._input=s,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,i.eW)(function(){var s=this._input[0];this.yytext+=s,this.yyleng++,this.offset++,this.match+=s,this.matched+=s;var t=s.match(/(?:\r\n?|\n).*/g);return t?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),s},"input"),unput:(0,i.eW)(function(s){var t=s.length,o=s.split(/(?:\r\n?|\n)/g);this._input=s+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var c=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),o.length-1&&(this.yylineno-=o.length-1);var h=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:o?(o.length===c.length?this.yylloc.first_column:0)+c[c.length-o.length].length-o[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[h[0],h[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},"unput"),more:(0,i.eW)(function(){return this._more=!0,this},"more"),reject:(0,i.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,i.eW)(function(s){this.unput(this.match.slice(s))},"less"),pastInput:(0,i.eW)(function(){var s=this.matched.substr(0,this.matched.length-this.match.length);return(s.length>20?"...":"")+s.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,i.eW)(function(){var s=this.match;return s.length<20&&(s+=this._input.substr(0,20-s.length)),(s.substr(0,20)+(s.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,i.eW)(function(){var s=this.pastInput(),t=new Array(s.length+1).join("-");return s+this.upcomingInput()+`
`+t+"^"},"showPosition"),test_match:(0,i.eW)(function(s,t){var o,c,h;if(this.options.backtrack_lexer&&(h={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(h.yylloc.range=this.yylloc.range.slice(0))),c=s[0].match(/(?:\r\n?|\n).*/g),c&&(this.yylineno+=c.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:c?c[c.length-1].length-c[c.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+s[0].length},this.yytext+=s[0],this.match+=s[0],this.matches=s,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(s[0].length),this.matched+=s[0],o=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o)return o;if(this._backtrack){for(var n in h)this[n]=h[n];return!1}return!1},"test_match"),next:(0,i.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var s,t,o,c;this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),n=0;n<h.length;n++)if(o=this._input.match(this.rules[h[n]]),o&&(!t||o[0].length>t[0].length)){if(t=o,c=n,this.options.backtrack_lexer){if(s=this.test_match(o,h[n]),s!==!1)return s;if(this._backtrack){t=!1;continue}else return!1}else if(!this.options.flex)break}return t?(s=this.test_match(t,h[c]),s!==!1?s:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,i.eW)(function(){var t=this.next();return t||this.lex()},"lex"),begin:(0,i.eW)(function(t){this.conditionStack.push(t)},"begin"),popState:(0,i.eW)(function(){var t=this.conditionStack.length-1;return t>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,i.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,i.eW)(function(t){return t=this.conditionStack.length-1-Math.abs(t||0),t>=0?this.conditionStack[t]:"INITIAL"},"topState"),pushState:(0,i.eW)(function(t){this.begin(t)},"pushState"),stateStackSize:(0,i.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,i.eW)(function(t,o,c,h){var n=h;switch(c){case 0:return this.pushState("shapeData"),o.yytext="",24;break;case 1:return this.pushState("shapeDataStr"),24;break;case 2:return this.popState(),24;break;case 3:const C=/\n\s*/g;return o.yytext=o.yytext.replace(C,"<br/>"),24;break;case 4:return 24;case 5:this.popState();break;case 6:return t.getLogger().trace("Found comment",o.yytext),6;break;case 7:return 8;case 8:this.begin("CLASS");break;case 9:return this.popState(),17;break;case 10:this.popState();break;case 11:t.getLogger().trace("Begin icon"),this.begin("ICON");break;case 12:return t.getLogger().trace("SPACELINE"),6;break;case 13:return 7;case 14:return 16;case 15:t.getLogger().trace("end icon"),this.popState();break;case 16:return t.getLogger().trace("Exploding node"),this.begin("NODE"),20;break;case 17:return t.getLogger().trace("Cloud"),this.begin("NODE"),20;break;case 18:return t.getLogger().trace("Explosion Bang"),this.begin("NODE"),20;break;case 19:return t.getLogger().trace("Cloud Bang"),this.begin("NODE"),20;break;case 20:return this.begin("NODE"),20;break;case 21:return this.begin("NODE"),20;break;case 22:return this.begin("NODE"),20;break;case 23:return this.begin("NODE"),20;break;case 24:return 13;case 25:return 23;case 26:return 11;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";case 29:this.popState();break;case 30:t.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 31:return t.getLogger().trace("description:",o.yytext),"NODE_DESCR";break;case 32:this.popState();break;case 33:return this.popState(),t.getLogger().trace("node end ))"),"NODE_DEND";break;case 34:return this.popState(),t.getLogger().trace("node end )"),"NODE_DEND";break;case 35:return this.popState(),t.getLogger().trace("node end ...",o.yytext),"NODE_DEND";break;case 36:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";break;case 37:return this.popState(),t.getLogger().trace("node end (-"),"NODE_DEND";break;case 38:return this.popState(),t.getLogger().trace("node end (-"),"NODE_DEND";break;case 39:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";break;case 40:return this.popState(),t.getLogger().trace("node end (("),"NODE_DEND";break;case 41:return t.getLogger().trace("Long description:",o.yytext),21;break;case 42:return t.getLogger().trace("Long description:",o.yytext),21;break}},"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:!1},shapeDataStr:{rules:[2,3],inclusive:!1},shapeData:{rules:[1,4,5],inclusive:!1},CLASS:{rules:[9,10],inclusive:!1},ICON:{rules:[14,15],inclusive:!1},NSTR2:{rules:[28,29],inclusive:!1},NSTR:{rules:[31,32],inclusive:!1},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:!1},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:!0}}};return p}();U.lexer=q;function w(){this.yy={}}return(0,i.eW)(w,"Parser"),w.prototype=U,U.Parser=w,new w}();ne.parser=ne;var Se=ne,L=[],se=[],ie=0,re={},ve=(0,i.eW)(()=>{L=[],se=[],ie=0,re={}},"clear"),De=(0,i.eW)(e=>{if(L.length===0)return null;const d=L[0].level;let f=null;for(let a=L.length-1;a>=0;a--)if(L[a].level===d&&!f&&(f=L[a]),L[a].level<d)throw new Error('Items without section detected, found section ("'+L[a].label+'")');return e===(f==null?void 0:f.level)?null:f},"getSection"),pe=(0,i.eW)(function(){return se},"getSections"),Le=(0,i.eW)(function(){var k,S;const e=[],d=[],f=pe(),a=(0,i.nV)();for(const u of f){const y={id:u.id,label:(0,i.oO)((k=u.label)!=null?k:"",a),isGroup:!0,ticket:u.ticket,shape:"kanbanSection",level:u.level,look:a.look};d.push(y);const N=L.filter(g=>g.parentId===u.id);for(const g of N){const I={id:g.id,parentId:u.id,label:(0,i.oO)((S=g.label)!=null?S:"",a),isGroup:!1,ticket:g==null?void 0:g.ticket,priority:g==null?void 0:g.priority,assigned:g==null?void 0:g.assigned,icon:g==null?void 0:g.icon,shape:"kanbanItem",level:g.level,rx:5,ry:5,cssStyles:["text-align: left"]};d.push(I)}}return{nodes:d,edges:e,other:{},config:(0,i.nV)()}},"getData"),Oe=(0,i.eW)((e,d,f,a,k)=>{var g,I,x,E;const S=(0,i.nV)();let u=(I=(g=S.mindmap)==null?void 0:g.padding)!=null?I:i.vZ.mindmap.padding;switch(a){case b.ROUNDED_RECT:case b.RECT:case b.HEXAGON:u*=2}const y={id:(0,i.oO)(d,S)||"kbn"+ie++,level:e,label:(0,i.oO)(f,S),width:(E=(x=S.mindmap)==null?void 0:x.maxNodeWidth)!=null?E:i.vZ.mindmap.maxNodeWidth,padding:u,isGroup:!1};if(k!==void 0){let W;k.includes(`
`)?W=k+`
`:W=`{
`+k+`
}`;const r=(0,P.z)(W,{schema:P.A});if(r.shape&&(r.shape!==r.shape.toLowerCase()||r.shape.includes("_")))throw new Error(`No such shape: ${r.shape}. Shape names should be lowercase.`);r!=null&&r.shape&&r.shape==="kanbanItem"&&(y.shape=r==null?void 0:r.shape),r!=null&&r.label&&(y.label=r==null?void 0:r.label),r!=null&&r.icon&&(y.icon=r==null?void 0:r.icon.toString()),r!=null&&r.assigned&&(y.assigned=r==null?void 0:r.assigned.toString()),r!=null&&r.ticket&&(y.ticket=r==null?void 0:r.ticket.toString()),r!=null&&r.priority&&(y.priority=r==null?void 0:r.priority)}const N=De(e);N?y.parentId=N.id||"kbn"+ie++:se.push(y),L.push(y)},"addNode"),b={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Ne=(0,i.eW)((e,d)=>{switch(i.cM.debug("In get type",e,d),e){case"[":return b.RECT;case"(":return d===")"?b.ROUNDED_RECT:b.CLOUD;case"((":return b.CIRCLE;case")":return b.CLOUD;case"))":return b.BANG;case"{{":return b.HEXAGON;default:return b.DEFAULT}},"getType"),xe=(0,i.eW)((e,d)=>{re[e]=d},"setElementForId"),Ie=(0,i.eW)(e=>{if(!e)return;const d=(0,i.nV)(),f=L[L.length-1];e.icon&&(f.icon=(0,i.oO)(e.icon,d)),e.class&&(f.cssClasses=(0,i.oO)(e.class,d))},"decorateNode"),We=(0,i.eW)(e=>{switch(e){case b.DEFAULT:return"no-border";case b.RECT:return"rect";case b.ROUNDED_RECT:return"rounded-rect";case b.CIRCLE:return"circle";case b.CLOUD:return"cloud";case b.BANG:return"bang";case b.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),Ce=(0,i.eW)(()=>i.cM,"getLogger"),Ae=(0,i.eW)(e=>re[e],"getElementById"),Te={clear:ve,addNode:Oe,getSections:pe,getData:Le,nodeType:b,getType:Ne,setElementForId:xe,decorateNode:Ie,type2Str:We,getLogger:Ce,getElementById:Ae},Re=Te,Pe=(0,i.eW)((e,d,f,a)=>ke(this,null,function*(){var H,Z,R,Y,U,q,w;i.cM.debug(`Rendering kanban diagram
`+e);const S=a.db.getData(),u=(0,i.nV)();u.htmlLabels=!1;const y=(0,Ee.P)(d),N=y.append("g");N.attr("class","sections");const g=y.append("g");g.attr("class","items");const I=S.nodes.filter(p=>p.isGroup);let x=0;const E=10,W=[];let r=25;for(const p of I){const s=((H=u==null?void 0:u.kanban)==null?void 0:H.sectionWidth)||200;x=x+1,p.x=s*x+(x-1)*E/2,p.width=s,p.y=0,p.height=s*3,p.rx=5,p.ry=5,p.cssClasses=p.cssClasses+" section-"+x;const t=yield(0,K.us)(N,p);r=Math.max(r,(Z=t==null?void 0:t.labelBBox)==null?void 0:Z.height),W.push(t)}let V=0;for(const p of I){const s=W[V];V=V+1;const t=((R=u==null?void 0:u.kanban)==null?void 0:R.sectionWidth)||200,o=-t*3/2+r;let c=o;const h=S.nodes.filter(l=>l.parentId===p.id);for(const l of h){if(l.isGroup)throw new Error("Groups within groups are not allowed in Kanban diagrams");l.x=p.x,l.width=t-1.5*E;const z=(yield(0,K.Lf)(g,l,{config:u})).node().getBBox();l.y=c+z.height/2,yield(0,K.aH)(l),c=l.y+z.height/2+E/2}const n=s.cluster.select("rect"),C=Math.max(c-o+3*E,50)+(r-25);n.attr("height",C)}(0,i.j7)(void 0,y,(U=(Y=u.mindmap)==null?void 0:Y.padding)!=null?U:i.vZ.kanban.padding,(w=(q=u.mindmap)==null?void 0:q.useMaxWidth)!=null?w:i.vZ.kanban.useMaxWidth)}),"draw"),we={draw:Pe},Me=(0,i.eW)(e=>{let d="";for(let a=0;a<e.THEME_COLOR_LIMIT;a++)e["lineColor"+a]=e["lineColor"+a]||e["cScaleInv"+a],(0,me.Z)(e["lineColor"+a])?e["lineColor"+a]=(0,ge.Z)(e["lineColor"+a],20):e["lineColor"+a]=(0,de.Z)(e["lineColor"+a],20);const f=(0,i.eW)((a,k)=>e.darkMode?(0,de.Z)(a,k):(0,ge.Z)(a,k),"adjuster");for(let a=0;a<e.THEME_COLOR_LIMIT;a++){const k=""+(17-3*a);d+=`
    .section-${a-1} rect, .section-${a-1} path, .section-${a-1} circle, .section-${a-1} polygon, .section-${a-1} path  {
      fill: ${f(e["cScale"+a],10)};
      stroke: ${f(e["cScale"+a],10)};

    }
    .section-${a-1} text {
     fill: ${e["cScaleLabel"+a]};
    }
    .node-icon-${a-1} {
      font-size: 40px;
      color: ${e["cScaleLabel"+a]};
    }
    .section-edge-${a-1}{
      stroke: ${e["cScale"+a]};
    }
    .edge-depth-${a-1}{
      stroke-width: ${k};
    }
    .section-${a-1} line {
      stroke: ${e["cScaleInv"+a]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }

  .kanban-ticket-link {
    fill: ${e.background};
    stroke: ${e.nodeBorder};
    text-decoration: underline;
  }
    `}return d},"genSections"),Be=(0,i.eW)(e=>`
  .edge {
    stroke-width: 3;
  }
  ${Me(e)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${e.git0};
  }
  .section-root text {
    fill: ${e.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .cluster-label, .label {
    color: ${e.textColor};
    fill: ${e.textColor};
    }
  .kanban-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
    ${(0,F.G)()}
`,"getStyles"),Ve=Be,Ue={db:Re,renderer:we,parser:Se,styles:Ve}}}]);
}());