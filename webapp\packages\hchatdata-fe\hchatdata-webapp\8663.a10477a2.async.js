!(function(){"use strict";var Ga=Object.defineProperty;var Ba=(Te,j,g)=>j in Te?Ga(Te,j,{enumerable:!0,configurable:!0,writable:!0,value:g}):Te[j]=g;var Mn=(Te,j,g)=>Ba(Te,typeof j!="symbol"?j+"":j,g);var Re=(Te,j,g)=>new Promise((y,O)=>{var F=b=>{try{L(g.next(b))}catch(V){O(V)}},x=b=>{try{L(g.throw(b))}catch(V){O(V)}},L=b=>b.done?y(b.value):Promise.resolve(b.value).then(F,x);L((g=g.apply(Te,j)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[8663],{82500:function(Te,j,g){var y;y={value:!0},j.AU=j.Ts=void 0;const O=g(38069),F=g(42911),x=g(32650);var L;(function(R){R.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:x.Event.None}),R.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:x.Event.None});function T(E){const _=E;return _&&(_===R.None||_===R.Cancelled||F.boolean(_.isCancellationRequested)&&!!_.onCancellationRequested)}R.is=T})(L||(j.Ts=L={}));const b=Object.freeze(function(R,T){const E=(0,O.default)().timer.setTimeout(R.bind(T),0);return{dispose(){E.dispose()}}});class V{constructor(){this._isCancelled=!1}cancel(){this._isCancelled||(this._isCancelled=!0,this._emitter&&(this._emitter.fire(void 0),this.dispose()))}get isCancellationRequested(){return this._isCancelled}get onCancellationRequested(){return this._isCancelled?b:(this._emitter||(this._emitter=new x.Emitter),this._emitter.event)}dispose(){this._emitter&&(this._emitter.dispose(),this._emitter=void 0)}}class B{get token(){return this._token||(this._token=new V),this._token}cancel(){this._token?this._token.cancel():this._token=L.Cancelled}dispose(){this._token?this._token instanceof V&&this._token.dispose():this._token=L.None}}j.AU=B},32650:function(Te,j,g){Object.defineProperty(j,"__esModule",{value:!0}),j.Emitter=j.Event=void 0;const y=g(38069);var O;(function(L){const b={dispose(){}};L.None=function(){return b}})(O||(j.Event=O={}));class F{add(b,V=null,B){this._callbacks||(this._callbacks=[],this._contexts=[]),this._callbacks.push(b),this._contexts.push(V),Array.isArray(B)&&B.push({dispose:()=>this.remove(b,V)})}remove(b,V=null){if(!this._callbacks)return;let B=!1;for(let R=0,T=this._callbacks.length;R<T;R++)if(this._callbacks[R]===b)if(this._contexts[R]===V){this._callbacks.splice(R,1),this._contexts.splice(R,1);return}else B=!0;if(B)throw new Error("When adding a listener with a context, you should remove it with the same context")}invoke(...b){if(!this._callbacks)return[];const V=[],B=this._callbacks.slice(0),R=this._contexts.slice(0);for(let T=0,E=B.length;T<E;T++)try{V.push(B[T].apply(R[T],b))}catch(_){(0,y.default)().console.error(_)}return V}isEmpty(){return!this._callbacks||this._callbacks.length===0}dispose(){this._callbacks=void 0,this._contexts=void 0}}class x{constructor(b){this._options=b}get event(){return this._event||(this._event=(b,V,B)=>{this._callbacks||(this._callbacks=new F),this._options&&this._options.onFirstListenerAdd&&this._callbacks.isEmpty()&&this._options.onFirstListenerAdd(this),this._callbacks.add(b,V);const R={dispose:()=>{this._callbacks&&(this._callbacks.remove(b,V),R.dispose=x._noop,this._options&&this._options.onLastListenerRemove&&this._callbacks.isEmpty()&&this._options.onLastListenerRemove(this))}};return Array.isArray(B)&&B.push(R),R}),this._event}fire(b){this._callbacks&&this._callbacks.invoke.call(this._callbacks,b)}dispose(){this._callbacks&&(this._callbacks.dispose(),this._callbacks=void 0)}}j.Emitter=x,x._noop=function(){}},42911:function(Te,j){Object.defineProperty(j,"__esModule",{value:!0}),j.stringArray=j.array=j.func=j.error=j.number=j.string=j.boolean=void 0;function g(V){return V===!0||V===!1}j.boolean=g;function y(V){return typeof V=="string"||V instanceof String}j.string=y;function O(V){return typeof V=="number"||V instanceof Number}j.number=O;function F(V){return V instanceof Error}j.error=F;function x(V){return typeof V=="function"}j.func=x;function L(V){return Array.isArray(V)}j.array=L;function b(V){return L(V)&&V.every(B=>y(B))}j.stringArray=b},38069:function(Te,j){Object.defineProperty(j,"__esModule",{value:!0});let g;function y(){if(g===void 0)throw new Error("No runtime abstraction layer installed");return g}(function(O){function F(x){if(x===void 0)throw new Error("No runtime abstraction layer provided");g=x}O.install=F})(y||(y={})),j.default=y},37521:function(Te,j,g){g.d(j,{e:function(){return p},O:function(){return v}});function y(C){return C.charCodeAt(0)}function O(C,h){Array.isArray(C)?C.forEach(function(S){h.push(S)}):h.push(C)}function F(C,h){if(C[h]===!0)throw"duplicate flag "+h;const S=C[h];C[h]=!0}function x(C){if(C===void 0)throw Error("Internal Error - Should never get here!");return!0}function L(){throw Error("Internal Error - Should never get here!")}function b(C){return C.type==="Character"}const V=[];for(let C=y("0");C<=y("9");C++)V.push(C);const B=[y("_")].concat(V);for(let C=y("a");C<=y("z");C++)B.push(C);for(let C=y("A");C<=y("Z");C++)B.push(C);const R=[y(" "),y("\f"),y(`
`),y("\r"),y("	"),y("\v"),y("	"),y("\xA0"),y("\u1680"),y("\u2000"),y("\u2001"),y("\u2002"),y("\u2003"),y("\u2004"),y("\u2005"),y("\u2006"),y("\u2007"),y("\u2008"),y("\u2009"),y("\u200A"),y("\u2028"),y("\u2029"),y("\u202F"),y("\u205F"),y("\u3000"),y("\uFEFF")],T=/[0-9a-fA-F]/,E=/[0-9]/,_=/[1-9]/;class v{constructor(){this.idx=0,this.input="",this.groupIdx=0}saveState(){return{idx:this.idx,input:this.input,groupIdx:this.groupIdx}}restoreState(h){this.idx=h.idx,this.input=h.input,this.groupIdx=h.groupIdx}pattern(h){this.idx=0,this.input=h,this.groupIdx=0,this.consumeChar("/");const S=this.disjunction();this.consumeChar("/");const w={type:"Flags",loc:{begin:this.idx,end:h.length},global:!1,ignoreCase:!1,multiLine:!1,unicode:!1,sticky:!1};for(;this.isRegExpFlag();)switch(this.popChar()){case"g":F(w,"global");break;case"i":F(w,"ignoreCase");break;case"m":F(w,"multiLine");break;case"u":F(w,"unicode");break;case"y":F(w,"sticky");break}if(this.idx!==this.input.length)throw Error("Redundant input: "+this.input.substring(this.idx));return{type:"Pattern",flags:w,value:S,loc:this.loc(0)}}disjunction(){const h=[],S=this.idx;for(h.push(this.alternative());this.peekChar()==="|";)this.consumeChar("|"),h.push(this.alternative());return{type:"Disjunction",value:h,loc:this.loc(S)}}alternative(){const h=[],S=this.idx;for(;this.isTerm();)h.push(this.term());return{type:"Alternative",value:h,loc:this.loc(S)}}term(){return this.isAssertion()?this.assertion():this.atom()}assertion(){const h=this.idx;switch(this.popChar()){case"^":return{type:"StartAnchor",loc:this.loc(h)};case"$":return{type:"EndAnchor",loc:this.loc(h)};case"\\":switch(this.popChar()){case"b":return{type:"WordBoundary",loc:this.loc(h)};case"B":return{type:"NonWordBoundary",loc:this.loc(h)}}throw Error("Invalid Assertion Escape");case"(":this.consumeChar("?");let S;switch(this.popChar()){case"=":S="Lookahead";break;case"!":S="NegativeLookahead";break}x(S);const w=this.disjunction();return this.consumeChar(")"),{type:S,value:w,loc:this.loc(h)}}return L()}quantifier(h=!1){let S;const w=this.idx;switch(this.popChar()){case"*":S={atLeast:0,atMost:1/0};break;case"+":S={atLeast:1,atMost:1/0};break;case"?":S={atLeast:0,atMost:1};break;case"{":const Z=this.integerIncludingZero();switch(this.popChar()){case"}":S={atLeast:Z,atMost:Z};break;case",":let P;this.isDigit()?(P=this.integerIncludingZero(),S={atLeast:Z,atMost:P}):S={atLeast:Z,atMost:1/0},this.consumeChar("}");break}if(h===!0&&S===void 0)return;x(S);break}if(!(h===!0&&S===void 0)&&x(S))return this.peekChar(0)==="?"?(this.consumeChar("?"),S.greedy=!1):S.greedy=!0,S.type="Quantifier",S.loc=this.loc(w),S}atom(){let h;const S=this.idx;switch(this.peekChar()){case".":h=this.dotAll();break;case"\\":h=this.atomEscape();break;case"[":h=this.characterClass();break;case"(":h=this.group();break}return h===void 0&&this.isPatternCharacter()&&(h=this.patternCharacter()),x(h)?(h.loc=this.loc(S),this.isQuantifier()&&(h.quantifier=this.quantifier()),h):L()}dotAll(){return this.consumeChar("."),{type:"Set",complement:!0,value:[y(`
`),y("\r"),y("\u2028"),y("\u2029")]}}atomEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":return this.decimalEscapeAtom();case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}decimalEscapeAtom(){return{type:"GroupBackReference",value:this.positiveInteger()}}characterClassEscape(){let h,S=!1;switch(this.popChar()){case"d":h=V;break;case"D":h=V,S=!0;break;case"s":h=R;break;case"S":h=R,S=!0;break;case"w":h=B;break;case"W":h=B,S=!0;break}return x(h)?{type:"Set",value:h,complement:S}:L()}controlEscapeAtom(){let h;switch(this.popChar()){case"f":h=y("\f");break;case"n":h=y(`
`);break;case"r":h=y("\r");break;case"t":h=y("	");break;case"v":h=y("\v");break}return x(h)?{type:"Character",value:h}:L()}controlLetterEscapeAtom(){this.consumeChar("c");const h=this.popChar();if(/[a-zA-Z]/.test(h)===!1)throw Error("Invalid ");return{type:"Character",value:h.toUpperCase().charCodeAt(0)-64}}nulCharacterAtom(){return this.consumeChar("0"),{type:"Character",value:y("\0")}}hexEscapeSequenceAtom(){return this.consumeChar("x"),this.parseHexDigits(2)}regExpUnicodeEscapeSequenceAtom(){return this.consumeChar("u"),this.parseHexDigits(4)}identityEscapeAtom(){const h=this.popChar();return{type:"Character",value:y(h)}}classPatternCharacterAtom(){switch(this.peekChar()){case`
`:case"\r":case"\u2028":case"\u2029":case"\\":case"]":throw Error("TBD");default:const h=this.popChar();return{type:"Character",value:y(h)}}}characterClass(){const h=[];let S=!1;for(this.consumeChar("["),this.peekChar(0)==="^"&&(this.consumeChar("^"),S=!0);this.isClassAtom();){const w=this.classAtom(),Z=w.type==="Character";if(b(w)&&this.isRangeDash()){this.consumeChar("-");const P=this.classAtom(),Q=P.type==="Character";if(b(P)){if(P.value<w.value)throw Error("Range out of order in character class");h.push({from:w.value,to:P.value})}else O(w.value,h),h.push(y("-")),O(P.value,h)}else O(w.value,h)}return this.consumeChar("]"),{type:"Set",complement:S,value:h}}classAtom(){switch(this.peekChar()){case"]":case`
`:case"\r":case"\u2028":case"\u2029":throw Error("TBD");case"\\":return this.classEscape();default:return this.classPatternCharacterAtom()}}classEscape(){switch(this.consumeChar("\\"),this.peekChar()){case"b":return this.consumeChar("b"),{type:"Character",value:y("\b")};case"d":case"D":case"s":case"S":case"w":case"W":return this.characterClassEscape();case"f":case"n":case"r":case"t":case"v":return this.controlEscapeAtom();case"c":return this.controlLetterEscapeAtom();case"0":return this.nulCharacterAtom();case"x":return this.hexEscapeSequenceAtom();case"u":return this.regExpUnicodeEscapeSequenceAtom();default:return this.identityEscapeAtom()}}group(){let h=!0;switch(this.consumeChar("("),this.peekChar(0)){case"?":this.consumeChar("?"),this.consumeChar(":"),h=!1;break;default:this.groupIdx++;break}const S=this.disjunction();this.consumeChar(")");const w={type:"Group",capturing:h,value:S};return h&&(w.idx=this.groupIdx),w}positiveInteger(){let h=this.popChar();if(_.test(h)===!1)throw Error("Expecting a positive integer");for(;E.test(this.peekChar(0));)h+=this.popChar();return parseInt(h,10)}integerIncludingZero(){let h=this.popChar();if(E.test(h)===!1)throw Error("Expecting an integer");for(;E.test(this.peekChar(0));)h+=this.popChar();return parseInt(h,10)}patternCharacter(){const h=this.popChar();switch(h){case`
`:case"\r":case"\u2028":case"\u2029":case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":throw Error("TBD");default:return{type:"Character",value:y(h)}}}isRegExpFlag(){switch(this.peekChar(0)){case"g":case"i":case"m":case"u":case"y":return!0;default:return!1}}isRangeDash(){return this.peekChar()==="-"&&this.isClassAtom(1)}isDigit(){return E.test(this.peekChar(0))}isClassAtom(h=0){switch(this.peekChar(h)){case"]":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}isTerm(){return this.isAtom()||this.isAssertion()}isAtom(){if(this.isPatternCharacter())return!0;switch(this.peekChar(0)){case".":case"\\":case"[":case"(":return!0;default:return!1}}isAssertion(){switch(this.peekChar(0)){case"^":case"$":return!0;case"\\":switch(this.peekChar(1)){case"b":case"B":return!0;default:return!1}case"(":return this.peekChar(1)==="?"&&(this.peekChar(2)==="="||this.peekChar(2)==="!");default:return!1}}isQuantifier(){const h=this.saveState();try{return this.quantifier(!0)!==void 0}catch(S){return!1}finally{this.restoreState(h)}}isPatternCharacter(){switch(this.peekChar()){case"^":case"$":case"\\":case".":case"*":case"+":case"?":case"(":case")":case"[":case"|":case"/":case`
`:case"\r":case"\u2028":case"\u2029":return!1;default:return!0}}parseHexDigits(h){let S="";for(let Z=0;Z<h;Z++){const P=this.popChar();if(T.test(P)===!1)throw Error("Expecting a HexDecimal digits");S+=P}return{type:"Character",value:parseInt(S,16)}}peekChar(h=0){return this.input[this.idx+h]}popChar(){const h=this.peekChar(0);return this.consumeChar(void 0),h}consumeChar(h){if(h!==void 0&&this.input[this.idx]!==h)throw Error("Expected: '"+h+"' but found: '"+this.input[this.idx]+"' at offset: "+this.idx);if(this.idx>=this.input.length)throw Error("Unexpected end of input");this.idx++}loc(h){return{begin:h,end:this.idx}}}class p{visitChildren(h){for(const S in h){const w=h[S];h.hasOwnProperty(S)&&(w.type!==void 0?this.visit(w):Array.isArray(w)&&w.forEach(Z=>{this.visit(Z)},this))}}visit(h){switch(h.type){case"Pattern":this.visitPattern(h);break;case"Flags":this.visitFlags(h);break;case"Disjunction":this.visitDisjunction(h);break;case"Alternative":this.visitAlternative(h);break;case"StartAnchor":this.visitStartAnchor(h);break;case"EndAnchor":this.visitEndAnchor(h);break;case"WordBoundary":this.visitWordBoundary(h);break;case"NonWordBoundary":this.visitNonWordBoundary(h);break;case"Lookahead":this.visitLookahead(h);break;case"NegativeLookahead":this.visitNegativeLookahead(h);break;case"Character":this.visitCharacter(h);break;case"Set":this.visitSet(h);break;case"Group":this.visitGroup(h);break;case"GroupBackReference":this.visitGroupBackReference(h);break;case"Quantifier":this.visitQuantifier(h);break}this.visitChildren(h)}visitPattern(h){}visitFlags(h){}visitDisjunction(h){}visitAlternative(h){}visitStartAnchor(h){}visitEndAnchor(h){}visitWordBoundary(h){}visitNonWordBoundary(h){}visitLookahead(h){}visitNegativeLookahead(h){}visitCharacter(h){}visitSet(h){}visitGroup(h){}visitGroupBackReference(h){}visitQuantifier(h){}}},6612:function(Te,j,g){var St,ut,Rt,Ne,ee;g.d(j,{T7:function(){return be},kb:function(){return Kn},Qr:function(){return Ft},nr:function(){return it},vn:function(){return Kt},F_:function(){return xt},GS:function(){return Bn},bb:function(){return Tn},WH:function(){return rt},gB:function(){return Qt},E$:function(){return Vt},eW:function(){return p}});var y=g(51720),O=g(79837),F=g(93044),x=g(71997),L=g(35003),b=g(43545);const V={Grammar:()=>{},LanguageMetaData:()=>({caseInsensitive:!1,fileExtensions:[".langium"],languageId:"langium"})},B={AstReflection:()=>new x.SV};function R(){const D=(0,F.f3)((0,O.T)(L.u),B),xe=(0,F.f3)((0,O.Q)({shared:D}),V);return D.ServiceRegistry.register(xe),xe}function T(D){var xe;const Ce=R(),pt=Ce.serializer.JsonSerializer.deserialize(D);return Ce.shared.workspace.LangiumDocumentFactory.fromModel(pt,b.o.parse(`memory://${(xe=pt.name)!==null&&xe!==void 0?xe:"grammar"}.langium`)),pt}var E=g(12058),_=g(93202),v=Object.defineProperty,p=(D,xe)=>v(D,"name",{value:xe,configurable:!0}),C="Statement",h="Architecture";function S(D){return lt.isInstance(D,h)}p(S,"isArchitecture");var w="Axis",Z="Branch";function P(D){return lt.isInstance(D,Z)}p(P,"isBranch");var Q="Checkout",J="CherryPicking",ve="ClassDefStatement",K="Commit";function ae(D){return lt.isInstance(D,K)}p(ae,"isCommit");var ce="Curve",ye="Edge",Ae="Entry",Se="GitGraph";function Je(D){return lt.isInstance(D,Se)}p(Je,"isGitGraph");var Ge="Group",oe="Info";function Y(D){return lt.isInstance(D,oe)}p(Y,"isInfo");var se="Item",re="Junction",ke="Merge";function ge(D){return lt.isInstance(D,ke)}p(ge,"isMerge");var he="Option",Me="Packet";function je(D){return lt.isInstance(D,Me)}p(je,"isPacket");var $e="PacketBlock";function He(D){return lt.isInstance(D,$e)}p(He,"isPacketBlock");var ze="Pie";function Ze(D){return lt.isInstance(D,ze)}p(Ze,"isPie");var I="PieSection";function W(D){return lt.isInstance(D,I)}p(W,"isPieSection");var z="Radar",le="Service",pe="Treemap";function Ie(D){return lt.isInstance(D,pe)}p(Ie,"isTreemap");var me="TreemapRow",vt="Direction",at="Leaf",Zt="Section",It=(St=class extends y.$v{getAllTypes(){return[h,w,Z,Q,J,ve,K,ce,vt,ye,Ae,Se,Ge,oe,se,re,at,ke,he,Me,$e,ze,I,z,Zt,le,C,pe,me]}computeIsSubtype(xe,Ce){switch(xe){case Z:case Q:case J:case K:case ke:return this.isSubtype(C,Ce);case vt:return this.isSubtype(Se,Ce);case at:case Zt:return this.isSubtype(se,Ce);default:return!1}}getReferenceType(xe){const Ce=`${xe.container.$type}:${xe.property}`;switch(Ce){case"Entry:axis":return w;default:throw new Error(`${Ce} is not a valid reference id.`)}}getTypeMetaData(xe){switch(xe){case h:return{name:h,properties:[{name:"accDescr"},{name:"accTitle"},{name:"edges",defaultValue:[]},{name:"groups",defaultValue:[]},{name:"junctions",defaultValue:[]},{name:"services",defaultValue:[]},{name:"title"}]};case w:return{name:w,properties:[{name:"label"},{name:"name"}]};case Z:return{name:Z,properties:[{name:"name"},{name:"order"}]};case Q:return{name:Q,properties:[{name:"branch"}]};case J:return{name:J,properties:[{name:"id"},{name:"parent"},{name:"tags",defaultValue:[]}]};case ve:return{name:ve,properties:[{name:"className"},{name:"styleText"}]};case K:return{name:K,properties:[{name:"id"},{name:"message"},{name:"tags",defaultValue:[]},{name:"type"}]};case ce:return{name:ce,properties:[{name:"entries",defaultValue:[]},{name:"label"},{name:"name"}]};case ye:return{name:ye,properties:[{name:"lhsDir"},{name:"lhsGroup",defaultValue:!1},{name:"lhsId"},{name:"lhsInto",defaultValue:!1},{name:"rhsDir"},{name:"rhsGroup",defaultValue:!1},{name:"rhsId"},{name:"rhsInto",defaultValue:!1},{name:"title"}]};case Ae:return{name:Ae,properties:[{name:"axis"},{name:"value"}]};case Se:return{name:Se,properties:[{name:"accDescr"},{name:"accTitle"},{name:"statements",defaultValue:[]},{name:"title"}]};case Ge:return{name:Ge,properties:[{name:"icon"},{name:"id"},{name:"in"},{name:"title"}]};case oe:return{name:oe,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"}]};case se:return{name:se,properties:[{name:"classSelector"},{name:"name"}]};case re:return{name:re,properties:[{name:"id"},{name:"in"}]};case ke:return{name:ke,properties:[{name:"branch"},{name:"id"},{name:"tags",defaultValue:[]},{name:"type"}]};case he:return{name:he,properties:[{name:"name"},{name:"value",defaultValue:!1}]};case Me:return{name:Me,properties:[{name:"accDescr"},{name:"accTitle"},{name:"blocks",defaultValue:[]},{name:"title"}]};case $e:return{name:$e,properties:[{name:"bits"},{name:"end"},{name:"label"},{name:"start"}]};case ze:return{name:ze,properties:[{name:"accDescr"},{name:"accTitle"},{name:"sections",defaultValue:[]},{name:"showData",defaultValue:!1},{name:"title"}]};case I:return{name:I,properties:[{name:"label"},{name:"value"}]};case z:return{name:z,properties:[{name:"accDescr"},{name:"accTitle"},{name:"axes",defaultValue:[]},{name:"curves",defaultValue:[]},{name:"options",defaultValue:[]},{name:"title"}]};case le:return{name:le,properties:[{name:"icon"},{name:"iconText"},{name:"id"},{name:"in"},{name:"title"}]};case pe:return{name:pe,properties:[{name:"accDescr"},{name:"accTitle"},{name:"title"},{name:"TreemapRows",defaultValue:[]}]};case me:return{name:me,properties:[{name:"indent"},{name:"item"}]};case vt:return{name:vt,properties:[{name:"accDescr"},{name:"accTitle"},{name:"dir"},{name:"statements",defaultValue:[]},{name:"title"}]};case at:return{name:at,properties:[{name:"classSelector"},{name:"name"},{name:"value"}]};case Zt:return{name:Zt,properties:[{name:"classSelector"},{name:"name"}]};default:return{name:xe,properties:[]}}}},p(St,"MermaidAstReflection"),St),lt=new It,Ct,cn=p(()=>Ct!=null?Ct:Ct=T(`{"$type":"Grammar","isDeclared":true,"name":"Info","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Info","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"info"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"},{"$type":"Group","elements":[{"$type":"Keyword","value":"showInfo"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"*"}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@7"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`),"InfoGrammar"),Qe,gn=p(()=>Qe!=null?Qe:Qe=T(`{"$type":"Grammar","isDeclared":true,"name":"Packet","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Packet","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"packet"},{"$type":"Keyword","value":"packet-beta"}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"blocks","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PacketBlock","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"start","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"end","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}],"cardinality":"?"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"+"},{"$type":"Assignment","feature":"bits","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]}]},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`),"PacketGrammar"),ct,bn=p(()=>ct!=null?ct:ct=T(`{"$type":"Grammar","isDeclared":true,"name":"Pie","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Pie","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"pie"},{"$type":"Assignment","feature":"showData","operator":"?=","terminal":{"$type":"Keyword","value":"showData"},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"Assignment","feature":"sections","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"PieSection","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@8"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@9"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`),"PieGrammar"),kt,qe=p(()=>kt!=null?kt:kt=T(`{"$type":"Grammar","isDeclared":true,"name":"Architecture","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Architecture","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"*"},{"$type":"Keyword","value":"architecture-beta"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"groups","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Assignment","feature":"services","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Assignment","feature":"junctions","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Assignment","feature":"edges","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"LeftPort","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":":"},{"$type":"Assignment","feature":"lhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"RightPort","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"rhsDir","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}},{"$type":"Keyword","value":":"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Arrow","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Assignment","feature":"lhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"--"},{"$type":"Group","elements":[{"$type":"Keyword","value":"-"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]}},{"$type":"Keyword","value":"-"}]}]},{"$type":"Assignment","feature":"rhsInto","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Group","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"group"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]},"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Service","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"service"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"iconText","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]}},{"$type":"Assignment","feature":"icon","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@28"},"arguments":[]}}],"cardinality":"?"},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@29"},"arguments":[]},"cardinality":"?"},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Junction","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"junction"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"in"},{"$type":"Assignment","feature":"in","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Edge","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"lhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"lhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Assignment","feature":"rhsId","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Assignment","feature":"rhsGroup","operator":"?=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"ARROW_DIRECTION","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"L"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"R"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"T"}}]},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"B"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_GROUP","definition":{"$type":"RegexToken","regex":"/\\\\{group\\\\}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARROW_INTO","definition":{"$type":"RegexToken","regex":"/<|>/"},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@18"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@19"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"ARCH_ICON","definition":{"$type":"RegexToken","regex":"/\\\\([\\\\w-:]+\\\\)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ARCH_TITLE","definition":{"$type":"RegexToken","regex":"/\\\\[[\\\\w ]+\\\\]/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`),"ArchitectureGrammar"),dt,Dn=p(()=>dt!=null?dt:dt=T(`{"$type":"Grammar","isDeclared":true,"name":"GitGraph","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"GitGraph","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"Keyword","value":":"}]},{"$type":"Keyword","value":"gitGraph:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"gitGraph"},{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]},{"$type":"Keyword","value":":"}]}]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"Assignment","feature":"statements","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Statement","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Direction","definition":{"$type":"Assignment","feature":"dir","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"LR"},{"$type":"Keyword","value":"TB"},{"$type":"Keyword","value":"BT"}]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Commit","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"commit"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"msg:","cardinality":"?"},{"$type":"Assignment","feature":"message","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Branch","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"branch"},{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Group","elements":[{"$type":"Keyword","value":"order:"},{"$type":"Assignment","feature":"order","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}}],"cardinality":"?"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Merge","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"merge"},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"type:"},{"$type":"Assignment","feature":"type","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"NORMAL"},{"$type":"Keyword","value":"REVERSE"},{"$type":"Keyword","value":"HIGHLIGHT"}]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Checkout","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"checkout"},{"$type":"Keyword","value":"switch"}]},{"$type":"Assignment","feature":"branch","operator":"=","terminal":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@24"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"CherryPicking","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"cherry-pick"},{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Keyword","value":"id:"},{"$type":"Assignment","feature":"id","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"tag:"},{"$type":"Assignment","feature":"tags","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"parent:"},{"$type":"Assignment","feature":"parent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@14"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false},{"$type":"TerminalRule","name":"REFERENCE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\\\w([-\\\\./\\\\w]*[-\\\\w])?/"},"fragment":false,"hidden":false}],"definesHiddenTokens":false,"hiddenTokens":[],"interfaces":[],"types":[],"usedGrammars":[]}`),"GitGraphGrammar"),ft,yn=p(()=>ft!=null?ft:ft=T(`{"$type":"Grammar","isDeclared":true,"name":"Radar","imports":[],"rules":[{"$type":"ParserRule","entry":true,"name":"Radar","definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":"radar-beta:"},{"$type":"Group","elements":[{"$type":"Keyword","value":"radar-beta"},{"$type":"Keyword","value":":"}]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]},{"$type":"Group","elements":[{"$type":"Keyword","value":"axis"},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"axes","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Keyword","value":"curve"},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"curves","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"Assignment","feature":"options","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]}}],"cardinality":"*"}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Label","definition":{"$type":"Group","elements":[{"$type":"Keyword","value":"["},{"$type":"Assignment","feature":"label","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]}},{"$type":"Keyword","value":"]"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Axis","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Curve","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@1"},"arguments":[],"cardinality":"?"},{"$type":"Keyword","value":"{"},{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]},{"$type":"Keyword","value":"}"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"Entries","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"Keyword","value":","},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"},{"$type":"Assignment","feature":"entries","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@5"},"arguments":[]}}],"cardinality":"*"},{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"*"}]}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"DetailedEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"axis","operator":"=","terminal":{"$type":"CrossReference","type":{"$ref":"#/rules@2"},"terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"deprecatedSyntax":false}},{"$type":"Keyword","value":":","cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"NumberEntry","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Option","definition":{"$type":"Alternatives","elements":[{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"showLegend"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@11"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"ticks"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"max"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"min"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}}]},{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"Keyword","value":"graticule"}},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]}}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"GRATICULE","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"circle"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"polygon"}}]},"fragment":false,"hidden":false},{"$type":"ParserRule","fragment":true,"name":"EOL","dataType":"string","definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[],"cardinality":"+"},{"$type":"EndOfFile"}]},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Group","elements":[{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@12"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@13"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"FLOAT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/[0-9]+\\\\.[0-9]+(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"INT","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"RegexToken","regex":"/0|[1-9][0-9]*(?!\\\\.)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER","type":{"$type":"ReturnType","name":"number"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@15"}},{"$type":"TerminalRuleCall","rule":{"$ref":"#/rules@16"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STRING","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/\\"([^\\"\\\\\\\\]|\\\\\\\\.)*\\"|'([^'\\\\\\\\]|\\\\\\\\.)*'/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID","type":{"$type":"ReturnType","name":"string"},"definition":{"$type":"RegexToken","regex":"/[\\\\w]([-\\\\w]*\\\\w)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NEWLINE","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WHITESPACE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"YAML","definition":{"$type":"RegexToken","regex":"/---[\\\\t ]*\\\\r?\\\\n(?:[\\\\S\\\\s]*?\\\\r?\\\\n)?---(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"DIRECTIVE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%{[\\\\S\\\\s]*?}%%(?:\\\\r?\\\\n|(?!\\\\S))/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"SINGLE_LINE_COMMENT","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*%%[^\\\\n\\\\r]*/"},"fragment":false}],"interfaces":[{"$type":"Interface","name":"Entry","attributes":[{"$type":"TypeAttribute","name":"axis","isOptional":true,"type":{"$type":"ReferenceType","referenceType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@2"}}}},{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"types":[],"usedGrammars":[]}`),"RadarGrammar"),ht,Zn=p(()=>ht!=null?ht:ht=T(`{"$type":"Grammar","isDeclared":true,"name":"Treemap","rules":[{"$type":"ParserRule","fragment":true,"name":"TitleAndAccessibilities","definition":{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"accDescr","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@2"},"arguments":[]}},{"$type":"Assignment","feature":"accTitle","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@3"},"arguments":[]}},{"$type":"Assignment","feature":"title","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@4"},"arguments":[]}}],"cardinality":"+"},"definesHiddenTokens":false,"entry":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"BOOLEAN","type":{"$type":"ReturnType","name":"boolean"},"definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"true"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"false"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_DESCR","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accDescr(?:[\\\\t ]*:([^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)|\\\\s*{([^}]*)})/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ACC_TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*accTitle[\\\\t ]*:(?:[^\\\\n\\\\r]*?(?=%%)|[^\\\\n\\\\r]*)/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"TITLE","definition":{"$type":"RegexToken","regex":"/[\\\\t ]*title(?:[\\\\t ][^\\\\n\\\\r]*?(?=%%)|[\\\\t ][^\\\\n\\\\r]*|)/"},"fragment":false,"hidden":false},{"$type":"ParserRule","entry":true,"name":"Treemap","returnType":{"$ref":"#/interfaces@4"},"definition":{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@6"},"arguments":[]},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@0"},"arguments":[]},{"$type":"Assignment","feature":"TreemapRows","operator":"+=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@14"},"arguments":[]}}],"cardinality":"*"}]},"definesHiddenTokens":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"TREEMAP_KEYWORD","definition":{"$type":"TerminalAlternatives","elements":[{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap-beta"}},{"$type":"CharacterRange","left":{"$type":"Keyword","value":"treemap"}}]},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"CLASS_DEF","definition":{"$type":"RegexToken","regex":"/classDef\\\\s+([a-zA-Z_][a-zA-Z0-9_]+)(?:\\\\s+([^;\\\\r\\\\n]*))?(?:;)?/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"STYLE_SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":::"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"SEPARATOR","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":":"}},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"COMMA","definition":{"$type":"CharacterRange","left":{"$type":"Keyword","value":","}},"fragment":false,"hidden":false},{"$type":"TerminalRule","hidden":true,"name":"WS","definition":{"$type":"RegexToken","regex":"/[ \\\\t]+/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"ML_COMMENT","definition":{"$type":"RegexToken","regex":"/\\\\%\\\\%[^\\\\n]*/"},"fragment":false},{"$type":"TerminalRule","hidden":true,"name":"NL","definition":{"$type":"RegexToken","regex":"/\\\\r?\\\\n/"},"fragment":false},{"$type":"ParserRule","name":"TreemapRow","definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"indent","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[]},"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"Assignment","feature":"item","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@16"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@15"},"arguments":[]}]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"ClassDef","dataType":"string","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@7"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Item","returnType":{"$ref":"#/interfaces@0"},"definition":{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@18"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@17"},"arguments":[]}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Section","returnType":{"$ref":"#/interfaces@1"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"ParserRule","name":"Leaf","returnType":{"$ref":"#/interfaces@2"},"definition":{"$type":"Group","elements":[{"$type":"Assignment","feature":"name","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@23"},"arguments":[]}},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Alternatives","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@9"},"arguments":[]},{"$type":"RuleCall","rule":{"$ref":"#/rules@10"},"arguments":[]}]},{"$type":"RuleCall","rule":{"$ref":"#/rules@19"},"arguments":[],"cardinality":"?"},{"$type":"Assignment","feature":"value","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@22"},"arguments":[]}},{"$type":"Group","elements":[{"$type":"RuleCall","rule":{"$ref":"#/rules@8"},"arguments":[]},{"$type":"Assignment","feature":"classSelector","operator":"=","terminal":{"$type":"RuleCall","rule":{"$ref":"#/rules@20"},"arguments":[]}}],"cardinality":"?"}]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"INDENTATION","definition":{"$type":"RegexToken","regex":"/[ \\\\t]{1,}/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"ID2","definition":{"$type":"RegexToken","regex":"/[a-zA-Z_][a-zA-Z0-9_]*/"},"fragment":false,"hidden":false},{"$type":"TerminalRule","name":"NUMBER2","definition":{"$type":"RegexToken","regex":"/[0-9_\\\\.\\\\,]+/"},"fragment":false,"hidden":false},{"$type":"ParserRule","name":"MyNumber","dataType":"number","definition":{"$type":"RuleCall","rule":{"$ref":"#/rules@21"},"arguments":[]},"definesHiddenTokens":false,"entry":false,"fragment":false,"hiddenTokens":[],"parameters":[],"wildcard":false},{"$type":"TerminalRule","name":"STRING2","definition":{"$type":"RegexToken","regex":"/\\"[^\\"]*\\"|'[^']*'/"},"fragment":false,"hidden":false}],"interfaces":[{"$type":"Interface","name":"Item","attributes":[{"$type":"TypeAttribute","name":"name","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"classSelector","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]},{"$type":"Interface","name":"Section","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[]},{"$type":"Interface","name":"Leaf","superTypes":[{"$ref":"#/interfaces@0"}],"attributes":[{"$type":"TypeAttribute","name":"value","type":{"$type":"SimpleType","primitiveType":"number"},"isOptional":false}]},{"$type":"Interface","name":"ClassDefStatement","attributes":[{"$type":"TypeAttribute","name":"className","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false},{"$type":"TypeAttribute","name":"styleText","type":{"$type":"SimpleType","primitiveType":"string"},"isOptional":false}],"superTypes":[]},{"$type":"Interface","name":"Treemap","attributes":[{"$type":"TypeAttribute","name":"TreemapRows","type":{"$type":"ArrayType","elementType":{"$type":"SimpleType","typeRef":{"$ref":"#/rules@14"}}},"isOptional":false},{"$type":"TypeAttribute","name":"title","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accTitle","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}},{"$type":"TypeAttribute","name":"accDescr","isOptional":true,"type":{"$type":"SimpleType","primitiveType":"string"}}],"superTypes":[]}],"definesHiddenTokens":false,"hiddenTokens":[],"imports":[],"types":[],"usedGrammars":[],"$comment":"/**\\n * Treemap grammar for Langium\\n * Converted from mindmap grammar\\n *\\n * The ML_COMMENT and NL hidden terminals handle whitespace, comments, and newlines\\n * before the treemap keyword, allowing for empty lines and comments before the\\n * treemap declaration.\\n */"}`),"TreemapGrammar"),Ye={languageId:"info",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Fn={languageId:"packet",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Bt={languageId:"pie",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Un={languageId:"architecture",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Wt={languageId:"gitGraph",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Gn={languageId:"radar",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},nt={languageId:"treemap",fileExtensions:[".mmd",".mermaid"],caseInsensitive:!1,mode:"production"},Bn={AstReflection:p(()=>new It,"AstReflection")},xt={Grammar:p(()=>cn(),"Grammar"),LanguageMetaData:p(()=>Ye,"LanguageMetaData"),parser:{}},Tn={Grammar:p(()=>gn(),"Grammar"),LanguageMetaData:p(()=>Fn,"LanguageMetaData"),parser:{}},rt={Grammar:p(()=>bn(),"Grammar"),LanguageMetaData:p(()=>Bt,"LanguageMetaData"),parser:{}},Ft={Grammar:p(()=>qe(),"Grammar"),LanguageMetaData:p(()=>Un,"LanguageMetaData"),parser:{}},Kt={Grammar:p(()=>Dn(),"Grammar"),LanguageMetaData:p(()=>Wt,"LanguageMetaData"),parser:{}},Qt={Grammar:p(()=>yn(),"Grammar"),LanguageMetaData:p(()=>Gn,"LanguageMetaData"),parser:{}},Vt={Grammar:p(()=>Zn(),"Grammar"),LanguageMetaData:p(()=>nt,"LanguageMetaData"),parser:{}},vn=/accDescr(?:[\t ]*:([^\n\r]*)|\s*{([^}]*)})/,jt=/accTitle[\t ]*:([^\n\r]*)/,Wn=/title([\t ][^\n\r]*|)/,Ht={ACC_DESCR:vn,ACC_TITLE:jt,TITLE:Wn},Kn=(ut=class extends E.t{runConverter(xe,Ce,pt){let Et=this.runCommonConverter(xe,Ce,pt);return Et===void 0&&(Et=this.runCustomConverter(xe,Ce,pt)),Et===void 0?super.runConverter(xe,Ce,pt):Et}runCommonConverter(xe,Ce,pt){const Et=Ht[xe.name];if(Et===void 0)return;const mt=Et.exec(Ce);if(mt!==null){if(mt[1]!==void 0)return mt[1].trim().replace(/[\t ]{2,}/gm," ");if(mt[2]!==void 0)return mt[2].replace(/^\s*/gm,"").replace(/\s+$/gm,"").replace(/[\t ]{2,}/gm," ").replace(/[\n\r]{2,}/gm,`
`)}}},p(ut,"AbstractMermaidValueConverter"),ut),it=(Rt=class extends Kn{runCustomConverter(xe,Ce,pt){}},p(Rt,"CommonValueConverter"),Rt),be=(Ne=class extends _.P{constructor(xe){super(),this.keywords=new Set(xe)}buildKeywordTokens(xe,Ce,pt){const Et=super.buildKeywordTokens(xe,Ce,pt);return Et.forEach(mt=>{this.keywords.has(mt.name)&&mt.PATTERN!==void 0&&(mt.PATTERN=new RegExp(mt.PATTERN.toString()+"(?:(?=%%)|(?!\\S))"))}),Et}},p(Ne,"AbstractMermaidTokenBuilder"),Ne),wt=(ee=class extends be{},p(ee,"CommonTokenBuilder"),ee)},65832:function(Te,j,g){var B;g.d(j,{z:function(){return V}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(B=class extends y.T7{constructor(){super(["gitGraph"])}},(0,y.eW)(B,"GitGraphTokenBuilder"),B),b={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new y.nr,"ValueConverter")}};function V(R=O.u){const T=(0,F.f3)((0,x.T)(R),y.GS),E=(0,F.f3)((0,x.Q)({shared:T}),y.vn,b);return T.ServiceRegistry.register(E),{shared:T,GitGraph:E}}(0,y.eW)(V,"createGitGraphServices")},30589:function(Te,j,g){var R,T;g.d(j,{i:function(){return B}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(R=class extends y.T7{constructor(){super(["architecture"])}},(0,y.eW)(R,"ArchitectureTokenBuilder"),R),b=(T=class extends y.kb{runCustomConverter(_,v,p){if(_.name==="ARCH_ICON")return v.replace(/[()]/g,"").trim();if(_.name==="ARCH_TEXT_ICON")return v.replace(/["()]/g,"");if(_.name==="ARCH_TITLE")return v.replace(/[[\]]/g,"").trim()}},(0,y.eW)(T,"ArchitectureValueConverter"),T),V={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new b,"ValueConverter")}};function B(E=O.u){const _=(0,F.f3)((0,x.T)(E),y.GS),v=(0,F.f3)((0,x.Q)({shared:_}),y.Qr,V);return _.ServiceRegistry.register(v),{shared:_,Architecture:v}}(0,y.eW)(B,"createArchitectureServices")},1478:function(Te,j,g){var B;g.d(j,{g:function(){return V}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(B=class extends y.T7{constructor(){super(["packet"])}},(0,y.eW)(B,"PacketTokenBuilder"),B),b={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new y.nr,"ValueConverter")}};function V(R=O.u){const T=(0,F.f3)((0,x.T)(R),y.GS),E=(0,F.f3)((0,x.Q)({shared:T}),y.bb,b);return T.ServiceRegistry.register(E),{shared:T,Packet:E}}(0,y.eW)(V,"createPacketServices")},82038:function(Te,j,g){var B;g.d(j,{M:function(){return V}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(B=class extends y.T7{constructor(){super(["info","showInfo"])}},(0,y.eW)(B,"InfoTokenBuilder"),B),b={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new y.nr,"ValueConverter")}};function V(R=O.u){const T=(0,F.f3)((0,x.T)(R),y.GS),E=(0,F.f3)((0,x.Q)({shared:T}),y.F_,b);return T.ServiceRegistry.register(E),{shared:T,Info:E}}(0,y.eW)(V,"createInfoServices")},37458:function(Te,j,g){var B;g.d(j,{T:function(){return V}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(B=class extends y.T7{constructor(){super(["radar-beta"])}},(0,y.eW)(B,"RadarTokenBuilder"),B),b={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new y.nr,"ValueConverter")}};function V(R=O.u){const T=(0,F.f3)((0,x.T)(R),y.GS),E=(0,F.f3)((0,x.Q)({shared:T}),y.gB,b);return T.ServiceRegistry.register(E),{shared:T,Radar:E}}(0,y.eW)(V,"createRadarServices")},65714:function(Te,j,g){var R,T;g.d(j,{l:function(){return B}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(R=class extends y.T7{constructor(){super(["pie","showData"])}},(0,y.eW)(R,"PieTokenBuilder"),R),b=(T=class extends y.kb{runCustomConverter(_,v,p){if(_.name==="PIE_SECTION_LABEL")return v.replace(/"/g,"").trim()}},(0,y.eW)(T,"PieValueConverter"),T),V={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new b,"ValueConverter")}};function B(E=O.u){const _=(0,F.f3)((0,x.T)(E),y.GS),v=(0,F.f3)((0,x.Q)({shared:_}),y.WH,V);return _.ServiceRegistry.register(v),{shared:_,Pie:v}}(0,y.eW)(B,"createPieServices")},7843:function(Te,j,g){var _,v,p;g.d(j,{K:function(){return E}});var y=g(6612),O=g(35003),F=g(93044),x=g(79837),L=(_=class extends y.T7{constructor(){super(["treemap"])}},(0,y.eW)(_,"TreemapTokenBuilder"),_),b=/classDef\s+([A-Z_a-z]\w+)(?:\s+([^\n\r;]*))?;?/,V=(v=class extends y.kb{runCustomConverter(h,S,w){if(h.name==="NUMBER2")return parseFloat(S.replace(/,/g,""));if(h.name==="SEPARATOR")return S.substring(1,S.length-1);if(h.name==="STRING2")return S.substring(1,S.length-1);if(h.name==="INDENTATION")return S.length;if(h.name==="ClassDef"){if(typeof S!="string")return S;const Z=b.exec(S);if(Z)return{$type:"ClassDefStatement",className:Z[1],styleText:Z[2]||void 0}}}},(0,y.eW)(v,"TreemapValueConverter"),v);function B(C){const h=C.validation.TreemapValidator,S=C.validation.ValidationRegistry;if(S){const w={Treemap:h.checkSingleRoot.bind(h)};S.register(w,h)}}(0,y.eW)(B,"registerValidationChecks");var R=(p=class{checkSingleRoot(h,S){let w;for(const Z of h.TreemapRows)Z.item&&(w===void 0&&Z.indent===void 0?w=0:Z.indent===void 0?S("error","Multiple root nodes are not allowed in a treemap.",{node:Z,property:"item"}):w!==void 0&&w>=parseInt(Z.indent,10)&&S("error","Multiple root nodes are not allowed in a treemap.",{node:Z,property:"item"}))}},(0,y.eW)(p,"TreemapValidator"),p),T={parser:{TokenBuilder:(0,y.eW)(()=>new L,"TokenBuilder"),ValueConverter:(0,y.eW)(()=>new V,"ValueConverter")},validation:{TreemapValidator:(0,y.eW)(()=>new R,"TreemapValidator")}};function E(C=O.u){const h=(0,F.f3)((0,x.T)(C),y.GS),S=(0,F.f3)((0,x.Q)({shared:h}),y.E$,T);return h.ServiceRegistry.register(S),B(S),{shared:h,Treemap:S}}(0,y.eW)(E,"createTreemapServices")},38663:function(Te,j,g){var v;g.d(j,{Qc:function(){return E}});var y=g(65832),O=g(82038),F=g(1478),x=g(65714),L=g(30589),b=g(37458),V=g(7843),B=g(6612),R={},T={info:(0,B.eW)(()=>Re(this,null,function*(){const{createInfoServices:p}=yield g.e(39).then(g.bind(g,50039)),C=p().Info.parser.LangiumParser;R.info=C}),"info"),packet:(0,B.eW)(()=>Re(this,null,function*(){const{createPacketServices:p}=yield g.e(9523).then(g.bind(g,99523)),C=p().Packet.parser.LangiumParser;R.packet=C}),"packet"),pie:(0,B.eW)(()=>Re(this,null,function*(){const{createPieServices:p}=yield g.e(4249).then(g.bind(g,84249)),C=p().Pie.parser.LangiumParser;R.pie=C}),"pie"),architecture:(0,B.eW)(()=>Re(this,null,function*(){const{createArchitectureServices:p}=yield g.e(7834).then(g.bind(g,67834)),C=p().Architecture.parser.LangiumParser;R.architecture=C}),"architecture"),gitGraph:(0,B.eW)(()=>Re(this,null,function*(){const{createGitGraphServices:p}=yield g.e(8425).then(g.bind(g,72344)),C=p().GitGraph.parser.LangiumParser;R.gitGraph=C}),"gitGraph"),radar:(0,B.eW)(()=>Re(this,null,function*(){const{createRadarServices:p}=yield g.e(3672).then(g.bind(g,53672)),C=p().Radar.parser.LangiumParser;R.radar=C}),"radar"),treemap:(0,B.eW)(()=>Re(this,null,function*(){const{createTreemapServices:p}=yield g.e(4548).then(g.bind(g,34548)),C=p().Treemap.parser.LangiumParser;R.treemap=C}),"treemap")};function E(p,C){return Re(this,null,function*(){const h=T[p];if(!h)throw new Error(`Unknown diagram type: ${p}`);R[p]||(yield h());const w=R[p].parse(C);if(w.lexerErrors.length>0||w.parserErrors.length>0)throw new _(w);return w.value})}(0,B.eW)(E,"parse");var _=(v=class extends Error{constructor(C){const h=C.lexerErrors.map(w=>w.message).join(`
`),S=C.parserErrors.map(w=>w.message).join(`
`);super(`Parsing failed: ${h} ${S}`),this.result=C}},(0,B.eW)(v,"MermaidParseError"),v)},76597:function(Te,j,g){g.d(j,{ue:function(){return Ie},_o:function(){return ba},sd:function(){return sn},nu:function(){return Da},dV:function(){return ua},hW:function(){return At},Sj:function(){return He},Wx:function(){return I},hI:function(){return le},ej:function(){return W},fK:function(){return z},pT:function(){return pe},oI:function(){return me},ZW:function(){return Vs},Hs:function(){return Ke},oC:function(){return Gi},l$:function(){return Sn},ol:function(){return Js}});var y=g(24285),O=g(10541),F=g(67127),x=g(857),L=g(67876),b=g(24220);function V(o){function t(){}t.prototype=o;const a=new t;function u(){return typeof a.bar}return u(),u(),o;(0,eval)(o)}var B=g(58566),R=g(42013);function T(o,t,a){var u=o==null?0:o.length;return u?(t=a||t===void 0?1:(0,R.Z)(t),(0,B.Z)(o,t<0?0:t,u)):[]}var E=T,_=g(56288),v=g(23996),p=g(21169),C=g(80824),h=g(13932),S=g(12375),w=g(92769),Z=Object.prototype,P=Z.hasOwnProperty,Q=(0,C.Z)(function(o,t){if((0,S.Z)(t)||(0,h.Z)(t)){(0,p.Z)(t,(0,w.Z)(t),o);return}for(var a in t)P.call(t,a)&&(0,v.Z)(o,a,t[a])}),J=Q,ve=g(3271),K=g(18458),ae=g(70648),ce=g(29488);function ye(o,t){if(o==null)return{};var a=(0,ve.Z)((0,ce.Z)(o),function(u){return[u]});return t=(0,K.Z)(t),(0,ae.Z)(o,a,function(u,d){return t(u,d[0])})}var Ae=ye,Se=g(71395),Je=g(54764),Ge="[object RegExp]";function oe(o){return(0,Je.Z)(o)&&(0,Se.Z)(o)==Ge}var Y=oe,se=g(70544),re=g(13851),ke=re.Z&&re.Z.isRegExp,ge=ke?(0,se.Z)(ke):Y,he=ge;function Me(o){return je(o)?o.LABEL:o.name}function je(o){return(0,_.Z)(o.LABEL)&&o.LABEL!==""}class $e{get definition(){return this._definition}set definition(t){this._definition=t}constructor(t){this._definition=t}accept(t){t.visit(this),(0,y.Z)(this.definition,a=>{a.accept(t)})}}class He extends $e{constructor(t){super([]),this.idx=1,J(this,Ae(t,a=>a!==void 0))}set definition(t){}get definition(){return this.referencedRule!==void 0?this.referencedRule.definition:[]}accept(t){t.visit(this)}}class ze extends $e{constructor(t){super(t.definition),this.orgText="",J(this,Ae(t,a=>a!==void 0))}}class Ze extends $e{constructor(t){super(t.definition),this.ignoreAmbiguities=!1,J(this,Ae(t,a=>a!==void 0))}}class I extends $e{constructor(t){super(t.definition),this.idx=1,J(this,Ae(t,a=>a!==void 0))}}class W extends $e{constructor(t){super(t.definition),this.idx=1,J(this,Ae(t,a=>a!==void 0))}}class z extends $e{constructor(t){super(t.definition),this.idx=1,J(this,Ae(t,a=>a!==void 0))}}class le extends $e{constructor(t){super(t.definition),this.idx=1,J(this,Ae(t,a=>a!==void 0))}}class pe extends $e{constructor(t){super(t.definition),this.idx=1,J(this,Ae(t,a=>a!==void 0))}}class Ie extends $e{get definition(){return this._definition}set definition(t){this._definition=t}constructor(t){super(t.definition),this.idx=1,this.ignoreAmbiguities=!1,this.hasPredicates=!1,J(this,Ae(t,a=>a!==void 0))}}class me{constructor(t){this.idx=1,J(this,Ae(t,a=>a!==void 0))}accept(t){t.visit(this)}}function vt(o){return(0,x.Z)(o,at)}function at(o){function t(a){return(0,x.Z)(a,at)}if(o instanceof He){const a={type:"NonTerminal",name:o.nonTerminalName,idx:o.idx};return(0,_.Z)(o.label)&&(a.label=o.label),a}else{if(o instanceof Ze)return{type:"Alternative",definition:t(o.definition)};if(o instanceof I)return{type:"Option",idx:o.idx,definition:t(o.definition)};if(o instanceof W)return{type:"RepetitionMandatory",idx:o.idx,definition:t(o.definition)};if(o instanceof z)return{type:"RepetitionMandatoryWithSeparator",idx:o.idx,separator:at(new me({terminalType:o.separator})),definition:t(o.definition)};if(o instanceof pe)return{type:"RepetitionWithSeparator",idx:o.idx,separator:at(new me({terminalType:o.separator})),definition:t(o.definition)};if(o instanceof le)return{type:"Repetition",idx:o.idx,definition:t(o.definition)};if(o instanceof Ie)return{type:"Alternation",idx:o.idx,definition:t(o.definition)};if(o instanceof me){const a={type:"Terminal",name:o.terminalType.name,label:Me(o.terminalType),idx:o.idx};(0,_.Z)(o.label)&&(a.terminalLabel=o.label);const u=o.terminalType.PATTERN;return o.terminalType.PATTERN&&(a.pattern=he(u)?u.source:u),a}else{if(o instanceof ze)return{type:"Rule",name:o.name,orgText:o.orgText,definition:t(o.definition)};throw Error("non exhaustive match")}}}class Zt{walk(t,a=[]){(0,y.Z)(t.definition,(u,d)=>{const f=E(t.definition,d+1);if(u instanceof He)this.walkProdRef(u,f,a);else if(u instanceof me)this.walkTerminal(u,f,a);else if(u instanceof Ze)this.walkFlat(u,f,a);else if(u instanceof I)this.walkOption(u,f,a);else if(u instanceof W)this.walkAtLeastOne(u,f,a);else if(u instanceof z)this.walkAtLeastOneSep(u,f,a);else if(u instanceof pe)this.walkManySep(u,f,a);else if(u instanceof le)this.walkMany(u,f,a);else if(u instanceof Ie)this.walkOr(u,f,a);else throw Error("non exhaustive match")})}walkTerminal(t,a,u){}walkProdRef(t,a,u){}walkFlat(t,a,u){const d=a.concat(u);this.walk(t,d)}walkOption(t,a,u){const d=a.concat(u);this.walk(t,d)}walkAtLeastOne(t,a,u){const d=[new I({definition:t.definition})].concat(a,u);this.walk(t,d)}walkAtLeastOneSep(t,a,u){const d=It(t,a,u);this.walk(t,d)}walkMany(t,a,u){const d=[new I({definition:t.definition})].concat(a,u);this.walk(t,d)}walkManySep(t,a,u){const d=It(t,a,u);this.walk(t,d)}walkOr(t,a,u){const d=a.concat(u);(0,y.Z)(t.definition,f=>{const A=new Ze({definition:[f]});this.walk(A,d)})}}function It(o,t,a){return[new I({definition:[new me({terminalType:o.separator})].concat(o.definition)})].concat(t,a)}var lt=g(57031);function Ct(o){return o&&o.length?(0,lt.Z)(o):[]}var cn=Ct,Qe=g(42060),gn=g(466),ct=g(61397);function bn(o,t){var a;return(0,ct.Z)(o,function(u,d,f){return a=t(u,d,f),!a}),!!a}var kt=bn,qe=g(83788),dt=g(14026);function Dn(o,t,a){var u=(0,qe.Z)(o)?gn.Z:kt;return a&&(0,dt.Z)(o,t,a)&&(t=void 0),u(o,(0,K.Z)(t,3))}var ft=Dn,yn=g(19213),ht=Math.max;function Zn(o,t,a,u){o=(0,h.Z)(o)?o:(0,O.Z)(o),a=a&&!u?(0,R.Z)(a):0;var d=o.length;return a<0&&(a=ht(d+a,0)),(0,_.Z)(o)?a<=d&&o.indexOf(t,a)>-1:!!d&&(0,yn.Z)(o,t,a)>-1}var Ye=Zn;function Fn(o,t){for(var a=-1,u=o==null?0:o.length;++a<u;)if(!t(o[a],a,o))return!1;return!0}var Bt=Fn;function Un(o,t){var a=!0;return(0,ct.Z)(o,function(u,d,f){return a=!!t(u,d,f),a}),a}var Wt=Un;function Gn(o,t,a){var u=(0,qe.Z)(o)?Bt:Wt;return a&&(0,dt.Z)(o,t,a)&&(t=void 0),u(o,(0,K.Z)(t,3))}var nt=Gn;function Bn(o){return o instanceof Ze||o instanceof I||o instanceof le||o instanceof W||o instanceof z||o instanceof pe||o instanceof me||o instanceof ze}function xt(o,t=[]){return o instanceof I||o instanceof le||o instanceof pe?!0:o instanceof Ie?ft(o.definition,u=>xt(u,t)):o instanceof He&&Ye(t,o)?!1:o instanceof $e?(o instanceof He&&t.push(o),nt(o.definition,u=>xt(u,t))):!1}function Tn(o){return o instanceof Ie}function rt(o){if(o instanceof He)return"SUBRULE";if(o instanceof I)return"OPTION";if(o instanceof Ie)return"OR";if(o instanceof W)return"AT_LEAST_ONE";if(o instanceof z)return"AT_LEAST_ONE_SEP";if(o instanceof pe)return"MANY_SEP";if(o instanceof le)return"MANY";if(o instanceof me)return"CONSUME";throw Error("non exhaustive match")}function Ft(o){if(o instanceof He)return Ft(o.referencedRule);if(o instanceof me)return Vt(o);if(Bn(o))return Kt(o);if(Tn(o))return Qt(o);throw Error("non exhaustive match")}function Kt(o){let t=[];const a=o.definition;let u=0,d=a.length>u,f,A=!0;for(;d&&A;)f=a[u],A=xt(f),t=t.concat(Ft(f)),u=u+1,d=a.length>u;return cn(t)}function Qt(o){const t=(0,x.Z)(o.definition,a=>Ft(a));return cn((0,Qe.Z)(t))}function Vt(o){return[o.terminalType]}const vn="_~IN~_";class jt extends Zt{constructor(t){super(),this.topProd=t,this.follows={}}startWalking(){return this.walk(this.topProd),this.follows}walkTerminal(t,a,u){}walkProdRef(t,a,u){const d=Ht(t.referencedRule,t.idx)+this.topProd.name,f=a.concat(u),A=new Ze({definition:f}),k=Ft(A);this.follows[d]=k}}function Wn(o){const t={};return(0,y.Z)(o,a=>{const u=new jt(a).startWalking();J(t,u)}),t}function Ht(o,t){return o.name+t+vn}function Kn(o){return o.terminalType.name+o.idx+IN}var it=g(80155),be=g(37521),wt=g(35565),St=g(18046),ut=g(16339),Rt="Expected a function";function Ne(o){if(typeof o!="function")throw new TypeError(Rt);return function(){var t=arguments;switch(t.length){case 0:return!o.call(this);case 1:return!o.call(this,t[0]);case 2:return!o.call(this,t[0],t[1]);case 3:return!o.call(this,t[0],t[1],t[2])}return!o.apply(this,t)}}var ee=Ne;function D(o,t){var a=(0,qe.Z)(o)?St.Z:ut.Z;return a(o,ee((0,K.Z)(t,3)))}var xe=D,Ce=g(89477),pt=Math.max;function Et(o,t,a){var u=o==null?0:o.length;if(!u)return-1;var d=a==null?0:(0,R.Z)(a);return d<0&&(d=pt(u+d,0)),(0,yn.Z)(o,t,d)}var mt=Et,gt=g(6613),yt=g(97175),ds=g(33063),fs=g(44867),hs=g(87105),ar=g(78686),Rn=200;function zt(o,t,a,u){var d=-1,f=fs.Z,A=!0,k=o.length,N=[],G=t.length;if(!k)return N;a&&(t=(0,ve.Z)(t,(0,se.Z)(a))),u?(f=hs.Z,A=!1):t.length>=Rn&&(f=ar.Z,A=!1,t=new ds.Z(t));e:for(;++d<k;){var q=o[d],te=a==null?q:a(q);if(q=u||q!==0?q:0,A&&te===te){for(var fe=G;fe--;)if(t[fe]===te)continue e;N.push(q)}else f(t,te,u)||N.push(q)}return N}var En=zt,ot=g(3148),qt=g(93092),Vn=g(45408),jn=(0,qt.Z)(function(o,t){return(0,Vn.Z)(o)?En(o,(0,ot.Z)(t,1,Vn.Z,!0)):[]}),en=jn;function Hn(o){for(var t=-1,a=o==null?0:o.length,u=0,d=[];++t<a;){var f=o[t];f&&(d[u++]=f)}return d}var dn=Hn;function or(o){return o&&o.length?o[0]:void 0}var Nt=or,zn=g(89045);function lr(o){console&&console.error&&console.error(`Error: ${o}`)}function wr(o){console&&console.warn&&console.warn(`Warning: ${o}`)}let fn={};const ps=new be.O;function An(o){const t=o.toString();if(fn.hasOwnProperty(t))return fn[t];{const a=ps.pattern(t);return fn[t]=a,a}}function Yn(){fn={}}const Lr="Complement Sets are not supported for first char optimization",Xn=`Unable to use "first char" lexer optimizations:
`;function ms(o,t=!1){try{const a=An(o);return ur(a.value,{},a.flags.ignoreCase)}catch(a){if(a.message===Lr)t&&wr(`${Xn}	Unable to optimize: < ${o.toString()} >
	Complement Sets cannot be automatically optimized.
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#COMPLEMENT for details.`);else{let u="";t&&(u=`
	This will disable the lexer's first char optimizations.
	See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#REGEXP_PARSING for details.`),lr(`${Xn}
	Failed parsing: < ${o.toString()} >
	Using the @chevrotain/regexp-to-ast library
	Please open an issue at: https://github.com/chevrotain/chevrotain/issues`+u)}}return[]}function ur(o,t,a){switch(o.type){case"Disjunction":for(let d=0;d<o.value.length;d++)ur(o.value[d],t,a);break;case"Alternative":const u=o.value;for(let d=0;d<u.length;d++){const f=u[d];switch(f.type){case"EndAnchor":case"GroupBackReference":case"Lookahead":case"NegativeLookahead":case"StartAnchor":case"WordBoundary":case"NonWordBoundary":continue}const A=f;switch(A.type){case"Character":Jn(A.value,t,a);break;case"Set":if(A.complement===!0)throw Error(Lr);(0,y.Z)(A.value,N=>{if(typeof N=="number")Jn(N,t,a);else{const G=N;if(a===!0)for(let q=G.from;q<=G.to;q++)Jn(q,t,a);else{for(let q=G.from;q<=G.to&&q<$n;q++)Jn(q,t,a);if(G.to>=$n){const q=G.from>=$n?G.from:$n,te=G.to,fe=M(q),Ee=M(te);for(let we=fe;we<=Ee;we++)t[we]=we}}}});break;case"Group":ur(A.value,t,a);break;default:throw Error("Non Exhaustive Match")}const k=A.quantifier!==void 0&&A.quantifier.atLeast===0;if(A.type==="Group"&&cr(A)===!1||A.type!=="Group"&&k===!1)break}break;default:throw Error("non exhaustive match!")}return(0,O.Z)(t)}function Jn(o,t,a){const u=M(o);t[u]=u,a===!0&&gs(o,t)}function gs(o,t){const a=String.fromCharCode(o),u=a.toUpperCase();if(u!==a){const d=M(u.charCodeAt(0));t[d]=d}else{const d=a.toLowerCase();if(d!==a){const f=M(d.charCodeAt(0));t[f]=f}}}function Or(o,t){return(0,zn.Z)(o.value,a=>{if(typeof a=="number")return Ye(t,a);{const u=a;return(0,zn.Z)(t,d=>u.from<=d&&d<=u.to)!==void 0}})}function cr(o){const t=o.quantifier;return t&&t.atLeast===0?!0:o.value?(0,qe.Z)(o.value)?nt(o.value,cr):cr(o.value):!1}class dr extends be.e{constructor(t){super(),this.targetCharCodes=t,this.found=!1}visitChildren(t){if(this.found!==!0){switch(t.type){case"Lookahead":this.visitLookahead(t);return;case"NegativeLookahead":this.visitNegativeLookahead(t);return}super.visitChildren(t)}}visitCharacter(t){Ye(this.targetCharCodes,t.value)&&(this.found=!0)}visitSet(t){t.complement?Or(t,this.targetCharCodes)===void 0&&(this.found=!0):Or(t,this.targetCharCodes)!==void 0&&(this.found=!0)}}function fr(o,t){if(t instanceof RegExp){const a=An(t),u=new dr(o);return u.visit(a),u.found}else return(0,zn.Z)(t,a=>Ye(o,a.charCodeAt(0)))!==void 0}const tn="PATTERN",_n="defaultMode",Qn="modes";let qn=typeof new RegExp("(?:)").sticky=="boolean";function xi(){qn=!1}function Si(){qn=!0}function ys(o,t){t=(0,wt.Z)(t,{useSticky:qn,debug:!1,safeMode:!1,positionTracking:"full",lineTerminatorCharacters:["\r",`
`],tracer:(de,ie)=>ie()});const a=t.tracer;a("initCharCodeToOptimizedIndexMap",()=>{Ni()});let u;a("Reject Lexer.NA",()=>{u=xe(o,de=>de[tn]===At.NA)});let d=!1,f;a("Transform Patterns",()=>{d=!1,f=(0,x.Z)(u,de=>{const ie=de[tn];if(he(ie)){const Oe=ie.source;return Oe.length===1&&Oe!=="^"&&Oe!=="$"&&Oe!=="."&&!ie.ignoreCase?Oe:Oe.length===2&&Oe[0]==="\\"&&!Ye(["d","D","s","S","t","r","n","t","0","c","b","B","f","v","w","W"],Oe[1])?Oe[1]:t.useSticky?pr(ie):Pr(ie)}else{if((0,Ce.Z)(ie))return d=!0,{exec:ie};if(typeof ie=="object")return d=!0,ie;if(typeof ie=="string"){if(ie.length===1)return ie;{const Oe=ie.replace(/[\\^$.*+?()[\]{}|]/g,"\\$&"),Pe=new RegExp(Oe);return t.useSticky?pr(Pe):Pr(Pe)}}else throw Error("non exhaustive match")}})});let A,k,N,G,q;a("misc mapping",()=>{A=(0,x.Z)(u,de=>de.tokenTypeIdx),k=(0,x.Z)(u,de=>{const ie=de.GROUP;if(ie!==At.SKIPPED){if((0,_.Z)(ie))return ie;if((0,it.Z)(ie))return!1;throw Error("non exhaustive match")}}),N=(0,x.Z)(u,de=>{const ie=de.LONGER_ALT;if(ie)return(0,qe.Z)(ie)?(0,x.Z)(ie,Pe=>mt(u,Pe)):[mt(u,ie)]}),G=(0,x.Z)(u,de=>de.PUSH_MODE),q=(0,x.Z)(u,de=>(0,L.Z)(de,"POP_MODE"))});let te;a("Line Terminator Handling",()=>{const de=Zr(t.lineTerminatorCharacters);te=(0,x.Z)(u,ie=>!1),t.positionTracking!=="onlyOffset"&&(te=(0,x.Z)(u,ie=>(0,L.Z)(ie,"LINE_BREAKS")?!!ie.LINE_BREAKS:Dr(ie,de)===!1&&fr(de,ie.PATTERN)))});let fe,Ee,we,Le;a("Misc Mapping #2",()=>{fe=(0,x.Z)(u,br),Ee=(0,x.Z)(f,Ms),we=(0,gt.Z)(u,(de,ie)=>{const Oe=ie.GROUP;return(0,_.Z)(Oe)&&Oe!==At.SKIPPED&&(de[Oe]=[]),de},{}),Le=(0,x.Z)(f,(de,ie)=>({pattern:f[ie],longerAlt:N[ie],canLineTerminator:te[ie],isCustom:fe[ie],short:Ee[ie],group:k[ie],push:G[ie],pop:q[ie],tokenTypeIdx:A[ie],tokenType:u[ie]}))});let Be=!0,_e=[];return t.safeMode||a("First Char Optimization",()=>{_e=(0,gt.Z)(u,(de,ie,Oe)=>{if(typeof ie.PATTERN=="string"){const Pe=ie.PATTERN.charCodeAt(0),bt=M(Pe);Zs(de,bt,Le[Oe])}else if((0,qe.Z)(ie.START_CHARS_HINT)){let Pe;(0,y.Z)(ie.START_CHARS_HINT,bt=>{const fa=typeof bt=="string"?bt.charCodeAt(0):bt,un=M(fa);Pe!==un&&(Pe=un,Zs(de,un,Le[Oe]))})}else if(he(ie.PATTERN))if(ie.PATTERN.unicode)Be=!1,t.ensureOptimizations&&lr(`${Xn}	Unable to analyze < ${ie.PATTERN.toString()} > pattern.
	The regexp unicode flag is not currently supported by the regexp-to-ast library.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNICODE_OPTIMIZE`);else{const Pe=ms(ie.PATTERN,t.ensureOptimizations);(0,F.Z)(Pe)&&(Be=!1),(0,y.Z)(Pe,bt=>{Zs(de,bt,Le[Oe])})}else t.ensureOptimizations&&lr(`${Xn}	TokenType: <${ie.name}> is using a custom token pattern without providing <start_chars_hint> parameter.
	This will disable the lexer's first char optimizations.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_OPTIMIZE`),Be=!1;return de},[])}),{emptyGroups:we,patternIdxToConfig:Le,charCodeToPatternIdxToConfig:_e,hasCustom:d,canBeOptimized:Be}}function Ts(o,t){let a=[];const u=Rs(o);a=a.concat(u.errors);const d=hr(u.valid),f=d.valid;return a=a.concat(d.errors),a=a.concat(vs(f)),a=a.concat(xs(f)),a=a.concat(Ss(f,t)),a=a.concat(Ns(f)),a}function vs(o){let t=[];const a=(0,yt.Z)(o,u=>he(u[tn]));return t=t.concat(As(a)),t=t.concat(Is(a)),t=t.concat(Cs(a)),t=t.concat(ks(a)),t=t.concat(_s(a)),t}function Rs(o){const t=(0,yt.Z)(o,d=>!(0,L.Z)(d,tn)),a=(0,x.Z)(t,d=>({message:"Token Type: ->"+d.name+"<- missing static 'PATTERN' property",type:Fe.MISSING_PATTERN,tokenTypes:[d]})),u=en(o,t);return{errors:a,valid:u}}function hr(o){const t=(0,yt.Z)(o,d=>{const f=d[tn];return!he(f)&&!(0,Ce.Z)(f)&&!(0,L.Z)(f,"exec")&&!(0,_.Z)(f)}),a=(0,x.Z)(t,d=>({message:"Token Type: ->"+d.name+"<- static 'PATTERN' can only be a RegExp, a Function matching the {CustomPatternMatcherFunc} type or an Object matching the {ICustomPattern} interface.",type:Fe.INVALID_PATTERN,tokenTypes:[d]})),u=en(o,t);return{errors:a,valid:u}}const Es=/[^\\][$]/;function As(o){class t extends be.e{constructor(){super(...arguments),this.found=!1}visitEndAnchor(f){this.found=!0}}const a=(0,yt.Z)(o,d=>{const f=d.PATTERN;try{const A=An(f),k=new t;return k.visit(A),k.found}catch(A){return Es.test(f.source)}});return(0,x.Z)(a,d=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+d.name+`<- static 'PATTERN' cannot contain end of input anchor '$'
	See chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:Fe.EOI_ANCHOR_FOUND,tokenTypes:[d]}))}function _s(o){const t=(0,yt.Z)(o,u=>u.PATTERN.test(""));return(0,x.Z)(t,u=>({message:"Token Type: ->"+u.name+"<- static 'PATTERN' must not match an empty string",type:Fe.EMPTY_MATCH_PATTERN,tokenTypes:[u]}))}const $s=/[^\\[][\^]|^\^/;function Is(o){class t extends be.e{constructor(){super(...arguments),this.found=!1}visitStartAnchor(f){this.found=!0}}const a=(0,yt.Z)(o,d=>{const f=d.PATTERN;try{const A=An(f),k=new t;return k.visit(A),k.found}catch(A){return $s.test(f.source)}});return(0,x.Z)(a,d=>({message:`Unexpected RegExp Anchor Error:
	Token Type: ->`+d.name+`<- static 'PATTERN' cannot contain start of input anchor '^'
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#ANCHORS	for details.`,type:Fe.SOI_ANCHOR_FOUND,tokenTypes:[d]}))}function Cs(o){const t=(0,yt.Z)(o,u=>{const d=u[tn];return d instanceof RegExp&&(d.multiline||d.global)});return(0,x.Z)(t,u=>({message:"Token Type: ->"+u.name+"<- static 'PATTERN' may NOT contain global('g') or multiline('m')",type:Fe.UNSUPPORTED_FLAGS_FOUND,tokenTypes:[u]}))}function ks(o){const t=[];let a=(0,x.Z)(o,f=>(0,gt.Z)(o,(A,k)=>(f.PATTERN.source===k.PATTERN.source&&!Ye(t,k)&&k.PATTERN!==At.NA&&(t.push(k),A.push(k)),A),[]));a=dn(a);const u=(0,yt.Z)(a,f=>f.length>1);return(0,x.Z)(u,f=>{const A=(0,x.Z)(f,N=>N.name);return{message:`The same RegExp pattern ->${Nt(f).PATTERN}<-has been used in all of the following Token Types: ${A.join(", ")} <-`,type:Fe.DUPLICATE_PATTERNS_FOUND,tokenTypes:f}})}function xs(o){const t=(0,yt.Z)(o,u=>{if(!(0,L.Z)(u,"GROUP"))return!1;const d=u.GROUP;return d!==At.SKIPPED&&d!==At.NA&&!(0,_.Z)(d)});return(0,x.Z)(t,u=>({message:"Token Type: ->"+u.name+"<- static 'GROUP' can only be Lexer.SKIPPED/Lexer.NA/A String",type:Fe.INVALID_GROUP_TYPE_FOUND,tokenTypes:[u]}))}function Ss(o,t){const a=(0,yt.Z)(o,d=>d.PUSH_MODE!==void 0&&!Ye(t,d.PUSH_MODE));return(0,x.Z)(a,d=>({message:`Token Type: ->${d.name}<- static 'PUSH_MODE' value cannot refer to a Lexer Mode ->${d.PUSH_MODE}<-which does not exist`,type:Fe.PUSH_MODE_DOES_NOT_EXIST,tokenTypes:[d]}))}function Ns(o){const t=[],a=(0,gt.Z)(o,(u,d,f)=>{const A=d.PATTERN;return A===At.NA||((0,_.Z)(A)?u.push({str:A,idx:f,tokenType:d}):he(A)&&Ls(A)&&u.push({str:A.source,idx:f,tokenType:d})),u},[]);return(0,y.Z)(o,(u,d)=>{(0,y.Z)(a,({str:f,idx:A,tokenType:k})=>{if(d<A&&ws(f,u.PATTERN)){const N=`Token: ->${k.name}<- can never be matched.
Because it appears AFTER the Token Type ->${u.name}<-in the lexer's definition.
See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#UNREACHABLE`;t.push({message:N,type:Fe.UNREACHABLE_PATTERN,tokenTypes:[u,k]})}})}),t}function ws(o,t){if(he(t)){const a=t.exec(o);return a!==null&&a.index===0}else{if((0,Ce.Z)(t))return t(o,0,[],{});if((0,L.Z)(t,"exec"))return t.exec(o,0,[],{});if(typeof t=="string")return t===o;throw Error("non exhaustive match")}}function Ls(o){const t=[".","\\","[","]","|","^","$","(",")","?","*","+","{"];return(0,zn.Z)(t,a=>o.source.indexOf(a)!==-1)===void 0}function Pr(o){const t=o.ignoreCase?"i":"";return new RegExp(`^(?:${o.source})`,t)}function pr(o){const t=o.ignoreCase?"iy":"y";return new RegExp(`${o.source}`,t)}function Mr(o,t,a){const u=[];return(0,L.Z)(o,_n)||u.push({message:"A MultiMode Lexer cannot be initialized without a <"+_n+`> property in its definition
`,type:Fe.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE}),(0,L.Z)(o,Qn)||u.push({message:"A MultiMode Lexer cannot be initialized without a <"+Qn+`> property in its definition
`,type:Fe.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY}),(0,L.Z)(o,Qn)&&(0,L.Z)(o,_n)&&!(0,L.Z)(o.modes,o.defaultMode)&&u.push({message:`A MultiMode Lexer cannot be initialized with a ${_n}: <${o.defaultMode}>which does not exist
`,type:Fe.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST}),(0,L.Z)(o,Qn)&&(0,y.Z)(o.modes,(d,f)=>{(0,y.Z)(d,(A,k)=>{if((0,it.Z)(A))u.push({message:`A Lexer cannot be initialized using an undefined Token Type. Mode:<${f}> at index: <${k}>
`,type:Fe.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED});else if((0,L.Z)(A,"LONGER_ALT")){const N=(0,qe.Z)(A.LONGER_ALT)?A.LONGER_ALT:[A.LONGER_ALT];(0,y.Z)(N,G=>{!(0,it.Z)(G)&&!Ye(d,G)&&u.push({message:`A MultiMode Lexer cannot be initialized with a longer_alt <${G.name}> on token <${A.name}> outside of mode <${f}>
`,type:Fe.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE})})}})}),u}function Os(o,t,a){const u=[];let d=!1;const f=dn((0,Qe.Z)((0,O.Z)(o.modes))),A=xe(f,N=>N[tn]===At.NA),k=Zr(a);return t&&(0,y.Z)(A,N=>{const G=Dr(N,k);if(G!==!1){const te={message:Ds(N,G),type:G.issue,tokenType:N};u.push(te)}else(0,L.Z)(N,"LINE_BREAKS")?N.LINE_BREAKS===!0&&(d=!0):fr(k,N.PATTERN)&&(d=!0)}),t&&!d&&u.push({message:`Warning: No LINE_BREAKS Found.
	This Lexer has been defined to track line and column information,
	But none of the Token Types can be identified as matching a line terminator.
	See https://chevrotain.io/docs/guide/resolving_lexer_errors.html#LINE_BREAKS 
	for details.`,type:Fe.NO_LINE_BREAKS_FLAGS}),u}function Ps(o){const t={},a=(0,w.Z)(o);return(0,y.Z)(a,u=>{const d=o[u];if((0,qe.Z)(d))t[u]=[];else throw Error("non exhaustive match")}),t}function br(o){const t=o.PATTERN;if(he(t))return!1;if((0,Ce.Z)(t))return!0;if((0,L.Z)(t,"exec"))return!0;if((0,_.Z)(t))return!1;throw Error("non exhaustive match")}function Ms(o){return(0,_.Z)(o)&&o.length===1?o.charCodeAt(0):!1}const bs={test:function(o){const t=o.length;for(let a=this.lastIndex;a<t;a++){const u=o.charCodeAt(a);if(u===10)return this.lastIndex=a+1,!0;if(u===13)return o.charCodeAt(a+1)===10?this.lastIndex=a+2:this.lastIndex=a+1,!0}return!1},lastIndex:0};function Dr(o,t){if((0,L.Z)(o,"LINE_BREAKS"))return!1;if(he(o.PATTERN)){try{fr(t,o.PATTERN)}catch(a){return{issue:Fe.IDENTIFY_TERMINATOR,errMsg:a.message}}return!1}else{if((0,_.Z)(o.PATTERN))return!1;if(br(o))return{issue:Fe.CUSTOM_LINE_BREAK};throw Error("non exhaustive match")}}function Ds(o,t){if(t.issue===Fe.IDENTIFY_TERMINATOR)return`Warning: unable to identify line terminator usage in pattern.
	The problem is in the <${o.name}> Token Type
	 Root cause: ${t.errMsg}.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#IDENTIFY_TERMINATOR`;if(t.issue===Fe.CUSTOM_LINE_BREAK)return`Warning: A Custom Token Pattern should specify the <line_breaks> option.
	The problem is in the <${o.name}> Token Type
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#CUSTOM_LINE_BREAK`;throw Error("non exhaustive match")}function Zr(o){return(0,x.Z)(o,a=>(0,_.Z)(a)?a.charCodeAt(0):a)}function Zs(o,t,a){o[t]===void 0?o[t]=[a]:o[t].push(a)}const $n=256;let mr=[];function M(o){return o<$n?o:mr[o]}function Ni(){if((0,F.Z)(mr)){mr=new Array(65536);for(let o=0;o<65536;o++)mr[o]=o>255?255+~~(o/255):o}}var Fr=g(35272),et=g(21122),nn=g(7683);function gr(o){const t=new Date().getTime(),a=o();return{time:new Date().getTime()-t,value:a}}function In(o,t){const a=o.tokenTypeIdx;return a===t.tokenTypeIdx?!0:t.isParent===!0&&t.categoryMatchesMap[a]===!0}function Cn(o,t){return o.tokenTypeIdx===t.tokenTypeIdx}let yr=1;const Ur={};function kn(o){const t=Fs(o);wi(t),Us(t),Li(t),(0,y.Z)(t,a=>{a.isParent=a.categoryMatches.length>0})}function Fs(o){let t=(0,b.Z)(o),a=o,u=!0;for(;u;){a=dn((0,Qe.Z)((0,x.Z)(a,f=>f.CATEGORIES)));const d=en(a,t);t=t.concat(d),(0,F.Z)(d)?u=!1:a=d}return t}function wi(o){(0,y.Z)(o,t=>{Bs(t)||(Ur[yr]=t,t.tokenTypeIdx=yr++),Ws(t)&&!(0,qe.Z)(t.CATEGORIES)&&(t.CATEGORIES=[t.CATEGORIES]),Ws(t)||(t.CATEGORIES=[]),xn(t)||(t.categoryMatches=[]),st(t)||(t.categoryMatchesMap={})})}function Li(o){(0,y.Z)(o,t=>{t.categoryMatches=[],(0,y.Z)(t.categoryMatchesMap,(a,u)=>{t.categoryMatches.push(Ur[u].tokenTypeIdx)})})}function Us(o){(0,y.Z)(o,t=>{Gs([],t)})}function Gs(o,t){(0,y.Z)(o,a=>{t.categoryMatchesMap[a.tokenTypeIdx]=!0}),(0,y.Z)(t.CATEGORIES,a=>{const u=o.concat(t);Ye(u,a)||Gs(u,a)})}function Bs(o){return(0,L.Z)(o,"tokenTypeIdx")}function Ws(o){return(0,L.Z)(o,"CATEGORIES")}function xn(o){return(0,L.Z)(o,"categoryMatches")}function st(o){return(0,L.Z)(o,"categoryMatchesMap")}function Ks(o){return(0,L.Z)(o,"tokenTypeIdx")}const Vs={buildUnableToPopLexerModeMessage(o){return`Unable to pop Lexer Mode after encountering Token ->${o.image}<- The Mode Stack is empty`},buildUnexpectedCharactersMessage(o,t,a,u,d){return`unexpected character: ->${o.charAt(t)}<- at offset: ${t}, skipped ${a} characters.`}};var Fe;(function(o){o[o.MISSING_PATTERN=0]="MISSING_PATTERN",o[o.INVALID_PATTERN=1]="INVALID_PATTERN",o[o.EOI_ANCHOR_FOUND=2]="EOI_ANCHOR_FOUND",o[o.UNSUPPORTED_FLAGS_FOUND=3]="UNSUPPORTED_FLAGS_FOUND",o[o.DUPLICATE_PATTERNS_FOUND=4]="DUPLICATE_PATTERNS_FOUND",o[o.INVALID_GROUP_TYPE_FOUND=5]="INVALID_GROUP_TYPE_FOUND",o[o.PUSH_MODE_DOES_NOT_EXIST=6]="PUSH_MODE_DOES_NOT_EXIST",o[o.MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE=7]="MULTI_MODE_LEXER_WITHOUT_DEFAULT_MODE",o[o.MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY=8]="MULTI_MODE_LEXER_WITHOUT_MODES_PROPERTY",o[o.MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST=9]="MULTI_MODE_LEXER_DEFAULT_MODE_VALUE_DOES_NOT_EXIST",o[o.LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED=10]="LEXER_DEFINITION_CANNOT_CONTAIN_UNDEFINED",o[o.SOI_ANCHOR_FOUND=11]="SOI_ANCHOR_FOUND",o[o.EMPTY_MATCH_PATTERN=12]="EMPTY_MATCH_PATTERN",o[o.NO_LINE_BREAKS_FLAGS=13]="NO_LINE_BREAKS_FLAGS",o[o.UNREACHABLE_PATTERN=14]="UNREACHABLE_PATTERN",o[o.IDENTIFY_TERMINATOR=15]="IDENTIFY_TERMINATOR",o[o.CUSTOM_LINE_BREAK=16]="CUSTOM_LINE_BREAK",o[o.MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE=17]="MULTI_MODE_LEXER_LONGER_ALT_NOT_IN_CURRENT_MODE"})(Fe||(Fe={}));const er={deferDefinitionErrorsHandling:!1,positionTracking:"full",lineTerminatorsPattern:/\n|\r\n?/g,lineTerminatorCharacters:[`
`,"\r"],ensureOptimizations:!1,safeMode:!1,errorMessageProvider:Vs,traceInitPerf:!1,skipValidations:!1,recoveryEnabled:!0};Object.freeze(er);class At{constructor(t,a=er){if(this.lexerDefinition=t,this.lexerDefinitionErrors=[],this.lexerDefinitionWarning=[],this.patternIdxToConfig={},this.charCodeToPatternIdxToConfig={},this.modes=[],this.emptyGroups={},this.trackStartLines=!0,this.trackEndLines=!0,this.hasCustom=!1,this.canModeBeOptimized={},this.TRACE_INIT=(d,f)=>{if(this.traceInitPerf===!0){this.traceInitIndent++;const A=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${A}--> <${d}>`);const{time:k,value:N}=gr(f),G=k>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&G(`${A}<-- <${d}> time: ${k}ms`),this.traceInitIndent--,N}else return f()},typeof a=="boolean")throw Error(`The second argument to the Lexer constructor is now an ILexerConfig Object.
a boolean 2nd argument is no longer supported`);this.config=J({},er,a);const u=this.config.traceInitPerf;u===!0?(this.traceInitMaxIdent=1/0,this.traceInitPerf=!0):typeof u=="number"&&(this.traceInitMaxIdent=u,this.traceInitPerf=!0),this.traceInitIndent=-1,this.TRACE_INIT("Lexer Constructor",()=>{let d,f=!0;this.TRACE_INIT("Lexer Config handling",()=>{if(this.config.lineTerminatorsPattern===er.lineTerminatorsPattern)this.config.lineTerminatorsPattern=bs;else if(this.config.lineTerminatorCharacters===er.lineTerminatorCharacters)throw Error(`Error: Missing <lineTerminatorCharacters> property on the Lexer config.
	For details See: https://chevrotain.io/docs/guide/resolving_lexer_errors.html#MISSING_LINE_TERM_CHARS`);if(a.safeMode&&a.ensureOptimizations)throw Error('"safeMode" and "ensureOptimizations" flags are mutually exclusive.');this.trackStartLines=/full|onlyStart/i.test(this.config.positionTracking),this.trackEndLines=/full/i.test(this.config.positionTracking),(0,qe.Z)(t)?d={modes:{defaultMode:(0,b.Z)(t)},defaultMode:_n}:(f=!1,d=(0,b.Z)(t))}),this.config.skipValidations===!1&&(this.TRACE_INIT("performRuntimeChecks",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Mr(d,this.trackStartLines,this.config.lineTerminatorCharacters))}),this.TRACE_INIT("performWarningRuntimeChecks",()=>{this.lexerDefinitionWarning=this.lexerDefinitionWarning.concat(Os(d,this.trackStartLines,this.config.lineTerminatorCharacters))})),d.modes=d.modes?d.modes:{},(0,y.Z)(d.modes,(k,N)=>{d.modes[N]=xe(k,G=>(0,it.Z)(G))});const A=(0,w.Z)(d.modes);if((0,y.Z)(d.modes,(k,N)=>{this.TRACE_INIT(`Mode: <${N}> processing`,()=>{if(this.modes.push(N),this.config.skipValidations===!1&&this.TRACE_INIT("validatePatterns",()=>{this.lexerDefinitionErrors=this.lexerDefinitionErrors.concat(Ts(k,A))}),(0,F.Z)(this.lexerDefinitionErrors)){kn(k);let G;this.TRACE_INIT("analyzeTokenTypes",()=>{G=ys(k,{lineTerminatorCharacters:this.config.lineTerminatorCharacters,positionTracking:a.positionTracking,ensureOptimizations:a.ensureOptimizations,safeMode:a.safeMode,tracer:this.TRACE_INIT})}),this.patternIdxToConfig[N]=G.patternIdxToConfig,this.charCodeToPatternIdxToConfig[N]=G.charCodeToPatternIdxToConfig,this.emptyGroups=J({},this.emptyGroups,G.emptyGroups),this.hasCustom=G.hasCustom||this.hasCustom,this.canModeBeOptimized[N]=G.canBeOptimized}})}),this.defaultMode=d.defaultMode,!(0,F.Z)(this.lexerDefinitionErrors)&&!this.config.deferDefinitionErrorsHandling){const N=(0,x.Z)(this.lexerDefinitionErrors,G=>G.message).join(`-----------------------
`);throw new Error(`Errors detected in definition of Lexer:
`+N)}(0,y.Z)(this.lexerDefinitionWarning,k=>{wr(k.message)}),this.TRACE_INIT("Choosing sub-methods implementations",()=>{if(qn?(this.chopInput=Fr.Z,this.match=this.matchWithTest):(this.updateLastIndex=et.Z,this.match=this.matchWithExec),f&&(this.handleModes=et.Z),this.trackStartLines===!1&&(this.computeNewColumn=Fr.Z),this.trackEndLines===!1&&(this.updateTokenEndLineColumnLocation=et.Z),/full/i.test(this.config.positionTracking))this.createTokenInstance=this.createFullToken;else if(/onlyStart/i.test(this.config.positionTracking))this.createTokenInstance=this.createStartOnlyToken;else if(/onlyOffset/i.test(this.config.positionTracking))this.createTokenInstance=this.createOffsetOnlyToken;else throw Error(`Invalid <positionTracking> config option: "${this.config.positionTracking}"`);this.hasCustom?(this.addToken=this.addTokenUsingPush,this.handlePayload=this.handlePayloadWithCustom):(this.addToken=this.addTokenUsingMemberAccess,this.handlePayload=this.handlePayloadNoCustom)}),this.TRACE_INIT("Failed Optimization Warnings",()=>{const k=(0,gt.Z)(this.canModeBeOptimized,(N,G,q)=>(G===!1&&N.push(q),N),[]);if(a.ensureOptimizations&&!(0,F.Z)(k))throw Error(`Lexer Modes: < ${k.join(", ")} > cannot be optimized.
	 Disable the "ensureOptimizations" lexer config flag to silently ignore this and run the lexer in an un-optimized mode.
	 Or inspect the console log for details on how to resolve these issues.`)}),this.TRACE_INIT("clearRegExpParserCache",()=>{Yn()}),this.TRACE_INIT("toFastProperties",()=>{V(this)})})}tokenize(t,a=this.defaultMode){if(!(0,F.Z)(this.lexerDefinitionErrors)){const d=(0,x.Z)(this.lexerDefinitionErrors,f=>f.message).join(`-----------------------
`);throw new Error(`Unable to Tokenize because Errors detected in definition of Lexer:
`+d)}return this.tokenizeInternal(t,a)}tokenizeInternal(t,a){let u,d,f,A,k,N,G,q,te,fe,Ee,we,Le,Be,_e,de;const ie=t,Oe=ie.length;let Pe=0,bt=0;const fa=this.hasCustom?0:Math.floor(t.length/10),un=new Array(fa),ha=[];let os=this.trackStartLines?1:void 0,Ln=this.trackStartLines?1:void 0;const ls=Ps(this.emptyGroups),Fa=this.trackStartLines,pa=this.config.lineTerminatorsPattern;let Ci=0,On=[],us=[];const ki=[],Ia=[];Object.freeze(Ia);let cs;function Ca(){return On}function ka(Tt){const Gt=M(Tt),Nr=us[Gt];return Nr===void 0?Ia:Nr}const Ua=Tt=>{if(ki.length===1&&Tt.tokenType.PUSH_MODE===void 0){const Gt=this.config.errorMessageProvider.buildUnableToPopLexerModeMessage(Tt);ha.push({offset:Tt.startOffset,line:Tt.startLine,column:Tt.startColumn,length:Tt.image.length,message:Gt})}else{ki.pop();const Gt=(0,nn.Z)(ki);On=this.patternIdxToConfig[Gt],us=this.charCodeToPatternIdxToConfig[Gt],Ci=On.length;const Nr=this.canModeBeOptimized[Gt]&&this.config.safeMode===!1;us&&Nr?cs=ka:cs=Ca}};function xa(Tt){ki.push(Tt),us=this.charCodeToPatternIdxToConfig[Tt],On=this.patternIdxToConfig[Tt],Ci=On.length,Ci=On.length;const Gt=this.canModeBeOptimized[Tt]&&this.config.safeMode===!1;us&&Gt?cs=ka:cs=Ca}xa.call(this,a);let Jt;const Sa=this.config.recoveryEnabled;for(;Pe<Oe;){N=null;const Tt=ie.charCodeAt(Pe),Gt=cs(Tt),Nr=Gt.length;for(u=0;u<Nr;u++){Jt=Gt[u];const Dt=Jt.pattern;G=null;const pn=Jt.short;if(pn!==!1?Tt===pn&&(N=Dt):Jt.isCustom===!0?(de=Dt.exec(ie,Pe,un,ls),de!==null?(N=de[0],de.payload!==void 0&&(G=de.payload)):N=null):(this.updateLastIndex(Dt,Pe),N=this.match(Dt,t,Pe)),N!==null){if(k=Jt.longerAlt,k!==void 0){const Pn=k.length;for(f=0;f<Pn;f++){const mn=On[k[f]],ir=mn.pattern;if(q=null,mn.isCustom===!0?(de=ir.exec(ie,Pe,un,ls),de!==null?(A=de[0],de.payload!==void 0&&(q=de.payload)):A=null):(this.updateLastIndex(ir,Pe),A=this.match(ir,t,Pe)),A&&A.length>N.length){N=A,G=q,Jt=mn;break}}}break}}if(N!==null){if(te=N.length,fe=Jt.group,fe!==void 0&&(Ee=Jt.tokenTypeIdx,we=this.createTokenInstance(N,Pe,Ee,Jt.tokenType,os,Ln,te),this.handlePayload(we,G),fe===!1?bt=this.addToken(un,bt,we):ls[fe].push(we)),t=this.chopInput(t,te),Pe=Pe+te,Ln=this.computeNewColumn(Ln,te),Fa===!0&&Jt.canLineTerminator===!0){let Dt=0,pn,Pn;pa.lastIndex=0;do pn=pa.test(N),pn===!0&&(Pn=pa.lastIndex-1,Dt++);while(pn===!0);Dt!==0&&(os=os+Dt,Ln=te-Pn,this.updateTokenEndLineColumnLocation(we,fe,Pn,Dt,os,Ln,te))}this.handleModes(Jt,Ua,xa,we)}else{const Dt=Pe,pn=os,Pn=Ln;let mn=Sa===!1;for(;mn===!1&&Pe<Oe;)for(t=this.chopInput(t,1),Pe++,d=0;d<Ci;d++){const ir=On[d],ma=ir.pattern,Na=ir.short;if(Na!==!1?ie.charCodeAt(Pe)===Na&&(mn=!0):ir.isCustom===!0?mn=ma.exec(ie,Pe,un,ls)!==null:(this.updateLastIndex(ma,Pe),mn=ma.exec(t)!==null),mn===!0)break}if(Le=Pe-Dt,Ln=this.computeNewColumn(Ln,Le),_e=this.config.errorMessageProvider.buildUnexpectedCharactersMessage(ie,Dt,Le,pn,Pn),ha.push({offset:Dt,line:pn,column:Pn,length:Le,message:_e}),Sa===!1)break}}return this.hasCustom||(un.length=bt),{tokens:un,groups:ls,errors:ha}}handleModes(t,a,u,d){if(t.pop===!0){const f=t.push;a(d),f!==void 0&&u.call(this,f)}else t.push!==void 0&&u.call(this,t.push)}chopInput(t,a){return t.substring(a)}updateLastIndex(t,a){t.lastIndex=a}updateTokenEndLineColumnLocation(t,a,u,d,f,A,k){let N,G;a!==void 0&&(N=u===k-1,G=N?-1:0,d===1&&N===!0||(t.endLine=f+G,t.endColumn=A-1+-G))}computeNewColumn(t,a){return t+a}createOffsetOnlyToken(t,a,u,d){return{image:t,startOffset:a,tokenTypeIdx:u,tokenType:d}}createStartOnlyToken(t,a,u,d,f,A){return{image:t,startOffset:a,startLine:f,startColumn:A,tokenTypeIdx:u,tokenType:d}}createFullToken(t,a,u,d,f,A,k){return{image:t,startOffset:a,endOffset:a+k-1,startLine:f,endLine:f,startColumn:A,endColumn:A+k-1,tokenTypeIdx:u,tokenType:d}}addTokenUsingPush(t,a,u){return t.push(u),a}addTokenUsingMemberAccess(t,a,u){return t[a]=u,a++,a}handlePayloadNoCustom(t,a){}handlePayloadWithCustom(t,a){a!==null&&(t.payload=a)}matchWithTest(t,a,u){return t.test(a)===!0?a.substring(u,t.lastIndex):null}matchWithExec(t,a){const u=t.exec(a);return u!==null?u[0]:null}}At.SKIPPED="This marks a skipped Token pattern, this means each token identified by it willbe consumed and then thrown into oblivion, this can be used to for example to completely ignore whitespace.",At.NA=/NOT_APPLICABLE/;function Sn(o){return js(o)?o.LABEL:o.name}function rn(o){return o.name}function js(o){return(0,_.Z)(o.LABEL)&&o.LABEL!==""}const Oi="parent",Hs="categories",tr="label",Gr="group",zs="push_mode",Br="pop_mode",Wr="longer_alt",Ys="line_breaks",Tr="start_chars_hint";function Xs(o){return Pi(o)}function Pi(o){const t=o.pattern,a={};if(a.name=o.name,(0,it.Z)(t)||(a.PATTERN=t),(0,L.Z)(o,Oi))throw`The parent property is no longer supported.
See: https://github.com/chevrotain/chevrotain/issues/564#issuecomment-349062346 for details.`;return(0,L.Z)(o,Hs)&&(a.CATEGORIES=o[Hs]),kn([a]),(0,L.Z)(o,tr)&&(a.LABEL=o[tr]),(0,L.Z)(o,Gr)&&(a.GROUP=o[Gr]),(0,L.Z)(o,Br)&&(a.POP_MODE=o[Br]),(0,L.Z)(o,zs)&&(a.PUSH_MODE=o[zs]),(0,L.Z)(o,Wr)&&(a.LONGER_ALT=o[Wr]),(0,L.Z)(o,Ys)&&(a.LINE_BREAKS=o[Ys]),(0,L.Z)(o,Tr)&&(a.START_CHARS_HINT=o[Tr]),a}const sn=Xs({name:"EOF",pattern:At.NA});kn([sn]);function Kr(o,t,a,u,d,f,A,k){return{image:t,startOffset:a,endOffset:u,startLine:d,endLine:f,startColumn:A,endColumn:k,tokenTypeIdx:o.tokenTypeIdx,tokenType:o}}function Js(o,t){return In(o,t)}const Ke={buildMismatchTokenMessage({expected:o,actual:t,previous:a,ruleName:u}){return`Expecting ${js(o)?`--> ${Sn(o)} <--`:`token of type --> ${o.name} <--`} but found --> '${t.image}' <--`},buildNotAllInputParsedMessage({firstRedundant:o,ruleName:t}){return"Redundant input, expecting EOF but found: "+o.image},buildNoViableAltMessage({expectedPathsPerAlt:o,actual:t,previous:a,customUserDescription:u,ruleName:d}){const f="Expecting: ",k=`
but found: '`+Nt(t).image+"'";if(u)return f+u+k;{const N=(0,gt.Z)(o,(fe,Ee)=>fe.concat(Ee),[]),G=(0,x.Z)(N,fe=>`[${(0,x.Z)(fe,Ee=>Sn(Ee)).join(", ")}]`),te=`one of these possible Token sequences:
${(0,x.Z)(G,(fe,Ee)=>`  ${Ee+1}. ${fe}`).join(`
`)}`;return f+te+k}},buildEarlyExitMessage({expectedIterationPaths:o,actual:t,customUserDescription:a,ruleName:u}){const d="Expecting: ",A=`
but found: '`+Nt(t).image+"'";if(a)return d+a+A;{const N=`expecting at least one iteration which starts with one of these possible Token sequences::
  <${(0,x.Z)(o,G=>`[${(0,x.Z)(G,q=>Sn(q)).join(",")}]`).join(" ,")}>`;return d+N+A}}};Object.freeze(Ke);const We={buildRuleNotFoundError(o,t){return"Invalid grammar, reference to a rule which is not defined: ->"+t.nonTerminalName+`<-
inside top level rule: ->`+o.name+"<-"}},hn={buildDuplicateFoundError(o,t){function a(q){return q instanceof me?q.terminalType.name:q instanceof He?q.nonTerminalName:""}const u=o.name,d=Nt(t),f=d.idx,A=rt(d),k=a(d),N=f>0;let G=`->${A}${N?f:""}<- ${k?`with argument: ->${k}<-`:""}
                  appears more than once (${t.length} times) in the top level rule: ->${u}<-.                  
                  For further details see: https://chevrotain.io/docs/FAQ.html#NUMERICAL_SUFFIXES 
                  `;return G=G.replace(/[ \t]+/g," "),G=G.replace(/\s\s+/g,`
`),G},buildNamespaceConflictError(o){return`Namespace conflict found in grammar.
The grammar has both a Terminal(Token) and a Non-Terminal(Rule) named: <${o.name}>.
To resolve this make sure each Terminal and Non-Terminal names are unique
This is easy to accomplish by using the convention that Terminal names start with an uppercase letter
and Non-Terminal names start with a lower case letter.`},buildAlternationPrefixAmbiguityError(o){const t=(0,x.Z)(o.prefixPath,d=>Sn(d)).join(", "),a=o.alternation.idx===0?"":o.alternation.idx;return`Ambiguous alternatives: <${o.ambiguityIndices.join(" ,")}> due to common lookahead prefix
in <OR${a}> inside <${o.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#COMMON_PREFIX
For Further details.`},buildAlternationAmbiguityError(o){const t=(0,x.Z)(o.prefixPath,d=>Sn(d)).join(", "),a=o.alternation.idx===0?"":o.alternation.idx;let u=`Ambiguous Alternatives Detected: <${o.ambiguityIndices.join(" ,")}> in <OR${a}> inside <${o.topLevelRule.name}> Rule,
<${t}> may appears as a prefix path in all these alternatives.
`;return u=u+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,u},buildEmptyRepetitionError(o){let t=rt(o.repetition);return o.repetition.idx!==0&&(t+=o.repetition.idx),`The repetition <${t}> within Rule <${o.topLevelRule.name}> can never consume any tokens.
This could lead to an infinite loop.`},buildTokenNameError(o){return"deprecated"},buildEmptyAlternationError(o){return`Ambiguous empty alternative: <${o.emptyChoiceIdx+1}> in <OR${o.alternation.idx}> inside <${o.topLevelRule.name}> Rule.
Only the last alternative may be an empty alternative.`},buildTooManyAlternativesError(o){return`An Alternation cannot have more than 256 alternatives:
<OR${o.alternation.idx}> inside <${o.topLevelRule.name}> Rule.
 has ${o.alternation.definition.length+1} alternatives.`},buildLeftRecursionError(o){const t=o.topLevelRule.name,a=(0,x.Z)(o.leftRecursionPath,f=>f.name),u=`${t} --> ${a.concat([t]).join(" --> ")}`;return`Left Recursion found in grammar.
rule: <${t}> can be invoked from itself (directly or indirectly)
without consuming any Tokens. The grammar path that causes this is: 
 ${u}
 To fix this refactor your grammar to remove the left recursion.
see: https://en.wikipedia.org/wiki/LL_parser#Left_factoring.`},buildInvalidRuleNameError(o){return"deprecated"},buildDuplicateRuleNameError(o){let t;return o.topLevelRule instanceof ze?t=o.topLevelRule.name:t=o.topLevelRule,`Duplicate definition, rule: ->${t}<- is already defined in the grammar: ->${o.grammarName}<-`}};class Yt{visit(t){const a=t;switch(a.constructor){case He:return this.visitNonTerminal(a);case Ze:return this.visitAlternative(a);case I:return this.visitOption(a);case W:return this.visitRepetitionMandatory(a);case z:return this.visitRepetitionMandatoryWithSeparator(a);case pe:return this.visitRepetitionWithSeparator(a);case le:return this.visitRepetition(a);case Ie:return this.visitAlternation(a);case me:return this.visitTerminal(a);case ze:return this.visitRule(a);default:throw Error("non exhaustive match")}}visitNonTerminal(t){}visitAlternative(t){}visitOption(t){}visitRepetition(t){}visitRepetitionMandatory(t){}visitRepetitionMandatoryWithSeparator(t){}visitRepetitionWithSeparator(t){}visitAlternation(t){}visitTerminal(t){}visitRule(t){}}function Qs(o,t){const a=new Mi(o,t);return a.resolveRefs(),a.errors}class Mi extends Yt{constructor(t,a){super(),this.nameToTopRule=t,this.errMsgProvider=a,this.errors=[]}resolveRefs(){(0,y.Z)((0,O.Z)(this.nameToTopRule),t=>{this.currTopLevel=t,t.accept(this)})}visitNonTerminal(t){const a=this.nameToTopRule[t.nonTerminalName];if(a)t.referencedRule=a;else{const u=this.errMsgProvider.buildRuleNotFoundError(this.currTopLevel,t);this.errors.push({message:u,type:$t.UNRESOLVED_SUBRULE_REF,ruleName:this.currTopLevel.name,unresolvedRefName:t.nonTerminalName})}}}var Lt=g(16571),vr=g(81186);function Rr(o,t,a,u){for(var d=-1,f=o==null?0:o.length;++d<f;){var A=o[d];t(u,A,a(A),o)}return u}var _t=Rr;function Vr(o,t,a,u){return(0,ct.Z)(o,function(d,f,A){t(u,d,a(d),A)}),u}var nr=Vr;function jr(o,t){return function(a,u){var d=(0,qe.Z)(a)?_t:nr,f=t?t():{};return d(a,o,(0,K.Z)(u,2),f)}}var Hr=jr,qs=Object.prototype,ei=qs.hasOwnProperty,ti=Hr(function(o,t,a){ei.call(o,a)?o[a].push(t):(0,vr.Z)(o,a,[t])}),bi=ti;function Ot(o,t,a){var u=o==null?0:o.length;return u?(t=a||t===void 0?1:(0,R.Z)(t),t=u-t,(0,B.Z)(o,0,t<0?0:t)):[]}var Ue=Ot;class Di extends Zt{constructor(t,a){super(),this.topProd=t,this.path=a,this.possibleTokTypes=[],this.nextProductionName="",this.nextProductionOccurrence=0,this.found=!1,this.isAtEndOfPath=!1}startWalking(){if(this.found=!1,this.path.ruleStack[0]!==this.topProd.name)throw Error("The path does not start with the walker's top Rule!");return this.ruleStack=(0,b.Z)(this.path.ruleStack).reverse(),this.occurrenceStack=(0,b.Z)(this.path.occurrenceStack).reverse(),this.ruleStack.pop(),this.occurrenceStack.pop(),this.updateExpectedNext(),this.walk(this.topProd),this.possibleTokTypes}walk(t,a=[]){this.found||super.walk(t,a)}walkProdRef(t,a,u){if(t.referencedRule.name===this.nextProductionName&&t.idx===this.nextProductionOccurrence){const d=a.concat(u);this.updateExpectedNext(),this.walk(t.referencedRule,d)}}updateExpectedNext(){(0,F.Z)(this.ruleStack)?(this.nextProductionName="",this.nextProductionOccurrence=0,this.isAtEndOfPath=!0):(this.nextProductionName=this.ruleStack.pop(),this.nextProductionOccurrence=this.occurrenceStack.pop())}}class Zi extends Di{constructor(t,a){super(t,a),this.path=a,this.nextTerminalName="",this.nextTerminalOccurrence=0,this.nextTerminalName=this.path.lastTok.name,this.nextTerminalOccurrence=this.path.lastTokOccurrence}walkTerminal(t,a,u){if(this.isAtEndOfPath&&t.terminalType.name===this.nextTerminalName&&t.idx===this.nextTerminalOccurrence&&!this.found){const d=a.concat(u),f=new Ze({definition:d});this.possibleTokTypes=Ft(f),this.found=!0}}}class Nn extends Zt{constructor(t,a){super(),this.topRule=t,this.occurrence=a,this.result={token:void 0,occurrence:void 0,isEndOfRule:void 0}}startWalking(){return this.walk(this.topRule),this.result}}class Fi extends Nn{walkMany(t,a,u){if(t.idx===this.occurrence){const d=Nt(a.concat(u));this.result.isEndOfRule=d===void 0,d instanceof me&&(this.result.token=d.terminalType,this.result.occurrence=d.idx)}else super.walkMany(t,a,u)}}class ni extends Nn{walkManySep(t,a,u){if(t.idx===this.occurrence){const d=Nt(a.concat(u));this.result.isEndOfRule=d===void 0,d instanceof me&&(this.result.token=d.terminalType,this.result.occurrence=d.idx)}else super.walkManySep(t,a,u)}}class Ui extends Nn{walkAtLeastOne(t,a,u){if(t.idx===this.occurrence){const d=Nt(a.concat(u));this.result.isEndOfRule=d===void 0,d instanceof me&&(this.result.token=d.terminalType,this.result.occurrence=d.idx)}else super.walkAtLeastOne(t,a,u)}}class Xt extends Nn{walkAtLeastOneSep(t,a,u){if(t.idx===this.occurrence){const d=Nt(a.concat(u));this.result.isEndOfRule=d===void 0,d instanceof me&&(this.result.token=d.terminalType,this.result.occurrence=d.idx)}else super.walkAtLeastOneSep(t,a,u)}}function zr(o,t,a=[]){a=(0,b.Z)(a);let u=[],d=0;function f(k){return k.concat(E(o,d+1))}function A(k){const N=zr(f(k),t,a);return u.concat(N)}for(;a.length<t&&d<o.length;){const k=o[d];if(k instanceof Ze)return A(k.definition);if(k instanceof He)return A(k.definition);if(k instanceof I)u=A(k.definition);else if(k instanceof W){const N=k.definition.concat([new le({definition:k.definition})]);return A(N)}else if(k instanceof z){const N=[new Ze({definition:k.definition}),new le({definition:[new me({terminalType:k.separator})].concat(k.definition)})];return A(N)}else if(k instanceof pe){const N=k.definition.concat([new le({definition:[new me({terminalType:k.separator})].concat(k.definition)})]);u=A(N)}else if(k instanceof le){const N=k.definition.concat([new le({definition:k.definition})]);u=A(N)}else{if(k instanceof Ie)return(0,y.Z)(k.definition,N=>{(0,F.Z)(N.definition)===!1&&(u=A(N.definition))}),u;if(k instanceof me)a.push(k.terminalType);else throw Error("non exhaustive match")}d++}return u.push({partialPath:a,suffixDef:E(o,d)}),u}function rr(o,t,a,u){const d="EXIT_NONE_TERMINAL",f=[d],A="EXIT_ALTERNATIVE";let k=!1;const N=t.length,G=N-u-1,q=[],te=[];for(te.push({idx:-1,def:o,ruleStack:[],occurrenceStack:[]});!(0,F.Z)(te);){const fe=te.pop();if(fe===A){k&&(0,nn.Z)(te).idx<=G&&te.pop();continue}const Ee=fe.def,we=fe.idx,Le=fe.ruleStack,Be=fe.occurrenceStack;if((0,F.Z)(Ee))continue;const _e=Ee[0];if(_e===d){const de={idx:we,def:E(Ee),ruleStack:Ue(Le),occurrenceStack:Ue(Be)};te.push(de)}else if(_e instanceof me)if(we<N-1){const de=we+1,ie=t[de];if(a(ie,_e.terminalType)){const Oe={idx:de,def:E(Ee),ruleStack:Le,occurrenceStack:Be};te.push(Oe)}}else if(we===N-1)q.push({nextTokenType:_e.terminalType,nextTokenOccurrence:_e.idx,ruleStack:Le,occurrenceStack:Be}),k=!0;else throw Error("non exhaustive match");else if(_e instanceof He){const de=(0,b.Z)(Le);de.push(_e.nonTerminalName);const ie=(0,b.Z)(Be);ie.push(_e.idx);const Oe={idx:we,def:_e.definition.concat(f,E(Ee)),ruleStack:de,occurrenceStack:ie};te.push(Oe)}else if(_e instanceof I){const de={idx:we,def:E(Ee),ruleStack:Le,occurrenceStack:Be};te.push(de),te.push(A);const ie={idx:we,def:_e.definition.concat(E(Ee)),ruleStack:Le,occurrenceStack:Be};te.push(ie)}else if(_e instanceof W){const de=new le({definition:_e.definition,idx:_e.idx}),ie=_e.definition.concat([de],E(Ee)),Oe={idx:we,def:ie,ruleStack:Le,occurrenceStack:Be};te.push(Oe)}else if(_e instanceof z){const de=new me({terminalType:_e.separator}),ie=new le({definition:[de].concat(_e.definition),idx:_e.idx}),Oe=_e.definition.concat([ie],E(Ee)),Pe={idx:we,def:Oe,ruleStack:Le,occurrenceStack:Be};te.push(Pe)}else if(_e instanceof pe){const de={idx:we,def:E(Ee),ruleStack:Le,occurrenceStack:Be};te.push(de),te.push(A);const ie=new me({terminalType:_e.separator}),Oe=new le({definition:[ie].concat(_e.definition),idx:_e.idx}),Pe=_e.definition.concat([Oe],E(Ee)),bt={idx:we,def:Pe,ruleStack:Le,occurrenceStack:Be};te.push(bt)}else if(_e instanceof le){const de={idx:we,def:E(Ee),ruleStack:Le,occurrenceStack:Be};te.push(de),te.push(A);const ie=new le({definition:_e.definition,idx:_e.idx}),Oe=_e.definition.concat([ie],E(Ee)),Pe={idx:we,def:Oe,ruleStack:Le,occurrenceStack:Be};te.push(Pe)}else if(_e instanceof Ie)for(let de=_e.definition.length-1;de>=0;de--){const ie=_e.definition[de],Oe={idx:we,def:ie.definition.concat(E(Ee)),ruleStack:Le,occurrenceStack:Be};te.push(Oe),te.push(A)}else if(_e instanceof Ze)te.push({idx:we,def:_e.definition.concat(E(Ee)),ruleStack:Le,occurrenceStack:Be});else if(_e instanceof ze)te.push(ri(_e,we,Le,Be));else throw Error("non exhaustive match")}return q}function ri(o,t,a,u){const d=(0,b.Z)(a);d.push(o.name);const f=(0,b.Z)(u);return f.push(1),{idx:t,def:o.definition,ruleStack:d,occurrenceStack:f}}var Xe;(function(o){o[o.OPTION=0]="OPTION",o[o.REPETITION=1]="REPETITION",o[o.REPETITION_MANDATORY=2]="REPETITION_MANDATORY",o[o.REPETITION_MANDATORY_WITH_SEPARATOR=3]="REPETITION_MANDATORY_WITH_SEPARATOR",o[o.REPETITION_WITH_SEPARATOR=4]="REPETITION_WITH_SEPARATOR",o[o.ALTERNATION=5]="ALTERNATION"})(Xe||(Xe={}));function Er(o){if(o instanceof I||o==="Option")return Xe.OPTION;if(o instanceof le||o==="Repetition")return Xe.REPETITION;if(o instanceof W||o==="RepetitionMandatory")return Xe.REPETITION_MANDATORY;if(o instanceof z||o==="RepetitionMandatoryWithSeparator")return Xe.REPETITION_MANDATORY_WITH_SEPARATOR;if(o instanceof pe||o==="RepetitionWithSeparator")return Xe.REPETITION_WITH_SEPARATOR;if(o instanceof Ie||o==="Alternation")return Xe.ALTERNATION;throw Error("non exhaustive match")}function Gi(o){const{occurrence:t,rule:a,prodType:u,maxLookahead:d}=o,f=Er(u);return f===Xe.ALTERNATION?Ar(t,a,d):an(t,a,f,d)}function ga(o,t,a,u,d,f){const A=Ar(o,t,a),k=ui(A)?Cn:In;return f(A,u,k,d)}function si(o,t,a,u,d,f){const A=an(o,t,d,a),k=ui(A)?Cn:In;return f(A[0],k,u)}function Bi(o,t,a,u){const d=o.length,f=nt(o,A=>nt(A,k=>k.length===1));if(t)return function(A){const k=(0,x.Z)(A,N=>N.GATE);for(let N=0;N<d;N++){const G=o[N],q=G.length,te=k[N];if(!(te!==void 0&&te.call(this)===!1))e:for(let fe=0;fe<q;fe++){const Ee=G[fe],we=Ee.length;for(let Le=0;Le<we;Le++){const Be=this.LA(Le+1);if(a(Be,Ee[Le])===!1)continue e}return N}}};if(f&&!u){const A=(0,x.Z)(o,N=>(0,Qe.Z)(N)),k=(0,gt.Z)(A,(N,G,q)=>((0,y.Z)(G,te=>{(0,L.Z)(N,te.tokenTypeIdx)||(N[te.tokenTypeIdx]=q),(0,y.Z)(te.categoryMatches,fe=>{(0,L.Z)(N,fe)||(N[fe]=q)})}),N),{});return function(){const N=this.LA(1);return k[N.tokenTypeIdx]}}else return function(){for(let A=0;A<d;A++){const k=o[A],N=k.length;e:for(let G=0;G<N;G++){const q=k[G],te=q.length;for(let fe=0;fe<te;fe++){const Ee=this.LA(fe+1);if(a(Ee,q[fe])===!1)continue e}return A}}}}function Wi(o,t,a){const u=nt(o,f=>f.length===1),d=o.length;if(u&&!a){const f=(0,Qe.Z)(o);if(f.length===1&&(0,F.Z)(f[0].categoryMatches)){const k=f[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===k}}else{const A=(0,gt.Z)(f,(k,N,G)=>(k[N.tokenTypeIdx]=!0,(0,y.Z)(N.categoryMatches,q=>{k[q]=!0}),k),[]);return function(){const k=this.LA(1);return A[k.tokenTypeIdx]===!0}}}else return function(){e:for(let f=0;f<d;f++){const A=o[f],k=A.length;for(let N=0;N<k;N++){const G=this.LA(N+1);if(t(G,A[N])===!1)continue e}return!0}return!1}}class ya extends Zt{constructor(t,a,u){super(),this.topProd=t,this.targetOccurrence=a,this.targetProdType=u}startWalking(){return this.walk(this.topProd),this.restDef}checkIsTarget(t,a,u,d){return t.idx===this.targetOccurrence&&this.targetProdType===a?(this.restDef=u.concat(d),!0):!1}walkOption(t,a,u){this.checkIsTarget(t,Xe.OPTION,a,u)||super.walkOption(t,a,u)}walkAtLeastOne(t,a,u){this.checkIsTarget(t,Xe.REPETITION_MANDATORY,a,u)||super.walkOption(t,a,u)}walkAtLeastOneSep(t,a,u){this.checkIsTarget(t,Xe.REPETITION_MANDATORY_WITH_SEPARATOR,a,u)||super.walkOption(t,a,u)}walkMany(t,a,u){this.checkIsTarget(t,Xe.REPETITION,a,u)||super.walkOption(t,a,u)}walkManySep(t,a,u){this.checkIsTarget(t,Xe.REPETITION_WITH_SEPARATOR,a,u)||super.walkOption(t,a,u)}}class ii extends Yt{constructor(t,a,u){super(),this.targetOccurrence=t,this.targetProdType=a,this.targetRef=u,this.result=[]}checkIsTarget(t,a){t.idx===this.targetOccurrence&&this.targetProdType===a&&(this.targetRef===void 0||t===this.targetRef)&&(this.result=t.definition)}visitOption(t){this.checkIsTarget(t,Xe.OPTION)}visitRepetition(t){this.checkIsTarget(t,Xe.REPETITION)}visitRepetitionMandatory(t){this.checkIsTarget(t,Xe.REPETITION_MANDATORY)}visitRepetitionMandatoryWithSeparator(t){this.checkIsTarget(t,Xe.REPETITION_MANDATORY_WITH_SEPARATOR)}visitRepetitionWithSeparator(t){this.checkIsTarget(t,Xe.REPETITION_WITH_SEPARATOR)}visitAlternation(t){this.checkIsTarget(t,Xe.ALTERNATION)}}function ai(o){const t=new Array(o);for(let a=0;a<o;a++)t[a]=[];return t}function Yr(o){let t=[""];for(let a=0;a<o.length;a++){const u=o[a],d=[];for(let f=0;f<t.length;f++){const A=t[f];d.push(A+"_"+u.tokenTypeIdx);for(let k=0;k<u.categoryMatches.length;k++){const N="_"+u.categoryMatches[k];d.push(A+N)}}t=d}return t}function oi(o,t,a){for(let u=0;u<o.length;u++){if(u===a)continue;const d=o[u];for(let f=0;f<t.length;f++){const A=t[f];if(d[A]===!0)return!1}}return!0}function li(o,t){const a=(0,x.Z)(o,A=>zr([A],1)),u=ai(a.length),d=(0,x.Z)(a,A=>{const k={};return(0,y.Z)(A,N=>{const G=Yr(N.partialPath);(0,y.Z)(G,q=>{k[q]=!0})}),k});let f=a;for(let A=1;A<=t;A++){const k=f;f=ai(k.length);for(let N=0;N<k.length;N++){const G=k[N];for(let q=0;q<G.length;q++){const te=G[q].partialPath,fe=G[q].suffixDef,Ee=Yr(te);if(oi(d,Ee,N)||(0,F.Z)(fe)||te.length===t){const Le=u[N];if(wn(Le,te)===!1){Le.push(te);for(let Be=0;Be<Ee.length;Be++){const _e=Ee[Be];d[N][_e]=!0}}}else{const Le=zr(fe,A+1,te);f[N]=f[N].concat(Le),(0,y.Z)(Le,Be=>{const _e=Yr(Be.partialPath);(0,y.Z)(_e,de=>{d[N][de]=!0})})}}}}return u}function Ar(o,t,a,u){const d=new ii(o,Xe.ALTERNATION,u);return t.accept(d),li(d.result,a)}function an(o,t,a,u){const d=new ii(o,a);t.accept(d);const f=d.result,k=new ya(t,o,a).startWalking(),N=new Ze({definition:f}),G=new Ze({definition:k});return li([N,G],u)}function wn(o,t){e:for(let a=0;a<o.length;a++){const u=o[a];if(u.length===t.length){for(let d=0;d<u.length;d++){const f=t[d],A=u[d];if((f===A||A.categoryMatchesMap[f.tokenTypeIdx]!==void 0)===!1)continue e}return!0}}return!1}function Ki(o,t){return o.length<t.length&&nt(o,(a,u)=>{const d=t[u];return a===d||d.categoryMatchesMap[a.tokenTypeIdx]})}function ui(o){return nt(o,t=>nt(t,a=>nt(a,u=>(0,F.Z)(u.categoryMatches))))}function Vi(o){const t=o.lookaheadStrategy.validate({rules:o.rules,tokenTypes:o.tokenTypes,grammarName:o.grammarName});return(0,x.Z)(t,a=>Object.assign({type:$t.CUSTOM_LOOKAHEAD_VALIDATION},a))}function Xr(o,t,a,u){const d=(0,Lt.Z)(o,N=>ji(N,a)),f=hi(o,t,a),A=(0,Lt.Z)(o,N=>qi(N,a)),k=(0,Lt.Z)(o,N=>zi(N,o,u,a));return d.concat(f,A,k)}function ji(o,t){const a=new Hi;o.accept(a);const u=a.allProductions,d=bi(u,Pt),f=Ae(d,k=>k.length>1);return(0,x.Z)((0,O.Z)(f),k=>{const N=Nt(k),G=t.buildDuplicateFoundError(o,k),q=rt(N),te={message:G,type:$t.DUPLICATE_PRODUCTIONS,ruleName:o.name,dslName:q,occurrence:N.idx},fe=ci(N);return fe&&(te.parameter=fe),te})}function Pt(o){return`${rt(o)}_#_${o.idx}_#_${ci(o)}`}function ci(o){return o instanceof me?o.terminalType.name:o instanceof He?o.nonTerminalName:""}class Hi extends Yt{constructor(){super(...arguments),this.allProductions=[]}visitNonTerminal(t){this.allProductions.push(t)}visitOption(t){this.allProductions.push(t)}visitRepetitionWithSeparator(t){this.allProductions.push(t)}visitRepetitionMandatory(t){this.allProductions.push(t)}visitRepetitionMandatoryWithSeparator(t){this.allProductions.push(t)}visitRepetition(t){this.allProductions.push(t)}visitAlternation(t){this.allProductions.push(t)}visitTerminal(t){this.allProductions.push(t)}}function zi(o,t,a,u){const d=[];if((0,gt.Z)(t,(A,k)=>k.name===o.name?A+1:A,0)>1){const A=u.buildDuplicateRuleNameError({topLevelRule:o,grammarName:a});d.push({message:A,type:$t.DUPLICATE_RULE_NAME,ruleName:o.name})}return d}function Yi(o,t,a){const u=[];let d;return Ye(t,o)||(d=`Invalid rule override, rule: ->${o}<- cannot be overridden in the grammar: ->${a}<-as it is not defined in any of the super grammars `,u.push({message:d,type:$t.INVALID_RULE_OVERRIDE,ruleName:o})),u}function di(o,t,a,u=[]){const d=[],f=on(t.definition);if((0,F.Z)(f))return[];{const A=o.name;Ye(f,o)&&d.push({message:a.buildLeftRecursionError({topLevelRule:o,leftRecursionPath:u}),type:$t.LEFT_RECURSION,ruleName:A});const N=en(f,u.concat([o])),G=(0,Lt.Z)(N,q=>{const te=(0,b.Z)(u);return te.push(q),di(o,q,a,te)});return d.concat(G)}}function on(o){let t=[];if((0,F.Z)(o))return t;const a=Nt(o);if(a instanceof He)t.push(a.referencedRule);else if(a instanceof Ze||a instanceof I||a instanceof W||a instanceof z||a instanceof pe||a instanceof le)t=t.concat(on(a.definition));else if(a instanceof Ie)t=(0,Qe.Z)((0,x.Z)(a.definition,f=>on(f.definition)));else if(!(a instanceof me))throw Error("non exhaustive match");const u=xt(a),d=o.length>1;if(u&&d){const f=E(o);return t.concat(on(f))}else return t}class Jr extends Yt{constructor(){super(...arguments),this.alternations=[]}visitAlternation(t){this.alternations.push(t)}}function Xi(o,t){const a=new Jr;o.accept(a);const u=a.alternations;return(0,Lt.Z)(u,f=>{const A=Ue(f.definition);return(0,Lt.Z)(A,(k,N)=>{const G=rr([k],[],In,1);return(0,F.Z)(G)?[{message:t.buildEmptyAlternationError({topLevelRule:o,alternation:f,emptyChoiceIdx:N}),type:$t.NONE_LAST_EMPTY_ALT,ruleName:o.name,occurrence:f.idx,alternative:N+1}]:[]})})}function Ji(o,t,a){const u=new Jr;o.accept(u);let d=u.alternations;return d=xe(d,A=>A.ignoreAmbiguities===!0),(0,Lt.Z)(d,A=>{const k=A.idx,N=A.maxLookahead||t,G=Ar(k,o,N,A),q=ta(G,A,o,a),te=fi(G,A,o,a);return q.concat(te)})}class Qi extends Yt{constructor(){super(...arguments),this.allProductions=[]}visitRepetitionWithSeparator(t){this.allProductions.push(t)}visitRepetitionMandatory(t){this.allProductions.push(t)}visitRepetitionMandatoryWithSeparator(t){this.allProductions.push(t)}visitRepetition(t){this.allProductions.push(t)}}function qi(o,t){const a=new Jr;o.accept(a);const u=a.alternations;return(0,Lt.Z)(u,f=>f.definition.length>255?[{message:t.buildTooManyAlternativesError({topLevelRule:o,alternation:f}),type:$t.TOO_MANY_ALTS,ruleName:o.name,occurrence:f.idx}]:[])}function ea(o,t,a){const u=[];return(0,y.Z)(o,d=>{const f=new Qi;d.accept(f);const A=f.allProductions;(0,y.Z)(A,k=>{const N=Er(k),G=k.maxLookahead||t,q=k.idx,fe=an(q,d,N,G)[0];if((0,F.Z)((0,Qe.Z)(fe))){const Ee=a.buildEmptyRepetitionError({topLevelRule:d,repetition:k});u.push({message:Ee,type:$t.NO_NON_EMPTY_LOOKAHEAD,ruleName:d.name})}})}),u}function ta(o,t,a,u){const d=[],f=(0,gt.Z)(o,(k,N,G)=>(t.definition[G].ignoreAmbiguities===!0||(0,y.Z)(N,q=>{const te=[G];(0,y.Z)(o,(fe,Ee)=>{G!==Ee&&wn(fe,q)&&t.definition[Ee].ignoreAmbiguities!==!0&&te.push(Ee)}),te.length>1&&!wn(d,q)&&(d.push(q),k.push({alts:te,path:q}))}),k),[]);return(0,x.Z)(f,k=>{const N=(0,x.Z)(k.alts,q=>q+1);return{message:u.buildAlternationAmbiguityError({topLevelRule:a,alternation:t,ambiguityIndices:N,prefixPath:k.path}),type:$t.AMBIGUOUS_ALTS,ruleName:a.name,occurrence:t.idx,alternatives:k.alts}})}function fi(o,t,a,u){const d=(0,gt.Z)(o,(A,k,N)=>{const G=(0,x.Z)(k,q=>({idx:N,path:q}));return A.concat(G)},[]);return dn((0,Lt.Z)(d,A=>{if(t.definition[A.idx].ignoreAmbiguities===!0)return[];const N=A.idx,G=A.path,q=(0,yt.Z)(d,fe=>t.definition[fe.idx].ignoreAmbiguities!==!0&&fe.idx<N&&Ki(fe.path,G));return(0,x.Z)(q,fe=>{const Ee=[fe.idx+1,N+1],we=t.idx===0?"":t.idx;return{message:u.buildAlternationPrefixAmbiguityError({topLevelRule:a,alternation:t,ambiguityIndices:Ee,prefixPath:fe.path}),type:$t.AMBIGUOUS_PREFIX_ALTS,ruleName:a.name,occurrence:we,alternatives:Ee}})}))}function hi(o,t,a){const u=[],d=(0,x.Z)(t,f=>f.name);return(0,y.Z)(o,f=>{const A=f.name;if(Ye(d,A)){const k=a.buildNamespaceConflictError(f);u.push({message:k,type:$t.CONFLICT_TOKENS_RULES_NAMESPACE,ruleName:A})}}),u}function na(o){const t=(0,wt.Z)(o,{errMsgProvider:We}),a={};return(0,y.Z)(o.rules,u=>{a[u.name]=u}),Qs(a,t.errMsgProvider)}function ra(o){return o=(0,wt.Z)(o,{errMsgProvider:hn}),Xr(o.rules,o.tokenTypes,o.errMsgProvider,o.grammarName)}const Qr="MismatchedTokenException",qr="NoViableAltException",pi="EarlyExitException",mi="NotAllInputParsedException",gi=[Qr,qr,pi,mi];Object.freeze(gi);function _r(o){return Ye(gi,o.name)}class $r extends Error{constructor(t,a){super(t),this.token=a,this.resyncedTokens=[],Object.setPrototypeOf(this,new.target.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}}class Ir extends $r{constructor(t,a,u){super(t,a),this.previousToken=u,this.name=Qr}}class sa extends $r{constructor(t,a,u){super(t,a),this.previousToken=u,this.name=qr}}class ia extends $r{constructor(t,a){super(t,a),this.name=mi}}class aa extends $r{constructor(t,a,u){super(t,a),this.previousToken=u,this.name=pi}}const es={},ts="InRuleRecoveryException";class oa extends Error{constructor(t){super(t),this.name=ts}}class yi{initRecoverable(t){this.firstAfterRepMap={},this.resyncFollows={},this.recoveryEnabled=(0,L.Z)(t,"recoveryEnabled")?t.recoveryEnabled:ln.recoveryEnabled,this.recoveryEnabled&&(this.attemptInRepetitionRecovery=Ti)}getTokenToInsert(t){const a=Kr(t,"",NaN,NaN,NaN,NaN,NaN,NaN);return a.isInsertedInRecovery=!0,a}canTokenTypeBeInsertedInRecovery(t){return!0}canTokenTypeBeDeletedInRecovery(t){return!0}tryInRepetitionRecovery(t,a,u,d){const f=this.findReSyncTokenType(),A=this.exportLexerState(),k=[];let N=!1;const G=this.LA(1);let q=this.LA(1);const te=()=>{const fe=this.LA(0),Ee=this.errorMessageProvider.buildMismatchTokenMessage({expected:d,actual:G,previous:fe,ruleName:this.getCurrRuleFullName()}),we=new Ir(Ee,G,this.LA(0));we.resyncedTokens=Ue(k),this.SAVE_ERROR(we)};for(;!N;)if(this.tokenMatcher(q,d)){te();return}else if(u.call(this)){te(),t.apply(this,a);return}else this.tokenMatcher(q,f)?N=!0:(q=this.SKIP_TOKEN(),this.addToResyncTokens(q,k));this.importLexerState(A)}shouldInRepetitionRecoveryBeTried(t,a,u){return!(u===!1||this.tokenMatcher(this.LA(1),t)||this.isBackTracking()||this.canPerformInRuleRecovery(t,this.getFollowsForInRuleRecovery(t,a)))}getFollowsForInRuleRecovery(t,a){const u=this.getCurrentGrammarPath(t,a);return this.getNextPossibleTokenTypes(u)}tryInRuleRecovery(t,a){if(this.canRecoverWithSingleTokenInsertion(t,a))return this.getTokenToInsert(t);if(this.canRecoverWithSingleTokenDeletion(t)){const u=this.SKIP_TOKEN();return this.consumeToken(),u}throw new oa("sad sad panda")}canPerformInRuleRecovery(t,a){return this.canRecoverWithSingleTokenInsertion(t,a)||this.canRecoverWithSingleTokenDeletion(t)}canRecoverWithSingleTokenInsertion(t,a){if(!this.canTokenTypeBeInsertedInRecovery(t)||(0,F.Z)(a))return!1;const u=this.LA(1);return(0,zn.Z)(a,f=>this.tokenMatcher(u,f))!==void 0}canRecoverWithSingleTokenDeletion(t){return this.canTokenTypeBeDeletedInRecovery(t)?this.tokenMatcher(this.LA(2),t):!1}isInCurrentRuleReSyncSet(t){const a=this.getCurrFollowKey(),u=this.getFollowSetFromFollowKey(a);return Ye(u,t)}findReSyncTokenType(){const t=this.flattenFollowSet();let a=this.LA(1),u=2;for(;;){const d=(0,zn.Z)(t,f=>Js(a,f));if(d!==void 0)return d;a=this.LA(u),u++}}getCurrFollowKey(){if(this.RULE_STACK.length===1)return es;const t=this.getLastExplicitRuleShortName(),a=this.getLastExplicitRuleOccurrenceIndex(),u=this.getPreviousExplicitRuleShortName();return{ruleName:this.shortRuleNameToFullName(t),idxInCallingRule:a,inRule:this.shortRuleNameToFullName(u)}}buildFullFollowKeyStack(){const t=this.RULE_STACK,a=this.RULE_OCCURRENCE_STACK;return(0,x.Z)(t,(u,d)=>d===0?es:{ruleName:this.shortRuleNameToFullName(u),idxInCallingRule:a[d],inRule:this.shortRuleNameToFullName(t[d-1])})}flattenFollowSet(){const t=(0,x.Z)(this.buildFullFollowKeyStack(),a=>this.getFollowSetFromFollowKey(a));return(0,Qe.Z)(t)}getFollowSetFromFollowKey(t){if(t===es)return[sn];const a=t.ruleName+t.idxInCallingRule+vn+t.inRule;return this.resyncFollows[a]}addToResyncTokens(t,a){return this.tokenMatcher(t,sn)||a.push(t),a}reSyncTo(t){const a=[];let u=this.LA(1);for(;this.tokenMatcher(u,t)===!1;)u=this.SKIP_TOKEN(),this.addToResyncTokens(u,a);return Ue(a)}attemptInRepetitionRecovery(t,a,u,d,f,A,k){}getCurrentGrammarPath(t,a){const u=this.getHumanReadableRuleStack(),d=(0,b.Z)(this.RULE_OCCURRENCE_STACK);return{ruleStack:u,occurrenceStack:d,lastTok:t,lastTokOccurrence:a}}getHumanReadableRuleStack(){return(0,x.Z)(this.RULE_STACK,t=>this.shortRuleNameToFullName(t))}}function Ti(o,t,a,u,d,f,A){const k=this.getKeyForAutomaticLookahead(u,d);let N=this.firstAfterRepMap[k];if(N===void 0){const fe=this.getCurrRuleFullName(),Ee=this.getGAstProductions()[fe];N=new f(Ee,d).startWalking(),this.firstAfterRepMap[k]=N}let G=N.token,q=N.occurrence;const te=N.isEndOfRule;this.RULE_STACK.length===1&&te&&G===void 0&&(G=sn,q=1),!(G===void 0||q===void 0)&&this.shouldInRepetitionRecoveryBeTried(G,q,A)&&this.tryInRepetitionRecovery(o,t,a,G)}const ns=4,Ut=8,la=12,rs=8,vi=1<<Ut,Ri=2<<Ut,sr=3<<Ut,Cr=4<<Ut,kr=5<<Ut,xr=6<<Ut;function ss(o,t,a){return a|t|o}const Ta=32-rs;class ua{constructor(t){var a;this.maxLookahead=(a=t==null?void 0:t.maxLookahead)!==null&&a!==void 0?a:ln.maxLookahead}validate(t){const a=this.validateNoLeftRecursion(t.rules);if((0,F.Z)(a)){const u=this.validateEmptyOrAlternatives(t.rules),d=this.validateAmbiguousAlternationAlternatives(t.rules,this.maxLookahead),f=this.validateSomeNonEmptyLookaheadPath(t.rules,this.maxLookahead);return[...a,...u,...d,...f]}return a}validateNoLeftRecursion(t){return(0,Lt.Z)(t,a=>di(a,a,hn))}validateEmptyOrAlternatives(t){return(0,Lt.Z)(t,a=>Xi(a,hn))}validateAmbiguousAlternationAlternatives(t,a){return(0,Lt.Z)(t,u=>Ji(u,a,hn))}validateSomeNonEmptyLookaheadPath(t,a){return ea(t,a,hn)}buildLookaheadForAlternation(t){return ga(t.prodOccurrence,t.rule,t.maxLookahead,t.hasPredicates,t.dynamicTokensEnabled,Bi)}buildLookaheadForOptional(t){return si(t.prodOccurrence,t.rule,t.maxLookahead,t.dynamicTokensEnabled,Er(t.prodType),Wi)}}class va{initLooksAhead(t){this.dynamicTokensEnabled=(0,L.Z)(t,"dynamicTokensEnabled")?t.dynamicTokensEnabled:ln.dynamicTokensEnabled,this.maxLookahead=(0,L.Z)(t,"maxLookahead")?t.maxLookahead:ln.maxLookahead,this.lookaheadStrategy=(0,L.Z)(t,"lookaheadStrategy")?t.lookaheadStrategy:new ua({maxLookahead:this.maxLookahead}),this.lookAheadFuncsCache=new Map}preComputeLookaheadFunctions(t){(0,y.Z)(t,a=>{this.TRACE_INIT(`${a.name} Rule Lookahead`,()=>{const{alternation:u,repetition:d,option:f,repetitionMandatory:A,repetitionMandatoryWithSeparator:k,repetitionWithSeparator:N}=da(a);(0,y.Z)(u,G=>{const q=G.idx===0?"":G.idx;this.TRACE_INIT(`${rt(G)}${q}`,()=>{const te=this.lookaheadStrategy.buildLookaheadForAlternation({prodOccurrence:G.idx,rule:a,maxLookahead:G.maxLookahead||this.maxLookahead,hasPredicates:G.hasPredicates,dynamicTokensEnabled:this.dynamicTokensEnabled}),fe=ss(this.fullRuleNameToShort[a.name],vi,G.idx);this.setLaFuncCache(fe,te)})}),(0,y.Z)(d,G=>{this.computeLookaheadFunc(a,G.idx,sr,"Repetition",G.maxLookahead,rt(G))}),(0,y.Z)(f,G=>{this.computeLookaheadFunc(a,G.idx,Ri,"Option",G.maxLookahead,rt(G))}),(0,y.Z)(A,G=>{this.computeLookaheadFunc(a,G.idx,Cr,"RepetitionMandatory",G.maxLookahead,rt(G))}),(0,y.Z)(k,G=>{this.computeLookaheadFunc(a,G.idx,xr,"RepetitionMandatoryWithSeparator",G.maxLookahead,rt(G))}),(0,y.Z)(N,G=>{this.computeLookaheadFunc(a,G.idx,kr,"RepetitionWithSeparator",G.maxLookahead,rt(G))})})})}computeLookaheadFunc(t,a,u,d,f,A){this.TRACE_INIT(`${A}${a===0?"":a}`,()=>{const k=this.lookaheadStrategy.buildLookaheadForOptional({prodOccurrence:a,rule:t,maxLookahead:f||this.maxLookahead,dynamicTokensEnabled:this.dynamicTokensEnabled,prodType:d}),N=ss(this.fullRuleNameToShort[t.name],u,a);this.setLaFuncCache(N,k)})}getKeyForAutomaticLookahead(t,a){const u=this.getLastExplicitRuleShortName();return ss(u,t,a)}getLaFuncFromCache(t){return this.lookAheadFuncsCache.get(t)}setLaFuncCache(t,a){this.lookAheadFuncsCache.set(t,a)}}class ca extends Yt{constructor(){super(...arguments),this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}reset(){this.dslMethods={option:[],alternation:[],repetition:[],repetitionWithSeparator:[],repetitionMandatory:[],repetitionMandatoryWithSeparator:[]}}visitOption(t){this.dslMethods.option.push(t)}visitRepetitionWithSeparator(t){this.dslMethods.repetitionWithSeparator.push(t)}visitRepetitionMandatory(t){this.dslMethods.repetitionMandatory.push(t)}visitRepetitionMandatoryWithSeparator(t){this.dslMethods.repetitionMandatoryWithSeparator.push(t)}visitRepetition(t){this.dslMethods.repetition.push(t)}visitAlternation(t){this.dslMethods.alternation.push(t)}}const Sr=new ca;function da(o){Sr.reset(),o.accept(Sr);const t=Sr.dslMethods;return Sr.reset(),t}function Ei(o,t){isNaN(o.startOffset)===!0?(o.startOffset=t.startOffset,o.endOffset=t.endOffset):o.endOffset<t.endOffset&&(o.endOffset=t.endOffset)}function i(o,t){isNaN(o.startOffset)===!0?(o.startOffset=t.startOffset,o.startColumn=t.startColumn,o.startLine=t.startLine,o.endOffset=t.endOffset,o.endColumn=t.endColumn,o.endLine=t.endLine):o.endOffset<t.endOffset&&(o.endOffset=t.endOffset,o.endColumn=t.endColumn,o.endLine=t.endLine)}function e(o,t,a){o.children[a]===void 0?o.children[a]=[t]:o.children[a].push(t)}function n(o,t,a){o.children[t]===void 0?o.children[t]=[a]:o.children[t].push(a)}const r="name";function s(o,t){Object.defineProperty(o,r,{enumerable:!1,configurable:!0,writable:!1,value:t})}function l(o,t){const a=(0,w.Z)(o),u=a.length;for(let d=0;d<u;d++){const f=a[d],A=o[f],k=A.length;for(let N=0;N<k;N++){const G=A[N];G.tokenTypeIdx===void 0&&this[G.name](G.children,t)}}}function c(o,t){const a=function(){};s(a,o+"BaseSemantics");const u={visit:function(d,f){if((0,qe.Z)(d)&&(d=d[0]),!(0,it.Z)(d))return this[d.name](d.children,f)},validateVisitor:function(){const d=U(this,t);if(!(0,F.Z)(d)){const f=(0,x.Z)(d,A=>A.msg);throw Error(`Errors Detected in CST Visitor <${this.constructor.name}>:
	${f.join(`

`).replace(/\n/g,`
	`)}`)}}};return a.prototype=u,a.prototype.constructor=a,a._RULE_NAMES=t,a}function m(o,t,a){const u=function(){};s(u,o+"BaseSemanticsWithDefaults");const d=Object.create(a.prototype);return(0,y.Z)(t,f=>{d[f]=l}),u.prototype=d,u.prototype.constructor=u,u}var $;(function(o){o[o.REDUNDANT_METHOD=0]="REDUNDANT_METHOD",o[o.MISSING_METHOD=1]="MISSING_METHOD"})($||($={}));function U(o,t){return H(o,t)}function H(o,t){const a=(0,yt.Z)(t,d=>(0,Ce.Z)(o[d])===!1),u=(0,x.Z)(a,d=>({msg:`Missing visitor method: <${d}> on ${o.constructor.name} CST Visitor.`,type:$.MISSING_METHOD,methodName:d}));return dn(u)}class ue{initTreeBuilder(t){if(this.CST_STACK=[],this.outputCst=t.outputCst,this.nodeLocationTracking=(0,L.Z)(t,"nodeLocationTracking")?t.nodeLocationTracking:ln.nodeLocationTracking,!this.outputCst)this.cstInvocationStateUpdate=et.Z,this.cstFinallyStateUpdate=et.Z,this.cstPostTerminal=et.Z,this.cstPostNonTerminal=et.Z,this.cstPostRule=et.Z;else if(/full/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=i,this.setNodeLocationFromNode=i,this.cstPostRule=et.Z,this.setInitialNodeLocation=this.setInitialNodeLocationFullRecovery):(this.setNodeLocationFromToken=et.Z,this.setNodeLocationFromNode=et.Z,this.cstPostRule=this.cstPostRuleFull,this.setInitialNodeLocation=this.setInitialNodeLocationFullRegular);else if(/onlyOffset/i.test(this.nodeLocationTracking))this.recoveryEnabled?(this.setNodeLocationFromToken=Ei,this.setNodeLocationFromNode=Ei,this.cstPostRule=et.Z,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRecovery):(this.setNodeLocationFromToken=et.Z,this.setNodeLocationFromNode=et.Z,this.cstPostRule=this.cstPostRuleOnlyOffset,this.setInitialNodeLocation=this.setInitialNodeLocationOnlyOffsetRegular);else if(/none/i.test(this.nodeLocationTracking))this.setNodeLocationFromToken=et.Z,this.setNodeLocationFromNode=et.Z,this.cstPostRule=et.Z,this.setInitialNodeLocation=et.Z;else throw Error(`Invalid <nodeLocationTracking> config option: "${t.nodeLocationTracking}"`)}setInitialNodeLocationOnlyOffsetRecovery(t){t.location={startOffset:NaN,endOffset:NaN}}setInitialNodeLocationOnlyOffsetRegular(t){t.location={startOffset:this.LA(1).startOffset,endOffset:NaN}}setInitialNodeLocationFullRecovery(t){t.location={startOffset:NaN,startLine:NaN,startColumn:NaN,endOffset:NaN,endLine:NaN,endColumn:NaN}}setInitialNodeLocationFullRegular(t){const a=this.LA(1);t.location={startOffset:a.startOffset,startLine:a.startLine,startColumn:a.startColumn,endOffset:NaN,endLine:NaN,endColumn:NaN}}cstInvocationStateUpdate(t){const a={name:t,children:Object.create(null)};this.setInitialNodeLocation(a),this.CST_STACK.push(a)}cstFinallyStateUpdate(){this.CST_STACK.pop()}cstPostRuleFull(t){const a=this.LA(0),u=t.location;u.startOffset<=a.startOffset?(u.endOffset=a.endOffset,u.endLine=a.endLine,u.endColumn=a.endColumn):(u.startOffset=NaN,u.startLine=NaN,u.startColumn=NaN)}cstPostRuleOnlyOffset(t){const a=this.LA(0),u=t.location;u.startOffset<=a.startOffset?u.endOffset=a.endOffset:u.startOffset=NaN}cstPostTerminal(t,a){const u=this.CST_STACK[this.CST_STACK.length-1];e(u,a,t),this.setNodeLocationFromToken(u.location,a)}cstPostNonTerminal(t,a){const u=this.CST_STACK[this.CST_STACK.length-1];n(u,a,t),this.setNodeLocationFromNode(u.location,t.location)}getBaseCstVisitorConstructor(){if((0,it.Z)(this.baseCstVisitorConstructor)){const t=c(this.className,(0,w.Z)(this.gastProductionsCache));return this.baseCstVisitorConstructor=t,t}return this.baseCstVisitorConstructor}getBaseCstVisitorConstructorWithDefaults(){if((0,it.Z)(this.baseCstVisitorWithDefaultsConstructor)){const t=m(this.className,(0,w.Z)(this.gastProductionsCache),this.getBaseCstVisitorConstructor());return this.baseCstVisitorWithDefaultsConstructor=t,t}return this.baseCstVisitorWithDefaultsConstructor}getLastExplicitRuleShortName(){const t=this.RULE_STACK;return t[t.length-1]}getPreviousExplicitRuleShortName(){const t=this.RULE_STACK;return t[t.length-2]}getLastExplicitRuleOccurrenceIndex(){const t=this.RULE_OCCURRENCE_STACK;return t[t.length-1]}}class ne{initLexerAdapter(){this.tokVector=[],this.tokVectorLength=0,this.currIdx=-1}set input(t){if(this.selfAnalysisDone!==!0)throw Error("Missing <performSelfAnalysis> invocation at the end of the Parser's constructor.");this.reset(),this.tokVector=t,this.tokVectorLength=t.length}get input(){return this.tokVector}SKIP_TOKEN(){return this.currIdx<=this.tokVector.length-2?(this.consumeToken(),this.LA(1)):$i}LA(t){const a=this.currIdx+t;return a<0||this.tokVectorLength<=a?$i:this.tokVector[a]}consumeToken(){this.currIdx++}exportLexerState(){return this.currIdx}importLexerState(t){this.currIdx=t}resetLexerState(){this.currIdx=-1}moveToTerminatedState(){this.currIdx=this.tokVector.length-1}getLexerPosition(){return this.exportLexerState()}}class X{ACTION(t){return t.call(this)}consume(t,a,u){return this.consumeInternal(a,t,u)}subrule(t,a,u){return this.subruleInternal(a,t,u)}option(t,a){return this.optionInternal(a,t)}or(t,a){return this.orInternal(a,t)}many(t,a){return this.manyInternal(t,a)}atLeastOne(t,a){return this.atLeastOneInternal(t,a)}CONSUME(t,a){return this.consumeInternal(t,0,a)}CONSUME1(t,a){return this.consumeInternal(t,1,a)}CONSUME2(t,a){return this.consumeInternal(t,2,a)}CONSUME3(t,a){return this.consumeInternal(t,3,a)}CONSUME4(t,a){return this.consumeInternal(t,4,a)}CONSUME5(t,a){return this.consumeInternal(t,5,a)}CONSUME6(t,a){return this.consumeInternal(t,6,a)}CONSUME7(t,a){return this.consumeInternal(t,7,a)}CONSUME8(t,a){return this.consumeInternal(t,8,a)}CONSUME9(t,a){return this.consumeInternal(t,9,a)}SUBRULE(t,a){return this.subruleInternal(t,0,a)}SUBRULE1(t,a){return this.subruleInternal(t,1,a)}SUBRULE2(t,a){return this.subruleInternal(t,2,a)}SUBRULE3(t,a){return this.subruleInternal(t,3,a)}SUBRULE4(t,a){return this.subruleInternal(t,4,a)}SUBRULE5(t,a){return this.subruleInternal(t,5,a)}SUBRULE6(t,a){return this.subruleInternal(t,6,a)}SUBRULE7(t,a){return this.subruleInternal(t,7,a)}SUBRULE8(t,a){return this.subruleInternal(t,8,a)}SUBRULE9(t,a){return this.subruleInternal(t,9,a)}OPTION(t){return this.optionInternal(t,0)}OPTION1(t){return this.optionInternal(t,1)}OPTION2(t){return this.optionInternal(t,2)}OPTION3(t){return this.optionInternal(t,3)}OPTION4(t){return this.optionInternal(t,4)}OPTION5(t){return this.optionInternal(t,5)}OPTION6(t){return this.optionInternal(t,6)}OPTION7(t){return this.optionInternal(t,7)}OPTION8(t){return this.optionInternal(t,8)}OPTION9(t){return this.optionInternal(t,9)}OR(t){return this.orInternal(t,0)}OR1(t){return this.orInternal(t,1)}OR2(t){return this.orInternal(t,2)}OR3(t){return this.orInternal(t,3)}OR4(t){return this.orInternal(t,4)}OR5(t){return this.orInternal(t,5)}OR6(t){return this.orInternal(t,6)}OR7(t){return this.orInternal(t,7)}OR8(t){return this.orInternal(t,8)}OR9(t){return this.orInternal(t,9)}MANY(t){this.manyInternal(0,t)}MANY1(t){this.manyInternal(1,t)}MANY2(t){this.manyInternal(2,t)}MANY3(t){this.manyInternal(3,t)}MANY4(t){this.manyInternal(4,t)}MANY5(t){this.manyInternal(5,t)}MANY6(t){this.manyInternal(6,t)}MANY7(t){this.manyInternal(7,t)}MANY8(t){this.manyInternal(8,t)}MANY9(t){this.manyInternal(9,t)}MANY_SEP(t){this.manySepFirstInternal(0,t)}MANY_SEP1(t){this.manySepFirstInternal(1,t)}MANY_SEP2(t){this.manySepFirstInternal(2,t)}MANY_SEP3(t){this.manySepFirstInternal(3,t)}MANY_SEP4(t){this.manySepFirstInternal(4,t)}MANY_SEP5(t){this.manySepFirstInternal(5,t)}MANY_SEP6(t){this.manySepFirstInternal(6,t)}MANY_SEP7(t){this.manySepFirstInternal(7,t)}MANY_SEP8(t){this.manySepFirstInternal(8,t)}MANY_SEP9(t){this.manySepFirstInternal(9,t)}AT_LEAST_ONE(t){this.atLeastOneInternal(0,t)}AT_LEAST_ONE1(t){return this.atLeastOneInternal(1,t)}AT_LEAST_ONE2(t){this.atLeastOneInternal(2,t)}AT_LEAST_ONE3(t){this.atLeastOneInternal(3,t)}AT_LEAST_ONE4(t){this.atLeastOneInternal(4,t)}AT_LEAST_ONE5(t){this.atLeastOneInternal(5,t)}AT_LEAST_ONE6(t){this.atLeastOneInternal(6,t)}AT_LEAST_ONE7(t){this.atLeastOneInternal(7,t)}AT_LEAST_ONE8(t){this.atLeastOneInternal(8,t)}AT_LEAST_ONE9(t){this.atLeastOneInternal(9,t)}AT_LEAST_ONE_SEP(t){this.atLeastOneSepFirstInternal(0,t)}AT_LEAST_ONE_SEP1(t){this.atLeastOneSepFirstInternal(1,t)}AT_LEAST_ONE_SEP2(t){this.atLeastOneSepFirstInternal(2,t)}AT_LEAST_ONE_SEP3(t){this.atLeastOneSepFirstInternal(3,t)}AT_LEAST_ONE_SEP4(t){this.atLeastOneSepFirstInternal(4,t)}AT_LEAST_ONE_SEP5(t){this.atLeastOneSepFirstInternal(5,t)}AT_LEAST_ONE_SEP6(t){this.atLeastOneSepFirstInternal(6,t)}AT_LEAST_ONE_SEP7(t){this.atLeastOneSepFirstInternal(7,t)}AT_LEAST_ONE_SEP8(t){this.atLeastOneSepFirstInternal(8,t)}AT_LEAST_ONE_SEP9(t){this.atLeastOneSepFirstInternal(9,t)}RULE(t,a,u=Ii){if(Ye(this.definedRulesNames,t)){const A={message:hn.buildDuplicateRuleNameError({topLevelRule:t,grammarName:this.className}),type:$t.DUPLICATE_RULE_NAME,ruleName:t};this.definitionErrors.push(A)}this.definedRulesNames.push(t);const d=this.defineRule(t,a,u);return this[t]=d,d}OVERRIDE_RULE(t,a,u=Ii){const d=Yi(t,this.definedRulesNames,this.className);this.definitionErrors=this.definitionErrors.concat(d);const f=this.defineRule(t,a,u);return this[t]=f,f}BACKTRACK(t,a){return function(){this.isBackTrackingStack.push(1);const u=this.saveRecogState();try{return t.apply(this,a),!0}catch(d){if(_r(d))return!1;throw d}finally{this.reloadRecogState(u),this.isBackTrackingStack.pop()}}}getGAstProductions(){return this.gastProductionsCache}getSerializedGastProductions(){return vt((0,O.Z)(this.gastProductionsCache))}}var De=g(7247);class Ve{initRecognizerEngine(t,a){if(this.className=this.constructor.name,this.shortRuleNameToFull={},this.fullRuleNameToShort={},this.ruleShortNameIdx=256,this.tokenMatcher=Cn,this.subruleIdx=0,this.definedRulesNames=[],this.tokensMap={},this.isBackTrackingStack=[],this.RULE_STACK=[],this.RULE_OCCURRENCE_STACK=[],this.gastProductionsCache={},(0,L.Z)(a,"serializedGrammar"))throw Error(`The Parser's configuration can no longer contain a <serializedGrammar> property.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_6-0-0
	For Further details.`);if((0,qe.Z)(t)){if((0,F.Z)(t))throw Error(`A Token Vocabulary cannot be empty.
	Note that the first argument for the parser constructor
	is no longer a Token vector (since v4.0).`);if(typeof t[0].startOffset=="number")throw Error(`The Parser constructor no longer accepts a token vector as the first argument.
	See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_4-0-0
	For Further details.`)}if((0,qe.Z)(t))this.tokensMap=(0,gt.Z)(t,(f,A)=>(f[A.name]=A,f),{});else if((0,L.Z)(t,"modes")&&nt((0,Qe.Z)((0,O.Z)(t.modes)),Ks)){const f=(0,Qe.Z)((0,O.Z)(t.modes)),A=cn(f);this.tokensMap=(0,gt.Z)(A,(k,N)=>(k[N.name]=N,k),{})}else if((0,De.Z)(t))this.tokensMap=(0,b.Z)(t);else throw new Error("<tokensDictionary> argument must be An Array of Token constructors, A dictionary of Token constructors or an IMultiModeLexerDefinition");this.tokensMap.EOF=sn;const u=(0,L.Z)(t,"modes")?(0,Qe.Z)((0,O.Z)(t.modes)):(0,O.Z)(t),d=nt(u,f=>(0,F.Z)(f.categoryMatches));this.tokenMatcher=d?Cn:In,kn((0,O.Z)(this.tokensMap))}defineRule(t,a,u){if(this.selfAnalysisDone)throw Error(`Grammar rule <${t}> may not be defined after the 'performSelfAnalysis' method has been called'
Make sure that all grammar rule definitions are done before 'performSelfAnalysis' is called.`);const d=(0,L.Z)(u,"resyncEnabled")?u.resyncEnabled:Ii.resyncEnabled,f=(0,L.Z)(u,"recoveryValueFunc")?u.recoveryValueFunc:Ii.recoveryValueFunc,A=this.ruleShortNameIdx<<ns+Ut;this.ruleShortNameIdx++,this.shortRuleNameToFull[A]=t,this.fullRuleNameToShort[t]=A;let k;return this.outputCst===!0?k=function(...q){try{this.ruleInvocationStateUpdate(A,t,this.subruleIdx),a.apply(this,q);const te=this.CST_STACK[this.CST_STACK.length-1];return this.cstPostRule(te),te}catch(te){return this.invokeRuleCatch(te,d,f)}finally{this.ruleFinallyStateUpdate()}}:k=function(...q){try{return this.ruleInvocationStateUpdate(A,t,this.subruleIdx),a.apply(this,q)}catch(te){return this.invokeRuleCatch(te,d,f)}finally{this.ruleFinallyStateUpdate()}},Object.assign(k,{ruleName:t,originalGrammarAction:a})}invokeRuleCatch(t,a,u){const d=this.RULE_STACK.length===1,f=a&&!this.isBackTracking()&&this.recoveryEnabled;if(_r(t)){const A=t;if(f){const k=this.findReSyncTokenType();if(this.isInCurrentRuleReSyncSet(k))if(A.resyncedTokens=this.reSyncTo(k),this.outputCst){const N=this.CST_STACK[this.CST_STACK.length-1];return N.recoveredNode=!0,N}else return u(t);else{if(this.outputCst){const N=this.CST_STACK[this.CST_STACK.length-1];N.recoveredNode=!0,A.partialCstResult=N}throw A}}else{if(d)return this.moveToTerminatedState(),u(t);throw A}}else throw t}optionInternal(t,a){const u=this.getKeyForAutomaticLookahead(Ri,a);return this.optionInternalLogic(t,a,u)}optionInternalLogic(t,a,u){let d=this.getLaFuncFromCache(u),f;if(typeof t!="function"){f=t.DEF;const A=t.GATE;if(A!==void 0){const k=d;d=()=>A.call(this)&&k.call(this)}}else f=t;if(d.call(this)===!0)return f.call(this)}atLeastOneInternal(t,a){const u=this.getKeyForAutomaticLookahead(Cr,t);return this.atLeastOneInternalLogic(t,a,u)}atLeastOneInternalLogic(t,a,u){let d=this.getLaFuncFromCache(u),f;if(typeof a!="function"){f=a.DEF;const A=a.GATE;if(A!==void 0){const k=d;d=()=>A.call(this)&&k.call(this)}}else f=a;if(d.call(this)===!0){let A=this.doSingleRepetition(f);for(;d.call(this)===!0&&A===!0;)A=this.doSingleRepetition(f)}else throw this.raiseEarlyExitException(t,Xe.REPETITION_MANDATORY,a.ERR_MSG);this.attemptInRepetitionRecovery(this.atLeastOneInternal,[t,a],d,Cr,t,Ui)}atLeastOneSepFirstInternal(t,a){const u=this.getKeyForAutomaticLookahead(xr,t);this.atLeastOneSepFirstInternalLogic(t,a,u)}atLeastOneSepFirstInternalLogic(t,a,u){const d=a.DEF,f=a.SEP;if(this.getLaFuncFromCache(u).call(this)===!0){d.call(this);const k=()=>this.tokenMatcher(this.LA(1),f);for(;this.tokenMatcher(this.LA(1),f)===!0;)this.CONSUME(f),d.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,f,k,d,Xt],k,xr,t,Xt)}else throw this.raiseEarlyExitException(t,Xe.REPETITION_MANDATORY_WITH_SEPARATOR,a.ERR_MSG)}manyInternal(t,a){const u=this.getKeyForAutomaticLookahead(sr,t);return this.manyInternalLogic(t,a,u)}manyInternalLogic(t,a,u){let d=this.getLaFuncFromCache(u),f;if(typeof a!="function"){f=a.DEF;const k=a.GATE;if(k!==void 0){const N=d;d=()=>k.call(this)&&N.call(this)}}else f=a;let A=!0;for(;d.call(this)===!0&&A===!0;)A=this.doSingleRepetition(f);this.attemptInRepetitionRecovery(this.manyInternal,[t,a],d,sr,t,Fi,A)}manySepFirstInternal(t,a){const u=this.getKeyForAutomaticLookahead(kr,t);this.manySepFirstInternalLogic(t,a,u)}manySepFirstInternalLogic(t,a,u){const d=a.DEF,f=a.SEP;if(this.getLaFuncFromCache(u).call(this)===!0){d.call(this);const k=()=>this.tokenMatcher(this.LA(1),f);for(;this.tokenMatcher(this.LA(1),f)===!0;)this.CONSUME(f),d.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,f,k,d,ni],k,kr,t,ni)}}repetitionSepSecondInternal(t,a,u,d,f){for(;u();)this.CONSUME(a),d.call(this);this.attemptInRepetitionRecovery(this.repetitionSepSecondInternal,[t,a,u,d,f],u,xr,t,f)}doSingleRepetition(t){const a=this.getLexerPosition();return t.call(this),this.getLexerPosition()>a}orInternal(t,a){const u=this.getKeyForAutomaticLookahead(vi,a),d=(0,qe.Z)(t)?t:t.DEF,A=this.getLaFuncFromCache(u).call(this,d);if(A!==void 0)return d[A].ALT.call(this);this.raiseNoAltException(a,t.ERR_MSG)}ruleFinallyStateUpdate(){if(this.RULE_STACK.pop(),this.RULE_OCCURRENCE_STACK.pop(),this.cstFinallyStateUpdate(),this.RULE_STACK.length===0&&this.isAtEndOfInput()===!1){const t=this.LA(1),a=this.errorMessageProvider.buildNotAllInputParsedMessage({firstRedundant:t,ruleName:this.getCurrRuleFullName()});this.SAVE_ERROR(new ia(a,t))}}subruleInternal(t,a,u){let d;try{const f=u!==void 0?u.ARGS:void 0;return this.subruleIdx=a,d=t.apply(this,f),this.cstPostNonTerminal(d,u!==void 0&&u.LABEL!==void 0?u.LABEL:t.ruleName),d}catch(f){throw this.subruleInternalError(f,u,t.ruleName)}}subruleInternalError(t,a,u){throw _r(t)&&t.partialCstResult!==void 0&&(this.cstPostNonTerminal(t.partialCstResult,a!==void 0&&a.LABEL!==void 0?a.LABEL:u),delete t.partialCstResult),t}consumeInternal(t,a,u){let d;try{const f=this.LA(1);this.tokenMatcher(f,t)===!0?(this.consumeToken(),d=f):this.consumeInternalError(t,f,u)}catch(f){d=this.consumeInternalRecovery(t,a,f)}return this.cstPostTerminal(u!==void 0&&u.LABEL!==void 0?u.LABEL:t.name,d),d}consumeInternalError(t,a,u){let d;const f=this.LA(0);throw u!==void 0&&u.ERR_MSG?d=u.ERR_MSG:d=this.errorMessageProvider.buildMismatchTokenMessage({expected:t,actual:a,previous:f,ruleName:this.getCurrRuleFullName()}),this.SAVE_ERROR(new Ir(d,a,f))}consumeInternalRecovery(t,a,u){if(this.recoveryEnabled&&u.name==="MismatchedTokenException"&&!this.isBackTracking()){const d=this.getFollowsForInRuleRecovery(t,a);try{return this.tryInRuleRecovery(t,d)}catch(f){throw f.name===ts?u:f}}else throw u}saveRecogState(){const t=this.errors,a=(0,b.Z)(this.RULE_STACK);return{errors:t,lexerState:this.exportLexerState(),RULE_STACK:a,CST_STACK:this.CST_STACK}}reloadRecogState(t){this.errors=t.errors,this.importLexerState(t.lexerState),this.RULE_STACK=t.RULE_STACK}ruleInvocationStateUpdate(t,a,u){this.RULE_OCCURRENCE_STACK.push(u),this.RULE_STACK.push(t),this.cstInvocationStateUpdate(a)}isBackTracking(){return this.isBackTrackingStack.length!==0}getCurrRuleFullName(){const t=this.getLastExplicitRuleShortName();return this.shortRuleNameToFull[t]}shortRuleNameToFullName(t){return this.shortRuleNameToFull[t]}isAtEndOfInput(){return this.tokenMatcher(this.LA(1),sn)}reset(){this.resetLexerState(),this.subruleIdx=0,this.isBackTrackingStack=[],this.errors=[],this.RULE_STACK=[],this.CST_STACK=[],this.RULE_OCCURRENCE_STACK=[]}}class tt{initErrorHandler(t){this._errors=[],this.errorMessageProvider=(0,L.Z)(t,"errorMessageProvider")?t.errorMessageProvider:ln.errorMessageProvider}SAVE_ERROR(t){if(_r(t))return t.context={ruleStack:this.getHumanReadableRuleStack(),ruleOccurrenceStack:(0,b.Z)(this.RULE_OCCURRENCE_STACK)},this._errors.push(t),t;throw Error("Trying to save an Error which is not a RecognitionException")}get errors(){return(0,b.Z)(this._errors)}set errors(t){this._errors=t}raiseEarlyExitException(t,a,u){const d=this.getCurrRuleFullName(),f=this.getGAstProductions()[d],k=an(t,f,a,this.maxLookahead)[0],N=[];for(let q=1;q<=this.maxLookahead;q++)N.push(this.LA(q));const G=this.errorMessageProvider.buildEarlyExitMessage({expectedIterationPaths:k,actual:N,previous:this.LA(0),customUserDescription:u,ruleName:d});throw this.SAVE_ERROR(new aa(G,this.LA(1),this.LA(0)))}raiseNoAltException(t,a){const u=this.getCurrRuleFullName(),d=this.getGAstProductions()[u],f=Ar(t,d,this.maxLookahead),A=[];for(let G=1;G<=this.maxLookahead;G++)A.push(this.LA(G));const k=this.LA(0),N=this.errorMessageProvider.buildNoViableAltMessage({expectedPathsPerAlt:f,actual:A,previous:k,customUserDescription:a,ruleName:this.getCurrRuleFullName()});throw this.SAVE_ERROR(new sa(N,this.LA(1),k))}}class Mt{initContentAssist(){}computeContentAssist(t,a){const u=this.gastProductionsCache[t];if((0,it.Z)(u))throw Error(`Rule ->${t}<- does not exist in this grammar.`);return rr([u],a,this.tokenMatcher,this.maxLookahead)}getNextPossibleTokenTypes(t){const a=Nt(t.ruleStack),d=this.getGAstProductions()[a];return new Zi(d,t).startWalking()}}const Ai={description:"This Object indicates the Parser is during Recording Phase"};Object.freeze(Ai);const Ra=!0,Ea=Math.pow(2,Ut)-1,Aa=Xs({name:"RECORDING_PHASE_TOKEN",pattern:At.NA});kn([Aa]);const _a=Kr(Aa,`This IToken indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,-1,-1,-1,-1,-1,-1);Object.freeze(_a);const wa={name:`This CSTNode indicates the Parser is in Recording Phase
	See: https://chevrotain.io/docs/guide/internals.html#grammar-recording for details`,children:{}};class La{initGastRecorder(t){this.recordingProdStack=[],this.RECORDING_PHASE=!1}enableRecording(){this.RECORDING_PHASE=!0,this.TRACE_INIT("Enable Recording",()=>{for(let t=0;t<10;t++){const a=t>0?t:"";this[`CONSUME${a}`]=function(u,d){return this.consumeInternalRecord(u,t,d)},this[`SUBRULE${a}`]=function(u,d){return this.subruleInternalRecord(u,t,d)},this[`OPTION${a}`]=function(u){return this.optionInternalRecord(u,t)},this[`OR${a}`]=function(u){return this.orInternalRecord(u,t)},this[`MANY${a}`]=function(u){this.manyInternalRecord(t,u)},this[`MANY_SEP${a}`]=function(u){this.manySepFirstInternalRecord(t,u)},this[`AT_LEAST_ONE${a}`]=function(u){this.atLeastOneInternalRecord(t,u)},this[`AT_LEAST_ONE_SEP${a}`]=function(u){this.atLeastOneSepFirstInternalRecord(t,u)}}this.consume=function(t,a,u){return this.consumeInternalRecord(a,t,u)},this.subrule=function(t,a,u){return this.subruleInternalRecord(a,t,u)},this.option=function(t,a){return this.optionInternalRecord(a,t)},this.or=function(t,a){return this.orInternalRecord(a,t)},this.many=function(t,a){this.manyInternalRecord(t,a)},this.atLeastOne=function(t,a){this.atLeastOneInternalRecord(t,a)},this.ACTION=this.ACTION_RECORD,this.BACKTRACK=this.BACKTRACK_RECORD,this.LA=this.LA_RECORD})}disableRecording(){this.RECORDING_PHASE=!1,this.TRACE_INIT("Deleting Recording methods",()=>{const t=this;for(let a=0;a<10;a++){const u=a>0?a:"";delete t[`CONSUME${u}`],delete t[`SUBRULE${u}`],delete t[`OPTION${u}`],delete t[`OR${u}`],delete t[`MANY${u}`],delete t[`MANY_SEP${u}`],delete t[`AT_LEAST_ONE${u}`],delete t[`AT_LEAST_ONE_SEP${u}`]}delete t.consume,delete t.subrule,delete t.option,delete t.or,delete t.many,delete t.atLeastOne,delete t.ACTION,delete t.BACKTRACK,delete t.LA})}ACTION_RECORD(t){}BACKTRACK_RECORD(t,a){return()=>!0}LA_RECORD(t){return $i}topLevelRuleRecord(t,a){try{const u=new ze({definition:[],name:t});return u.name=t,this.recordingProdStack.push(u),a.call(this),this.recordingProdStack.pop(),u}catch(u){if(u.KNOWN_RECORDER_ERROR!==!0)try{u.message=u.message+`
	 This error was thrown during the "grammar recording phase" For more info see:
	https://chevrotain.io/docs/guide/internals.html#grammar-recording`}catch(d){throw u}throw u}}optionInternalRecord(t,a){return is.call(this,I,t,a)}atLeastOneInternalRecord(t,a){is.call(this,W,a,t)}atLeastOneSepFirstInternalRecord(t,a){is.call(this,z,a,t,Ra)}manyInternalRecord(t,a){is.call(this,le,a,t)}manySepFirstInternalRecord(t,a){is.call(this,pe,a,t,Ra)}orInternalRecord(t,a){return Oa.call(this,t,a)}subruleInternalRecord(t,a,u){if(_i(a),!t||(0,L.Z)(t,"ruleName")===!1){const k=new Error(`<SUBRULE${$a(a)}> argument is invalid expecting a Parser method reference but got: <${JSON.stringify(t)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw k.KNOWN_RECORDER_ERROR=!0,k}const d=(0,nn.Z)(this.recordingProdStack),f=t.ruleName,A=new He({idx:a,nonTerminalName:f,label:u==null?void 0:u.LABEL,referencedRule:void 0});return d.definition.push(A),this.outputCst?wa:Ai}consumeInternalRecord(t,a,u){if(_i(a),!Bs(t)){const A=new Error(`<CONSUME${$a(a)}> argument is invalid expecting a TokenType reference but got: <${JSON.stringify(t)}>
 inside top level rule: <${this.recordingProdStack[0].name}>`);throw A.KNOWN_RECORDER_ERROR=!0,A}const d=(0,nn.Z)(this.recordingProdStack),f=new me({idx:a,terminalType:t,label:u==null?void 0:u.LABEL});return d.definition.push(f),_a}}function is(o,t,a,u=!1){_i(a);const d=(0,nn.Z)(this.recordingProdStack),f=(0,Ce.Z)(t)?t:t.DEF,A=new o({definition:[],idx:a});return u&&(A.separator=t.SEP),(0,L.Z)(t,"MAX_LOOKAHEAD")&&(A.maxLookahead=t.MAX_LOOKAHEAD),this.recordingProdStack.push(A),f.call(this),d.definition.push(A),this.recordingProdStack.pop(),Ai}function Oa(o,t){_i(t);const a=(0,nn.Z)(this.recordingProdStack),u=(0,qe.Z)(o)===!1,d=u===!1?o:o.DEF,f=new Ie({definition:[],idx:t,ignoreAmbiguities:u&&o.IGNORE_AMBIGUITIES===!0});(0,L.Z)(o,"MAX_LOOKAHEAD")&&(f.maxLookahead=o.MAX_LOOKAHEAD);const A=ft(d,k=>(0,Ce.Z)(k.GATE));return f.hasPredicates=A,a.definition.push(f),(0,y.Z)(d,k=>{const N=new Ze({definition:[]});f.definition.push(N),(0,L.Z)(k,"IGNORE_AMBIGUITIES")?N.ignoreAmbiguities=k.IGNORE_AMBIGUITIES:(0,L.Z)(k,"GATE")&&(N.ignoreAmbiguities=!0),this.recordingProdStack.push(N),k.ALT.call(this),this.recordingProdStack.pop()}),Ai}function $a(o){return o===0?"":`${o}`}function _i(o){if(o<0||o>Ea){const t=new Error(`Invalid DSL Method idx value: <${o}>
	Idx value must be a none negative value smaller than ${Ea+1}`);throw t.KNOWN_RECORDER_ERROR=!0,t}}class Pa{initPerformanceTracer(t){if((0,L.Z)(t,"traceInitPerf")){const a=t.traceInitPerf,u=typeof a=="number";this.traceInitMaxIdent=u?a:1/0,this.traceInitPerf=u?a>0:a}else this.traceInitMaxIdent=0,this.traceInitPerf=ln.traceInitPerf;this.traceInitIndent=-1}TRACE_INIT(t,a){if(this.traceInitPerf===!0){this.traceInitIndent++;const u=new Array(this.traceInitIndent+1).join("	");this.traceInitIndent<this.traceInitMaxIdent&&console.log(`${u}--> <${t}>`);const{time:d,value:f}=gr(a),A=d>10?console.warn:console.log;return this.traceInitIndent<this.traceInitMaxIdent&&A(`${u}<-- <${t}> time: ${d}ms`),this.traceInitIndent--,f}else return a()}}function Ma(o,t){t.forEach(a=>{const u=a.prototype;Object.getOwnPropertyNames(u).forEach(d=>{if(d==="constructor")return;const f=Object.getOwnPropertyDescriptor(u,d);f&&(f.get||f.set)?Object.defineProperty(o.prototype,d,f):o.prototype[d]=a.prototype[d]})})}const $i=Kr(sn,"",NaN,NaN,NaN,NaN,NaN,NaN);Object.freeze($i);const ln=Object.freeze({recoveryEnabled:!1,maxLookahead:3,dynamicTokensEnabled:!1,outputCst:!0,errorMessageProvider:Ke,nodeLocationTracking:"none",traceInitPerf:!1,skipValidations:!1}),Ii=Object.freeze({recoveryValueFunc:()=>{},resyncEnabled:!0});var $t;(function(o){o[o.INVALID_RULE_NAME=0]="INVALID_RULE_NAME",o[o.DUPLICATE_RULE_NAME=1]="DUPLICATE_RULE_NAME",o[o.INVALID_RULE_OVERRIDE=2]="INVALID_RULE_OVERRIDE",o[o.DUPLICATE_PRODUCTIONS=3]="DUPLICATE_PRODUCTIONS",o[o.UNRESOLVED_SUBRULE_REF=4]="UNRESOLVED_SUBRULE_REF",o[o.LEFT_RECURSION=5]="LEFT_RECURSION",o[o.NONE_LAST_EMPTY_ALT=6]="NONE_LAST_EMPTY_ALT",o[o.AMBIGUOUS_ALTS=7]="AMBIGUOUS_ALTS",o[o.CONFLICT_TOKENS_RULES_NAMESPACE=8]="CONFLICT_TOKENS_RULES_NAMESPACE",o[o.INVALID_TOKEN_NAME=9]="INVALID_TOKEN_NAME",o[o.NO_NON_EMPTY_LOOKAHEAD=10]="NO_NON_EMPTY_LOOKAHEAD",o[o.AMBIGUOUS_PREFIX_ALTS=11]="AMBIGUOUS_PREFIX_ALTS",o[o.TOO_MANY_ALTS=12]="TOO_MANY_ALTS",o[o.CUSTOM_LOOKAHEAD_VALIDATION=13]="CUSTOM_LOOKAHEAD_VALIDATION"})($t||($t={}));function ba(o=void 0){return function(){return o}}class as{static performSelfAnalysis(t){throw Error("The **static** `performSelfAnalysis` method has been deprecated.	\nUse the **instance** method with the same name instead.")}performSelfAnalysis(){this.TRACE_INIT("performSelfAnalysis",()=>{let t;this.selfAnalysisDone=!0;const a=this.className;this.TRACE_INIT("toFastProps",()=>{V(this)}),this.TRACE_INIT("Grammar Recording",()=>{try{this.enableRecording(),(0,y.Z)(this.definedRulesNames,d=>{const A=this[d].originalGrammarAction;let k;this.TRACE_INIT(`${d} Rule`,()=>{k=this.topLevelRuleRecord(d,A)}),this.gastProductionsCache[d]=k})}finally{this.disableRecording()}});let u=[];if(this.TRACE_INIT("Grammar Resolving",()=>{u=na({rules:(0,O.Z)(this.gastProductionsCache)}),this.definitionErrors=this.definitionErrors.concat(u)}),this.TRACE_INIT("Grammar Validations",()=>{if((0,F.Z)(u)&&this.skipValidations===!1){const d=ra({rules:(0,O.Z)(this.gastProductionsCache),tokenTypes:(0,O.Z)(this.tokensMap),errMsgProvider:hn,grammarName:a}),f=Vi({lookaheadStrategy:this.lookaheadStrategy,rules:(0,O.Z)(this.gastProductionsCache),tokenTypes:(0,O.Z)(this.tokensMap),grammarName:a});this.definitionErrors=this.definitionErrors.concat(d,f)}}),(0,F.Z)(this.definitionErrors)&&(this.recoveryEnabled&&this.TRACE_INIT("computeAllProdsFollows",()=>{const d=Wn((0,O.Z)(this.gastProductionsCache));this.resyncFollows=d}),this.TRACE_INIT("ComputeLookaheadFunctions",()=>{var d,f;(f=(d=this.lookaheadStrategy).initialize)===null||f===void 0||f.call(d,{rules:(0,O.Z)(this.gastProductionsCache)}),this.preComputeLookaheadFunctions((0,O.Z)(this.gastProductionsCache))})),!as.DEFER_DEFINITION_ERRORS_HANDLING&&!(0,F.Z)(this.definitionErrors))throw t=(0,x.Z)(this.definitionErrors,d=>d.message),new Error(`Parser Definition Errors detected:
 ${t.join(`
-------------------------------
`)}`)})}constructor(t,a){this.definitionErrors=[],this.selfAnalysisDone=!1;const u=this;if(u.initErrorHandler(a),u.initLexerAdapter(),u.initLooksAhead(a),u.initRecognizerEngine(t,a),u.initRecoverable(a),u.initTreeBuilder(a),u.initContentAssist(),u.initGastRecorder(a),u.initPerformanceTracer(a),(0,L.Z)(a,"ignoredIssues"))throw new Error(`The <ignoredIssues> IParserConfig property has been deprecated.
	Please use the <IGNORE_AMBIGUITIES> flag on the relevant DSL method instead.
	See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#IGNORING_AMBIGUITIES
	For further details.`);this.skipValidations=(0,L.Z)(a,"skipValidations")?a.skipValidations:ln.skipValidations}}as.DEFER_DEFINITION_ERRORS_HANDLING=!1,Ma(as,[yi,va,ue,ne,Ve,X,tt,Mt,La,Pa]);class Wa extends null{constructor(t,a=ln){const u=clone(a);u.outputCst=!0,super(t,u)}}class Da extends as{constructor(t,a=ln){const u=(0,b.Z)(a);u.outputCst=!1,super(t,u)}}const Za={includeVisitorInterface:!0,visitorInterfaceName:"ICstNodeVisitor"};function Ka(o,t){const a=Object.assign(Object.assign({},Za),t),u=buildModel(o);return genDts(u,a)}function Va(){console.warn(`The clearCache function was 'soft' removed from the Chevrotain API.
	 It performs no action other than printing this message.
	 Please avoid using it as it will be completely removed in the future`)}class ja{constructor(){throw new Error(`The Parser class has been deprecated, use CstParser or EmbeddedActionsParser instead.	
See: https://chevrotain.io/docs/changes/BREAKING_CHANGES.html#_7-0-0`)}}},79837:function(Te,j,g){g.d(j,{Q:function(){return da},T:function(){return Ei}});var y=g(45785),O=g(25733),F=g(13161),x=g(71997);function L(i){const e=[],n=i.Grammar;for(const r of n.rules)(0,x.MS)(r)&&(0,O.md)(r)&&(0,F.Rn)((0,O.s1)(r))&&e.push(r.name);return{multilineCommentRules:e,nameRegexp:y.uz}}var b=g(76597),V=g(857),B=g(97175);function R(i,e,n){return`${i.name}_${e}_${n}`}const T=0,E=1,_=2,v=4,p=5,C=6,h=7,S=8,w=9,Z=10,P=11,Q=12;class J{constructor(e){this.target=e}isEpsilon(){return!1}}class ve extends J{constructor(e,n){super(e),this.tokenType=n}}class K extends J{constructor(e){super(e)}isEpsilon(){return!0}}class ae extends J{constructor(e,n,r){super(e),this.rule=n,this.followState=r}isEpsilon(){return!0}}function ce(i){const e={decisionMap:{},decisionStates:[],ruleToStartState:new Map,ruleToStopState:new Map,states:[]};ye(e,i);const n=i.length;for(let r=0;r<n;r++){const s=i[r],l=re(e,s,s);l!==void 0&&I(e,s,l)}return e}function ye(i,e){const n=e.length;for(let r=0;r<n;r++){const s=e[r],l=z(i,s,void 0,{type:_}),c=z(i,s,void 0,{type:h});l.stop=c,i.ruleToStartState.set(s,l),i.ruleToStopState.set(s,c)}}function Ae(i,e,n){return n instanceof b.oI?ze(i,e,n.terminalType,n):n instanceof b.Sj?Ze(i,e,n):n instanceof b.ue?Y(i,e,n):n instanceof b.Wx?se(i,e,n):n instanceof b.hI?Se(i,e,n):n instanceof b.pT?Je(i,e,n):n instanceof b.ej?Ge(i,e,n):n instanceof b.fK?oe(i,e,n):re(i,e,n)}function Se(i,e,n){const r=z(i,e,n,{type:p});Me(i,r);const s=je(i,e,r,n,re(i,e,n));return ge(i,e,n,s)}function Je(i,e,n){const r=z(i,e,n,{type:p});Me(i,r);const s=je(i,e,r,n,re(i,e,n)),l=ze(i,e,n.separator,n);return ge(i,e,n,s,l)}function Ge(i,e,n){const r=z(i,e,n,{type:v});Me(i,r);const s=je(i,e,r,n,re(i,e,n));return ke(i,e,n,s)}function oe(i,e,n){const r=z(i,e,n,{type:v});Me(i,r);const s=je(i,e,r,n,re(i,e,n)),l=ze(i,e,n.separator,n);return ke(i,e,n,s,l)}function Y(i,e,n){const r=z(i,e,n,{type:E});Me(i,r);const s=(0,V.Z)(n.definition,c=>Ae(i,e,c));return je(i,e,r,n,...s)}function se(i,e,n){const r=z(i,e,n,{type:E});Me(i,r);const s=je(i,e,r,n,re(i,e,n));return he(i,e,n,s)}function re(i,e,n){const r=(0,B.Z)((0,V.Z)(n.definition,s=>Ae(i,e,s)),s=>s!==void 0);return r.length===1?r[0]:r.length===0?void 0:He(i,r)}function ke(i,e,n,r,s){const l=r.left,c=r.right,m=z(i,e,n,{type:P});Me(i,m);const $=z(i,e,n,{type:Q});return l.loopback=m,$.loopback=m,i.decisionMap[R(e,s?"RepetitionMandatoryWithSeparator":"RepetitionMandatory",n.idx)]=m,W(c,m),s===void 0?(W(m,l),W(m,$)):(W(m,$),W(m,s.left),W(s.right,l)),{left:l,right:$}}function ge(i,e,n,r,s){const l=r.left,c=r.right,m=z(i,e,n,{type:Z});Me(i,m);const $=z(i,e,n,{type:Q}),U=z(i,e,n,{type:w});return m.loopback=U,$.loopback=U,W(m,l),W(m,$),W(c,U),s!==void 0?(W(U,$),W(U,s.left),W(s.right,l)):W(U,m),i.decisionMap[R(e,s?"RepetitionWithSeparator":"Repetition",n.idx)]=m,{left:m,right:$}}function he(i,e,n,r){const s=r.left,l=r.right;return W(s,l),i.decisionMap[R(e,"Option",n.idx)]=s,r}function Me(i,e){return i.decisionStates.push(e),e.decision=i.decisionStates.length-1,e.decision}function je(i,e,n,r,...s){const l=z(i,e,r,{type:S,start:n});n.end=l;for(const m of s)m!==void 0?(W(n,m.left),W(m.right,l)):W(n,l);const c={left:n,right:l};return i.decisionMap[R(e,$e(r),r.idx)]=n,c}function $e(i){if(i instanceof b.ue)return"Alternation";if(i instanceof b.Wx)return"Option";if(i instanceof b.hI)return"Repetition";if(i instanceof b.pT)return"RepetitionWithSeparator";if(i instanceof b.ej)return"RepetitionMandatory";if(i instanceof b.fK)return"RepetitionMandatoryWithSeparator";throw new Error("Invalid production type encountered")}function He(i,e){const n=e.length;for(let l=0;l<n-1;l++){const c=e[l];let m;c.left.transitions.length===1&&(m=c.left.transitions[0]);const $=m instanceof ae,U=m,H=e[l+1].left;c.left.type===E&&c.right.type===E&&m!==void 0&&($&&U.followState===c.right||m.target===c.right)?($?U.followState=H:m.target=H,pe(i,c.right)):W(c.right,H)}const r=e[0],s=e[n-1];return{left:r.left,right:s.right}}function ze(i,e,n,r){const s=z(i,e,r,{type:E}),l=z(i,e,r,{type:E});return le(s,new ve(l,n)),{left:s,right:l}}function Ze(i,e,n){const r=n.referencedRule,s=i.ruleToStartState.get(r),l=z(i,e,n,{type:E}),c=z(i,e,n,{type:E}),m=new ae(s,r,c);return le(l,m),{left:l,right:c}}function I(i,e,n){const r=i.ruleToStartState.get(e);W(r,n.left);const s=i.ruleToStopState.get(e);return W(n.right,s),{left:r,right:s}}function W(i,e){const n=new K(e);le(i,n)}function z(i,e,n,r){const s=Object.assign({atn:i,production:n,epsilonOnlyTransitions:!1,rule:e,transitions:[],nextTokenWithinRule:[],stateNumber:i.states.length},r);return i.states.push(s),s}function le(i,e){i.transitions.length===0&&(i.epsilonOnlyTransitions=e.isEpsilon()),i.transitions.push(e)}function pe(i,e){i.states.splice(i.states.indexOf(e),1)}const Ie={};class me{constructor(){this.map={},this.configs=[]}get size(){return this.configs.length}finalize(){this.map={}}add(e){const n=vt(e);n in this.map||(this.map[n]=this.configs.length,this.configs.push(e))}get elements(){return this.configs}get alts(){return(0,V.Z)(this.configs,e=>e.alt)}get key(){let e="";for(const n in this.map)e+=n+":";return e}}function vt(i,e=!0){return`${e?`a${i.alt}`:""}s${i.state.stateNumber}:${i.stack.map(n=>n.stateNumber.toString()).join("_")}`}var at=g(93422),Zt=g(16571),It=g(18458),lt=g(57031);function Ct(i,e){return i&&i.length?(0,lt.Z)(i,(0,It.Z)(e,2)):[]}var cn=Ct,Qe=g(42060),gn=g(24285),ct=g(67127),bn=g(6613);function kt(i,e){const n={};return r=>{const s=r.toString();let l=n[s];return l!==void 0||(l={atnStartState:i,decision:e,states:{}},n[s]=l),l}}class qe{constructor(){this.predicates=[]}is(e){return e>=this.predicates.length||this.predicates[e]}set(e,n){this.predicates[e]=n}toString(){let e="";const n=this.predicates.length;for(let r=0;r<n;r++)e+=this.predicates[r]===!0?"1":"0";return e}}const dt=new qe;class Dn extends b.dV{constructor(e){var n;super(),this.logging=(n=e==null?void 0:e.logging)!==null&&n!==void 0?n:r=>console.log(r)}initialize(e){this.atn=ce(e.rules),this.dfas=yn(this.atn)}validateAmbiguousAlternationAlternatives(){return[]}validateEmptyOrAlternatives(){return[]}buildLookaheadForAlternation(e){const{prodOccurrence:n,rule:r,hasPredicates:s,dynamicTokensEnabled:l}=e,c=this.dfas,m=this.logging,$=R(r,"Alternation",n),H=this.atn.decisionMap[$].decision,ue=(0,V.Z)((0,b.oC)({maxLookahead:1,occurrence:n,prodType:"Alternation",rule:r}),ne=>(0,V.Z)(ne,X=>X[0]));if(ft(ue,!1)&&!l){const ne=(0,bn.Z)(ue,(X,De,Ve)=>((0,gn.Z)(De,tt=>{tt&&(X[tt.tokenTypeIdx]=Ve,(0,gn.Z)(tt.categoryMatches,Mt=>{X[Mt]=Ve}))}),X),{});return s?function(X){var De;const Ve=this.LA(1),tt=ne[Ve.tokenTypeIdx];if(X!==void 0&&tt!==void 0){const Mt=(De=X[tt])===null||De===void 0?void 0:De.GATE;if(Mt!==void 0&&Mt.call(this)===!1)return}return tt}:function(){const X=this.LA(1);return ne[X.tokenTypeIdx]}}else return s?function(ne){const X=new qe,De=ne===void 0?0:ne.length;for(let tt=0;tt<De;tt++){const Mt=ne==null?void 0:ne[tt].GATE;X.set(tt,Mt===void 0||Mt.call(this))}const Ve=ht.call(this,c,H,X,m);return typeof Ve=="number"?Ve:void 0}:function(){const ne=ht.call(this,c,H,dt,m);return typeof ne=="number"?ne:void 0}}buildLookaheadForOptional(e){const{prodOccurrence:n,rule:r,prodType:s,dynamicTokensEnabled:l}=e,c=this.dfas,m=this.logging,$=R(r,s,n),H=this.atn.decisionMap[$].decision,ue=(0,V.Z)((0,b.oC)({maxLookahead:1,occurrence:n,prodType:s,rule:r}),ne=>(0,V.Z)(ne,X=>X[0]));if(ft(ue)&&ue[0][0]&&!l){const ne=ue[0],X=(0,Qe.Z)(ne);if(X.length===1&&(0,ct.Z)(X[0].categoryMatches)){const Ve=X[0].tokenTypeIdx;return function(){return this.LA(1).tokenTypeIdx===Ve}}else{const De=(0,bn.Z)(X,(Ve,tt)=>(tt!==void 0&&(Ve[tt.tokenTypeIdx]=!0,(0,gn.Z)(tt.categoryMatches,Mt=>{Ve[Mt]=!0})),Ve),{});return function(){const Ve=this.LA(1);return De[Ve.tokenTypeIdx]===!0}}}return function(){const ne=ht.call(this,c,H,dt,m);return typeof ne=="object"?!1:ne===0}}}function ft(i,e=!0){const n=new Set;for(const r of i){const s=new Set;for(const l of r){if(l===void 0){if(e)break;return!1}const c=[l.tokenTypeIdx].concat(l.categoryMatches);for(const m of c)if(n.has(m)){if(!s.has(m))return!1}else n.add(m),s.add(m)}}return!0}function yn(i){const e=i.decisionStates.length,n=Array(e);for(let r=0;r<e;r++)n[r]=kt(i.decisionStates[r],r);return n}function ht(i,e,n,r){const s=i[e](n);let l=s.start;if(l===void 0){const m=Kt(s.atnStartState);l=Ft(s,Tn(m)),s.start=l}return Zn.apply(this,[s,l,n,r])}function Zn(i,e,n,r){let s=e,l=1;const c=[];let m=this.LA(l++);for(;;){let $=Gn(s,m);if($===void 0&&($=Ye.apply(this,[i,s,m,l,n,r])),$===Ie)return Wt(c,s,m);if($.isAcceptState===!0)return $.prediction;s=$,c.push(m),m=this.LA(l++)}}function Ye(i,e,n,r,s,l){const c=nt(e.configs,n,s);if(c.size===0)return rt(i,e,n,Ie),Ie;let m=Tn(c);const $=xt(c,s);if($!==void 0)m.isAcceptState=!0,m.prediction=$,m.configs.uniqueAlt=$;else if(Wn(c)){const U=(0,at.Z)(c.alts);m.isAcceptState=!0,m.prediction=U,m.configs.uniqueAlt=U,Fn.apply(this,[i,r,c.alts,l])}return m=rt(i,e,n,m),m}function Fn(i,e,n,r){const s=[];for(let U=1;U<=e;U++)s.push(this.LA(U).tokenType);const l=i.atnStartState,c=l.rule,m=l.production,$=Bt({topLevelRule:c,ambiguityIndices:n,production:m,prefixPath:s});r($)}function Bt(i){const e=(0,V.Z)(i.prefixPath,s=>(0,b.l$)(s)).join(", "),n=i.production.idx===0?"":i.production.idx;let r=`Ambiguous Alternatives Detected: <${i.ambiguityIndices.join(", ")}> in <${Un(i.production)}${n}> inside <${i.topLevelRule.name}> Rule,
<${e}> may appears as a prefix path in all these alternatives.
`;return r=r+`See: https://chevrotain.io/docs/guide/resolving_grammar_errors.html#AMBIGUOUS_ALTERNATIVES
For Further details.`,r}function Un(i){if(i instanceof b.Sj)return"SUBRULE";if(i instanceof b.Wx)return"OPTION";if(i instanceof b.ue)return"OR";if(i instanceof b.ej)return"AT_LEAST_ONE";if(i instanceof b.fK)return"AT_LEAST_ONE_SEP";if(i instanceof b.pT)return"MANY_SEP";if(i instanceof b.hI)return"MANY";if(i instanceof b.oI)return"CONSUME";throw Error("non exhaustive match")}function Wt(i,e,n){const r=(0,Zt.Z)(e.configs.elements,l=>l.state.transitions),s=cn(r.filter(l=>l instanceof ve).map(l=>l.tokenType),l=>l.tokenTypeIdx);return{actualToken:n,possibleTokenTypes:s,tokenPath:i}}function Gn(i,e){return i.edges[e.tokenTypeIdx]}function nt(i,e,n){const r=new me,s=[];for(const c of i.elements){if(n.is(c.alt)===!1)continue;if(c.state.type===h){s.push(c);continue}const m=c.state.transitions.length;for(let $=0;$<m;$++){const U=c.state.transitions[$],H=Bn(U,e);H!==void 0&&r.add({state:H,alt:c.alt,stack:c.stack})}}let l;if(s.length===0&&r.size===1&&(l=r),l===void 0){l=new me;for(const c of r.elements)Qt(c,l)}if(s.length>0&&!vn(l))for(const c of s)l.add(c);return l}function Bn(i,e){if(i instanceof ve&&(0,b.ol)(e,i.tokenType))return i.target}function xt(i,e){let n;for(const r of i.elements)if(e.is(r.alt)===!0){if(n===void 0)n=r.alt;else if(n!==r.alt)return}return n}function Tn(i){return{configs:i,edges:{},isAcceptState:!1,prediction:-1}}function rt(i,e,n,r){return r=Ft(i,r),e.edges[n.tokenTypeIdx]=r,r}function Ft(i,e){if(e===Ie)return e;const n=e.configs.key,r=i.states[n];return r!==void 0?r:(e.configs.finalize(),i.states[n]=e,e)}function Kt(i){const e=new me,n=i.transitions.length;for(let r=0;r<n;r++){const l={state:i.transitions[r].target,alt:r,stack:[]};Qt(l,e)}return e}function Qt(i,e){const n=i.state;if(n.type===h){if(i.stack.length>0){const s=[...i.stack],c={state:s.pop(),alt:i.alt,stack:s};Qt(c,e)}else e.add(i);return}n.epsilonOnlyTransitions||e.add(i);const r=n.transitions.length;for(let s=0;s<r;s++){const l=n.transitions[s],c=Vt(i,l);c!==void 0&&Qt(c,e)}}function Vt(i,e){if(e instanceof K)return{state:e.target,alt:i.alt,stack:i.stack};if(e instanceof ae){const n=[...i.stack,e.followState];return{state:e.target,alt:i.alt,stack:n}}}function vn(i){for(const e of i.elements)if(e.state.type===h)return!0;return!1}function jt(i){for(const e of i.elements)if(e.state.type!==h)return!1;return!0}function Wn(i){if(jt(i))return!0;const e=Ht(i.elements);return Kn(e)&&!it(e)}function Ht(i){const e=new Map;for(const n of i){const r=vt(n,!1);let s=e.get(r);s===void 0&&(s={},e.set(r,s)),s[n.alt]=!0}return e}function Kn(i){for(const e of Array.from(i.values()))if(Object.keys(e).length>1)return!0;return!1}function it(i){for(const e of Array.from(i.values()))if(Object.keys(e).length===1)return!0;return!1}var be=g(49602),wt;(function(i){function e(n){return typeof n=="string"}i.is=e})(wt||(wt={}));var St;(function(i){function e(n){return typeof n=="string"}i.is=e})(St||(St={}));var ut;(function(i){i.MIN_VALUE=-2147483648,i.MAX_VALUE=2147483647;function e(n){return typeof n=="number"&&i.MIN_VALUE<=n&&n<=i.MAX_VALUE}i.is=e})(ut||(ut={}));var Rt;(function(i){i.MIN_VALUE=0,i.MAX_VALUE=2147483647;function e(n){return typeof n=="number"&&i.MIN_VALUE<=n&&n<=i.MAX_VALUE}i.is=e})(Rt||(Rt={}));var Ne;(function(i){function e(r,s){return r===Number.MAX_VALUE&&(r=Rt.MAX_VALUE),s===Number.MAX_VALUE&&(s=Rt.MAX_VALUE),{line:r,character:s}}i.create=e;function n(r){let s=r;return M.objectLiteral(s)&&M.uinteger(s.line)&&M.uinteger(s.character)}i.is=n})(Ne||(Ne={}));var ee;(function(i){function e(r,s,l,c){if(M.uinteger(r)&&M.uinteger(s)&&M.uinteger(l)&&M.uinteger(c))return{start:Ne.create(r,s),end:Ne.create(l,c)};if(Ne.is(r)&&Ne.is(s))return{start:r,end:s};throw new Error(`Range#create called with invalid arguments[${r}, ${s}, ${l}, ${c}]`)}i.create=e;function n(r){let s=r;return M.objectLiteral(s)&&Ne.is(s.start)&&Ne.is(s.end)}i.is=n})(ee||(ee={}));var D;(function(i){function e(r,s){return{uri:r,range:s}}i.create=e;function n(r){let s=r;return M.objectLiteral(s)&&ee.is(s.range)&&(M.string(s.uri)||M.undefined(s.uri))}i.is=n})(D||(D={}));var xe;(function(i){function e(r,s,l,c){return{targetUri:r,targetRange:s,targetSelectionRange:l,originSelectionRange:c}}i.create=e;function n(r){let s=r;return M.objectLiteral(s)&&ee.is(s.targetRange)&&M.string(s.targetUri)&&ee.is(s.targetSelectionRange)&&(ee.is(s.originSelectionRange)||M.undefined(s.originSelectionRange))}i.is=n})(xe||(xe={}));var Ce;(function(i){function e(r,s,l,c){return{red:r,green:s,blue:l,alpha:c}}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&M.numberRange(s.red,0,1)&&M.numberRange(s.green,0,1)&&M.numberRange(s.blue,0,1)&&M.numberRange(s.alpha,0,1)}i.is=n})(Ce||(Ce={}));var pt;(function(i){function e(r,s){return{range:r,color:s}}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&ee.is(s.range)&&Ce.is(s.color)}i.is=n})(pt||(pt={}));var Et;(function(i){function e(r,s,l){return{label:r,textEdit:s,additionalTextEdits:l}}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&M.string(s.label)&&(M.undefined(s.textEdit)||zt.is(s))&&(M.undefined(s.additionalTextEdits)||M.typedArray(s.additionalTextEdits,zt.is))}i.is=n})(Et||(Et={}));var mt;(function(i){i.Comment="comment",i.Imports="imports",i.Region="region"})(mt||(mt={}));var gt;(function(i){function e(r,s,l,c,m,$){const U={startLine:r,endLine:s};return M.defined(l)&&(U.startCharacter=l),M.defined(c)&&(U.endCharacter=c),M.defined(m)&&(U.kind=m),M.defined($)&&(U.collapsedText=$),U}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&M.uinteger(s.startLine)&&M.uinteger(s.startLine)&&(M.undefined(s.startCharacter)||M.uinteger(s.startCharacter))&&(M.undefined(s.endCharacter)||M.uinteger(s.endCharacter))&&(M.undefined(s.kind)||M.string(s.kind))}i.is=n})(gt||(gt={}));var yt;(function(i){function e(r,s){return{location:r,message:s}}i.create=e;function n(r){let s=r;return M.defined(s)&&D.is(s.location)&&M.string(s.message)}i.is=n})(yt||(yt={}));var ds;(function(i){i.Error=1,i.Warning=2,i.Information=3,i.Hint=4})(ds||(ds={}));var fs;(function(i){i.Unnecessary=1,i.Deprecated=2})(fs||(fs={}));var hs;(function(i){function e(n){const r=n;return M.objectLiteral(r)&&M.string(r.href)}i.is=e})(hs||(hs={}));var ar;(function(i){function e(r,s,l,c,m,$){let U={range:r,message:s};return M.defined(l)&&(U.severity=l),M.defined(c)&&(U.code=c),M.defined(m)&&(U.source=m),M.defined($)&&(U.relatedInformation=$),U}i.create=e;function n(r){var s;let l=r;return M.defined(l)&&ee.is(l.range)&&M.string(l.message)&&(M.number(l.severity)||M.undefined(l.severity))&&(M.integer(l.code)||M.string(l.code)||M.undefined(l.code))&&(M.undefined(l.codeDescription)||M.string((s=l.codeDescription)===null||s===void 0?void 0:s.href))&&(M.string(l.source)||M.undefined(l.source))&&(M.undefined(l.relatedInformation)||M.typedArray(l.relatedInformation,yt.is))}i.is=n})(ar||(ar={}));var Rn;(function(i){function e(r,s,...l){let c={title:r,command:s};return M.defined(l)&&l.length>0&&(c.arguments=l),c}i.create=e;function n(r){let s=r;return M.defined(s)&&M.string(s.title)&&M.string(s.command)}i.is=n})(Rn||(Rn={}));var zt;(function(i){function e(l,c){return{range:l,newText:c}}i.replace=e;function n(l,c){return{range:{start:l,end:l},newText:c}}i.insert=n;function r(l){return{range:l,newText:""}}i.del=r;function s(l){const c=l;return M.objectLiteral(c)&&M.string(c.newText)&&ee.is(c.range)}i.is=s})(zt||(zt={}));var En;(function(i){function e(r,s,l){const c={label:r};return s!==void 0&&(c.needsConfirmation=s),l!==void 0&&(c.description=l),c}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&M.string(s.label)&&(M.boolean(s.needsConfirmation)||s.needsConfirmation===void 0)&&(M.string(s.description)||s.description===void 0)}i.is=n})(En||(En={}));var ot;(function(i){function e(n){const r=n;return M.string(r)}i.is=e})(ot||(ot={}));var qt;(function(i){function e(l,c,m){return{range:l,newText:c,annotationId:m}}i.replace=e;function n(l,c,m){return{range:{start:l,end:l},newText:c,annotationId:m}}i.insert=n;function r(l,c){return{range:l,newText:"",annotationId:c}}i.del=r;function s(l){const c=l;return zt.is(c)&&(En.is(c.annotationId)||ot.is(c.annotationId))}i.is=s})(qt||(qt={}));var Vn;(function(i){function e(r,s){return{textDocument:r,edits:s}}i.create=e;function n(r){let s=r;return M.defined(s)&&fn.is(s.textDocument)&&Array.isArray(s.edits)}i.is=n})(Vn||(Vn={}));var jn;(function(i){function e(r,s,l){let c={kind:"create",uri:r};return s!==void 0&&(s.overwrite!==void 0||s.ignoreIfExists!==void 0)&&(c.options=s),l!==void 0&&(c.annotationId=l),c}i.create=e;function n(r){let s=r;return s&&s.kind==="create"&&M.string(s.uri)&&(s.options===void 0||(s.options.overwrite===void 0||M.boolean(s.options.overwrite))&&(s.options.ignoreIfExists===void 0||M.boolean(s.options.ignoreIfExists)))&&(s.annotationId===void 0||ot.is(s.annotationId))}i.is=n})(jn||(jn={}));var en;(function(i){function e(r,s,l,c){let m={kind:"rename",oldUri:r,newUri:s};return l!==void 0&&(l.overwrite!==void 0||l.ignoreIfExists!==void 0)&&(m.options=l),c!==void 0&&(m.annotationId=c),m}i.create=e;function n(r){let s=r;return s&&s.kind==="rename"&&M.string(s.oldUri)&&M.string(s.newUri)&&(s.options===void 0||(s.options.overwrite===void 0||M.boolean(s.options.overwrite))&&(s.options.ignoreIfExists===void 0||M.boolean(s.options.ignoreIfExists)))&&(s.annotationId===void 0||ot.is(s.annotationId))}i.is=n})(en||(en={}));var Hn;(function(i){function e(r,s,l){let c={kind:"delete",uri:r};return s!==void 0&&(s.recursive!==void 0||s.ignoreIfNotExists!==void 0)&&(c.options=s),l!==void 0&&(c.annotationId=l),c}i.create=e;function n(r){let s=r;return s&&s.kind==="delete"&&M.string(s.uri)&&(s.options===void 0||(s.options.recursive===void 0||M.boolean(s.options.recursive))&&(s.options.ignoreIfNotExists===void 0||M.boolean(s.options.ignoreIfNotExists)))&&(s.annotationId===void 0||ot.is(s.annotationId))}i.is=n})(Hn||(Hn={}));var dn;(function(i){function e(n){let r=n;return r&&(r.changes!==void 0||r.documentChanges!==void 0)&&(r.documentChanges===void 0||r.documentChanges.every(s=>M.string(s.kind)?jn.is(s)||en.is(s)||Hn.is(s):Vn.is(s)))}i.is=e})(dn||(dn={}));class or{constructor(e,n){this.edits=e,this.changeAnnotations=n}insert(e,n,r){let s,l;if(r===void 0?s=zt.insert(e,n):ot.is(r)?(l=r,s=qt.insert(e,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),l=this.changeAnnotations.manage(r),s=qt.insert(e,n,l)),this.edits.push(s),l!==void 0)return l}replace(e,n,r){let s,l;if(r===void 0?s=zt.replace(e,n):ot.is(r)?(l=r,s=qt.replace(e,n,r)):(this.assertChangeAnnotations(this.changeAnnotations),l=this.changeAnnotations.manage(r),s=qt.replace(e,n,l)),this.edits.push(s),l!==void 0)return l}delete(e,n){let r,s;if(n===void 0?r=zt.del(e):ot.is(n)?(s=n,r=qt.del(e,n)):(this.assertChangeAnnotations(this.changeAnnotations),s=this.changeAnnotations.manage(n),r=qt.del(e,s)),this.edits.push(r),s!==void 0)return s}add(e){this.edits.push(e)}all(){return this.edits}clear(){this.edits.splice(0,this.edits.length)}assertChangeAnnotations(e){if(e===void 0)throw new Error("Text edit change is not configured to manage change annotations.")}}class Nt{constructor(e){this._annotations=e===void 0?Object.create(null):e,this._counter=0,this._size=0}all(){return this._annotations}get size(){return this._size}manage(e,n){let r;if(ot.is(e)?r=e:(r=this.nextId(),n=e),this._annotations[r]!==void 0)throw new Error(`Id ${r} is already in use.`);if(n===void 0)throw new Error(`No annotation provided for id ${r}`);return this._annotations[r]=n,this._size++,r}nextId(){return this._counter++,this._counter.toString()}}class zn{constructor(e){this._textEditChanges=Object.create(null),e!==void 0?(this._workspaceEdit=e,e.documentChanges?(this._changeAnnotations=new Nt(e.changeAnnotations),e.changeAnnotations=this._changeAnnotations.all(),e.documentChanges.forEach(n=>{if(Vn.is(n)){const r=new or(n.edits,this._changeAnnotations);this._textEditChanges[n.textDocument.uri]=r}})):e.changes&&Object.keys(e.changes).forEach(n=>{const r=new or(e.changes[n]);this._textEditChanges[n]=r})):this._workspaceEdit={}}get edit(){return this.initDocumentChanges(),this._changeAnnotations!==void 0&&(this._changeAnnotations.size===0?this._workspaceEdit.changeAnnotations=void 0:this._workspaceEdit.changeAnnotations=this._changeAnnotations.all()),this._workspaceEdit}getTextEditChange(e){if(fn.is(e)){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");const n={uri:e.uri,version:e.version};let r=this._textEditChanges[n.uri];if(!r){const s=[],l={textDocument:n,edits:s};this._workspaceEdit.documentChanges.push(l),r=new or(s,this._changeAnnotations),this._textEditChanges[n.uri]=r}return r}else{if(this.initChanges(),this._workspaceEdit.changes===void 0)throw new Error("Workspace edit is not configured for normal text edit changes.");let n=this._textEditChanges[e];if(!n){let r=[];this._workspaceEdit.changes[e]=r,n=new or(r),this._textEditChanges[e]=n}return n}}initDocumentChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._changeAnnotations=new Nt,this._workspaceEdit.documentChanges=[],this._workspaceEdit.changeAnnotations=this._changeAnnotations.all())}initChanges(){this._workspaceEdit.documentChanges===void 0&&this._workspaceEdit.changes===void 0&&(this._workspaceEdit.changes=Object.create(null))}createFile(e,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let s;En.is(n)||ot.is(n)?s=n:r=n;let l,c;if(s===void 0?l=jn.create(e,r):(c=ot.is(s)?s:this._changeAnnotations.manage(s),l=jn.create(e,r,c)),this._workspaceEdit.documentChanges.push(l),c!==void 0)return c}renameFile(e,n,r,s){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let l;En.is(r)||ot.is(r)?l=r:s=r;let c,m;if(l===void 0?c=en.create(e,n,s):(m=ot.is(l)?l:this._changeAnnotations.manage(l),c=en.create(e,n,s,m)),this._workspaceEdit.documentChanges.push(c),m!==void 0)return m}deleteFile(e,n,r){if(this.initDocumentChanges(),this._workspaceEdit.documentChanges===void 0)throw new Error("Workspace edit is not configured for document changes.");let s;En.is(n)||ot.is(n)?s=n:r=n;let l,c;if(s===void 0?l=Hn.create(e,r):(c=ot.is(s)?s:this._changeAnnotations.manage(s),l=Hn.create(e,r,c)),this._workspaceEdit.documentChanges.push(l),c!==void 0)return c}}var lr;(function(i){function e(r){return{uri:r}}i.create=e;function n(r){let s=r;return M.defined(s)&&M.string(s.uri)}i.is=n})(lr||(lr={}));var wr;(function(i){function e(r,s){return{uri:r,version:s}}i.create=e;function n(r){let s=r;return M.defined(s)&&M.string(s.uri)&&M.integer(s.version)}i.is=n})(wr||(wr={}));var fn;(function(i){function e(r,s){return{uri:r,version:s}}i.create=e;function n(r){let s=r;return M.defined(s)&&M.string(s.uri)&&(s.version===null||M.integer(s.version))}i.is=n})(fn||(fn={}));var ps;(function(i){function e(r,s,l,c){return{uri:r,languageId:s,version:l,text:c}}i.create=e;function n(r){let s=r;return M.defined(s)&&M.string(s.uri)&&M.string(s.languageId)&&M.integer(s.version)&&M.string(s.text)}i.is=n})(ps||(ps={}));var An;(function(i){i.PlainText="plaintext",i.Markdown="markdown";function e(n){const r=n;return r===i.PlainText||r===i.Markdown}i.is=e})(An||(An={}));var Yn;(function(i){function e(n){const r=n;return M.objectLiteral(n)&&An.is(r.kind)&&M.string(r.value)}i.is=e})(Yn||(Yn={}));var Lr;(function(i){i.Text=1,i.Method=2,i.Function=3,i.Constructor=4,i.Field=5,i.Variable=6,i.Class=7,i.Interface=8,i.Module=9,i.Property=10,i.Unit=11,i.Value=12,i.Enum=13,i.Keyword=14,i.Snippet=15,i.Color=16,i.File=17,i.Reference=18,i.Folder=19,i.EnumMember=20,i.Constant=21,i.Struct=22,i.Event=23,i.Operator=24,i.TypeParameter=25})(Lr||(Lr={}));var Xn;(function(i){i.PlainText=1,i.Snippet=2})(Xn||(Xn={}));var ms;(function(i){i.Deprecated=1})(ms||(ms={}));var ur;(function(i){function e(r,s,l){return{newText:r,insert:s,replace:l}}i.create=e;function n(r){const s=r;return s&&M.string(s.newText)&&ee.is(s.insert)&&ee.is(s.replace)}i.is=n})(ur||(ur={}));var Jn;(function(i){i.asIs=1,i.adjustIndentation=2})(Jn||(Jn={}));var gs;(function(i){function e(n){const r=n;return r&&(M.string(r.detail)||r.detail===void 0)&&(M.string(r.description)||r.description===void 0)}i.is=e})(gs||(gs={}));var Or;(function(i){function e(n){return{label:n}}i.create=e})(Or||(Or={}));var cr;(function(i){function e(n,r){return{items:n||[],isIncomplete:!!r}}i.create=e})(cr||(cr={}));var dr;(function(i){function e(r){return r.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}i.fromPlainText=e;function n(r){const s=r;return M.string(s)||M.objectLiteral(s)&&M.string(s.language)&&M.string(s.value)}i.is=n})(dr||(dr={}));var fr;(function(i){function e(n){let r=n;return!!r&&M.objectLiteral(r)&&(Yn.is(r.contents)||dr.is(r.contents)||M.typedArray(r.contents,dr.is))&&(n.range===void 0||ee.is(n.range))}i.is=e})(fr||(fr={}));var tn;(function(i){function e(n,r){return r?{label:n,documentation:r}:{label:n}}i.create=e})(tn||(tn={}));var _n;(function(i){function e(n,r,...s){let l={label:n};return M.defined(r)&&(l.documentation=r),M.defined(s)?l.parameters=s:l.parameters=[],l}i.create=e})(_n||(_n={}));var Qn;(function(i){i.Text=1,i.Read=2,i.Write=3})(Qn||(Qn={}));var qn;(function(i){function e(n,r){let s={range:n};return M.number(r)&&(s.kind=r),s}i.create=e})(qn||(qn={}));var xi;(function(i){i.File=1,i.Module=2,i.Namespace=3,i.Package=4,i.Class=5,i.Method=6,i.Property=7,i.Field=8,i.Constructor=9,i.Enum=10,i.Interface=11,i.Function=12,i.Variable=13,i.Constant=14,i.String=15,i.Number=16,i.Boolean=17,i.Array=18,i.Object=19,i.Key=20,i.Null=21,i.EnumMember=22,i.Struct=23,i.Event=24,i.Operator=25,i.TypeParameter=26})(xi||(xi={}));var Si;(function(i){i.Deprecated=1})(Si||(Si={}));var ys;(function(i){function e(n,r,s,l,c){let m={name:n,kind:r,location:{uri:l,range:s}};return c&&(m.containerName=c),m}i.create=e})(ys||(ys={}));var Ts;(function(i){function e(n,r,s,l){return l!==void 0?{name:n,kind:r,location:{uri:s,range:l}}:{name:n,kind:r,location:{uri:s}}}i.create=e})(Ts||(Ts={}));var vs;(function(i){function e(r,s,l,c,m,$){let U={name:r,detail:s,kind:l,range:c,selectionRange:m};return $!==void 0&&(U.children=$),U}i.create=e;function n(r){let s=r;return s&&M.string(s.name)&&M.number(s.kind)&&ee.is(s.range)&&ee.is(s.selectionRange)&&(s.detail===void 0||M.string(s.detail))&&(s.deprecated===void 0||M.boolean(s.deprecated))&&(s.children===void 0||Array.isArray(s.children))&&(s.tags===void 0||Array.isArray(s.tags))}i.is=n})(vs||(vs={}));var Rs;(function(i){i.Empty="",i.QuickFix="quickfix",i.Refactor="refactor",i.RefactorExtract="refactor.extract",i.RefactorInline="refactor.inline",i.RefactorRewrite="refactor.rewrite",i.Source="source",i.SourceOrganizeImports="source.organizeImports",i.SourceFixAll="source.fixAll"})(Rs||(Rs={}));var hr;(function(i){i.Invoked=1,i.Automatic=2})(hr||(hr={}));var Es;(function(i){function e(r,s,l){let c={diagnostics:r};return s!=null&&(c.only=s),l!=null&&(c.triggerKind=l),c}i.create=e;function n(r){let s=r;return M.defined(s)&&M.typedArray(s.diagnostics,ar.is)&&(s.only===void 0||M.typedArray(s.only,M.string))&&(s.triggerKind===void 0||s.triggerKind===hr.Invoked||s.triggerKind===hr.Automatic)}i.is=n})(Es||(Es={}));var As;(function(i){function e(r,s,l){let c={title:r},m=!0;return typeof s=="string"?(m=!1,c.kind=s):Rn.is(s)?c.command=s:c.edit=s,m&&l!==void 0&&(c.kind=l),c}i.create=e;function n(r){let s=r;return s&&M.string(s.title)&&(s.diagnostics===void 0||M.typedArray(s.diagnostics,ar.is))&&(s.kind===void 0||M.string(s.kind))&&(s.edit!==void 0||s.command!==void 0)&&(s.command===void 0||Rn.is(s.command))&&(s.isPreferred===void 0||M.boolean(s.isPreferred))&&(s.edit===void 0||dn.is(s.edit))}i.is=n})(As||(As={}));var _s;(function(i){function e(r,s){let l={range:r};return M.defined(s)&&(l.data=s),l}i.create=e;function n(r){let s=r;return M.defined(s)&&ee.is(s.range)&&(M.undefined(s.command)||Rn.is(s.command))}i.is=n})(_s||(_s={}));var $s;(function(i){function e(r,s){return{tabSize:r,insertSpaces:s}}i.create=e;function n(r){let s=r;return M.defined(s)&&M.uinteger(s.tabSize)&&M.boolean(s.insertSpaces)}i.is=n})($s||($s={}));var Is;(function(i){function e(r,s,l){return{range:r,target:s,data:l}}i.create=e;function n(r){let s=r;return M.defined(s)&&ee.is(s.range)&&(M.undefined(s.target)||M.string(s.target))}i.is=n})(Is||(Is={}));var Cs;(function(i){function e(r,s){return{range:r,parent:s}}i.create=e;function n(r){let s=r;return M.objectLiteral(s)&&ee.is(s.range)&&(s.parent===void 0||i.is(s.parent))}i.is=n})(Cs||(Cs={}));var ks;(function(i){i.namespace="namespace",i.type="type",i.class="class",i.enum="enum",i.interface="interface",i.struct="struct",i.typeParameter="typeParameter",i.parameter="parameter",i.variable="variable",i.property="property",i.enumMember="enumMember",i.event="event",i.function="function",i.method="method",i.macro="macro",i.keyword="keyword",i.modifier="modifier",i.comment="comment",i.string="string",i.number="number",i.regexp="regexp",i.operator="operator",i.decorator="decorator"})(ks||(ks={}));var xs;(function(i){i.declaration="declaration",i.definition="definition",i.readonly="readonly",i.static="static",i.deprecated="deprecated",i.abstract="abstract",i.async="async",i.modification="modification",i.documentation="documentation",i.defaultLibrary="defaultLibrary"})(xs||(xs={}));var Ss;(function(i){function e(n){const r=n;return M.objectLiteral(r)&&(r.resultId===void 0||typeof r.resultId=="string")&&Array.isArray(r.data)&&(r.data.length===0||typeof r.data[0]=="number")}i.is=e})(Ss||(Ss={}));var Ns;(function(i){function e(r,s){return{range:r,text:s}}i.create=e;function n(r){const s=r;return s!=null&&ee.is(s.range)&&M.string(s.text)}i.is=n})(Ns||(Ns={}));var ws;(function(i){function e(r,s,l){return{range:r,variableName:s,caseSensitiveLookup:l}}i.create=e;function n(r){const s=r;return s!=null&&ee.is(s.range)&&M.boolean(s.caseSensitiveLookup)&&(M.string(s.variableName)||s.variableName===void 0)}i.is=n})(ws||(ws={}));var Ls;(function(i){function e(r,s){return{range:r,expression:s}}i.create=e;function n(r){const s=r;return s!=null&&ee.is(s.range)&&(M.string(s.expression)||s.expression===void 0)}i.is=n})(Ls||(Ls={}));var Pr;(function(i){function e(r,s){return{frameId:r,stoppedLocation:s}}i.create=e;function n(r){const s=r;return M.defined(s)&&ee.is(r.stoppedLocation)}i.is=n})(Pr||(Pr={}));var pr;(function(i){i.Type=1,i.Parameter=2;function e(n){return n===1||n===2}i.is=e})(pr||(pr={}));var Mr;(function(i){function e(r){return{value:r}}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&(s.tooltip===void 0||M.string(s.tooltip)||Yn.is(s.tooltip))&&(s.location===void 0||D.is(s.location))&&(s.command===void 0||Rn.is(s.command))}i.is=n})(Mr||(Mr={}));var Os;(function(i){function e(r,s,l){const c={position:r,label:s};return l!==void 0&&(c.kind=l),c}i.create=e;function n(r){const s=r;return M.objectLiteral(s)&&Ne.is(s.position)&&(M.string(s.label)||M.typedArray(s.label,Mr.is))&&(s.kind===void 0||pr.is(s.kind))&&s.textEdits===void 0||M.typedArray(s.textEdits,zt.is)&&(s.tooltip===void 0||M.string(s.tooltip)||Yn.is(s.tooltip))&&(s.paddingLeft===void 0||M.boolean(s.paddingLeft))&&(s.paddingRight===void 0||M.boolean(s.paddingRight))}i.is=n})(Os||(Os={}));var Ps;(function(i){function e(n){return{kind:"snippet",value:n}}i.createSnippet=e})(Ps||(Ps={}));var br;(function(i){function e(n,r,s,l){return{insertText:n,filterText:r,range:s,command:l}}i.create=e})(br||(br={}));var Ms;(function(i){function e(n){return{items:n}}i.create=e})(Ms||(Ms={}));var bs;(function(i){i.Invoked=0,i.Automatic=1})(bs||(bs={}));var Dr;(function(i){function e(n,r){return{range:n,text:r}}i.create=e})(Dr||(Dr={}));var Ds;(function(i){function e(n,r){return{triggerKind:n,selectedCompletionInfo:r}}i.create=e})(Ds||(Ds={}));var Zr;(function(i){function e(n){const r=n;return M.objectLiteral(r)&&St.is(r.uri)&&M.string(r.name)}i.is=e})(Zr||(Zr={}));const Zs=null;var $n;(function(i){function e(l,c,m,$){return new mr(l,c,m,$)}i.create=e;function n(l){let c=l;return!!(M.defined(c)&&M.string(c.uri)&&(M.undefined(c.languageId)||M.string(c.languageId))&&M.uinteger(c.lineCount)&&M.func(c.getText)&&M.func(c.positionAt)&&M.func(c.offsetAt))}i.is=n;function r(l,c){let m=l.getText(),$=s(c,(H,ue)=>{let ne=H.range.start.line-ue.range.start.line;return ne===0?H.range.start.character-ue.range.start.character:ne}),U=m.length;for(let H=$.length-1;H>=0;H--){let ue=$[H],ne=l.offsetAt(ue.range.start),X=l.offsetAt(ue.range.end);if(X<=U)m=m.substring(0,ne)+ue.newText+m.substring(X,m.length);else throw new Error("Overlapping edit");U=ne}return m}i.applyEdits=r;function s(l,c){if(l.length<=1)return l;const m=l.length/2|0,$=l.slice(0,m),U=l.slice(m);s($,c),s(U,c);let H=0,ue=0,ne=0;for(;H<$.length&&ue<U.length;)c($[H],U[ue])<=0?l[ne++]=$[H++]:l[ne++]=U[ue++];for(;H<$.length;)l[ne++]=$[H++];for(;ue<U.length;)l[ne++]=U[ue++];return l}})($n||($n={}));class mr{constructor(e,n,r,s){this._uri=e,this._languageId=n,this._version=r,this._content=s,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){let n=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(n,r)}return this._content}update(e,n){this._content=e.text,this._version=n,this._lineOffsets=void 0}getLineOffsets(){if(this._lineOffsets===void 0){let e=[],n=this._content,r=!0;for(let s=0;s<n.length;s++){r&&(e.push(s),r=!1);let l=n.charAt(s);r=l==="\r"||l===`
`,l==="\r"&&s+1<n.length&&n.charAt(s+1)===`
`&&s++}r&&n.length>0&&e.push(n.length),this._lineOffsets=e}return this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);let n=this.getLineOffsets(),r=0,s=n.length;if(s===0)return Ne.create(0,e);for(;r<s;){let c=Math.floor((r+s)/2);n[c]>e?s=c:r=c+1}let l=r-1;return Ne.create(l,e-n[l])}offsetAt(e){let n=this.getLineOffsets();if(e.line>=n.length)return this._content.length;if(e.line<0)return 0;let r=n[e.line],s=e.line+1<n.length?n[e.line+1]:this._content.length;return Math.max(Math.min(r+e.character,s),r)}get lineCount(){return this.getLineOffsets().length}}var M;(function(i){const e=Object.prototype.toString;function n(X){return typeof X!="undefined"}i.defined=n;function r(X){return typeof X=="undefined"}i.undefined=r;function s(X){return X===!0||X===!1}i.boolean=s;function l(X){return e.call(X)==="[object String]"}i.string=l;function c(X){return e.call(X)==="[object Number]"}i.number=c;function m(X,De,Ve){return e.call(X)==="[object Number]"&&De<=X&&X<=Ve}i.numberRange=m;function $(X){return e.call(X)==="[object Number]"&&-2147483648<=X&&X<=2147483647}i.integer=$;function U(X){return e.call(X)==="[object Number]"&&0<=X&&X<=2147483647}i.uinteger=U;function H(X){return e.call(X)==="[object Function]"}i.func=H;function ue(X){return X!==null&&typeof X=="object"}i.objectLiteral=ue;function ne(X,De){return Array.isArray(X)&&X.every(De)}i.typedArray=ne})(M||(M={}));class Ni{constructor(){this.nodeStack=[]}get current(){var e;return(e=this.nodeStack[this.nodeStack.length-1])!==null&&e!==void 0?e:this.rootNode}buildRootNode(e){return this.rootNode=new In(e),this.rootNode.root=this.rootNode,this.nodeStack=[this.rootNode],this.rootNode}buildCompositeNode(e){const n=new nn;return n.grammarSource=e,n.root=this.rootNode,this.current.content.push(n),this.nodeStack.push(n),n}buildLeafNode(e,n){const r=new et(e.startOffset,e.image.length,(0,y.sp)(e),e.tokenType,!n);return r.grammarSource=n,r.root=this.rootNode,this.current.content.push(r),r}removeNode(e){const n=e.container;if(n){const r=n.content.indexOf(e);r>=0&&n.content.splice(r,1)}}addHiddenNodes(e){const n=[];for(const l of e){const c=new et(l.startOffset,l.image.length,(0,y.sp)(l),l.tokenType,!0);c.root=this.rootNode,n.push(c)}let r=this.current,s=!1;if(r.content.length>0){r.content.push(...n);return}for(;r.container;){const l=r.container.content.indexOf(r);if(l>0){r.container.content.splice(l,0,...n),s=!0;break}r=r.container}s||this.rootNode.content.unshift(...n)}construct(e){const n=this.current;typeof e.$type=="string"&&(this.current.astNode=e),e.$cstNode=n;const r=this.nodeStack.pop();(r==null?void 0:r.content.length)===0&&this.removeNode(r)}}class Fr{get parent(){return this.container}get feature(){return this.grammarSource}get hidden(){return!1}get astNode(){var e,n;const r=typeof((e=this._astNode)===null||e===void 0?void 0:e.$type)=="string"?this._astNode:(n=this.container)===null||n===void 0?void 0:n.astNode;if(!r)throw new Error("This node has no associated AST element");return r}set astNode(e){this._astNode=e}get element(){return this.astNode}get text(){return this.root.fullText.substring(this.offset,this.end)}}class et extends Fr{get offset(){return this._offset}get length(){return this._length}get end(){return this._offset+this._length}get hidden(){return this._hidden}get tokenType(){return this._tokenType}get range(){return this._range}constructor(e,n,r,s,l=!1){super(),this._hidden=l,this._offset=e,this._tokenType=s,this._length=n,this._range=r}}class nn extends Fr{constructor(){super(...arguments),this.content=new gr(this)}get children(){return this.content}get offset(){var e,n;return(n=(e=this.firstNonHiddenNode)===null||e===void 0?void 0:e.offset)!==null&&n!==void 0?n:0}get length(){return this.end-this.offset}get end(){var e,n;return(n=(e=this.lastNonHiddenNode)===null||e===void 0?void 0:e.end)!==null&&n!==void 0?n:0}get range(){const e=this.firstNonHiddenNode,n=this.lastNonHiddenNode;if(e&&n){if(this._rangeCache===void 0){const{range:r}=e,{range:s}=n;this._rangeCache={start:r.start,end:s.end.line<r.start.line?r.start:s.end}}return this._rangeCache}else return{start:Ne.create(0,0),end:Ne.create(0,0)}}get firstNonHiddenNode(){for(const e of this.content)if(!e.hidden)return e;return this.content[0]}get lastNonHiddenNode(){for(let e=this.content.length-1;e>=0;e--){const n=this.content[e];if(!n.hidden)return n}return this.content[this.content.length-1]}}class gr extends Array{constructor(e){super(),this.parent=e,Object.setPrototypeOf(this,gr.prototype)}push(...e){return this.addParents(e),super.push(...e)}unshift(...e){return this.addParents(e),super.unshift(...e)}splice(e,n,...r){return this.addParents(r),super.splice(e,n,...r)}addParents(e){for(const n of e)n.container=this.parent}}class In extends nn{get text(){return this._text.substring(this.offset,this.end)}get fullText(){return this._text}constructor(e){super(),this._text="",this._text=e!=null?e:""}}const Cn=Symbol("Datatype");function yr(i){return i.$type===Cn}const Ur="\u200B",kn=i=>i.endsWith(Ur)?i:i+Ur;class Fs{constructor(e){this._unorderedGroups=new Map,this.allRules=new Map,this.lexer=e.parser.Lexer;const n=this.lexer.definition,r=e.LanguageMetaData.mode==="production";this.wrapper=new Ws(n,Object.assign(Object.assign({},e.parser.ParserConfig),{skipValidations:r,errorMessageProvider:e.parser.ParserErrorMessageProvider}))}alternatives(e,n){this.wrapper.wrapOr(e,n)}optional(e,n){this.wrapper.wrapOption(e,n)}many(e,n){this.wrapper.wrapMany(e,n)}atLeastOne(e,n){this.wrapper.wrapAtLeastOne(e,n)}getRule(e){return this.allRules.get(e)}isRecording(){return this.wrapper.IS_RECORDING}get unorderedGroups(){return this._unorderedGroups}getRuleStack(){return this.wrapper.RULE_STACK}finalize(){this.wrapper.wrapSelfAnalysis()}}class wi extends Fs{get current(){return this.stack[this.stack.length-1]}constructor(e){super(e),this.nodeBuilder=new Ni,this.stack=[],this.assignmentMap=new Map,this.linker=e.references.Linker,this.converter=e.parser.ValueConverter,this.astReflection=e.shared.AstReflection}rule(e,n){const r=this.computeRuleType(e),s=this.wrapper.DEFINE_RULE(kn(e.name),this.startImplementation(r,n).bind(this));return this.allRules.set(e.name,s),e.entry&&(this.mainRule=s),s}computeRuleType(e){if(!e.fragment){if((0,O.UP)(e))return Cn;{const n=(0,O.$G)(e);return n!=null?n:e.name}}}parse(e,n={}){this.nodeBuilder.buildRootNode(e);const r=this.lexerResult=this.lexer.tokenize(e);this.wrapper.input=r.tokens;const s=n.rule?this.allRules.get(n.rule):this.mainRule;if(!s)throw new Error(n.rule?`No rule found with name '${n.rule}'`:"No main rule available.");const l=s.call(this.wrapper,{});return this.nodeBuilder.addHiddenNodes(r.hidden),this.unorderedGroups.clear(),this.lexerResult=void 0,{value:l,lexerErrors:r.errors,lexerReport:r.report,parserErrors:this.wrapper.errors}}startImplementation(e,n){return r=>{const s=!this.isRecording()&&e!==void 0;if(s){const c={$type:e};this.stack.push(c),e===Cn&&(c.value="")}let l;try{l=n(r)}catch(c){l=void 0}return l===void 0&&s&&(l=this.construct()),l}}extractHiddenTokens(e){const n=this.lexerResult.hidden;if(!n.length)return[];const r=e.startOffset;for(let s=0;s<n.length;s++)if(n[s].startOffset>r)return n.splice(0,s);return n.splice(0,n.length)}consume(e,n,r){const s=this.wrapper.wrapConsume(e,n);if(!this.isRecording()&&this.isValidToken(s)){const l=this.extractHiddenTokens(s);this.nodeBuilder.addHiddenNodes(l);const c=this.nodeBuilder.buildLeafNode(s,r),{assignment:m,isCrossRef:$}=this.getAssignment(r),U=this.current;if(m){const H=(0,x.p1)(r)?s.image:this.converter.convert(s.image,c);this.assign(m.operator,m.feature,H,c,$)}else if(yr(U)){let H=s.image;(0,x.p1)(r)||(H=this.converter.convert(H,c).toString()),U.value+=H}}}isValidToken(e){return!e.isInsertedInRecovery&&!isNaN(e.startOffset)&&typeof e.endOffset=="number"&&!isNaN(e.endOffset)}subrule(e,n,r,s,l){let c;!this.isRecording()&&!r&&(c=this.nodeBuilder.buildCompositeNode(s));const m=this.wrapper.wrapSubrule(e,n,l);!this.isRecording()&&c&&c.length>0&&this.performSubruleAssignment(m,s,c)}performSubruleAssignment(e,n,r){const{assignment:s,isCrossRef:l}=this.getAssignment(n);if(s)this.assign(s.operator,s.feature,e,r,l);else if(!s){const c=this.current;if(yr(c))c.value+=e.toString();else if(typeof e=="object"&&e){const $=this.assignWithoutOverride(e,c);this.stack.pop(),this.stack.push($)}}}action(e,n){if(!this.isRecording()){let r=this.current;if(n.feature&&n.operator){r=this.construct(),this.nodeBuilder.removeNode(r.$cstNode),this.nodeBuilder.buildCompositeNode(n).content.push(r.$cstNode);const l={$type:e};this.stack.push(l),this.assign(n.operator,n.feature,r,r.$cstNode,!1)}else r.$type=e}}construct(){if(this.isRecording())return;const e=this.current;return(0,be.b2)(e),this.nodeBuilder.construct(e),this.stack.pop(),yr(e)?this.converter.convert(e.value,e.$cstNode):((0,be.a1)(this.astReflection,e),e)}getAssignment(e){if(!this.assignmentMap.has(e)){const n=(0,be.V_)(e,x.B7);this.assignmentMap.set(e,{assignment:n,isCrossRef:n?(0,x.Ki)(n.terminal):!1})}return this.assignmentMap.get(e)}assign(e,n,r,s,l){const c=this.current;let m;switch(l&&typeof r=="string"?m=this.linker.buildReference(c,n,s,r):m=r,e){case"=":{c[n]=m;break}case"?=":{c[n]=!0;break}case"+=":Array.isArray(c[n])||(c[n]=[]),c[n].push(m)}}assignWithoutOverride(e,n){for(const[s,l]of Object.entries(n)){const c=e[s];c===void 0?e[s]=l:Array.isArray(c)&&Array.isArray(l)&&(l.push(...c),e[s]=l)}const r=e.$cstNode;return r&&(r.astNode=void 0,e.$cstNode=void 0),e}get definitionErrors(){return this.wrapper.definitionErrors}}class Li{buildMismatchTokenMessage(e){return b.Hs.buildMismatchTokenMessage(e)}buildNotAllInputParsedMessage(e){return b.Hs.buildNotAllInputParsedMessage(e)}buildNoViableAltMessage(e){return b.Hs.buildNoViableAltMessage(e)}buildEarlyExitMessage(e){return b.Hs.buildEarlyExitMessage(e)}}class Us extends Li{buildMismatchTokenMessage({expected:e,actual:n}){return`Expecting ${e.LABEL?"`"+e.LABEL+"`":e.name.endsWith(":KW")?`keyword '${e.name.substring(0,e.name.length-3)}'`:`token of type '${e.name}'`} but found \`${n.image}\`.`}buildNotAllInputParsedMessage({firstRedundant:e}){return`Expecting end of file but found \`${e.image}\`.`}}class Gs extends Fs{constructor(){super(...arguments),this.tokens=[],this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}action(){}construct(){}parse(e){this.resetState();const n=this.lexer.tokenize(e,{mode:"partial"});return this.tokens=n.tokens,this.wrapper.input=[...this.tokens],this.mainRule.call(this.wrapper,{}),this.unorderedGroups.clear(),{tokens:this.tokens,elementStack:[...this.lastElementStack],tokenIndex:this.nextTokenIndex}}rule(e,n){const r=this.wrapper.DEFINE_RULE(kn(e.name),this.startImplementation(n).bind(this));return this.allRules.set(e.name,r),e.entry&&(this.mainRule=r),r}resetState(){this.elementStack=[],this.lastElementStack=[],this.nextTokenIndex=0,this.stackSize=0}startImplementation(e){return n=>{const r=this.keepStackSize();try{e(n)}finally{this.resetStackSize(r)}}}removeUnexpectedElements(){this.elementStack.splice(this.stackSize)}keepStackSize(){const e=this.elementStack.length;return this.stackSize=e,e}resetStackSize(e){this.removeUnexpectedElements(),this.stackSize=e}consume(e,n,r){this.wrapper.wrapConsume(e,n),this.isRecording()||(this.lastElementStack=[...this.elementStack,r],this.nextTokenIndex=this.currIdx+1)}subrule(e,n,r,s,l){this.before(s),this.wrapper.wrapSubrule(e,n,l),this.after(s)}before(e){this.isRecording()||this.elementStack.push(e)}after(e){if(!this.isRecording()){const n=this.elementStack.lastIndexOf(e);n>=0&&this.elementStack.splice(n)}}get currIdx(){return this.wrapper.currIdx}}const Bs={recoveryEnabled:!0,nodeLocationTracking:"full",skipValidations:!0,errorMessageProvider:new Us};class Ws extends b.nu{constructor(e,n){const r=n&&"maxLookahead"in n;super(e,Object.assign(Object.assign(Object.assign({},Bs),{lookaheadStrategy:r?new b.dV({maxLookahead:n.maxLookahead}):new Dn({logging:n.skipValidations?()=>{}:void 0})}),n))}get IS_RECORDING(){return this.RECORDING_PHASE}DEFINE_RULE(e,n){return this.RULE(e,n)}wrapSelfAnalysis(){this.performSelfAnalysis()}wrapConsume(e,n){return this.consume(e,n)}wrapSubrule(e,n,r){return this.subrule(e,n,{ARGS:[r]})}wrapOr(e,n){this.or(e,n)}wrapOption(e,n){this.option(e,n)}wrapMany(e,n){this.many(e,n)}wrapAtLeastOne(e,n){this.atLeastOne(e,n)}}var xn=g(44465),st=g(21442);function Ks(i,e,n){return Vs({parser:e,tokens:n,ruleNames:new Map},i),e}function Vs(i,e){const n=(0,O.VD)(e,!1),r=(0,st.Vw)(e.rules).filter(x.F9).filter(s=>n.has(s));for(const s of r){const l=Object.assign(Object.assign({},i),{consume:1,optional:1,subrule:1,many:1,or:1});i.parser.rule(s,Fe(l,s.definition))}}function Fe(i,e,n=!1){let r;if((0,x.p1)(e))r=zs(i,e);else if((0,x.LG)(e))r=er(i,e);else if((0,x.B7)(e))r=Fe(i,e.terminal);else if((0,x.Ki)(e))r=Gr(i,e);else if((0,x.t3)(e))r=At(i,e);else if((0,x.MZ)(e))r=js(i,e);else if((0,x.W1)(e))r=Oi(i,e);else if((0,x.ty)(e))r=Hs(i,e);else if((0,x.rT)(e)){const s=i.consume++;r=()=>i.parser.consume(s,b.sd,e)}else throw new xn.h(e.$cstNode,`Unexpected element type: ${e.$type}`);return Br(i,n?void 0:tr(e),r,e.cardinality)}function er(i,e){const n=(0,O.z$)(e);return()=>i.parser.action(n,e)}function At(i,e){const n=e.rule.ref;if((0,x.F9)(n)){const r=i.subrule++,s=n.fragment,l=e.arguments.length>0?Sn(n,e.arguments):()=>({});return c=>i.parser.subrule(r,Wr(i,n),s,e,l(c))}else if((0,x.MS)(n)){const r=i.consume++,s=Tr(i,n.name);return()=>i.parser.consume(r,s,e)}else if(n)(0,xn.U)(n);else throw new xn.h(e.$cstNode,`Undefined rule: ${e.rule.$refText}`)}function Sn(i,e){const n=e.map(r=>rn(r.value));return r=>{const s={};for(let l=0;l<n.length;l++){const c=i.parameters[l],m=n[l];s[c.name]=m(r)}return s}}function rn(i){if((0,x.F8)(i)){const e=rn(i.left),n=rn(i.right);return r=>e(r)||n(r)}else if((0,x.TB)(i)){const e=rn(i.left),n=rn(i.right);return r=>e(r)&&n(r)}else if((0,x.Ii)(i)){const e=rn(i.value);return n=>!e(n)}else if((0,x.yW)(i)){const e=i.parameter.ref.name;return n=>n!==void 0&&n[e]===!0}else if((0,x.L)(i)){const e=!!i.true;return()=>e}(0,xn.U)(i)}function js(i,e){if(e.elements.length===1)return Fe(i,e.elements[0]);{const n=[];for(const s of e.elements){const l={ALT:Fe(i,s,!0)},c=tr(s);c&&(l.GATE=rn(c)),n.push(l)}const r=i.or++;return s=>i.parser.alternatives(r,n.map(l=>{const c={ALT:()=>l.ALT(s)},m=l.GATE;return m&&(c.GATE=()=>m(s)),c}))}}function Oi(i,e){if(e.elements.length===1)return Fe(i,e.elements[0]);const n=[];for(const m of e.elements){const $={ALT:Fe(i,m,!0)},U=tr(m);U&&($.GATE=rn(U)),n.push($)}const r=i.or++,s=(m,$)=>{const U=$.getRuleStack().join("-");return`uGroup_${m}_${U}`},l=m=>i.parser.alternatives(r,n.map(($,U)=>{const H={ALT:()=>!0},ue=i.parser;H.ALT=()=>{if($.ALT(m),!ue.isRecording()){const X=s(r,ue);ue.unorderedGroups.get(X)||ue.unorderedGroups.set(X,[]);const De=ue.unorderedGroups.get(X);typeof(De==null?void 0:De[U])=="undefined"&&(De[U]=!0)}};const ne=$.GATE;return ne?H.GATE=()=>ne(m):H.GATE=()=>{const X=ue.unorderedGroups.get(s(r,ue));return!(X!=null&&X[U])},H})),c=Br(i,tr(e),l,"*");return m=>{c(m),i.parser.isRecording()||i.parser.unorderedGroups.delete(s(r,i.parser))}}function Hs(i,e){const n=e.elements.map(r=>Fe(i,r));return r=>n.forEach(s=>s(r))}function tr(i){if((0,x.ty)(i))return i.guardCondition}function Gr(i,e,n=e.terminal){if(n)if((0,x.t3)(n)&&(0,x.F9)(n.rule.ref)){const r=n.rule.ref,s=i.subrule++;return l=>i.parser.subrule(s,Wr(i,r),!1,e,l)}else if((0,x.t3)(n)&&(0,x.MS)(n.rule.ref)){const r=i.consume++,s=Tr(i,n.rule.ref.name);return()=>i.parser.consume(r,s,e)}else if((0,x.p1)(n)){const r=i.consume++,s=Tr(i,n.value);return()=>i.parser.consume(r,s,e)}else throw new Error("Could not build cross reference parser");else{if(!e.type.ref)throw new Error("Could not resolve reference to type: "+e.type.$refText);const r=(0,O.ib)(e.type.ref),s=r==null?void 0:r.terminal;if(!s)throw new Error("Could not find name assignment for type: "+(0,O.z$)(e.type.ref));return Gr(i,e,s)}}function zs(i,e){const n=i.consume++,r=i.tokens[e.value];if(!r)throw new Error("Could not find token for keyword: "+e.value);return()=>i.parser.consume(n,r,e)}function Br(i,e,n,r){const s=e&&rn(e);if(!r)if(s){const l=i.or++;return c=>i.parser.alternatives(l,[{ALT:()=>n(c),GATE:()=>s(c)},{ALT:(0,b._o)(),GATE:()=>!s(c)}])}else return n;if(r==="*"){const l=i.many++;return c=>i.parser.many(l,{DEF:()=>n(c),GATE:s?()=>s(c):void 0})}else if(r==="+"){const l=i.many++;if(s){const c=i.or++;return m=>i.parser.alternatives(c,[{ALT:()=>i.parser.atLeastOne(l,{DEF:()=>n(m)}),GATE:()=>s(m)},{ALT:(0,b._o)(),GATE:()=>!s(m)}])}else return c=>i.parser.atLeastOne(l,{DEF:()=>n(c)})}else if(r==="?"){const l=i.optional++;return c=>i.parser.optional(l,{DEF:()=>n(c),GATE:s?()=>s(c):void 0})}else(0,xn.U)(r)}function Wr(i,e){const n=Ys(i,e),r=i.parser.getRule(n);if(!r)throw new Error(`Rule "${n}" not found."`);return r}function Ys(i,e){if((0,x.F9)(e))return e.name;if(i.ruleNames.has(e))return i.ruleNames.get(e);{let n=e,r=n.$container,s=e.$type;for(;!(0,x.F9)(r);)((0,x.ty)(r)||(0,x.MZ)(r)||(0,x.W1)(r))&&(s=r.elements.indexOf(n).toString()+":"+s),n=r,r=r.$container;return s=r.name+":"+s,i.ruleNames.set(e,s),s}}function Tr(i,e){const n=i.tokens[e];if(!n)throw new Error(`Token "${e}" not found."`);return n}function Xs(i){const e=i.Grammar,n=i.parser.Lexer,r=new Gs(i);return Ks(e,r,n.definition),r.finalize(),r}function Pi(i){const e=sn(i);return e.finalize(),e}function sn(i){const e=i.Grammar,n=i.parser.Lexer,r=new wi(i);return Ks(e,r,n.definition)}var Kr=g(93202),Js=g(12058),Ke=g(82500),We=g(51720);function hn(){return new Promise(i=>{typeof setImmediate=="undefined"?setTimeout(i,0):setImmediate(i)})}let Yt=0,Qs=10;function Mi(){return Yt=performance.now(),new Ke.AU}function Lt(i){Qs=i}const vr=Symbol("OperationCancelled");function Rr(i){return i===vr}function _t(i){return Re(this,null,function*(){if(i===Ke.Ts.None)return;const e=performance.now();if(e-Yt>=Qs&&(Yt=e,yield hn(),Yt=performance.now()),i.isCancellationRequested)throw vr})}class Vr{constructor(){this.promise=new Promise((e,n)=>{this.resolve=r=>(e(r),this),this.reject=r=>(n(r),this)})}}class nr{constructor(e,n,r,s){this._uri=e,this._languageId=n,this._version=r,this._content=s,this._lineOffsets=void 0}get uri(){return this._uri}get languageId(){return this._languageId}get version(){return this._version}getText(e){if(e){const n=this.offsetAt(e.start),r=this.offsetAt(e.end);return this._content.substring(n,r)}return this._content}update(e,n){for(const r of e)if(nr.isIncremental(r)){const s=ti(r.range),l=this.offsetAt(s.start),c=this.offsetAt(s.end);this._content=this._content.substring(0,l)+r.text+this._content.substring(c,this._content.length);const m=Math.max(s.start.line,0),$=Math.max(s.end.line,0);let U=this._lineOffsets;const H=qs(r.text,!1,l);if($-m===H.length)for(let ne=0,X=H.length;ne<X;ne++)U[ne+m+1]=H[ne];else H.length<1e4?U.splice(m+1,$-m,...H):this._lineOffsets=U=U.slice(0,m+1).concat(H,U.slice($+1));const ue=r.text.length-(c-l);if(ue!==0)for(let ne=m+1+H.length,X=U.length;ne<X;ne++)U[ne]=U[ne]+ue}else if(nr.isFull(r))this._content=r.text,this._lineOffsets=void 0;else throw new Error("Unknown change event received");this._version=n}getLineOffsets(){return this._lineOffsets===void 0&&(this._lineOffsets=qs(this._content,!0)),this._lineOffsets}positionAt(e){e=Math.max(Math.min(e,this._content.length),0);const n=this.getLineOffsets();let r=0,s=n.length;if(s===0)return{line:0,character:e};for(;r<s;){const c=Math.floor((r+s)/2);n[c]>e?s=c:r=c+1}const l=r-1;return e=this.ensureBeforeEOL(e,n[l]),{line:l,character:e-n[l]}}offsetAt(e){const n=this.getLineOffsets();if(e.line>=n.length)return this._content.length;if(e.line<0)return 0;const r=n[e.line];if(e.character<=0)return r;const s=e.line+1<n.length?n[e.line+1]:this._content.length,l=Math.min(r+e.character,s);return this.ensureBeforeEOL(l,r)}ensureBeforeEOL(e,n){for(;e>n&&ei(this._content.charCodeAt(e-1));)e--;return e}get lineCount(){return this.getLineOffsets().length}static isIncremental(e){const n=e;return n!=null&&typeof n.text=="string"&&n.range!==void 0&&(n.rangeLength===void 0||typeof n.rangeLength=="number")}static isFull(e){const n=e;return n!=null&&typeof n.text=="string"&&n.range===void 0&&n.rangeLength===void 0}}var jr;(function(i){function e(s,l,c,m){return new nr(s,l,c,m)}i.create=e;function n(s,l,c){if(s instanceof nr)return s.update(l,c),s;throw new Error("TextDocument.update: document must be created by TextDocument.create")}i.update=n;function r(s,l){const c=s.getText(),m=Hr(l.map(bi),(H,ue)=>{const ne=H.range.start.line-ue.range.start.line;return ne===0?H.range.start.character-ue.range.start.character:ne});let $=0;const U=[];for(const H of m){const ue=s.offsetAt(H.range.start);if(ue<$)throw new Error("Overlapping edit");ue>$&&U.push(c.substring($,ue)),H.newText.length&&U.push(H.newText),$=s.offsetAt(H.range.end)}return U.push(c.substr($)),U.join("")}i.applyEdits=r})(jr||(jr={}));function Hr(i,e){if(i.length<=1)return i;const n=i.length/2|0,r=i.slice(0,n),s=i.slice(n);Hr(r,e),Hr(s,e);let l=0,c=0,m=0;for(;l<r.length&&c<s.length;)e(r[l],s[c])<=0?i[m++]=r[l++]:i[m++]=s[c++];for(;l<r.length;)i[m++]=r[l++];for(;c<s.length;)i[m++]=s[c++];return i}function qs(i,e,n=0){const r=e?[n]:[];for(let s=0;s<i.length;s++){const l=i.charCodeAt(s);ei(l)&&(l===13&&s+1<i.length&&i.charCodeAt(s+1)===10&&s++,r.push(n+s+1))}return r}function ei(i){return i===13||i===10}function ti(i){const e=i.start,n=i.end;return e.line>n.line||e.line===n.line&&e.character>n.character?{start:n,end:e}:i}function bi(i){const e=ti(i.range);return e!==i.range?{newText:i.newText,range:e}:i}var Ot=g(43545),Ue;(function(i){i[i.Changed=0]="Changed",i[i.Parsed=1]="Parsed",i[i.IndexedContent=2]="IndexedContent",i[i.ComputedScopes=3]="ComputedScopes",i[i.Linked=4]="Linked",i[i.IndexedReferences=5]="IndexedReferences",i[i.Validated=6]="Validated"})(Ue||(Ue={}));class Di{constructor(e){this.serviceRegistry=e.ServiceRegistry,this.textDocuments=e.workspace.TextDocuments,this.fileSystemProvider=e.workspace.FileSystemProvider}fromUri(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const s=yield this.fileSystemProvider.readFile(e);return this.createAsync(e,s,n)})}fromTextDocument(e,n,r){return n=n!=null?n:Ot.o.parse(e.uri),Ke.Ts.is(r)?this.createAsync(n,e,r):this.create(n,e,r)}fromString(e,n,r){return Ke.Ts.is(r)?this.createAsync(n,e,r):this.create(n,e,r)}fromModel(e,n){return this.create(n,{$model:e})}create(e,n,r){if(typeof n=="string"){const s=this.parse(e,n,r);return this.createLangiumDocument(s,e,void 0,n)}else if("$model"in n){const s={value:n.$model,parserErrors:[],lexerErrors:[]};return this.createLangiumDocument(s,e)}else{const s=this.parse(e,n.getText(),r);return this.createLangiumDocument(s,e,n)}}createAsync(e,n,r){return Re(this,null,function*(){if(typeof n=="string"){const s=yield this.parseAsync(e,n,r);return this.createLangiumDocument(s,e,void 0,n)}else{const s=yield this.parseAsync(e,n.getText(),r);return this.createLangiumDocument(s,e,n)}})}createLangiumDocument(e,n,r,s){let l;if(r)l={parseResult:e,uri:n,state:Ue.Parsed,references:[],textDocument:r};else{const c=this.createTextDocumentGetter(n,s);l={parseResult:e,uri:n,state:Ue.Parsed,references:[],get textDocument(){return c()}}}return e.value.$document=l,l}update(e,n){return Re(this,null,function*(){var r,s;const l=(r=e.parseResult.value.$cstNode)===null||r===void 0?void 0:r.root.fullText,c=(s=this.textDocuments)===null||s===void 0?void 0:s.get(e.uri.toString()),m=c?c.getText():yield this.fileSystemProvider.readFile(e.uri);if(c)Object.defineProperty(e,"textDocument",{value:c});else{const $=this.createTextDocumentGetter(e.uri,m);Object.defineProperty(e,"textDocument",{get:$})}return l!==m&&(e.parseResult=yield this.parseAsync(e.uri,m,n),e.parseResult.value.$document=e),e.state=Ue.Parsed,e})}parse(e,n,r){return this.serviceRegistry.getServices(e).parser.LangiumParser.parse(n,r)}parseAsync(e,n,r){return this.serviceRegistry.getServices(e).parser.AsyncParser.parse(n,r)}createTextDocumentGetter(e,n){const r=this.serviceRegistry;let s;return()=>s!=null?s:s=jr.create(e.toString(),r.getServices(e).LanguageMetaData.languageId,0,n!=null?n:"")}}class Zi{constructor(e){this.documentMap=new Map,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.serviceRegistry=e.ServiceRegistry}get all(){return(0,st.Vw)(this.documentMap.values())}addDocument(e){const n=e.uri.toString();if(this.documentMap.has(n))throw new Error(`A document with the URI '${n}' is already present.`);this.documentMap.set(n,e)}getDocument(e){const n=e.toString();return this.documentMap.get(n)}getOrCreateDocument(e,n){return Re(this,null,function*(){let r=this.getDocument(e);return r||(r=yield this.langiumDocumentFactory.fromUri(e,n),this.addDocument(r),r)})}createDocument(e,n,r){if(r)return this.langiumDocumentFactory.fromString(n,e,r).then(s=>(this.addDocument(s),s));{const s=this.langiumDocumentFactory.fromString(n,e);return this.addDocument(s),s}}hasDocument(e){return this.documentMap.has(e.toString())}invalidateDocument(e){const n=e.toString(),r=this.documentMap.get(n);return r&&(this.serviceRegistry.getServices(e).references.Linker.unlink(r),r.state=Ue.Changed,r.precomputedScopes=void 0,r.diagnostics=void 0),r}deleteDocument(e){const n=e.toString(),r=this.documentMap.get(n);return r&&(r.state=Ue.Changed,this.documentMap.delete(n)),r}}const Nn=Symbol("ref_resolving");class Fi{constructor(e){this.reflection=e.shared.AstReflection,this.langiumDocuments=()=>e.shared.workspace.LangiumDocuments,this.scopeProvider=e.references.ScopeProvider,this.astNodeLocator=e.workspace.AstNodeLocator}link(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){for(const s of(0,be.Zc)(e.parseResult.value))yield _t(n),(0,be.fy)(s).forEach(l=>this.doLink(l,e))})}doLink(e,n){var r;const s=e.reference;if(s._ref===void 0){s._ref=Nn;try{const l=this.getCandidate(e);if((0,We.et)(l))s._ref=l;else if(s._nodeDescription=l,this.langiumDocuments().hasDocument(l.documentUri)){const c=this.loadAstNode(l);s._ref=c!=null?c:this.createLinkingError(e,l)}else s._ref=void 0}catch(l){console.error(`An error occurred while resolving reference to '${s.$refText}':`,l);const c=(r=l.message)!==null&&r!==void 0?r:String(l);s._ref=Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${s.$refText}': ${c}`})}n.references.push(s)}}unlink(e){for(const n of e.references)delete n._ref,delete n._nodeDescription;e.references=[]}getCandidate(e){const r=this.scopeProvider.getScope(e).getElement(e.reference.$refText);return r!=null?r:this.createLinkingError(e)}buildReference(e,n,r,s){const l=this,c={$refNode:r,$refText:s,get ref(){var m;if((0,We.xA)(this._ref))return this._ref;if((0,We.SI)(this._nodeDescription)){const $=l.loadAstNode(this._nodeDescription);this._ref=$!=null?$:l.createLinkingError({reference:c,container:e,property:n},this._nodeDescription)}else if(this._ref===void 0){this._ref=Nn;const $=(0,be.E$)(e).$document,U=l.getLinkedNode({reference:c,container:e,property:n});if(U.error&&$&&$.state<Ue.ComputedScopes)return this._ref=void 0;this._ref=(m=U.node)!==null&&m!==void 0?m:U.error,this._nodeDescription=U.descr,$==null||$.references.push(this)}else if(this._ref===Nn)throw new Error(`Cyclic reference resolution detected: ${l.astNodeLocator.getAstNodePath(e)}/${n} (symbol '${s}')`);return(0,We.xA)(this._ref)?this._ref:void 0},get $nodeDescription(){return this._nodeDescription},get error(){return(0,We.et)(this._ref)?this._ref:void 0}};return c}getLinkedNode(e){var n;try{const r=this.getCandidate(e);if((0,We.et)(r))return{error:r};const s=this.loadAstNode(r);return s?{node:s,descr:r}:{descr:r,error:this.createLinkingError(e,r)}}catch(r){console.error(`An error occurred while resolving reference to '${e.reference.$refText}':`,r);const s=(n=r.message)!==null&&n!==void 0?n:String(r);return{error:Object.assign(Object.assign({},e),{message:`An error occurred while resolving reference to '${e.reference.$refText}': ${s}`})}}}loadAstNode(e){if(e.node)return e.node;const n=this.langiumDocuments().getDocument(e.documentUri);if(n)return this.astNodeLocator.getAstNode(n.parseResult.value,e.path)}createLinkingError(e,n){const r=(0,be.E$)(e.container).$document;r&&r.state<Ue.ComputedScopes&&console.warn(`Attempted reference resolution before document reached ComputedScopes state (${r.uri}).`);const s=this.reflection.getReferenceType(e);return Object.assign(Object.assign({},e),{message:`Could not resolve reference to ${s} named '${e.reference.$refText}'.`,targetDescription:n})}}function ni(i){return typeof i.name=="string"}class Ui{getName(e){if(ni(e))return e.name}getNameNode(e){return(0,O.vb)(e.$cstNode,"name")}}var Xt;(function(i){i.basename=Ot.c.basename,i.dirname=Ot.c.dirname,i.extname=Ot.c.extname,i.joinPath=Ot.c.joinPath,i.resolvePath=Ot.c.resolvePath;function e(s,l){return(s==null?void 0:s.toString())===(l==null?void 0:l.toString())}i.equals=e;function n(s,l){const c=typeof s=="string"?s:s.path,m=typeof l=="string"?l:l.path,$=c.split("/").filter(X=>X.length>0),U=m.split("/").filter(X=>X.length>0);let H=0;for(;H<$.length&&$[H]===U[H];H++);const ue="../".repeat($.length-H),ne=U.slice(H).join("/");return ue+ne}i.relative=n;function r(s){return Ot.o.parse(s.toString()).toString()}i.normalize=r})(Xt||(Xt={}));class zr{constructor(e){this.nameProvider=e.references.NameProvider,this.index=e.shared.workspace.IndexManager,this.nodeLocator=e.workspace.AstNodeLocator}findDeclaration(e){if(e){const n=(0,O.h7)(e),r=e.astNode;if(n&&r){const s=r[n.feature];if((0,We.Yk)(s))return s.ref;if(Array.isArray(s)){for(const l of s)if((0,We.Yk)(l)&&l.$refNode&&l.$refNode.offset<=e.offset&&l.$refNode.end>=e.end)return l.ref}}if(r){const s=this.nameProvider.getNameNode(r);if(s&&(s===e||(0,y.OB)(e,s)))return r}}}findDeclarationNode(e){const n=this.findDeclaration(e);if(n!=null&&n.$cstNode){const r=this.nameProvider.getNameNode(n);return r!=null?r:n.$cstNode}}findReferences(e,n){const r=[];if(n.includeDeclaration){const l=this.getReferenceToSelf(e);l&&r.push(l)}let s=this.index.findAllReferences(e,this.nodeLocator.getAstNodePath(e));return n.documentUri&&(s=s.filter(l=>Xt.equals(l.sourceUri,n.documentUri))),r.push(...s),(0,st.Vw)(r)}getReferenceToSelf(e){const n=this.nameProvider.getNameNode(e);if(n){const r=(0,be.Me)(e),s=this.nodeLocator.getAstNodePath(e);return{sourceUri:r.uri,sourcePath:s,targetUri:r.uri,targetPath:s,segment:(0,y.yn)(n),local:!0}}}}class rr{constructor(e){if(this.map=new Map,e)for(const[n,r]of e)this.add(n,r)}get size(){return st.IH.sum((0,st.Vw)(this.map.values()).map(e=>e.length))}clear(){this.map.clear()}delete(e,n){if(n===void 0)return this.map.delete(e);{const r=this.map.get(e);if(r){const s=r.indexOf(n);if(s>=0)return r.length===1?this.map.delete(e):r.splice(s,1),!0}return!1}}get(e){var n;return(n=this.map.get(e))!==null&&n!==void 0?n:[]}has(e,n){if(n===void 0)return this.map.has(e);{const r=this.map.get(e);return r?r.indexOf(n)>=0:!1}}add(e,n){return this.map.has(e)?this.map.get(e).push(n):this.map.set(e,[n]),this}addAll(e,n){return this.map.has(e)?this.map.get(e).push(...n):this.map.set(e,Array.from(n)),this}forEach(e){this.map.forEach((n,r)=>n.forEach(s=>e(s,r,this)))}[Symbol.iterator](){return this.entries().iterator()}entries(){return(0,st.Vw)(this.map.entries()).flatMap(([e,n])=>n.map(r=>[e,r]))}keys(){return(0,st.Vw)(this.map.keys())}values(){return(0,st.Vw)(this.map.values()).flat()}entriesGroupedByKey(){return(0,st.Vw)(this.map.entries())}}class ri{get size(){return this.map.size}constructor(e){if(this.map=new Map,this.inverse=new Map,e)for(const[n,r]of e)this.set(n,r)}clear(){this.map.clear(),this.inverse.clear()}set(e,n){return this.map.set(e,n),this.inverse.set(n,e),this}get(e){return this.map.get(e)}getKey(e){return this.inverse.get(e)}delete(e){const n=this.map.get(e);return n!==void 0?(this.map.delete(e),this.inverse.delete(n),!0):!1}}class Xe{constructor(e){this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider}computeExports(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){return this.computeExportsForNode(e.parseResult.value,e,void 0,n)})}computeExportsForNode(l,c){return Re(this,arguments,function*(e,n,r=be.sx,s=Ke.Ts.None){const m=[];this.exportNode(e,m,n);for(const $ of r(e))yield _t(s),this.exportNode($,m,n);return m})}exportNode(e,n,r){const s=this.nameProvider.getName(e);s&&n.push(this.descriptions.createDescription(e,s,r))}computeLocalScopes(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const s=e.parseResult.value,l=new rr;for(const c of(0,be.VY)(s))yield _t(n),this.processNode(c,e,l);return l})}processNode(e,n,r){const s=e.$container;if(s){const l=this.nameProvider.getName(e);l&&r.add(s,this.descriptions.createDescription(e,l,n))}}}class Er{constructor(e,n,r){var s;this.elements=e,this.outerScope=n,this.caseInsensitive=(s=r==null?void 0:r.caseInsensitive)!==null&&s!==void 0?s:!1}getAllElements(){return this.outerScope?this.elements.concat(this.outerScope.getAllElements()):this.elements}getElement(e){const n=this.caseInsensitive?this.elements.find(r=>r.name.toLowerCase()===e.toLowerCase()):this.elements.find(r=>r.name===e);if(n)return n;if(this.outerScope)return this.outerScope.getElement(e)}}class Gi{constructor(e,n,r){var s;this.elements=new Map,this.caseInsensitive=(s=r==null?void 0:r.caseInsensitive)!==null&&s!==void 0?s:!1;for(const l of e){const c=this.caseInsensitive?l.name.toLowerCase():l.name;this.elements.set(c,l)}this.outerScope=n}getElement(e){const n=this.caseInsensitive?e.toLowerCase():e,r=this.elements.get(n);if(r)return r;if(this.outerScope)return this.outerScope.getElement(e)}getAllElements(){let e=(0,st.Vw)(this.elements.values());return this.outerScope&&(e=e.concat(this.outerScope.getAllElements())),e}}const ga={getElement(){},getAllElements(){return st.Cl}};class si{constructor(){this.toDispose=[],this.isDisposed=!1}onDispose(e){this.toDispose.push(e)}dispose(){this.throwIfDisposed(),this.clear(),this.isDisposed=!0,this.toDispose.forEach(e=>e.dispose())}throwIfDisposed(){if(this.isDisposed)throw new Error("This cache has already been disposed")}}class Bi extends si{constructor(){super(...arguments),this.cache=new Map}has(e){return this.throwIfDisposed(),this.cache.has(e)}set(e,n){this.throwIfDisposed(),this.cache.set(e,n)}get(e,n){if(this.throwIfDisposed(),this.cache.has(e))return this.cache.get(e);if(n){const r=n();return this.cache.set(e,r),r}else return}delete(e){return this.throwIfDisposed(),this.cache.delete(e)}clear(){this.throwIfDisposed(),this.cache.clear()}}class Wi extends si{constructor(e){super(),this.cache=new Map,this.converter=e!=null?e:n=>n}has(e,n){return this.throwIfDisposed(),this.cacheForContext(e).has(n)}set(e,n,r){this.throwIfDisposed(),this.cacheForContext(e).set(n,r)}get(e,n,r){this.throwIfDisposed();const s=this.cacheForContext(e);if(s.has(n))return s.get(n);if(r){const l=r();return s.set(n,l),l}else return}delete(e,n){return this.throwIfDisposed(),this.cacheForContext(e).delete(n)}clear(e){if(this.throwIfDisposed(),e){const n=this.converter(e);this.cache.delete(n)}else this.cache.clear()}cacheForContext(e){const n=this.converter(e);let r=this.cache.get(n);return r||(r=new Map,this.cache.set(n,r)),r}}class ya extends null{constructor(e,n){super(r=>r.toString()),n?(this.toDispose.push(e.workspace.DocumentBuilder.onDocumentPhase(n,r=>{this.clear(r.uri.toString())})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,s)=>{for(const l of s)this.clear(l)}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,s)=>{const l=r.concat(s);for(const c of l)this.clear(c)}))}}class ii extends Bi{constructor(e,n){super(),n?(this.toDispose.push(e.workspace.DocumentBuilder.onBuildPhase(n,()=>{this.clear()})),this.toDispose.push(e.workspace.DocumentBuilder.onUpdate((r,s)=>{s.length>0&&this.clear()}))):this.toDispose.push(e.workspace.DocumentBuilder.onUpdate(()=>{this.clear()}))}}class ai{constructor(e){this.reflection=e.shared.AstReflection,this.nameProvider=e.references.NameProvider,this.descriptions=e.workspace.AstNodeDescriptionProvider,this.indexManager=e.shared.workspace.IndexManager,this.globalScopeCache=new ii(e.shared)}getScope(e){const n=[],r=this.reflection.getReferenceType(e),s=(0,be.Me)(e.container).precomputedScopes;if(s){let c=e.container;do{const m=s.get(c);m.length>0&&n.push((0,st.Vw)(m).filter($=>this.reflection.isSubtype($.type,r))),c=c.$container}while(c)}let l=this.getGlobalScope(r,e);for(let c=n.length-1;c>=0;c--)l=this.createScope(n[c],l);return l}createScope(e,n,r){return new Er((0,st.Vw)(e),n,r)}createScopeForNodes(e,n,r){const s=(0,st.Vw)(e).map(l=>{const c=this.nameProvider.getName(l);if(c)return this.descriptions.createDescription(l,c)}).nonNullable();return new Er(s,n,r)}getGlobalScope(e,n){return this.globalScopeCache.get(e,()=>new Gi(this.indexManager.allElements(e)))}}function Yr(i){return typeof i.$comment=="string"}function oi(i){return typeof i=="object"&&!!i&&("$ref"in i||"$error"in i)}class li{constructor(e){this.ignoreProperties=new Set(["$container","$containerProperty","$containerIndex","$document","$cstNode"]),this.langiumDocuments=e.shared.workspace.LangiumDocuments,this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider,this.commentProvider=e.documentation.CommentProvider}serialize(e,n){const r=n!=null?n:{},s=n==null?void 0:n.replacer,l=(m,$)=>this.replacer(m,$,r),c=s?(m,$)=>s(m,$,l):l;try{return this.currentDocument=(0,be.Me)(e),JSON.stringify(e,c,n==null?void 0:n.space)}finally{this.currentDocument=void 0}}deserialize(e,n){const r=n!=null?n:{},s=JSON.parse(e);return this.linkNode(s,s,r),s}replacer(e,n,{refText:r,sourceText:s,textRegions:l,comments:c,uriConverter:m}){var $,U,H,ue;if(!this.ignoreProperties.has(e))if((0,We.Yk)(n)){const ne=n.ref,X=r?n.$refText:void 0;if(ne){const De=(0,be.Me)(ne);let Ve="";this.currentDocument&&this.currentDocument!==De&&(m?Ve=m(De.uri,n):Ve=De.uri.toString());const tt=this.astNodeLocator.getAstNodePath(ne);return{$ref:`${Ve}#${tt}`,$refText:X}}else return{$error:(U=($=n.error)===null||$===void 0?void 0:$.message)!==null&&U!==void 0?U:"Could not resolve reference",$refText:X}}else if((0,We.xA)(n)){let ne;if(l&&(ne=this.addAstNodeRegionWithAssignmentsTo(Object.assign({},n)),(!e||n.$document)&&(ne!=null&&ne.$textRegion)&&(ne.$textRegion.documentURI=(H=this.currentDocument)===null||H===void 0?void 0:H.uri.toString())),s&&!e&&(ne!=null||(ne=Object.assign({},n)),ne.$sourceText=(ue=n.$cstNode)===null||ue===void 0?void 0:ue.text),c){ne!=null||(ne=Object.assign({},n));const X=this.commentProvider.getComment(n);X&&(ne.$comment=X.replace(/\r/g,""))}return ne!=null?ne:n}else return n}addAstNodeRegionWithAssignmentsTo(e){const n=r=>({offset:r.offset,end:r.end,length:r.length,range:r.range});if(e.$cstNode){const r=e.$textRegion=n(e.$cstNode),s=r.assignments={};return Object.keys(e).filter(l=>!l.startsWith("$")).forEach(l=>{const c=(0,O.EL)(e.$cstNode,l).map(n);c.length!==0&&(s[l]=c)}),e}}linkNode(e,n,r,s,l,c){for(const[$,U]of Object.entries(e))if(Array.isArray(U))for(let H=0;H<U.length;H++){const ue=U[H];oi(ue)?U[H]=this.reviveReference(e,$,n,ue,r):(0,We.xA)(ue)&&this.linkNode(ue,n,r,e,$,H)}else oi(U)?e[$]=this.reviveReference(e,$,n,U,r):(0,We.xA)(U)&&this.linkNode(U,n,r,e,$);const m=e;m.$container=s,m.$containerProperty=l,m.$containerIndex=c}reviveReference(e,n,r,s,l){let c=s.$refText,m=s.$error;if(s.$ref){const $=this.getRefNode(r,s.$ref,l.uriConverter);if((0,We.xA)($))return c||(c=this.nameProvider.getName($)),{$refText:c!=null?c:"",ref:$};m=$}if(m){const $={$refText:c!=null?c:""};return $.error={container:e,property:n,message:m,reference:$},$}else return}getRefNode(e,n,r){try{const s=n.indexOf("#");if(s===0){const $=this.astNodeLocator.getAstNode(e,n.substring(1));return $||"Could not resolve path: "+n}if(s<0){const $=r?r(n):Ot.o.parse(n),U=this.langiumDocuments.getDocument($);return U?U.parseResult.value:"Could not find document for URI: "+n}const l=r?r(n.substring(0,s)):Ot.o.parse(n.substring(0,s)),c=this.langiumDocuments.getDocument(l);if(!c)return"Could not find document for URI: "+n;if(s===n.length-1)return c.parseResult.value;const m=this.astNodeLocator.getAstNode(c.parseResult.value,n.substring(s+1));return m||"Could not resolve URI: "+n}catch(s){return String(s)}}}class Ar{get map(){return this.fileExtensionMap}constructor(e){this.languageIdMap=new Map,this.fileExtensionMap=new Map,this.textDocuments=e==null?void 0:e.workspace.TextDocuments}register(e){const n=e.LanguageMetaData;for(const r of n.fileExtensions)this.fileExtensionMap.has(r)&&console.warn(`The file extension ${r} is used by multiple languages. It is now assigned to '${n.languageId}'.`),this.fileExtensionMap.set(r,e);this.languageIdMap.set(n.languageId,e),this.languageIdMap.size===1?this.singleton=e:this.singleton=void 0}getServices(e){var n,r;if(this.singleton!==void 0)return this.singleton;if(this.languageIdMap.size===0)throw new Error("The service registry is empty. Use `register` to register the services of a language.");const s=(r=(n=this.textDocuments)===null||n===void 0?void 0:n.get(e))===null||r===void 0?void 0:r.languageId;if(s!==void 0){const m=this.languageIdMap.get(s);if(m)return m}const l=Xt.extname(e),c=this.fileExtensionMap.get(l);if(!c)throw s?new Error(`The service registry contains no services for the extension '${l}' for language '${s}'.`):new Error(`The service registry contains no services for the extension '${l}'.`);return c}hasServices(e){try{return this.getServices(e),!0}catch(n){return!1}}get all(){return Array.from(this.languageIdMap.values())}}function an(i){return{code:i}}var wn;(function(i){i.all=["fast","slow","built-in"]})(wn||(wn={}));class Ki{constructor(e){this.entries=new rr,this.entriesBefore=[],this.entriesAfter=[],this.reflection=e.shared.AstReflection}register(e,n=this,r="fast"){if(r==="built-in")throw new Error("The 'built-in' category is reserved for lexer, parser, and linker errors.");for(const[s,l]of Object.entries(e)){const c=l;if(Array.isArray(c))for(const m of c){const $={check:this.wrapValidationException(m,n),category:r};this.addEntry(s,$)}else if(typeof c=="function"){const m={check:this.wrapValidationException(c,n),category:r};this.addEntry(s,m)}else(0,xn.U)(c)}}wrapValidationException(e,n){return(r,s,l)=>Re(this,null,function*(){yield this.handleException(()=>e.call(n,r,s,l),"An error occurred during validation",s,r)})}handleException(e,n,r,s){return Re(this,null,function*(){try{yield e()}catch(l){if(Rr(l))throw l;console.error(`${n}:`,l),l instanceof Error&&l.stack&&console.error(l.stack);const c=l instanceof Error?l.message:String(l);r("error",`${n}: ${c}`,{node:s})}})}addEntry(e,n){if(e==="AstNode"){this.entries.add("AstNode",n);return}for(const r of this.reflection.getAllSubTypes(e))this.entries.add(r,n)}getChecks(e,n){let r=(0,st.Vw)(this.entries.get(e)).concat(this.entries.get("AstNode"));return n&&(r=r.filter(s=>n.includes(s.category))),r.map(s=>s.check)}registerBeforeDocument(e,n=this){this.entriesBefore.push(this.wrapPreparationException(e,"An error occurred during set-up of the validation",n))}registerAfterDocument(e,n=this){this.entriesAfter.push(this.wrapPreparationException(e,"An error occurred during tear-down of the validation",n))}wrapPreparationException(e,n,r){return(s,l,c,m)=>Re(this,null,function*(){yield this.handleException(()=>e.call(r,s,l,c,m),n,l,s)})}get checksBefore(){return this.entriesBefore}get checksAfter(){return this.entriesAfter}}class ui{constructor(e){this.validationRegistry=e.validation.ValidationRegistry,this.metadata=e.LanguageMetaData}validateDocument(s){return Re(this,arguments,function*(e,n={},r=Ke.Ts.None){const l=e.parseResult,c=[];if(yield _t(r),(!n.categories||n.categories.includes("built-in"))&&(this.processLexingErrors(l,c,n),n.stopAfterLexingErrors&&c.some(m=>{var $;return(($=m.data)===null||$===void 0?void 0:$.code)===Pt.LexingError})||(this.processParsingErrors(l,c,n),n.stopAfterParsingErrors&&c.some(m=>{var $;return(($=m.data)===null||$===void 0?void 0:$.code)===Pt.ParsingError}))||(this.processLinkingErrors(e,c,n),n.stopAfterLinkingErrors&&c.some(m=>{var $;return(($=m.data)===null||$===void 0?void 0:$.code)===Pt.LinkingError}))))return c;try{c.push(...yield this.validateAst(l.value,n,r))}catch(m){if(Rr(m))throw m;console.error("An error occurred during validation:",m)}return yield _t(r),c})}processLexingErrors(e,n,r){var s,l,c;const m=[...e.lexerErrors,...(l=(s=e.lexerReport)===null||s===void 0?void 0:s.diagnostics)!==null&&l!==void 0?l:[]];for(const $ of m){const U=(c=$.severity)!==null&&c!==void 0?c:"error",H={severity:Xr(U),range:{start:{line:$.line-1,character:$.column-1},end:{line:$.line-1,character:$.column+$.length-1}},message:$.message,data:ji(U),source:this.getSource()};n.push(H)}}processParsingErrors(e,n,r){for(const s of e.parserErrors){let l;if(isNaN(s.token.startOffset)){if("previousToken"in s){const c=s.previousToken;if(isNaN(c.startOffset)){const m={line:0,character:0};l={start:m,end:m}}else{const m={line:c.endLine-1,character:c.endColumn};l={start:m,end:m}}}}else l=(0,y.sp)(s.token);if(l){const c={severity:Xr("error"),range:l,message:s.message,data:an(Pt.ParsingError),source:this.getSource()};n.push(c)}}}processLinkingErrors(e,n,r){for(const s of e.references){const l=s.error;if(l){const c={node:l.container,property:l.property,index:l.index,data:{code:Pt.LinkingError,containerType:l.container.$type,property:l.property,refText:l.reference.$refText}};n.push(this.toDiagnostic("error",l.message,c))}}}validateAst(s,l){return Re(this,arguments,function*(e,n,r=Ke.Ts.None){const c=[],m=($,U,H)=>{c.push(this.toDiagnostic($,U,H))};return yield this.validateAstBefore(e,n,m,r),yield this.validateAstNodes(e,n,m,r),yield this.validateAstAfter(e,n,m,r),c})}validateAstBefore(l,c,m){return Re(this,arguments,function*(e,n,r,s=Ke.Ts.None){var $;const U=this.validationRegistry.checksBefore;for(const H of U)yield _t(s),yield H(e,r,($=n.categories)!==null&&$!==void 0?$:[],s)})}validateAstNodes(l,c,m){return Re(this,arguments,function*(e,n,r,s=Ke.Ts.None){yield Promise.all((0,be.Zc)(e).map($=>Re(this,null,function*(){yield _t(s);const U=this.validationRegistry.getChecks($.$type,n.categories);for(const H of U)yield H($,r,s)})))})}validateAstAfter(l,c,m){return Re(this,arguments,function*(e,n,r,s=Ke.Ts.None){var $;const U=this.validationRegistry.checksAfter;for(const H of U)yield _t(s),yield H(e,r,($=n.categories)!==null&&$!==void 0?$:[],s)})}toDiagnostic(e,n,r){return{message:n,range:Vi(r),severity:Xr(e),code:r.code,codeDescription:r.codeDescription,tags:r.tags,relatedInformation:r.relatedInformation,data:r.data,source:this.getSource()}}getSource(){return this.metadata.languageId}}function Vi(i){if(i.range)return i.range;let e;return typeof i.property=="string"?e=(0,O.vb)(i.node.$cstNode,i.property,i.index):typeof i.keyword=="string"&&(e=(0,O.lA)(i.node.$cstNode,i.keyword,i.index)),e!=null||(e=i.node.$cstNode),e?e.range:{start:{line:0,character:0},end:{line:0,character:0}}}function Xr(i){switch(i){case"error":return 1;case"warning":return 2;case"info":return 3;case"hint":return 4;default:throw new Error("Invalid diagnostic severity: "+i)}}function ji(i){switch(i){case"error":return an(Pt.LexingError);case"warning":return an(Pt.LexingWarning);case"info":return an(Pt.LexingInfo);case"hint":return an(Pt.LexingHint);default:throw new Error("Invalid diagnostic severity: "+i)}}var Pt;(function(i){i.LexingError="lexing-error",i.LexingWarning="lexing-warning",i.LexingInfo="lexing-info",i.LexingHint="lexing-hint",i.ParsingError="parsing-error",i.LinkingError="linking-error"})(Pt||(Pt={}));class ci{constructor(e){this.astNodeLocator=e.workspace.AstNodeLocator,this.nameProvider=e.references.NameProvider}createDescription(e,n,r){const s=r!=null?r:(0,be.Me)(e);n!=null||(n=this.nameProvider.getName(e));const l=this.astNodeLocator.getAstNodePath(e);if(!n)throw new Error(`Node at path ${l} has no name.`);let c;const m=()=>{var $;return c!=null?c:c=(0,y.yn)(($=this.nameProvider.getNameNode(e))!==null&&$!==void 0?$:e.$cstNode)};return{node:e,name:n,get nameSegment(){return m()},selectionSegment:(0,y.yn)(e.$cstNode),type:e.$type,documentUri:s.uri,path:l}}}class Hi{constructor(e){this.nodeLocator=e.workspace.AstNodeLocator}createDescriptions(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const s=[],l=e.parseResult.value;for(const c of(0,be.Zc)(l))yield _t(n),(0,be.fy)(c).filter(m=>!(0,We.et)(m)).forEach(m=>{const $=this.createDescription(m);$&&s.push($)});return s})}createDescription(e){const n=e.reference.$nodeDescription,r=e.reference.$refNode;if(!n||!r)return;const s=(0,be.Me)(e.container).uri;return{sourceUri:s,sourcePath:this.nodeLocator.getAstNodePath(e.container),targetUri:n.documentUri,targetPath:n.path,segment:(0,y.yn)(r),local:Xt.equals(n.documentUri,s)}}}class zi{constructor(){this.segmentSeparator="/",this.indexSeparator="@"}getAstNodePath(e){if(e.$container){const n=this.getAstNodePath(e.$container),r=this.getPathSegment(e);return n+this.segmentSeparator+r}return""}getPathSegment({$containerProperty:e,$containerIndex:n}){if(!e)throw new Error("Missing '$containerProperty' in AST node.");return n!==void 0?e+this.indexSeparator+n:e}getAstNode(e,n){return n.split(this.segmentSeparator).reduce((s,l)=>{if(!s||l.length===0)return s;const c=l.indexOf(this.indexSeparator);if(c>0){const m=l.substring(0,c),$=parseInt(l.substring(c+1)),U=s[m];return U==null?void 0:U[$]}return s[l]},e)}}var Yi=g(32650);class di{constructor(e){this._ready=new Vr,this.settings={},this.workspaceConfig=!1,this.onConfigurationSectionUpdateEmitter=new Yi.Emitter,this.serviceRegistry=e.ServiceRegistry}get ready(){return this._ready.promise}initialize(e){var n,r;this.workspaceConfig=(r=(n=e.capabilities.workspace)===null||n===void 0?void 0:n.configuration)!==null&&r!==void 0?r:!1}initialized(e){return Re(this,null,function*(){if(this.workspaceConfig){if(e.register){const n=this.serviceRegistry.all;e.register({section:n.map(r=>this.toSectionName(r.LanguageMetaData.languageId))})}if(e.fetchConfiguration){const n=this.serviceRegistry.all.map(s=>({section:this.toSectionName(s.LanguageMetaData.languageId)})),r=yield e.fetchConfiguration(n);n.forEach((s,l)=>{this.updateSectionConfiguration(s.section,r[l])})}}this._ready.resolve()})}updateConfiguration(e){e.settings&&Object.keys(e.settings).forEach(n=>{const r=e.settings[n];this.updateSectionConfiguration(n,r),this.onConfigurationSectionUpdateEmitter.fire({section:n,configuration:r})})}updateSectionConfiguration(e,n){this.settings[e]=n}getConfiguration(e,n){return Re(this,null,function*(){yield this.ready;const r=this.toSectionName(e);if(this.settings[r])return this.settings[r][n]})}toSectionName(e){return`${e}`}get onConfigurationSectionUpdate(){return this.onConfigurationSectionUpdateEmitter.event}}var on;(function(i){function e(n){return{dispose:()=>Re(this,null,function*(){return yield n()})}}i.create=e})(on||(on={}));class Jr{constructor(e){this.updateBuildOptions={validation:{categories:["built-in","fast"]}},this.updateListeners=[],this.buildPhaseListeners=new rr,this.documentPhaseListeners=new rr,this.buildState=new Map,this.documentBuildWaiters=new Map,this.currentState=Ue.Changed,this.langiumDocuments=e.workspace.LangiumDocuments,this.langiumDocumentFactory=e.workspace.LangiumDocumentFactory,this.textDocuments=e.workspace.TextDocuments,this.indexManager=e.workspace.IndexManager,this.serviceRegistry=e.ServiceRegistry}build(s){return Re(this,arguments,function*(e,n={},r=Ke.Ts.None){var l,c;for(const m of e){const $=m.uri.toString();if(m.state===Ue.Validated){if(typeof n.validation=="boolean"&&n.validation)m.state=Ue.IndexedReferences,m.diagnostics=void 0,this.buildState.delete($);else if(typeof n.validation=="object"){const U=this.buildState.get($),H=(l=U==null?void 0:U.result)===null||l===void 0?void 0:l.validationChecks;if(H){const ne=((c=n.validation.categories)!==null&&c!==void 0?c:wn.all).filter(X=>!H.includes(X));ne.length>0&&(this.buildState.set($,{completed:!1,options:{validation:Object.assign(Object.assign({},n.validation),{categories:ne})},result:U.result}),m.state=Ue.IndexedReferences)}}}else this.buildState.delete($)}this.currentState=Ue.Changed,yield this.emitUpdate(e.map(m=>m.uri),[]),yield this.buildDocuments(e,n,r)})}update(s,l){return Re(this,arguments,function*(e,n,r=Ke.Ts.None){this.currentState=Ue.Changed;for(const $ of n)this.langiumDocuments.deleteDocument($),this.buildState.delete($.toString()),this.indexManager.remove($);for(const $ of e){if(!this.langiumDocuments.invalidateDocument($)){const H=this.langiumDocumentFactory.fromModel({$type:"INVALID"},$);H.state=Ue.Changed,this.langiumDocuments.addDocument(H)}this.buildState.delete($.toString())}const c=(0,st.Vw)(e).concat(n).map($=>$.toString()).toSet();this.langiumDocuments.all.filter($=>!c.has($.uri.toString())&&this.shouldRelink($,c)).forEach($=>{this.serviceRegistry.getServices($.uri).references.Linker.unlink($),$.state=Math.min($.state,Ue.ComputedScopes),$.diagnostics=void 0}),yield this.emitUpdate(e,n),yield _t(r);const m=this.sortDocuments(this.langiumDocuments.all.filter($=>{var U;return $.state<Ue.Linked||!(!((U=this.buildState.get($.uri.toString()))===null||U===void 0)&&U.completed)}).toArray());yield this.buildDocuments(m,this.updateBuildOptions,r)})}emitUpdate(e,n){return Re(this,null,function*(){yield Promise.all(this.updateListeners.map(r=>r(e,n)))})}sortDocuments(e){let n=0,r=e.length-1;for(;n<r;){for(;n<e.length&&this.hasTextDocument(e[n]);)n++;for(;r>=0&&!this.hasTextDocument(e[r]);)r--;n<r&&([e[n],e[r]]=[e[r],e[n]])}return e}hasTextDocument(e){var n;return!!(!((n=this.textDocuments)===null||n===void 0)&&n.get(e.uri))}shouldRelink(e,n){return e.references.some(r=>r.error!==void 0)?!0:this.indexManager.isAffected(e,n)}onUpdate(e){return this.updateListeners.push(e),on.create(()=>{const n=this.updateListeners.indexOf(e);n>=0&&this.updateListeners.splice(n,1)})}buildDocuments(e,n,r){return Re(this,null,function*(){this.prepareBuild(e,n),yield this.runCancelable(e,Ue.Parsed,r,l=>this.langiumDocumentFactory.update(l,r)),yield this.runCancelable(e,Ue.IndexedContent,r,l=>this.indexManager.updateContent(l,r)),yield this.runCancelable(e,Ue.ComputedScopes,r,l=>Re(this,null,function*(){const c=this.serviceRegistry.getServices(l.uri).references.ScopeComputation;l.precomputedScopes=yield c.computeLocalScopes(l,r)})),yield this.runCancelable(e,Ue.Linked,r,l=>this.serviceRegistry.getServices(l.uri).references.Linker.link(l,r)),yield this.runCancelable(e,Ue.IndexedReferences,r,l=>this.indexManager.updateReferences(l,r));const s=e.filter(l=>this.shouldValidate(l));yield this.runCancelable(s,Ue.Validated,r,l=>this.validate(l,r));for(const l of e){const c=this.buildState.get(l.uri.toString());c&&(c.completed=!0)}})}prepareBuild(e,n){for(const r of e){const s=r.uri.toString(),l=this.buildState.get(s);(!l||l.completed)&&this.buildState.set(s,{completed:!1,options:n,result:l==null?void 0:l.result})}}runCancelable(e,n,r,s){return Re(this,null,function*(){const l=e.filter(m=>m.state<n);for(const m of l)yield _t(r),yield s(m),m.state=n,yield this.notifyDocumentPhase(m,n,r);const c=e.filter(m=>m.state===n);yield this.notifyBuildPhase(c,n,r),this.currentState=n})}onBuildPhase(e,n){return this.buildPhaseListeners.add(e,n),on.create(()=>{this.buildPhaseListeners.delete(e,n)})}onDocumentPhase(e,n){return this.documentPhaseListeners.add(e,n),on.create(()=>{this.documentPhaseListeners.delete(e,n)})}waitUntil(e,n,r){let s;if(n&&"path"in n?s=n:r=n,r!=null||(r=Ke.Ts.None),s){const l=this.langiumDocuments.getDocument(s);if(l&&l.state>e)return Promise.resolve(s)}return this.currentState>=e?Promise.resolve(void 0):r.isCancellationRequested?Promise.reject(vr):new Promise((l,c)=>{const m=this.onBuildPhase(e,()=>{if(m.dispose(),$.dispose(),s){const U=this.langiumDocuments.getDocument(s);l(U==null?void 0:U.uri)}else l(void 0)}),$=r.onCancellationRequested(()=>{m.dispose(),$.dispose(),c(vr)})})}notifyDocumentPhase(e,n,r){return Re(this,null,function*(){const l=this.documentPhaseListeners.get(n).slice();for(const c of l)try{yield c(e,r)}catch(m){if(!Rr(m))throw m}})}notifyBuildPhase(e,n,r){return Re(this,null,function*(){if(e.length===0)return;const l=this.buildPhaseListeners.get(n).slice();for(const c of l)yield _t(r),yield c(e,r)})}shouldValidate(e){return!!this.getBuildOptions(e).validation}validate(e,n){return Re(this,null,function*(){var r,s;const l=this.serviceRegistry.getServices(e.uri).validation.DocumentValidator,c=this.getBuildOptions(e).validation,m=typeof c=="object"?c:void 0,$=yield l.validateDocument(e,m,n);e.diagnostics?e.diagnostics.push(...$):e.diagnostics=$;const U=this.buildState.get(e.uri.toString());if(U){(r=U.result)!==null&&r!==void 0||(U.result={});const H=(s=m==null?void 0:m.categories)!==null&&s!==void 0?s:wn.all;U.result.validationChecks?U.result.validationChecks.push(...H):U.result.validationChecks=[...H]}})}getBuildOptions(e){var n,r;return(r=(n=this.buildState.get(e.uri.toString()))===null||n===void 0?void 0:n.options)!==null&&r!==void 0?r:{}}}class Xi{constructor(e){this.symbolIndex=new Map,this.symbolByTypeIndex=new Wi,this.referenceIndex=new Map,this.documents=e.workspace.LangiumDocuments,this.serviceRegistry=e.ServiceRegistry,this.astReflection=e.AstReflection}findAllReferences(e,n){const r=(0,be.Me)(e).uri,s=[];return this.referenceIndex.forEach(l=>{l.forEach(c=>{Xt.equals(c.targetUri,r)&&c.targetPath===n&&s.push(c)})}),(0,st.Vw)(s)}allElements(e,n){let r=(0,st.Vw)(this.symbolIndex.keys());return n&&(r=r.filter(s=>!n||n.has(s))),r.map(s=>this.getFileDescriptions(s,e)).flat()}getFileDescriptions(e,n){var r;return n?this.symbolByTypeIndex.get(e,n,()=>{var l;return((l=this.symbolIndex.get(e))!==null&&l!==void 0?l:[]).filter(m=>this.astReflection.isSubtype(m.type,n))}):(r=this.symbolIndex.get(e))!==null&&r!==void 0?r:[]}remove(e){const n=e.toString();this.symbolIndex.delete(n),this.symbolByTypeIndex.clear(n),this.referenceIndex.delete(n)}updateContent(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const l=yield this.serviceRegistry.getServices(e.uri).references.ScopeComputation.computeExports(e,n),c=e.uri.toString();this.symbolIndex.set(c,l),this.symbolByTypeIndex.clear(c)})}updateReferences(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const l=yield this.serviceRegistry.getServices(e.uri).workspace.ReferenceDescriptionProvider.createDescriptions(e,n);this.referenceIndex.set(e.uri.toString(),l)})}isAffected(e,n){const r=this.referenceIndex.get(e.uri.toString());return r?r.some(s=>!s.local&&n.has(s.targetUri.toString())):!1}}class Ji{constructor(e){this.initialBuildOptions={},this._ready=new Vr,this.serviceRegistry=e.ServiceRegistry,this.langiumDocuments=e.workspace.LangiumDocuments,this.documentBuilder=e.workspace.DocumentBuilder,this.fileSystemProvider=e.workspace.FileSystemProvider,this.mutex=e.workspace.WorkspaceLock}get ready(){return this._ready.promise}get workspaceFolders(){return this.folders}initialize(e){var n;this.folders=(n=e.workspaceFolders)!==null&&n!==void 0?n:void 0}initialized(e){return this.mutex.write(n=>{var r;return this.initializeWorkspace((r=this.folders)!==null&&r!==void 0?r:[],n)})}initializeWorkspace(r){return Re(this,arguments,function*(e,n=Ke.Ts.None){const s=yield this.performStartup(e);yield _t(n),yield this.documentBuilder.build(s,this.initialBuildOptions,n)})}performStartup(e){return Re(this,null,function*(){const n=this.serviceRegistry.all.flatMap(l=>l.LanguageMetaData.fileExtensions),r=[],s=l=>{r.push(l),this.langiumDocuments.hasDocument(l.uri)||this.langiumDocuments.addDocument(l)};return yield this.loadAdditionalDocuments(e,s),yield Promise.all(e.map(l=>[l,this.getRootFolder(l)]).map(l=>Re(this,null,function*(){return this.traverseFolder(...l,n,s)}))),this._ready.resolve(),r})}loadAdditionalDocuments(e,n){return Promise.resolve()}getRootFolder(e){return Ot.o.parse(e.uri)}traverseFolder(e,n,r,s){return Re(this,null,function*(){const l=yield this.fileSystemProvider.readDirectory(n);yield Promise.all(l.map(c=>Re(this,null,function*(){if(this.includeEntry(e,c,r)){if(c.isDirectory)yield this.traverseFolder(e,c.uri,r,s);else if(c.isFile){const m=yield this.langiumDocuments.getOrCreateDocument(c.uri);s(m)}}})))})}includeEntry(e,n,r){const s=Xt.basename(n.uri);if(s.startsWith("."))return!1;if(n.isDirectory)return s!=="node_modules"&&s!=="out";if(n.isFile){const l=Xt.extname(n.uri);return r.includes(l)}return!1}}class Qi{buildUnexpectedCharactersMessage(e,n,r,s,l){return b.ZW.buildUnexpectedCharactersMessage(e,n,r,s,l)}buildUnableToPopLexerModeMessage(e){return b.ZW.buildUnableToPopLexerModeMessage(e)}}const qi={mode:"full"};class ea{constructor(e){this.errorMessageProvider=e.parser.LexerErrorMessageProvider,this.tokenBuilder=e.parser.TokenBuilder;const n=this.tokenBuilder.buildTokens(e.Grammar,{caseInsensitive:e.LanguageMetaData.caseInsensitive});this.tokenTypes=this.toTokenTypeDictionary(n);const r=hi(n)?Object.values(n):n,s=e.LanguageMetaData.mode==="production";this.chevrotainLexer=new b.hW(r,{positionTracking:"full",skipValidations:s,errorMessageProvider:this.errorMessageProvider})}get definition(){return this.tokenTypes}tokenize(e,n=qi){var r,s,l;const c=this.chevrotainLexer.tokenize(e);return{tokens:c.tokens,errors:c.errors,hidden:(r=c.groups.hidden)!==null&&r!==void 0?r:[],report:(l=(s=this.tokenBuilder).flushLexingReport)===null||l===void 0?void 0:l.call(s,e)}}toTokenTypeDictionary(e){if(hi(e))return e;const n=fi(e)?Object.values(e.modes).flat():e,r={};return n.forEach(s=>r[s.name]=s),r}}function ta(i){return Array.isArray(i)&&(i.length===0||"name"in i[0])}function fi(i){return i&&"modes"in i&&"defaultMode"in i}function hi(i){return!ta(i)&&!fi(i)}function na(i,e,n){let r,s;typeof i=="string"?(s=e,r=n):(s=i.range.start,r=e),s||(s=Ne.create(0,0));const l=Qr(i),c=ns(r),m=mi({lines:l,position:s,options:c});return ia({index:0,tokens:m,position:s})}function ra(i,e){const n=ns(e),r=Qr(i);if(r.length===0)return!1;const s=r[0],l=r[r.length-1],c=n.start,m=n.end;return!!(c!=null&&c.exec(s))&&!!(m!=null&&m.exec(l))}function Qr(i){let e="";return typeof i=="string"?e=i:e=i.text,e.split(F.K0)}const qr=/\s*(@([\p{L}][\p{L}\p{N}]*)?)/uy,pi=/\{(@[\p{L}][\p{L}\p{N}]*)(\s*)([^\r\n}]+)?\}/gu;function mi(i){var e,n,r;const s=[];let l=i.position.line,c=i.position.character;for(let m=0;m<i.lines.length;m++){const $=m===0,U=m===i.lines.length-1;let H=i.lines[m],ue=0;if($&&i.options.start){const X=(e=i.options.start)===null||e===void 0?void 0:e.exec(H);X&&(ue=X.index+X[0].length)}else{const X=(n=i.options.line)===null||n===void 0?void 0:n.exec(H);X&&(ue=X.index+X[0].length)}if(U){const X=(r=i.options.end)===null||r===void 0?void 0:r.exec(H);X&&(H=H.substring(0,X.index))}if(H=H.substring(0,sa(H)),Ir(H,ue)>=H.length){if(s.length>0){const X=Ne.create(l,c);s.push({type:"break",content:"",range:ee.create(X,X)})}}else{qr.lastIndex=ue;const X=qr.exec(H);if(X){const De=X[0],Ve=X[1],tt=Ne.create(l,c+ue),Mt=Ne.create(l,c+ue+De.length);s.push({type:"tag",content:Ve,range:ee.create(tt,Mt)}),ue+=De.length,ue=Ir(H,ue)}if(ue<H.length){const De=H.substring(ue),Ve=Array.from(De.matchAll(pi));s.push(...gi(Ve,De,l,c+ue))}}l++,c=0}return s.length>0&&s[s.length-1].type==="break"?s.slice(0,-1):s}function gi(i,e,n,r){const s=[];if(i.length===0){const l=Ne.create(n,r),c=Ne.create(n,r+e.length);s.push({type:"text",content:e,range:ee.create(l,c)})}else{let l=0;for(const m of i){const $=m.index,U=e.substring(l,$);U.length>0&&s.push({type:"text",content:e.substring(l,$),range:ee.create(Ne.create(n,l+r),Ne.create(n,$+r))});let H=U.length+1;const ue=m[1];if(s.push({type:"inline-tag",content:ue,range:ee.create(Ne.create(n,l+H+r),Ne.create(n,l+H+ue.length+r))}),H+=ue.length,m.length===4){H+=m[2].length;const ne=m[3];s.push({type:"text",content:ne,range:ee.create(Ne.create(n,l+H+r),Ne.create(n,l+H+ne.length+r))})}else s.push({type:"text",content:"",range:ee.create(Ne.create(n,l+H+r),Ne.create(n,l+H+r))});l=$+m[0].length}const c=e.substring(l);c.length>0&&s.push({type:"text",content:c,range:ee.create(Ne.create(n,l+r),Ne.create(n,l+r+c.length))})}return s}const _r=/\S/,$r=/\s*$/;function Ir(i,e){const n=i.substring(e).match(_r);return n?e+n.index:i.length}function sa(i){const e=i.match($r);if(e&&typeof e.index=="number")return e.index}function ia(i){var e,n,r,s;const l=Ne.create(i.position.line,i.position.character);if(i.tokens.length===0)return new la([],ee.create(l,l));const c=[];for(;i.index<i.tokens.length;){const U=aa(i,c[c.length-1]);U&&c.push(U)}const m=(n=(e=c[0])===null||e===void 0?void 0:e.range.start)!==null&&n!==void 0?n:l,$=(s=(r=c[c.length-1])===null||r===void 0?void 0:r.range.end)!==null&&s!==void 0?s:l;return new la(c,ee.create(m,$))}function aa(i,e){const n=i.tokens[i.index];if(n.type==="tag")return yi(i,!1);if(n.type==="text"||n.type==="inline-tag")return ts(i);es(n,e),i.index++}function es(i,e){if(e){const n=new Cr("",i.range);"inlines"in e?e.inlines.push(n):e.content.inlines.push(n)}}function ts(i){let e=i.tokens[i.index];const n=e;let r=e;const s=[];for(;e&&e.type!=="break"&&e.type!=="tag";)s.push(oa(i)),r=e,e=i.tokens[i.index];return new sr(s,ee.create(n.range.start,r.range.end))}function oa(i){return i.tokens[i.index].type==="inline-tag"?yi(i,!0):Ti(i)}function yi(i,e){const n=i.tokens[i.index++],r=n.content.substring(1),s=i.tokens[i.index];if((s==null?void 0:s.type)==="text")if(e){const l=Ti(i);return new rs(r,new sr([l],l.range),e,ee.create(n.range.start,l.range.end))}else{const l=ts(i);return new rs(r,l,e,ee.create(n.range.start,l.range.end))}else{const l=n.range;return new rs(r,new sr([],l),e,l)}}function Ti(i){const e=i.tokens[i.index++];return new Cr(e.content,e.range)}function ns(i){if(!i)return ns({start:"/**",end:"*/",line:"*"});const{start:e,end:n,line:r}=i;return{start:Ut(e,!0),end:Ut(n,!1),line:Ut(r,!0)}}function Ut(i,e){if(typeof i=="string"||typeof i=="object"){const n=typeof i=="string"?(0,F.hr)(i):i.source;return e?new RegExp(`^\\s*${n}`):new RegExp(`\\s*${n}\\s*$`)}else return i}class la{constructor(e,n){this.elements=e,this.range=n}getTag(e){return this.getAllTags().find(n=>n.name===e)}getTags(e){return this.getAllTags().filter(n=>n.name===e)}getAllTags(){return this.elements.filter(e=>"name"in e)}toString(){let e="";for(const n of this.elements)if(e.length===0)e=n.toString();else{const r=n.toString();e+=kr(e)+r}return e.trim()}toMarkdown(e){let n="";for(const r of this.elements)if(n.length===0)n=r.toMarkdown(e);else{const s=r.toMarkdown(e);n+=kr(n)+s}return n.trim()}}class rs{constructor(e,n,r,s){this.name=e,this.content=n,this.inline=r,this.range=s}toString(){let e=`@${this.name}`;const n=this.content.toString();return this.content.inlines.length===1?e=`${e} ${n}`:this.content.inlines.length>1&&(e=`${e}
${n}`),this.inline?`{${e}}`:e}toMarkdown(e){var n,r;return(r=(n=e==null?void 0:e.renderTag)===null||n===void 0?void 0:n.call(e,this))!==null&&r!==void 0?r:this.toMarkdownDefault(e)}toMarkdownDefault(e){const n=this.content.toMarkdown(e);if(this.inline){const l=vi(this.name,n,e!=null?e:{});if(typeof l=="string")return l}let r="";(e==null?void 0:e.tag)==="italic"||(e==null?void 0:e.tag)===void 0?r="*":(e==null?void 0:e.tag)==="bold"?r="**":(e==null?void 0:e.tag)==="bold-italic"&&(r="***");let s=`${r}@${this.name}${r}`;return this.content.inlines.length===1?s=`${s} \u2014 ${n}`:this.content.inlines.length>1&&(s=`${s}
${n}`),this.inline?`{${s}}`:s}}function vi(i,e,n){var r,s;if(i==="linkplain"||i==="linkcode"||i==="link"){const l=e.indexOf(" ");let c=e;if(l>0){const $=Ir(e,l);c=e.substring($),e=e.substring(0,l)}return(i==="linkcode"||i==="link"&&n.link==="code")&&(c=`\`${c}\``),(s=(r=n.renderLink)===null||r===void 0?void 0:r.call(n,e,c))!==null&&s!==void 0?s:Ri(e,c)}}function Ri(i,e){try{return Ot.o.parse(i,!0),`[${e}](${i})`}catch(n){return i}}class sr{constructor(e,n){this.inlines=e,this.range=n}toString(){let e="";for(let n=0;n<this.inlines.length;n++){const r=this.inlines[n],s=this.inlines[n+1];e+=r.toString(),s&&s.range.start.line>r.range.start.line&&(e+=`
`)}return e}toMarkdown(e){let n="";for(let r=0;r<this.inlines.length;r++){const s=this.inlines[r],l=this.inlines[r+1];n+=s.toMarkdown(e),l&&l.range.start.line>s.range.start.line&&(n+=`
`)}return n}}class Cr{constructor(e,n){this.text=e,this.range=n}toString(){return this.text}toMarkdown(){return this.text}}function kr(i){return i.endsWith(`
`)?`
`:`

`}class xr{constructor(e){this.indexManager=e.shared.workspace.IndexManager,this.commentProvider=e.documentation.CommentProvider}getDocumentation(e){const n=this.commentProvider.getComment(e);if(n&&ra(n))return na(n).toMarkdown({renderLink:(s,l)=>this.documentationLinkRenderer(e,s,l),renderTag:s=>this.documentationTagRenderer(e,s)})}documentationLinkRenderer(e,n,r){var s;const l=(s=this.findNameInPrecomputedScopes(e,n))!==null&&s!==void 0?s:this.findNameInGlobalScope(e,n);if(l&&l.nameSegment){const c=l.nameSegment.range.start.line+1,m=l.nameSegment.range.start.character+1,$=l.documentUri.with({fragment:`L${c},${m}`});return`[${r}](${$.toString()})`}else return}documentationTagRenderer(e,n){}findNameInPrecomputedScopes(e,n){const s=(0,be.Me)(e).precomputedScopes;if(!s)return;let l=e;do{const m=s.get(l).find($=>$.name===n);if(m)return m;l=l.$container}while(l)}findNameInGlobalScope(e,n){return this.indexManager.allElements().find(s=>s.name===n)}}class ss{constructor(e){this.grammarConfig=()=>e.parser.GrammarConfig}getComment(e){var n;return Yr(e)?e.$comment:(n=(0,y.LK)(e.$cstNode,this.grammarConfig().multilineCommentRules))===null||n===void 0?void 0:n.text}}class Ta{constructor(e){this.syncParser=e.parser.LangiumParser}parse(e,n){return Promise.resolve(this.syncParser.parse(e))}}class ua{constructor(e){this.threadCount=8,this.terminationDelay=200,this.workerPool=[],this.queue=[],this.hydrator=e.serializer.Hydrator}initializeWorkers(){for(;this.workerPool.length<this.threadCount;){const e=this.createWorker();e.onReady(()=>{if(this.queue.length>0){const n=this.queue.shift();n&&(e.lock(),n.resolve(e))}}),this.workerPool.push(e)}}parse(e,n){return Re(this,null,function*(){const r=yield this.acquireParserWorker(n),s=new Deferred;let l;const c=n.onCancellationRequested(()=>{l=setTimeout(()=>{this.terminateWorker(r)},this.terminationDelay)});return r.parse(e).then(m=>{const $=this.hydrator.hydrate(m);s.resolve($)}).catch(m=>{s.reject(m)}).finally(()=>{c.dispose(),clearTimeout(l)}),s.promise})}terminateWorker(e){e.terminate();const n=this.workerPool.indexOf(e);n>=0&&this.workerPool.splice(n,1)}acquireParserWorker(e){return Re(this,null,function*(){this.initializeWorkers();for(const r of this.workerPool)if(r.ready)return r.lock(),r;const n=new Deferred;return e.onCancellationRequested(()=>{const r=this.queue.indexOf(n);r>=0&&this.queue.splice(r,1),n.reject(OperationCancelled)}),this.queue.push(n),n.promise})}}class va{get ready(){return this._ready}get onReady(){return this.onReadyEmitter.event}constructor(e,n,r,s){this.onReadyEmitter=new Emitter,this.deferred=new Deferred,this._ready=!0,this._parsing=!1,this.sendMessage=e,this._terminate=s,n(l=>{const c=l;this.deferred.resolve(c),this.unlock()}),r(l=>{this.deferred.reject(l),this.unlock()})}terminate(){this.deferred.reject(OperationCancelled),this._terminate()}lock(){this._ready=!1}unlock(){this._parsing=!1,this._ready=!0,this.onReadyEmitter.fire()}parse(e){if(this._parsing)throw new Error("Parser worker is busy");return this._parsing=!0,this.deferred=new Deferred,this.sendMessage(e),this.deferred.promise}}class ca{constructor(){this.previousTokenSource=new Ke.AU,this.writeQueue=[],this.readQueue=[],this.done=!0}write(e){this.cancelWrite();const n=Mi();return this.previousTokenSource=n,this.enqueue(this.writeQueue,e,n.token)}read(e){return this.enqueue(this.readQueue,e)}enqueue(e,n,r=Ke.Ts.None){const s=new Vr,l={action:n,deferred:s,cancellationToken:r};return e.push(l),this.performNextOperation(),s.promise}performNextOperation(){return Re(this,null,function*(){if(!this.done)return;const e=[];if(this.writeQueue.length>0)e.push(this.writeQueue.shift());else if(this.readQueue.length>0)e.push(...this.readQueue.splice(0,this.readQueue.length));else return;this.done=!1,yield Promise.all(e.map(l=>Re(this,[l],function*({action:n,deferred:r,cancellationToken:s}){try{const c=yield Promise.resolve().then(()=>n(s));r.resolve(c)}catch(c){Rr(c)?r.resolve(void 0):r.reject(c)}}))),this.done=!0,this.performNextOperation()})}cancelWrite(){this.previousTokenSource.cancel()}}class Sr{constructor(e){this.grammarElementIdMap=new ri,this.tokenTypeIdMap=new ri,this.grammar=e.Grammar,this.lexer=e.parser.Lexer,this.linker=e.references.Linker}dehydrate(e){return{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport?this.dehydrateLexerReport(e.lexerReport):void 0,parserErrors:e.parserErrors.map(n=>Object.assign(Object.assign({},n),{message:n.message})),value:this.dehydrateAstNode(e.value,this.createDehyrationContext(e.value))}}dehydrateLexerReport(e){return e}createDehyrationContext(e){const n=new Map,r=new Map;for(const s of(0,be.Zc)(e))n.set(s,{});if(e.$cstNode)for(const s of(0,y._t)(e.$cstNode))r.set(s,{});return{astNodes:n,cstNodes:r}}dehydrateAstNode(e,n){const r=n.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode!==void 0&&(r.$cstNode=this.dehydrateCstNode(e.$cstNode,n));for(const[s,l]of Object.entries(e))if(!s.startsWith("$"))if(Array.isArray(l)){const c=[];r[s]=c;for(const m of l)(0,We.xA)(m)?c.push(this.dehydrateAstNode(m,n)):(0,We.Yk)(m)?c.push(this.dehydrateReference(m,n)):c.push(m)}else(0,We.xA)(l)?r[s]=this.dehydrateAstNode(l,n):(0,We.Yk)(l)?r[s]=this.dehydrateReference(l,n):l!==void 0&&(r[s]=l);return r}dehydrateReference(e,n){const r={};return r.$refText=e.$refText,e.$refNode&&(r.$refNode=n.cstNodes.get(e.$refNode)),r}dehydrateCstNode(e,n){const r=n.cstNodes.get(e);return(0,We.U8)(e)?r.fullText=e.fullText:r.grammarSource=this.getGrammarElementId(e.grammarSource),r.hidden=e.hidden,r.astNode=n.astNodes.get(e.astNode),(0,We.al)(e)?r.content=e.content.map(s=>this.dehydrateCstNode(s,n)):(0,We.dm)(e)&&(r.tokenType=e.tokenType.name,r.offset=e.offset,r.length=e.length,r.startLine=e.range.start.line,r.startColumn=e.range.start.character,r.endLine=e.range.end.line,r.endColumn=e.range.end.character),r}hydrate(e){const n=e.value,r=this.createHydrationContext(n);return"$cstNode"in n&&this.hydrateCstNode(n.$cstNode,r),{lexerErrors:e.lexerErrors,lexerReport:e.lexerReport,parserErrors:e.parserErrors,value:this.hydrateAstNode(n,r)}}createHydrationContext(e){const n=new Map,r=new Map;for(const l of(0,be.Zc)(e))n.set(l,{});let s;if(e.$cstNode)for(const l of(0,y._t)(e.$cstNode)){let c;"fullText"in l?(c=new In(l.fullText),s=c):"content"in l?c=new nn:"tokenType"in l&&(c=this.hydrateCstLeafNode(l)),c&&(r.set(l,c),c.root=s)}return{astNodes:n,cstNodes:r}}hydrateAstNode(e,n){const r=n.astNodes.get(e);r.$type=e.$type,r.$containerIndex=e.$containerIndex,r.$containerProperty=e.$containerProperty,e.$cstNode&&(r.$cstNode=n.cstNodes.get(e.$cstNode));for(const[s,l]of Object.entries(e))if(!s.startsWith("$"))if(Array.isArray(l)){const c=[];r[s]=c;for(const m of l)(0,We.xA)(m)?c.push(this.setParent(this.hydrateAstNode(m,n),r)):(0,We.Yk)(m)?c.push(this.hydrateReference(m,r,s,n)):c.push(m)}else(0,We.xA)(l)?r[s]=this.setParent(this.hydrateAstNode(l,n),r):(0,We.Yk)(l)?r[s]=this.hydrateReference(l,r,s,n):l!==void 0&&(r[s]=l);return r}setParent(e,n){return e.$container=n,e}hydrateReference(e,n,r,s){return this.linker.buildReference(n,r,s.cstNodes.get(e.$refNode),e.$refText)}hydrateCstNode(e,n,r=0){const s=n.cstNodes.get(e);if(typeof e.grammarSource=="number"&&(s.grammarSource=this.getGrammarElement(e.grammarSource)),s.astNode=n.astNodes.get(e.astNode),(0,We.al)(s))for(const l of e.content){const c=this.hydrateCstNode(l,n,r++);s.content.push(c)}return s}hydrateCstLeafNode(e){const n=this.getTokenType(e.tokenType),r=e.offset,s=e.length,l=e.startLine,c=e.startColumn,m=e.endLine,$=e.endColumn,U=e.hidden;return new et(r,s,{start:{line:l,character:c},end:{line:m,character:$}},n,U)}getTokenType(e){return this.lexer.definition[e]}getGrammarElementId(e){if(e)return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.get(e)}getGrammarElement(e){return this.grammarElementIdMap.size===0&&this.createGrammarElementIdMap(),this.grammarElementIdMap.getKey(e)}createGrammarElementIdMap(){let e=0;for(const n of(0,be.Zc)(this.grammar))(0,x.zJ)(n)&&this.grammarElementIdMap.set(n,e++)}}function da(i){return{documentation:{CommentProvider:e=>new ss(e),DocumentationProvider:e=>new xr(e)},parser:{AsyncParser:e=>new Ta(e),GrammarConfig:e=>L(e),LangiumParser:e=>Pi(e),CompletionParser:e=>Xs(e),ValueConverter:()=>new Js.t,TokenBuilder:()=>new Kr.P,Lexer:e=>new ea(e),ParserErrorMessageProvider:()=>new Us,LexerErrorMessageProvider:()=>new Qi},workspace:{AstNodeLocator:()=>new zi,AstNodeDescriptionProvider:e=>new ci(e),ReferenceDescriptionProvider:e=>new Hi(e)},references:{Linker:e=>new Fi(e),NameProvider:()=>new Ui,ScopeProvider:e=>new ai(e),ScopeComputation:e=>new Xe(e),References:e=>new zr(e)},serializer:{Hydrator:e=>new Sr(e),JsonSerializer:e=>new li(e)},validation:{DocumentValidator:e=>new ui(e),ValidationRegistry:e=>new Ki(e)},shared:()=>i.shared}}function Ei(i){return{ServiceRegistry:e=>new Ar(e),workspace:{LangiumDocuments:e=>new Zi(e),LangiumDocumentFactory:e=>new Di(e),DocumentBuilder:e=>new Jr(e),IndexManager:e=>new Xi(e),WorkspaceManager:e=>new Ji(e),FileSystemProvider:e=>i.fileSystemProvider(e),WorkspaceLock:()=>new ca,ConfigurationProvider:e=>new di(e)}}}},93044:function(Te,j,g){g.d(j,{f3:function(){return O}});var y;(function(R){R.merge=(T,E)=>B(B({},T),E)})(y||(y={}));function O(R,T,E,_,v,p,C,h,S){const w=[R,T,E,_,v,p,C,h,S].reduce(B,{});return L(w)}const F=Symbol("isProxy");function x(R){if(R&&R[F])for(const T of Object.values(R))x(T);return R}function L(R,T){const E=new Proxy({},{deleteProperty:()=>!1,set:()=>{throw new Error("Cannot set property on injected service container")},get:(_,v)=>v===F?!0:V(_,v,R,T||E),getOwnPropertyDescriptor:(_,v)=>(V(_,v,R,T||E),Object.getOwnPropertyDescriptor(_,v)),has:(_,v)=>v in R,ownKeys:()=>[...Object.getOwnPropertyNames(R)]});return E}const b=Symbol();function V(R,T,E,_){if(T in R){if(R[T]instanceof Error)throw new Error("Construction failure. Please make sure that your dependencies are constructable.",{cause:R[T]});if(R[T]===b)throw new Error('Cycle detected. Please make "'+String(T)+'" lazy. Visit https://langium.org/docs/reference/configuration-services/#resolving-cyclic-dependencies');return R[T]}else if(T in E){const v=E[T];R[T]=b;try{R[T]=typeof v=="function"?v(_):L(v,_)}catch(p){throw R[T]=p instanceof Error?p:void 0,p}return R[T]}else return}function B(R,T){if(T){for(const[E,_]of Object.entries(T))if(_!==void 0){const v=R[E];v!==null&&_!==null&&typeof v=="object"&&typeof _=="object"?R[E]=B(v,_):R[E]=_}}return R}},71997:function(Te,j,g){g.d(j,{B7:function(){return yn},Bf:function(){return Zn},Bi:function(){return Tn},F8:function(){return ce},F9:function(){return W},Ii:function(){return he},Iy:function(){return vt},Ki:function(){return Fn},L:function(){return J},LG:function(){return qe},MS:function(){return lt},MZ:function(){return Dn},Mp:function(){return Ie},OG:function(){return St},P9:function(){return cn},QV:function(){return se},SV:function(){return Ne},S_:function(){return oe},Sg:function(){return Ft},TB:function(){return K},V7:function(){return vn},W1:function(){return be},X9:function(){return Wn},gf:function(){return Kn},p1:function(){return Bn},qm:function(){return Rt},rT:function(){return Un},t3:function(){return Qt},ty:function(){return Gn},yW:function(){return Ze},zJ:function(){return h}});var y=g(51720);const O={ID:/\^?[_a-zA-Z][\w_]*/,STRING:/"(\\.|[^"\\])*"|'(\\.|[^'\\])*'/,NUMBER:/NaN|-?((\d*\.\d+|\d+)([Ee][+-]?\d+)?|Infinity)/,RegexLiteral:/\/(?![*+?])(?:[^\r\n\[/\\]|\\.|\[(?:[^\r\n\]\\]|\\.)*\])+\/[a-z]*/,WS:/\s+/,ML_COMMENT:/\/\*[\s\S]*?\*\//,SL_COMMENT:/\/\/[^\n\r]*/},F="AbstractRule";function x(D){return ee.isInstance(D,F)}const L="AbstractType";function b(D){return ee.isInstance(D,L)}const V="Condition";function B(D){return ee.isInstance(D,V)}function R(D){return T(D)||D==="current"||D==="entry"||D==="extends"||D==="false"||D==="fragment"||D==="grammar"||D==="hidden"||D==="import"||D==="interface"||D==="returns"||D==="terminal"||D==="true"||D==="type"||D==="infer"||D==="infers"||D==="with"||typeof D=="string"&&/\^?[_a-zA-Z][\w_]*/.test(D)}function T(D){return D==="string"||D==="number"||D==="boolean"||D==="Date"||D==="bigint"}const E="TypeDefinition";function _(D){return ee.isInstance(D,E)}const v="ValueLiteral";function p(D){return ee.isInstance(D,v)}const C="AbstractElement";function h(D){return ee.isInstance(D,C)}const S="ArrayLiteral";function w(D){return ee.isInstance(D,S)}const Z="ArrayType";function P(D){return ee.isInstance(D,Z)}const Q="BooleanLiteral";function J(D){return ee.isInstance(D,Q)}const ve="Conjunction";function K(D){return ee.isInstance(D,ve)}const ae="Disjunction";function ce(D){return ee.isInstance(D,ae)}const ye="Grammar";function Ae(D){return ee.isInstance(D,ye)}const Se="GrammarImport";function Je(D){return ee.isInstance(D,Se)}const Ge="InferredType";function oe(D){return ee.isInstance(D,Ge)}const Y="Interface";function se(D){return ee.isInstance(D,Y)}const re="NamedArgument";function ke(D){return ee.isInstance(D,re)}const ge="Negation";function he(D){return ee.isInstance(D,ge)}const Me="NumberLiteral";function je(D){return ee.isInstance(D,Me)}const $e="Parameter";function He(D){return ee.isInstance(D,$e)}const ze="ParameterReference";function Ze(D){return ee.isInstance(D,ze)}const I="ParserRule";function W(D){return ee.isInstance(D,I)}const z="ReferenceType";function le(D){return ee.isInstance(D,z)}const pe="ReturnType";function Ie(D){return ee.isInstance(D,pe)}const me="SimpleType";function vt(D){return ee.isInstance(D,me)}const at="StringLiteral";function Zt(D){return ee.isInstance(D,at)}const It="TerminalRule";function lt(D){return ee.isInstance(D,It)}const Ct="Type";function cn(D){return ee.isInstance(D,Ct)}const Qe="TypeAttribute";function gn(D){return ee.isInstance(D,Qe)}const ct="UnionType";function bn(D){return ee.isInstance(D,ct)}const kt="Action";function qe(D){return ee.isInstance(D,kt)}const dt="Alternatives";function Dn(D){return ee.isInstance(D,dt)}const ft="Assignment";function yn(D){return ee.isInstance(D,ft)}const ht="CharacterRange";function Zn(D){return ee.isInstance(D,ht)}const Ye="CrossReference";function Fn(D){return ee.isInstance(D,Ye)}const Bt="EndOfFile";function Un(D){return ee.isInstance(D,Bt)}const Wt="Group";function Gn(D){return ee.isInstance(D,Wt)}const nt="Keyword";function Bn(D){return ee.isInstance(D,nt)}const xt="NegatedToken";function Tn(D){return ee.isInstance(D,xt)}const rt="RegexToken";function Ft(D){return ee.isInstance(D,rt)}const Kt="RuleCall";function Qt(D){return ee.isInstance(D,Kt)}const Vt="TerminalAlternatives";function vn(D){return ee.isInstance(D,Vt)}const jt="TerminalGroup";function Wn(D){return ee.isInstance(D,jt)}const Ht="TerminalRuleCall";function Kn(D){return ee.isInstance(D,Ht)}const it="UnorderedGroup";function be(D){return ee.isInstance(D,it)}const wt="UntilToken";function St(D){return ee.isInstance(D,wt)}const ut="Wildcard";function Rt(D){return ee.isInstance(D,ut)}class Ne extends y.$v{getAllTypes(){return[C,F,L,kt,dt,S,Z,ft,Q,ht,V,ve,Ye,ae,Bt,ye,Se,Wt,Ge,Y,nt,re,xt,ge,Me,$e,ze,I,z,rt,pe,Kt,me,at,Vt,jt,It,Ht,Ct,Qe,E,ct,it,wt,v,ut]}computeIsSubtype(xe,Ce){switch(xe){case kt:case dt:case ft:case ht:case Ye:case Bt:case Wt:case nt:case xt:case rt:case Kt:case Vt:case jt:case Ht:case it:case wt:case ut:return this.isSubtype(C,Ce);case S:case Me:case at:return this.isSubtype(v,Ce);case Z:case z:case me:case ct:return this.isSubtype(E,Ce);case Q:return this.isSubtype(V,Ce)||this.isSubtype(v,Ce);case ve:case ae:case ge:case ze:return this.isSubtype(V,Ce);case Ge:case Y:case Ct:return this.isSubtype(L,Ce);case I:return this.isSubtype(F,Ce)||this.isSubtype(L,Ce);case It:return this.isSubtype(F,Ce);default:return!1}}getReferenceType(xe){const Ce=`${xe.container.$type}:${xe.property}`;switch(Ce){case"Action:type":case"CrossReference:type":case"Interface:superTypes":case"ParserRule:returnType":case"SimpleType:typeRef":return L;case"Grammar:hiddenTokens":case"ParserRule:hiddenTokens":case"RuleCall:rule":return F;case"Grammar:usedGrammars":return ye;case"NamedArgument:parameter":case"ParameterReference:parameter":return $e;case"TerminalRuleCall:rule":return It;default:throw new Error(`${Ce} is not a valid reference id.`)}}getTypeMetaData(xe){switch(xe){case C:return{name:C,properties:[{name:"cardinality"},{name:"lookahead"}]};case S:return{name:S,properties:[{name:"elements",defaultValue:[]}]};case Z:return{name:Z,properties:[{name:"elementType"}]};case Q:return{name:Q,properties:[{name:"true",defaultValue:!1}]};case ve:return{name:ve,properties:[{name:"left"},{name:"right"}]};case ae:return{name:ae,properties:[{name:"left"},{name:"right"}]};case ye:return{name:ye,properties:[{name:"definesHiddenTokens",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"imports",defaultValue:[]},{name:"interfaces",defaultValue:[]},{name:"isDeclared",defaultValue:!1},{name:"name"},{name:"rules",defaultValue:[]},{name:"types",defaultValue:[]},{name:"usedGrammars",defaultValue:[]}]};case Se:return{name:Se,properties:[{name:"path"}]};case Ge:return{name:Ge,properties:[{name:"name"}]};case Y:return{name:Y,properties:[{name:"attributes",defaultValue:[]},{name:"name"},{name:"superTypes",defaultValue:[]}]};case re:return{name:re,properties:[{name:"calledByName",defaultValue:!1},{name:"parameter"},{name:"value"}]};case ge:return{name:ge,properties:[{name:"value"}]};case Me:return{name:Me,properties:[{name:"value"}]};case $e:return{name:$e,properties:[{name:"name"}]};case ze:return{name:ze,properties:[{name:"parameter"}]};case I:return{name:I,properties:[{name:"dataType"},{name:"definesHiddenTokens",defaultValue:!1},{name:"definition"},{name:"entry",defaultValue:!1},{name:"fragment",defaultValue:!1},{name:"hiddenTokens",defaultValue:[]},{name:"inferredType"},{name:"name"},{name:"parameters",defaultValue:[]},{name:"returnType"},{name:"wildcard",defaultValue:!1}]};case z:return{name:z,properties:[{name:"referenceType"}]};case pe:return{name:pe,properties:[{name:"name"}]};case me:return{name:me,properties:[{name:"primitiveType"},{name:"stringType"},{name:"typeRef"}]};case at:return{name:at,properties:[{name:"value"}]};case It:return{name:It,properties:[{name:"definition"},{name:"fragment",defaultValue:!1},{name:"hidden",defaultValue:!1},{name:"name"},{name:"type"}]};case Ct:return{name:Ct,properties:[{name:"name"},{name:"type"}]};case Qe:return{name:Qe,properties:[{name:"defaultValue"},{name:"isOptional",defaultValue:!1},{name:"name"},{name:"type"}]};case ct:return{name:ct,properties:[{name:"types",defaultValue:[]}]};case kt:return{name:kt,properties:[{name:"cardinality"},{name:"feature"},{name:"inferredType"},{name:"lookahead"},{name:"operator"},{name:"type"}]};case dt:return{name:dt,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case ft:return{name:ft,properties:[{name:"cardinality"},{name:"feature"},{name:"lookahead"},{name:"operator"},{name:"terminal"}]};case ht:return{name:ht,properties:[{name:"cardinality"},{name:"left"},{name:"lookahead"},{name:"right"}]};case Ye:return{name:Ye,properties:[{name:"cardinality"},{name:"deprecatedSyntax",defaultValue:!1},{name:"lookahead"},{name:"terminal"},{name:"type"}]};case Bt:return{name:Bt,properties:[{name:"cardinality"},{name:"lookahead"}]};case Wt:return{name:Wt,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"guardCondition"},{name:"lookahead"}]};case nt:return{name:nt,properties:[{name:"cardinality"},{name:"lookahead"},{name:"value"}]};case xt:return{name:xt,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case rt:return{name:rt,properties:[{name:"cardinality"},{name:"lookahead"},{name:"regex"}]};case Kt:return{name:Kt,properties:[{name:"arguments",defaultValue:[]},{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case Vt:return{name:Vt,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case jt:return{name:jt,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case Ht:return{name:Ht,properties:[{name:"cardinality"},{name:"lookahead"},{name:"rule"}]};case it:return{name:it,properties:[{name:"cardinality"},{name:"elements",defaultValue:[]},{name:"lookahead"}]};case wt:return{name:wt,properties:[{name:"cardinality"},{name:"lookahead"},{name:"terminal"}]};case ut:return{name:ut,properties:[{name:"cardinality"},{name:"lookahead"}]};default:return{name:xe,properties:[]}}}}const ee=new Ne},93202:function(Te,j,g){g.d(j,{P:function(){return V}});var y=g(76597),O=g(71997),F=g(49602),x=g(25733),L=g(13161),b=g(21442);class V{constructor(){this.diagnostics=[]}buildTokens(R,T){const E=(0,b.Vw)((0,x.VD)(R,!1)),_=this.buildTerminalTokens(E),v=this.buildKeywordTokens(E,_,T);return _.forEach(p=>{const C=p.PATTERN;typeof C=="object"&&C&&"test"in C&&(0,L.cb)(C)?v.unshift(p):v.push(p)}),v}flushLexingReport(R){return{diagnostics:this.popDiagnostics()}}popDiagnostics(){const R=[...this.diagnostics];return this.diagnostics=[],R}buildTerminalTokens(R){return R.filter(O.MS).filter(T=>!T.fragment).map(T=>this.buildTerminalToken(T)).toArray()}buildTerminalToken(R){const T=(0,x.s1)(R),E=this.requiresCustomPattern(T)?this.regexPatternFunction(T):T,_={name:R.name,PATTERN:E};return typeof E=="function"&&(_.LINE_BREAKS=!0),R.hidden&&(_.GROUP=(0,L.cb)(T)?y.hW.SKIPPED:"hidden"),_}requiresCustomPattern(R){return R.flags.includes("u")||R.flags.includes("s")?!0:!!(R.source.includes("?<=")||R.source.includes("?<!"))}regexPatternFunction(R){const T=new RegExp(R,R.flags+"y");return(E,_)=>(T.lastIndex=_,T.exec(E))}buildKeywordTokens(R,T,E){return R.filter(O.F9).flatMap(_=>(0,F.VY)(_).filter(O.p1)).distinct(_=>_.value).toArray().sort((_,v)=>v.value.length-_.value.length).map(_=>this.buildKeywordToken(_,T,!!(E!=null&&E.caseInsensitive)))}buildKeywordToken(R,T,E){const _=this.buildKeywordPattern(R,E),v={name:R.value,PATTERN:_,LONGER_ALT:this.findLongerAlt(R,T)};return typeof _=="function"&&(v.LINE_BREAKS=!0),v}buildKeywordPattern(R,T){return T?new RegExp((0,L.cp)(R.value)):R.value}findLongerAlt(R,T){return T.reduce((E,_)=>{const v=_==null?void 0:_.PATTERN;return v!=null&&v.source&&(0,L.XC)("^"+v.source+"$",R.value)&&E.push(_),E},[])}}},12058:function(Te,j,g){g.d(j,{t:function(){return F}});var y=g(71997),O=g(25733);class F{convert(b,V){let B=V.grammarSource;if((0,y.Ki)(B)&&(B=(0,O.eN)(B)),(0,y.t3)(B)){const R=B.rule.ref;if(!R)throw new Error("This cst node was not parsed by a rule.");return this.runConverter(R,b,V)}return b}runConverter(b,V,B){var R;switch(b.name.toUpperCase()){case"INT":return x.convertInt(V);case"STRING":return x.convertString(V);case"ID":return x.convertID(V)}switch((R=(0,O.mJ)(b))===null||R===void 0?void 0:R.toLowerCase()){case"number":return x.convertNumber(V);case"boolean":return x.convertBoolean(V);case"bigint":return x.convertBigint(V);case"date":return x.convertDate(V);default:return V}}}var x;(function(L){function b(p){let C="";for(let h=1;h<p.length-1;h++){const S=p.charAt(h);if(S==="\\"){const w=p.charAt(++h);C+=V(w)}else C+=S}return C}L.convertString=b;function V(p){switch(p){case"b":return"\b";case"f":return"\f";case"n":return`
`;case"r":return"\r";case"t":return"	";case"v":return"\v";case"0":return"\0";default:return p}}function B(p){return p.charAt(0)==="^"?p.substring(1):p}L.convertID=B;function R(p){return parseInt(p)}L.convertInt=R;function T(p){return BigInt(p)}L.convertBigint=T;function E(p){return new Date(p)}L.convertDate=E;function _(p){return Number(p)}L.convertNumber=_;function v(p){return p.toLowerCase()==="true"}L.convertBoolean=v})(x||(x={}))},51720:function(Te,j,g){g.d(j,{$v:function(){return L},SI:function(){return F},U8:function(){return B},Yk:function(){return O},al:function(){return b},dm:function(){return V},et:function(){return x},xA:function(){return y}});function y(R){return typeof R=="object"&&R!==null&&typeof R.$type=="string"}function O(R){return typeof R=="object"&&R!==null&&typeof R.$refText=="string"}function F(R){return typeof R=="object"&&R!==null&&typeof R.name=="string"&&typeof R.type=="string"&&typeof R.path=="string"}function x(R){return typeof R=="object"&&R!==null&&y(R.container)&&O(R.reference)&&typeof R.message=="string"}class L{constructor(){this.subtypes={},this.allSubtypes={}}isInstance(T,E){return y(T)&&this.isSubtype(T.$type,E)}isSubtype(T,E){if(T===E)return!0;let _=this.subtypes[T];_||(_=this.subtypes[T]={});const v=_[E];if(v!==void 0)return v;{const p=this.computeIsSubtype(T,E);return _[E]=p,p}}getAllSubTypes(T){const E=this.allSubtypes[T];if(E)return E;{const _=this.getAllTypes(),v=[];for(const p of _)this.isSubtype(p,T)&&v.push(p);return this.allSubtypes[T]=v,v}}}function b(R){return typeof R=="object"&&R!==null&&Array.isArray(R.content)}function V(R){return typeof R=="object"&&R!==null&&typeof R.tokenType=="object"}function B(R){return b(R)&&typeof R.fullText=="string"}},49602:function(Te,j,g){g.d(j,{E$:function(){return B},Me:function(){return V},VY:function(){return T},V_:function(){return L},Zc:function(){return E},a1:function(){return C},b2:function(){return x},fy:function(){return v},sx:function(){return R}});var y=g(51720),O=g(21442),F=g(45785);function x(w){for(const[Z,P]of Object.entries(w))Z.startsWith("$")||(Array.isArray(P)?P.forEach((Q,J)=>{(0,y.xA)(Q)&&(Q.$container=w,Q.$containerProperty=Z,Q.$containerIndex=J)}):(0,y.xA)(P)&&(P.$container=w,P.$containerProperty=Z))}function L(w,Z){let P=w;for(;P;){if(Z(P))return P;P=P.$container}}function b(w,Z){let P=w;for(;P;){if(Z(P))return!0;P=P.$container}return!1}function V(w){const P=B(w).$document;if(!P)throw new Error("AST node has no document.");return P}function B(w){for(;w.$container;)w=w.$container;return w}function R(w,Z){if(!w)throw new Error("Node must be an AstNode.");const P=Z==null?void 0:Z.range;return new O.i(()=>({keys:Object.keys(w),keyIndex:0,arrayIndex:0}),Q=>{for(;Q.keyIndex<Q.keys.length;){const J=Q.keys[Q.keyIndex];if(!J.startsWith("$")){const ve=w[J];if((0,y.xA)(ve)){if(Q.keyIndex++,_(ve,P))return{done:!1,value:ve}}else if(Array.isArray(ve)){for(;Q.arrayIndex<ve.length;){const K=Q.arrayIndex++,ae=ve[K];if((0,y.xA)(ae)&&_(ae,P))return{done:!1,value:ae}}Q.arrayIndex=0}}Q.keyIndex++}return O.Ry})}function T(w,Z){if(!w)throw new Error("Root node must be an AstNode.");return new O.i8(w,P=>R(P,Z))}function E(w,Z){if(w){if(Z!=null&&Z.range&&!_(w,Z.range))return new O.i8(w,()=>[])}else throw new Error("Root node must be an AstNode.");return new O.i8(w,P=>R(P,Z),{includeRoot:!0})}function _(w,Z){var P;if(!Z)return!0;const Q=(P=w.$cstNode)===null||P===void 0?void 0:P.range;return Q?(0,F.Z2)(Q,Z):!1}function v(w){return new O.i(()=>({keys:Object.keys(w),keyIndex:0,arrayIndex:0}),Z=>{for(;Z.keyIndex<Z.keys.length;){const P=Z.keys[Z.keyIndex];if(!P.startsWith("$")){const Q=w[P];if((0,y.Yk)(Q))return Z.keyIndex++,{done:!1,value:{reference:Q,container:w,property:P}};if(Array.isArray(Q)){for(;Z.arrayIndex<Q.length;){const J=Z.arrayIndex++,ve=Q[J];if((0,y.Yk)(ve))return{done:!1,value:{reference:ve,container:w,property:P,index:J}}}Z.arrayIndex=0}}Z.keyIndex++}return O.Ry})}function p(w,Z=V(w).parseResult.value){const P=[];return E(Z).forEach(Q=>{v(Q).forEach(J=>{J.reference.ref===w&&P.push(J.reference)})}),stream(P)}function C(w,Z){const P=w.getTypeMetaData(Z.$type),Q=Z;for(const J of P.properties)J.defaultValue!==void 0&&Q[J.name]===void 0&&(Q[J.name]=h(J.defaultValue))}function h(w){return Array.isArray(w)?[...w.map(h)]:w}function S(w,Z){const P={$type:w.$type};for(const[Q,J]of Object.entries(w))if(!Q.startsWith("$"))if(isAstNode(J))P[Q]=S(J,Z);else if(isReference(J))P[Q]=Z(P,Q,J.$refNode,J.$refText);else if(Array.isArray(J)){const ve=[];for(const K of J)isAstNode(K)?ve.push(S(K,Z)):isReference(K)?ve.push(Z(P,Q,K.$refNode,K.$refText)):ve.push(K);P[Q]=ve}else P[Q]=J;return x(P),P}},45785:function(Te,j,g){g.d(j,{LK:function(){return v},OB:function(){return L},Z2:function(){return T},_t:function(){return F},sp:function(){return b},uz:function(){return E},yn:function(){return V}});var y=g(51720),O=g(21442);function F(K){return new O.i8(K,ae=>(0,y.al)(ae)?ae.content:[],{includeRoot:!0})}function x(K){return F(K).filter(isLeafCstNode)}function L(K,ae){for(;K.container;)if(K=K.container,K===ae)return!0;return!1}function b(K){return{start:{character:K.startColumn-1,line:K.startLine-1},end:{character:K.endColumn,line:K.endLine-1}}}function V(K){if(!K)return;const{offset:ae,end:ce,range:ye}=K;return{range:ye,offset:ae,end:ce,length:ce-ae}}var B;(function(K){K[K.Before=0]="Before",K[K.After=1]="After",K[K.OverlapFront=2]="OverlapFront",K[K.OverlapBack=3]="OverlapBack",K[K.Inside=4]="Inside",K[K.Outside=5]="Outside"})(B||(B={}));function R(K,ae){if(K.end.line<ae.start.line||K.end.line===ae.start.line&&K.end.character<=ae.start.character)return B.Before;if(K.start.line>ae.end.line||K.start.line===ae.end.line&&K.start.character>=ae.end.character)return B.After;const ce=K.start.line>ae.start.line||K.start.line===ae.start.line&&K.start.character>=ae.start.character,ye=K.end.line<ae.end.line||K.end.line===ae.end.line&&K.end.character<=ae.end.character;return ce&&ye?B.Inside:ce?B.OverlapBack:ye?B.OverlapFront:B.Outside}function T(K,ae){return R(K,ae)>B.After}const E=/^[\w\p{L}]$/u;function _(K,ae,ce=E){if(K){if(ae>0){const ye=ae-K.offset,Ae=K.text.charAt(ye);ce.test(Ae)||ae--}return C(K,ae)}}function v(K,ae){if(K){const ce=w(K,!0);if(ce&&p(ce,ae))return ce;if((0,y.U8)(K)){const ye=K.content.findIndex(Ae=>!Ae.hidden);for(let Ae=ye-1;Ae>=0;Ae--){const Se=K.content[Ae];if(p(Se,ae))return Se}}}}function p(K,ae){return(0,y.dm)(K)&&ae.includes(K.tokenType.name)}function C(K,ae){if(isLeafCstNode(K))return K;if(isCompositeCstNode(K)){const ce=S(K,ae,!1);if(ce)return C(ce,ae)}}function h(K,ae){if(isLeafCstNode(K))return K;if(isCompositeCstNode(K)){const ce=S(K,ae,!0);if(ce)return h(ce,ae)}}function S(K,ae,ce){let ye=0,Ae=K.content.length-1,Se;for(;ye<=Ae;){const Je=Math.floor((ye+Ae)/2),Ge=K.content[Je];if(Ge.offset<=ae&&Ge.end>ae)return Ge;Ge.end<=ae?(Se=ce?Ge:void 0,ye=Je+1):Ae=Je-1}return Se}function w(K,ae=!0){for(;K.container;){const ce=K.container;let ye=ce.content.indexOf(K);for(;ye>0;){ye--;const Ae=ce.content[ye];if(ae||!Ae.hidden)return Ae}K=ce}}function Z(K,ae=!0){for(;K.container;){const ce=K.container;let ye=ce.content.indexOf(K);const Ae=ce.content.length-1;for(;ye<Ae;){ye++;const Se=ce.content[ye];if(ae||!Se.hidden)return Se}K=ce}}function P(K){if(K.range.start.character===0)return K;const ae=K.range.start.line;let ce=K,ye;for(;K.container;){const Ae=K.container,Se=ye!=null?ye:Ae.content.indexOf(K);if(Se===0?(K=Ae,ye=void 0):(ye=Se-1,K=Ae.content[ye]),K.range.start.line!==ae)break;ce=K}return ce}function Q(K,ae){const ce=J(K,ae);return ce?ce.parent.content.slice(ce.a+1,ce.b):[]}function J(K,ae){const ce=ve(K),ye=ve(ae);let Ae;for(let Se=0;Se<ce.length&&Se<ye.length;Se++){const Je=ce[Se],Ge=ye[Se];if(Je.parent===Ge.parent)Ae={parent:Je.parent,a:Je.index,b:Ge.index};else break}return Ae}function ve(K){const ae=[];for(;K.container;){const ce=K.container,ye=ce.content.indexOf(K);ae.push({parent:ce,index:ye}),K=ce}return ae.reverse()}},44465:function(Te,j,g){g.d(j,{U:function(){return O},h:function(){return y}});class y extends Error{constructor(x,L){super(x?`${L} at ${x.range.start.line}:${x.range.start.character}`:L)}}function O(F){throw new Error("Error! The input value was not handled.")}},25733:function(Te,j,g){g.d(j,{$G:function(){return Je},EL:function(){return v},UP:function(){return ce},VD:function(){return R},eN:function(){return E},h7:function(){return Z},ib:function(){return P},lA:function(){return S},mJ:function(){return se},md:function(){return _},s1:function(){return re},vb:function(){return p},z$:function(){return Ge}});var y=g(44465),O=g(71997),F=g(51720),x=g(49602),L=g(45785),b=g(13161);function V(I){return I.rules.find(W=>O.F9(W)&&W.entry)}function B(I){return I.rules.filter(W=>O.MS(W)&&W.hidden)}function R(I,W){const z=new Set,le=V(I);if(!le)return new Set(I.rules);const pe=[le].concat(B(I));for(const me of pe)T(me,z,W);const Ie=new Set;for(const me of I.rules)(z.has(me.name)||O.MS(me)&&me.hidden)&&Ie.add(me);return Ie}function T(I,W,z){W.add(I.name),(0,x.VY)(I).forEach(le=>{if(O.t3(le)||z&&O.gf(le)){const pe=le.rule.ref;pe&&!W.has(pe.name)&&T(pe,W,z)}})}function E(I){if(I.terminal)return I.terminal;if(I.type.ref){const W=P(I.type.ref);return W==null?void 0:W.terminal}}function _(I){return I.hidden&&!(0,b.cb)(re(I))}function v(I,W){return!I||!W?[]:C(I,W,I.astNode,!0)}function p(I,W,z){if(!I||!W)return;const le=C(I,W,I.astNode,!0);if(le.length!==0)return z!==void 0?z=Math.max(0,Math.min(z,le.length-1)):z=0,le[z]}function C(I,W,z,le){if(!le){const pe=(0,x.V_)(I.grammarSource,O.B7);if(pe&&pe.feature===W)return[I]}return(0,F.al)(I)&&I.astNode===z?I.content.flatMap(pe=>C(pe,W,z,!1)):[]}function h(I,W){return I?w(I,W,I==null?void 0:I.astNode):[]}function S(I,W,z){if(!I)return;const le=w(I,W,I==null?void 0:I.astNode);if(le.length!==0)return z!==void 0?z=Math.max(0,Math.min(z,le.length-1)):z=0,le[z]}function w(I,W,z){if(I.astNode!==z)return[];if(O.p1(I.grammarSource)&&I.grammarSource.value===W)return[I];const le=(0,L._t)(I).iterator();let pe;const Ie=[];do if(pe=le.next(),!pe.done){const me=pe.value;me.astNode===z?O.p1(me.grammarSource)&&me.grammarSource.value===W&&Ie.push(me):le.prune()}while(!pe.done);return Ie}function Z(I){var W;const z=I.astNode;for(;z===((W=I.container)===null||W===void 0?void 0:W.astNode);){const le=(0,x.V_)(I.grammarSource,O.B7);if(le)return le;I=I.container}}function P(I){let W=I;return O.S_(W)&&(O.LG(W.$container)?W=W.$container.$container:O.F9(W.$container)?W=W.$container:(0,y.U)(W.$container)),Q(I,W,new Map)}function Q(I,W,z){var le;function pe(Ie,me){let vt;return(0,x.V_)(Ie,O.B7)||(vt=Q(me,me,z)),z.set(I,vt),vt}if(z.has(I))return z.get(I);z.set(I,void 0);for(const Ie of(0,x.VY)(W)){if(O.B7(Ie)&&Ie.feature.toLowerCase()==="name")return z.set(I,Ie),Ie;if(O.t3(Ie)&&O.F9(Ie.rule.ref))return pe(Ie,Ie.rule.ref);if(O.Iy(Ie)&&(!((le=Ie.typeRef)===null||le===void 0)&&le.ref))return pe(Ie,Ie.typeRef.ref)}}function J(I){const W=I.$container;if(ast.isGroup(W)){const z=W.elements,le=z.indexOf(I);for(let pe=le-1;pe>=0;pe--){const Ie=z[pe];if(ast.isAction(Ie))return Ie;{const me=streamAllContents(z[pe]).find(ast.isAction);if(me)return me}}}if(ast.isAbstractElement(W))return J(W)}function ve(I,W){return I==="?"||I==="*"||ast.isGroup(W)&&!!W.guardCondition}function K(I){return I==="*"||I==="+"}function ae(I){return I==="+="}function ce(I){return ye(I,new Set)}function ye(I,W){if(W.has(I))return!0;W.add(I);for(const z of(0,x.VY)(I))if(O.t3(z)){if(!z.rule.ref||O.F9(z.rule.ref)&&!ye(z.rule.ref,W))return!1}else{if(O.B7(z))return!1;if(O.LG(z))return!1}return!!I.definition}function Ae(I){return Se(I.type,new Set)}function Se(I,W){if(W.has(I))return!0;if(W.add(I),ast.isArrayType(I))return!1;if(ast.isReferenceType(I))return!1;if(ast.isUnionType(I))return I.types.every(z=>Se(z,W));if(ast.isSimpleType(I)){if(I.primitiveType!==void 0)return!0;if(I.stringType!==void 0)return!0;if(I.typeRef!==void 0){const z=I.typeRef.ref;return ast.isType(z)?Se(z.type,W):!1}else return!1}else return!1}function Je(I){if(I.inferredType)return I.inferredType.name;if(I.dataType)return I.dataType;if(I.returnType){const W=I.returnType.ref;if(W){if(O.F9(W))return W.name;if(O.QV(W)||O.P9(W))return W.name}}}function Ge(I){var W;if(O.F9(I))return ce(I)?I.name:(W=Je(I))!==null&&W!==void 0?W:I.name;if(O.QV(I)||O.P9(I)||O.Mp(I))return I.name;if(O.LG(I)){const z=oe(I);if(z)return z}else if(O.S_(I))return I.name;throw new Error("Cannot get name of Unknown Type")}function oe(I){var W;if(I.inferredType)return I.inferredType.name;if(!((W=I.type)===null||W===void 0)&&W.ref)return Ge(I.type.ref)}function Y(I){var W,z,le;return ast.isTerminalRule(I)?(z=(W=I.type)===null||W===void 0?void 0:W.name)!==null&&z!==void 0?z:"string":ce(I)?I.name:(le=Je(I))!==null&&le!==void 0?le:I.name}function se(I){var W,z,le;return O.MS(I)?(z=(W=I.type)===null||W===void 0?void 0:W.name)!==null&&z!==void 0?z:"string":(le=Je(I))!==null&&le!==void 0?le:I.name}function re(I){const W={s:!1,i:!1,u:!1},z=ge(I.definition,W),le=Object.entries(W).filter(([,pe])=>pe).map(([pe])=>pe).join("");return new RegExp(z,le)}const ke=/[\s\S]/.source;function ge(I,W){if(O.V7(I))return he(I);if(O.X9(I))return Me(I);if(O.Bf(I))return He(I);if(O.gf(I)){const z=I.rule.ref;if(!z)throw new Error("Missing rule reference.");return Ze(ge(z.definition),{cardinality:I.cardinality,lookahead:I.lookahead})}else{if(O.Bi(I))return $e(I);if(O.OG(I))return je(I);if(O.Sg(I)){const z=I.regex.lastIndexOf("/"),le=I.regex.substring(1,z),pe=I.regex.substring(z+1);return W&&(W.i=pe.includes("i"),W.s=pe.includes("s"),W.u=pe.includes("u")),Ze(le,{cardinality:I.cardinality,lookahead:I.lookahead,wrap:!1})}else{if(O.qm(I))return Ze(ke,{cardinality:I.cardinality,lookahead:I.lookahead});throw new Error(`Invalid terminal element: ${I==null?void 0:I.$type}`)}}}function he(I){return Ze(I.elements.map(W=>ge(W)).join("|"),{cardinality:I.cardinality,lookahead:I.lookahead})}function Me(I){return Ze(I.elements.map(W=>ge(W)).join(""),{cardinality:I.cardinality,lookahead:I.lookahead})}function je(I){return Ze(`${ke}*?${ge(I.terminal)}`,{cardinality:I.cardinality,lookahead:I.lookahead})}function $e(I){return Ze(`(?!${ge(I.terminal)})${ke}*?`,{cardinality:I.cardinality,lookahead:I.lookahead})}function He(I){return I.right?Ze(`[${ze(I.left)}-${ze(I.right)}]`,{cardinality:I.cardinality,lookahead:I.lookahead,wrap:!1}):Ze(ze(I.left),{cardinality:I.cardinality,lookahead:I.lookahead,wrap:!1})}function ze(I){return(0,b.hr)(I.value)}function Ze(I,W){var z;return(W.wrap!==!1||W.lookahead)&&(I=`(${(z=W.lookahead)!==null&&z!==void 0?z:""}${I})`),W.cardinality?`${I}${W.cardinality}`:I}},13161:function(Te,j,g){g.d(j,{K0:function(){return O},Rn:function(){return V},XC:function(){return _},cb:function(){return R},cp:function(){return E},hr:function(){return T}});var y=g(37521);const O=/\r?\n/gm,F=new y.O;class x extends y.e{constructor(){super(...arguments),this.isStarting=!0,this.endRegexpStack=[],this.multiline=!1}get endRegex(){return this.endRegexpStack.join("")}reset(C){this.multiline=!1,this.regex=C,this.startRegexp="",this.isStarting=!0,this.endRegexpStack=[]}visitGroup(C){C.quantifier&&(this.isStarting=!1,this.endRegexpStack=[])}visitCharacter(C){const h=String.fromCharCode(C.value);if(!this.multiline&&h===`
`&&(this.multiline=!0),C.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const S=T(h);this.endRegexpStack.push(S),this.isStarting&&(this.startRegexp+=S)}}visitSet(C){if(!this.multiline){const h=this.regex.substring(C.loc.begin,C.loc.end),S=new RegExp(h);this.multiline=!!`
`.match(S)}if(C.quantifier)this.isStarting=!1,this.endRegexpStack=[];else{const h=this.regex.substring(C.loc.begin,C.loc.end);this.endRegexpStack.push(h),this.isStarting&&(this.startRegexp+=h)}}visitChildren(C){C.type==="Group"&&C.quantifier||super.visitChildren(C)}}const L=new x;function b(p){try{typeof p!="string"&&(p=p.source),p=`/${p}/`;const C=F.pattern(p),h=[];for(const S of C.value.value)L.reset(p),L.visit(S),h.push({start:L.startRegexp,end:L.endRegex});return h}catch(C){return[]}}function V(p){try{return typeof p=="string"&&(p=new RegExp(p)),p=p.toString(),L.reset(p),L.visit(F.pattern(p)),L.multiline}catch(C){return!1}}const B=`\f
\r	\v \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u2028\u2029\u202F\u205F\u3000\uFEFF`.split("");function R(p){const C=typeof p=="string"?new RegExp(p):p;return B.some(h=>C.test(h))}function T(p){return p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function E(p){return Array.prototype.map.call(p,C=>/\w/.test(C)?`[${C.toLowerCase()}${C.toUpperCase()}]`:T(C)).join("")}function _(p,C){const h=v(p),S=C.match(h);return!!S&&S[0].length>0}function v(p){typeof p=="string"&&(p=new RegExp(p));const C=p,h=p.source;let S=0;function w(){let Z="",P;function Q(ve){Z+=h.substr(S,ve),S+=ve}function J(ve){Z+="(?:"+h.substr(S,ve)+"|$)",S+=ve}for(;S<h.length;)switch(h[S]){case"\\":switch(h[S+1]){case"c":J(3);break;case"x":J(4);break;case"u":C.unicode?h[S+2]==="{"?J(h.indexOf("}",S)-S+1):J(6):J(2);break;case"p":case"P":C.unicode?J(h.indexOf("}",S)-S+1):J(2);break;case"k":J(h.indexOf(">",S)-S+1);break;default:J(2);break}break;case"[":P=/\[(?:\\.|.)*?\]/g,P.lastIndex=S,P=P.exec(h)||[],J(P[0].length);break;case"|":case"^":case"$":case"*":case"+":case"?":Q(1);break;case"{":P=/\{\d+,?\d*\}/g,P.lastIndex=S,P=P.exec(h),P?Q(P[0].length):J(1);break;case"(":if(h[S+1]==="?")switch(h[S+2]){case":":Z+="(?:",S+=3,Z+=w()+"|$)";break;case"=":Z+="(?=",S+=3,Z+=w()+")";break;case"!":P=S,S+=3,w(),Z+=h.substr(P,S-P);break;case"<":switch(h[S+3]){case"=":case"!":P=S,S+=4,w(),Z+=h.substr(P,S-P);break;default:Q(h.indexOf(">",S)-S+1),Z+=w()+"|$)";break}break}else Q(1),Z+=w()+"|$)";break;case")":return++S,Z;default:J(1);break}return Z}return new RegExp(w(),p.flags)}},21442:function(Te,j,g){g.d(j,{Cl:function(){return x},IH:function(){return B},Ry:function(){return L},Vw:function(){return b},i:function(){return y},i8:function(){return V}});class y{constructor(T,E){this.startFn=T,this.nextFn=E}iterator(){const T={state:this.startFn(),next:()=>this.nextFn(T.state),[Symbol.iterator]:()=>T};return T}[Symbol.iterator](){return this.iterator()}isEmpty(){return!!this.iterator().next().done}count(){const T=this.iterator();let E=0,_=T.next();for(;!_.done;)E++,_=T.next();return E}toArray(){const T=[],E=this.iterator();let _;do _=E.next(),_.value!==void 0&&T.push(_.value);while(!_.done);return T}toSet(){return new Set(this)}toMap(T,E){const _=this.map(v=>[T?T(v):v,E?E(v):v]);return new Map(_)}toString(){return this.join()}concat(T){return new y(()=>({first:this.startFn(),firstDone:!1,iterator:T[Symbol.iterator]()}),E=>{let _;if(!E.firstDone){do if(_=this.nextFn(E.first),!_.done)return _;while(!_.done);E.firstDone=!0}do if(_=E.iterator.next(),!_.done)return _;while(!_.done);return L})}join(T=","){const E=this.iterator();let _="",v,p=!1;do v=E.next(),v.done||(p&&(_+=T),_+=O(v.value)),p=!0;while(!v.done);return _}indexOf(T,E=0){const _=this.iterator();let v=0,p=_.next();for(;!p.done;){if(v>=E&&p.value===T)return v;p=_.next(),v++}return-1}every(T){const E=this.iterator();let _=E.next();for(;!_.done;){if(!T(_.value))return!1;_=E.next()}return!0}some(T){const E=this.iterator();let _=E.next();for(;!_.done;){if(T(_.value))return!0;_=E.next()}return!1}forEach(T){const E=this.iterator();let _=0,v=E.next();for(;!v.done;)T(v.value,_),v=E.next(),_++}map(T){return new y(this.startFn,E=>{const{done:_,value:v}=this.nextFn(E);return _?L:{done:!1,value:T(v)}})}filter(T){return new y(this.startFn,E=>{let _;do if(_=this.nextFn(E),!_.done&&T(_.value))return _;while(!_.done);return L})}nonNullable(){return this.filter(T=>T!=null)}reduce(T,E){const _=this.iterator();let v=E,p=_.next();for(;!p.done;)v===void 0?v=p.value:v=T(v,p.value),p=_.next();return v}reduceRight(T,E){return this.recursiveReduce(this.iterator(),T,E)}recursiveReduce(T,E,_){const v=T.next();if(v.done)return _;const p=this.recursiveReduce(T,E,_);return p===void 0?v.value:E(p,v.value)}find(T){const E=this.iterator();let _=E.next();for(;!_.done;){if(T(_.value))return _.value;_=E.next()}}findIndex(T){const E=this.iterator();let _=0,v=E.next();for(;!v.done;){if(T(v.value))return _;v=E.next(),_++}return-1}includes(T){const E=this.iterator();let _=E.next();for(;!_.done;){if(_.value===T)return!0;_=E.next()}return!1}flatMap(T){return new y(()=>({this:this.startFn()}),E=>{do{if(E.iterator){const p=E.iterator.next();if(p.done)E.iterator=void 0;else return p}const{done:_,value:v}=this.nextFn(E.this);if(!_){const p=T(v);if(F(p))E.iterator=p[Symbol.iterator]();else return{done:!1,value:p}}}while(E.iterator);return L})}flat(T){if(T===void 0&&(T=1),T<=0)return this;const E=T>1?this.flat(T-1):this;return new y(()=>({this:E.startFn()}),_=>{do{if(_.iterator){const C=_.iterator.next();if(C.done)_.iterator=void 0;else return C}const{done:v,value:p}=E.nextFn(_.this);if(!v)if(F(p))_.iterator=p[Symbol.iterator]();else return{done:!1,value:p}}while(_.iterator);return L})}head(){const E=this.iterator().next();if(!E.done)return E.value}tail(T=1){return new y(()=>{const E=this.startFn();for(let _=0;_<T;_++)if(this.nextFn(E).done)return E;return E},this.nextFn)}limit(T){return new y(()=>({size:0,state:this.startFn()}),E=>(E.size++,E.size>T?L:this.nextFn(E.state)))}distinct(T){return new y(()=>({set:new Set,internalState:this.startFn()}),E=>{let _;do if(_=this.nextFn(E.internalState),!_.done){const v=T?T(_.value):_.value;if(!E.set.has(v))return E.set.add(v),_}while(!_.done);return L})}exclude(T,E){const _=new Set;for(const v of T){const p=E?E(v):v;_.add(p)}return this.filter(v=>{const p=E?E(v):v;return!_.has(p)})}}function O(R){return typeof R=="string"?R:typeof R=="undefined"?"undefined":typeof R.toString=="function"?R.toString():Object.prototype.toString.call(R)}function F(R){return!!R&&typeof R[Symbol.iterator]=="function"}const x=new y(()=>{},()=>L),L=Object.freeze({done:!0,value:void 0});function b(...R){if(R.length===1){const T=R[0];if(T instanceof y)return T;if(F(T))return new y(()=>T[Symbol.iterator](),E=>E.next());if(typeof T.length=="number")return new y(()=>({index:0}),E=>E.index<T.length?{done:!1,value:T[E.index++]}:L)}return R.length>1?new y(()=>({collIndex:0,arrIndex:0}),T=>{do{if(T.iterator){const E=T.iterator.next();if(!E.done)return E;T.iterator=void 0}if(T.array){if(T.arrIndex<T.array.length)return{done:!1,value:T.array[T.arrIndex++]};T.array=void 0,T.arrIndex=0}if(T.collIndex<R.length){const E=R[T.collIndex++];F(E)?T.iterator=E[Symbol.iterator]():E&&typeof E.length=="number"&&(T.array=E)}}while(T.iterator||T.array||T.collIndex<R.length);return L}):x}class V extends y{constructor(T,E,_){super(()=>({iterators:_!=null&&_.includeRoot?[[T][Symbol.iterator]()]:[E(T)[Symbol.iterator]()],pruned:!1}),v=>{for(v.pruned&&(v.iterators.pop(),v.pruned=!1);v.iterators.length>0;){const C=v.iterators[v.iterators.length-1].next();if(C.done)v.iterators.pop();else return v.iterators.push(E(C.value)[Symbol.iterator]()),C}return L})}iterator(){const T={state:this.startFn(),next:()=>this.nextFn(T.state),prune:()=>{T.state.pruned=!0},[Symbol.iterator]:()=>T};return T}}var B;(function(R){function T(p){return p.reduce((C,h)=>C+h,0)}R.sum=T;function E(p){return p.reduce((C,h)=>C*h,0)}R.product=E;function _(p){return p.reduce((C,h)=>Math.min(C,h))}R.min=_;function v(p){return p.reduce((C,h)=>Math.max(C,h))}R.max=v})(B||(B={}))},35003:function(Te,j,g){g.d(j,{u:function(){return O}});class y{readFile(){throw new Error("No file system is available.")}readDirectory(){return Re(this,null,function*(){return[]})}}const O={fileSystemProvider:()=>new y}},71818:function(Te,j,g){var y=g(80718);function O(F,x,L){for(var b=-1,V=F.length;++b<V;){var B=F[b],R=x(B);if(R!=null&&(T===void 0?R===R&&!(0,y.Z)(R):L(R,T)))var T=R,E=B}return E}j.Z=O},65052:function(Te,j){function g(y,O){return y<O}j.Z=g},46623:function(Te,j,g){var y=g(61397),O=g(13932);function F(x,L){var b=-1,V=(0,O.Z)(x)?Array(x.length):[];return(0,y.Z)(x,function(B,R,T){V[++b]=L(B,R,T)}),V}j.Z=F},70648:function(Te,j,g){g.d(j,{Z:function(){return T}});var y=g(51327),O=g(23996),F=g(86255),x=g(25281),L=g(7247),b=g(65931);function V(E,_,v,p){if(!(0,L.Z)(E))return E;_=(0,F.Z)(_,E);for(var C=-1,h=_.length,S=h-1,w=E;w!=null&&++C<h;){var Z=(0,b.Z)(_[C]),P=v;if(Z==="__proto__"||Z==="constructor"||Z==="prototype")return E;if(C!=S){var Q=w[Z];P=p?p(Q,Z,w):void 0,P===void 0&&(P=(0,L.Z)(Q)?Q:(0,x.Z)(_[C+1])?[]:{})}(0,O.Z)(w,Z,P),w=w[Z]}return E}var B=V;function R(E,_,v){for(var p=-1,C=_.length,h={};++p<C;){var S=_[p],w=(0,y.Z)(E,S);v(w,S)&&B(h,(0,F.Z)(S,E),w)}return h}var T=R},58566:function(Te,j){function g(y,O,F){var x=-1,L=y.length;O<0&&(O=-O>L?0:L+O),F=F>L?L:F,F<0&&(F+=L),L=O>F?0:F-O>>>0,O>>>=0;for(var b=Array(L);++x<L;)b[x]=y[x+O];return b}j.Z=g},24220:function(Te,j,g){var y=g(68194),O=4;function F(x){return(0,y.Z)(x,O)}j.Z=F},35565:function(Te,j,g){var y=g(93092),O=g(28782),F=g(14026),x=g(44399),L=Object.prototype,b=L.hasOwnProperty,V=(0,y.Z)(function(B,R){B=Object(B);var T=-1,E=R.length,_=E>2?R[2]:void 0;for(_&&(0,F.Z)(R[0],R[1],_)&&(E=1);++T<E;)for(var v=R[T],p=(0,x.Z)(v),C=-1,h=p.length;++C<h;){var S=p[C],w=B[S];(w===void 0||(0,O.Z)(w,L[S])&&!b.call(B,S))&&(B[S]=v[S])}return B});j.Z=V},89045:function(Te,j,g){g.d(j,{Z:function(){return _}});var y=g(18458),O=g(13932),F=g(92769);function x(v){return function(p,C,h){var S=Object(p);if(!(0,O.Z)(p)){var w=(0,y.Z)(C,3);p=(0,F.Z)(p),C=function(P){return w(S[P],P,S)}}var Z=v(p,C,h);return Z>-1?S[w?p[Z]:Z]:void 0}}var L=x,b=g(55799),V=g(42013),B=Math.max;function R(v,p,C){var h=v==null?0:v.length;if(!h)return-1;var S=C==null?0:(0,V.Z)(C);return S<0&&(S=B(h+S,0)),(0,b.Z)(v,(0,y.Z)(p,3),S)}var T=R,E=L(T),_=E},16571:function(Te,j,g){var y=g(3148),O=g(857);function F(x,L){return(0,y.Z)((0,O.Z)(x,L),1)}j.Z=F},42060:function(Te,j,g){var y=g(3148);function O(F){var x=F==null?0:F.length;return x?(0,y.Z)(F,1):[]}j.Z=O},67876:function(Te,j,g){g.d(j,{Z:function(){return V}});var y=Object.prototype,O=y.hasOwnProperty;function F(B,R){return B!=null&&O.call(B,R)}var x=F,L=g(20534);function b(B,R){return B!=null&&(0,L.Z)(B,R,x)}var V=b},56288:function(Te,j,g){var y=g(71395),O=g(83788),F=g(54764),x="[object String]";function L(b){return typeof b=="string"||!(0,O.Z)(b)&&(0,F.Z)(b)&&(0,y.Z)(b)==x}j.Z=L},7683:function(Te,j){function g(y){var O=y==null?0:y.length;return O?y[O-1]:void 0}j.Z=g},857:function(Te,j,g){var y=g(3271),O=g(18458),F=g(46623),x=g(83788);function L(b,V){var B=(0,x.Z)(b)?y.Z:F.Z;return B(b,(0,O.Z)(V,3))}j.Z=L},93422:function(Te,j,g){var y=g(71818),O=g(65052),F=g(35272);function x(L){return L&&L.length?(0,y.Z)(L,F.Z,O.Z):void 0}j.Z=x},53689:function(Te,j,g){g.d(j,{Z:function(){return Z}});var y=/\s/;function O(P){for(var Q=P.length;Q--&&y.test(P.charAt(Q)););return Q}var F=O,x=/^\s+/;function L(P){return P&&P.slice(0,F(P)+1).replace(x,"")}var b=L,V=g(7247),B=g(80718),R=NaN,T=/^[-+]0x[0-9a-f]+$/i,E=/^0b[01]+$/i,_=/^0o[0-7]+$/i,v=parseInt;function p(P){if(typeof P=="number")return P;if((0,B.Z)(P))return R;if((0,V.Z)(P)){var Q=typeof P.valueOf=="function"?P.valueOf():P;P=(0,V.Z)(Q)?Q+"":Q}if(typeof P!="string")return P===0?P:+P;P=b(P);var J=E.test(P);return J||_.test(P)?v(P.slice(2),J?2:8):T.test(P)?R:+P}var C=p,h=1/0,S=17976931348623157e292;function w(P){if(!P)return P===0?P:0;if(P=C(P),P===h||P===-h){var Q=P<0?-1:1;return Q*S}return P===P?P:0}var Z=w},42013:function(Te,j,g){var y=g(53689);function O(F){var x=(0,y.Z)(F),L=x%1;return x===x?L?x-L:x:0}j.Z=O},43545:function(Te,j,g){g.d(j,{c:function(){return x},o:function(){return F}});var y=g(73656),O;(()=>{"use strict";var L={470:R=>{function T(v){if(typeof v!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(v))}function E(v,p){for(var C,h="",S=0,w=-1,Z=0,P=0;P<=v.length;++P){if(P<v.length)C=v.charCodeAt(P);else{if(C===47)break;C=47}if(C===47){if(!(w===P-1||Z===1))if(w!==P-1&&Z===2){if(h.length<2||S!==2||h.charCodeAt(h.length-1)!==46||h.charCodeAt(h.length-2)!==46){if(h.length>2){var Q=h.lastIndexOf("/");if(Q!==h.length-1){Q===-1?(h="",S=0):S=(h=h.slice(0,Q)).length-1-h.lastIndexOf("/"),w=P,Z=0;continue}}else if(h.length===2||h.length===1){h="",S=0,w=P,Z=0;continue}}p&&(h.length>0?h+="/..":h="..",S=2)}else h.length>0?h+="/"+v.slice(w+1,P):h=v.slice(w+1,P),S=P-w-1;w=P,Z=0}else C===46&&Z!==-1?++Z:Z=-1}return h}var _={resolve:function(){for(var v,p="",C=!1,h=arguments.length-1;h>=-1&&!C;h--){var S;h>=0?S=arguments[h]:(v===void 0&&(v=y.cwd()),S=v),T(S),S.length!==0&&(p=S+"/"+p,C=S.charCodeAt(0)===47)}return p=E(p,!C),C?p.length>0?"/"+p:"/":p.length>0?p:"."},normalize:function(v){if(T(v),v.length===0)return".";var p=v.charCodeAt(0)===47,C=v.charCodeAt(v.length-1)===47;return(v=E(v,!p)).length!==0||p||(v="."),v.length>0&&C&&(v+="/"),p?"/"+v:v},isAbsolute:function(v){return T(v),v.length>0&&v.charCodeAt(0)===47},join:function(){if(arguments.length===0)return".";for(var v,p=0;p<arguments.length;++p){var C=arguments[p];T(C),C.length>0&&(v===void 0?v=C:v+="/"+C)}return v===void 0?".":_.normalize(v)},relative:function(v,p){if(T(v),T(p),v===p||(v=_.resolve(v))===(p=_.resolve(p)))return"";for(var C=1;C<v.length&&v.charCodeAt(C)===47;++C);for(var h=v.length,S=h-C,w=1;w<p.length&&p.charCodeAt(w)===47;++w);for(var Z=p.length-w,P=S<Z?S:Z,Q=-1,J=0;J<=P;++J){if(J===P){if(Z>P){if(p.charCodeAt(w+J)===47)return p.slice(w+J+1);if(J===0)return p.slice(w+J)}else S>P&&(v.charCodeAt(C+J)===47?Q=J:J===0&&(Q=0));break}var ve=v.charCodeAt(C+J);if(ve!==p.charCodeAt(w+J))break;ve===47&&(Q=J)}var K="";for(J=C+Q+1;J<=h;++J)J!==h&&v.charCodeAt(J)!==47||(K.length===0?K+="..":K+="/..");return K.length>0?K+p.slice(w+Q):(w+=Q,p.charCodeAt(w)===47&&++w,p.slice(w))},_makeLong:function(v){return v},dirname:function(v){if(T(v),v.length===0)return".";for(var p=v.charCodeAt(0),C=p===47,h=-1,S=!0,w=v.length-1;w>=1;--w)if((p=v.charCodeAt(w))===47){if(!S){h=w;break}}else S=!1;return h===-1?C?"/":".":C&&h===1?"//":v.slice(0,h)},basename:function(v,p){if(p!==void 0&&typeof p!="string")throw new TypeError('"ext" argument must be a string');T(v);var C,h=0,S=-1,w=!0;if(p!==void 0&&p.length>0&&p.length<=v.length){if(p.length===v.length&&p===v)return"";var Z=p.length-1,P=-1;for(C=v.length-1;C>=0;--C){var Q=v.charCodeAt(C);if(Q===47){if(!w){h=C+1;break}}else P===-1&&(w=!1,P=C+1),Z>=0&&(Q===p.charCodeAt(Z)?--Z==-1&&(S=C):(Z=-1,S=P))}return h===S?S=P:S===-1&&(S=v.length),v.slice(h,S)}for(C=v.length-1;C>=0;--C)if(v.charCodeAt(C)===47){if(!w){h=C+1;break}}else S===-1&&(w=!1,S=C+1);return S===-1?"":v.slice(h,S)},extname:function(v){T(v);for(var p=-1,C=0,h=-1,S=!0,w=0,Z=v.length-1;Z>=0;--Z){var P=v.charCodeAt(Z);if(P!==47)h===-1&&(S=!1,h=Z+1),P===46?p===-1?p=Z:w!==1&&(w=1):p!==-1&&(w=-1);else if(!S){C=Z+1;break}}return p===-1||h===-1||w===0||w===1&&p===h-1&&p===C+1?"":v.slice(p,h)},format:function(v){if(v===null||typeof v!="object")throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof v);return function(p,C){var h=C.dir||C.root,S=C.base||(C.name||"")+(C.ext||"");return h?h===C.root?h+S:h+"/"+S:S}(0,v)},parse:function(v){T(v);var p={root:"",dir:"",base:"",ext:"",name:""};if(v.length===0)return p;var C,h=v.charCodeAt(0),S=h===47;S?(p.root="/",C=1):C=0;for(var w=-1,Z=0,P=-1,Q=!0,J=v.length-1,ve=0;J>=C;--J)if((h=v.charCodeAt(J))!==47)P===-1&&(Q=!1,P=J+1),h===46?w===-1?w=J:ve!==1&&(ve=1):w!==-1&&(ve=-1);else if(!Q){Z=J+1;break}return w===-1||P===-1||ve===0||ve===1&&w===P-1&&w===Z+1?P!==-1&&(p.base=p.name=Z===0&&S?v.slice(1,P):v.slice(Z,P)):(Z===0&&S?(p.name=v.slice(1,w),p.base=v.slice(1,P)):(p.name=v.slice(Z,w),p.base=v.slice(Z,P)),p.ext=v.slice(w,P)),Z>0?p.dir=v.slice(0,Z-1):S&&(p.dir="/"),p},sep:"/",delimiter:":",win32:null,posix:null};_.posix=_,R.exports=_}},b={};function V(R){var T=b[R];if(T!==void 0)return T.exports;var E=b[R]={exports:{}};return L[R](E,E.exports,V),E.exports}V.d=(R,T)=>{for(var E in T)V.o(T,E)&&!V.o(R,E)&&Object.defineProperty(R,E,{enumerable:!0,get:T[E]})},V.o=(R,T)=>Object.prototype.hasOwnProperty.call(R,T),V.r=R=>{typeof Symbol!="undefined"&&Symbol.toStringTag&&Object.defineProperty(R,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(R,"__esModule",{value:!0})};var B={};(()=>{let R;V.r(B),V.d(B,{URI:()=>S,Utils:()=>Ge}),typeof y=="object"?R=y.platform==="win32":typeof navigator=="object"&&(R=navigator.userAgent.indexOf("Windows")>=0);const T=/^\w[\w\d+.-]*$/,E=/^\//,_=/^\/\//;function v(oe,Y){if(!oe.scheme&&Y)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${oe.authority}", path: "${oe.path}", query: "${oe.query}", fragment: "${oe.fragment}"}`);if(oe.scheme&&!T.test(oe.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(oe.path){if(oe.authority){if(!E.test(oe.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(_.test(oe.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}const p="",C="/",h=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/;class S{constructor(Y,se,re,ke,ge,he=!1){Mn(this,"scheme");Mn(this,"authority");Mn(this,"path");Mn(this,"query");Mn(this,"fragment");typeof Y=="object"?(this.scheme=Y.scheme||p,this.authority=Y.authority||p,this.path=Y.path||p,this.query=Y.query||p,this.fragment=Y.fragment||p):(this.scheme=function(Me,je){return Me||je?Me:"file"}(Y,he),this.authority=se||p,this.path=function(Me,je){switch(Me){case"https":case"http":case"file":je?je[0]!==C&&(je=C+je):je=C}return je}(this.scheme,re||p),this.query=ke||p,this.fragment=ge||p,v(this,he))}static isUri(Y){return Y instanceof S||!!Y&&typeof Y.authority=="string"&&typeof Y.fragment=="string"&&typeof Y.path=="string"&&typeof Y.query=="string"&&typeof Y.scheme=="string"&&typeof Y.fsPath=="string"&&typeof Y.with=="function"&&typeof Y.toString=="function"}get fsPath(){return ve(this,!1)}with(Y){if(!Y)return this;let{scheme:se,authority:re,path:ke,query:ge,fragment:he}=Y;return se===void 0?se=this.scheme:se===null&&(se=p),re===void 0?re=this.authority:re===null&&(re=p),ke===void 0?ke=this.path:ke===null&&(ke=p),ge===void 0?ge=this.query:ge===null&&(ge=p),he===void 0?he=this.fragment:he===null&&(he=p),se===this.scheme&&re===this.authority&&ke===this.path&&ge===this.query&&he===this.fragment?this:new Z(se,re,ke,ge,he)}static parse(Y,se=!1){const re=h.exec(Y);return re?new Z(re[2]||p,ye(re[4]||p),ye(re[5]||p),ye(re[7]||p),ye(re[9]||p),se):new Z(p,p,p,p,p)}static file(Y){let se=p;if(R&&(Y=Y.replace(/\\/g,C)),Y[0]===C&&Y[1]===C){const re=Y.indexOf(C,2);re===-1?(se=Y.substring(2),Y=C):(se=Y.substring(2,re),Y=Y.substring(re)||C)}return new Z("file",se,Y,p,p)}static from(Y){const se=new Z(Y.scheme,Y.authority,Y.path,Y.query,Y.fragment);return v(se,!0),se}toString(Y=!1){return K(this,Y)}toJSON(){return this}static revive(Y){if(Y){if(Y instanceof S)return Y;{const se=new Z(Y);return se._formatted=Y.external,se._fsPath=Y._sep===w?Y.fsPath:null,se}}return Y}}const w=R?1:void 0;class Z extends S{constructor(){super(...arguments);Mn(this,"_formatted",null);Mn(this,"_fsPath",null)}get fsPath(){return this._fsPath||(this._fsPath=ve(this,!1)),this._fsPath}toString(se=!1){return se?K(this,!0):(this._formatted||(this._formatted=K(this,!1)),this._formatted)}toJSON(){const se={$mid:1};return this._fsPath&&(se.fsPath=this._fsPath,se._sep=w),this._formatted&&(se.external=this._formatted),this.path&&(se.path=this.path),this.scheme&&(se.scheme=this.scheme),this.authority&&(se.authority=this.authority),this.query&&(se.query=this.query),this.fragment&&(se.fragment=this.fragment),se}}const P={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function Q(oe,Y,se){let re,ke=-1;for(let ge=0;ge<oe.length;ge++){const he=oe.charCodeAt(ge);if(he>=97&&he<=122||he>=65&&he<=90||he>=48&&he<=57||he===45||he===46||he===95||he===126||Y&&he===47||se&&he===91||se&&he===93||se&&he===58)ke!==-1&&(re+=encodeURIComponent(oe.substring(ke,ge)),ke=-1),re!==void 0&&(re+=oe.charAt(ge));else{re===void 0&&(re=oe.substr(0,ge));const Me=P[he];Me!==void 0?(ke!==-1&&(re+=encodeURIComponent(oe.substring(ke,ge)),ke=-1),re+=Me):ke===-1&&(ke=ge)}}return ke!==-1&&(re+=encodeURIComponent(oe.substring(ke))),re!==void 0?re:oe}function J(oe){let Y;for(let se=0;se<oe.length;se++){const re=oe.charCodeAt(se);re===35||re===63?(Y===void 0&&(Y=oe.substr(0,se)),Y+=P[re]):Y!==void 0&&(Y+=oe[se])}return Y!==void 0?Y:oe}function ve(oe,Y){let se;return se=oe.authority&&oe.path.length>1&&oe.scheme==="file"?`//${oe.authority}${oe.path}`:oe.path.charCodeAt(0)===47&&(oe.path.charCodeAt(1)>=65&&oe.path.charCodeAt(1)<=90||oe.path.charCodeAt(1)>=97&&oe.path.charCodeAt(1)<=122)&&oe.path.charCodeAt(2)===58?Y?oe.path.substr(1):oe.path[1].toLowerCase()+oe.path.substr(2):oe.path,R&&(se=se.replace(/\//g,"\\")),se}function K(oe,Y){const se=Y?J:Q;let re="",{scheme:ke,authority:ge,path:he,query:Me,fragment:je}=oe;if(ke&&(re+=ke,re+=":"),(ge||ke==="file")&&(re+=C,re+=C),ge){let $e=ge.indexOf("@");if($e!==-1){const He=ge.substr(0,$e);ge=ge.substr($e+1),$e=He.lastIndexOf(":"),$e===-1?re+=se(He,!1,!1):(re+=se(He.substr(0,$e),!1,!1),re+=":",re+=se(He.substr($e+1),!1,!0)),re+="@"}ge=ge.toLowerCase(),$e=ge.lastIndexOf(":"),$e===-1?re+=se(ge,!1,!0):(re+=se(ge.substr(0,$e),!1,!0),re+=ge.substr($e))}if(he){if(he.length>=3&&he.charCodeAt(0)===47&&he.charCodeAt(2)===58){const $e=he.charCodeAt(1);$e>=65&&$e<=90&&(he=`/${String.fromCharCode($e+32)}:${he.substr(3)}`)}else if(he.length>=2&&he.charCodeAt(1)===58){const $e=he.charCodeAt(0);$e>=65&&$e<=90&&(he=`${String.fromCharCode($e+32)}:${he.substr(2)}`)}re+=se(he,!0,!1)}return Me&&(re+="?",re+=se(Me,!1,!1)),je&&(re+="#",re+=Y?je:Q(je,!1,!1)),re}function ae(oe){try{return decodeURIComponent(oe)}catch(Y){return oe.length>3?oe.substr(0,3)+ae(oe.substr(3)):oe}}const ce=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function ye(oe){return oe.match(ce)?oe.replace(ce,Y=>ae(Y)):oe}var Ae=V(470);const Se=Ae.posix||Ae,Je="/";var Ge;(function(oe){oe.joinPath=function(Y,...se){return Y.with({path:Se.join(Y.path,...se)})},oe.resolvePath=function(Y,...se){let re=Y.path,ke=!1;re[0]!==Je&&(re=Je+re,ke=!0);let ge=Se.resolve(re,...se);return ke&&ge[0]===Je&&!Y.authority&&(ge=ge.substring(1)),Y.with({path:ge})},oe.dirname=function(Y){if(Y.path.length===0||Y.path===Je)return Y;let se=Se.dirname(Y.path);return se.length===1&&se.charCodeAt(0)===46&&(se=""),Y.with({path:se})},oe.basename=function(Y){return Se.basename(Y.path)},oe.extname=function(Y){return Se.extname(Y.path)}})(Ge||(Ge={}))})(),O=B})();const{URI:F,Utils:x}=O}}]);
}());