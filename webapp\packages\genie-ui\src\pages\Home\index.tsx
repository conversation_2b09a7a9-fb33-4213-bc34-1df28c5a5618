import { useState, useCallback, memo } from "react";
import { DesktopOutlined, FileTextOutlined, FileExcelOutlined, TableOutlined } from '@ant-design/icons';
import GeneralInput from "../../components/GeneralInput";
import Slogn from "../../components/Slogn";
import ChatView from "../../components/ChatView";
import { productList, defaultProduct } from "../../utils/constants";
import { Image } from "antd";
import { demoList } from "../../utils/constants";

type HomeProps = Record<string, never>;

// 图标映射函数
const getProductIcon = (iconName: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    'icon-diannao': <DesktopOutlined />,
    'icon-wendang': <FileTextOutlined />,
    'icon-ppt': <FileExcelOutlined />,
    'icon-biaoge': <TableOutlined />,
  };
  return iconMap[iconName] || <DesktopOutlined />;
};

const Home: GenieType.FC<HomeProps> = memo(() => {
  const [inputInfo, setInputInfo] = useState<CHAT.TInputInfo>({
    message: "",
    deepThink: false,
  });
  const [product, setProduct] = useState(defaultProduct);
  const [videoModalOpen, setVideoModalOpen] = useState();

  const changeInputInfo = useCallback((info: CHAT.TInputInfo) => {
    setInputInfo(info);
  }, []);

  const renderContent = () => {
    if (inputInfo.message.length === 0) {
      return (
        <div className="pt-[120px] flex flex-col items-center">
          {/* <Slogn /> */}
          <div className="w-640 rounded-xl shadow-[0_18px_39px_0_rgba(198,202,240,0.1)]">
            <GeneralInput
              placeholder={product.placeholder}
              showBtn={true}
              size="big"
              disabled={false}
              product={product}
              send={changeInputInfo}
            />
          </div>
          <div className="w-640 flex flex-wrap gap-16 mt-[16px]">
            {productList.map((item, i) => (
              <div
                key={i}
                className={`w-[22%] h-[36px] cursor-pointer flex items-center justify-center border rounded-[8px] ${item.type === product.type ? "border-[#4040ff] bg-[rgba(64,64,255,0.02)] text-[#4040ff]" : "border-[#E9E9F0] text-[#666]"}`}
                onClick={() => setProduct(item)}
              >
                <span className={`text-14 ${item.color}`}>
                  {getProductIcon(item.img)}
                </span>
                <div className="ml-[6px]">{item.name}</div>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return <ChatView inputInfo={inputInfo} product={product} />;
  };

  return (
    <div className="h-full flex flex-col items-center ">
      {renderContent()}
    </div>
  );
});

Home.displayName = "Home";

export default Home;
