@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 确保 Tailwind utilities 有足够的优先级 */
@layer utilities {
  /* 确保 border 基础类生效 */
  .border {
    border-width: 1px !important;
    border-style: solid !important;
  }

  .border-red-500 {
    border-color: #ef4444 !important;
  }
  .border-gray-200 {
    border-color: #e5e7eb !important;
  }
  .border-blue-500 {
    border-color: #3b82f6 !important;
  }

  /* 确保 border-2 也生效 */
  .border-2 {
    border-width: 2px !important;
    border-style: solid !important;
  }
}

/* 项目特殊样式 - 内联避免路径问题 */
.no-border-textarea:focus{
  border: none;
  box-shadow: none;
}

/* 隐藏 TextArea 右下角的调整大小图标 */
.ant-input {
  resize: none !important;
}

.no-resize-textarea {
  resize: none !important;
}

/* 更具体的选择器确保 TextArea 不显示调整大小图标 */
.ant-input.no-resize-textarea,
textarea.no-resize-textarea,
.ant-input[class*="no-resize-textarea"] {
  resize: none !important;
}

/* 针对所有 textarea 元素 */
textarea {
  resize: none !important;
}

/* 自定义CSS变量和动画 */
:root {
  --spacing: 1px;
  --default-transition-duration: 0.3s;
}

@keyframes dot-pulse {
  0%,
  100% {
    transform: scale(1);
    background-color: #acbdff;
  }
  50% {
    transform: scale(1.5); /* 4px * 1.5 = 6px */
    background-color: #4040ff;
  }
}


html,
body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  height: 100%;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: bold;
}

a {
  color: #007bff;
  text-decoration: none;

  &:hover {
    color: #0056b3;
  }
}

ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

#root{
  height: 100%;
}

.no-scrollbar::-webkit-scrollbar { 
  display: none; 
} 
 
/* 为 IE、Edge 和 Firefox 提供支持 */ 
.no-scrollbar { 
  -ms-overflow-style: none;  /* IE 和 Edge */ 
  scrollbar-width: none;  /* Firefox */ 
} 

.delay-400ms {
  animation-delay: 0.4s !important;
}

.delay-0ms {
  animation-delay: 0s !important;
}
.delay-800ms {
  animation-delay: 0.8s !important;
}

.lottie{
  height: 80px !important;
}