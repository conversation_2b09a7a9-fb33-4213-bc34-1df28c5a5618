!(function(){"use strict";var tt=Object.defineProperty,rt=Object.defineProperties;var at=Object.getOwnPropertyDescriptors;var se=Object.getOwnPropertySymbols;var st=Object.prototype.hasOwnProperty,ot=Object.prototype.propertyIsEnumerable;var oe=(E,b,x)=>b in E?tt(E,b,{enumerable:!0,configurable:!0,writable:!0,value:x}):E[b]=x,U=(E,b)=>{for(var x in b||(b={}))st.call(b,x)&&oe(E,x,b[x]);if(se)for(var x of se(b))ot.call(b,x)&&oe(E,x,b[x]);return E},ce=(E,b)=>rt(E,at(b));var ne=(E,b,x)=>new Promise((q,R)=>{var v=W=>{try{P(x.next(W))}catch(g){R(g)}},c=W=>{try{P(x.throw(W))}catch(g){R(g)}},P=W=>W.done?q(W.value):Promise.resolve(W.value).then(v,c);P((x=x.apply(E,b)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[1592],{96547:function(E,b,x){x.d(b,{A:function(){return R}});var q=x(29134);function R(v,c){var P,W,g;v.accDescr&&((P=c.setAccDescription)==null||P.call(c,v.accDescr)),v.accTitle&&((W=c.setAccTitle)==null||W.call(c,v.accTitle)),v.title&&((g=c.setDiagramTitle)==null||g.call(c,v.title))}(0,q.eW)(R,"populateCommonDb")},78605:function(E,b,x){var v;x.d(b,{A:function(){return R}});var q=x(29134),R=(v=class{constructor(P){this.init=P,this.records=this.init()}reset(){this.records=this.init()}},(0,q.eW)(v,"ImperativeState"),v)},71592:function(E,b,x){x.d(b,{diagram:function(){return et}});var q=x(96547),R=x(78605),v=x(50854),c=x(29134),P=x(38663),W=x(69471),g={NORMAL:0,REVERSE:1,HIGHLIGHT:2,MERGE:3,CHERRY_PICK:4},ie=c.vZ.gitGraph,H=(0,c.eW)(()=>(0,v.Rb)(U(U({},ie),(0,c.iE)().gitGraph)),"getConfig"),d=new R.A(()=>{const a=H(),t=a.mainBranchName,r=a.mainBranchOrder;return{mainBranchName:t,commits:new Map,head:null,branchConfig:new Map([[t,{name:t,order:r}]]),branches:new Map([[t,null]]),currBranch:t,direction:"LR",seq:0,options:{}}});function K(){return(0,v.MX)({length:7})}(0,c.eW)(K,"getID");function V(a,t){const r=Object.create(null);return a.reduce((o,e)=>{const s=t(e);return r[s]||(r[s]=!0,o.push(e)),o},[])}(0,c.eW)(V,"uniqBy");var de=(0,c.eW)(function(a){d.records.direction=a},"setDirection"),he=(0,c.eW)(function(a){c.cM.debug("options str",a),a=a==null?void 0:a.trim(),a=a||"{}";try{d.records.options=JSON.parse(a)}catch(t){c.cM.error("error while parsing gitGraph options",t.message)}},"setOptions"),le=(0,c.eW)(function(){return d.records.options},"getOptions"),me=(0,c.eW)(function(a){let t=a.msg,r=a.id;const o=a.type;let e=a.tags;c.cM.info("commit",t,r,o,e),c.cM.debug("Entering commit:",t,r,o,e);const s=H();r=c.SY.sanitizeText(r,s),t=c.SY.sanitizeText(t,s),e=e==null?void 0:e.map(n=>c.SY.sanitizeText(n,s));const i={id:r||d.records.seq+"-"+K(),message:t,seq:d.records.seq++,type:o!=null?o:g.NORMAL,tags:e!=null?e:[],parents:d.records.head==null?[]:[d.records.head.id],branch:d.records.currBranch};d.records.head=i,c.cM.info("main branch",s.mainBranchName),d.records.commits.has(i.id)&&c.cM.warn(`Commit ID ${i.id} already exists`),d.records.commits.set(i.id,i),d.records.branches.set(d.records.currBranch,i.id),c.cM.debug("in pushCommit "+i.id)},"commit"),pe=(0,c.eW)(function(a){let t=a.name;const r=a.order;if(t=c.SY.sanitizeText(t,H()),d.records.branches.has(t))throw new Error(`Trying to create an existing branch. (Help: Either use a new name if you want create a new branch or try using "checkout ${t}")`);d.records.branches.set(t,d.records.head!=null?d.records.head.id:null),d.records.branchConfig.set(t,{name:t,order:r}),Q(t),c.cM.debug("in createBranch")},"branch"),fe=(0,c.eW)(a=>{let t=a.branch,r=a.id;const o=a.type,e=a.tags,s=H();t=c.SY.sanitizeText(t,s),r&&(r=c.SY.sanitizeText(r,s));const i=d.records.branches.get(d.records.currBranch),n=d.records.branches.get(t),m=i?d.records.commits.get(i):void 0,l=n?d.records.commits.get(n):void 0;if(m&&l&&m.branch===t)throw new Error(`Cannot merge branch '${t}' into itself.`);if(d.records.currBranch===t){const h=new Error('Incorrect usage of "merge". Cannot merge a branch to itself');throw h.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["branch abc"]},h}if(m===void 0||!m){const h=new Error(`Incorrect usage of "merge". Current branch (${d.records.currBranch})has no commits`);throw h.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["commit"]},h}if(!d.records.branches.has(t)){const h=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") does not exist");throw h.hash={text:`merge ${t}`,token:`merge ${t}`,expected:[`branch ${t}`]},h}if(l===void 0||!l){const h=new Error('Incorrect usage of "merge". Branch to be merged ('+t+") has no commits");throw h.hash={text:`merge ${t}`,token:`merge ${t}`,expected:['"commit"']},h}if(m===l){const h=new Error('Incorrect usage of "merge". Both branches have same head');throw h.hash={text:`merge ${t}`,token:`merge ${t}`,expected:["branch abc"]},h}if(r&&d.records.commits.has(r)){const h=new Error('Incorrect usage of "merge". Commit with id:'+r+" already exists, use different custom id");throw h.hash={text:`merge ${t} ${r} ${o} ${e==null?void 0:e.join(" ")}`,token:`merge ${t} ${r} ${o} ${e==null?void 0:e.join(" ")}`,expected:[`merge ${t} ${r}_UNIQUE ${o} ${e==null?void 0:e.join(" ")}`]},h}const p=n||"",$={id:r||`${d.records.seq}-${K()}`,message:`merged branch ${t} into ${d.records.currBranch}`,seq:d.records.seq++,parents:d.records.head==null?[]:[d.records.head.id,p],branch:d.records.currBranch,type:g.MERGE,customType:o,customId:!!r,tags:e!=null?e:[]};d.records.head=$,d.records.commits.set($.id,$),d.records.branches.set(d.records.currBranch,$.id),c.cM.debug(d.records.branches),c.cM.debug("in mergeBranch")},"merge"),ge=(0,c.eW)(function(a){let t=a.id,r=a.targetId,o=a.tags,e=a.parent;c.cM.debug("Entering cherryPick:",t,r,o);const s=H();if(t=c.SY.sanitizeText(t,s),r=c.SY.sanitizeText(r,s),o=o==null?void 0:o.map(m=>c.SY.sanitizeText(m,s)),e=c.SY.sanitizeText(e,s),!t||!d.records.commits.has(t)){const m=new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');throw m.hash={text:`cherryPick ${t} ${r}`,token:`cherryPick ${t} ${r}`,expected:["cherry-pick abc"]},m}const i=d.records.commits.get(t);if(i===void 0||!i)throw new Error('Incorrect usage of "cherryPick". Source commit id should exist and provided');if(e&&!(Array.isArray(i.parents)&&i.parents.includes(e)))throw new Error("Invalid operation: The specified parent commit is not an immediate parent of the cherry-picked commit.");const n=i.branch;if(i.type===g.MERGE&&!e)throw new Error("Incorrect usage of cherry-pick: If the source commit is a merge commit, an immediate parent commit must be specified.");if(!r||!d.records.commits.has(r)){if(n===d.records.currBranch){const $=new Error('Incorrect usage of "cherryPick". Source commit is already on current branch');throw $.hash={text:`cherryPick ${t} ${r}`,token:`cherryPick ${t} ${r}`,expected:["cherry-pick abc"]},$}const m=d.records.branches.get(d.records.currBranch);if(m===void 0||!m){const $=new Error(`Incorrect usage of "cherry-pick". Current branch (${d.records.currBranch})has no commits`);throw $.hash={text:`cherryPick ${t} ${r}`,token:`cherryPick ${t} ${r}`,expected:["cherry-pick abc"]},$}const l=d.records.commits.get(m);if(l===void 0||!l){const $=new Error(`Incorrect usage of "cherry-pick". Current branch (${d.records.currBranch})has no commits`);throw $.hash={text:`cherryPick ${t} ${r}`,token:`cherryPick ${t} ${r}`,expected:["cherry-pick abc"]},$}const p={id:d.records.seq+"-"+K(),message:`cherry-picked ${i==null?void 0:i.message} into ${d.records.currBranch}`,seq:d.records.seq++,parents:d.records.head==null?[]:[d.records.head.id,i.id],branch:d.records.currBranch,type:g.CHERRY_PICK,tags:o?o.filter(Boolean):[`cherry-pick:${i.id}${i.type===g.MERGE?`|parent:${e}`:""}`]};d.records.head=p,d.records.commits.set(p.id,p),d.records.branches.set(d.records.currBranch,p.id),c.cM.debug(d.records.branches),c.cM.debug("in cherryPick")}},"cherryPick"),Q=(0,c.eW)(function(a){var t;if(a=c.SY.sanitizeText(a,H()),d.records.branches.has(a)){d.records.currBranch=a;const r=d.records.branches.get(d.records.currBranch);r===void 0||!r?d.records.head=null:d.records.head=(t=d.records.commits.get(r))!=null?t:null}else{const r=new Error(`Trying to checkout branch which is not yet created. (Help try using "branch ${a}")`);throw r.hash={text:`checkout ${a}`,token:`checkout ${a}`,expected:[`branch ${a}`]},r}},"checkout");function Z(a,t,r){const o=a.indexOf(t);o===-1?a.push(r):a.splice(o,1,r)}(0,c.eW)(Z,"upsert");function X(a){const t=a.reduce((e,s)=>e.seq>s.seq?e:s,a[0]);let r="";a.forEach(function(e){e===t?r+="	*":r+="	|"});const o=[r,t.id,t.seq];for(const e in d.records.branches)d.records.branches.get(e)===t.id&&o.push(e);if(c.cM.debug(o.join(" ")),t.parents&&t.parents.length==2&&t.parents[0]&&t.parents[1]){const e=d.records.commits.get(t.parents[0]);Z(a,t,e),t.parents[1]&&a.push(d.records.commits.get(t.parents[1]))}else{if(t.parents.length==0)return;if(t.parents[0]){const e=d.records.commits.get(t.parents[0]);Z(a,t,e)}}a=V(a,e=>e.id),X(a)}(0,c.eW)(X,"prettyPrintCommitHistory");var ye=(0,c.eW)(function(){c.cM.debug(d.records.commits);const a=F()[0];X([a])},"prettyPrint"),$e=(0,c.eW)(function(){d.reset(),(0,c.ZH)()},"clear"),xe=(0,c.eW)(function(){return[...d.records.branchConfig.values()].map((t,r)=>t.order!==null&&t.order!==void 0?t:ce(U({},t),{order:parseFloat(`0.${r}`)})).sort((t,r)=>{var o,e;return((o=t.order)!=null?o:0)-((e=r.order)!=null?e:0)}).map(({name:t})=>({name:t}))},"getBranchesAsObjArray"),ue=(0,c.eW)(function(){return d.records.branches},"getBranches"),be=(0,c.eW)(function(){return d.records.commits},"getCommits"),F=(0,c.eW)(function(){const a=[...d.records.commits.values()];return a.forEach(function(t){c.cM.debug(t.id)}),a.sort((t,r)=>t.seq-r.seq),a},"getCommitsArray"),we=(0,c.eW)(function(){return d.records.currBranch},"getCurrentBranch"),Be=(0,c.eW)(function(){return d.records.direction},"getDirection"),ve=(0,c.eW)(function(){return d.records.head},"getHead"),ee={commitType:g,getConfig:H,setDirection:de,setOptions:he,getOptions:le,commit:me,branch:pe,merge:fe,cherryPick:ge,checkout:Q,prettyPrint:ye,clear:$e,getBranchesAsObjArray:xe,getBranches:ue,getCommits:be,getCommitsArray:F,getCurrentBranch:we,getDirection:Be,getHead:ve,setAccTitle:c.GN,getAccTitle:c.eu,getAccDescription:c.Mx,setAccDescription:c.U$,setDiagramTitle:c.g2,getDiagramTitle:c.Kr},Ce=(0,c.eW)((a,t)=>{(0,q.A)(a,t),a.dir&&t.setDirection(a.dir);for(const r of a.statements)Ee(r,t)},"populate"),Ee=(0,c.eW)((a,t)=>{const o={Commit:(0,c.eW)(e=>t.commit(ke(e)),"Commit"),Branch:(0,c.eW)(e=>t.branch(Me(e)),"Branch"),Merge:(0,c.eW)(e=>t.merge(Te(e)),"Merge"),Checkout:(0,c.eW)(e=>t.checkout(We(e)),"Checkout"),CherryPicking:(0,c.eW)(e=>t.cherryPick(Le(e)),"CherryPicking")}[a.$type];o?o(a):c.cM.error(`Unknown statement type: ${a.$type}`)},"parseStatement"),ke=(0,c.eW)(a=>{var r,o;return{id:a.id,msg:(r=a.message)!=null?r:"",type:a.type!==void 0?g[a.type]:g.NORMAL,tags:(o=a.tags)!=null?o:void 0}},"parseCommit"),Me=(0,c.eW)(a=>{var r;return{name:a.name,order:(r=a.order)!=null?r:0}},"parseBranch"),Te=(0,c.eW)(a=>{var r,o;return{branch:a.branch,id:(r=a.id)!=null?r:"",type:a.type!==void 0?g[a.type]:void 0,tags:(o=a.tags)!=null?o:void 0}},"parseMerge"),We=(0,c.eW)(a=>a.branch,"parseCheckout"),Le=(0,c.eW)(a=>{var r;return{id:a.id,targetId:"",tags:((r=a.tags)==null?void 0:r.length)===0?void 0:a.tags,parent:a.parent}},"parseCherryPicking"),Pe={parse:(0,c.eW)(a=>ne(this,null,function*(){const t=yield(0,P.Qc)("gitGraph",a);c.cM.debug(t),Ce(t,ee)}),"parse")},J=(0,c.nV)(),w=J==null?void 0:J.gitGraph,D=10,A=40,_=4,O=2,G=8,k=new Map,M=new Map,j=30,Y=new Map,z=[],I=0,y="LR",_e=(0,c.eW)(()=>{k.clear(),M.clear(),Y.clear(),I=0,z=[],y="LR"},"clear"),te=(0,c.eW)(a=>{const t=document.createElementNS("http://www.w3.org/2000/svg","text");return(typeof a=="string"?a.split(/\\n|\n|<br\s*\/?>/gi):a).forEach(o=>{const e=document.createElementNS("http://www.w3.org/2000/svg","tspan");e.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),e.setAttribute("dy","1em"),e.setAttribute("x","0"),e.setAttribute("class","row"),e.textContent=o.trim(),t.appendChild(e)}),t},"drawText"),re=(0,c.eW)(a=>{let t,r,o;return y==="BT"?(r=(0,c.eW)((e,s)=>e<=s,"comparisonFunc"),o=1/0):(r=(0,c.eW)((e,s)=>e>=s,"comparisonFunc"),o=0),a.forEach(e=>{var i,n;const s=y==="TB"||y=="BT"?(i=M.get(e))==null?void 0:i.y:(n=M.get(e))==null?void 0:n.x;s!==void 0&&r(s,o)&&(t=e,o=s)}),t},"findClosestParent"),Re=(0,c.eW)(a=>{let t="",r=1/0;return a.forEach(o=>{const e=M.get(o).y;e<=r&&(t=o,r=e)}),t||void 0},"findClosestParentBT"),Oe=(0,c.eW)((a,t,r)=>{let o=r,e=r;const s=[];a.forEach(i=>{const n=t.get(i);if(!n)throw new Error(`Commit not found for key ${i}`);n.parents.length?(o=Ae(n),e=Math.max(o,e)):s.push(n),Ie(n,o)}),o=e,s.forEach(i=>{qe(i,o,r)}),a.forEach(i=>{const n=t.get(i);if(n!=null&&n.parents.length){const m=Re(n.parents);o=M.get(m).y-A,o<=e&&(e=o);const l=k.get(n.branch).pos,p=o-D;M.set(n.id,{x:l,y:p})}})},"setParallelBTPos"),De=(0,c.eW)(a=>{var o;const t=re(a.parents.filter(e=>e!==null));if(!t)throw new Error(`Closest parent not found for commit ${a.id}`);const r=(o=M.get(t))==null?void 0:o.y;if(r===void 0)throw new Error(`Closest parent position not found for commit ${a.id}`);return r},"findClosestParentPos"),Ae=(0,c.eW)(a=>De(a)+A,"calculateCommitPosition"),Ie=(0,c.eW)((a,t)=>{const r=k.get(a.branch);if(!r)throw new Error(`Branch not found for commit ${a.id}`);const o=r.pos,e=t+D;return M.set(a.id,{x:o,y:e}),{x:o,y:e}},"setCommitPosition"),qe=(0,c.eW)((a,t,r)=>{const o=k.get(a.branch);if(!o)throw new Error(`Branch not found for commit ${a.id}`);const e=t+r,s=o.pos;M.set(a.id,{x:s,y:e})},"setRootPosition"),He=(0,c.eW)((a,t,r,o,e,s)=>{if(s===g.HIGHLIGHT)a.append("rect").attr("x",r.x-10).attr("y",r.y-10).attr("width",20).attr("height",20).attr("class",`commit ${t.id} commit-highlight${e%G} ${o}-outer`),a.append("rect").attr("x",r.x-6).attr("y",r.y-6).attr("width",12).attr("height",12).attr("class",`commit ${t.id} commit${e%G} ${o}-inner`);else if(s===g.CHERRY_PICK)a.append("circle").attr("cx",r.x).attr("cy",r.y).attr("r",10).attr("class",`commit ${t.id} ${o}`),a.append("circle").attr("cx",r.x-3).attr("cy",r.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${t.id} ${o}`),a.append("circle").attr("cx",r.x+3).attr("cy",r.y+2).attr("r",2.75).attr("fill","#fff").attr("class",`commit ${t.id} ${o}`),a.append("line").attr("x1",r.x+3).attr("y1",r.y+1).attr("x2",r.x).attr("y2",r.y-5).attr("stroke","#fff").attr("class",`commit ${t.id} ${o}`),a.append("line").attr("x1",r.x-3).attr("y1",r.y+1).attr("x2",r.x).attr("y2",r.y-5).attr("stroke","#fff").attr("class",`commit ${t.id} ${o}`);else{const i=a.append("circle");if(i.attr("cx",r.x),i.attr("cy",r.y),i.attr("r",t.type===g.MERGE?9:10),i.attr("class",`commit ${t.id} commit${e%G}`),s===g.MERGE){const n=a.append("circle");n.attr("cx",r.x),n.attr("cy",r.y),n.attr("r",6),n.attr("class",`commit ${o} ${t.id} commit${e%G}`)}s===g.REVERSE&&a.append("path").attr("d",`M ${r.x-5},${r.y-5}L${r.x+5},${r.y+5}M${r.x-5},${r.y+5}L${r.x+5},${r.y-5}`).attr("class",`commit ${o} ${t.id} commit${e%G}`)}},"drawCommitBullet"),Ge=(0,c.eW)((a,t,r,o)=>{var e;if(t.type!==g.CHERRY_PICK&&(t.customId&&t.type===g.MERGE||t.type!==g.MERGE)&&(w!=null&&w.showCommitLabel)){const s=a.append("g"),i=s.insert("rect").attr("class","commit-label-bkg"),n=s.append("text").attr("x",o).attr("y",r.y+25).attr("class","commit-label").text(t.id),m=(e=n.node())==null?void 0:e.getBBox();if(m&&(i.attr("x",r.posWithOffset-m.width/2-O).attr("y",r.y+13.5).attr("width",m.width+2*O).attr("height",m.height+2*O),y==="TB"||y==="BT"?(i.attr("x",r.x-(m.width+4*_+5)).attr("y",r.y-12),n.attr("x",r.x-(m.width+4*_)).attr("y",r.y+m.height-12)):n.attr("x",r.posWithOffset-m.width/2),w.rotateCommitLabel))if(y==="TB"||y==="BT")n.attr("transform","rotate(-45, "+r.x+", "+r.y+")"),i.attr("transform","rotate(-45, "+r.x+", "+r.y+")");else{const l=-7.5-(m.width+10)/25*9.5,p=10+m.width/25*8.5;s.attr("transform","translate("+l+", "+p+") rotate(-45, "+o+", "+r.y+")")}}},"drawCommitLabel"),Se=(0,c.eW)((a,t,r,o)=>{var e;if(t.tags.length>0){let s=0,i=0,n=0;const m=[];for(const l of t.tags.reverse()){const p=a.insert("polygon"),$=a.append("circle"),h=a.append("text").attr("y",r.y-16-s).attr("class","tag-label").text(l),f=(e=h.node())==null?void 0:e.getBBox();if(!f)throw new Error("Tag bbox not found");i=Math.max(i,f.width),n=Math.max(n,f.height),h.attr("x",r.posWithOffset-f.width/2),m.push({tag:h,hole:$,rect:p,yOffset:s}),s+=20}for(const{tag:l,hole:p,rect:$,yOffset:h}of m){const f=n/2,u=r.y-19.2-h;if($.attr("class","tag-label-bkg").attr("points",`
      ${o-i/2-_/2},${u+O}  
      ${o-i/2-_/2},${u-O}
      ${r.posWithOffset-i/2-_},${u-f-O}
      ${r.posWithOffset+i/2+_},${u-f-O}
      ${r.posWithOffset+i/2+_},${u+f+O}
      ${r.posWithOffset-i/2-_},${u+f+O}`),p.attr("cy",u).attr("cx",o-i/2+_/2).attr("r",1.5).attr("class","tag-hole"),y==="TB"||y==="BT"){const B=o+h;$.attr("class","tag-label-bkg").attr("points",`
        ${r.x},${B+2}
        ${r.x},${B-2}
        ${r.x+D},${B-f-2}
        ${r.x+D+i+4},${B-f-2}
        ${r.x+D+i+4},${B+f+2}
        ${r.x+D},${B+f+2}`).attr("transform","translate(12,12) rotate(45, "+r.x+","+o+")"),p.attr("cx",r.x+_/2).attr("cy",B).attr("transform","translate(12,12) rotate(45, "+r.x+","+o+")"),l.attr("x",r.x+5).attr("y",B+3).attr("transform","translate(14,14) rotate(45, "+r.x+","+o+")")}}}},"drawCommitTags"),Ye=(0,c.eW)(a=>{var r;switch((r=a.customType)!=null?r:a.type){case g.NORMAL:return"commit-normal";case g.REVERSE:return"commit-reverse";case g.HIGHLIGHT:return"commit-highlight";case g.MERGE:return"commit-merge";case g.CHERRY_PICK:return"commit-cherry-pick";default:return"commit-normal"}},"getCommitClassType"),Ne=(0,c.eW)((a,t,r,o)=>{var s,i,n;const e={x:0,y:0};if(a.parents.length>0){const m=re(a.parents);if(m){const l=(s=o.get(m))!=null?s:e;return t==="TB"?l.y+A:t==="BT"?((i=o.get(a.id))!=null?i:e).y-A:l.x+A}}else return t==="TB"?j:t==="BT"?((n=o.get(a.id))!=null?n:e).y-A:0;return 0},"calculatePosition"),Ke=(0,c.eW)((a,t,r)=>{var i,n;const o=y==="BT"&&r?t:t+D,e=y==="TB"||y==="BT"?o:(i=k.get(a.branch))==null?void 0:i.pos,s=y==="TB"||y==="BT"?(n=k.get(a.branch))==null?void 0:n.pos:o;if(s===void 0||e===void 0)throw new Error(`Position were undefined for commit ${a.id}`);return{x:s,y:e,posWithOffset:o}},"getCommitPosition"),ae=(0,c.eW)((a,t,r)=>{var p;if(!w)throw new Error("GitGraph config not found");const o=a.append("g").attr("class","commit-bullets"),e=a.append("g").attr("class","commit-labels");let s=y==="TB"||y==="BT"?j:0;const i=[...t.keys()],n=(p=w==null?void 0:w.parallelCommits)!=null?p:!1,m=(0,c.eW)(($,h)=>{var B,L;const f=(B=t.get($))==null?void 0:B.seq,u=(L=t.get(h))==null?void 0:L.seq;return f!==void 0&&u!==void 0?f-u:0},"sortKeys");let l=i.sort(m);y==="BT"&&(n&&Oe(l,t,s),l=l.reverse()),l.forEach($=>{var u,B,L;const h=t.get($);if(!h)throw new Error(`Commit not found for key ${$}`);n&&(s=Ne(h,y,s,M));const f=Ke(h,s,n);if(r){const S=Ye(h),T=(u=h.customType)!=null?u:h.type,C=(L=(B=k.get(h.branch))==null?void 0:B.index)!=null?L:0;He(o,h,f,S,C,T),Ge(e,h,f,s),Se(e,h,f,s)}y==="TB"||y==="BT"?M.set(h.id,{x:f.x,y:f.posWithOffset}):M.set(h.id,{x:f.posWithOffset,y:f.y}),s=y==="BT"&&n?s+A:s+A+D,s>I&&(I=s)})},"drawCommits"),je=(0,c.eW)((a,t,r,o,e)=>{const i=(y==="TB"||y==="BT"?r.x<o.x:r.y<o.y)?t.branch:a.branch,n=(0,c.eW)(l=>l.branch===i,"isOnBranchToGetCurve"),m=(0,c.eW)(l=>l.seq>a.seq&&l.seq<t.seq,"isBetweenCommits");return[...e.values()].some(l=>m(l)&&n(l))},"shouldRerouteArrow"),N=(0,c.eW)((a,t,r=0)=>{const o=a+Math.abs(a-t)/2;if(r>5)return o;if(z.every(i=>Math.abs(i-o)>=10))return z.push(o),o;const s=Math.abs(a-t);return N(a,t-s/5,r+1)},"findLane"),ze=(0,c.eW)((a,t,r,o)=>{var f,u,B,L,S;const e=M.get(t.id),s=M.get(r.id);if(e===void 0||s===void 0)throw new Error(`Commit positions not found for commits ${t.id} and ${r.id}`);const i=je(t,r,e,s,o);let n="",m="",l=0,p=0,$=(f=k.get(r.branch))==null?void 0:f.index;r.type===g.MERGE&&t.id!==r.parents[0]&&($=(u=k.get(t.branch))==null?void 0:u.index);let h;if(i){n="A 10 10, 0, 0, 0,",m="A 10 10, 0, 0, 1,",l=10,p=10;const T=e.y<s.y?N(e.y,s.y):N(s.y,e.y),C=e.x<s.x?N(e.x,s.x):N(s.x,e.x);y==="TB"?e.x<s.x?h=`M ${e.x} ${e.y} L ${C-l} ${e.y} ${m} ${C} ${e.y+p} L ${C} ${s.y-l} ${n} ${C+p} ${s.y} L ${s.x} ${s.y}`:($=(B=k.get(t.branch))==null?void 0:B.index,h=`M ${e.x} ${e.y} L ${C+l} ${e.y} ${n} ${C} ${e.y+p} L ${C} ${s.y-l} ${m} ${C-p} ${s.y} L ${s.x} ${s.y}`):y==="BT"?e.x<s.x?h=`M ${e.x} ${e.y} L ${C-l} ${e.y} ${n} ${C} ${e.y-p} L ${C} ${s.y+l} ${m} ${C+p} ${s.y} L ${s.x} ${s.y}`:($=(L=k.get(t.branch))==null?void 0:L.index,h=`M ${e.x} ${e.y} L ${C+l} ${e.y} ${m} ${C} ${e.y-p} L ${C} ${s.y+l} ${n} ${C-p} ${s.y} L ${s.x} ${s.y}`):e.y<s.y?h=`M ${e.x} ${e.y} L ${e.x} ${T-l} ${n} ${e.x+p} ${T} L ${s.x-l} ${T} ${m} ${s.x} ${T+p} L ${s.x} ${s.y}`:($=(S=k.get(t.branch))==null?void 0:S.index,h=`M ${e.x} ${e.y} L ${e.x} ${T+l} ${m} ${e.x+p} ${T} L ${s.x-l} ${T} ${n} ${s.x} ${T-p} L ${s.x} ${s.y}`)}else n="A 20 20, 0, 0, 0,",m="A 20 20, 0, 0, 1,",l=20,p=20,y==="TB"?(e.x<s.x&&(r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${e.x} ${s.y-l} ${n} ${e.x+p} ${s.y} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${s.x-l} ${e.y} ${m} ${s.x} ${e.y+p} L ${s.x} ${s.y}`),e.x>s.x&&(n="A 20 20, 0, 0, 0,",m="A 20 20, 0, 0, 1,",l=20,p=20,r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${e.x} ${s.y-l} ${m} ${e.x-p} ${s.y} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${s.x+l} ${e.y} ${n} ${s.x} ${e.y+p} L ${s.x} ${s.y}`),e.x===s.x&&(h=`M ${e.x} ${e.y} L ${s.x} ${s.y}`)):y==="BT"?(e.x<s.x&&(r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${e.x} ${s.y+l} ${m} ${e.x+p} ${s.y} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${s.x-l} ${e.y} ${n} ${s.x} ${e.y-p} L ${s.x} ${s.y}`),e.x>s.x&&(n="A 20 20, 0, 0, 0,",m="A 20 20, 0, 0, 1,",l=20,p=20,r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${e.x} ${s.y+l} ${n} ${e.x-p} ${s.y} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${s.x-l} ${e.y} ${n} ${s.x} ${e.y-p} L ${s.x} ${s.y}`),e.x===s.x&&(h=`M ${e.x} ${e.y} L ${s.x} ${s.y}`)):(e.y<s.y&&(r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${s.x-l} ${e.y} ${m} ${s.x} ${e.y+p} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${e.x} ${s.y-l} ${n} ${e.x+p} ${s.y} L ${s.x} ${s.y}`),e.y>s.y&&(r.type===g.MERGE&&t.id!==r.parents[0]?h=`M ${e.x} ${e.y} L ${s.x-l} ${e.y} ${n} ${s.x} ${e.y-p} L ${s.x} ${s.y}`:h=`M ${e.x} ${e.y} L ${e.x} ${s.y+l} ${m} ${e.x+p} ${s.y} L ${s.x} ${s.y}`),e.y===s.y&&(h=`M ${e.x} ${e.y} L ${s.x} ${s.y}`));if(h===void 0)throw new Error("Line definition not found");a.append("path").attr("d",h).attr("class","arrow arrow"+$%G)},"drawArrow"),Ue=(0,c.eW)((a,t)=>{const r=a.append("g").attr("class","commit-arrows");[...t.keys()].forEach(o=>{const e=t.get(o);e.parents&&e.parents.length>0&&e.parents.forEach(s=>{ze(r,t.get(s),e,t)})})},"drawArrows"),Ze=(0,c.eW)((a,t)=>{const r=a.append("g");t.forEach((o,e)=>{var u;const s=e%G,i=(u=k.get(o.name))==null?void 0:u.pos;if(i===void 0)throw new Error(`Position not found for branch ${o.name}`);const n=r.append("line");n.attr("x1",0),n.attr("y1",i),n.attr("x2",I),n.attr("y2",i),n.attr("class","branch branch"+s),y==="TB"?(n.attr("y1",j),n.attr("x1",i),n.attr("y2",I),n.attr("x2",i)):y==="BT"&&(n.attr("y1",I),n.attr("x1",i),n.attr("y2",j),n.attr("x2",i)),z.push(i);const m=o.name,l=te(m),p=r.insert("rect"),h=r.insert("g").attr("class","branchLabel").insert("g").attr("class","label branch-label"+s);h.node().appendChild(l);const f=l.getBBox();p.attr("class","branchLabelBkg label"+s).attr("rx",4).attr("ry",4).attr("x",-f.width-4-((w==null?void 0:w.rotateCommitLabel)===!0?30:0)).attr("y",-f.height/2+8).attr("width",f.width+18).attr("height",f.height+4),h.attr("transform","translate("+(-f.width-14-((w==null?void 0:w.rotateCommitLabel)===!0?30:0))+", "+(i-f.height/2-1)+")"),y==="TB"?(p.attr("x",i-f.width/2-10).attr("y",0),h.attr("transform","translate("+(i-f.width/2-5)+", 0)")):y==="BT"?(p.attr("x",i-f.width/2-10).attr("y",I),h.attr("transform","translate("+(i-f.width/2-5)+", "+I+")")):p.attr("transform","translate(-19, "+(i-f.height/2)+")")})},"drawBranches"),Xe=(0,c.eW)(function(a,t,r,o,e){return k.set(a,{pos:t,index:r}),t+=50+(e?40:0)+(y==="TB"||y==="BT"?o.width/2:0),t},"setBranchPosition"),Je=(0,c.eW)(function(a,t,r,o){var l,p;if(_e(),c.cM.debug("in gitgraph renderer",a+`
`,"id:",t,r),!w)throw new Error("GitGraph config not found");const e=(l=w.rotateCommitLabel)!=null?l:!1,s=o.db;Y=s.getCommits();const i=s.getBranchesAsObjArray();y=s.getDirection();const n=(0,W.Ys)(`[id="${t}"]`);let m=0;i.forEach(($,h)=>{var T;const f=te($.name),u=n.append("g"),B=u.insert("g").attr("class","branchLabel"),L=B.insert("g").attr("class","label branch-label");(T=L.node())==null||T.appendChild(f);const S=f.getBBox();m=Xe($.name,m,h,S,e),L.remove(),B.remove(),u.remove()}),ae(n,Y,!1),w.showBranches&&Ze(n,i),Ue(n,Y),ae(n,Y,!0),v.w8.insertTitle(n,"gitTitleText",(p=w.titleTopMargin)!=null?p:0,s.getDiagramTitle()),(0,c.Rw)(void 0,n,w.diagramPadding,w.useMaxWidth)},"draw"),Ve={draw:Je},Qe=(0,c.eW)(a=>`
  .commit-id,
  .commit-msg,
  .branch-label {
    fill: lightgrey;
    color: lightgrey;
    font-family: 'trebuchet ms', verdana, arial, sans-serif;
    font-family: var(--mermaid-font-family);
  }
  ${[0,1,2,3,4,5,6,7].map(t=>`
        .branch-label${t} { fill: ${a["gitBranchLabel"+t]}; }
        .commit${t} { stroke: ${a["git"+t]}; fill: ${a["git"+t]}; }
        .commit-highlight${t} { stroke: ${a["gitInv"+t]}; fill: ${a["gitInv"+t]}; }
        .label${t}  { fill: ${a["git"+t]}; }
        .arrow${t} { stroke: ${a["git"+t]}; }
        `).join(`
`)}

  .branch {
    stroke-width: 1;
    stroke: ${a.lineColor};
    stroke-dasharray: 2;
  }
  .commit-label { font-size: ${a.commitLabelFontSize}; fill: ${a.commitLabelColor};}
  .commit-label-bkg { font-size: ${a.commitLabelFontSize}; fill: ${a.commitLabelBackground}; opacity: 0.5; }
  .tag-label { font-size: ${a.tagLabelFontSize}; fill: ${a.tagLabelColor};}
  .tag-label-bkg { fill: ${a.tagLabelBackground}; stroke: ${a.tagLabelBorder}; }
  .tag-hole { fill: ${a.textColor}; }

  .commit-merge {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
  }
  .commit-reverse {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
    stroke-width: 3;
  }
  .commit-highlight-outer {
  }
  .commit-highlight-inner {
    stroke: ${a.primaryColor};
    fill: ${a.primaryColor};
  }

  .arrow { stroke-width: 8; stroke-linecap: round; fill: none}
  .gitTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${a.textColor};
  }
`,"getStyles"),Fe=Qe,et={parser:Pe,db:ee,renderer:Ve,styles:Fe}}}]);
}());