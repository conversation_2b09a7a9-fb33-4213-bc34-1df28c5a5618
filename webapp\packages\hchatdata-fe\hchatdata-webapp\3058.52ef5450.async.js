"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3058],{73058:function(Mm,Wf,$f){$f.d(Wf,{Z:function(){return Lt}});function Zn(r,e){(e==null||e>r.length)&&(e=r.length);for(var t=0,a=Array(e);t<e;t++)a[t]=r[t];return a}function Uf(r){if(Array.isArray(r))return r}function Kf(r){if(Array.isArray(r))return Zn(r)}function jr(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function Xf(r,e){for(var t=0;t<e.length;t++){var a=e[t];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(r,xo(a.key),a)}}function et(r,e,t){return e&&Xf(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}function Er(r,e){var t=typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=Qn(r))||e){t&&(r=t);var a=0,n=function(){};return{s:n,n:function(){return a>=r.length?{done:!0}:{done:!1,value:r[a++]}},e:function(l){throw l},f:n}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i,s=!0,o=!1;return{s:function(){t=t.call(r)},n:function(){var l=t.next();return s=l.done,l},e:function(l){o=!0,i=l},f:function(){try{s||t.return==null||t.return()}finally{if(o)throw i}}}}function wo(r,e,t){return(e=xo(e))in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function Yf(r){if(typeof Symbol!="undefined"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}function Zf(r,e){var t=r==null?null:typeof Symbol!="undefined"&&r[Symbol.iterator]||r["@@iterator"];if(t!=null){var a,n,i,s,o=[],l=!0,u=!1;try{if(i=(t=t.call(r)).next,e===0){if(Object(t)!==t)return;l=!1}else for(;!(l=(a=i.call(t)).done)&&(o.push(a.value),o.length!==e);l=!0);}catch(v){u=!0,n=v}finally{try{if(!l&&t.return!=null&&(s=t.return(),Object(s)!==s))return}finally{if(u)throw n}}return o}}function Qf(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jf(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ye(r,e){return Uf(r)||Zf(r,e)||Qn(r,e)||Qf()}function $a(r){return Kf(r)||Yf(r)||Qn(r)||Jf()}function jf(r,e){if(typeof r!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var a=t.call(r,e);if(typeof a!="object")return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(r)}function xo(r){var e=jf(r,"string");return typeof e=="symbol"?e:e+""}function je(r){"@babel/helpers - typeof";return je=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},je(r)}function Qn(r,e){if(r){if(typeof r=="string")return Zn(r,e);var t={}.toString.call(r).slice(8,-1);return t==="Object"&&r.constructor&&(t=r.constructor.name),t==="Map"||t==="Set"?Array.from(r):t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?Zn(r,e):void 0}}var er=typeof window=="undefined"?null:window,Eo=er?er.navigator:null;er&&er.document;var ec=je(""),Co=je({}),rc=je(function(){}),tc=typeof HTMLElement=="undefined"?"undefined":je(HTMLElement),oa=function(e){return e&&e.instanceString&&Ue(e.instanceString)?e.instanceString():null},he=function(e){return e!=null&&je(e)==ec},Ue=function(e){return e!=null&&je(e)===rc},_e=function(e){return!Dr(e)&&(Array.isArray?Array.isArray(e):e!=null&&e instanceof Array)},Me=function(e){return e!=null&&je(e)===Co&&!_e(e)&&e.constructor===Object},ac=function(e){return e!=null&&je(e)===Co},te=function(e){return e!=null&&je(e)===je(1)&&!isNaN(e)},nc=function(e){return te(e)&&Math.floor(e)===e},Ua=function(e){if(tc!=="undefined")return e!=null&&e instanceof HTMLElement},Dr=function(e){return ua(e)||To(e)},ua=function(e){return oa(e)==="collection"&&e._private.single},To=function(e){return oa(e)==="collection"&&!e._private.single},Jn=function(e){return oa(e)==="core"},So=function(e){return oa(e)==="stylesheet"},ic=function(e){return oa(e)==="event"},rt=function(e){return e==null?!0:!!(e===""||e.match(/^\s+$/))},sc=function(e){return typeof HTMLElement=="undefined"?!1:e instanceof HTMLElement},oc=function(e){return Me(e)&&te(e.x1)&&te(e.x2)&&te(e.y1)&&te(e.y2)},uc=function(e){return ac(e)&&Ue(e.then)},lc=function(){return Eo&&Eo.userAgent.match(/msie|trident|edge/i)},Ot=function(e,t){t||(t=function(){if(arguments.length===1)return arguments[0];if(arguments.length===0)return"undefined";for(var i=[],s=0;s<arguments.length;s++)i.push(arguments[s]);return i.join("$")});var a=function(){var i=this,s=arguments,o,l=t.apply(i,s),u=a.cache;return(o=u[l])||(o=u[l]=e.apply(i,s)),o};return a.cache={},a},jn=Ot(function(r){return r.replace(/([A-Z])/g,function(e){return"-"+e.toLowerCase()})}),Ka=Ot(function(r){return r.replace(/(-\w)/g,function(e){return e[1].toUpperCase()})}),ko=Ot(function(r,e){return r+e[0].toUpperCase()+e.substring(1)},function(r,e){return r+"$"+e}),Do=function(e){return rt(e)?e:e.charAt(0).toUpperCase()+e.substring(1)},tt=function(e,t){return e.slice(-1*t.length)===t},rr="(?:[-+]?(?:(?:\\d+|\\d*\\.\\d+)(?:[Ee][+-]?\\d+)?))",vc="rgb[a]?\\(("+rr+"[%]?)\\s*,\\s*("+rr+"[%]?)\\s*,\\s*("+rr+"[%]?)(?:\\s*,\\s*("+rr+"))?\\)",fc="rgb[a]?\\((?:"+rr+"[%]?)\\s*,\\s*(?:"+rr+"[%]?)\\s*,\\s*(?:"+rr+"[%]?)(?:\\s*,\\s*(?:"+rr+"))?\\)",cc="hsl[a]?\\(("+rr+")\\s*,\\s*("+rr+"[%])\\s*,\\s*("+rr+"[%])(?:\\s*,\\s*("+rr+"))?\\)",dc="hsl[a]?\\((?:"+rr+")\\s*,\\s*(?:"+rr+"[%])\\s*,\\s*(?:"+rr+"[%])(?:\\s*,\\s*(?:"+rr+"))?\\)",hc="\\#[0-9a-fA-F]{3}",gc="\\#[0-9a-fA-F]{6}",Bo=function(e,t){return e<t?-1:e>t?1:0},pc=function(e,t){return-1*Bo(e,t)},me=Object.assign!=null?Object.assign.bind(Object):function(r){for(var e=arguments,t=1;t<e.length;t++){var a=e[t];if(a!=null)for(var n=Object.keys(a),i=0;i<n.length;i++){var s=n[i];r[s]=a[s]}}return r},yc=function(e){if(!(!(e.length===4||e.length===7)||e[0]!=="#")){var t=e.length===4,a,n,i,s=16;return t?(a=parseInt(e[1]+e[1],s),n=parseInt(e[2]+e[2],s),i=parseInt(e[3]+e[3],s)):(a=parseInt(e[1]+e[2],s),n=parseInt(e[3]+e[4],s),i=parseInt(e[5]+e[6],s)),[a,n,i]}},mc=function(e){var t,a,n,i,s,o,l,u;function v(d,y,g){return g<0&&(g+=1),g>1&&(g-=1),g<1/6?d+(y-d)*6*g:g<1/2?y:g<2/3?d+(y-d)*(2/3-g)*6:d}var f=new RegExp("^"+cc+"$").exec(e);if(f){if(a=parseInt(f[1]),a<0?a=(360- -1*a%360)%360:a>360&&(a=a%360),a/=360,n=parseFloat(f[2]),n<0||n>100||(n=n/100,i=parseFloat(f[3]),i<0||i>100)||(i=i/100,s=f[4],s!==void 0&&(s=parseFloat(s),s<0||s>1)))return;if(n===0)o=l=u=Math.round(i*255);else{var c=i<.5?i*(1+n):i+n-i*n,h=2*i-c;o=Math.round(255*v(h,c,a+1/3)),l=Math.round(255*v(h,c,a)),u=Math.round(255*v(h,c,a-1/3))}t=[o,l,u,s]}return t},bc=function(e){var t,a=new RegExp("^"+vc+"$").exec(e);if(a){t=[];for(var n=[],i=1;i<=3;i++){var s=a[i];if(s[s.length-1]==="%"&&(n[i]=!0),s=parseFloat(s),n[i]&&(s=s/100*255),s<0||s>255)return;t.push(Math.floor(s))}var o=n[1]||n[2]||n[3],l=n[1]&&n[2]&&n[3];if(o&&!l)return;var u=a[4];if(u!==void 0){if(u=parseFloat(u),u<0||u>1)return;t.push(u)}}return t},wc=function(e){return xc[e.toLowerCase()]},Po=function(e){return(_e(e)?e:null)||wc(e)||yc(e)||bc(e)||mc(e)},xc={transparent:[0,0,0,0],aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],grey:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},Ao=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Me(s))throw Error("Tried to set map with object key");i<a.length-1?(t[s]==null&&(t[s]={}),t=t[s]):t[s]=e.value}},Ro=function(e){for(var t=e.map,a=e.keys,n=a.length,i=0;i<n;i++){var s=a[i];if(Me(s))throw Error("Tried to get map with object key");if(t=t[s],t==null)return t}return t},Xa=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function la(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}var ei,Mo;function va(){if(Mo)return ei;Mo=1;function r(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}return ei=r,ei}var ri,Lo;function Ec(){if(Lo)return ri;Lo=1;var r=typeof Xa=="object"&&Xa&&Xa.Object===Object&&Xa;return ri=r,ri}var ti,Io;function Ya(){if(Io)return ti;Io=1;var r=Ec(),e=typeof self=="object"&&self&&self.Object===Object&&self,t=r||e||Function("return this")();return ti=t,ti}var ai,Oo;function Cc(){if(Oo)return ai;Oo=1;var r=Ya(),e=function(){return r.Date.now()};return ai=e,ai}var ni,No;function Tc(){if(No)return ni;No=1;var r=/\s/;function e(t){for(var a=t.length;a--&&r.test(t.charAt(a)););return a}return ni=e,ni}var ii,zo;function Sc(){if(zo)return ii;zo=1;var r=Tc(),e=/^\s+/;function t(a){return a&&a.slice(0,r(a)+1).replace(e,"")}return ii=t,ii}var si,Fo;function oi(){if(Fo)return si;Fo=1;var r=Ya(),e=r.Symbol;return si=e,si}var ui,Vo;function kc(){if(Vo)return ui;Vo=1;var r=oi(),e=Object.prototype,t=e.hasOwnProperty,a=e.toString,n=r?r.toStringTag:void 0;function i(s){var o=t.call(s,n),l=s[n];try{s[n]=void 0;var u=!0}catch(f){}var v=a.call(s);return u&&(o?s[n]=l:delete s[n]),v}return ui=i,ui}var li,qo;function Dc(){if(qo)return li;qo=1;var r=Object.prototype,e=r.toString;function t(a){return e.call(a)}return li=t,li}var vi,_o;function Go(){if(_o)return vi;_o=1;var r=oi(),e=kc(),t=Dc(),a="[object Null]",n="[object Undefined]",i=r?r.toStringTag:void 0;function s(o){return o==null?o===void 0?n:a:i&&i in Object(o)?e(o):t(o)}return vi=s,vi}var fi,Ho;function Bc(){if(Ho)return fi;Ho=1;function r(e){return e!=null&&typeof e=="object"}return fi=r,fi}var ci,Wo;function fa(){if(Wo)return ci;Wo=1;var r=Go(),e=Bc(),t="[object Symbol]";function a(n){return typeof n=="symbol"||e(n)&&r(n)==t}return ci=a,ci}var di,$o;function Pc(){if($o)return di;$o=1;var r=Sc(),e=va(),t=fa(),a=NaN,n=/^[-+]0x[0-9a-f]+$/i,i=/^0b[01]+$/i,s=/^0o[0-7]+$/i,o=parseInt;function l(u){if(typeof u=="number")return u;if(t(u))return a;if(e(u)){var v=typeof u.valueOf=="function"?u.valueOf():u;u=e(v)?v+"":v}if(typeof u!="string")return u===0?u:+u;u=r(u);var f=i.test(u);return f||s.test(u)?o(u.slice(2),f?2:8):n.test(u)?a:+u}return di=l,di}var hi,Uo;function Ac(){if(Uo)return hi;Uo=1;var r=va(),e=Cc(),t=Pc(),a="Expected a function",n=Math.max,i=Math.min;function s(o,l,u){var v,f,c,h,d,y,g=0,p=!1,m=!1,b=!0;if(typeof o!="function")throw new TypeError(a);l=t(l)||0,r(u)&&(p=!!u.leading,m="maxWait"in u,c=m?n(t(u.maxWait)||0,l):c,b="trailing"in u?!!u.trailing:b);function w(A){var R=v,L=f;return v=f=void 0,g=A,h=o.apply(L,R),h}function E(A){return g=A,d=setTimeout(T,l),p?w(A):h}function C(A){var R=A-y,L=A-g,I=l-R;return m?i(I,c-L):I}function x(A){var R=A-y,L=A-g;return y===void 0||R>=l||R<0||m&&L>=c}function T(){var A=e();if(x(A))return k(A);d=setTimeout(T,C(A))}function k(A){return d=void 0,b&&v?w(A):(v=f=void 0,h)}function D(){d!==void 0&&clearTimeout(d),g=0,v=y=f=d=void 0}function B(){return d===void 0?h:k(e())}function P(){var A=e(),R=x(A);if(v=arguments,f=this,y=A,R){if(d===void 0)return E(y);if(m)return clearTimeout(d),d=setTimeout(T,l),w(y)}return d===void 0&&(d=setTimeout(T,l)),h}return P.cancel=D,P.flush=B,P}return hi=s,hi}var Rc=Ac(),ca=la(Rc),gi=er?er.performance:null,Ko=gi&&gi.now?function(){return gi.now()}:function(){return Date.now()},Mc=function(){if(er){if(er.requestAnimationFrame)return function(r){er.requestAnimationFrame(r)};if(er.mozRequestAnimationFrame)return function(r){er.mozRequestAnimationFrame(r)};if(er.webkitRequestAnimationFrame)return function(r){er.webkitRequestAnimationFrame(r)};if(er.msRequestAnimationFrame)return function(r){er.msRequestAnimationFrame(r)}}return function(r){r&&setTimeout(function(){r(Ko())},1e3/60)}}(),Za=function(e){return Mc(e)},Wr=Ko,bt=9261,Xo=65599,Nt=5381,Yo=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:bt,a=t,n;n=e.next(),!n.done;)a=a*Xo+n.value|0;return a},da=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:bt;return t*Xo+e|0},ha=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Nt;return(t<<5)+t+e|0},Lc=function(e,t){return e*2097152+t},at=function(e){return e[0]*2097152+e[1]},Qa=function(e,t){return[da(e[0],t[0]),ha(e[1],t[1])]},Zo=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e[n++]:a.done=!0,a}};return Yo(s,t)},wt=function(e,t){var a={value:0,done:!1},n=0,i=e.length,s={next:function(){return n<i?a.value=e.charCodeAt(n++):a.done=!0,a}};return Yo(s,t)},Qo=function(){return Ic(arguments)},Ic=function(e){for(var t,a=0;a<e.length;a++){var n=e[a];a===0?t=wt(n):t=wt(n,t)}return t};function Oc(r,e,t,a,n){var i=n*Math.PI/180,s=Math.cos(i)*(r-t)-Math.sin(i)*(e-a)+t,o=Math.sin(i)*(r-t)+Math.cos(i)*(e-a)+a;return{x:s,y:o}}var Nc=function(e,t,a,n,i,s){return{x:(e-a)*i+a,y:(t-n)*s+n}};function zc(r,e,t){if(t===0)return r;var a=(e.x1+e.x2)/2,n=(e.y1+e.y2)/2,i=e.w/e.h,s=1/i,o=Oc(r.x,r.y,a,n,t),l=Nc(o.x,o.y,a,n,i,s);return{x:l.x,y:l.y}}var Jo=!0,Fc=console.warn!=null,Vc=console.trace!=null,pi=Number.MAX_SAFE_INTEGER||9007199254740991,jo=function(){return!0},Ja=function(){return!1},eu=function(){return 0},yi=function(){},$e=function(e){throw new Error(e)},ru=function(e){if(e!==void 0)Jo=!!e;else return Jo},Oe=function(e){ru()&&(Fc?console.warn(e):(console.log(e),Vc&&console.trace()))},qc=function(e){return me({},e)},zr=function(e){return e==null?e:_e(e)?e.slice():Me(e)?qc(e):e},_c=function(e){return e.slice()},tu=function(e,t){for(t=e="";e++<36;t+=e*51&52?(e^15?8^Math.random()*(e^20?16:4):4).toString(16):"-");return t},Gc={},au=function(){return Gc},lr=function(e){var t=Object.keys(e);return function(a){for(var n={},i=0;i<t.length;i++){var s=t[i],o=a==null?void 0:a[s];n[s]=o===void 0?e[s]:o}return n}},nt=function(e,t,a){for(var n=e.length-1;n>=0;n--)e[n]===t&&e.splice(n,1)},mi=function(e){e.splice(0,e.length)},Hc=function(e,t){for(var a=0;a<t.length;a++){var n=t[a];e.push(n)}},Cr=function(e,t,a){return a&&(t=ko(a,t)),e[t]},$r=function(e,t,a,n){a&&(t=ko(a,t)),e[t]=n},Wc=function(){function r(){jr(this,r),this._obj={}}return et(r,[{key:"set",value:function(t,a){return this._obj[t]=a,this}},{key:"delete",value:function(t){return this._obj[t]=void 0,this}},{key:"clear",value:function(){this._obj={}}},{key:"has",value:function(t){return this._obj[t]!==void 0}},{key:"get",value:function(t){return this._obj[t]}}])}(),Ur=typeof Map!="undefined"?Map:Wc,$c="undefined",Uc=function(){function r(e){if(jr(this,r),this._obj=Object.create(null),this.size=0,e!=null){var t;e.instanceString!=null&&e.instanceString()===this.instanceString()?t=e.toArray():t=e;for(var a=0;a<t.length;a++)this.add(t[a])}}return et(r,[{key:"instanceString",value:function(){return"set"}},{key:"add",value:function(t){var a=this._obj;a[t]!==1&&(a[t]=1,this.size++)}},{key:"delete",value:function(t){var a=this._obj;a[t]===1&&(a[t]=0,this.size--)}},{key:"clear",value:function(){this._obj=Object.create(null)}},{key:"has",value:function(t){return this._obj[t]===1}},{key:"toArray",value:function(){var t=this;return Object.keys(this._obj).filter(function(a){return t.has(a)})}},{key:"forEach",value:function(t,a){return this.toArray().forEach(t,a)}}])}(),zt=(typeof Set=="undefined"?"undefined":je(Set))!==$c?Set:Uc,ja=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(e===void 0||t===void 0||!Jn(e)){$e("An element must have a core reference and parameters set");return}var n=t.group;if(n==null&&(t.data&&t.data.source!=null&&t.data.target!=null?n="edges":n="nodes"),n!=="nodes"&&n!=="edges"){$e("An element must be of type `nodes` or `edges`; you specified `"+n+"`");return}this.length=1,this[0]=this;var i=this._private={cy:e,single:!0,data:t.data||{},position:t.position||{x:0,y:0},autoWidth:void 0,autoHeight:void 0,autoPadding:void 0,compoundBoundsClean:!1,listeners:[],group:n,style:{},rstyle:{},styleCxts:[],styleKeys:{},removed:!0,selected:!!t.selected,selectable:t.selectable===void 0?!0:!!t.selectable,locked:!!t.locked,grabbed:!1,grabbable:t.grabbable===void 0?!0:!!t.grabbable,pannable:t.pannable===void 0?n==="edges":!!t.pannable,active:!1,classes:new zt,animation:{current:[],queue:[]},rscratch:{},scratch:t.scratch||{},edges:[],children:[],parent:t.parent&&t.parent.isNode()?t.parent:null,traversalCache:{},backgrounding:!1,bbCache:null,bbCacheShift:{x:0,y:0},bodyBounds:null,overlayBounds:null,labelBounds:{all:null,source:null,target:null,main:null},arrowBounds:{source:null,target:null,"mid-source":null,"mid-target":null}};if(i.position.x==null&&(i.position.x=0),i.position.y==null&&(i.position.y=0),t.renderedPosition){var s=t.renderedPosition,o=e.pan(),l=e.zoom();i.position={x:(s.x-o.x)/l,y:(s.y-o.y)/l}}var u=[];_e(t.classes)?u=t.classes:he(t.classes)&&(u=t.classes.split(/\s+/));for(var v=0,f=u.length;v<f;v++){var c=u[v];!c||c===""||i.classes.add(c)}this.createEmitter(),(a===void 0||a)&&this.restore();var h=t.style||t.css;h&&(Oe("Setting a `style` bypass at element creation should be done only when absolutely necessary.  Try to use the stylesheet instead."),this.style(h))},nu=function(e){return e={bfs:e.bfs||!e.dfs,dfs:e.dfs||!e.bfs},function(a,n,i){var s;Me(a)&&!Dr(a)&&(s=a,a=s.roots||s.root,n=s.visit,i=s.directed),i=arguments.length===2&&!Ue(n)?n:i,n=Ue(n)?n:function(){};for(var o=this._private.cy,l=a=he(a)?this.filter(a):a,u=[],v=[],f={},c={},h={},d=0,y,g=this.byGroup(),p=g.nodes,m=g.edges,b=0;b<l.length;b++){var w=l[b],E=w.id();w.isNode()&&(u.unshift(w),e.bfs&&(h[E]=!0,v.push(w)),c[E]=0)}for(var C=function(){var A=e.bfs?u.shift():u.pop(),R=A.id();if(e.dfs){if(h[R])return 0;h[R]=!0,v.push(A)}var L=c[R],I=f[R],M=I!=null?I.source():null,O=I!=null?I.target():null,V=I==null?void 0:A.same(M)?O[0]:M[0],G;if(G=n(A,I,V,d++,L),G===!0)return y=A,1;if(G===!1)return 1;for(var N=A.connectedEdges().filter(function(j){return(!i||j.source().same(A))&&m.has(j)}),F=0;F<N.length;F++){var U=N[F],Q=U.connectedNodes().filter(function(j){return!j.same(A)&&p.has(j)}),K=Q.id();Q.length!==0&&!h[K]&&(Q=Q[0],u.push(Q),e.bfs&&(h[K]=!0,v.push(Q)),f[K]=U,c[K]=c[R]+1)}},x;u.length!==0&&(x=C(),!(x!==0&&x===1)););for(var T=o.collection(),k=0;k<v.length;k++){var D=v[k],B=f[D.id()];B!=null&&T.push(B),T.push(D)}return{path:o.collection(T),found:o.collection(y)}}},ga={breadthFirstSearch:nu({bfs:!0}),depthFirstSearch:nu({dfs:!0})};ga.bfs=ga.breadthFirstSearch,ga.dfs=ga.depthFirstSearch;var en={exports:{}},Kc=en.exports,iu;function Xc(){return iu||(iu=1,function(r,e){(function(){var t,a,n,i,s,o,l,u,v,f,c,h,d,y,g;n=Math.floor,f=Math.min,a=function(p,m){return p<m?-1:p>m?1:0},v=function(p,m,b,w,E){var C;if(b==null&&(b=0),E==null&&(E=a),b<0)throw new Error("lo must be non-negative");for(w==null&&(w=p.length);b<w;)C=n((b+w)/2),E(m,p[C])<0?w=C:b=C+1;return[].splice.apply(p,[b,b-b].concat(m)),m},o=function(p,m,b){return b==null&&(b=a),p.push(m),y(p,0,p.length-1,b)},s=function(p,m){var b,w;return m==null&&(m=a),b=p.pop(),p.length?(w=p[0],p[0]=b,g(p,0,m)):w=b,w},u=function(p,m,b){var w;return b==null&&(b=a),w=p[0],p[0]=m,g(p,0,b),w},l=function(p,m,b){var w;return b==null&&(b=a),p.length&&b(p[0],m)<0&&(w=[p[0],m],m=w[0],p[0]=w[1],g(p,0,b)),m},i=function(p,m){var b,w,E,C,x,T;for(m==null&&(m=a),C=function(){T=[];for(var k=0,D=n(p.length/2);0<=D?k<D:k>D;0<=D?k++:k--)T.push(k);return T}.apply(this).reverse(),x=[],w=0,E=C.length;w<E;w++)b=C[w],x.push(g(p,b,m));return x},d=function(p,m,b){var w;if(b==null&&(b=a),w=p.indexOf(m),w!==-1)return y(p,0,w,b),g(p,w,b)},c=function(p,m,b){var w,E,C,x,T;if(b==null&&(b=a),E=p.slice(0,m),!E.length)return E;for(i(E,b),T=p.slice(m),C=0,x=T.length;C<x;C++)w=T[C],l(E,w,b);return E.sort(b).reverse()},h=function(p,m,b){var w,E,C,x,T,k,D,B,P;if(b==null&&(b=a),m*10<=p.length){if(C=p.slice(0,m).sort(b),!C.length)return C;for(E=C[C.length-1],D=p.slice(m),x=0,k=D.length;x<k;x++)w=D[x],b(w,E)<0&&(v(C,w,0,null,b),C.pop(),E=C[C.length-1]);return C}for(i(p,b),P=[],T=0,B=f(m,p.length);0<=B?T<B:T>B;0<=B?++T:--T)P.push(s(p,b));return P},y=function(p,m,b,w){var E,C,x;for(w==null&&(w=a),E=p[b];b>m;){if(x=b-1>>1,C=p[x],w(E,C)<0){p[b]=C,b=x;continue}break}return p[b]=E},g=function(p,m,b){var w,E,C,x,T;for(b==null&&(b=a),E=p.length,T=m,C=p[m],w=2*m+1;w<E;)x=w+1,x<E&&!(b(p[w],p[x])<0)&&(w=x),p[m]=p[w],m=w,w=2*m+1;return p[m]=C,y(p,T,m,b)},t=function(){p.push=o,p.pop=s,p.replace=u,p.pushpop=l,p.heapify=i,p.updateItem=d,p.nlargest=c,p.nsmallest=h;function p(m){this.cmp=m!=null?m:a,this.nodes=[]}return p.prototype.push=function(m){return o(this.nodes,m,this.cmp)},p.prototype.pop=function(){return s(this.nodes,this.cmp)},p.prototype.peek=function(){return this.nodes[0]},p.prototype.contains=function(m){return this.nodes.indexOf(m)!==-1},p.prototype.replace=function(m){return u(this.nodes,m,this.cmp)},p.prototype.pushpop=function(m){return l(this.nodes,m,this.cmp)},p.prototype.heapify=function(){return i(this.nodes,this.cmp)},p.prototype.updateItem=function(m){return d(this.nodes,m,this.cmp)},p.prototype.clear=function(){return this.nodes=[]},p.prototype.empty=function(){return this.nodes.length===0},p.prototype.size=function(){return this.nodes.length},p.prototype.clone=function(){var m;return m=new p,m.nodes=this.nodes.slice(0),m},p.prototype.toArray=function(){return this.nodes.slice(0)},p.prototype.insert=p.prototype.push,p.prototype.top=p.prototype.peek,p.prototype.front=p.prototype.peek,p.prototype.has=p.prototype.contains,p.prototype.copy=p.prototype.clone,p}(),function(p,m){return r.exports=m()}(this,function(){return t})}).call(Kc)}(en)),en.exports}var bi,su;function Yc(){return su||(su=1,bi=Xc()),bi}var Zc=Yc(),pa=la(Zc),Qc=lr({root:null,weight:function(e){return 1},directed:!1}),Jc={dijkstra:function(e){if(!Me(e)){var t=arguments;e={root:t[0],weight:t[1],directed:t[2]}}var a=Qc(e),n=a.root,i=a.weight,s=a.directed,o=this,l=i,u=he(n)?this.filter(n)[0]:n[0],v={},f={},c={},h=this.byGroup(),d=h.nodes,y=h.edges;y.unmergeBy(function(L){return L.isLoop()});for(var g=function(I){return v[I.id()]},p=function(I,M){v[I.id()]=M,m.updateItem(I)},m=new pa(function(L,I){return g(L)-g(I)}),b=0;b<d.length;b++){var w=d[b];v[w.id()]=w.same(u)?0:1/0,m.push(w)}for(var E=function(I,M){for(var O=(s?I.edgesTo(M):I.edgesWith(M)).intersect(y),V=1/0,G,N=0;N<O.length;N++){var F=O[N],U=l(F);(U<V||!G)&&(V=U,G=F)}return{edge:G,dist:V}};m.size()>0;){var C=m.pop(),x=g(C),T=C.id();if(c[T]=x,x!==1/0)for(var k=C.neighborhood().intersect(d),D=0;D<k.length;D++){var B=k[D],P=B.id(),A=E(C,B),R=x+A.dist;R<g(B)&&(p(B,R),f[P]={node:C,edge:A.edge})}}return{distanceTo:function(I){var M=he(I)?d.filter(I)[0]:I[0];return c[M.id()]},pathTo:function(I){var M=he(I)?d.filter(I)[0]:I[0],O=[],V=M,G=V.id();if(M.length>0)for(O.unshift(M);f[G];){var N=f[G];O.unshift(N.edge),O.unshift(N.node),V=N.node,G=V.id()}return o.spawn(O)}}}},jc={kruskal:function(e){e=e||function(b){return 1};for(var t=this.byGroup(),a=t.nodes,n=t.edges,i=a.length,s=new Array(i),o=a,l=function(w){for(var E=0;E<s.length;E++){var C=s[E];if(C.has(w))return E}},u=0;u<i;u++)s[u]=this.spawn(a[u]);for(var v=n.sort(function(b,w){return e(b)-e(w)}),f=0;f<v.length;f++){var c=v[f],h=c.source()[0],d=c.target()[0],y=l(h),g=l(d),p=s[y],m=s[g];y!==g&&(o.merge(c),p.merge(m),s.splice(g,1))}return o}},ed=lr({root:null,goal:null,weight:function(e){return 1},heuristic:function(e){return 0},directed:!1}),rd={aStar:function(e){var t=this.cy(),a=ed(e),n=a.root,i=a.goal,s=a.heuristic,o=a.directed,l=a.weight;n=t.collection(n)[0],i=t.collection(i)[0];var u=n.id(),v=i.id(),f={},c={},h={},d=new pa(function(G,N){return c[G.id()]-c[N.id()]}),y=new zt,g={},p={},m=function(N,F){d.push(N),y.add(F)},b,w,E=function(){b=d.pop(),w=b.id(),y.delete(w)},C=function(N){return y.has(N)};m(n,u),f[u]=0,c[u]=s(n);for(var x=0;d.size()>0;){if(E(),x++,w===v){for(var T=[],k=i,D=v,B=p[D];T.unshift(k),B!=null&&T.unshift(B),k=g[D],k!=null;)D=k.id(),B=p[D];return{found:!0,distance:f[w],path:this.spawn(T),steps:x}}h[w]=!0;for(var P=b._private.edges,A=0;A<P.length;A++){var R=P[A];if(this.hasElementWithId(R.id())&&!(o&&R.data("source")!==w)){var L=R.source(),I=R.target(),M=L.id()!==w?L:I,O=M.id();if(this.hasElementWithId(O)&&!h[O]){var V=f[w]+l(R);if(!C(O)){f[O]=V,c[O]=V+s(M),m(M,O),g[O]=b,p[O]=R;continue}V<f[O]&&(f[O]=V,c[O]=V+s(M),g[O]=b,p[O]=R)}}}}return{found:!1,distance:void 0,path:void 0,steps:x}}},td=lr({weight:function(e){return 1},directed:!1}),ad={floydWarshall:function(e){for(var t=this.cy(),a=td(e),n=a.weight,i=a.directed,s=n,o=this.byGroup(),l=o.nodes,u=o.edges,v=l.length,f=v*v,c=function(U){return l.indexOf(U)},h=function(U){return l[U]},d=new Array(f),y=0;y<f;y++){var g=y%v,p=(y-g)/v;p===g?d[y]=0:d[y]=1/0}for(var m=new Array(f),b=new Array(f),w=0;w<u.length;w++){var E=u[w],C=E.source()[0],x=E.target()[0];if(C!==x){var T=c(C),k=c(x),D=T*v+k,B=s(E);if(d[D]>B&&(d[D]=B,m[D]=k,b[D]=E),!i){var P=k*v+T;!i&&d[P]>B&&(d[P]=B,m[P]=T,b[P]=E)}}}for(var A=0;A<v;A++)for(var R=0;R<v;R++)for(var L=R*v+A,I=0;I<v;I++){var M=R*v+I,O=A*v+I;d[L]+d[O]<d[M]&&(d[M]=d[L]+d[O],m[M]=m[L])}var V=function(U){return(he(U)?t.filter(U):U)[0]},G=function(U){return c(V(U))},N={distance:function(U,Q){var K=G(U),j=G(Q);return d[K*v+j]},path:function(U,Q){var K=G(U),j=G(Q),re=h(K);if(K===j)return re.collection();if(m[K*v+j]==null)return t.collection();var ne=t.collection(),J=K,z;for(ne.merge(re);K!==j;)J=K,K=m[K*v+j],z=b[J*v+K],ne.merge(z),ne.merge(h(K));return ne}};return N}},nd=lr({weight:function(e){return 1},directed:!1,root:null}),id={bellmanFord:function(e){var t=this,a=nd(e),n=a.weight,i=a.directed,s=a.root,o=n,l=this,u=this.cy(),v=this.byGroup(),f=v.edges,c=v.nodes,h=c.length,d=new Ur,y=!1,g=[];s=u.collection(s)[0],f.unmergeBy(function(Ce){return Ce.isLoop()});for(var p=f.length,m=function(we){var ye=d.get(we.id());return ye||(ye={},d.set(we.id(),ye)),ye},b=function(we){return(he(we)?u.$(we):we)[0]},w=function(we){return m(b(we)).dist},E=function(we){for(var ye=arguments.length>1&&arguments[1]!==void 0?arguments[1]:s,ie=b(we),de=[],ge=ie;;){if(ge==null)return t.spawn();var Ee=m(ge),pe=Ee.edge,ke=Ee.pred;if(de.unshift(ge[0]),ge.same(ye)&&de.length>0)break;pe!=null&&de.unshift(pe),ge=ke}return l.spawn(de)},C=0;C<h;C++){var x=c[C],T=m(x);x.same(s)?T.dist=0:T.dist=1/0,T.pred=null,T.edge=null}for(var k=!1,D=function(we,ye,ie,de,ge,Ee){var pe=de.dist+Ee;pe<ge.dist&&!ie.same(de.edge)&&(ge.dist=pe,ge.pred=we,ge.edge=ie,k=!0)},B=1;B<h;B++){k=!1;for(var P=0;P<p;P++){var A=f[P],R=A.source(),L=A.target(),I=o(A),M=m(R),O=m(L);D(R,L,A,M,O,I),i||D(L,R,A,O,M,I)}if(!k)break}if(k)for(var V=[],G=0;G<p;G++){var N=f[G],F=N.source(),U=N.target(),Q=o(N),K=m(F).dist,j=m(U).dist;if(K+Q<j||!i&&j+Q<K)if(y||(Oe("Graph contains a negative weight cycle for Bellman-Ford"),y=!0),e.findNegativeWeightCycles!==!1){var re=[];K+Q<j&&re.push(F),!i&&j+Q<K&&re.push(U);for(var ne=re.length,J=0;J<ne;J++){var z=re[J],q=[z];q.push(m(z).edge);for(var H=m(z).pred;q.indexOf(H)===-1;)q.push(H),q.push(m(H).edge),H=m(H).pred;q=q.slice(q.indexOf(H));for(var Y=q[0].id(),ae=0,ce=2;ce<q.length;ce+=2)q[ce].id()<Y&&(Y=q[ce].id(),ae=ce);q=q.slice(ae).concat(q.slice(0,ae)),q.push(q[0]);var Ae=q.map(function(Ce){return Ce.id()}).join(",");V.indexOf(Ae)===-1&&(g.push(l.spawn(q)),V.push(Ae))}}else break}return{distanceTo:w,pathTo:E,hasNegativeWeightCycle:y,negativeWeightCycles:g}}},sd=Math.sqrt(2),od=function(e,t,a){a.length===0&&$e("Karger-Stein must be run on a connected (sub)graph");for(var n=a[e],i=n[1],s=n[2],o=t[i],l=t[s],u=a,v=u.length-1;v>=0;v--){var f=u[v],c=f[1],h=f[2];(t[c]===o&&t[h]===l||t[c]===l&&t[h]===o)&&u.splice(v,1)}for(var d=0;d<u.length;d++){var y=u[d];y[1]===l?(u[d]=y.slice(),u[d][1]=o):y[2]===l&&(u[d]=y.slice(),u[d][2]=o)}for(var g=0;g<t.length;g++)t[g]===l&&(t[g]=o);return u},wi=function(e,t,a,n){for(;a>n;){var i=Math.floor(Math.random()*t.length);t=od(i,e,t),a--}return t},ud={kargerStein:function(){var e=this,t=this.byGroup(),a=t.nodes,n=t.edges;n.unmergeBy(function(O){return O.isLoop()});var i=a.length,s=n.length,o=Math.ceil(Math.pow(Math.log(i)/Math.LN2,2)),l=Math.floor(i/sd);if(i<2){$e("At least 2 nodes are required for Karger-Stein algorithm");return}for(var u=[],v=0;v<s;v++){var f=n[v];u.push([v,a.indexOf(f.source()),a.indexOf(f.target())])}for(var c=1/0,h=[],d=new Array(i),y=new Array(i),g=new Array(i),p=function(V,G){for(var N=0;N<i;N++)G[N]=V[N]},m=0;m<=o;m++){for(var b=0;b<i;b++)y[b]=b;var w=wi(y,u.slice(),i,l),E=w.slice();p(y,g);var C=wi(y,w,l,2),x=wi(g,E,l,2);C.length<=x.length&&C.length<c?(c=C.length,h=C,p(y,d)):x.length<=C.length&&x.length<c&&(c=x.length,h=x,p(g,d))}for(var T=this.spawn(h.map(function(O){return n[O[0]]})),k=this.spawn(),D=this.spawn(),B=d[0],P=0;P<d.length;P++){var A=d[P],R=a[P];A===B?k.merge(R):D.merge(R)}var L=function(V){var G=e.spawn();return V.forEach(function(N){G.merge(N),N.connectedEdges().forEach(function(F){e.contains(F)&&!T.contains(F)&&G.merge(F)})}),G},I=[L(k),L(D)],M={cut:T,components:I,partition1:k,partition2:D};return M}},xi,ld=function(e){return{x:e.x,y:e.y}},rn=function(e,t,a){return{x:e.x*t+a.x,y:e.y*t+a.y}},ou=function(e,t,a){return{x:(e.x-a.x)/t,y:(e.y-a.y)/t}},Ft=function(e){return{x:e[0],y:e[1]}},vd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.min(s,n))}return n},fd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=-1/0,i=t;i<a;i++){var s=e[i];isFinite(s)&&(n=Math.max(s,n))}return n},cd=function(e){for(var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=0,i=0,s=t;s<a;s++){var o=e[s];isFinite(o)&&(n+=o,i++)}return n/i},dd=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:e.length,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0;n?e=e.slice(t,a):(a<e.length&&e.splice(a,e.length-a),t>0&&e.splice(0,t));for(var o=0,l=e.length-1;l>=0;l--){var u=e[l];s?isFinite(u)||(e[l]=-1/0,o++):e.splice(l,1)}i&&e.sort(function(c,h){return c-h});var v=e.length,f=Math.floor(v/2);return v%2!==0?e[f+1+o]:(e[f-1+o]+e[f+o])/2},hd=function(e){return Math.PI*e/180},tn=function(e,t){return Math.atan2(t,e)-Math.PI/2},Ei=Math.log2||function(r){return Math.log(r)/Math.log(2)},Ci=function(e){return e>0?1:e<0?-1:0},xt=function(e,t){return Math.sqrt(Et(e,t))},Et=function(e,t){var a=t.x-e.x,n=t.y-e.y;return a*a+n*n},gd=function(e){for(var t=e.length,a=0,n=0;n<t;n++)a+=e[n];for(var i=0;i<t;i++)e[i]=e[i]/a;return e},ir=function(e,t,a,n){return(1-n)*(1-n)*e+2*(1-n)*n*t+n*n*a},Vt=function(e,t,a,n){return{x:ir(e.x,t.x,a.x,n),y:ir(e.y,t.y,a.y,n)}},pd=function(e,t,a,n){var i={x:t.x-e.x,y:t.y-e.y},s=xt(e,t),o={x:i.x/s,y:i.y/s};return a=a==null?0:a,n=n!=null?n:a*s,{x:e.x+o.x*n,y:e.y+o.y*n}},ya=function(e,t,a){return Math.max(e,Math.min(a,t))},mr=function(e){if(e==null)return{x1:1/0,y1:1/0,x2:-1/0,y2:-1/0,w:0,h:0};if(e.x1!=null&&e.y1!=null){if(e.x2!=null&&e.y2!=null&&e.x2>=e.x1&&e.y2>=e.y1)return{x1:e.x1,y1:e.y1,x2:e.x2,y2:e.y2,w:e.x2-e.x1,h:e.y2-e.y1};if(e.w!=null&&e.h!=null&&e.w>=0&&e.h>=0)return{x1:e.x1,y1:e.y1,x2:e.x1+e.w,y2:e.y1+e.h,w:e.w,h:e.h}}},yd=function(e){return{x1:e.x1,x2:e.x2,w:e.w,y1:e.y1,y2:e.y2,h:e.h}},md=function(e){e.x1=1/0,e.y1=1/0,e.x2=-1/0,e.y2=-1/0,e.w=0,e.h=0},bd=function(e,t){e.x1=Math.min(e.x1,t.x1),e.x2=Math.max(e.x2,t.x2),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,t.y1),e.y2=Math.max(e.y2,t.y2),e.h=e.y2-e.y1},uu=function(e,t,a){e.x1=Math.min(e.x1,t),e.x2=Math.max(e.x2,t),e.w=e.x2-e.x1,e.y1=Math.min(e.y1,a),e.y2=Math.max(e.y2,a),e.h=e.y2-e.y1},an=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.x1-=t,e.x2+=t,e.y1-=t,e.y2+=t,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},nn=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[0],a,n,i,s;if(t.length===1)a=n=i=s=t[0];else if(t.length===2)a=i=t[0],s=n=t[1];else if(t.length===4){var o=Ye(t,4);a=o[0],n=o[1],i=o[2],s=o[3]}return e.x1-=s,e.x2+=n,e.y1-=a,e.y2+=i,e.w=e.x2-e.x1,e.h=e.y2-e.y1,e},lu=function(e,t){e.x1=t.x1,e.y1=t.y1,e.x2=t.x2,e.y2=t.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1},Ti=function(e,t){return!(e.x1>t.x2||t.x1>e.x2||e.x2<t.x1||t.x2<e.x1||e.y2<t.y1||t.y2<e.y1||e.y1>t.y2||t.y1>e.y2)},it=function(e,t,a){return e.x1<=t&&t<=e.x2&&e.y1<=a&&a<=e.y2},vu=function(e,t){return it(e,t.x,t.y)},fu=function(e,t){return it(e,t.x1,t.y1)&&it(e,t.x2,t.y2)},wd=(xi=Math.hypot)!==null&&xi!==void 0?xi:function(r,e){return Math.sqrt(r*r+e*e)};function xd(r,e){if(r.length<3)throw new Error("Need at least 3 vertices");var t=function(T,k){return{x:T.x+k.x,y:T.y+k.y}},a=function(T,k){return{x:T.x-k.x,y:T.y-k.y}},n=function(T,k){return{x:T.x*k,y:T.y*k}},i=function(T,k){return T.x*k.y-T.y*k.x},s=function(T){var k=wd(T.x,T.y);return k===0?{x:0,y:0}:{x:T.x/k,y:T.y/k}},o=function(T){for(var k=0,D=0;D<T.length;D++){var B=T[D],P=T[(D+1)%T.length];k+=B.x*P.y-P.x*B.y}return k/2},l=function(T,k,D,B){var P=a(k,T),A=a(B,D),R=i(P,A);if(Math.abs(R)<1e-9)return t(T,n(P,.5));var L=i(a(D,T),A)/R;return t(T,n(P,L))},u=r.map(function(x){return{x:x.x,y:x.y}});o(u)<0&&u.reverse();for(var v=u.length,f=[],c=0;c<v;c++){var h=u[c],d=u[(c+1)%v],y=a(d,h),g=s({x:y.y,y:-y.x});f.push(g)}for(var p=f.map(function(x,T){var k=t(u[T],n(x,e)),D=t(u[(T+1)%v],n(x,e));return{p1:k,p2:D}}),m=[],b=0;b<v;b++){var w=p[(b-1+v)%v],E=p[b],C=l(w.p1,w.p2,E.p1,E.p2);m.push(C)}return m}function Ed(r,e,t,a,n,i){var s=Rd(r,e,t,a,n),o=xd(s,i),l=mr();return o.forEach(function(u){return uu(l,u.x,u.y)}),l}var cu=function(e,t,a,n,i,s,o){var l=arguments.length>7&&arguments[7]!==void 0?arguments[7]:"auto",u=l==="auto"?ot(i,s):l,v=i/2,f=s/2;u=Math.min(u,v,f);var c=u!==v,h=u!==f,d;if(c){var y=a-v+u-o,g=n-f-o,p=a+v-u+o,m=g;if(d=st(e,t,a,n,y,g,p,m,!1),d.length>0)return d}if(h){var b=a+v+o,w=n-f+u-o,E=b,C=n+f-u+o;if(d=st(e,t,a,n,b,w,E,C,!1),d.length>0)return d}if(c){var x=a-v+u-o,T=n+f+o,k=a+v-u+o,D=T;if(d=st(e,t,a,n,x,T,k,D,!1),d.length>0)return d}if(h){var B=a-v-o,P=n-f+u-o,A=B,R=n+f-u+o;if(d=st(e,t,a,n,B,P,A,R,!1),d.length>0)return d}var L;{var I=a-v+u,M=n-f+u;if(L=ma(e,t,a,n,I,M,u+o),L.length>0&&L[0]<=I&&L[1]<=M)return[L[0],L[1]]}{var O=a+v-u,V=n-f+u;if(L=ma(e,t,a,n,O,V,u+o),L.length>0&&L[0]>=O&&L[1]<=V)return[L[0],L[1]]}{var G=a+v-u,N=n+f-u;if(L=ma(e,t,a,n,G,N,u+o),L.length>0&&L[0]>=G&&L[1]>=N)return[L[0],L[1]]}{var F=a-v+u,U=n+f-u;if(L=ma(e,t,a,n,F,U,u+o),L.length>0&&L[0]<=F&&L[1]>=U)return[L[0],L[1]]}return[]},Cd=function(e,t,a,n,i,s,o){var l=o,u=Math.min(a,i),v=Math.max(a,i),f=Math.min(n,s),c=Math.max(n,s);return u-l<=e&&e<=v+l&&f-l<=t&&t<=c+l},Td=function(e,t,a,n,i,s,o,l,u){var v={x1:Math.min(a,o,i)-u,x2:Math.max(a,o,i)+u,y1:Math.min(n,l,s)-u,y2:Math.max(n,l,s)+u};return!(e<v.x1||e>v.x2||t<v.y1||t>v.y2)},Sd=function(e,t,a,n){a-=n;var i=t*t-4*e*a;if(i<0)return[];var s=Math.sqrt(i),o=2*e,l=(-t+s)/o,u=(-t-s)/o;return[l,u]},kd=function(e,t,a,n,i){var s=1e-5;e===0&&(e=s),t/=e,a/=e,n/=e;var o,l,u,v,f,c,h,d;if(l=(3*a-t*t)/9,u=-(27*n)+t*(9*a-2*(t*t)),u/=54,o=l*l*l+u*u,i[1]=0,h=t/3,o>0){f=u+Math.sqrt(o),f=f<0?-Math.pow(-f,1/3):Math.pow(f,1/3),c=u-Math.sqrt(o),c=c<0?-Math.pow(-c,1/3):Math.pow(c,1/3),i[0]=-h+f+c,h+=(f+c)/2,i[4]=i[2]=-h,h=Math.sqrt(3)*(-c+f)/2,i[3]=h,i[5]=-h;return}if(i[5]=i[3]=0,o===0){d=u<0?-Math.pow(-u,1/3):Math.pow(u,1/3),i[0]=-h+2*d,i[4]=i[2]=-(d+h);return}l=-l,v=l*l*l,v=Math.acos(u/Math.sqrt(v)),d=2*Math.sqrt(l),i[0]=-h+d*Math.cos(v/3),i[2]=-h+d*Math.cos((v+2*Math.PI)/3),i[4]=-h+d*Math.cos((v+4*Math.PI)/3)},Dd=function(e,t,a,n,i,s,o,l){var u=1*a*a-4*a*i+2*a*o+4*i*i-4*i*o+o*o+n*n-4*n*s+2*n*l+4*s*s-4*s*l+l*l,v=1*9*a*i-3*a*a-3*a*o-6*i*i+3*i*o+9*n*s-3*n*n-3*n*l-6*s*s+3*s*l,f=1*3*a*a-6*a*i+a*o-a*e+2*i*i+2*i*e-o*e+3*n*n-6*n*s+n*l-n*t+2*s*s+2*s*t-l*t,c=1*a*i-a*a+a*e-i*e+n*s-n*n+n*t-s*t,h=[];kd(u,v,f,c,h);for(var d=1e-7,y=[],g=0;g<6;g+=2)Math.abs(h[g+1])<d&&h[g]>=0&&h[g]<=1&&y.push(h[g]);y.push(1),y.push(0);for(var p=-1,m,b,w,E=0;E<y.length;E++)m=Math.pow(1-y[E],2)*a+2*(1-y[E])*y[E]*i+y[E]*y[E]*o,b=Math.pow(1-y[E],2)*n+2*(1-y[E])*y[E]*s+y[E]*y[E]*l,w=Math.pow(m-e,2)+Math.pow(b-t,2),p>=0?w<p&&(p=w):p=w;return p},Bd=function(e,t,a,n,i,s){var o=[e-a,t-n],l=[i-a,s-n],u=l[0]*l[0]+l[1]*l[1],v=o[0]*o[0]+o[1]*o[1],f=o[0]*l[0]+o[1]*l[1],c=f*f/u;return f<0?v:c>u?(e-i)*(e-i)+(t-s)*(t-s):v-c},Tr=function(e,t,a){for(var n,i,s,o,l,u=0,v=0;v<a.length/2;v++)if(n=a[v*2],i=a[v*2+1],v+1<a.length/2?(s=a[(v+1)*2],o=a[(v+1)*2+1]):(s=a[(v+1-a.length/2)*2],o=a[(v+1-a.length/2)*2+1]),!(n==e&&s==e))if(n>=e&&e>=s||n<=e&&e<=s)l=(e-n)/(s-n)*(o-i)+i,l>t&&u++;else continue;return u%2!==0},Kr=function(e,t,a,n,i,s,o,l,u){var v=new Array(a.length),f;l[0]!=null?(f=Math.atan(l[1]/l[0]),l[0]<0?f=f+Math.PI/2:f=-f-Math.PI/2):f=l;for(var c=Math.cos(-f),h=Math.sin(-f),d=0;d<v.length/2;d++)v[d*2]=s/2*(a[d*2]*c-a[d*2+1]*h),v[d*2+1]=o/2*(a[d*2+1]*c+a[d*2]*h),v[d*2]+=n,v[d*2+1]+=i;var y;if(u>0){var g=on(v,-u);y=sn(g)}else y=v;return Tr(e,t,y)},Pd=function(e,t,a,n,i,s,o,l){for(var u=new Array(a.length*2),v=0;v<l.length;v++){var f=l[v];u[v*4+0]=f.startX,u[v*4+1]=f.startY,u[v*4+2]=f.stopX,u[v*4+3]=f.stopY;var c=Math.pow(f.cx-e,2)+Math.pow(f.cy-t,2);if(c<=Math.pow(f.radius,2))return!0}return Tr(e,t,u)},sn=function(e){for(var t=new Array(e.length/2),a,n,i,s,o,l,u,v,f=0;f<e.length/4;f++){a=e[f*4],n=e[f*4+1],i=e[f*4+2],s=e[f*4+3],f<e.length/4-1?(o=e[(f+1)*4],l=e[(f+1)*4+1],u=e[(f+1)*4+2],v=e[(f+1)*4+3]):(o=e[0],l=e[1],u=e[2],v=e[3]);var c=st(a,n,i,s,o,l,u,v,!0);t[f*2]=c[0],t[f*2+1]=c[1]}return t},on=function(e,t){for(var a=new Array(e.length*2),n,i,s,o,l=0;l<e.length/2;l++){n=e[l*2],i=e[l*2+1],l<e.length/2-1?(s=e[(l+1)*2],o=e[(l+1)*2+1]):(s=e[0],o=e[1]);var u=o-i,v=-(s-n),f=Math.sqrt(u*u+v*v),c=u/f,h=v/f;a[l*4]=n+c*t,a[l*4+1]=i+h*t,a[l*4+2]=s+c*t,a[l*4+3]=o+h*t}return a},Ad=function(e,t,a,n,i,s){var o=a-e,l=n-t;o/=i,l/=s;var u=Math.sqrt(o*o+l*l),v=u-1;if(v<0)return[];var f=v/u;return[(a-e)*f+e,(n-t)*f+t]},Ct=function(e,t,a,n,i,s,o){return e-=i,t-=s,e/=a/2+o,t/=n/2+o,e*e+t*t<=1},ma=function(e,t,a,n,i,s,o){var l=[a-e,n-t],u=[e-i,t-s],v=l[0]*l[0]+l[1]*l[1],f=2*(u[0]*l[0]+u[1]*l[1]),c=u[0]*u[0]+u[1]*u[1]-o*o,h=f*f-4*v*c;if(h<0)return[];var d=(-f+Math.sqrt(h))/(2*v),y=(-f-Math.sqrt(h))/(2*v),g=Math.min(d,y),p=Math.max(d,y),m=[];if(g>=0&&g<=1&&m.push(g),p>=0&&p<=1&&m.push(p),m.length===0)return[];var b=m[0]*l[0]+e,w=m[0]*l[1]+t;if(m.length>1){if(m[0]==m[1])return[b,w];var E=m[1]*l[0]+e,C=m[1]*l[1]+t;return[b,w,E,C]}else return[b,w]},Si=function(e,t,a){return t<=e&&e<=a||a<=e&&e<=t?e:e<=t&&t<=a||a<=t&&t<=e?t:a},st=function(e,t,a,n,i,s,o,l,u){var v=e-i,f=a-e,c=o-i,h=t-s,d=n-t,y=l-s,g=c*h-y*v,p=f*h-d*v,m=y*f-c*d;if(m!==0){var b=g/m,w=p/m,E=.001,C=0-E,x=1+E;return C<=b&&b<=x&&C<=w&&w<=x?[e+b*f,t+b*d]:u?[e+b*f,t+b*d]:[]}else return g===0||p===0?Si(e,a,o)===o?[o,l]:Si(e,a,i)===i?[i,s]:Si(i,o,a)===a?[a,n]:[]:[]},Rd=function(e,t,a,n,i){var s=[],o=n/2,l=i/2,u=t,v=a;s.push({x:u+o*e[0],y:v+l*e[1]});for(var f=1;f<e.length/2;f++)s.push({x:u+o*e[f*2],y:v+l*e[f*2+1]});return s},ba=function(e,t,a,n,i,s,o,l){var u=[],v,f=new Array(a.length),c=!0;s==null&&(c=!1);var h;if(c){for(var d=0;d<f.length/2;d++)f[d*2]=a[d*2]*s+n,f[d*2+1]=a[d*2+1]*o+i;if(l>0){var y=on(f,-l);h=sn(y)}else h=f}else h=a;for(var g,p,m,b,w=0;w<h.length/2;w++)g=h[w*2],p=h[w*2+1],w<h.length/2-1?(m=h[(w+1)*2],b=h[(w+1)*2+1]):(m=h[0],b=h[1]),v=st(e,t,n,i,g,p,m,b),v.length!==0&&u.push(v[0],v[1]);return u},Md=function(e,t,a,n,i,s,o,l,u){var v=[],f,c=new Array(a.length*2);u.forEach(function(m,b){b===0?(c[c.length-2]=m.startX,c[c.length-1]=m.startY):(c[b*4-2]=m.startX,c[b*4-1]=m.startY),c[b*4]=m.stopX,c[b*4+1]=m.stopY,f=ma(e,t,n,i,m.cx,m.cy,m.radius),f.length!==0&&v.push(f[0],f[1])});for(var h=0;h<c.length/4;h++)f=st(e,t,n,i,c[h*4],c[h*4+1],c[h*4+2],c[h*4+3],!1),f.length!==0&&v.push(f[0],f[1]);if(v.length>2){for(var d=[v[0],v[1]],y=Math.pow(d[0]-e,2)+Math.pow(d[1]-t,2),g=1;g<v.length/2;g++){var p=Math.pow(v[g*2]-e,2)+Math.pow(v[g*2+1]-t,2);p<=y&&(d[0]=v[g*2],d[1]=v[g*2+1],y=p)}return d}return v},un=function(e,t,a){var n=[e[0]-t[0],e[1]-t[1]],i=Math.sqrt(n[0]*n[0]+n[1]*n[1]),s=(i-a)/i;return s<0&&(s=1e-5),[t[0]+s*n[0],t[1]+s*n[1]]},br=function(e,t){var a=ki(e,t);return a=du(a),a},du=function(e){for(var t,a,n=e.length/2,i=1/0,s=1/0,o=-1/0,l=-1/0,u=0;u<n;u++)t=e[2*u],a=e[2*u+1],i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);for(var v=2/(o-i),f=2/(l-s),c=0;c<n;c++)t=e[2*c]=e[2*c]*v,a=e[2*c+1]=e[2*c+1]*f,i=Math.min(i,t),o=Math.max(o,t),s=Math.min(s,a),l=Math.max(l,a);if(s<-1)for(var h=0;h<n;h++)a=e[2*h+1]=e[2*h+1]+(-1-s);return e},ki=function(e,t){var a=1/e*2*Math.PI,n=e%2===0?Math.PI/2+a/2:Math.PI/2;n+=t;for(var i=new Array(e*2),s,o=0;o<e;o++)s=o*a+n,i[2*o]=Math.cos(s),i[2*o+1]=Math.sin(-s);return i},ot=function(e,t){return Math.min(e/4,t/4,8)},hu=function(e,t){return Math.min(e/10,t/10,8)},Di=function(){return 8},Ld=function(e,t,a){return[e-2*t+a,2*(t-e),e]},Bi=function(e,t){return{heightOffset:Math.min(15,.05*t),widthOffset:Math.min(100,.25*e),ctrlPtOffsetPct:.05}};function Pi(r,e){function t(f){for(var c=[],h=0;h<f.length;h++){var d=f[h],y=f[(h+1)%f.length],g={x:y.x-d.x,y:y.y-d.y},p={x:-g.y,y:g.x},m=Math.sqrt(p.x*p.x+p.y*p.y);c.push({x:p.x/m,y:p.y/m})}return c}function a(f,c){var h=1/0,d=-1/0,y=Er(f),g;try{for(y.s();!(g=y.n()).done;){var p=g.value,m=p.x*c.x+p.y*c.y;h=Math.min(h,m),d=Math.max(d,m)}}catch(b){y.e(b)}finally{y.f()}return{min:h,max:d}}function n(f,c){return!(f.max<c.min||c.max<f.min)}var i=[].concat($a(t(r)),$a(t(e))),s=Er(i),o;try{for(s.s();!(o=s.n()).done;){var l=o.value,u=a(r,l),v=a(e,l);if(!n(u,v))return!1}}catch(f){s.e(f)}finally{s.f()}return!0}var Id=lr({dampingFactor:.8,precision:1e-6,iterations:200,weight:function(e){return 1}}),Od={pageRank:function(e){for(var t=Id(e),a=t.dampingFactor,n=t.precision,i=t.iterations,s=t.weight,o=this._private.cy,l=this.byGroup(),u=l.nodes,v=l.edges,f=u.length,c=f*f,h=v.length,d=new Array(c),y=new Array(f),g=(1-a)/f,p=0;p<f;p++){for(var m=0;m<f;m++){var b=p*f+m;d[b]=0}y[p]=0}for(var w=0;w<h;w++){var E=v[w],C=E.data("source"),x=E.data("target");if(C!==x){var T=u.indexOfId(C),k=u.indexOfId(x),D=s(E),B=k*f+T;d[B]+=D,y[T]+=D}}for(var P=1/f+g,A=0;A<f;A++)if(y[A]===0)for(var R=0;R<f;R++){var L=R*f+A;d[L]=P}else for(var I=0;I<f;I++){var M=I*f+A;d[M]=d[M]/y[A]+g}for(var O=new Array(f),V=new Array(f),G,N=0;N<f;N++)O[N]=1;for(var F=0;F<i;F++){for(var U=0;U<f;U++)V[U]=0;for(var Q=0;Q<f;Q++)for(var K=0;K<f;K++){var j=Q*f+K;V[Q]+=d[j]*O[K]}gd(V),G=O,O=V,V=G;for(var re=0,ne=0;ne<f;ne++){var J=G[ne]-O[ne];re+=J*J}if(re<n)break}var z={rank:function(H){return H=o.collection(H)[0],O[u.indexOf(H)]}};return z}},gu=lr({root:null,weight:function(e){return 1},directed:!1,alpha:0}),qt={degreeCentralityNormalized:function(e){e=gu(e);var t=this.cy(),a=this.nodes(),n=a.length;if(e.directed){for(var v={},f={},c=0,h=0,d=0;d<n;d++){var y=a[d],g=y.id();e.root=y;var p=this.degreeCentrality(e);c<p.indegree&&(c=p.indegree),h<p.outdegree&&(h=p.outdegree),v[g]=p.indegree,f[g]=p.outdegree}return{indegree:function(b){return c==0?0:(he(b)&&(b=t.filter(b)),v[b.id()]/c)},outdegree:function(b){return h===0?0:(he(b)&&(b=t.filter(b)),f[b.id()]/h)}}}else{for(var i={},s=0,o=0;o<n;o++){var l=a[o];e.root=l;var u=this.degreeCentrality(e);s<u.degree&&(s=u.degree),i[l.id()]=u.degree}return{degree:function(b){return s===0?0:(he(b)&&(b=t.filter(b)),i[b.id()]/s)}}}},degreeCentrality:function(e){e=gu(e);var t=this.cy(),a=this,n=e,i=n.root,s=n.weight,o=n.directed,l=n.alpha;if(i=t.collection(i)[0],o){for(var h=i.connectedEdges(),d=h.filter(function(C){return C.target().same(i)&&a.has(C)}),y=h.filter(function(C){return C.source().same(i)&&a.has(C)}),g=d.length,p=y.length,m=0,b=0,w=0;w<d.length;w++)m+=s(d[w]);for(var E=0;E<y.length;E++)b+=s(y[E]);return{indegree:Math.pow(g,1-l)*Math.pow(m,l),outdegree:Math.pow(p,1-l)*Math.pow(b,l)}}else{for(var u=i.connectedEdges().intersection(a),v=u.length,f=0,c=0;c<u.length;c++)f+=s(u[c]);return{degree:Math.pow(v,1-l)*Math.pow(f,l)}}}};qt.dc=qt.degreeCentrality,qt.dcn=qt.degreeCentralityNormalised=qt.degreeCentralityNormalized;var pu=lr({harmonic:!0,weight:function(){return 1},directed:!1,root:null}),_t={closenessCentralityNormalized:function(e){for(var t=pu(e),a=t.harmonic,n=t.weight,i=t.directed,s=this.cy(),o={},l=0,u=this.nodes(),v=this.floydWarshall({weight:n,directed:i}),f=0;f<u.length;f++){for(var c=0,h=u[f],d=0;d<u.length;d++)if(f!==d){var y=v.distance(h,u[d]);a?c+=1/y:c+=y}a||(c=1/c),l<c&&(l=c),o[h.id()]=c}return{closeness:function(p){return l==0?0:(he(p)?p=s.filter(p)[0].id():p=p.id(),o[p]/l)}}},closenessCentrality:function(e){var t=pu(e),a=t.root,n=t.weight,i=t.directed,s=t.harmonic;a=this.filter(a)[0];for(var o=this.dijkstra({root:a,weight:n,directed:i}),l=0,u=this.nodes(),v=0;v<u.length;v++){var f=u[v];if(!f.same(a)){var c=o.distanceTo(f);s?l+=1/c:l+=c}}return s?l:1/l}};_t.cc=_t.closenessCentrality,_t.ccn=_t.closenessCentralityNormalised=_t.closenessCentralityNormalized;var Nd=lr({weight:null,directed:!1}),Ai={betweennessCentrality:function(e){for(var t=Nd(e),a=t.directed,n=t.weight,i=n!=null,s=this.cy(),o=this.nodes(),l={},u={},v=0,f={set:function(b,w){u[b]=w,w>v&&(v=w)},get:function(b){return u[b]}},c=0;c<o.length;c++){var h=o[c],d=h.id();a?l[d]=h.outgoers().nodes():l[d]=h.openNeighborhood().nodes(),f.set(d,0)}for(var y=function(){for(var b=o[g].id(),w=[],E={},C={},x={},T=new pa(function(Q,K){return x[Q]-x[K]}),k=0;k<o.length;k++){var D=o[k].id();E[D]=[],C[D]=0,x[D]=1/0}for(C[b]=1,x[b]=0,T.push(b);!T.empty();){var B=T.pop();if(w.push(B),i)for(var P=0;P<l[B].length;P++){var A=l[B][P],R=s.getElementById(B),L=void 0;R.edgesTo(A).length>0?L=R.edgesTo(A)[0]:L=A.edgesTo(R)[0];var I=n(L);A=A.id(),x[A]>x[B]+I&&(x[A]=x[B]+I,T.nodes.indexOf(A)<0?T.push(A):T.updateItem(A),C[A]=0,E[A]=[]),x[A]==x[B]+I&&(C[A]=C[A]+C[B],E[A].push(B))}else for(var M=0;M<l[B].length;M++){var O=l[B][M].id();x[O]==1/0&&(T.push(O),x[O]=x[B]+1),x[O]==x[B]+1&&(C[O]=C[O]+C[B],E[O].push(B))}}for(var V={},G=0;G<o.length;G++)V[o[G].id()]=0;for(;w.length>0;){for(var N=w.pop(),F=0;F<E[N].length;F++){var U=E[N][F];V[U]=V[U]+C[U]/C[N]*(1+V[N])}N!=o[g].id()&&f.set(N,f.get(N)+V[N])}},g=0;g<o.length;g++)y();var p={betweenness:function(b){var w=s.collection(b).id();return f.get(w)},betweennessNormalized:function(b){if(v==0)return 0;var w=s.collection(b).id();return f.get(w)/v}};return p.betweennessNormalised=p.betweennessNormalized,p}};Ai.bc=Ai.betweennessCentrality;var zd=lr({expandFactor:2,inflateFactor:2,multFactor:1,maxIterations:20,attributes:[function(r){return 1}]}),Fd=function(e){return zd(e)},Vd=function(e,t){for(var a=0,n=0;n<t.length;n++)a+=t[n](e);return a},qd=function(e,t,a){for(var n=0;n<t;n++)e[n*t+n]=a},yu=function(e,t){for(var a,n=0;n<t;n++){a=0;for(var i=0;i<t;i++)a+=e[i*t+n];for(var s=0;s<t;s++)e[s*t+n]=e[s*t+n]/a}},_d=function(e,t,a){for(var n=new Array(a*a),i=0;i<a;i++){for(var s=0;s<a;s++)n[i*a+s]=0;for(var o=0;o<a;o++)for(var l=0;l<a;l++)n[i*a+l]+=e[i*a+o]*t[o*a+l]}return n},Gd=function(e,t,a){for(var n=e.slice(0),i=1;i<a;i++)e=_d(e,n,t);return e},Hd=function(e,t,a){for(var n=new Array(t*t),i=0;i<t*t;i++)n[i]=Math.pow(e[i],a);return yu(n,t),n},Wd=function(e,t,a,n){for(var i=0;i<a;i++){var s=Math.round(e[i]*Math.pow(10,n))/Math.pow(10,n),o=Math.round(t[i]*Math.pow(10,n))/Math.pow(10,n);if(s!==o)return!1}return!0},$d=function(e,t,a,n){for(var i=[],s=0;s<t;s++){for(var o=[],l=0;l<t;l++)Math.round(e[s*t+l]*1e3)/1e3>0&&o.push(a[l]);o.length!==0&&i.push(n.collection(o))}return i},Ud=function(e,t){for(var a=0;a<e.length;a++)if(!t[a]||e[a].id()!==t[a].id())return!1;return!0},Kd=function(e){for(var t=0;t<e.length;t++)for(var a=0;a<e.length;a++)t!=a&&Ud(e[t],e[a])&&e.splice(a,1);return e},mu=function(e){for(var t=this.nodes(),a=this.edges(),n=this.cy(),i=Fd(e),s={},o=0;o<t.length;o++)s[t[o].id()]=o;for(var l=t.length,u=l*l,v=new Array(u),f,c=0;c<u;c++)v[c]=0;for(var h=0;h<a.length;h++){var d=a[h],y=s[d.source().id()],g=s[d.target().id()],p=Vd(d,i.attributes);v[y*l+g]+=p,v[g*l+y]+=p}qd(v,l,i.multFactor),yu(v,l);for(var m=!0,b=0;m&&b<i.maxIterations;)m=!1,f=Gd(v,l,i.expandFactor),v=Hd(f,l,i.inflateFactor),Wd(v,f,u,4)||(m=!0),b++;var w=$d(v,l,t,n);return w=Kd(w),w},Xd={markovClustering:mu,mcl:mu},Yd=function(e){return e},bu=function(e,t){return Math.abs(t-e)},wu=function(e,t,a){return e+bu(t,a)},xu=function(e,t,a){return e+Math.pow(a-t,2)},Zd=function(e){return Math.sqrt(e)},Qd=function(e,t,a){return Math.max(e,bu(t,a))},wa=function(e,t,a,n,i){for(var s=arguments.length>5&&arguments[5]!==void 0?arguments[5]:Yd,o=n,l,u,v=0;v<e;v++)l=t(v),u=a(v),o=i(o,l,u);return s(o)},Gt={euclidean:function(e,t,a){return e>=2?wa(e,t,a,0,xu,Zd):wa(e,t,a,0,wu)},squaredEuclidean:function(e,t,a){return wa(e,t,a,0,xu)},manhattan:function(e,t,a){return wa(e,t,a,0,wu)},max:function(e,t,a){return wa(e,t,a,-1/0,Qd)}};Gt["squared-euclidean"]=Gt.squaredEuclidean,Gt.squaredeuclidean=Gt.squaredEuclidean;function ln(r,e,t,a,n,i){var s;return Ue(r)?s=r:s=Gt[r]||Gt.euclidean,e===0&&Ue(r)?s(n,i):s(e,t,a,n,i)}var Jd=lr({k:2,m:2,sensitivityThreshold:1e-4,distance:"euclidean",maxIterations:10,attributes:[],testMode:!1,testCentroids:null}),Ri=function(e){return Jd(e)},vn=function(e,t,a,n,i){var s=i!=="kMedoids",o=s?function(f){return a[f]}:function(f){return n[f](a)},l=function(c){return n[c](t)},u=a,v=t;return ln(e,n.length,o,l,u,v)},Mi=function(e,t,a){for(var n=a.length,i=new Array(n),s=new Array(n),o=new Array(t),l=null,u=0;u<n;u++)i[u]=e.min(a[u]).value,s[u]=e.max(a[u]).value;for(var v=0;v<t;v++){l=[];for(var f=0;f<n;f++)l[f]=Math.random()*(s[f]-i[f])+i[f];o[v]=l}return o},Eu=function(e,t,a,n,i){for(var s=1/0,o=0,l=0;l<t.length;l++){var u=vn(a,e,t[l],n,i);u<s&&(s=u,o=l)}return o},Cu=function(e,t,a){for(var n=[],i=null,s=0;s<t.length;s++)i=t[s],a[i.id()]===e&&n.push(i);return n},jd=function(e,t,a){return Math.abs(t-e)<=a},eh=function(e,t,a){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var s=Math.abs(e[n][i]-t[n][i]);if(s>a)return!1}return!0},rh=function(e,t,a){for(var n=0;n<a;n++)if(e===t[n])return!0;return!1},Tu=function(e,t){var a=new Array(t);if(e.length<50)for(var n=0;n<t;n++){for(var i=e[Math.floor(Math.random()*e.length)];rh(i,a,n);)i=e[Math.floor(Math.random()*e.length)];a[n]=i}else for(var s=0;s<t;s++)a[s]=e[Math.floor(Math.random()*e.length)];return a},Su=function(e,t,a){for(var n=0,i=0;i<t.length;i++)n+=vn("manhattan",t[i],e,a,"kMedoids");return n},th=function(e){var t=this.cy(),a=this.nodes(),n=null,i=Ri(e),s=new Array(i.k),o={},l;i.testMode?typeof i.testCentroids=="number"?(i.testCentroids,l=Mi(a,i.k,i.attributes)):je(i.testCentroids)==="object"?l=i.testCentroids:l=Mi(a,i.k,i.attributes):l=Mi(a,i.k,i.attributes);for(var u=!0,v=0;u&&v<i.maxIterations;){for(var f=0;f<a.length;f++)n=a[f],o[n.id()]=Eu(n,l,i.distance,i.attributes,"kMeans");u=!1;for(var c=0;c<i.k;c++){var h=Cu(c,a,o);if(h.length!==0){for(var d=i.attributes.length,y=l[c],g=new Array(d),p=new Array(d),m=0;m<d;m++){p[m]=0;for(var b=0;b<h.length;b++)n=h[b],p[m]+=i.attributes[m](n);g[m]=p[m]/h.length,jd(g[m],y[m],i.sensitivityThreshold)||(u=!0)}l[c]=g,s[c]=t.collection(h)}}v++}return s},ah=function(e){var t=this.cy(),a=this.nodes(),n=null,i=Ri(e),s=new Array(i.k),o,l={},u,v=new Array(i.k);i.testMode?typeof i.testCentroids=="number"||(je(i.testCentroids)==="object"?o=i.testCentroids:o=Tu(a,i.k)):o=Tu(a,i.k);for(var f=!0,c=0;f&&c<i.maxIterations;){for(var h=0;h<a.length;h++)n=a[h],l[n.id()]=Eu(n,o,i.distance,i.attributes,"kMedoids");f=!1;for(var d=0;d<o.length;d++){var y=Cu(d,a,l);if(y.length!==0){v[d]=Su(o[d],y,i.attributes);for(var g=0;g<y.length;g++)u=Su(y[g],y,i.attributes),u<v[d]&&(v[d]=u,o[d]=y[g],f=!0);s[d]=t.collection(y)}}c++}return s},nh=function(e,t,a,n,i){for(var s,o,l=0;l<t.length;l++)for(var u=0;u<e.length;u++)n[l][u]=Math.pow(a[l][u],i.m);for(var v=0;v<e.length;v++)for(var f=0;f<i.attributes.length;f++){s=0,o=0;for(var c=0;c<t.length;c++)s+=n[c][v]*i.attributes[f](t[c]),o+=n[c][v];e[v][f]=s/o}},ih=function(e,t,a,n,i){for(var s=0;s<e.length;s++)t[s]=e[s].slice();for(var o,l,u,v=2/(i.m-1),f=0;f<a.length;f++)for(var c=0;c<n.length;c++){o=0;for(var h=0;h<a.length;h++)l=vn(i.distance,n[c],a[f],i.attributes,"cmeans"),u=vn(i.distance,n[c],a[h],i.attributes,"cmeans"),o+=Math.pow(l/u,v);e[c][f]=1/o}},sh=function(e,t,a,n){for(var i=new Array(a.k),s=0;s<i.length;s++)i[s]=[];for(var o,l,u=0;u<t.length;u++){o=-1/0,l=-1;for(var v=0;v<t[0].length;v++)t[u][v]>o&&(o=t[u][v],l=v);i[l].push(e[u])}for(var f=0;f<i.length;f++)i[f]=n.collection(i[f]);return i},ku=function(e){var t=this.cy(),a=this.nodes(),n=Ri(e),i,s,o,l,u;l=new Array(a.length);for(var v=0;v<a.length;v++)l[v]=new Array(n.k);o=new Array(a.length);for(var f=0;f<a.length;f++)o[f]=new Array(n.k);for(var c=0;c<a.length;c++){for(var h=0,d=0;d<n.k;d++)o[c][d]=Math.random(),h+=o[c][d];for(var y=0;y<n.k;y++)o[c][y]=o[c][y]/h}s=new Array(n.k);for(var g=0;g<n.k;g++)s[g]=new Array(n.attributes.length);u=new Array(a.length);for(var p=0;p<a.length;p++)u[p]=new Array(n.k);for(var m=!0,b=0;m&&b<n.maxIterations;)m=!1,nh(s,a,o,u,n),ih(o,l,s,a,n),eh(o,l,n.sensitivityThreshold)||(m=!0),b++;return i=sh(a,o,n,t),{clusters:i,degreeOfMembership:o}},oh={kMeans:th,kMedoids:ah,fuzzyCMeans:ku,fcm:ku},uh=lr({distance:"euclidean",linkage:"min",mode:"threshold",threshold:1/0,addDendrogram:!1,dendrogramDepth:0,attributes:[]}),lh={single:"min",complete:"max"},vh=function(e){var t=uh(e),a=lh[t.linkage];return a!=null&&(t.linkage=a),t},Du=function(e,t,a,n,i){for(var s=0,o=1/0,l,u=i.attributes,v=function(k,D){return ln(i.distance,u.length,function(B){return u[B](k)},function(B){return u[B](D)},k,D)},f=0;f<e.length;f++){var c=e[f].key,h=a[c][n[c]];h<o&&(s=c,o=h)}if(i.mode==="threshold"&&o>=i.threshold||i.mode==="dendrogram"&&e.length===1)return!1;var d=t[s],y=t[n[s]],g;i.mode==="dendrogram"?g={left:d,right:y,key:d.key}:g={value:d.value.concat(y.value),key:d.key},e[d.index]=g,e.splice(y.index,1),t[d.key]=g;for(var p=0;p<e.length;p++){var m=e[p];d.key===m.key?l=1/0:i.linkage==="min"?(l=a[d.key][m.key],a[d.key][m.key]>a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="max"?(l=a[d.key][m.key],a[d.key][m.key]<a[y.key][m.key]&&(l=a[y.key][m.key])):i.linkage==="mean"?l=(a[d.key][m.key]*d.size+a[y.key][m.key]*y.size)/(d.size+y.size):i.mode==="dendrogram"?l=v(m.value,d.value):l=v(m.value[0],d.value[0]),a[d.key][m.key]=a[m.key][d.key]=l}for(var b=0;b<e.length;b++){var w=e[b].key;if(n[w]===d.key||n[w]===y.key){for(var E=w,C=0;C<e.length;C++){var x=e[C].key;a[w][x]<a[w][E]&&(E=x)}n[w]=E}e[b].index=b}return d.key=y.key=d.index=y.index=null,!0},Ht=function(e,t,a){e&&(e.value?t.push(e.value):(e.left&&Ht(e.left,t),e.right&&Ht(e.right,t)))},Li=function(e,t){if(!e)return"";if(e.left&&e.right){var a=Li(e.left,t),n=Li(e.right,t),i=t.add({group:"nodes",data:{id:a+","+n}});return t.add({group:"edges",data:{source:a,target:i.id()}}),t.add({group:"edges",data:{source:n,target:i.id()}}),i.id()}else if(e.value)return e.value.id()},Ii=function(e,t,a){if(!e)return[];var n=[],i=[],s=[];return t===0?(e.left&&Ht(e.left,n),e.right&&Ht(e.right,i),s=n.concat(i),[a.collection(s)]):t===1?e.value?[a.collection(e.value)]:(e.left&&Ht(e.left,n),e.right&&Ht(e.right,i),[a.collection(n),a.collection(i)]):e.value?[a.collection(e.value)]:(e.left&&(n=Ii(e.left,t-1,a)),e.right&&(i=Ii(e.right,t-1,a)),n.concat(i))},Bu=function(e){for(var t=this.cy(),a=this.nodes(),n=vh(e),i=n.attributes,s=function(b,w){return ln(n.distance,i.length,function(E){return i[E](b)},function(E){return i[E](w)},b,w)},o=[],l=[],u=[],v=[],f=0;f<a.length;f++){var c={value:n.mode==="dendrogram"?a[f]:[a[f]],key:f,index:f};o[f]=c,v[f]=c,l[f]=[],u[f]=0}for(var h=0;h<o.length;h++)for(var d=0;d<=h;d++){var y=void 0;n.mode==="dendrogram"?y=h===d?1/0:s(o[h].value,o[d].value):y=h===d?1/0:s(o[h].value[0],o[d].value[0]),l[h][d]=y,l[d][h]=y,y<l[h][u[h]]&&(u[h]=d)}for(var g=Du(o,v,l,u,n);g;)g=Du(o,v,l,u,n);var p;return n.mode==="dendrogram"?(p=Ii(o[0],n.dendrogramDepth,t),n.addDendrogram&&Li(o[0],t)):(p=new Array(o.length),o.forEach(function(m,b){m.key=m.index=null,p[b]=t.collection(m.value)})),p},fh={hierarchicalClustering:Bu,hca:Bu},ch=lr({distance:"euclidean",preference:"median",damping:.8,maxIterations:1e3,minIterations:100,attributes:[]}),dh=function(e){var t=e.damping,a=e.preference;.5<=t&&t<1||$e("Damping must range on [0.5, 1).  Got: ".concat(t));var n=["median","mean","min","max"];return n.some(function(i){return i===a})||te(a)||$e("Preference must be one of [".concat(n.map(function(i){return"'".concat(i,"'")}).join(", "),"] or a number.  Got: ").concat(a)),ch(e)},hh=function(e,t,a,n){var i=function(o,l){return n[l](o)};return-ln(e,n.length,function(s){return i(t,s)},function(s){return i(a,s)},t,a)},gh=function(e,t){var a=null;return t==="median"?a=dd(e):t==="mean"?a=cd(e):t==="min"?a=vd(e):t==="max"?a=fd(e):a=t,a},ph=function(e,t,a){for(var n=[],i=0;i<e;i++)t[i*e+i]+a[i*e+i]>0&&n.push(i);return n},Pu=function(e,t,a){for(var n=[],i=0;i<e;i++){for(var s=-1,o=-1/0,l=0;l<a.length;l++){var u=a[l];t[i*e+u]>o&&(s=u,o=t[i*e+u])}s>0&&n.push(s)}for(var v=0;v<a.length;v++)n[a[v]]=a[v];return n},yh=function(e,t,a){for(var n=Pu(e,t,a),i=0;i<a.length;i++){for(var s=[],o=0;o<n.length;o++)n[o]===a[i]&&s.push(o);for(var l=-1,u=-1/0,v=0;v<s.length;v++){for(var f=0,c=0;c<s.length;c++)f+=t[s[c]*e+s[v]];f>u&&(l=v,u=f)}a[i]=s[l]}return n=Pu(e,t,a),n},Au=function(e){for(var t=this.cy(),a=this.nodes(),n=dh(e),i={},s=0;s<a.length;s++)i[a[s].id()]=s;var o,l,u,v,f,c;o=a.length,l=o*o,u=new Array(l);for(var h=0;h<l;h++)u[h]=-1/0;for(var d=0;d<o;d++)for(var y=0;y<o;y++)d!==y&&(u[d*o+y]=hh(n.distance,a[d],a[y],n.attributes));v=gh(u,n.preference);for(var g=0;g<o;g++)u[g*o+g]=v;f=new Array(l);for(var p=0;p<l;p++)f[p]=0;c=new Array(l);for(var m=0;m<l;m++)c[m]=0;for(var b=new Array(o),w=new Array(o),E=new Array(o),C=0;C<o;C++)b[C]=0,w[C]=0,E[C]=0;for(var x=new Array(o*n.minIterations),T=0;T<x.length;T++)x[T]=0;var k;for(k=0;k<n.maxIterations;k++){for(var D=0;D<o;D++){for(var B=-1/0,P=-1/0,A=-1,R=0,L=0;L<o;L++)b[L]=f[D*o+L],R=c[D*o+L]+u[D*o+L],R>=B?(P=B,B=R,A=L):R>P&&(P=R);for(var I=0;I<o;I++)f[D*o+I]=(1-n.damping)*(u[D*o+I]-B)+n.damping*b[I];f[D*o+A]=(1-n.damping)*(u[D*o+A]-P)+n.damping*b[A]}for(var M=0;M<o;M++){for(var O=0,V=0;V<o;V++)b[V]=c[V*o+M],w[V]=Math.max(0,f[V*o+M]),O+=w[V];O-=w[M],w[M]=f[M*o+M],O+=w[M];for(var G=0;G<o;G++)c[G*o+M]=(1-n.damping)*Math.min(0,O-w[G])+n.damping*b[G];c[M*o+M]=(1-n.damping)*(O-w[M])+n.damping*b[M]}for(var N=0,F=0;F<o;F++){var U=c[F*o+F]+f[F*o+F]>0?1:0;x[k%n.minIterations*o+F]=U,N+=U}if(N>0&&(k>=n.minIterations-1||k==n.maxIterations-1)){for(var Q=0,K=0;K<o;K++){E[K]=0;for(var j=0;j<n.minIterations;j++)E[K]+=x[j*o+K];(E[K]===0||E[K]===n.minIterations)&&Q++}if(Q===o)break}}for(var re=ph(o,f,c),ne=yh(o,u,re),J={},z=0;z<re.length;z++)J[re[z]]=[];for(var q=0;q<a.length;q++){var H=i[a[q].id()],Y=ne[H];Y!=null&&J[Y].push(a[q])}for(var ae=new Array(re.length),ce=0;ce<re.length;ce++)ae[ce]=t.collection(J[re[ce]]);return ae},mh={affinityPropagation:Au,ap:Au},bh=lr({root:void 0,directed:!1}),wh={hierholzer:function(e){if(!Me(e)){var t=arguments;e={root:t[0],directed:t[1]}}var a=bh(e),n=a.root,i=a.directed,s=this,o=!1,l,u,v;n&&(v=he(n)?this.filter(n)[0].id():n[0].id());var f={},c={};i?s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.indegree(!0),E=m.outdegree(!0),C=w-E,x=E-w;C==1?l?o=!0:l=b:x==1?u?o=!0:u=b:(x>1||C>1)&&(o=!0),f[b]=[],m.outgoers().forEach(function(T){T.isEdge()&&f[b].push(T.id())})}else c[b]=[void 0,m.target().id()]}):s.forEach(function(m){var b=m.id();if(m.isNode()){var w=m.degree(!0);w%2&&(l?u?o=!0:u=b:l=b),f[b]=[],m.connectedEdges().forEach(function(E){return f[b].push(E.id())})}else c[b]=[m.source().id(),m.target().id()]});var h={found:!1,trail:void 0};if(o)return h;if(u&&l)if(i){if(v&&u!=v)return h;v=u}else{if(v&&u!=v&&l!=v)return h;v||(v=u)}else v||(v=s[0].id());var d=function(b){for(var w=b,E=[b],C,x,T;f[w].length;)C=f[w].shift(),x=c[C][0],T=c[C][1],w!=T?(f[T]=f[T].filter(function(k){return k!=C}),w=T):!i&&w!=x&&(f[x]=f[x].filter(function(k){return k!=C}),w=x),E.unshift(C),E.unshift(w);return E},y=[],g=[];for(g=d(v);g.length!=1;)f[g[0]].length==0?(y.unshift(s.getElementById(g.shift())),y.unshift(s.getElementById(g.shift()))):g=d(g.shift()).concat(g);y.unshift(s.getElementById(g.shift()));for(var p in f)if(f[p].length)return h;return h.found=!0,h.trail=this.spawn(y,!0),h}},fn=function(){var e=this,t={},a=0,n=0,i=[],s=[],o={},l=function(c,h){for(var d=s.length-1,y=[],g=e.spawn();s[d].x!=c||s[d].y!=h;)y.push(s.pop().edge),d--;y.push(s.pop().edge),y.forEach(function(p){var m=p.connectedNodes().intersection(e);g.merge(p),m.forEach(function(b){var w=b.id(),E=b.connectedEdges().intersection(e);g.merge(b),t[w].cutVertex?g.merge(E.filter(function(C){return C.isLoop()})):g.merge(E)})}),i.push(g)},u=function(c,h,d){c===d&&(n+=1),t[h]={id:a,low:a++,cutVertex:!1};var y=e.getElementById(h).connectedEdges().intersection(e);if(y.size()===0)i.push(e.spawn(e.getElementById(h)));else{var g,p,m,b;y.forEach(function(w){g=w.source().id(),p=w.target().id(),m=g===h?p:g,m!==d&&(b=w.id(),o[b]||(o[b]=!0,s.push({x:h,y:m,edge:w})),m in t?t[h].low=Math.min(t[h].low,t[m].id):(u(c,m,h),t[h].low=Math.min(t[h].low,t[m].low),t[h].id<=t[m].low&&(t[h].cutVertex=!0,l(h,m))))})}};e.forEach(function(f){if(f.isNode()){var c=f.id();c in t||(n=0,u(c,c),t[c].cutVertex=n>1)}});var v=Object.keys(t).filter(function(f){return t[f].cutVertex}).map(function(f){return e.getElementById(f)});return{cut:e.spawn(v),components:i}},xh={hopcroftTarjanBiconnected:fn,htbc:fn,htb:fn,hopcroftTarjanBiconnectedComponents:fn},cn=function(){var e=this,t={},a=0,n=[],i=[],s=e.spawn(e),o=function(u){i.push(u),t[u]={index:a,low:a++,explored:!1};var v=e.getElementById(u).connectedEdges().intersection(e);if(v.forEach(function(y){var g=y.target().id();g!==u&&(g in t||o(g),t[g].explored||(t[u].low=Math.min(t[u].low,t[g].low)))}),t[u].index===t[u].low){for(var f=e.spawn();;){var c=i.pop();if(f.merge(e.getElementById(c)),t[c].low=t[u].index,t[c].explored=!0,c===u)break}var h=f.edgesWith(f),d=f.merge(h);n.push(d),s=s.difference(d)}};return e.forEach(function(l){if(l.isNode()){var u=l.id();u in t||o(u)}}),{cut:s,components:n}},Eh={tarjanStronglyConnected:cn,tsc:cn,tscc:cn,tarjanStronglyConnectedComponents:cn},Ru={};[ga,Jc,jc,rd,ad,id,ud,Od,qt,_t,Ai,Xd,oh,fh,mh,wh,xh,Eh].forEach(function(r){me(Ru,r)});var Mu=0,Lu=1,Iu=2,Mr=function(e){if(!(this instanceof Mr))return new Mr(e);this.id="Thenable/1.0.7",this.state=Mu,this.fulfillValue=void 0,this.rejectReason=void 0,this.onFulfilled=[],this.onRejected=[],this.proxy={then:this.then.bind(this)},typeof e=="function"&&e.call(this,this.fulfill.bind(this),this.reject.bind(this))};Mr.prototype={fulfill:function(e){return Ou(this,Lu,"fulfillValue",e)},reject:function(e){return Ou(this,Iu,"rejectReason",e)},then:function(e,t){var a=this,n=new Mr;return a.onFulfilled.push(Fu(e,n,"fulfill")),a.onRejected.push(Fu(t,n,"reject")),Nu(a),n.proxy}};var Ou=function(e,t,a,n){return e.state===Mu&&(e.state=t,e[a]=n,Nu(e)),e},Nu=function(e){e.state===Lu?zu(e,"onFulfilled",e.fulfillValue):e.state===Iu&&zu(e,"onRejected",e.rejectReason)},zu=function(e,t,a){if(e[t].length!==0){var n=e[t];e[t]=[];var i=function(){for(var o=0;o<n.length;o++)n[o](a)};typeof setImmediate=="function"?setImmediate(i):setTimeout(i,0)}},Fu=function(e,t,a){return function(n){if(typeof e!="function")t[a].call(t,n);else{var i;try{i=e(n)}catch(s){t.reject(s);return}Vu(t,i)}}},Vu=function(e,t){if(e===t||e.proxy===t){e.reject(new TypeError("cannot resolve promise with itself"));return}var a;if(je(t)==="object"&&t!==null||typeof t=="function")try{a=t.then}catch(i){e.reject(i);return}if(typeof a=="function"){var n=!1;try{a.call(t,function(i){n||(n=!0,i===t?e.reject(new TypeError("circular thenable chain")):Vu(e,i))},function(i){n||(n=!0,e.reject(i))})}catch(i){n||e.reject(i)}return}e.fulfill(t)};Mr.all=function(r){return new Mr(function(e,t){for(var a=new Array(r.length),n=0,i=function(l,u){a[l]=u,n++,n===r.length&&e(a)},s=0;s<r.length;s++)(function(o){var l=r[o],u=l!=null&&l.then!=null;if(u)l.then(function(f){i(o,f)},function(f){t(f)});else{var v=l;i(o,v)}})(s)})},Mr.resolve=function(r){return new Mr(function(e,t){e(r)})},Mr.reject=function(r){return new Mr(function(e,t){t(r)})};var Wt=typeof Promise!="undefined"?Promise:Mr,Oi=function(e,t,a){var n=Jn(e),i=!n,s=this._private=me({duration:1e3},t,a);if(s.target=e,s.style=s.style||s.css,s.started=!1,s.playing=!1,s.hooked=!1,s.applying=!1,s.progress=0,s.completes=[],s.frames=[],s.complete&&Ue(s.complete)&&s.completes.push(s.complete),i){var o=e.position();s.startPosition=s.startPosition||{x:o.x,y:o.y},s.startStyle=s.startStyle||e.cy().style().getAnimationStartStyle(e,s.style)}if(n){var l=e.pan();s.startPan={x:l.x,y:l.y},s.startZoom=e.zoom()}this.length=1,this[0]=this},Tt=Oi.prototype;me(Tt,{instanceString:function(){return"animation"},hook:function(){var e=this._private;if(!e.hooked){var t,a=e.target._private.animation;e.queue?t=a.queue:t=a.current,t.push(this),Dr(e.target)&&e.target.cy().addToAnimationPool(e.target),e.hooked=!0}return this},play:function(){var e=this._private;return e.progress===1&&(e.progress=0),e.playing=!0,e.started=!1,e.stopped=!1,this.hook(),this},playing:function(){return this._private.playing},apply:function(){var e=this._private;return e.applying=!0,e.started=!1,e.stopped=!1,this.hook(),this},applying:function(){return this._private.applying},pause:function(){var e=this._private;return e.playing=!1,e.started=!1,this},stop:function(){var e=this._private;return e.playing=!1,e.started=!1,e.stopped=!0,this},rewind:function(){return this.progress(0)},fastforward:function(){return this.progress(1)},time:function(e){var t=this._private;return e===void 0?t.progress*t.duration:this.progress(e/t.duration)},progress:function(e){var t=this._private,a=t.playing;return e===void 0?t.progress:(a&&this.pause(),t.progress=e,t.started=!1,a&&this.play(),this)},completed:function(){return this._private.progress===1},reverse:function(){var e=this._private,t=e.playing;t&&this.pause(),e.progress=1-e.progress,e.started=!1;var a=function(u,v){var f=e[u];f!=null&&(e[u]=e[v],e[v]=f)};if(a("zoom","startZoom"),a("pan","startPan"),a("position","startPosition"),e.style)for(var n=0;n<e.style.length;n++){var i=e.style[n],s=i.name,o=e.startStyle[s];e.startStyle[s]=i,e.style[n]=o}return t&&this.play(),this},promise:function(e){var t=this._private,a;switch(e){case"frame":a=t.frames;break;default:case"complete":case"completed":a=t.completes}return new Wt(function(n,i){a.push(function(){n()})})}}),Tt.complete=Tt.completed,Tt.run=Tt.play,Tt.running=Tt.playing;var Ch={animated:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return!1;var s=n[0];if(s)return s._private.animation.current.length>0}},clearQueue:function(){return function(){var t=this,a=t.length!==void 0,n=a?t:[t],i=this._private.cy||this;if(!i.styleEnabled())return this;for(var s=0;s<n.length;s++){var o=n[s];o._private.animation.queue=[]}return this}},delay:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animate({delay:t,duration:t,complete:a}):this}},delayAnimation:function(){return function(t,a){var n=this._private.cy||this;return n.styleEnabled()?this.animation({delay:t,duration:t,complete:a}):this}},animation:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this,l=!i,u=!l;if(!o.styleEnabled())return this;var v=o.style();t=me({},t,a);var f=Object.keys(t).length===0;if(f)return new Oi(s[0],t);switch(t.duration===void 0&&(t.duration=400),t.duration){case"slow":t.duration=600;break;case"fast":t.duration=200;break}if(u&&(t.style=v.getPropsList(t.style||t.css),t.css=void 0),u&&t.renderedPosition!=null){var c=t.renderedPosition,h=o.pan(),d=o.zoom();t.position=ou(c,d,h)}if(l&&t.panBy!=null){var y=t.panBy,g=o.pan();t.pan={x:g.x+y.x,y:g.y+y.y}}var p=t.center||t.centre;if(l&&p!=null){var m=o.getCenterPan(p.eles,t.zoom);m!=null&&(t.pan=m)}if(l&&t.fit!=null){var b=t.fit,w=o.getFitViewport(b.eles||b.boundingBox,b.padding);w!=null&&(t.pan=w.pan,t.zoom=w.zoom)}if(l&&Me(t.zoom)){var E=o.getZoomedViewport(t.zoom);E!=null?(E.zoomed&&(t.zoom=E.zoom),E.panned&&(t.pan=E.pan)):t.zoom=null}return new Oi(s[0],t)}},animate:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;a&&(t=me({},t,a));for(var l=0;l<s.length;l++){var u=s[l],v=u.animated()&&(t.queue===void 0||t.queue),f=u.animation(t,v?{queue:!0}:void 0);f.play()}return this}},stop:function(){return function(t,a){var n=this,i=n.length!==void 0,s=i?n:[n],o=this._private.cy||this;if(!o.styleEnabled())return this;for(var l=0;l<s.length;l++){for(var u=s[l],v=u._private,f=v.animation.current,c=0;c<f.length;c++){var h=f[c],d=h._private;a&&(d.duration=0)}t&&(v.animation.queue=[]),a||(v.animation.current=[])}return o.notify("draw"),this}}},Ni,qu;function dn(){if(qu)return Ni;qu=1;var r=Array.isArray;return Ni=r,Ni}var zi,_u;function Th(){if(_u)return zi;_u=1;var r=dn(),e=fa(),t=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;function n(i,s){if(r(i))return!1;var o=typeof i;return o=="number"||o=="symbol"||o=="boolean"||i==null||e(i)?!0:a.test(i)||!t.test(i)||s!=null&&i in Object(s)}return zi=n,zi}var Fi,Gu;function Sh(){if(Gu)return Fi;Gu=1;var r=Go(),e=va(),t="[object AsyncFunction]",a="[object Function]",n="[object GeneratorFunction]",i="[object Proxy]";function s(o){if(!e(o))return!1;var l=r(o);return l==a||l==n||l==t||l==i}return Fi=s,Fi}var Vi,Hu;function kh(){if(Hu)return Vi;Hu=1;var r=Ya(),e=r["__core-js_shared__"];return Vi=e,Vi}var qi,Wu;function Dh(){if(Wu)return qi;Wu=1;var r=kh(),e=function(){var a=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}();function t(a){return!!e&&e in a}return qi=t,qi}var _i,$u;function Bh(){if($u)return _i;$u=1;var r=Function.prototype,e=r.toString;function t(a){if(a!=null){try{return e.call(a)}catch(n){}try{return a+""}catch(n){}}return""}return _i=t,_i}var Gi,Uu;function Ph(){if(Uu)return Gi;Uu=1;var r=Sh(),e=Dh(),t=va(),a=Bh(),n=/[\\^$.*+?()[\]{}|]/g,i=/^\[object .+?Constructor\]$/,s=Function.prototype,o=Object.prototype,l=s.toString,u=o.hasOwnProperty,v=RegExp("^"+l.call(u).replace(n,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function f(c){if(!t(c)||e(c))return!1;var h=r(c)?v:i;return h.test(a(c))}return Gi=f,Gi}var Hi,Ku;function Ah(){if(Ku)return Hi;Ku=1;function r(e,t){return e==null?void 0:e[t]}return Hi=r,Hi}var Wi,Xu;function $i(){if(Xu)return Wi;Xu=1;var r=Ph(),e=Ah();function t(a,n){var i=e(a,n);return r(i)?i:void 0}return Wi=t,Wi}var Ui,Yu;function hn(){if(Yu)return Ui;Yu=1;var r=$i(),e=r(Object,"create");return Ui=e,Ui}var Ki,Zu;function Rh(){if(Zu)return Ki;Zu=1;var r=hn();function e(){this.__data__=r?r(null):{},this.size=0}return Ki=e,Ki}var Xi,Qu;function Mh(){if(Qu)return Xi;Qu=1;function r(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}return Xi=r,Xi}var Yi,Ju;function Lh(){if(Ju)return Yi;Ju=1;var r=hn(),e="__lodash_hash_undefined__",t=Object.prototype,a=t.hasOwnProperty;function n(i){var s=this.__data__;if(r){var o=s[i];return o===e?void 0:o}return a.call(s,i)?s[i]:void 0}return Yi=n,Yi}var Zi,ju;function Ih(){if(ju)return Zi;ju=1;var r=hn(),e=Object.prototype,t=e.hasOwnProperty;function a(n){var i=this.__data__;return r?i[n]!==void 0:t.call(i,n)}return Zi=a,Zi}var Qi,el;function Oh(){if(el)return Qi;el=1;var r=hn(),e="__lodash_hash_undefined__";function t(a,n){var i=this.__data__;return this.size+=this.has(a)?0:1,i[a]=r&&n===void 0?e:n,this}return Qi=t,Qi}var Ji,rl;function Nh(){if(rl)return Ji;rl=1;var r=Rh(),e=Mh(),t=Lh(),a=Ih(),n=Oh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,Ji=i,Ji}var ji,tl;function zh(){if(tl)return ji;tl=1;function r(){this.__data__=[],this.size=0}return ji=r,ji}var es,al;function nl(){if(al)return es;al=1;function r(e,t){return e===t||e!==e&&t!==t}return es=r,es}var rs,il;function gn(){if(il)return rs;il=1;var r=nl();function e(t,a){for(var n=t.length;n--;)if(r(t[n][0],a))return n;return-1}return rs=e,rs}var ts,sl;function Fh(){if(sl)return ts;sl=1;var r=gn(),e=Array.prototype,t=e.splice;function a(n){var i=this.__data__,s=r(i,n);if(s<0)return!1;var o=i.length-1;return s==o?i.pop():t.call(i,s,1),--this.size,!0}return ts=a,ts}var as,ol;function Vh(){if(ol)return as;ol=1;var r=gn();function e(t){var a=this.__data__,n=r(a,t);return n<0?void 0:a[n][1]}return as=e,as}var ns,ul;function qh(){if(ul)return ns;ul=1;var r=gn();function e(t){return r(this.__data__,t)>-1}return ns=e,ns}var is,ll;function _h(){if(ll)return is;ll=1;var r=gn();function e(t,a){var n=this.__data__,i=r(n,t);return i<0?(++this.size,n.push([t,a])):n[i][1]=a,this}return is=e,is}var ss,vl;function Gh(){if(vl)return ss;vl=1;var r=zh(),e=Fh(),t=Vh(),a=qh(),n=_h();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,ss=i,ss}var os,fl;function Hh(){if(fl)return os;fl=1;var r=$i(),e=Ya(),t=r(e,"Map");return os=t,os}var us,cl;function Wh(){if(cl)return us;cl=1;var r=Nh(),e=Gh(),t=Hh();function a(){this.size=0,this.__data__={hash:new r,map:new(t||e),string:new r}}return us=a,us}var ls,dl;function $h(){if(dl)return ls;dl=1;function r(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}return ls=r,ls}var vs,hl;function pn(){if(hl)return vs;hl=1;var r=$h();function e(t,a){var n=t.__data__;return r(a)?n[typeof a=="string"?"string":"hash"]:n.map}return vs=e,vs}var fs,gl;function Uh(){if(gl)return fs;gl=1;var r=pn();function e(t){var a=r(this,t).delete(t);return this.size-=a?1:0,a}return fs=e,fs}var cs,pl;function Kh(){if(pl)return cs;pl=1;var r=pn();function e(t){return r(this,t).get(t)}return cs=e,cs}var ds,yl;function Xh(){if(yl)return ds;yl=1;var r=pn();function e(t){return r(this,t).has(t)}return ds=e,ds}var hs,ml;function Yh(){if(ml)return hs;ml=1;var r=pn();function e(t,a){var n=r(this,t),i=n.size;return n.set(t,a),this.size+=n.size==i?0:1,this}return hs=e,hs}var gs,bl;function Zh(){if(bl)return gs;bl=1;var r=Wh(),e=Uh(),t=Kh(),a=Xh(),n=Yh();function i(s){var o=-1,l=s==null?0:s.length;for(this.clear();++o<l;){var u=s[o];this.set(u[0],u[1])}}return i.prototype.clear=r,i.prototype.delete=e,i.prototype.get=t,i.prototype.has=a,i.prototype.set=n,gs=i,gs}var ps,wl;function Qh(){if(wl)return ps;wl=1;var r=Zh(),e="Expected a function";function t(a,n){if(typeof a!="function"||n!=null&&typeof n!="function")throw new TypeError(e);var i=function(){var s=arguments,o=n?n.apply(this,s):s[0],l=i.cache;if(l.has(o))return l.get(o);var u=a.apply(this,s);return i.cache=l.set(o,u)||l,u};return i.cache=new(t.Cache||r),i}return t.Cache=r,ps=t,ps}var ys,xl;function Jh(){if(xl)return ys;xl=1;var r=Qh(),e=500;function t(a){var n=r(a,function(s){return i.size===e&&i.clear(),s}),i=n.cache;return n}return ys=t,ys}var ms,El;function Cl(){if(El)return ms;El=1;var r=Jh(),e=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,t=/\\(\\)?/g,a=r(function(n){var i=[];return n.charCodeAt(0)===46&&i.push(""),n.replace(e,function(s,o,l,u){i.push(l?u.replace(t,"$1"):o||s)}),i});return ms=a,ms}var bs,Tl;function Sl(){if(Tl)return bs;Tl=1;function r(e,t){for(var a=-1,n=e==null?0:e.length,i=Array(n);++a<n;)i[a]=t(e[a],a,e);return i}return bs=r,bs}var ws,kl;function jh(){if(kl)return ws;kl=1;var r=oi(),e=Sl(),t=dn(),a=fa(),n=r?r.prototype:void 0,i=n?n.toString:void 0;function s(o){if(typeof o=="string")return o;if(t(o))return e(o,s)+"";if(a(o))return i?i.call(o):"";var l=o+"";return l=="0"&&1/o==-1/0?"-0":l}return ws=s,ws}var xs,Dl;function Bl(){if(Dl)return xs;Dl=1;var r=jh();function e(t){return t==null?"":r(t)}return xs=e,xs}var Es,Pl;function Al(){if(Pl)return Es;Pl=1;var r=dn(),e=Th(),t=Cl(),a=Bl();function n(i,s){return r(i)?i:e(i,s)?[i]:t(a(i))}return Es=n,Es}var Cs,Rl;function Ts(){if(Rl)return Cs;Rl=1;var r=fa();function e(t){if(typeof t=="string"||r(t))return t;var a=t+"";return a=="0"&&1/t==-1/0?"-0":a}return Cs=e,Cs}var Ss,Ml;function eg(){if(Ml)return Ss;Ml=1;var r=Al(),e=Ts();function t(a,n){n=r(n,a);for(var i=0,s=n.length;a!=null&&i<s;)a=a[e(n[i++])];return i&&i==s?a:void 0}return Ss=t,Ss}var ks,Ll;function rg(){if(Ll)return ks;Ll=1;var r=eg();function e(t,a,n){var i=t==null?void 0:r(t,a);return i===void 0?n:i}return ks=e,ks}var tg=rg(),ag=la(tg),Ds,Il;function ng(){if(Il)return Ds;Il=1;var r=$i(),e=function(){try{var t=r(Object,"defineProperty");return t({},"",{}),t}catch(a){}}();return Ds=e,Ds}var Bs,Ol;function ig(){if(Ol)return Bs;Ol=1;var r=ng();function e(t,a,n){a=="__proto__"&&r?r(t,a,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[a]=n}return Bs=e,Bs}var Ps,Nl;function sg(){if(Nl)return Ps;Nl=1;var r=ig(),e=nl(),t=Object.prototype,a=t.hasOwnProperty;function n(i,s,o){var l=i[s];(!(a.call(i,s)&&e(l,o))||o===void 0&&!(s in i))&&r(i,s,o)}return Ps=n,Ps}var As,zl;function og(){if(zl)return As;zl=1;var r=9007199254740991,e=/^(?:0|[1-9]\d*)$/;function t(a,n){var i=typeof a;return n=n==null?r:n,!!n&&(i=="number"||i!="symbol"&&e.test(a))&&a>-1&&a%1==0&&a<n}return As=t,As}var Rs,Fl;function ug(){if(Fl)return Rs;Fl=1;var r=sg(),e=Al(),t=og(),a=va(),n=Ts();function i(s,o,l,u){if(!a(s))return s;o=e(o,s);for(var v=-1,f=o.length,c=f-1,h=s;h!=null&&++v<f;){var d=n(o[v]),y=l;if(d==="__proto__"||d==="constructor"||d==="prototype")return s;if(v!=c){var g=h[d];y=u?u(g,d,h):void 0,y===void 0&&(y=a(g)?g:t(o[v+1])?[]:{})}r(h,d,y),h=h[d]}return s}return Rs=i,Rs}var Ms,Vl;function lg(){if(Vl)return Ms;Vl=1;var r=ug();function e(t,a,n){return t==null?t:r(t,a,n)}return Ms=e,Ms}var vg=lg(),fg=la(vg),Ls,ql;function cg(){if(ql)return Ls;ql=1;function r(e,t){var a=-1,n=e.length;for(t||(t=Array(n));++a<n;)t[a]=e[a];return t}return Ls=r,Ls}var Is,_l;function dg(){if(_l)return Is;_l=1;var r=Sl(),e=cg(),t=dn(),a=fa(),n=Cl(),i=Ts(),s=Bl();function o(l){return t(l)?r(l,i):a(l)?[l]:e(n(s(l)))}return Is=o,Is}var hg=dg(),gg=la(hg),pg={data:function(e){var t={field:"data",bindingEvent:"data",allowBinding:!1,allowSetting:!1,allowGetting:!1,settingEvent:"data",settingTriggersEvent:!1,triggerFnName:"trigger",immutableKeys:{},updateStyle:!1,beforeGet:function(n){},beforeSet:function(n,i){},onSet:function(n){},canSet:function(n){return!0}};return e=me({},t,e),function(n,i){var s=e,o=this,l=o.length!==void 0,u=l?o:[o],v=l?o[0]:o;if(he(n)){var f=n.indexOf(".")!==-1,c=f&&gg(n);if(s.allowGetting&&i===void 0){var h;return v&&(s.beforeGet(v),c&&v._private[s.field][n]===void 0?h=ag(v._private[s.field],c):h=v._private[s.field][n]),h}else if(s.allowSetting&&i!==void 0){var d=!s.immutableKeys[n];if(d){var y=wo({},n,i);s.beforeSet(o,y);for(var g=0,p=u.length;g<p;g++){var m=u[g];s.canSet(m)&&(c&&v._private[s.field][n]===void 0?fg(m._private[s.field],c,i):m._private[s.field][n]=i)}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}}}else if(s.allowSetting&&Me(n)){var b=n,w,E,C=Object.keys(b);s.beforeSet(o,b);for(var x=0;x<C.length;x++){w=C[x],E=b[w];var T=!s.immutableKeys[w];if(T)for(var k=0;k<u.length;k++){var D=u[k];s.canSet(D)&&(D._private[s.field][w]=E)}}s.updateStyle&&o.updateStyle(),s.onSet(o),s.settingTriggersEvent&&o[s.triggerFnName](s.settingEvent)}else if(s.allowBinding&&Ue(n)){var B=n;o.on(s.bindingEvent,B)}else if(s.allowGetting&&n===void 0){var P;return v&&(s.beforeGet(v),P=v._private[s.field]),P}return o}},removeData:function(e){var t={field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!1,immutableKeys:{}};return e=me({},t,e),function(n){var i=e,s=this,o=s.length!==void 0,l=o?s:[s];if(he(n)){for(var u=n.split(/\s+/),v=u.length,f=0;f<v;f++){var c=u[f];if(!rt(c)){var h=!i.immutableKeys[c];if(h)for(var d=0,y=l.length;d<y;d++)l[d]._private[i.field][c]=void 0}}i.triggerEvent&&s[i.triggerFnName](i.event)}else if(n===void 0){for(var g=0,p=l.length;g<p;g++)for(var m=l[g]._private[i.field],b=Object.keys(m),w=0;w<b.length;w++){var E=b[w],C=!i.immutableKeys[E];C&&(m[E]=void 0)}i.triggerEvent&&s[i.triggerFnName](i.event)}return s}}},yg={eventAliasesOn:function(e){var t=e;t.addListener=t.listen=t.bind=t.on,t.unlisten=t.unbind=t.off=t.removeListener,t.trigger=t.emit,t.pon=t.promiseOn=function(a,n){var i=this,s=Array.prototype.slice.call(arguments,0);return new Wt(function(o,l){var u=function(h){i.off.apply(i,f),o(h)},v=s.concat([u]),f=v.concat([]);i.on.apply(i,v)})}}},Ne={};[Ch,pg,yg].forEach(function(r){me(Ne,r)});var mg={animate:Ne.animate(),animation:Ne.animation(),animated:Ne.animated(),clearQueue:Ne.clearQueue(),delay:Ne.delay(),delayAnimation:Ne.delayAnimation(),stop:Ne.stop()},yn={classes:function(e){var t=this;if(e===void 0){var a=[];return t[0]._private.classes.forEach(function(d){return a.push(d)}),a}else _e(e)||(e=(e||"").match(/\S+/g)||[]);for(var n=[],i=new zt(e),s=0;s<t.length;s++){for(var o=t[s],l=o._private,u=l.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c);if(!h){v=!0;break}}v||(v=u.size!==e.length),v&&(l.classes=i,n.push(o))}return n.length>0&&this.spawn(n).updateStyle().emit("class"),t},addClass:function(e){return this.toggleClass(e,!0)},hasClass:function(e){var t=this[0];return t!=null&&t._private.classes.has(e)},toggleClass:function(e,t){_e(e)||(e=e.match(/\S+/g)||[]);for(var a=this,n=t===void 0,i=[],s=0,o=a.length;s<o;s++)for(var l=a[s],u=l._private.classes,v=!1,f=0;f<e.length;f++){var c=e[f],h=u.has(c),d=!1;t||n&&!h?(u.add(c),d=!0):(!t||n&&h)&&(u.delete(c),d=!0),!v&&d&&(i.push(l),v=!0)}return i.length>0&&this.spawn(i).updateStyle().emit("class"),a},removeClass:function(e){return this.toggleClass(e,!1)},flashClass:function(e,t){var a=this;if(t==null)t=250;else if(t===0)return a;return a.addClass(e),setTimeout(function(){a.removeClass(e)},t),a}};yn.className=yn.classNames=yn.classes;var Le={metaChar:"[\\!\\\"\\#\\$\\%\\&\\'\\(\\)\\*\\+\\,\\.\\/\\:\\;\\<\\=\\>\\?\\@\\[\\]\\^\\`\\{\\|\\}\\~]",comparatorOp:"=|\\!=|>|>=|<|<=|\\$=|\\^=|\\*=",boolOp:"\\?|\\!|\\^",string:`"(?:\\\\"|[^"])*"|'(?:\\\\'|[^'])*'`,number:rr,meta:"degree|indegree|outdegree",separator:"\\s*,\\s*",descendant:"\\s+",child:"\\s+>\\s+",subject:"\\$",group:"node|edge|\\*",directedEdge:"\\s+->\\s+",undirectedEdge:"\\s+<->\\s+"};Le.variable="(?:[\\w-.]|(?:\\\\"+Le.metaChar+"))+",Le.className="(?:[\\w-]|(?:\\\\"+Le.metaChar+"))+",Le.value=Le.string+"|"+Le.number,Le.id=Le.variable,function(){var r,e,t;for(r=Le.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],Le.comparatorOp+="|@"+e;for(r=Le.comparatorOp.split("|"),t=0;t<r.length;t++)e=r[t],!(e.indexOf("!")>=0)&&e!=="="&&(Le.comparatorOp+="|\\!"+e)}();var qe=function(){return{checks:[]}},se={GROUP:0,COLLECTION:1,FILTER:2,DATA_COMPARE:3,DATA_EXIST:4,DATA_BOOL:5,META_COMPARE:6,STATE:7,ID:8,CLASS:9,UNDIRECTED_EDGE:10,DIRECTED_EDGE:11,NODE_SOURCE:12,NODE_TARGET:13,NODE_NEIGHBOR:14,CHILD:15,DESCENDANT:16,PARENT:17,ANCESTOR:18,COMPOUND_SPLIT:19,TRUE:20},Os=[{selector:":selected",matches:function(e){return e.selected()}},{selector:":unselected",matches:function(e){return!e.selected()}},{selector:":selectable",matches:function(e){return e.selectable()}},{selector:":unselectable",matches:function(e){return!e.selectable()}},{selector:":locked",matches:function(e){return e.locked()}},{selector:":unlocked",matches:function(e){return!e.locked()}},{selector:":visible",matches:function(e){return e.visible()}},{selector:":hidden",matches:function(e){return!e.visible()}},{selector:":transparent",matches:function(e){return e.transparent()}},{selector:":grabbed",matches:function(e){return e.grabbed()}},{selector:":free",matches:function(e){return!e.grabbed()}},{selector:":removed",matches:function(e){return e.removed()}},{selector:":inside",matches:function(e){return!e.removed()}},{selector:":grabbable",matches:function(e){return e.grabbable()}},{selector:":ungrabbable",matches:function(e){return!e.grabbable()}},{selector:":animated",matches:function(e){return e.animated()}},{selector:":unanimated",matches:function(e){return!e.animated()}},{selector:":parent",matches:function(e){return e.isParent()}},{selector:":childless",matches:function(e){return e.isChildless()}},{selector:":child",matches:function(e){return e.isChild()}},{selector:":orphan",matches:function(e){return e.isOrphan()}},{selector:":nonorphan",matches:function(e){return e.isChild()}},{selector:":compound",matches:function(e){return e.isNode()?e.isParent():e.source().isParent()||e.target().isParent()}},{selector:":loop",matches:function(e){return e.isLoop()}},{selector:":simple",matches:function(e){return e.isSimple()}},{selector:":active",matches:function(e){return e.active()}},{selector:":inactive",matches:function(e){return!e.active()}},{selector:":backgrounding",matches:function(e){return e.backgrounding()}},{selector:":nonbackgrounding",matches:function(e){return!e.backgrounding()}}].sort(function(r,e){return pc(r.selector,e.selector)}),bg=function(){for(var r={},e,t=0;t<Os.length;t++)e=Os[t],r[e.selector]=e.matches;return r}(),wg=function(e,t){return bg[e](t)},xg="("+Os.map(function(r){return r.selector}).join("|")+")",$t=function(e){return e.replace(new RegExp("\\\\("+Le.metaChar+")","g"),function(t,a){return a})},ut=function(e,t,a){e[e.length-1]=a},Ns=[{name:"group",query:!0,regex:"("+Le.group+")",populate:function(e,t,a){var n=Ye(a,1),i=n[0];t.checks.push({type:se.GROUP,value:i==="*"?i:i+"s"})}},{name:"state",query:!0,regex:xg,populate:function(e,t,a){var n=Ye(a,1),i=n[0];t.checks.push({type:se.STATE,value:i})}},{name:"id",query:!0,regex:"\\#("+Le.id+")",populate:function(e,t,a){var n=Ye(a,1),i=n[0];t.checks.push({type:se.ID,value:$t(i)})}},{name:"className",query:!0,regex:"\\.("+Le.className+")",populate:function(e,t,a){var n=Ye(a,1),i=n[0];t.checks.push({type:se.CLASS,value:$t(i)})}},{name:"dataExists",query:!0,regex:"\\[\\s*("+Le.variable+")\\s*\\]",populate:function(e,t,a){var n=Ye(a,1),i=n[0];t.checks.push({type:se.DATA_EXIST,field:$t(i)})}},{name:"dataCompare",query:!0,regex:"\\[\\s*("+Le.variable+")\\s*("+Le.comparatorOp+")\\s*("+Le.value+")\\s*\\]",populate:function(e,t,a){var n=Ye(a,3),i=n[0],s=n[1],o=n[2],l=new RegExp("^"+Le.string+"$").exec(o)!=null;l?o=o.substring(1,o.length-1):o=parseFloat(o),t.checks.push({type:se.DATA_COMPARE,field:$t(i),operator:s,value:o})}},{name:"dataBool",query:!0,regex:"\\[\\s*("+Le.boolOp+")\\s*("+Le.variable+")\\s*\\]",populate:function(e,t,a){var n=Ye(a,2),i=n[0],s=n[1];t.checks.push({type:se.DATA_BOOL,field:$t(s),operator:i})}},{name:"metaCompare",query:!0,regex:"\\[\\[\\s*("+Le.meta+")\\s*("+Le.comparatorOp+")\\s*("+Le.number+")\\s*\\]\\]",populate:function(e,t,a){var n=Ye(a,3),i=n[0],s=n[1],o=n[2];t.checks.push({type:se.META_COMPARE,field:$t(i),operator:s,value:parseFloat(o)})}},{name:"nextQuery",separator:!0,regex:Le.separator,populate:function(e,t){var a=e.currentSubject,n=e.edgeCount,i=e.compoundCount,s=e[e.length-1];a!=null&&(s.subject=a,e.currentSubject=null),s.edgeCount=n,s.compoundCount=i,e.edgeCount=0,e.compoundCount=0;var o=e[e.length++]=qe();return o}},{name:"directedEdge",separator:!0,regex:Le.directedEdge,populate:function(e,t){if(e.currentSubject==null){var a=qe(),n=t,i=qe();return a.checks.push({type:se.DIRECTED_EDGE,source:n,target:i}),ut(e,t,a),e.edgeCount++,i}else{var s=qe(),o=t,l=qe();return s.checks.push({type:se.NODE_SOURCE,source:o,target:l}),ut(e,t,s),e.edgeCount++,l}}},{name:"undirectedEdge",separator:!0,regex:Le.undirectedEdge,populate:function(e,t){if(e.currentSubject==null){var a=qe(),n=t,i=qe();return a.checks.push({type:se.UNDIRECTED_EDGE,nodes:[n,i]}),ut(e,t,a),e.edgeCount++,i}else{var s=qe(),o=t,l=qe();return s.checks.push({type:se.NODE_NEIGHBOR,node:o,neighbor:l}),ut(e,t,s),l}}},{name:"child",separator:!0,regex:Le.child,populate:function(e,t){if(e.currentSubject==null){var a=qe(),n=qe(),i=e[e.length-1];return a.checks.push({type:se.CHILD,parent:i,child:n}),ut(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=qe(),o=e[e.length-1],l=qe(),u=qe(),v=qe(),f=qe();return s.checks.push({type:se.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:se.TRUE}],f.checks.push({type:se.TRUE}),l.checks.push({type:se.PARENT,parent:f,child:v}),ut(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=qe(),h=qe(),d=[{type:se.PARENT,parent:c,child:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"descendant",separator:!0,regex:Le.descendant,populate:function(e,t){if(e.currentSubject==null){var a=qe(),n=qe(),i=e[e.length-1];return a.checks.push({type:se.DESCENDANT,ancestor:i,descendant:n}),ut(e,t,a),e.compoundCount++,n}else if(e.currentSubject===t){var s=qe(),o=e[e.length-1],l=qe(),u=qe(),v=qe(),f=qe();return s.checks.push({type:se.COMPOUND_SPLIT,left:o,right:l,subject:u}),u.checks=t.checks,t.checks=[{type:se.TRUE}],f.checks.push({type:se.TRUE}),l.checks.push({type:se.ANCESTOR,ancestor:f,descendant:v}),ut(e,o,s),e.currentSubject=u,e.compoundCount++,v}else{var c=qe(),h=qe(),d=[{type:se.ANCESTOR,ancestor:c,descendant:h}];return c.checks=t.checks,t.checks=d,e.compoundCount++,h}}},{name:"subject",modifier:!0,regex:Le.subject,populate:function(e,t){if(e.currentSubject!=null&&e.currentSubject!==t)return Oe("Redefinition of subject in selector `"+e.toString()+"`"),!1;e.currentSubject=t;var a=e[e.length-1],n=a.checks[0],i=n==null?null:n.type;i===se.DIRECTED_EDGE?n.type=se.NODE_TARGET:i===se.UNDIRECTED_EDGE&&(n.type=se.NODE_NEIGHBOR,n.node=n.nodes[1],n.neighbor=n.nodes[0],n.nodes=null)}}];Ns.forEach(function(r){return r.regexObj=new RegExp("^"+r.regex)});var Eg=function(e){for(var t,a,n,i=0;i<Ns.length;i++){var s=Ns[i],o=s.name,l=e.match(s.regexObj);if(l!=null){a=l,t=s,n=o;var u=l[0];e=e.substring(u.length);break}}return{expr:t,match:a,name:n,remaining:e}},Cg=function(e){var t=e.match(/^\s+/);if(t){var a=t[0];e=e.substring(a.length)}return e},Tg=function(e){var t=this,a=t.inputText=e,n=t[0]=qe();for(t.length=1,a=Cg(a);;){var i=Eg(a);if(i.expr==null)return Oe("The selector `"+e+"`is invalid"),!1;var s=i.match.slice(1),o=i.expr.populate(t,n,s);if(o===!1)return!1;if(o!=null&&(n=o),a=i.remaining,a.match(/^\s*$/))break}var l=t[t.length-1];t.currentSubject!=null&&(l.subject=t.currentSubject),l.edgeCount=t.edgeCount,l.compoundCount=t.compoundCount;for(var u=0;u<t.length;u++){var v=t[u];if(v.compoundCount>0&&v.edgeCount>0)return Oe("The selector `"+e+"` is invalid because it uses both a compound selector and an edge selector"),!1;if(v.edgeCount>1)return Oe("The selector `"+e+"` is invalid because it uses multiple edge selectors"),!1;v.edgeCount===1&&Oe("The selector `"+e+"` is deprecated.  Edge selectors do not take effect on changes to source and target nodes after an edge is added, for performance reasons.  Use a class or data selector on edges instead, updating the class or data of an edge when your app detects a change in source or target nodes.")}return!0},Sg=function(){if(this.toStringCache!=null)return this.toStringCache;for(var e=function(v){return v==null?"":v},t=function(v){return he(v)?'"'+v+'"':e(v)},a=function(v){return" "+v+" "},n=function(v,f){var c=v.type,h=v.value;switch(c){case se.GROUP:{var d=e(h);return d.substring(0,d.length-1)}case se.DATA_COMPARE:{var y=v.field,g=v.operator;return"["+y+a(e(g))+t(h)+"]"}case se.DATA_BOOL:{var p=v.operator,m=v.field;return"["+e(p)+m+"]"}case se.DATA_EXIST:{var b=v.field;return"["+b+"]"}case se.META_COMPARE:{var w=v.operator,E=v.field;return"[["+E+a(e(w))+t(h)+"]]"}case se.STATE:return h;case se.ID:return"#"+h;case se.CLASS:return"."+h;case se.PARENT:case se.CHILD:return i(v.parent,f)+a(">")+i(v.child,f);case se.ANCESTOR:case se.DESCENDANT:return i(v.ancestor,f)+" "+i(v.descendant,f);case se.COMPOUND_SPLIT:{var C=i(v.left,f),x=i(v.subject,f),T=i(v.right,f);return C+(C.length>0?" ":"")+x+T}case se.TRUE:return""}},i=function(v,f){return v.checks.reduce(function(c,h,d){return c+(f===v&&d===0?"$":"")+n(h,f)},"")},s="",o=0;o<this.length;o++){var l=this[o];s+=i(l,l.subject),this.length>1&&o<this.length-1&&(s+=", ")}return this.toStringCache=s,s},kg={parse:Tg,toString:Sg},Gl=function(e,t,a){var n,i=he(e),s=te(e),o=he(a),l,u,v=!1,f=!1,c=!1;switch(t.indexOf("!")>=0&&(t=t.replace("!",""),f=!0),t.indexOf("@")>=0&&(t=t.replace("@",""),v=!0),(i||o||v)&&(l=!i&&!s?"":""+e,u=""+a),v&&(e=l=l.toLowerCase(),a=u=u.toLowerCase()),t){case"*=":n=l.indexOf(u)>=0;break;case"$=":n=l.indexOf(u,l.length-u.length)>=0;break;case"^=":n=l.indexOf(u)===0;break;case"=":n=e===a;break;case">":c=!0,n=e>a;break;case">=":c=!0,n=e>=a;break;case"<":c=!0,n=e<a;break;case"<=":c=!0,n=e<=a;break;default:n=!1;break}return f&&(e!=null||!c)&&(n=!n),n},Dg=function(e,t){switch(t){case"?":return!!e;case"!":return!e;case"^":return e===void 0}},Bg=function(e){return e!==void 0},zs=function(e,t){return e.data(t)},Pg=function(e,t){return e[t]()},Ke=[],We=function(e,t){return e.checks.every(function(a){return Ke[a.type](a,t)})};Ke[se.GROUP]=function(r,e){var t=r.value;return t==="*"||t===e.group()},Ke[se.STATE]=function(r,e){var t=r.value;return wg(t,e)},Ke[se.ID]=function(r,e){var t=r.value;return e.id()===t},Ke[se.CLASS]=function(r,e){var t=r.value;return e.hasClass(t)},Ke[se.META_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Gl(Pg(e,t),a,n)},Ke[se.DATA_COMPARE]=function(r,e){var t=r.field,a=r.operator,n=r.value;return Gl(zs(e,t),a,n)},Ke[se.DATA_BOOL]=function(r,e){var t=r.field,a=r.operator;return Dg(zs(e,t),a)},Ke[se.DATA_EXIST]=function(r,e){var t=r.field;return r.operator,Bg(zs(e,t))},Ke[se.UNDIRECTED_EDGE]=function(r,e){var t=r.nodes[0],a=r.nodes[1],n=e.source(),i=e.target();return We(t,n)&&We(a,i)||We(a,n)&&We(t,i)},Ke[se.NODE_NEIGHBOR]=function(r,e){return We(r.node,e)&&e.neighborhood().some(function(t){return t.isNode()&&We(r.neighbor,t)})},Ke[se.DIRECTED_EDGE]=function(r,e){return We(r.source,e.source())&&We(r.target,e.target())},Ke[se.NODE_SOURCE]=function(r,e){return We(r.source,e)&&e.outgoers().some(function(t){return t.isNode()&&We(r.target,t)})},Ke[se.NODE_TARGET]=function(r,e){return We(r.target,e)&&e.incomers().some(function(t){return t.isNode()&&We(r.source,t)})},Ke[se.CHILD]=function(r,e){return We(r.child,e)&&We(r.parent,e.parent())},Ke[se.PARENT]=function(r,e){return We(r.parent,e)&&e.children().some(function(t){return We(r.child,t)})},Ke[se.DESCENDANT]=function(r,e){return We(r.descendant,e)&&e.ancestors().some(function(t){return We(r.ancestor,t)})},Ke[se.ANCESTOR]=function(r,e){return We(r.ancestor,e)&&e.descendants().some(function(t){return We(r.descendant,t)})},Ke[se.COMPOUND_SPLIT]=function(r,e){return We(r.subject,e)&&We(r.left,e)&&We(r.right,e)},Ke[se.TRUE]=function(){return!0},Ke[se.COLLECTION]=function(r,e){var t=r.value;return t.has(e)},Ke[se.FILTER]=function(r,e){var t=r.value;return t(e)};var Ag=function(e){var t=this;if(t.length===1&&t[0].checks.length===1&&t[0].checks[0].type===se.ID)return e.getElementById(t[0].checks[0].value).collection();var a=function(i){for(var s=0;s<t.length;s++){var o=t[s];if(We(o,i))return!0}return!1};return t.text()==null&&(a=function(){return!0}),e.filter(a)},Rg=function(e){for(var t=this,a=0;a<t.length;a++){var n=t[a];if(We(n,e))return!0}return!1},Mg={matches:Rg,filter:Ag},lt=function(e){this.inputText=e,this.currentSubject=null,this.compoundCount=0,this.edgeCount=0,this.length=0,e==null||he(e)&&e.match(/^\s*$/)||(Dr(e)?this.addQuery({checks:[{type:se.COLLECTION,value:e.collection()}]}):Ue(e)?this.addQuery({checks:[{type:se.FILTER,value:e}]}):he(e)?this.parse(e)||(this.invalid=!0):$e("A selector must be created from a string; found "))},vt=lt.prototype;[kg,Mg].forEach(function(r){return me(vt,r)}),vt.text=function(){return this.inputText},vt.size=function(){return this.length},vt.eq=function(r){return this[r]},vt.sameText=function(r){return!this.invalid&&!r.invalid&&this.text()===r.text()},vt.addQuery=function(r){this[this.length++]=r},vt.selector=vt.toString;var ft={allAre:function(e){var t=new lt(e);return this.every(function(a){return t.matches(a)})},is:function(e){var t=new lt(e);return this.some(function(a){return t.matches(a)})},some:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(n)return!0}return!1},every:function(e,t){for(var a=0;a<this.length;a++){var n=t?e.apply(t,[this[a],a,this]):e(this[a],a,this);if(!n)return!1}return!0},same:function(e){if(this===e)return!0;e=this.cy().collection(e);var t=this.length,a=e.length;return t!==a?!1:t===1?this[0]===e[0]:this.every(function(n){return e.hasElementWithId(n.id())})},anySame:function(e){return e=this.cy().collection(e),this.some(function(t){return e.hasElementWithId(t.id())})},allAreNeighbors:function(e){e=this.cy().collection(e);var t=this.neighborhood();return e.every(function(a){return t.hasElementWithId(a.id())})},contains:function(e){e=this.cy().collection(e);var t=this;return e.every(function(a){return t.hasElementWithId(a.id())})}};ft.allAreNeighbours=ft.allAreNeighbors,ft.has=ft.contains,ft.equal=ft.equals=ft.same;var Br=function(e,t){return function(n,i,s,o){var l=n,u=this,v;if(l==null?v="":Dr(l)&&l.length===1&&(v=l.id()),u.length===1&&v){var f=u[0]._private,c=f.traversalCache=f.traversalCache||{},h=c[t]=c[t]||[],d=wt(v),y=h[d];return y||(h[d]=e.call(u,n,i,s,o))}else return e.call(u,n,i,s,o)}},Ut={parent:function(e){var t=[];if(this.length===1){var a=this[0]._private.parent;if(a)return a}for(var n=0;n<this.length;n++){var i=this[n],s=i._private.parent;s&&t.push(s)}return this.spawn(t,!0).filter(e)},parents:function(e){for(var t=[],a=this.parent();a.nonempty();){for(var n=0;n<a.length;n++){var i=a[n];t.push(i)}a=a.parent()}return this.spawn(t,!0).filter(e)},commonAncestors:function(e){for(var t,a=0;a<this.length;a++){var n=this[a],i=n.parents();t=t||i,t=t.intersect(i)}return t.filter(e)},orphans:function(e){return this.stdFilter(function(t){return t.isOrphan()}).filter(e)},nonorphans:function(e){return this.stdFilter(function(t){return t.isChild()}).filter(e)},children:Br(function(r){for(var e=[],t=0;t<this.length;t++)for(var a=this[t],n=a._private.children,i=0;i<n.length;i++)e.push(n[i]);return this.spawn(e,!0).filter(r)},"children"),siblings:function(e){return this.parent().children().not(this).filter(e)},isParent:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length!==0},isChildless:function(){var e=this[0];if(e)return e.isNode()&&e._private.children.length===0},isChild:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent!=null},isOrphan:function(){var e=this[0];if(e)return e.isNode()&&e._private.parent==null},descendants:function(e){var t=[];function a(n){for(var i=0;i<n.length;i++){var s=n[i];t.push(s),s.children().nonempty()&&a(s.children())}}return a(this.children()),this.spawn(t,!0).filter(e)}};function Fs(r,e,t,a){for(var n=[],i=new zt,s=r.cy(),o=s.hasCompoundNodes(),l=0;l<r.length;l++){var u=r[l];t?n.push(u):o&&a(n,i,u)}for(;n.length>0;){var v=n.shift();e(v),i.add(v.id()),o&&a(n,i,v)}return r}function Hl(r,e,t){if(t.isParent())for(var a=t._private.children,n=0;n<a.length;n++){var i=a[n];e.has(i.id())||r.push(i)}}Ut.forEachDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Fs(this,r,e,Hl)};function Wl(r,e,t){if(t.isChild()){var a=t._private.parent;e.has(a.id())||r.push(a)}}Ut.forEachUp=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Fs(this,r,e,Wl)};function Lg(r,e,t){Wl(r,e,t),Hl(r,e,t)}Ut.forEachUpAndDown=function(r){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;return Fs(this,r,e,Lg)},Ut.ancestors=Ut.parents;var xa,$l;xa=$l={data:Ne.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),removeData:Ne.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,immutableKeys:{id:!0,source:!0,target:!0,parent:!0},updateStyle:!0}),scratch:Ne.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ne.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),rscratch:Ne.data({field:"rscratch",allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!0}),removeRscratch:Ne.removeData({field:"rscratch",triggerEvent:!1}),id:function(){var e=this[0];if(e)return e._private.data.id}},xa.attr=xa.data,xa.removeAttr=xa.removeData;var Ig=$l,mn={};function Vs(r){return function(e){var t=this;if(e===void 0&&(e=!0),t.length!==0)if(t.isNode()&&!t.removed()){for(var a=0,n=t[0],i=n._private.edges,s=0;s<i.length;s++){var o=i[s];!e&&o.isLoop()||(a+=r(n,o))}return a}else return}}me(mn,{degree:Vs(function(r,e){return e.source().same(e.target())?2:1}),indegree:Vs(function(r,e){return e.target().same(r)?1:0}),outdegree:Vs(function(r,e){return e.source().same(r)?1:0})});function Kt(r,e){return function(t){for(var a,n=this.nodes(),i=0;i<n.length;i++){var s=n[i],o=s[r](t);o!==void 0&&(a===void 0||e(o,a))&&(a=o)}return a}}me(mn,{minDegree:Kt("degree",function(r,e){return r<e}),maxDegree:Kt("degree",function(r,e){return r>e}),minIndegree:Kt("indegree",function(r,e){return r<e}),maxIndegree:Kt("indegree",function(r,e){return r>e}),minOutdegree:Kt("outdegree",function(r,e){return r<e}),maxOutdegree:Kt("outdegree",function(r,e){return r>e})}),me(mn,{totalDegree:function(e){for(var t=0,a=this.nodes(),n=0;n<a.length;n++)t+=a[n].degree(e);return t}});var Lr,Ul,Kl=function(e,t,a){for(var n=0;n<e.length;n++){var i=e[n];if(!i.locked()){var s=i._private.position,o={x:t.x!=null?t.x-s.x:0,y:t.y!=null?t.y-s.y:0};i.isParent()&&!(o.x===0&&o.y===0)&&i.children().shift(o,a),i.dirtyBoundingBoxCache()}}},Xl={field:"position",bindingEvent:"position",allowBinding:!0,allowSetting:!0,settingEvent:"position",settingTriggersEvent:!0,triggerFnName:"emitAndNotify",allowGetting:!0,validKeys:["x","y"],beforeGet:function(e){e.updateCompoundBounds()},beforeSet:function(e,t){Kl(e,t,!1)},onSet:function(e){e.dirtyCompoundBoundsCache()},canSet:function(e){return!e.locked()}};Lr=Ul={position:Ne.data(Xl),silentPosition:Ne.data(me({},Xl,{allowBinding:!1,allowSetting:!0,settingTriggersEvent:!1,allowGetting:!1,beforeSet:function(e,t){Kl(e,t,!0)},onSet:function(e){e.dirtyCompoundBoundsCache()}})),positions:function(e,t){if(Me(e))t?this.silentPosition(e):this.position(e);else if(Ue(e)){var a=e,n=this.cy();n.startBatch();for(var i=0;i<this.length;i++){var s=this[i],o=void 0;(o=a(s,i))&&(t?s.silentPosition(o):s.position(o))}n.endBatch()}return this},silentPositions:function(e){return this.positions(e,!0)},shift:function(e,t,a){var n;if(Me(e)?(n={x:te(e.x)?e.x:0,y:te(e.y)?e.y:0},a=t):he(e)&&te(t)&&(n={x:0,y:0},n[e]=t),n!=null){var i=this.cy();i.startBatch();for(var s=0;s<this.length;s++){var o=this[s];if(!(i.hasCompoundNodes()&&o.isChild()&&o.ancestors().anySame(this))){var l=o.position(),u={x:l.x+n.x,y:l.y+n.y};a?o.silentPosition(u):o.position(u)}}i.endBatch()}return this},silentShift:function(e,t){return Me(e)?this.shift(e,!0):he(e)&&te(t)&&this.shift(e,t,!0),this},renderedPosition:function(e,t){var a=this[0],n=this.cy(),i=n.zoom(),s=n.pan(),o=Me(e)?e:void 0,l=o!==void 0||t!==void 0&&he(e);if(a&&a.isNode())if(l)for(var u=0;u<this.length;u++){var v=this[u];t!==void 0?v.position(e,(t-s[e])/i):o!==void 0&&v.position(ou(o,i,s))}else{var f=a.position();return o=rn(f,i,s),e===void 0?o:o[e]}else if(!l)return;return this},relativePosition:function(e,t){var a=this[0],n=this.cy(),i=Me(e)?e:void 0,s=i!==void 0||t!==void 0&&he(e),o=n.hasCompoundNodes();if(a&&a.isNode())if(s)for(var l=0;l<this.length;l++){var u=this[l],v=o?u.parent():null,f=v&&v.length>0,c=f;f&&(v=v[0]);var h=c?v.position():{x:0,y:0};t!==void 0?u.position(e,t+h[e]):i!==void 0&&u.position({x:i.x+h.x,y:i.y+h.y})}else{var d=a.position(),y=o?a.parent():null,g=y&&y.length>0,p=g;g&&(y=y[0]);var m=p?y.position():{x:0,y:0};return i={x:d.x-m.x,y:d.y-m.y},e===void 0?i:i[e]}else if(!s)return;return this}},Lr.modelPosition=Lr.point=Lr.position,Lr.modelPositions=Lr.points=Lr.positions,Lr.renderedPoint=Lr.renderedPosition,Lr.relativePoint=Lr.relativePosition;var Og=Ul,Xt,ct;Xt=ct={},ct.renderedBoundingBox=function(r){var e=this.boundingBox(r),t=this.cy(),a=t.zoom(),n=t.pan(),i=e.x1*a+n.x,s=e.x2*a+n.x,o=e.y1*a+n.y,l=e.y2*a+n.y;return{x1:i,x2:s,y1:o,y2:l,w:s-i,h:l-o}},ct.dirtyCompoundBoundsCache=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();return!e.styleEnabled()||!e.hasCompoundNodes()?this:(this.forEachUp(function(t){if(t.isParent()){var a=t._private;a.compoundBoundsClean=!1,a.bbCache=null,r||t.emitAndNotify("bounds")}}),this)},ct.updateCompoundBounds=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,e=this.cy();if(!e.styleEnabled()||!e.hasCompoundNodes())return this;if(!r&&e.batching())return this;function t(s){if(!s.isParent())return;var o=s._private,l=s.children(),u=s.pstyle("compound-sizing-wrt-labels").value==="include",v={width:{val:s.pstyle("min-width").pfValue,left:s.pstyle("min-width-bias-left"),right:s.pstyle("min-width-bias-right")},height:{val:s.pstyle("min-height").pfValue,top:s.pstyle("min-height-bias-top"),bottom:s.pstyle("min-height-bias-bottom")}},f=l.boundingBox({includeLabels:u,includeOverlays:!1,useCache:!1}),c=o.position;(f.w===0||f.h===0)&&(f={w:s.pstyle("width").pfValue,h:s.pstyle("height").pfValue},f.x1=c.x-f.w/2,f.x2=c.x+f.w/2,f.y1=c.y-f.h/2,f.y2=c.y+f.h/2);function h(k,D,B){var P=0,A=0,R=D+B;return k>0&&R>0&&(P=D/R*k,A=B/R*k),{biasDiff:P,biasComplementDiff:A}}function d(k,D,B,P){if(B.units==="%")switch(P){case"width":return k>0?B.pfValue*k:0;case"height":return D>0?B.pfValue*D:0;case"average":return k>0&&D>0?B.pfValue*(k+D)/2:0;case"min":return k>0&&D>0?k>D?B.pfValue*D:B.pfValue*k:0;case"max":return k>0&&D>0?k>D?B.pfValue*k:B.pfValue*D:0;default:return 0}else return B.units==="px"?B.pfValue:0}var y=v.width.left.value;v.width.left.units==="px"&&v.width.val>0&&(y=y*100/v.width.val);var g=v.width.right.value;v.width.right.units==="px"&&v.width.val>0&&(g=g*100/v.width.val);var p=v.height.top.value;v.height.top.units==="px"&&v.height.val>0&&(p=p*100/v.height.val);var m=v.height.bottom.value;v.height.bottom.units==="px"&&v.height.val>0&&(m=m*100/v.height.val);var b=h(v.width.val-f.w,y,g),w=b.biasDiff,E=b.biasComplementDiff,C=h(v.height.val-f.h,p,m),x=C.biasDiff,T=C.biasComplementDiff;o.autoPadding=d(f.w,f.h,s.pstyle("padding"),s.pstyle("padding-relative-to").value),o.autoWidth=Math.max(f.w,v.width.val),c.x=(-w+f.x1+f.x2+E)/2,o.autoHeight=Math.max(f.h,v.height.val),c.y=(-x+f.y1+f.y2+T)/2}for(var a=0;a<this.length;a++){var n=this[a],i=n._private;(!i.compoundBoundsClean||r)&&(t(n),e.batching()||(i.compoundBoundsClean=!0))}return this};var Pr=function(e){return e===1/0||e===-1/0?0:e},Ir=function(e,t,a,n,i){n-t===0||i-a===0||t==null||a==null||n==null||i==null||(e.x1=t<e.x1?t:e.x1,e.x2=n>e.x2?n:e.x2,e.y1=a<e.y1?a:e.y1,e.y2=i>e.y2?i:e.y2,e.w=e.x2-e.x1,e.h=e.y2-e.y1)},dt=function(e,t){return t==null?e:Ir(e,t.x1,t.y1,t.x2,t.y2)},Ea=function(e,t,a){return Cr(e,t,a)},bn=function(e,t,a){if(!t.cy().headless()){var n=t._private,i=n.rstyle,s=i.arrowWidth/2,o=t.pstyle(a+"-arrow-shape").value,l,u;if(o!=="none"){a==="source"?(l=i.srcX,u=i.srcY):a==="target"?(l=i.tgtX,u=i.tgtY):(l=i.midX,u=i.midY);var v=n.arrowBounds=n.arrowBounds||{},f=v[a]=v[a]||{};f.x1=l-s,f.y1=u-s,f.x2=l+s,f.y2=u+s,f.w=f.x2-f.x1,f.h=f.y2-f.y1,an(f,1),Ir(e,f.x1,f.y1,f.x2,f.y2)}}},qs=function(e,t,a){if(!t.cy().headless()){var n;a?n=a+"-":n="";var i=t._private,s=i.rstyle,o=t.pstyle(n+"label").strValue;if(o){var l=t.pstyle("text-halign"),u=t.pstyle("text-valign"),v=Ea(s,"labelWidth",a),f=Ea(s,"labelHeight",a),c=Ea(s,"labelX",a),h=Ea(s,"labelY",a),d=t.pstyle(n+"text-margin-x").pfValue,y=t.pstyle(n+"text-margin-y").pfValue,g=t.isEdge(),p=t.pstyle(n+"text-rotation"),m=t.pstyle("text-outline-width").pfValue,b=t.pstyle("text-border-width").pfValue,w=b/2,E=t.pstyle("text-background-padding").pfValue,C=2,x=f,T=v,k=T/2,D=x/2,B,P,A,R;if(g)B=c-k,P=c+k,A=h-D,R=h+D;else{switch(l.value){case"left":B=c-T,P=c;break;case"center":B=c-k,P=c+k;break;case"right":B=c,P=c+T;break}switch(u.value){case"top":A=h-x,R=h;break;case"center":A=h-D,R=h+D;break;case"bottom":A=h,R=h+x;break}}var L=d-Math.max(m,w)-E-C,I=d+Math.max(m,w)+E+C,M=y-Math.max(m,w)-E-C,O=y+Math.max(m,w)+E+C;B+=L,P+=I,A+=M,R+=O;var V=a||"main",G=i.labelBounds,N=G[V]=G[V]||{};N.x1=B,N.y1=A,N.x2=P,N.y2=R,N.w=P-B,N.h=R-A,N.leftPad=L,N.rightPad=I,N.topPad=M,N.botPad=O;var F=g&&p.strValue==="autorotate",U=p.pfValue!=null&&p.pfValue!==0;if(F||U){var Q=F?Ea(i.rstyle,"labelAngle",a):p.pfValue,K=Math.cos(Q),j=Math.sin(Q),re=(B+P)/2,ne=(A+R)/2;if(!g){switch(l.value){case"left":re=P;break;case"right":re=B;break}switch(u.value){case"top":ne=R;break;case"bottom":ne=A;break}}var J=function(Ce,we){return Ce=Ce-re,we=we-ne,{x:Ce*K-we*j+re,y:Ce*j+we*K+ne}},z=J(B,A),q=J(B,R),H=J(P,A),Y=J(P,R);B=Math.min(z.x,q.x,H.x,Y.x),P=Math.max(z.x,q.x,H.x,Y.x),A=Math.min(z.y,q.y,H.y,Y.y),R=Math.max(z.y,q.y,H.y,Y.y)}var ae=V+"Rot",ce=G[ae]=G[ae]||{};ce.x1=B,ce.y1=A,ce.x2=P,ce.y2=R,ce.w=P-B,ce.h=R-A,Ir(e,B,A,P,R),Ir(i.labelBounds.all,B,A,P,R)}return e}},Yl=function(e,t){if(!t.cy().headless()){var a=t.pstyle("outline-opacity").value,n=t.pstyle("outline-width").value,i=t.pstyle("outline-offset").value,s=n+i;Zl(e,t,a,s,"outside",s/2)}},Zl=function(e,t,a,n,i,s){if(!(a===0||n<=0||i==="inside")){var o=t.cy(),l=t.pstyle("shape").value,u=o.renderer().nodeShapes[l],v=t.position(),f=v.x,c=v.y,h=t.width(),d=t.height();if(u.hasMiterBounds){i==="center"&&(n/=2);var y=u.miterBounds(f,c,h,d,n);dt(e,y)}else s!=null&&s>0&&nn(e,[s,s,s,s])}},Ng=function(e,t){if(!t.cy().headless()){var a=t.pstyle("border-opacity").value,n=t.pstyle("border-width").pfValue,i=t.pstyle("border-position").value;Zl(e,t,a,n,i)}},zg=function(e,t){var a=e._private.cy,n=a.styleEnabled(),i=a.headless(),s=mr(),o=e._private,l=e.isNode(),u=e.isEdge(),v,f,c,h,d,y,g=o.rstyle,p=l&&n?e.pstyle("bounds-expansion").pfValue:[0],m=function(Ae){return Ae.pstyle("display").value!=="none"},b=!n||m(e)&&(!u||m(e.source())&&m(e.target()));if(b){var w=0,E=0;n&&t.includeOverlays&&(w=e.pstyle("overlay-opacity").value,w!==0&&(E=e.pstyle("overlay-padding").value));var C=0,x=0;n&&t.includeUnderlays&&(C=e.pstyle("underlay-opacity").value,C!==0&&(x=e.pstyle("underlay-padding").value));var T=Math.max(E,x),k=0,D=0;if(n&&(k=e.pstyle("width").pfValue,D=k/2),l&&t.includeNodes){var B=e.position();d=B.x,y=B.y;var P=e.outerWidth(),A=P/2,R=e.outerHeight(),L=R/2;v=d-A,f=d+A,c=y-L,h=y+L,Ir(s,v,c,f,h),n&&Yl(s,e),n&&t.includeOutlines&&!i&&Yl(s,e),n&&Ng(s,e)}else if(u&&t.includeEdges)if(n&&!i){var I=e.pstyle("curve-style").strValue;if(v=Math.min(g.srcX,g.midX,g.tgtX),f=Math.max(g.srcX,g.midX,g.tgtX),c=Math.min(g.srcY,g.midY,g.tgtY),h=Math.max(g.srcY,g.midY,g.tgtY),v-=D,f+=D,c-=D,h+=D,Ir(s,v,c,f,h),I==="haystack"){var M=g.haystackPts;if(M&&M.length===2){if(v=M[0].x,c=M[0].y,f=M[1].x,h=M[1].y,v>f){var O=v;v=f,f=O}if(c>h){var V=c;c=h,h=V}Ir(s,v-D,c-D,f+D,h+D)}}else if(I==="bezier"||I==="unbundled-bezier"||tt(I,"segments")||tt(I,"taxi")){var G;switch(I){case"bezier":case"unbundled-bezier":G=g.bezierPts;break;case"segments":case"taxi":case"round-segments":case"round-taxi":G=g.linePts;break}if(G!=null)for(var N=0;N<G.length;N++){var F=G[N];v=F.x-D,f=F.x+D,c=F.y-D,h=F.y+D,Ir(s,v,c,f,h)}}}else{var U=e.source(),Q=U.position(),K=e.target(),j=K.position();if(v=Q.x,f=j.x,c=Q.y,h=j.y,v>f){var re=v;v=f,f=re}if(c>h){var ne=c;c=h,h=ne}v-=D,f+=D,c-=D,h+=D,Ir(s,v,c,f,h)}if(n&&t.includeEdges&&u&&(bn(s,e,"mid-source"),bn(s,e,"mid-target"),bn(s,e,"source"),bn(s,e,"target")),n){var J=e.pstyle("ghost").value==="yes";if(J){var z=e.pstyle("ghost-offset-x").pfValue,q=e.pstyle("ghost-offset-y").pfValue;Ir(s,s.x1+z,s.y1+q,s.x2+z,s.y2+q)}}var H=o.bodyBounds=o.bodyBounds||{};lu(H,s),nn(H,p),an(H,1),n&&(v=s.x1,f=s.x2,c=s.y1,h=s.y2,Ir(s,v-T,c-T,f+T,h+T));var Y=o.overlayBounds=o.overlayBounds||{};lu(Y,s),nn(Y,p),an(Y,1);var ae=o.labelBounds=o.labelBounds||{};ae.all!=null?md(ae.all):ae.all=mr(),n&&t.includeLabels&&(t.includeMainLabels&&qs(s,e,null),u&&(t.includeSourceLabels&&qs(s,e,"source"),t.includeTargetLabels&&qs(s,e,"target")))}return s.x1=Pr(s.x1),s.y1=Pr(s.y1),s.x2=Pr(s.x2),s.y2=Pr(s.y2),s.w=Pr(s.x2-s.x1),s.h=Pr(s.y2-s.y1),s.w>0&&s.h>0&&b&&(nn(s,p),an(s,1)),s},Ql=function(e){var t=0,a=function(s){return(s?1:0)<<t++},n=0;return n+=a(e.incudeNodes),n+=a(e.includeEdges),n+=a(e.includeLabels),n+=a(e.includeMainLabels),n+=a(e.includeSourceLabels),n+=a(e.includeTargetLabels),n+=a(e.includeOverlays),n+=a(e.includeOutlines),n},Jl=function(e){var t=function(o){return Math.round(o)};if(e.isEdge()){var a=e.source().position(),n=e.target().position();return Zo([t(a.x),t(a.y),t(n.x),t(n.y)])}else{var i=e.position();return Zo([t(i.x),t(i.y)])}},jl=function(e,t){var a=e._private,n,i=e.isEdge(),s=t==null?ev:Ql(t),o=s===ev;if(a.bbCache==null?(n=zg(e,Ca),a.bbCache=n,a.bbCachePosKey=Jl(e)):n=a.bbCache,!o){var l=e.isNode();n=mr(),(t.includeNodes&&l||t.includeEdges&&!l)&&(t.includeOverlays?dt(n,a.overlayBounds):dt(n,a.bodyBounds)),t.includeLabels&&(t.includeMainLabels&&(!i||t.includeSourceLabels&&t.includeTargetLabels)?dt(n,a.labelBounds.all):(t.includeMainLabels&&dt(n,a.labelBounds.mainRot),t.includeSourceLabels&&dt(n,a.labelBounds.sourceRot),t.includeTargetLabels&&dt(n,a.labelBounds.targetRot))),n.w=n.x2-n.x1,n.h=n.y2-n.y1}return n},Ca={includeNodes:!0,includeEdges:!0,includeLabels:!0,includeMainLabels:!0,includeSourceLabels:!0,includeTargetLabels:!0,includeOverlays:!0,includeUnderlays:!0,includeOutlines:!0,useCache:!0},ev=Ql(Ca),rv=lr(Ca);ct.boundingBox=function(r){var e,t=r===void 0||r.useCache===void 0||r.useCache===!0,a=Ot(function(v){var f=v._private;return f.bbCache==null||f.styleDirty||f.bbCachePosKey!==Jl(v)},function(v){return v.id()});if(t&&this.length===1&&!a(this[0]))r===void 0?r=Ca:r=rv(r),e=jl(this[0],r);else{e=mr(),r=r||Ca;var n=rv(r),i=this,s=i.cy(),o=s.styleEnabled();this.edges().forEach(a),this.nodes().forEach(a),o&&this.recalculateRenderedStyle(t),this.updateCompoundBounds(!t);for(var l=0;l<i.length;l++){var u=i[l];a(u)&&u.dirtyBoundingBoxCache(),dt(e,jl(u,n))}}return e.x1=Pr(e.x1),e.y1=Pr(e.y1),e.x2=Pr(e.x2),e.y2=Pr(e.y2),e.w=Pr(e.x2-e.x1),e.h=Pr(e.y2-e.y1),e},ct.dirtyBoundingBoxCache=function(){for(var r=0;r<this.length;r++){var e=this[r]._private;e.bbCache=null,e.bbCachePosKey=null,e.bodyBounds=null,e.overlayBounds=null,e.labelBounds.all=null,e.labelBounds.source=null,e.labelBounds.target=null,e.labelBounds.main=null,e.labelBounds.sourceRot=null,e.labelBounds.targetRot=null,e.labelBounds.mainRot=null,e.arrowBounds.source=null,e.arrowBounds.target=null,e.arrowBounds["mid-source"]=null,e.arrowBounds["mid-target"]=null}return this.emitAndNotify("bounds"),this},ct.boundingBoxAt=function(r){var e=this.nodes(),t=this.cy(),a=t.hasCompoundNodes(),n=t.collection();if(a&&(n=e.filter(function(u){return u.isParent()}),e=e.not(n)),Me(r)){var i=r;r=function(){return i}}var s=function(v,f){return v._private.bbAtOldPos=r(v,f)},o=function(v){return v._private.bbAtOldPos};t.startBatch(),e.forEach(s).silentPositions(r),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0));var l=yd(this.boundingBox({useCache:!1}));return e.silentPositions(o),a&&(n.dirtyCompoundBoundsCache(),n.dirtyBoundingBoxCache(),n.updateCompoundBounds(!0)),t.endBatch(),l},Xt.boundingbox=Xt.bb=Xt.boundingBox,Xt.renderedBoundingbox=Xt.renderedBoundingBox;var Fg=ct,Ta,Sa;Ta=Sa={};var tv=function(e){e.uppercaseName=Do(e.name),e.autoName="auto"+e.uppercaseName,e.labelName="label"+e.uppercaseName,e.outerName="outer"+e.uppercaseName,e.uppercaseOuterName=Do(e.outerName),Ta[e.name]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){if(a.isParent())return a.updateCompoundBounds(),n[e.autoName]||0;var o=a.pstyle(e.name);switch(o.strValue){case"label":return a.recalculateRenderedStyle(),n.rstyle[e.labelName]||0;default:return o.pfValue}}else return 1},Ta["outer"+e.uppercaseName]=function(){var a=this[0],n=a._private,i=n.cy,s=i._private.styleEnabled;if(a)if(s){var o=a[e.name](),l=a.pstyle("border-position").value,u;l==="center"?u=a.pstyle("border-width").pfValue:l==="outside"?u=2*a.pstyle("border-width").pfValue:u=0;var v=2*a.padding();return o+u+v}else return 1},Ta["rendered"+e.uppercaseName]=function(){var a=this[0];if(a){var n=a[e.name]();return n*this.cy().zoom()}},Ta["rendered"+e.uppercaseOuterName]=function(){var a=this[0];if(a){var n=a[e.outerName]();return n*this.cy().zoom()}}};tv({name:"width"}),tv({name:"height"}),Sa.padding=function(){var r=this[0],e=r._private;return r.isParent()?(r.updateCompoundBounds(),e.autoPadding!==void 0?e.autoPadding:r.pstyle("padding").pfValue):r.pstyle("padding").pfValue},Sa.paddedHeight=function(){var r=this[0];return r.height()+2*r.padding()},Sa.paddedWidth=function(){var r=this[0];return r.width()+2*r.padding()};var Vg=Sa,qg=function(e,t){if(e.isEdge()&&e.takesUpSpace())return t(e)},_g=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy();return rn(t(e),a.zoom(),a.pan())}},Gg=function(e,t){if(e.isEdge()&&e.takesUpSpace()){var a=e.cy(),n=a.pan(),i=a.zoom();return t(e).map(function(s){return rn(s,i,n)})}},Hg=function(e){return e.renderer().getControlPoints(e)},Wg=function(e){return e.renderer().getSegmentPoints(e)},$g=function(e){return e.renderer().getSourceEndpoint(e)},Ug=function(e){return e.renderer().getTargetEndpoint(e)},Kg=function(e){return e.renderer().getEdgeMidpoint(e)},av={controlPoints:{get:Hg,mult:!0},segmentPoints:{get:Wg,mult:!0},sourceEndpoint:{get:$g},targetEndpoint:{get:Ug},midpoint:{get:Kg}},Xg=function(e){return"rendered"+e[0].toUpperCase()+e.substr(1)},Yg=Object.keys(av).reduce(function(r,e){var t=av[e],a=Xg(e);return r[e]=function(){return qg(this,t.get)},t.mult?r[a]=function(){return Gg(this,t.get)}:r[a]=function(){return _g(this,t.get)},r},{}),Zg=me({},Og,Fg,Vg,Yg);var nv=function(e,t){this.recycle(e,t)};function ka(){return!1}function wn(){return!0}nv.prototype={instanceString:function(){return"event"},recycle:function(e,t){if(this.isImmediatePropagationStopped=this.isPropagationStopped=this.isDefaultPrevented=ka,e!=null&&e.preventDefault?(this.type=e.type,this.isDefaultPrevented=e.defaultPrevented?wn:ka):e!=null&&e.type?t=e:this.type=e,t!=null&&(this.originalEvent=t.originalEvent,this.type=t.type!=null?t.type:this.type,this.cy=t.cy,this.target=t.target,this.position=t.position,this.renderedPosition=t.renderedPosition,this.namespace=t.namespace,this.layout=t.layout),this.cy!=null&&this.position!=null&&this.renderedPosition==null){var a=this.position,n=this.cy.zoom(),i=this.cy.pan();this.renderedPosition={x:a.x*n+i.x,y:a.y*n+i.y}}this.timeStamp=e&&e.timeStamp||Date.now()},preventDefault:function(){this.isDefaultPrevented=wn;var e=this.originalEvent;e&&e.preventDefault&&e.preventDefault()},stopPropagation:function(){this.isPropagationStopped=wn;var e=this.originalEvent;e&&e.stopPropagation&&e.stopPropagation()},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=wn,this.stopPropagation()},isDefaultPrevented:ka,isPropagationStopped:ka,isImmediatePropagationStopped:ka};var iv=/^([^.]+)(\.(?:[^.]+))?$/,Qg=".*",sv={qualifierCompare:function(e,t){return e===t},eventMatches:function(){return!0},addEventFields:function(){},callbackContext:function(e){return e},beforeEmit:function(){},afterEmit:function(){},bubble:function(){return!1},parent:function(){return null},context:null},ov=Object.keys(sv),Jg={};function xn(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Jg,e=arguments.length>1?arguments[1]:void 0,t=0;t<ov.length;t++){var a=ov[t];this[a]=r[a]||sv[a]}this.context=e||this.context,this.listeners=[],this.emitting=0}var ht=xn.prototype,uv=function(e,t,a,n,i,s,o){Ue(n)&&(i=n,n=null),o&&(s==null?s=o:s=me({},s,o));for(var l=_e(a)?a:a.split(/\s+/),u=0;u<l.length;u++){var v=l[u];if(!rt(v)){var f=v.match(iv);if(f){var c=f[1],h=f[2]?f[2]:null,d=t(e,v,c,h,n,i,s);if(d===!1)break}}}},lv=function(e,t){return e.addEventFields(e.context,t),new nv(t.type,t)},jg=function(e,t,a){if(ic(a)){t(e,a);return}else if(Me(a)){t(e,lv(e,a));return}for(var n=_e(a)?a:a.split(/\s+/),i=0;i<n.length;i++){var s=n[i];if(!rt(s)){var o=s.match(iv);if(o){var l=o[1],u=o[2]?o[2]:null,v=lv(e,{type:l,namespace:u,target:e.context});t(e,v)}}}};ht.on=ht.addListener=function(r,e,t,a,n){return uv(this,function(i,s,o,l,u,v,f){Ue(v)&&i.listeners.push({event:s,callback:v,type:o,namespace:l,qualifier:u,conf:f})},r,e,t,a,n),this},ht.one=function(r,e,t,a){return this.on(r,e,t,a,{one:!0})},ht.removeListener=ht.off=function(r,e,t,a){var n=this;this.emitting!==0&&(this.listeners=_c(this.listeners));for(var i=this.listeners,s=function(u){var v=i[u];uv(n,function(f,c,h,d,y,g){if((v.type===h||r==="*")&&(!d&&v.namespace!==".*"||v.namespace===d)&&(!y||f.qualifierCompare(v.qualifier,y))&&(!g||v.callback===g))return i.splice(u,1),!1},r,e,t,a)},o=i.length-1;o>=0;o--)s(o);return this},ht.removeAllListeners=function(){return this.removeListener("*")},ht.emit=ht.trigger=function(r,e,t){var a=this.listeners,n=a.length;return this.emitting++,_e(e)||(e=[e]),jg(this,function(i,s){t!=null&&(a=[{event:s.event,type:s.type,namespace:s.namespace,callback:t}],n=a.length);for(var o=function(){var v=a[l];if(v.type===s.type&&(!v.namespace||v.namespace===s.namespace||v.namespace===Qg)&&i.eventMatches(i.context,v,s)){var f=[s];e!=null&&Hc(f,e),i.beforeEmit(i.context,v,s),v.conf&&v.conf.one&&(i.listeners=i.listeners.filter(function(d){return d!==v}));var c=i.callbackContext(i.context,v,s),h=v.callback.apply(c,f);i.afterEmit(i.context,v,s),h===!1&&(s.stopPropagation(),s.preventDefault())}},l=0;l<n;l++)o();i.bubble(i.context)&&!s.isPropagationStopped()&&i.parent(i.context).emit(s,e)},r),this.emitting--,this};var ep={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&ua(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e.cy(),t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e},beforeEmit:function(e,t){t.conf&&t.conf.once&&t.conf.onceCollection.removeListener(t.event,t.qualifier,t.callback)},bubble:function(){return!0},parent:function(e){return e.isChild()?e.parent():e.cy()}},En=function(e){return he(e)?new lt(e):e},vv={createEmitter:function(){for(var e=0;e<this.length;e++){var t=this[e],a=t._private;a.emitter||(a.emitter=new xn(ep,t))}return this},emitter:function(){return this._private.emitter},on:function(e,t,a){for(var n=En(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a)}return this},removeListener:function(e,t,a){for(var n=En(t),i=0;i<this.length;i++){var s=this[i];s.emitter().removeListener(e,n,a)}return this},removeAllListeners:function(){for(var e=0;e<this.length;e++){var t=this[e];t.emitter().removeAllListeners()}return this},one:function(e,t,a){for(var n=En(t),i=0;i<this.length;i++){var s=this[i];s.emitter().one(e,n,a)}return this},once:function(e,t,a){for(var n=En(t),i=0;i<this.length;i++){var s=this[i];s.emitter().on(e,n,a,{once:!0,onceCollection:this})}},emit:function(e,t){for(var a=0;a<this.length;a++){var n=this[a];n.emitter().emit(e,t)}return this},emitAndNotify:function(e,t){if(this.length!==0)return this.cy().notify(e,this),this.emit(e,t),this}};Ne.eventAliasesOn(vv);var fv={nodes:function(e){return this.filter(function(t){return t.isNode()}).filter(e)},edges:function(e){return this.filter(function(t){return t.isEdge()}).filter(e)},byGroup:function(){for(var e=this.spawn(),t=this.spawn(),a=0;a<this.length;a++){var n=this[a];n.isNode()?e.push(n):t.push(n)}return{nodes:e,edges:t}},filter:function(e,t){if(e===void 0)return this;if(he(e)||Dr(e))return new lt(e).filter(this);if(Ue(e)){for(var a=this.spawn(),n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);o&&a.push(s)}return a}return this.spawn()},not:function(e){if(e){he(e)&&(e=this.filter(e));for(var t=this.spawn(),a=0;a<this.length;a++){var n=this[a],i=e.has(n);i||t.push(n)}return t}else return this},absoluteComplement:function(){var e=this.cy();return e.mutableElements().not(this)},intersect:function(e){if(he(e)){var t=e;return this.filter(t)}for(var a=this.spawn(),n=this,i=e,s=this.length<e.length,o=s?n:i,l=s?i:n,u=0;u<o.length;u++){var v=o[u];l.has(v)&&a.push(v)}return a},xor:function(e){var t=this._private.cy;he(e)&&(e=t.$(e));var a=this.spawn(),n=this,i=e,s=function(l,u){for(var v=0;v<l.length;v++){var f=l[v],c=f._private.data.id,h=u.hasElementWithId(c);h||a.push(f)}};return s(n,i),s(i,n),a},diff:function(e){var t=this._private.cy;he(e)&&(e=t.$(e));var a=this.spawn(),n=this.spawn(),i=this.spawn(),s=this,o=e,l=function(v,f,c){for(var h=0;h<v.length;h++){var d=v[h],y=d._private.data.id,g=f.hasElementWithId(y);g?i.merge(d):c.push(d)}};return l(s,o,a),l(o,s,n),{left:a,right:n,both:i}},add:function(e){var t=this._private.cy;if(!e)return this;if(he(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=this.spawnSelf(),i=0;i<e.length;i++){var s=e[i],o=!this.has(s);o&&n.push(s)}return n},merge:function(e){var t=this._private,a=t.cy;if(!e)return this;if(e&&he(e)){var n=e;e=a.mutableElements().filter(n)}for(var i=t.map,s=0;s<e.length;s++){var o=e[s],l=o._private.data.id,u=!i.has(l);if(u){var v=this.length++;this[v]=o,i.set(l,{ele:o,index:v})}}return this},unmergeAt:function(e){var t=this[e],a=t.id(),n=this._private,i=n.map;this[e]=void 0,i.delete(a);var s=e===this.length-1;if(this.length>1&&!s){var o=this.length-1,l=this[o],u=l._private.data.id;this[o]=void 0,this[e]=l,i.set(u,{ele:l,index:e})}return this.length--,this},unmergeOne:function(e){e=e[0];var t=this._private,a=e._private.data.id,n=t.map,i=n.get(a);if(!i)return this;var s=i.index;return this.unmergeAt(s),this},unmerge:function(e){var t=this._private.cy;if(!e)return this;if(e&&he(e)){var a=e;e=t.mutableElements().filter(a)}for(var n=0;n<e.length;n++)this.unmergeOne(e[n]);return this},unmergeBy:function(e){for(var t=this.length-1;t>=0;t--){var a=this[t];e(a)&&this.unmergeAt(t)}return this},map:function(e,t){for(var a=[],n=this,i=0;i<n.length;i++){var s=n[i],o=t?e.apply(t,[s,i,n]):e(s,i,n);a.push(o)}return a},reduce:function(e,t){for(var a=t,n=this,i=0;i<n.length;i++)a=e(a,n[i],i,n);return a},max:function(e,t){for(var a=-1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l>a&&(a=l,n=o)}return{value:a,ele:n}},min:function(e,t){for(var a=1/0,n,i=this,s=0;s<i.length;s++){var o=i[s],l=t?e.apply(t,[o,s,i]):e(o,s,i);l<a&&(a=l,n=o)}return{value:a,ele:n}}},Ie=fv;Ie.u=Ie["|"]=Ie["+"]=Ie.union=Ie.or=Ie.add,Ie["\\"]=Ie["!"]=Ie["-"]=Ie.difference=Ie.relativeComplement=Ie.subtract=Ie.not,Ie.n=Ie["&"]=Ie["."]=Ie.and=Ie.intersection=Ie.intersect,Ie["^"]=Ie["(+)"]=Ie["(-)"]=Ie.symmetricDifference=Ie.symdiff=Ie.xor,Ie.fnFilter=Ie.filterFn=Ie.stdFilter=Ie.filter,Ie.complement=Ie.abscomp=Ie.absoluteComplement;var rp={isNode:function(){return this.group()==="nodes"},isEdge:function(){return this.group()==="edges"},isLoop:function(){return this.isEdge()&&this.source()[0]===this.target()[0]},isSimple:function(){return this.isEdge()&&this.source()[0]!==this.target()[0]},group:function(){var e=this[0];if(e)return e._private.group}},cv=function(e,t){var a=e.cy(),n=a.hasCompoundNodes();function i(v){var f=v.pstyle("z-compound-depth");return f.value==="auto"?n?v.zDepth():0:f.value==="bottom"?-1:f.value==="top"?pi:0}var s=i(e)-i(t);if(s!==0)return s;function o(v){var f=v.pstyle("z-index-compare");return f.value==="auto"&&v.isNode()?1:0}var l=o(e)-o(t);if(l!==0)return l;var u=e.pstyle("z-index").value-t.pstyle("z-index").value;return u!==0?u:e.poolIndex()-t.poolIndex()},Cn={forEach:function(e,t){if(Ue(e))for(var a=this.length,n=0;n<a;n++){var i=this[n],s=t?e.apply(t,[i,n,this]):e(i,n,this);if(s===!1)break}return this},toArray:function(){for(var e=[],t=0;t<this.length;t++)e.push(this[t]);return e},slice:function(e,t){var a=[],n=this.length;t==null&&(t=n),e==null&&(e=0),e<0&&(e=n+e),t<0&&(t=n+t);for(var i=e;i>=0&&i<t&&i<n;i++)a.push(this[i]);return this.spawn(a)},size:function(){return this.length},eq:function(e){return this[e]||this.spawn()},first:function(){return this[0]||this.spawn()},last:function(){return this[this.length-1]||this.spawn()},empty:function(){return this.length===0},nonempty:function(){return!this.empty()},sort:function(e){if(!Ue(e))return this;var t=this.toArray().sort(e);return this.spawn(t)},sortByZIndex:function(){return this.sort(cv)},zDepth:function(){var e=this[0];if(e){var t=e._private,a=t.group;if(a==="nodes"){var n=t.data.parent?e.parents().size():0;return e.isParent()?n:pi-1}else{var i=t.source,s=t.target,o=i.zDepth(),l=s.zDepth();return Math.max(o,l,0)}}}};Cn.each=Cn.forEach;var tp=function(){var e="undefined",t=(typeof Symbol=="undefined"?"undefined":je(Symbol))!=e&&je(Symbol.iterator)!=e;t&&(Cn[Symbol.iterator]=function(){var a=this,n={value:void 0,done:!1},i=0,s=this.length;return wo({next:function(){return i<s?n.value=a[i++]:(n.value=void 0,n.done=!0),n}},Symbol.iterator,function(){return this})})};tp();var ap=lr({nodeDimensionsIncludeLabels:!1}),Tn={layoutDimensions:function(e){e=ap(e);var t;if(!this.takesUpSpace())t={w:0,h:0};else if(e.nodeDimensionsIncludeLabels){var a=this.boundingBox();t={w:a.w,h:a.h}}else t={w:this.outerWidth(),h:this.outerHeight()};return(t.w===0||t.h===0)&&(t.w=t.h=1),t},layoutPositions:function(e,t,a){var n=this.nodes().filter(function(E){return!E.isParent()}),i=this.cy(),s=t.eles,o=function(C){return C.id()},l=Ot(a,o);e.emit({type:"layoutstart",layout:e}),e.animations=[];var u=function(C,x,T){var k={x:x.x1+x.w/2,y:x.y1+x.h/2},D={x:(T.x-k.x)*C,y:(T.y-k.y)*C};return{x:k.x+D.x,y:k.y+D.y}},v=t.spacingFactor&&t.spacingFactor!==1,f=function(){if(!v)return null;for(var C=mr(),x=0;x<n.length;x++){var T=n[x],k=l(T,x);uu(C,k.x,k.y)}return C},c=f(),h=Ot(function(E,C){var x=l(E,C);if(v){var T=Math.abs(t.spacingFactor);x=u(T,c,x)}return t.transform!=null&&(x=t.transform(E,x)),x},o);if(t.animate){for(var d=0;d<n.length;d++){var y=n[d],g=h(y,d),p=t.animateFilter==null||t.animateFilter(y,d);if(p){var m=y.animation({position:g,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(m)}else y.position(g)}if(t.fit){var b=i.animation({fit:{boundingBox:s.boundingBoxAt(h),padding:t.padding},duration:t.animationDuration,easing:t.animationEasing});e.animations.push(b)}else if(t.zoom!==void 0&&t.pan!==void 0){var w=i.animation({zoom:t.zoom,pan:t.pan,duration:t.animationDuration,easing:t.animationEasing});e.animations.push(w)}e.animations.forEach(function(E){return E.play()}),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),Wt.all(e.animations.map(function(E){return E.promise()})).then(function(){e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e})})}else n.positions(h),t.fit&&i.fit(t.eles,t.padding),t.zoom!=null&&i.zoom(t.zoom),t.pan&&i.pan(t.pan),e.one("layoutready",t.ready),e.emit({type:"layoutready",layout:e}),e.one("layoutstop",t.stop),e.emit({type:"layoutstop",layout:e});return this},layout:function(e){var t=this.cy();return t.makeLayout(me({},e,{eles:this}))}};Tn.createLayout=Tn.makeLayout=Tn.layout;function dv(r,e,t){var a=t._private,n=a.styleCache=a.styleCache||[],i;return(i=n[r])!=null||(i=n[r]=e(t)),i}function Sn(r,e){return r=wt(r),function(a){return dv(r,e,a)}}function kn(r,e){r=wt(r);var t=function(n){return e.call(n)};return function(){var n=this[0];if(n)return dv(r,t,n)}}var vr={recalculateRenderedStyle:function(e){var t=this.cy(),a=t.renderer(),n=t.styleEnabled();return a&&n&&a.recalculateRenderedStyle(this,e),this},dirtyStyleCache:function(){var e=this.cy(),t=function(i){return i._private.styleCache=null};if(e.hasCompoundNodes()){var a;a=this.spawnSelf().merge(this.descendants()).merge(this.parents()),a.merge(a.connectedEdges()),a.forEach(t)}else this.forEach(function(n){t(n),n.connectedEdges().forEach(t)});return this},updateStyle:function(e){var t=this._private.cy;if(!t.styleEnabled())return this;if(t.batching()){var a=t._private.batchStyleEles;return a.merge(this),this}var n=t.hasCompoundNodes(),i=this;e=!!(e||e===void 0),n&&(i=this.spawnSelf().merge(this.descendants()).merge(this.parents()));var s=i;return e?s.emitAndNotify("style"):s.emit("style"),i.forEach(function(o){return o._private.styleDirty=!0}),this},cleanStyle:function(){var e=this.cy();if(e.styleEnabled())for(var t=0;t<this.length;t++){var a=this[t];a._private.styleDirty&&(a._private.styleDirty=!1,e.style().apply(a))}},parsedStyle:function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,a=this[0],n=a.cy();if(n.styleEnabled()&&a){a._private.styleDirty&&(a._private.styleDirty=!1,n.style().apply(a));var i=a._private.style[e];return i!=null?i:t?n.style().getDefaultProperty(e):null}},numericStyle:function(e){var t=this[0];if(t.cy().styleEnabled()&&t){var a=t.pstyle(e);return a.pfValue!==void 0?a.pfValue:a.value}},numericStyleUnits:function(e){var t=this[0];if(t.cy().styleEnabled()&&t)return t.pstyle(e).units},renderedStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=this[0];if(a)return t.style().getRenderedStyle(a,e)},style:function(e,t){var a=this.cy();if(!a.styleEnabled())return this;var n=!1,i=a.style();if(Me(e)){var s=e;i.applyBypass(this,s,n),this.emitAndNotify("style")}else if(he(e))if(t===void 0){var o=this[0];return o?i.getStylePropertyValue(o,e):void 0}else i.applyBypass(this,e,t,n),this.emitAndNotify("style");else if(e===void 0){var l=this[0];return l?i.getRawStyle(l):void 0}return this},removeStyle:function(e){var t=this.cy();if(!t.styleEnabled())return this;var a=!1,n=t.style(),i=this;if(e===void 0)for(var s=0;s<i.length;s++){var o=i[s];n.removeAllBypasses(o,a)}else{e=e.split(/\s+/);for(var l=0;l<i.length;l++){var u=i[l];n.removeBypasses(u,e,a)}}return this.emitAndNotify("style"),this},show:function(){return this.css("display","element"),this},hide:function(){return this.css("display","none"),this},effectiveOpacity:function(){var e=this.cy();if(!e.styleEnabled())return 1;var t=e.hasCompoundNodes(),a=this[0];if(a){var n=a._private,i=a.pstyle("opacity").value;if(!t)return i;var s=n.data.parent?a.parents():null;if(s)for(var o=0;o<s.length;o++){var l=s[o],u=l.pstyle("opacity").value;i=u*i}return i}},transparent:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0],a=t.cy().hasCompoundNodes();if(t)return a?t.effectiveOpacity()===0:t.pstyle("opacity").value===0},backgrounding:function(){var e=this.cy();if(!e.styleEnabled())return!1;var t=this[0];return!!t._private.backgrounding}};function _s(r,e){var t=r._private,a=t.data.parent?r.parents():null;if(a)for(var n=0;n<a.length;n++){var i=a[n];if(!e(i))return!1}return!0}function Gs(r){var e=r.ok,t=r.edgeOkViaNode||r.ok,a=r.parentOk||r.ok;return function(){var n=this.cy();if(!n.styleEnabled())return!0;var i=this[0],s=n.hasCompoundNodes();if(i){var o=i._private;if(!e(i))return!1;if(i.isNode())return!s||_s(i,a);var l=o.source,u=o.target;return t(l)&&(!s||_s(l,t))&&(l===u||t(u)&&(!s||_s(u,t)))}}}var Yt=Sn("eleTakesUpSpace",function(r){return r.pstyle("display").value==="element"&&r.width()!==0&&(r.isNode()?r.height()!==0:!0)});vr.takesUpSpace=kn("takesUpSpace",Gs({ok:Yt}));var np=Sn("eleInteractive",function(r){return r.pstyle("events").value==="yes"&&r.pstyle("visibility").value==="visible"&&Yt(r)}),ip=Sn("parentInteractive",function(r){return r.pstyle("visibility").value==="visible"&&Yt(r)});vr.interactive=kn("interactive",Gs({ok:np,parentOk:ip,edgeOkViaNode:Yt})),vr.noninteractive=function(){var r=this[0];if(r)return!r.interactive()};var sp=Sn("eleVisible",function(r){return r.pstyle("visibility").value==="visible"&&r.pstyle("opacity").pfValue!==0&&Yt(r)}),op=Yt;vr.visible=kn("visible",Gs({ok:sp,edgeOkViaNode:op})),vr.hidden=function(){var r=this[0];if(r)return!r.visible()},vr.isBundledBezier=kn("isBundledBezier",function(){return this.cy().styleEnabled()?!this.removed()&&this.pstyle("curve-style").value==="bezier"&&this.takesUpSpace():!1}),vr.bypass=vr.css=vr.style,vr.renderedCss=vr.renderedStyle,vr.removeBypass=vr.removeCss=vr.removeStyle,vr.pstyle=vr.parsedStyle;var gt={};function hv(r){return function(){var e=arguments,t=[];if(e.length===2){var a=e[0],n=e[1];this.on(r.event,a,n)}else if(e.length===1&&Ue(e[0])){var i=e[0];this.on(r.event,i)}else if(e.length===0||e.length===1&&_e(e[0])){for(var s=e.length===1?e[0]:null,o=0;o<this.length;o++){var l=this[o],u=!r.ableField||l._private[r.ableField],v=l._private[r.field]!=r.value;if(r.overrideAble){var f=r.overrideAble(l);if(f!==void 0&&(u=f,!f))return this}u&&(l._private[r.field]=r.value,v&&t.push(l))}var c=this.spawn(t);c.updateStyle(),c.emit(r.event),s&&c.emit(s)}return this}}function Zt(r){gt[r.field]=function(){var e=this[0];if(e){if(r.overrideField){var t=r.overrideField(e);if(t!==void 0)return t}return e._private[r.field]}},gt[r.on]=hv({event:r.on,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!0}),gt[r.off]=hv({event:r.off,field:r.field,ableField:r.ableField,overrideAble:r.overrideAble,value:!1})}Zt({field:"locked",overrideField:function(e){return e.cy().autolock()?!0:void 0},on:"lock",off:"unlock"}),Zt({field:"grabbable",overrideField:function(e){return e.cy().autoungrabify()||e.pannable()?!1:void 0},on:"grabify",off:"ungrabify"}),Zt({field:"selected",ableField:"selectable",overrideAble:function(e){return e.cy().autounselectify()?!1:void 0},on:"select",off:"unselect"}),Zt({field:"selectable",overrideField:function(e){return e.cy().autounselectify()?!1:void 0},on:"selectify",off:"unselectify"}),gt.deselect=gt.unselect,gt.grabbed=function(){var r=this[0];if(r)return r._private.grabbed},Zt({field:"active",on:"activate",off:"unactivate"}),Zt({field:"pannable",on:"panify",off:"unpanify"}),gt.inactive=function(){var r=this[0];if(r)return!r._private.active};var hr={},gv=function(e){return function(a){for(var n=this,i=[],s=0;s<n.length;s++){var o=n[s];if(o.isNode()){for(var l=!1,u=o.connectedEdges(),v=0;v<u.length;v++){var f=u[v],c=f.source(),h=f.target();if(e.noIncomingEdges&&h===o&&c!==o||e.noOutgoingEdges&&c===o&&h!==o){l=!0;break}}l||i.push(o)}}return this.spawn(i,!0).filter(a)}},pv=function(e){return function(t){for(var a=this,n=[],i=0;i<a.length;i++){var s=a[i];if(s.isNode())for(var o=s.connectedEdges(),l=0;l<o.length;l++){var u=o[l],v=u.source(),f=u.target();e.outgoing&&v===s?(n.push(u),n.push(f)):e.incoming&&f===s&&(n.push(u),n.push(v))}}return this.spawn(n,!0).filter(t)}},yv=function(e){return function(t){for(var a=this,n=[],i={};;){var s=e.outgoing?a.outgoers():a.incomers();if(s.length===0)break;for(var o=!1,l=0;l<s.length;l++){var u=s[l],v=u.id();i[v]||(i[v]=!0,n.push(u),o=!0)}if(!o)break;a=s}return this.spawn(n,!0).filter(t)}};hr.clearTraversalCache=function(){for(var r=0;r<this.length;r++)this[r]._private.traversalCache=null},me(hr,{roots:gv({noIncomingEdges:!0}),leaves:gv({noOutgoingEdges:!0}),outgoers:Br(pv({outgoing:!0}),"outgoers"),successors:yv({outgoing:!0}),incomers:Br(pv({incoming:!0}),"incomers"),predecessors:yv({})}),me(hr,{neighborhood:Br(function(r){for(var e=[],t=this.nodes(),a=0;a<t.length;a++)for(var n=t[a],i=n.connectedEdges(),s=0;s<i.length;s++){var o=i[s],l=o.source(),u=o.target(),v=n===l?u:l;v.length>0&&e.push(v[0]),e.push(o[0])}return this.spawn(e,!0).filter(r)},"neighborhood"),closedNeighborhood:function(e){return this.neighborhood().add(this).filter(e)},openNeighborhood:function(e){return this.neighborhood(e)}}),hr.neighbourhood=hr.neighborhood,hr.closedNeighbourhood=hr.closedNeighborhood,hr.openNeighbourhood=hr.openNeighborhood,me(hr,{source:Br(function(e){var t=this[0],a;return t&&(a=t._private.source||t.cy().collection()),a&&e?a.filter(e):a},"source"),target:Br(function(e){var t=this[0],a;return t&&(a=t._private.target||t.cy().collection()),a&&e?a.filter(e):a},"target"),sources:mv({attr:"source"}),targets:mv({attr:"target"})});function mv(r){return function(t){for(var a=[],n=0;n<this.length;n++){var i=this[n],s=i._private[r.attr];s&&a.push(s)}return this.spawn(a,!0).filter(t)}}me(hr,{edgesWith:Br(bv(),"edgesWith"),edgesTo:Br(bv({thisIsSrc:!0}),"edgesTo")});function bv(r){return function(t){var a=[],n=this._private.cy,i=r||{};he(t)&&(t=n.$(t));for(var s=0;s<t.length;s++)for(var o=t[s]._private.edges,l=0;l<o.length;l++){var u=o[l],v=u._private.data,f=this.hasElementWithId(v.source)&&t.hasElementWithId(v.target),c=t.hasElementWithId(v.source)&&this.hasElementWithId(v.target),h=f||c;h&&((i.thisIsSrc||i.thisIsTgt)&&(i.thisIsSrc&&!f||i.thisIsTgt&&!c)||a.push(u))}return this.spawn(a,!0)}}me(hr,{connectedEdges:Br(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];if(n.isNode())for(var i=n._private.edges,s=0;s<i.length;s++){var o=i[s];e.push(o)}}return this.spawn(e,!0).filter(r)},"connectedEdges"),connectedNodes:Br(function(r){for(var e=[],t=this,a=0;a<t.length;a++){var n=t[a];n.isEdge()&&(e.push(n.source()[0]),e.push(n.target()[0]))}return this.spawn(e,!0).filter(r)},"connectedNodes"),parallelEdges:Br(wv(),"parallelEdges"),codirectedEdges:Br(wv({codirected:!0}),"codirectedEdges")});function wv(r){var e={codirected:!1};return r=me({},e,r),function(a){for(var n=[],i=this.edges(),s=r,o=0;o<i.length;o++)for(var l=i[o],u=l._private,v=u.source,f=v._private.data.id,c=u.data.target,h=v._private.edges,d=0;d<h.length;d++){var y=h[d],g=y._private.data,p=g.target,m=g.source,b=p===c&&m===f,w=f===p&&c===m;(s.codirected&&b||!s.codirected&&(b||w))&&n.push(y)}return this.spawn(n,!0).filter(a)}}me(hr,{components:function(e){var t=this,a=t.cy(),n=a.collection(),i=e==null?t.nodes():e.nodes(),s=[];e!=null&&i.empty()&&(i=e.sources());var o=function(v,f){n.merge(v),i.unmerge(v),f.merge(v)};if(i.empty())return t.spawn();var l=function(){var v=a.collection();s.push(v);var f=i[0];o(f,v),t.bfs({directed:!1,roots:f,visit:function(h){return o(h,v)}}),v.forEach(function(c){c.connectedEdges().forEach(function(h){t.has(h)&&v.has(h.source())&&v.has(h.target())&&v.merge(h)})})};do l();while(i.length>0);return s},component:function(){var e=this[0];return e.cy().mutableElements().components(e)[0]}}),hr.componentsOf=hr.components;var fr=function(e,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e===void 0){$e("A collection must have a reference to the core");return}var i=new Ur,s=!1;if(!t)t=[];else if(t.length>0&&Me(t[0])&&!ua(t[0])){s=!0;for(var o=[],l=new zt,u=0,v=t.length;u<v;u++){var f=t[u];f.data==null&&(f.data={});var c=f.data;if(c.id==null)c.id=tu();else if(e.hasElementWithId(c.id)||l.has(c.id))continue;var h=new ja(e,f,!1);o.push(h),l.add(c.id)}t=o}this.length=0;for(var d=0,y=t.length;d<y;d++){var g=t[d][0];if(g!=null){var p=g._private.data.id;(!a||!i.has(p))&&(a&&i.set(p,{index:this.length,ele:g}),this[this.length]=g,this.length++)}}this._private={eles:this,cy:e,get map(){return this.lazyMap==null&&this.rebuildMap(),this.lazyMap},set map(m){this.lazyMap=m},rebuildMap:function(){for(var b=this.lazyMap=new Ur,w=this.eles,E=0;E<w.length;E++){var C=w[E];b.set(C.id(),{index:E,ele:C})}}},a&&(this._private.map=i),s&&!n&&this.restore()},Ge=ja.prototype=fr.prototype=Object.create(Array.prototype);Ge.instanceString=function(){return"collection"},Ge.spawn=function(r,e){return new fr(this.cy(),r,e)},Ge.spawnSelf=function(){return this.spawn(this)},Ge.cy=function(){return this._private.cy},Ge.renderer=function(){return this._private.cy.renderer()},Ge.element=function(){return this[0]},Ge.collection=function(){return To(this)?this:new fr(this._private.cy,[this])},Ge.unique=function(){return new fr(this._private.cy,this,!0)},Ge.hasElementWithId=function(r){return r=""+r,this._private.map.has(r)},Ge.getElementById=function(r){r=""+r;var e=this._private.cy,t=this._private.map.get(r);return t?t.ele:new fr(e)},Ge.$id=Ge.getElementById,Ge.poolIndex=function(){var r=this._private.cy,e=r._private.elements,t=this[0]._private.data.id;return e._private.map.get(t).index},Ge.indexOf=function(r){var e=r[0]._private.data.id;return this._private.map.get(e).index},Ge.indexOfId=function(r){return r=""+r,this._private.map.get(r).index},Ge.json=function(r){var e=this.element(),t=this.cy();if(e==null&&r)return this;if(e!=null){var a=e._private;if(Me(r)){if(t.startBatch(),r.data){e.data(r.data);var n=a.data;if(e.isEdge()){var i=!1,s={},o=r.data.source,l=r.data.target;o!=null&&o!=n.source&&(s.source=""+o,i=!0),l!=null&&l!=n.target&&(s.target=""+l,i=!0),i&&(e=e.move(s))}else{var u="parent"in r.data,v=r.data.parent;u&&(v!=null||n.parent!=null)&&v!=n.parent&&(v===void 0&&(v=null),v!=null&&(v=""+v),e=e.move({parent:v}))}}r.position&&e.position(r.position);var f=function(y,g,p){var m=r[y];m!=null&&m!==a[y]&&(m?e[g]():e[p]())};return f("removed","remove","restore"),f("selected","select","unselect"),f("selectable","selectify","unselectify"),f("locked","lock","unlock"),f("grabbable","grabify","ungrabify"),f("pannable","panify","unpanify"),r.classes!=null&&e.classes(r.classes),t.endBatch(),this}else if(r===void 0){var c={data:zr(a.data),position:zr(a.position),group:a.group,removed:a.removed,selected:a.selected,selectable:a.selectable,locked:a.locked,grabbable:a.grabbable,pannable:a.pannable,classes:null};c.classes="";var h=0;return a.classes.forEach(function(d){return c.classes+=h++===0?d:" "+d}),c}}},Ge.jsons=function(){for(var r=[],e=0;e<this.length;e++){var t=this[e],a=t.json();r.push(a)}return r},Ge.clone=function(){for(var r=this.cy(),e=[],t=0;t<this.length;t++){var a=this[t],n=a.json(),i=new ja(r,n,!1);e.push(i)}return new fr(r,e)},Ge.copy=Ge.clone,Ge.restore=function(){for(var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=t.cy(),n=a._private,i=[],s=[],o,l=0,u=t.length;l<u;l++){var v=t[l];e&&!v.removed()||(v.isNode()?i.push(v):s.push(v))}o=i.concat(s);var f,c=function(){o.splice(f,1),f--};for(f=0;f<o.length;f++){var h=o[f],d=h._private,y=d.data;if(h.clearTraversalCache(),!(!e&&!d.removed)){if(y.id===void 0)y.id=tu();else if(te(y.id))y.id=""+y.id;else if(rt(y.id)||!he(y.id)){$e("Can not create element with invalid string ID `"+y.id+"`"),c();continue}else if(a.hasElementWithId(y.id)){$e("Can not create second element with ID `"+y.id+"`"),c();continue}}var g=y.id;if(h.isNode()){var p=d.position;p.x==null&&(p.x=0),p.y==null&&(p.y=0)}if(h.isEdge()){for(var m=h,b=["source","target"],w=b.length,E=!1,C=0;C<w;C++){var x=b[C],T=y[x];te(T)&&(T=y[x]=""+y[x]),T==null||T===""?($e("Can not create edge `"+g+"` with unspecified "+x),E=!0):a.hasElementWithId(T)||($e("Can not create edge `"+g+"` with nonexistant "+x+" `"+T+"`"),E=!0)}if(E){c();continue}var k=a.getElementById(y.source),D=a.getElementById(y.target);k.same(D)?k._private.edges.push(m):(k._private.edges.push(m),D._private.edges.push(m)),m._private.source=k,m._private.target=D}d.map=new Ur,d.map.set(g,{ele:h,index:0}),d.removed=!1,e&&a.addToPool(h)}for(var B=0;B<i.length;B++){var P=i[B],A=P._private.data;te(A.parent)&&(A.parent=""+A.parent);var R=A.parent,L=R!=null;if(L||P._private.parent){var I=P._private.parent?a.collection().merge(P._private.parent):a.getElementById(R);if(I.empty())A.parent=void 0;else if(I[0].removed())Oe("Node added with missing parent, reference to parent removed"),A.parent=void 0,P._private.parent=null;else{for(var M=!1,O=I;!O.empty();){if(P.same(O)){M=!0,A.parent=void 0;break}O=O.parent()}M||(I[0]._private.children.push(P),P._private.parent=I[0],n.hasCompoundNodes=!0)}}}if(o.length>0){for(var V=o.length===t.length?t:new fr(a,o),G=0;G<V.length;G++){var N=V[G];N.isNode()||(N.parallelEdges().clearTraversalCache(),N.source().clearTraversalCache(),N.target().clearTraversalCache())}var F;n.hasCompoundNodes?F=a.collection().merge(V).merge(V.connectedNodes()).merge(V.parent()):F=V,F.dirtyCompoundBoundsCache().dirtyBoundingBoxCache().updateStyle(r),r?V.emitAndNotify("add"):e&&V.emit("add")}return t},Ge.removed=function(){var r=this[0];return r&&r._private.removed},Ge.inside=function(){var r=this[0];return r&&!r._private.removed},Ge.remove=function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t=this,a=[],n={},i=t._private.cy;function s(R){for(var L=R._private.edges,I=0;I<L.length;I++)l(L[I])}function o(R){for(var L=R._private.children,I=0;I<L.length;I++)l(L[I])}function l(R){var L=n[R.id()];e&&R.removed()||L||(n[R.id()]=!0,R.isNode()?(a.push(R),s(R),o(R)):a.unshift(R))}for(var u=0,v=t.length;u<v;u++){var f=t[u];l(f)}function c(R,L){var I=R._private.edges;nt(I,L),R.clearTraversalCache()}function h(R){R.clearTraversalCache()}var d=[];d.ids={};function y(R,L){L=L[0],R=R[0];var I=R._private.children,M=R.id();nt(I,L),L._private.parent=null,d.ids[M]||(d.ids[M]=!0,d.push(R))}t.dirtyCompoundBoundsCache(),e&&i.removeFromPool(a);for(var g=0;g<a.length;g++){var p=a[g];if(p.isEdge()){var m=p.source()[0],b=p.target()[0];c(m,p),c(b,p);for(var w=p.parallelEdges(),E=0;E<w.length;E++){var C=w[E];h(C),C.isBundledBezier()&&C.dirtyBoundingBoxCache()}}else{var x=p.parent();x.length!==0&&y(x,p)}e&&(p._private.removed=!0)}var T=i._private.elements;i._private.hasCompoundNodes=!1;for(var k=0;k<T.length;k++){var D=T[k];if(D.isParent()){i._private.hasCompoundNodes=!0;break}}var B=new fr(this.cy(),a);B.size()>0&&(r?B.emitAndNotify("remove"):e&&B.emit("remove"));for(var P=0;P<d.length;P++){var A=d[P];(!e||!A.removed())&&A.updateStyle()}return B},Ge.move=function(r){var e=this._private.cy,t=this,a=!1,n=!1,i=function(d){return d==null?d:""+d};if(r.source!==void 0||r.target!==void 0){var s=i(r.source),o=i(r.target),l=s!=null&&e.hasElementWithId(s),u=o!=null&&e.hasElementWithId(o);(l||u)&&(e.batch(function(){t.remove(a,n),t.emitAndNotify("moveout");for(var h=0;h<t.length;h++){var d=t[h],y=d._private.data;d.isEdge()&&(l&&(y.source=s),u&&(y.target=o))}t.restore(a,n)}),t.emitAndNotify("move"))}else if(r.parent!==void 0){var v=i(r.parent),f=v===null||e.hasElementWithId(v);if(f){var c=v===null?void 0:v;e.batch(function(){var h=t.remove(a,n);h.emitAndNotify("moveout");for(var d=0;d<t.length;d++){var y=t[d],g=y._private.data;y.isNode()&&(g.parent=c)}h.restore(a,n)}),t.emitAndNotify("move")}}return this},[Ru,mg,yn,ft,Ut,Ig,mn,Zg,vv,fv,rp,Cn,Tn,vr,gt,hr].forEach(function(r){me(Ge,r)});var up={add:function(e){var t,a=this;if(Dr(e)){var n=e;if(n._private.cy===a)t=n.restore();else{for(var i=[],s=0;s<n.length;s++){var o=n[s];i.push(o.json())}t=new fr(a,i)}}else if(_e(e)){var l=e;t=new fr(a,l)}else if(Me(e)&&(_e(e.nodes)||_e(e.edges))){for(var u=e,v=[],f=["nodes","edges"],c=0,h=f.length;c<h;c++){var d=f[c],y=u[d];if(_e(y))for(var g=0,p=y.length;g<p;g++){var m=me({group:d},y[g]);v.push(m)}}t=new fr(a,v)}else{var b=e;t=new ja(a,b).collection()}return t},remove:function(e){if(!Dr(e)){if(he(e)){var t=e;e=this.$(t)}}return e.remove()}};function lp(r,e,t,a){var n=4,i=.001,s=1e-7,o=10,l=11,u=1/(l-1),v=typeof Float32Array!="undefined";if(arguments.length!==4)return!1;for(var f=0;f<4;++f)if(typeof arguments[f]!="number"||isNaN(arguments[f])||!isFinite(arguments[f]))return!1;r=Math.min(r,1),t=Math.min(t,1),r=Math.max(r,0),t=Math.max(t,0);var c=v?new Float32Array(l):new Array(l);function h(D,B){return 1-3*B+3*D}function d(D,B){return 3*B-6*D}function y(D){return 3*D}function g(D,B,P){return((h(B,P)*D+d(B,P))*D+y(B))*D}function p(D,B,P){return 3*h(B,P)*D*D+2*d(B,P)*D+y(B)}function m(D,B){for(var P=0;P<n;++P){var A=p(B,r,t);if(A===0)return B;var R=g(B,r,t)-D;B-=R/A}return B}function b(){for(var D=0;D<l;++D)c[D]=g(D*u,r,t)}function w(D,B,P){var A,R,L=0;do R=B+(P-B)/2,A=g(R,r,t)-D,A>0?P=R:B=R;while(Math.abs(A)>s&&++L<o);return R}function E(D){for(var B=0,P=1,A=l-1;P!==A&&c[P]<=D;++P)B+=u;--P;var R=(D-c[P])/(c[P+1]-c[P]),L=B+R*u,I=p(L,r,t);return I>=i?m(D,L):I===0?L:w(D,B,B+u)}var C=!1;function x(){C=!0,(r!==e||t!==a)&&b()}var T=function(B){return C||x(),r===e&&t===a?B:B===0?0:B===1?1:g(E(B),e,a)};T.getControlPoints=function(){return[{x:r,y:e},{x:t,y:a}]};var k="generateBezier("+[r,e,t,a]+")";return T.toString=function(){return k},T}var vp=function(){function r(a){return-a.tension*a.x-a.friction*a.v}function e(a,n,i){var s={x:a.x+i.dx*n,v:a.v+i.dv*n,tension:a.tension,friction:a.friction};return{dx:s.v,dv:r(s)}}function t(a,n){var i={dx:a.v,dv:r(a)},s=e(a,n*.5,i),o=e(a,n*.5,s),l=e(a,n,o),u=1/6*(i.dx+2*(s.dx+o.dx)+l.dx),v=1/6*(i.dv+2*(s.dv+o.dv)+l.dv);return a.x=a.x+u*n,a.v=a.v+v*n,a}return function a(n,i,s){var o={x:-1,v:0,tension:null,friction:null},l=[0],u=0,v=1/1e4,f=16/1e3,c,h,d;for(n=parseFloat(n)||500,i=parseFloat(i)||20,s=s||null,o.tension=n,o.friction=i,c=s!==null,c?(u=a(n,i),h=u/s*f):h=f;d=t(d||o,h),l.push(1+d.x),u+=16,Math.abs(d.x)>v&&Math.abs(d.v)>v;);return c?function(y){return l[y*(l.length-1)|0]}:u}}(),He=function(e,t,a,n){var i=lp(e,t,a,n);return function(s,o,l){return s+(o-s)*i(l)}},Dn={linear:function(e,t,a){return e+(t-e)*a},ease:He(.25,.1,.25,1),"ease-in":He(.42,0,1,1),"ease-out":He(0,0,.58,1),"ease-in-out":He(.42,0,.58,1),"ease-in-sine":He(.47,0,.745,.715),"ease-out-sine":He(.39,.575,.565,1),"ease-in-out-sine":He(.445,.05,.55,.95),"ease-in-quad":He(.55,.085,.68,.53),"ease-out-quad":He(.25,.46,.45,.94),"ease-in-out-quad":He(.455,.03,.515,.955),"ease-in-cubic":He(.55,.055,.675,.19),"ease-out-cubic":He(.215,.61,.355,1),"ease-in-out-cubic":He(.645,.045,.355,1),"ease-in-quart":He(.895,.03,.685,.22),"ease-out-quart":He(.165,.84,.44,1),"ease-in-out-quart":He(.77,0,.175,1),"ease-in-quint":He(.755,.05,.855,.06),"ease-out-quint":He(.23,1,.32,1),"ease-in-out-quint":He(.86,0,.07,1),"ease-in-expo":He(.95,.05,.795,.035),"ease-out-expo":He(.19,1,.22,1),"ease-in-out-expo":He(1,0,0,1),"ease-in-circ":He(.6,.04,.98,.335),"ease-out-circ":He(.075,.82,.165,1),"ease-in-out-circ":He(.785,.135,.15,.86),spring:function(e,t,a){if(a===0)return Dn.linear;var n=vp(e,t,a);return function(i,s,o){return i+(s-i)*n(o)}},"cubic-bezier":He};function xv(r,e,t,a,n){if(a===1||e===t)return t;var i=n(e,t,a);return r==null||((r.roundValue||r.color)&&(i=Math.round(i)),r.min!==void 0&&(i=Math.max(i,r.min)),r.max!==void 0&&(i=Math.min(i,r.max))),i}function Ev(r,e){return r.pfValue!=null||r.value!=null?r.pfValue!=null&&(e==null||e.type.units!=="%")?r.pfValue:r.value:r}function Qt(r,e,t,a,n){var i=n!=null?n.type:null;t<0?t=0:t>1&&(t=1);var s=Ev(r,n),o=Ev(e,n);if(te(s)&&te(o))return xv(i,s,o,t,a);if(_e(s)&&_e(o)){for(var l=[],u=0;u<o.length;u++){var v=s[u],f=o[u];if(v!=null&&f!=null){var c=xv(i,v,f,t,a);l.push(c)}else l.push(f)}return l}}function fp(r,e,t,a){var n=!a,i=r._private,s=e._private,o=s.easing,l=s.startTime,u=a?r:r.cy(),v=u.style();if(!s.easingImpl)if(o==null)s.easingImpl=Dn.linear;else{var f;if(he(o)){var c=v.parse("transition-timing-function",o);f=c.value}else f=o;var h,d;he(f)?(h=f,d=[]):(h=f[1],d=f.slice(2).map(function(V){return+V})),d.length>0?(h==="spring"&&d.push(s.duration),s.easingImpl=Dn[h].apply(null,d)):s.easingImpl=Dn[h]}var y=s.easingImpl,g;if(s.duration===0?g=1:g=(t-l)/s.duration,s.applying&&(g=s.progress),g<0?g=0:g>1&&(g=1),s.delay==null){var p=s.startPosition,m=s.position;if(m&&n&&!r.locked()){var b={};Da(p.x,m.x)&&(b.x=Qt(p.x,m.x,g,y)),Da(p.y,m.y)&&(b.y=Qt(p.y,m.y,g,y)),r.position(b)}var w=s.startPan,E=s.pan,C=i.pan,x=E!=null&&a;x&&(Da(w.x,E.x)&&(C.x=Qt(w.x,E.x,g,y)),Da(w.y,E.y)&&(C.y=Qt(w.y,E.y,g,y)),r.emit("pan"));var T=s.startZoom,k=s.zoom,D=k!=null&&a;D&&(Da(T,k)&&(i.zoom=ya(i.minZoom,Qt(T,k,g,y),i.maxZoom)),r.emit("zoom")),(x||D)&&r.emit("viewport");var B=s.style;if(B&&B.length>0&&n){for(var P=0;P<B.length;P++){var A=B[P],R=A.name,L=A,I=s.startStyle[R],M=v.properties[I.name],O=Qt(I,L,g,y,M);v.overrideBypass(r,R,O)}r.emit("style")}}return s.progress=g,g}function Da(r,e){return r==null||e==null?!1:te(r)&&te(e)?!0:!!(r&&e)}function cp(r,e,t,a){var n=e._private;n.started=!0,n.startTime=t-n.progress*n.duration}function Cv(r,e){var t=e._private.aniEles,a=[];function n(v,f){var c=v._private,h=c.animation.current,d=c.animation.queue,y=!1;if(h.length===0){var g=d.shift();g&&h.push(g)}for(var p=function(C){for(var x=C.length-1;x>=0;x--){var T=C[x];T()}C.splice(0,C.length)},m=h.length-1;m>=0;m--){var b=h[m],w=b._private;if(w.stopped){h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.frames);continue}!w.playing&&!w.applying||(w.playing&&w.applying&&(w.applying=!1),w.started||cp(v,b,r),fp(v,b,r,f),w.applying&&(w.applying=!1),p(w.frames),w.step!=null&&w.step(r),b.completed()&&(h.splice(m,1),w.hooked=!1,w.playing=!1,w.started=!1,p(w.completes)),y=!0)}return!f&&h.length===0&&d.length===0&&a.push(v),y}for(var i=!1,s=0;s<t.length;s++){var o=t[s],l=n(o);i=i||l}var u=n(e,!0);(i||u)&&(t.length>0?e.notify("draw",t):e.notify("draw")),t.unmerge(a),e.emit("step")}var dp={animate:Ne.animate(),animation:Ne.animation(),animated:Ne.animated(),clearQueue:Ne.clearQueue(),delay:Ne.delay(),delayAnimation:Ne.delayAnimation(),stop:Ne.stop(),addToAnimationPool:function(e){var t=this;t.styleEnabled()&&t._private.aniEles.merge(e)},stopAnimationLoop:function(){this._private.animationsRunning=!1},startAnimationLoop:function(){var e=this;if(e._private.animationsRunning=!0,!e.styleEnabled())return;function t(){e._private.animationsRunning&&Za(function(i){Cv(i,e),t()})}var a=e.renderer();a&&a.beforeRender?a.beforeRender(function(i,s){Cv(s,e)},a.beforeRenderPriorities.animations):t()}},hp={qualifierCompare:function(e,t){return e==null||t==null?e==null&&t==null:e.sameText(t)},eventMatches:function(e,t,a){var n=t.qualifier;return n!=null?e!==a.target&&ua(a.target)&&n.matches(a.target):!0},addEventFields:function(e,t){t.cy=e,t.target=e},callbackContext:function(e,t,a){return t.qualifier!=null?a.target:e}},Bn=function(e){return he(e)?new lt(e):e},Tv={createEmitter:function(){var e=this._private;return e.emitter||(e.emitter=new xn(hp,this)),this},emitter:function(){return this._private.emitter},on:function(e,t,a){return this.emitter().on(e,Bn(t),a),this},removeListener:function(e,t,a){return this.emitter().removeListener(e,Bn(t),a),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},one:function(e,t,a){return this.emitter().one(e,Bn(t),a),this},once:function(e,t,a){return this.emitter().one(e,Bn(t),a),this},emit:function(e,t){return this.emitter().emit(e,t),this},emitAndNotify:function(e,t){return this.emit(e),this.notify(e,t),this}};Ne.eventAliasesOn(Tv);var Hs={png:function(e){var t=this._private.renderer;return e=e||{},t.png(e)},jpg:function(e){var t=this._private.renderer;return e=e||{},e.bg=e.bg||"#fff",t.jpg(e)}};Hs.jpeg=Hs.jpg;var Pn={layout:function(e){var t=this;if(e==null){$e("Layout options must be specified to make a layout");return}if(e.name==null){$e("A `name` must be specified to make a layout");return}var a=e.name,n=t.extension("layout",a);if(n==null){$e("No such layout `"+a+"` found.  Did you forget to import it and `cytoscape.use()` it?");return}var i;he(e.eles)?i=t.$(e.eles):i=e.eles!=null?e.eles:t.$();var s=new n(me({},e,{cy:t,eles:i}));return s}};Pn.createLayout=Pn.makeLayout=Pn.layout;var gp={notify:function(e,t){var a=this._private;if(this.batching()){a.batchNotifications=a.batchNotifications||{};var n=a.batchNotifications[e]=a.batchNotifications[e]||this.collection();t!=null&&n.merge(t);return}if(a.notificationsEnabled){var i=this.renderer();this.destroyed()||!i||i.notify(e,t)}},notifications:function(e){var t=this._private;return e===void 0?t.notificationsEnabled:(t.notificationsEnabled=!!e,this)},noNotifications:function(e){this.notifications(!1),e(),this.notifications(!0)},batching:function(){return this._private.batchCount>0},startBatch:function(){var e=this._private;return e.batchCount==null&&(e.batchCount=0),e.batchCount===0&&(e.batchStyleEles=this.collection(),e.batchNotifications={}),e.batchCount++,this},endBatch:function(){var e=this._private;if(e.batchCount===0)return this;if(e.batchCount--,e.batchCount===0){e.batchStyleEles.updateStyle();var t=this.renderer();Object.keys(e.batchNotifications).forEach(function(a){var n=e.batchNotifications[a];n.empty()?t.notify(a):t.notify(a,n)})}return this},batch:function(e){return this.startBatch(),e(),this.endBatch(),this},batchData:function(e){var t=this;return this.batch(function(){for(var a=Object.keys(e),n=0;n<a.length;n++){var i=a[n],s=e[i],o=t.getElementById(i);o.data(s)}})}},pp=lr({hideEdgesOnViewport:!1,textureOnViewport:!1,motionBlur:!1,motionBlurOpacity:.05,pixelRatio:void 0,desktopTapThreshold:4,touchTapThreshold:8,wheelSensitivity:1,debug:!1,showFps:!1,webgl:!1,webglDebug:!1,webglDebugShowAtlases:!1,webglTexSize:2048,webglTexRows:36,webglTexRowsNodes:18,webglBatchSize:2048,webglTexPerBatch:14,webglBgColor:[255,255,255]}),Ws={renderTo:function(e,t,a,n){var i=this._private.renderer;return i.renderTo(e,t,a,n),this},renderer:function(){return this._private.renderer},forceRender:function(){return this.notify("draw"),this},resize:function(){return this.invalidateSize(),this.emitAndNotify("resize"),this},initRenderer:function(e){var t=this,a=t.extension("renderer",e.name);if(a==null){$e("Can not initialise: No such renderer `".concat(e.name,"` found. Did you forget to import it and `cytoscape.use()` it?"));return}e.wheelSensitivity!==void 0&&Oe("You have set a custom wheel sensitivity.  This will make your app zoom unnaturally when using mainstream mice.  You should change this value from the default only if you can guarantee that all your users will use the same hardware and OS configuration as your current machine.");var n=pp(e);n.cy=t,t._private.renderer=new a(n),this.notify("init")},destroyRenderer:function(){var e=this;e.notify("destroy");var t=e.container();if(t)for(t._cyreg=null;t.childNodes.length>0;)t.removeChild(t.childNodes[0]);e._private.renderer=null,e.mutableElements().forEach(function(a){var n=a._private;n.rscratch={},n.rstyle={},n.animation.current=[],n.animation.queue=[]})},onRender:function(e){return this.on("render",e)},offRender:function(e){return this.off("render",e)}};Ws.invalidateDimensions=Ws.resize;var An={collection:function(e,t){return he(e)?this.$(e):Dr(e)?e.collection():_e(e)?(t||(t={}),new fr(this,e,t.unique,t.removed)):new fr(this)},nodes:function(e){var t=this.$(function(a){return a.isNode()});return e?t.filter(e):t},edges:function(e){var t=this.$(function(a){return a.isEdge()});return e?t.filter(e):t},$:function(e){var t=this._private.elements;return e?t.filter(e):t.spawnSelf()},mutableElements:function(){return this._private.elements}};An.elements=An.filter=An.$;var sr={},Ba="t",yp="f";sr.apply=function(r){for(var e=this,t=e._private,a=t.cy,n=a.collection(),i=0;i<r.length;i++){var s=r[i],o=e.getContextMeta(s);if(!o.empty){var l=e.getContextStyle(o),u=e.applyContextStyle(o,l,s);s._private.appliedInitStyle?e.updateTransitions(s,u.diffProps):s._private.appliedInitStyle=!0;var v=e.updateStyleHints(s);v&&n.push(s)}}return n},sr.getPropertiesDiff=function(r,e){var t=this,a=t._private.propDiffs=t._private.propDiffs||{},n=r+"-"+e,i=a[n];if(i)return i;for(var s=[],o={},l=0;l<t.length;l++){var u=t[l],v=r[l]===Ba,f=e[l]===Ba,c=v!==f,h=u.mappedProperties.length>0;if(c||f&&h){var d=void 0;c&&h||c?d=u.properties:h&&(d=u.mappedProperties);for(var y=0;y<d.length;y++){for(var g=d[y],p=g.name,m=!1,b=l+1;b<t.length;b++){var w=t[b],E=e[b]===Ba;if(E&&(m=w.properties[g.name]!=null,m))break}!o[p]&&!m&&(o[p]=!0,s.push(p))}}}return a[n]=s,s},sr.getContextMeta=function(r){for(var e=this,t="",a,n=r._private.styleCxtKey||"",i=0;i<e.length;i++){var s=e[i],o=s.selector&&s.selector.matches(r);o?t+=Ba:t+=yp}return a=e.getPropertiesDiff(n,t),r._private.styleCxtKey=t,{key:t,diffPropNames:a,empty:a.length===0}},sr.getContextStyle=function(r){var e=r.key,t=this,a=this._private.contextStyles=this._private.contextStyles||{};if(a[e])return a[e];for(var n={_private:{key:e}},i=0;i<t.length;i++){var s=t[i],o=e[i]===Ba;if(o)for(var l=0;l<s.properties.length;l++){var u=s.properties[l];n[u.name]=u}}return a[e]=n,n},sr.applyContextStyle=function(r,e,t){for(var a=this,n=r.diffPropNames,i={},s=a.types,o=0;o<n.length;o++){var l=n[o],u=e[l],v=t.pstyle(l);if(!u)if(v)v.bypass?u={name:l,deleteBypassed:!0}:u={name:l,delete:!0};else continue;if(v!==u){if(u.mapped===s.fn&&v!=null&&v.mapping!=null&&v.mapping.value===u.value){var f=v.mapping,c=f.fnValue=u.value(t);if(c===f.prevFnValue)continue}var h=i[l]={prev:v};a.applyParsedProperty(t,u),h.next=t.pstyle(l),h.next&&h.next.bypass&&(h.next=h.next.bypassed)}}return{diffProps:i}},sr.updateStyleHints=function(r){var e=r._private,t=this,a=t.propertyGroupNames,n=t.propertyGroupKeys,i=function(H,Y,ae){return t.getPropertiesHash(H,Y,ae)},s=e.styleKey;if(r.removed())return!1;var o=e.group==="nodes",l=r._private.style;a=Object.keys(l);for(var u=0;u<n.length;u++){var v=n[u];e.styleKeys[v]=[bt,Nt]}for(var f=function(H,Y){return e.styleKeys[Y][0]=da(H,e.styleKeys[Y][0])},c=function(H,Y){return e.styleKeys[Y][1]=ha(H,e.styleKeys[Y][1])},h=function(H,Y){f(H,Y),c(H,Y)},d=function(H,Y){for(var ae=0;ae<H.length;ae++){var ce=H.charCodeAt(ae);f(ce,Y),c(ce,Y)}},y=2e9,g=function(H){return-128<H&&H<128&&Math.floor(H)!==H?y-(H*1024|0):H},p=0;p<a.length;p++){var m=a[p],b=l[m];if(b!=null){var w=this.properties[m],E=w.type,C=w.groupKey,x=void 0;w.hashOverride!=null?x=w.hashOverride(r,b):b.pfValue!=null&&(x=b.pfValue);var T=w.enums==null?b.value:null,k=x!=null,D=T!=null,B=k||D,P=b.units;if(E.number&&B&&!E.multiple){var A=k?x:T;h(g(A),C),!k&&P!=null&&d(P,C)}else d(b.strValue,C)}}for(var R=[bt,Nt],L=0;L<n.length;L++){var I=n[L],M=e.styleKeys[I];R[0]=da(M[0],R[0]),R[1]=ha(M[1],R[1])}e.styleKey=Lc(R[0],R[1]);var O=e.styleKeys;e.labelDimsKey=at(O.labelDimensions);var V=i(r,["label"],O.labelDimensions);if(e.labelKey=at(V),e.labelStyleKey=at(Qa(O.commonLabel,V)),!o){var G=i(r,["source-label"],O.labelDimensions);e.sourceLabelKey=at(G),e.sourceLabelStyleKey=at(Qa(O.commonLabel,G));var N=i(r,["target-label"],O.labelDimensions);e.targetLabelKey=at(N),e.targetLabelStyleKey=at(Qa(O.commonLabel,N))}if(o){var F=e.styleKeys,U=F.nodeBody,Q=F.nodeBorder,K=F.nodeOutline,j=F.backgroundImage,re=F.compound,ne=F.pie,J=F.stripe,z=[U,Q,K,j,re,ne,J].filter(function(q){return q!=null}).reduce(Qa,[bt,Nt]);e.nodeKey=at(z),e.hasPie=ne!=null&&ne[0]!==bt&&ne[1]!==Nt,e.hasStripe=J!=null&&J[0]!==bt&&J[1]!==Nt}return s!==e.styleKey},sr.clearStyleHints=function(r){var e=r._private;e.styleCxtKey="",e.styleKeys={},e.styleKey=null,e.labelKey=null,e.labelStyleKey=null,e.sourceLabelKey=null,e.sourceLabelStyleKey=null,e.targetLabelKey=null,e.targetLabelStyleKey=null,e.nodeKey=null,e.hasPie=null,e.hasStripe=null},sr.applyParsedProperty=function(r,e){var t=this,a=e,n=r._private.style,i,s=t.types,o=t.properties[a.name].type,l=a.bypass,u=n[a.name],v=u&&u.bypass,f=r._private,c="mapping",h=function(U){return U==null?null:U.pfValue!=null?U.pfValue:U.value},d=function(){var U=h(u),Q=h(a);t.checkTriggers(r,a.name,U,Q)};if(e.name==="curve-style"&&r.isEdge()&&(e.value!=="bezier"&&r.isLoop()||e.value==="haystack"&&(r.source().isParent()||r.target().isParent()))&&(a=e=this.parse(e.name,"bezier",l)),a.delete)return n[a.name]=void 0,d(),!0;if(a.deleteBypassed)return u?u.bypass?(u.bypassed=void 0,d(),!0):!1:(d(),!0);if(a.deleteBypass)return u?u.bypass?(n[a.name]=u.bypassed,d(),!0):!1:(d(),!0);var y=function(){Oe("Do not assign mappings to elements without corresponding data (i.e. ele `"+r.id()+"` has no mapping for property `"+a.name+"` with data field `"+a.field+"`); try a `["+a.field+"]` selector to limit scope to elements with `"+a.field+"` defined")};switch(a.mapped){case s.mapData:{for(var g=a.field.split("."),p=f.data,m=0;m<g.length&&p;m++){var b=g[m];p=p[b]}if(p==null)return y(),!1;var w;if(te(p)){var E=a.fieldMax-a.fieldMin;E===0?w=0:w=(p-a.fieldMin)/E}else return Oe("Do not use continuous mappers without specifying numeric data (i.e. `"+a.field+": "+p+"` for `"+r.id()+"` is non-numeric)"),!1;if(w<0?w=0:w>1&&(w=1),o.color){var C=a.valueMin[0],x=a.valueMax[0],T=a.valueMin[1],k=a.valueMax[1],D=a.valueMin[2],B=a.valueMax[2],P=a.valueMin[3]==null?1:a.valueMin[3],A=a.valueMax[3]==null?1:a.valueMax[3],R=[Math.round(C+(x-C)*w),Math.round(T+(k-T)*w),Math.round(D+(B-D)*w),Math.round(P+(A-P)*w)];i={bypass:a.bypass,name:a.name,value:R,strValue:"rgb("+R[0]+", "+R[1]+", "+R[2]+")"}}else if(o.number){var L=a.valueMin+(a.valueMax-a.valueMin)*w;i=this.parse(a.name,L,a.bypass,c)}else return!1;if(!i)return y(),!1;i.mapping=a,a=i;break}case s.data:{for(var I=a.field.split("."),M=f.data,O=0;O<I.length&&M;O++){var V=I[O];M=M[V]}if(M!=null&&(i=this.parse(a.name,M,a.bypass,c)),!i)return y(),!1;i.mapping=a,a=i;break}case s.fn:{var G=a.value,N=a.fnValue!=null?a.fnValue:G(r);if(a.prevFnValue=N,N==null)return Oe("Custom function mappers may not return null (i.e. `"+a.name+"` for ele `"+r.id()+"` is null)"),!1;if(i=this.parse(a.name,N,a.bypass,c),!i)return Oe("Custom function mappers may not return invalid values for the property type (i.e. `"+a.name+"` for ele `"+r.id()+"` is invalid)"),!1;i.mapping=zr(a),a=i;break}case void 0:break;default:return!1}return l?(v?a.bypassed=u.bypassed:a.bypassed=u,n[a.name]=a):v?u.bypassed=a:n[a.name]=a,d(),!0},sr.cleanElements=function(r,e){for(var t=0;t<r.length;t++){var a=r[t];if(this.clearStyleHints(a),a.dirtyCompoundBoundsCache(),a.dirtyBoundingBoxCache(),!e)a._private.style={};else for(var n=a._private.style,i=Object.keys(n),s=0;s<i.length;s++){var o=i[s],l=n[o];l!=null&&(l.bypass?l.bypassed=null:n[o]=null)}}},sr.update=function(){var r=this._private.cy,e=r.mutableElements();e.updateStyle()},sr.updateTransitions=function(r,e){var t=this,a=r._private,n=r.pstyle("transition-property").value,i=r.pstyle("transition-duration").pfValue,s=r.pstyle("transition-delay").pfValue;if(n.length>0&&i>0){for(var o={},l=!1,u=0;u<n.length;u++){var v=n[u],f=r.pstyle(v),c=e[v];if(c){var h=c.prev,d=h,y=c.next!=null?c.next:f,g=!1,p=void 0,m=1e-6;d&&(te(d.pfValue)&&te(y.pfValue)?(g=y.pfValue-d.pfValue,p=d.pfValue+m*g):te(d.value)&&te(y.value)?(g=y.value-d.value,p=d.value+m*g):_e(d.value)&&_e(y.value)&&(g=d.value[0]!==y.value[0]||d.value[1]!==y.value[1]||d.value[2]!==y.value[2],p=d.strValue),g&&(o[v]=y.strValue,this.applyBypass(r,v,p),l=!0))}}if(!l)return;a.transitioning=!0,new Wt(function(b){s>0?r.delayAnimation(s).play().promise().then(b):b()}).then(function(){return r.animation({style:o,duration:i,easing:r.pstyle("transition-timing-function").value,queue:!1}).play().promise()}).then(function(){t.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1})}else a.transitioning&&(this.removeBypasses(r,n),r.emitAndNotify("style"),a.transitioning=!1)},sr.checkTrigger=function(r,e,t,a,n,i){var s=this.properties[e],o=n(s);r.removed()||o!=null&&o(t,a,r)&&i(s)},sr.checkZOrderTrigger=function(r,e,t,a){var n=this;this.checkTrigger(r,e,t,a,function(i){return i.triggersZOrder},function(){n._private.cy.notify("zorder",r)})},sr.checkBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBounds},function(n){r.dirtyCompoundBoundsCache(),r.dirtyBoundingBoxCache()})},sr.checkConnectedEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfConnectedEdges},function(n){r.connectedEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})},sr.checkParallelEdgesBoundsTrigger=function(r,e,t,a){this.checkTrigger(r,e,t,a,function(n){return n.triggersBoundsOfParallelEdges},function(n){r.parallelEdges().forEach(function(i){i.dirtyBoundingBoxCache()})})},sr.checkTriggers=function(r,e,t,a){r.dirtyStyleCache(),this.checkZOrderTrigger(r,e,t,a),this.checkBoundsTrigger(r,e,t,a),this.checkConnectedEdgesBoundsTrigger(r,e,t,a),this.checkParallelEdgesBoundsTrigger(r,e,t,a)};var Pa={};Pa.applyBypass=function(r,e,t,a){var n=this,i=[],s=!0;if(e==="*"||e==="**"){if(t!==void 0)for(var o=0;o<n.properties.length;o++){var l=n.properties[o],u=l.name,v=this.parse(u,t,!0);v&&i.push(v)}}else if(he(e)){var f=this.parse(e,t,!0);f&&i.push(f)}else if(Me(e)){var c=e;a=t;for(var h=Object.keys(c),d=0;d<h.length;d++){var y=h[d],g=c[y];if(g===void 0&&(g=c[Ka(y)]),g!==void 0){var p=this.parse(y,g,!0);p&&i.push(p)}}}else return!1;if(i.length===0)return!1;for(var m=!1,b=0;b<r.length;b++){for(var w=r[b],E={},C=void 0,x=0;x<i.length;x++){var T=i[x];if(a){var k=w.pstyle(T.name);C=E[T.name]={prev:k}}m=this.applyParsedProperty(w,zr(T))||m,a&&(C.next=w.pstyle(T.name))}m&&this.updateStyleHints(w),a&&this.updateTransitions(w,E,s)}return m},Pa.overrideBypass=function(r,e,t){e=jn(e);for(var a=0;a<r.length;a++){var n=r[a],i=n._private.style[e],s=this.properties[e].type,o=s.color,l=s.mutiple,u=i?i.pfValue!=null?i.pfValue:i.value:null;!i||!i.bypass?this.applyBypass(n,e,t):(i.value=t,i.pfValue!=null&&(i.pfValue=t),o?i.strValue="rgb("+t.join(",")+")":l?i.strValue=t.join(" "):i.strValue=""+t,this.updateStyleHints(n)),this.checkTriggers(n,e,u,t)}},Pa.removeAllBypasses=function(r,e){return this.removeBypasses(r,this.propertyNames,e)},Pa.removeBypasses=function(r,e,t){for(var a=!0,n=0;n<r.length;n++){for(var i=r[n],s={},o=0;o<e.length;o++){var l=e[o],u=this.properties[l],v=i.pstyle(u.name);if(!(!v||!v.bypass)){var f="",c=this.parse(l,f,!0),h=s[u.name]={prev:v};this.applyParsedProperty(i,c),h.next=i.pstyle(u.name)}}this.updateStyleHints(i),t&&this.updateTransitions(i,s,a)}};var $s={};$s.getEmSizeInPixels=function(){var r=this.containerCss("font-size");return r!=null?parseFloat(r):1},$s.containerCss=function(r){var e=this._private.cy,t=e.container(),a=e.window();if(a&&t&&a.getComputedStyle)return a.getComputedStyle(t).getPropertyValue(r)};var Fr={};Fr.getRenderedStyle=function(r,e){return e?this.getStylePropertyValue(r,e,!0):this.getRawStyle(r,!0)},Fr.getRawStyle=function(r,e){var t=this;if(r=r[0],r){for(var a={},n=0;n<t.properties.length;n++){var i=t.properties[n],s=t.getStylePropertyValue(r,i.name,e);s!=null&&(a[i.name]=s,a[Ka(i.name)]=s)}return a}},Fr.getIndexedStyle=function(r,e,t,a){var n=r.pstyle(e)[t][a];return n!=null?n:r.cy().style().getDefaultProperty(e)[t][0]},Fr.getStylePropertyValue=function(r,e,t){var a=this;if(r=r[0],r){var n=a.properties[e];n.alias&&(n=n.pointsTo);var i=n.type,s=r.pstyle(n.name);if(s){var o=s.value,l=s.units,u=s.strValue;if(t&&i.number&&o!=null&&te(o)){var v=r.cy().zoom(),f=function(g){return g*v},c=function(g,p){return f(g)+p},h=_e(o),d=h?l.every(function(y){return y!=null}):l!=null;return d?h?o.map(function(y,g){return c(y,l[g])}).join(" "):c(o,l):h?o.map(function(y){return he(y)?y:""+f(y)}).join(" "):""+f(o)}else if(u!=null)return u}return null}},Fr.getAnimationStartStyle=function(r,e){for(var t={},a=0;a<e.length;a++){var n=e[a],i=n.name,s=r.pstyle(i);s!==void 0&&(Me(s)?s=this.parse(i,s.strValue):s=this.parse(i,s)),s&&(t[i]=s)}return t},Fr.getPropsList=function(r){var e=this,t=[],a=r,n=e.properties;if(a)for(var i=Object.keys(a),s=0;s<i.length;s++){var o=i[s],l=a[o],u=n[o]||n[jn(o)],v=this.parse(u.name,l);v&&t.push(v)}return t},Fr.getNonDefaultPropertiesHash=function(r,e,t){var a=t.slice(),n,i,s,o,l,u;for(l=0;l<e.length;l++)if(n=e[l],i=r.pstyle(n,!1),i!=null)if(i.pfValue!=null)a[0]=da(o,a[0]),a[1]=ha(o,a[1]);else for(s=i.strValue,u=0;u<s.length;u++)o=s.charCodeAt(u),a[0]=da(o,a[0]),a[1]=ha(o,a[1]);return a},Fr.getPropertiesHash=Fr.getNonDefaultPropertiesHash;var Rn={};Rn.appendFromJson=function(r){for(var e=this,t=0;t<r.length;t++){var a=r[t],n=a.selector,i=a.style||a.css,s=Object.keys(i);e.selector(n);for(var o=0;o<s.length;o++){var l=s[o],u=i[l];e.css(l,u)}}return e},Rn.fromJson=function(r){var e=this;return e.resetToDefault(),e.appendFromJson(r),e},Rn.json=function(){for(var r=[],e=this.defaultLength;e<this.length;e++){for(var t=this[e],a=t.selector,n=t.properties,i={},s=0;s<n.length;s++){var o=n[s];i[o.name]=o.strValue}r.push({selector:a?a.toString():"core",style:i})}return r};var Us={};Us.appendFromString=function(r){var e=this,t=this,a=""+r,n,i,s;a=a.replace(/[/][*](\s|.)+?[*][/]/g,"");function o(){a.length>n.length?a=a.substr(n.length):a=""}function l(){i.length>s.length?i=i.substr(s.length):i=""}for(;;){var u=a.match(/^\s*$/);if(u)break;var v=a.match(/^\s*((?:.|\s)+?)\s*\{((?:.|\s)+?)\}/);if(!v){Oe("Halting stylesheet parsing: String stylesheet contains more to parse but no selector and block found in: "+a);break}n=v[0];var f=v[1];if(f!=="core"){var c=new lt(f);if(c.invalid){Oe("Skipping parsing of block: Invalid selector found in string stylesheet: "+f),o();continue}}var h=v[2],d=!1;i=h;for(var y=[];;){var g=i.match(/^\s*$/);if(g)break;var p=i.match(/^\s*(.+?)\s*:\s*(.+?)(?:\s*;|\s*$)/);if(!p){Oe("Skipping parsing of block: Invalid formatting of style property and value definitions found in:"+h),d=!0;break}s=p[0];var m=p[1],b=p[2],w=e.properties[m];if(!w){Oe("Skipping property: Invalid property name in: "+s),l();continue}var E=t.parse(m,b);if(!E){Oe("Skipping property: Invalid property definition in: "+s),l();continue}y.push({name:m,val:b}),l()}if(d){o();break}t.selector(f);for(var C=0;C<y.length;C++){var x=y[C];t.css(x.name,x.val)}o()}return t},Us.fromString=function(r){var e=this;return e.resetToDefault(),e.appendFromString(r),e};var Ze={};(function(){var r=rr,e=fc,t=dc,a=hc,n=gc,i=function(q){return"^"+q+"\\s*\\(\\s*([\\w\\.]+)\\s*\\)$"},s=function(q){var H=r+"|\\w+|"+e+"|"+t+"|"+a+"|"+n;return"^"+q+"\\s*\\(([\\w\\.]+)\\s*\\,\\s*("+r+")\\s*\\,\\s*("+r+")\\s*,\\s*("+H+")\\s*\\,\\s*("+H+")\\)$"},o=[`^url\\s*\\(\\s*['"]?(.+?)['"]?\\s*\\)$`,"^(none)$","^(.+)$"];Ze.types={time:{number:!0,min:0,units:"s|ms",implicitUnits:"ms"},percent:{number:!0,min:0,max:100,units:"%",implicitUnits:"%"},percentages:{number:!0,min:0,max:100,units:"%",implicitUnits:"%",multiple:!0},zeroOneNumber:{number:!0,min:0,max:1,unitless:!0},zeroOneNumbers:{number:!0,min:0,max:1,unitless:!0,multiple:!0},nOneOneNumber:{number:!0,min:-1,max:1,unitless:!0},nonNegativeInt:{number:!0,min:0,integer:!0,unitless:!0},nonNegativeNumber:{number:!0,min:0,unitless:!0},position:{enums:["parent","origin"]},nodeSize:{number:!0,min:0,enums:["label"]},number:{number:!0,unitless:!0},numbers:{number:!0,unitless:!0,multiple:!0},positiveNumber:{number:!0,unitless:!0,min:0,strictMin:!0},size:{number:!0,min:0},bidirectionalSize:{number:!0},bidirectionalSizeMaybePercent:{number:!0,allowPercent:!0},bidirectionalSizes:{number:!0,multiple:!0},sizeMaybePercent:{number:!0,min:0,allowPercent:!0},axisDirection:{enums:["horizontal","leftward","rightward","vertical","upward","downward","auto"]},axisDirectionExplicit:{enums:["leftward","rightward","upward","downward"]},axisDirectionPrimary:{enums:["horizontal","vertical"]},paddingRelativeTo:{enums:["width","height","average","min","max"]},bgWH:{number:!0,min:0,allowPercent:!0,enums:["auto"],multiple:!0},bgPos:{number:!0,allowPercent:!0,multiple:!0},bgRelativeTo:{enums:["inner","include-padding"],multiple:!0},bgRepeat:{enums:["repeat","repeat-x","repeat-y","no-repeat"],multiple:!0},bgFit:{enums:["none","contain","cover"],multiple:!0},bgCrossOrigin:{enums:["anonymous","use-credentials","null"],multiple:!0},bgClip:{enums:["none","node"],multiple:!0},bgContainment:{enums:["inside","over"],multiple:!0},boxSelection:{enums:["contain","overlap","none"]},color:{color:!0},colors:{color:!0,multiple:!0},fill:{enums:["solid","linear-gradient","radial-gradient"]},bool:{enums:["yes","no"]},bools:{enums:["yes","no"],multiple:!0},lineStyle:{enums:["solid","dotted","dashed"]},lineCap:{enums:["butt","round","square"]},linePosition:{enums:["center","inside","outside"]},lineJoin:{enums:["round","bevel","miter"]},borderStyle:{enums:["solid","dotted","dashed","double"]},curveStyle:{enums:["bezier","unbundled-bezier","haystack","segments","straight","straight-triangle","taxi","round-segments","round-taxi"]},radiusType:{enums:["arc-radius","influence-radius"],multiple:!0},fontFamily:{regex:'^([\\w- \\"]+(?:\\s*,\\s*[\\w- \\"]+)*)$'},fontStyle:{enums:["italic","normal","oblique"]},fontWeight:{enums:["normal","bold","bolder","lighter","100","200","300","400","500","600","800","900",100,200,300,400,500,600,700,800,900]},textDecoration:{enums:["none","underline","overline","line-through"]},textTransform:{enums:["none","uppercase","lowercase"]},textWrap:{enums:["none","wrap","ellipsis"]},textOverflowWrap:{enums:["whitespace","anywhere"]},textBackgroundShape:{enums:["rectangle","roundrectangle","round-rectangle","circle"]},nodeShape:{enums:["rectangle","roundrectangle","round-rectangle","cutrectangle","cut-rectangle","bottomroundrectangle","bottom-round-rectangle","barrel","ellipse","triangle","round-triangle","square","pentagon","round-pentagon","hexagon","round-hexagon","concavehexagon","concave-hexagon","heptagon","round-heptagon","octagon","round-octagon","tag","round-tag","star","diamond","round-diamond","vee","rhomboid","right-rhomboid","polygon"]},overlayShape:{enums:["roundrectangle","round-rectangle","ellipse"]},cornerRadius:{number:!0,min:0,units:"px|em",implicitUnits:"px",enums:["auto"]},compoundIncludeLabels:{enums:["include","exclude"]},arrowShape:{enums:["tee","triangle","triangle-tee","circle-triangle","triangle-cross","triangle-backcurve","vee","square","circle","diamond","chevron","none"]},arrowFill:{enums:["filled","hollow"]},arrowWidth:{number:!0,units:"%|px|em",implicitUnits:"px",enums:["match-line"]},display:{enums:["element","none"]},visibility:{enums:["hidden","visible"]},zCompoundDepth:{enums:["bottom","orphan","auto","top"]},zIndexCompare:{enums:["auto","manual"]},valign:{enums:["top","center","bottom"]},halign:{enums:["left","center","right"]},justification:{enums:["left","center","right","auto"]},text:{string:!0},data:{mapping:!0,regex:i("data")},layoutData:{mapping:!0,regex:i("layoutData")},scratch:{mapping:!0,regex:i("scratch")},mapData:{mapping:!0,regex:s("mapData")},mapLayoutData:{mapping:!0,regex:s("mapLayoutData")},mapScratch:{mapping:!0,regex:s("mapScratch")},fn:{mapping:!0,fn:!0},url:{regexes:o,singleRegexMatchValue:!0},urls:{regexes:o,singleRegexMatchValue:!0,multiple:!0},propList:{propList:!0},angle:{number:!0,units:"deg|rad",implicitUnits:"rad"},textRotation:{number:!0,units:"deg|rad",implicitUnits:"rad",enums:["none","autorotate"]},polygonPointList:{number:!0,multiple:!0,evenMultiple:!0,min:-1,max:1,unitless:!0},edgeDistances:{enums:["intersection","node-position","endpoints"]},edgeEndpoint:{number:!0,multiple:!0,units:"%|px|em|deg|rad",implicitUnits:"px",enums:["inside-to-node","outside-to-node","outside-to-node-or-label","outside-to-line","outside-to-line-or-label"],singleEnum:!0,validate:function(q,H){switch(q.length){case 2:return H[0]!=="deg"&&H[0]!=="rad"&&H[1]!=="deg"&&H[1]!=="rad";case 1:return he(q[0])||H[0]==="deg"||H[0]==="rad";default:return!1}}},easing:{regexes:["^(spring)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$","^(cubic-bezier)\\s*\\(\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*,\\s*("+r+")\\s*\\)$"],enums:["linear","ease","ease-in","ease-out","ease-in-out","ease-in-sine","ease-out-sine","ease-in-out-sine","ease-in-quad","ease-out-quad","ease-in-out-quad","ease-in-cubic","ease-out-cubic","ease-in-out-cubic","ease-in-quart","ease-out-quart","ease-in-out-quart","ease-in-quint","ease-out-quint","ease-in-out-quint","ease-in-expo","ease-out-expo","ease-in-out-expo","ease-in-circ","ease-out-circ","ease-in-out-circ"]},gradientDirection:{enums:["to-bottom","to-top","to-left","to-right","to-bottom-right","to-bottom-left","to-top-right","to-top-left","to-right-bottom","to-left-bottom","to-right-top","to-left-top"]},boundsExpansion:{number:!0,multiple:!0,min:0,validate:function(q){var H=q.length;return H===1||H===2||H===4}}};var l={zeroNonZero:function(q,H){return(q==null||H==null)&&q!==H||q==0&&H!=0?!0:q!=0&&H==0},any:function(q,H){return q!=H},emptyNonEmpty:function(q,H){var Y=rt(q),ae=rt(H);return Y&&!ae||!Y&&ae}},u=Ze.types,v=[{name:"label",type:u.text,triggersBounds:l.any,triggersZOrder:l.emptyNonEmpty},{name:"text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any}],f=[{name:"source-label",type:u.text,triggersBounds:l.any},{name:"source-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"source-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"source-text-offset",type:u.size,triggersBounds:l.any}],c=[{name:"target-label",type:u.text,triggersBounds:l.any},{name:"target-text-rotation",type:u.textRotation,triggersBounds:l.any},{name:"target-text-margin-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-margin-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"target-text-offset",type:u.size,triggersBounds:l.any}],h=[{name:"font-family",type:u.fontFamily,triggersBounds:l.any},{name:"font-style",type:u.fontStyle,triggersBounds:l.any},{name:"font-weight",type:u.fontWeight,triggersBounds:l.any},{name:"font-size",type:u.size,triggersBounds:l.any},{name:"text-transform",type:u.textTransform,triggersBounds:l.any},{name:"text-wrap",type:u.textWrap,triggersBounds:l.any},{name:"text-overflow-wrap",type:u.textOverflowWrap,triggersBounds:l.any},{name:"text-max-width",type:u.size,triggersBounds:l.any},{name:"text-outline-width",type:u.size,triggersBounds:l.any},{name:"line-height",type:u.positiveNumber,triggersBounds:l.any}],d=[{name:"text-valign",type:u.valign,triggersBounds:l.any},{name:"text-halign",type:u.halign,triggersBounds:l.any},{name:"color",type:u.color},{name:"text-outline-color",type:u.color},{name:"text-outline-opacity",type:u.zeroOneNumber},{name:"text-background-color",type:u.color},{name:"text-background-opacity",type:u.zeroOneNumber},{name:"text-background-padding",type:u.size,triggersBounds:l.any},{name:"text-border-opacity",type:u.zeroOneNumber},{name:"text-border-color",type:u.color},{name:"text-border-width",type:u.size,triggersBounds:l.any},{name:"text-border-style",type:u.borderStyle,triggersBounds:l.any},{name:"text-background-shape",type:u.textBackgroundShape,triggersBounds:l.any},{name:"text-justification",type:u.justification},{name:"box-select-labels",type:u.bool,triggersBounds:l.any}],y=[{name:"events",type:u.bool,triggersZOrder:l.any},{name:"text-events",type:u.bool,triggersZOrder:l.any},{name:"box-selection",type:u.boxSelection,triggersZOrder:l.any}],g=[{name:"display",type:u.display,triggersZOrder:l.any,triggersBounds:l.any,triggersBoundsOfConnectedEdges:l.any,triggersBoundsOfParallelEdges:function(q,H,Y){return q===H?!1:Y.pstyle("curve-style").value==="bezier"}},{name:"visibility",type:u.visibility,triggersZOrder:l.any},{name:"opacity",type:u.zeroOneNumber,triggersZOrder:l.zeroNonZero},{name:"text-opacity",type:u.zeroOneNumber},{name:"min-zoomed-font-size",type:u.size},{name:"z-compound-depth",type:u.zCompoundDepth,triggersZOrder:l.any},{name:"z-index-compare",type:u.zIndexCompare,triggersZOrder:l.any},{name:"z-index",type:u.number,triggersZOrder:l.any}],p=[{name:"overlay-padding",type:u.size,triggersBounds:l.any},{name:"overlay-color",type:u.color},{name:"overlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"overlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"overlay-corner-radius",type:u.cornerRadius}],m=[{name:"underlay-padding",type:u.size,triggersBounds:l.any},{name:"underlay-color",type:u.color},{name:"underlay-opacity",type:u.zeroOneNumber,triggersBounds:l.zeroNonZero},{name:"underlay-shape",type:u.overlayShape,triggersBounds:l.any},{name:"underlay-corner-radius",type:u.cornerRadius}],b=[{name:"transition-property",type:u.propList},{name:"transition-duration",type:u.time},{name:"transition-delay",type:u.time},{name:"transition-timing-function",type:u.easing}],w=function(q,H){return H.value==="label"?-q.poolIndex():H.pfValue},E=[{name:"height",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"width",type:u.nodeSize,triggersBounds:l.any,hashOverride:w},{name:"shape",type:u.nodeShape,triggersBounds:l.any},{name:"shape-polygon-points",type:u.polygonPointList,triggersBounds:l.any},{name:"corner-radius",type:u.cornerRadius},{name:"background-color",type:u.color},{name:"background-fill",type:u.fill},{name:"background-opacity",type:u.zeroOneNumber},{name:"background-blacken",type:u.nOneOneNumber},{name:"background-gradient-stop-colors",type:u.colors},{name:"background-gradient-stop-positions",type:u.percentages},{name:"background-gradient-direction",type:u.gradientDirection},{name:"padding",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"padding-relative-to",type:u.paddingRelativeTo,triggersBounds:l.any},{name:"bounds-expansion",type:u.boundsExpansion,triggersBounds:l.any}],C=[{name:"border-color",type:u.color},{name:"border-opacity",type:u.zeroOneNumber},{name:"border-width",type:u.size,triggersBounds:l.any},{name:"border-style",type:u.borderStyle},{name:"border-cap",type:u.lineCap},{name:"border-join",type:u.lineJoin},{name:"border-dash-pattern",type:u.numbers},{name:"border-dash-offset",type:u.number},{name:"border-position",type:u.linePosition}],x=[{name:"outline-color",type:u.color},{name:"outline-opacity",type:u.zeroOneNumber},{name:"outline-width",type:u.size,triggersBounds:l.any},{name:"outline-style",type:u.borderStyle},{name:"outline-offset",type:u.size,triggersBounds:l.any}],T=[{name:"background-image",type:u.urls},{name:"background-image-crossorigin",type:u.bgCrossOrigin},{name:"background-image-opacity",type:u.zeroOneNumbers},{name:"background-image-containment",type:u.bgContainment},{name:"background-image-smoothing",type:u.bools},{name:"background-position-x",type:u.bgPos},{name:"background-position-y",type:u.bgPos},{name:"background-width-relative-to",type:u.bgRelativeTo},{name:"background-height-relative-to",type:u.bgRelativeTo},{name:"background-repeat",type:u.bgRepeat},{name:"background-fit",type:u.bgFit},{name:"background-clip",type:u.bgClip},{name:"background-width",type:u.bgWH},{name:"background-height",type:u.bgWH},{name:"background-offset-x",type:u.bgPos},{name:"background-offset-y",type:u.bgPos}],k=[{name:"position",type:u.position,triggersBounds:l.any},{name:"compound-sizing-wrt-labels",type:u.compoundIncludeLabels,triggersBounds:l.any},{name:"min-width",type:u.size,triggersBounds:l.any},{name:"min-width-bias-left",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-width-bias-right",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height",type:u.size,triggersBounds:l.any},{name:"min-height-bias-top",type:u.sizeMaybePercent,triggersBounds:l.any},{name:"min-height-bias-bottom",type:u.sizeMaybePercent,triggersBounds:l.any}],D=[{name:"line-style",type:u.lineStyle},{name:"line-color",type:u.color},{name:"line-fill",type:u.fill},{name:"line-cap",type:u.lineCap},{name:"line-opacity",type:u.zeroOneNumber},{name:"line-dash-pattern",type:u.numbers},{name:"line-dash-offset",type:u.number},{name:"line-outline-width",type:u.size},{name:"line-outline-color",type:u.color},{name:"line-gradient-stop-colors",type:u.colors},{name:"line-gradient-stop-positions",type:u.percentages},{name:"curve-style",type:u.curveStyle,triggersBounds:l.any,triggersBoundsOfParallelEdges:function(q,H){return q===H?!1:q==="bezier"||H==="bezier"}},{name:"haystack-radius",type:u.zeroOneNumber,triggersBounds:l.any},{name:"source-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"target-endpoint",type:u.edgeEndpoint,triggersBounds:l.any},{name:"control-point-step-size",type:u.size,triggersBounds:l.any},{name:"control-point-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"control-point-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-distances",type:u.bidirectionalSizes,triggersBounds:l.any},{name:"segment-weights",type:u.numbers,triggersBounds:l.any},{name:"segment-radii",type:u.numbers,triggersBounds:l.any},{name:"radius-type",type:u.radiusType,triggersBounds:l.any},{name:"taxi-turn",type:u.bidirectionalSizeMaybePercent,triggersBounds:l.any},{name:"taxi-turn-min-distance",type:u.size,triggersBounds:l.any},{name:"taxi-direction",type:u.axisDirection,triggersBounds:l.any},{name:"taxi-radius",type:u.number,triggersBounds:l.any},{name:"edge-distances",type:u.edgeDistances,triggersBounds:l.any},{name:"arrow-scale",type:u.positiveNumber,triggersBounds:l.any},{name:"loop-direction",type:u.angle,triggersBounds:l.any},{name:"loop-sweep",type:u.angle,triggersBounds:l.any},{name:"source-distance-from-node",type:u.size,triggersBounds:l.any},{name:"target-distance-from-node",type:u.size,triggersBounds:l.any}],B=[{name:"ghost",type:u.bool,triggersBounds:l.any},{name:"ghost-offset-x",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-offset-y",type:u.bidirectionalSize,triggersBounds:l.any},{name:"ghost-opacity",type:u.zeroOneNumber}],P=[{name:"selection-box-color",type:u.color},{name:"selection-box-opacity",type:u.zeroOneNumber},{name:"selection-box-border-color",type:u.color},{name:"selection-box-border-width",type:u.size},{name:"active-bg-color",type:u.color},{name:"active-bg-opacity",type:u.zeroOneNumber},{name:"active-bg-size",type:u.size},{name:"outside-texture-bg-color",type:u.color},{name:"outside-texture-bg-opacity",type:u.zeroOneNumber}],A=[];Ze.pieBackgroundN=16,A.push({name:"pie-size",type:u.sizeMaybePercent}),A.push({name:"pie-hole",type:u.sizeMaybePercent}),A.push({name:"pie-start-angle",type:u.angle});for(var R=1;R<=Ze.pieBackgroundN;R++)A.push({name:"pie-"+R+"-background-color",type:u.color}),A.push({name:"pie-"+R+"-background-size",type:u.percent}),A.push({name:"pie-"+R+"-background-opacity",type:u.zeroOneNumber});var L=[];Ze.stripeBackgroundN=16,L.push({name:"stripe-size",type:u.sizeMaybePercent}),L.push({name:"stripe-direction",type:u.axisDirectionPrimary});for(var I=1;I<=Ze.stripeBackgroundN;I++)L.push({name:"stripe-"+I+"-background-color",type:u.color}),L.push({name:"stripe-"+I+"-background-size",type:u.percent}),L.push({name:"stripe-"+I+"-background-opacity",type:u.zeroOneNumber});var M=[],O=Ze.arrowPrefixes=["source","mid-source","target","mid-target"];[{name:"arrow-shape",type:u.arrowShape,triggersBounds:l.any},{name:"arrow-color",type:u.color},{name:"arrow-fill",type:u.arrowFill},{name:"arrow-width",type:u.arrowWidth}].forEach(function(z){O.forEach(function(q){var H=q+"-"+z.name,Y=z.type,ae=z.triggersBounds;M.push({name:H,type:Y,triggersBounds:ae})})},{});var V=Ze.properties=[].concat(y,b,g,p,m,B,d,h,v,f,c,E,C,x,T,A,L,k,D,M,P),G=Ze.propertyGroups={behavior:y,transition:b,visibility:g,overlay:p,underlay:m,ghost:B,commonLabel:d,labelDimensions:h,mainLabel:v,sourceLabel:f,targetLabel:c,nodeBody:E,nodeBorder:C,nodeOutline:x,backgroundImage:T,pie:A,stripe:L,compound:k,edgeLine:D,edgeArrow:M,core:P},N=Ze.propertyGroupNames={},F=Ze.propertyGroupKeys=Object.keys(G);F.forEach(function(z){N[z]=G[z].map(function(q){return q.name}),G[z].forEach(function(q){return q.groupKey=z})});var U=Ze.aliases=[{name:"content",pointsTo:"label"},{name:"control-point-distance",pointsTo:"control-point-distances"},{name:"control-point-weight",pointsTo:"control-point-weights"},{name:"segment-distance",pointsTo:"segment-distances"},{name:"segment-weight",pointsTo:"segment-weights"},{name:"segment-radius",pointsTo:"segment-radii"},{name:"edge-text-rotation",pointsTo:"text-rotation"},{name:"padding-left",pointsTo:"padding"},{name:"padding-right",pointsTo:"padding"},{name:"padding-top",pointsTo:"padding"},{name:"padding-bottom",pointsTo:"padding"}];Ze.propertyNames=V.map(function(z){return z.name});for(var Q=0;Q<V.length;Q++){var K=V[Q];V[K.name]=K}for(var j=0;j<U.length;j++){var re=U[j],ne=V[re.pointsTo],J={name:re.name,alias:!0,pointsTo:ne};V.push(J),V[re.name]=J}})(),Ze.getDefaultProperty=function(r){return this.getDefaultProperties()[r]},Ze.getDefaultProperties=function(){var r=this._private;if(r.defaultProperties!=null)return r.defaultProperties;for(var e=me({"selection-box-color":"#ddd","selection-box-opacity":.65,"selection-box-border-color":"#aaa","selection-box-border-width":1,"active-bg-color":"black","active-bg-opacity":.15,"active-bg-size":30,"outside-texture-bg-color":"#000","outside-texture-bg-opacity":.125,events:"yes","text-events":"no","text-valign":"top","text-halign":"center","text-justification":"auto","line-height":1,color:"#000","box-selection":"contain","text-outline-color":"#000","text-outline-width":0,"text-outline-opacity":1,"text-opacity":1,"text-decoration":"none","text-transform":"none","text-wrap":"none","text-overflow-wrap":"whitespace","text-max-width":9999,"text-background-color":"#000","text-background-opacity":0,"text-background-shape":"rectangle","text-background-padding":0,"text-border-opacity":0,"text-border-width":0,"text-border-style":"solid","text-border-color":"#000","font-family":"Helvetica Neue, Helvetica, sans-serif","font-style":"normal","font-weight":"normal","font-size":16,"min-zoomed-font-size":0,"text-rotation":"none","source-text-rotation":"none","target-text-rotation":"none",visibility:"visible",display:"element",opacity:1,"z-compound-depth":"auto","z-index-compare":"auto","z-index":0,label:"","text-margin-x":0,"text-margin-y":0,"source-label":"","source-text-offset":0,"source-text-margin-x":0,"source-text-margin-y":0,"target-label":"","target-text-offset":0,"target-text-margin-x":0,"target-text-margin-y":0,"overlay-opacity":0,"overlay-color":"#000","overlay-padding":10,"overlay-shape":"round-rectangle","overlay-corner-radius":"auto","underlay-opacity":0,"underlay-color":"#000","underlay-padding":10,"underlay-shape":"round-rectangle","underlay-corner-radius":"auto","transition-property":"none","transition-duration":0,"transition-delay":0,"transition-timing-function":"linear","box-select-labels":"no","background-blacken":0,"background-color":"#999","background-fill":"solid","background-opacity":1,"background-image":"none","background-image-crossorigin":"anonymous","background-image-opacity":1,"background-image-containment":"inside","background-image-smoothing":"yes","background-position-x":"50%","background-position-y":"50%","background-offset-x":0,"background-offset-y":0,"background-width-relative-to":"include-padding","background-height-relative-to":"include-padding","background-repeat":"no-repeat","background-fit":"none","background-clip":"node","background-width":"auto","background-height":"auto","border-color":"#000","border-opacity":1,"border-width":0,"border-style":"solid","border-dash-pattern":[4,2],"border-dash-offset":0,"border-cap":"butt","border-join":"miter","border-position":"center","outline-color":"#999","outline-opacity":1,"outline-width":0,"outline-offset":0,"outline-style":"solid",height:30,width:30,shape:"ellipse","shape-polygon-points":"-1, -1,   1, -1,   1, 1,   -1, 1","corner-radius":"auto","bounds-expansion":0,"background-gradient-direction":"to-bottom","background-gradient-stop-colors":"#999","background-gradient-stop-positions":"0%",ghost:"no","ghost-offset-y":0,"ghost-offset-x":0,"ghost-opacity":0,padding:0,"padding-relative-to":"width",position:"origin","compound-sizing-wrt-labels":"include","min-width":0,"min-width-bias-left":0,"min-width-bias-right":0,"min-height":0,"min-height-bias-top":0,"min-height-bias-bottom":0},{"pie-size":"100%","pie-hole":0,"pie-start-angle":"0deg"},[{name:"pie-{{i}}-background-color",value:"black"},{name:"pie-{{i}}-background-size",value:"0%"},{name:"pie-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Ze.pieBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"stripe-size":"100%","stripe-direction":"horizontal"},[{name:"stripe-{{i}}-background-color",value:"black"},{name:"stripe-{{i}}-background-size",value:"0%"},{name:"stripe-{{i}}-background-opacity",value:1}].reduce(function(l,u){for(var v=1;v<=Ze.stripeBackgroundN;v++){var f=u.name.replace("{{i}}",v),c=u.value;l[f]=c}return l},{}),{"line-style":"solid","line-color":"#999","line-fill":"solid","line-cap":"butt","line-opacity":1,"line-outline-width":0,"line-outline-color":"#000","line-gradient-stop-colors":"#999","line-gradient-stop-positions":"0%","control-point-step-size":40,"control-point-weights":.5,"segment-weights":.5,"segment-distances":20,"segment-radii":15,"radius-type":"arc-radius","taxi-turn":"50%","taxi-radius":15,"taxi-turn-min-distance":10,"taxi-direction":"auto","edge-distances":"intersection","curve-style":"haystack","haystack-radius":0,"arrow-scale":1,"loop-direction":"-45deg","loop-sweep":"-90deg","source-distance-from-node":0,"target-distance-from-node":0,"source-endpoint":"outside-to-node","target-endpoint":"outside-to-node","line-dash-pattern":[6,3],"line-dash-offset":0},[{name:"arrow-shape",value:"none"},{name:"arrow-color",value:"#999"},{name:"arrow-fill",value:"filled"},{name:"arrow-width",value:1}].reduce(function(l,u){return Ze.arrowPrefixes.forEach(function(v){var f=v+"-"+u.name,c=u.value;l[f]=c}),l},{})),t={},a=0;a<this.properties.length;a++){var n=this.properties[a];if(!n.pointsTo){var i=n.name,s=e[i],o=this.parse(i,s);t[i]=o}}return r.defaultProperties=t,r.defaultProperties},Ze.addDefaultStylesheet=function(){this.selector(":parent").css({shape:"rectangle",padding:10,"background-color":"#eee","border-color":"#ccc","border-width":1}).selector("edge").css({width:3}).selector(":loop").css({"curve-style":"bezier"}).selector("edge:compound").css({"curve-style":"bezier","source-endpoint":"outside-to-line","target-endpoint":"outside-to-line"}).selector(":selected").css({"background-color":"#0169D9","line-color":"#0169D9","source-arrow-color":"#0169D9","target-arrow-color":"#0169D9","mid-source-arrow-color":"#0169D9","mid-target-arrow-color":"#0169D9"}).selector(":parent:selected").css({"background-color":"#CCE1F9","border-color":"#aec8e5"}).selector(":active").css({"overlay-color":"black","overlay-padding":10,"overlay-opacity":.25}),this.defaultLength=this.length};var Mn={};Mn.parse=function(r,e,t,a){var n=this;if(Ue(e))return n.parseImplWarn(r,e,t,a);var i=a==="mapping"||a===!0||a===!1||a==null?"dontcare":a,s=t?"t":"f",o=""+e,l=Qo(r,o,s,i),u=n.propCache=n.propCache||[],v;return(v=u[l])||(v=u[l]=n.parseImplWarn(r,e,t,a)),(t||a==="mapping")&&(v=zr(v),v&&(v.value=zr(v.value))),v},Mn.parseImplWarn=function(r,e,t,a){var n=this.parseImpl(r,e,t,a);return!n&&e!=null&&Oe("The style property `".concat(r,": ").concat(e,"` is invalid")),n&&(n.name==="width"||n.name==="height")&&e==="label"&&Oe("The style value of `label` is deprecated for `"+n.name+"`"),n},Mn.parseImpl=function(r,e,t,a){var n=this;r=jn(r);var i=n.properties[r],s=e,o=n.types;if(!i||e===void 0)return null;i.alias&&(i=i.pointsTo,r=i.name);var l=he(e);l&&(e=e.trim());var u=i.type;if(!u)return null;if(t&&(e===""||e===null))return{name:r,value:e,bypass:!0,deleteBypass:!0};if(Ue(e))return{name:r,value:e,strValue:"fn",mapped:o.fn,bypass:t};var v,f;if(!(!l||a||e.length<7||e[1]!=="a")){if(e.length>=7&&e[0]==="d"&&(v=new RegExp(o.data.regex).exec(e))){if(t)return!1;var c=o.data;return{name:r,value:v,strValue:""+e,mapped:c,field:v[1],bypass:t}}else if(e.length>=10&&e[0]==="m"&&(f=new RegExp(o.mapData.regex).exec(e))){if(t||u.multiple)return!1;var h=o.mapData;if(!(u.color||u.number))return!1;var d=this.parse(r,f[4]);if(!d||d.mapped)return!1;var y=this.parse(r,f[5]);if(!y||y.mapped)return!1;if(d.pfValue===y.pfValue||d.strValue===y.strValue)return Oe("`"+r+": "+e+"` is not a valid mapper because the output range is zero; converting to `"+r+": "+d.strValue+"`"),this.parse(r,d.strValue);if(u.color){var g=d.value,p=y.value,m=g[0]===p[0]&&g[1]===p[1]&&g[2]===p[2]&&(g[3]===p[3]||(g[3]==null||g[3]===1)&&(p[3]==null||p[3]===1));if(m)return!1}return{name:r,value:f,strValue:""+e,mapped:h,field:f[1],fieldMin:parseFloat(f[2]),fieldMax:parseFloat(f[3]),valueMin:d.value,valueMax:y.value,bypass:t}}}if(u.multiple&&a!=="multiple"){var b;if(l?b=e.split(/\s+/):_e(e)?b=e:b=[e],u.evenMultiple&&b.length%2!==0)return null;for(var w=[],E=[],C=[],x="",T=!1,k=0;k<b.length;k++){var D=n.parse(r,b[k],t,"multiple");T=T||he(D.value),w.push(D.value),C.push(D.pfValue!=null?D.pfValue:D.value),E.push(D.units),x+=(k>0?" ":"")+D.strValue}return u.validate&&!u.validate(w,E)?null:u.singleEnum&&T?w.length===1&&he(w[0])?{name:r,value:w[0],strValue:w[0],bypass:t}:null:{name:r,value:w,pfValue:C,strValue:x,bypass:t,units:E}}var B=function(){for(var J=0;J<u.enums.length;J++){var z=u.enums[J];if(z===e)return{name:r,value:e,strValue:""+e,bypass:t}}return null};if(u.number){var P,A="px";if(u.units&&(P=u.units),u.implicitUnits&&(A=u.implicitUnits),!u.unitless)if(l){var R="px|em"+(u.allowPercent?"|\\%":"");P&&(R=P);var L=e.match("^("+rr+")("+R+")?$");L&&(e=L[1],P=L[2]||A)}else(!P||u.implicitUnits)&&(P=A);if(e=parseFloat(e),isNaN(e)&&u.enums===void 0)return null;if(isNaN(e)&&u.enums!==void 0)return e=s,B();if(u.integer&&!nc(e)||u.min!==void 0&&(e<u.min||u.strictMin&&e===u.min)||u.max!==void 0&&(e>u.max||u.strictMax&&e===u.max))return null;var I={name:r,value:e,strValue:""+e+(P||""),units:P,bypass:t};return u.unitless||P!=="px"&&P!=="em"?I.pfValue=e:I.pfValue=P==="px"||!P?e:this.getEmSizeInPixels()*e,(P==="ms"||P==="s")&&(I.pfValue=P==="ms"?e:1e3*e),(P==="deg"||P==="rad")&&(I.pfValue=P==="rad"?e:hd(e)),P==="%"&&(I.pfValue=e/100),I}else if(u.propList){var M=[],O=""+e;if(O!=="none"){for(var V=O.split(/\s*,\s*|\s+/),G=0;G<V.length;G++){var N=V[G].trim();n.properties[N]?M.push(N):Oe("`"+N+"` is not a valid property name")}if(M.length===0)return null}return{name:r,value:M,strValue:M.length===0?"none":M.join(" "),bypass:t}}else if(u.color){var F=Po(e);return F?{name:r,value:F,pfValue:F,strValue:"rgb("+F[0]+","+F[1]+","+F[2]+")",bypass:t}:null}else if(u.regex||u.regexes){if(u.enums){var U=B();if(U)return U}for(var Q=u.regexes?u.regexes:[u.regex],K=0;K<Q.length;K++){var j=new RegExp(Q[K]),re=j.exec(e);if(re)return{name:r,value:u.singleRegexMatchValue?re[1]:re,strValue:""+e,bypass:t}}return null}else return u.string?{name:r,value:""+e,strValue:""+e,bypass:t}:u.enums?B():null};var or=function(e){if(!(this instanceof or))return new or(e);if(!Jn(e)){$e("A style must have a core reference");return}this._private={cy:e,coreStyle:{}},this.length=0,this.resetToDefault()},gr=or.prototype;gr.instanceString=function(){return"style"},gr.clear=function(){for(var r=this._private,e=r.cy,t=e.elements(),a=0;a<this.length;a++)this[a]=void 0;return this.length=0,r.contextStyles={},r.propDiffs={},this.cleanElements(t,!0),t.forEach(function(n){var i=n[0]._private;i.styleDirty=!0,i.appliedInitStyle=!1}),this},gr.resetToDefault=function(){return this.clear(),this.addDefaultStylesheet(),this},gr.core=function(r){return this._private.coreStyle[r]||this.getDefaultProperty(r)},gr.selector=function(r){var e=r==="core"?null:new lt(r),t=this.length++;return this[t]={selector:e,properties:[],mappedProperties:[],index:t},this},gr.css=function(){var r=this,e=arguments;if(e.length===1)for(var t=e[0],a=0;a<r.properties.length;a++){var n=r.properties[a],i=t[n.name];i===void 0&&(i=t[Ka(n.name)]),i!==void 0&&this.cssRule(n.name,i)}else e.length===2&&this.cssRule(e[0],e[1]);return this},gr.style=gr.css,gr.cssRule=function(r,e){var t=this.parse(r,e);if(t){var a=this.length-1;this[a].properties.push(t),this[a].properties[t.name]=t,t.name.match(/pie-(\d+)-background-size/)&&t.value&&(this._private.hasPie=!0),t.name.match(/stripe-(\d+)-background-size/)&&t.value&&(this._private.hasStripe=!0),t.mapped&&this[a].mappedProperties.push(t);var n=!this[a].selector;n&&(this._private.coreStyle[t.name]=t)}return this},gr.append=function(r){return So(r)?r.appendToStyle(this):_e(r)?this.appendFromJson(r):he(r)&&this.appendFromString(r),this},or.fromJson=function(r,e){var t=new or(r);return t.fromJson(e),t},or.fromString=function(r,e){return new or(r).fromString(e)},[sr,Pa,$s,Fr,Rn,Us,Ze,Mn].forEach(function(r){me(gr,r)}),or.types=gr.types,or.properties=gr.properties,or.propertyGroups=gr.propertyGroups,or.propertyGroupNames=gr.propertyGroupNames,or.propertyGroupKeys=gr.propertyGroupKeys;var mp={style:function(e){if(e){var t=this.setStyle(e);t.update()}return this._private.style},setStyle:function(e){var t=this._private;return So(e)?t.style=e.generateStyle(this):_e(e)?t.style=or.fromJson(this,e):he(e)?t.style=or.fromString(this,e):t.style=or(this),t.style},updateStyle:function(){this.mutableElements().updateStyle()}},bp="single",St={autolock:function(e){if(e!==void 0)this._private.autolock=!!e;else return this._private.autolock;return this},autoungrabify:function(e){if(e!==void 0)this._private.autoungrabify=!!e;else return this._private.autoungrabify;return this},autounselectify:function(e){if(e!==void 0)this._private.autounselectify=!!e;else return this._private.autounselectify;return this},selectionType:function(e){var t=this._private;if(t.selectionType==null&&(t.selectionType=bp),e!==void 0)(e==="additive"||e==="single")&&(t.selectionType=e);else return t.selectionType;return this},panningEnabled:function(e){if(e!==void 0)this._private.panningEnabled=!!e;else return this._private.panningEnabled;return this},userPanningEnabled:function(e){if(e!==void 0)this._private.userPanningEnabled=!!e;else return this._private.userPanningEnabled;return this},zoomingEnabled:function(e){if(e!==void 0)this._private.zoomingEnabled=!!e;else return this._private.zoomingEnabled;return this},userZoomingEnabled:function(e){if(e!==void 0)this._private.userZoomingEnabled=!!e;else return this._private.userZoomingEnabled;return this},boxSelectionEnabled:function(e){if(e!==void 0)this._private.boxSelectionEnabled=!!e;else return this._private.boxSelectionEnabled;return this},pan:function(){var e=arguments,t=this._private.pan,a,n,i,s,o;switch(e.length){case 0:return t;case 1:if(he(e[0]))return a=e[0],t[a];if(Me(e[0])){if(!this._private.panningEnabled)return this;i=e[0],s=i.x,o=i.y,te(s)&&(t.x=s),te(o)&&(t.y=o),this.emit("pan viewport")}break;case 2:if(!this._private.panningEnabled)return this;a=e[0],n=e[1],(a==="x"||a==="y")&&te(n)&&(t[a]=n),this.emit("pan viewport");break}return this.notify("viewport"),this},panBy:function(e,t){var a=arguments,n=this._private.pan,i,s,o,l,u;if(!this._private.panningEnabled)return this;switch(a.length){case 1:Me(e)&&(o=a[0],l=o.x,u=o.y,te(l)&&(n.x+=l),te(u)&&(n.y+=u),this.emit("pan viewport"));break;case 2:i=e,s=t,(i==="x"||i==="y")&&te(s)&&(n[i]+=s),this.emit("pan viewport");break}return this.notify("viewport"),this},gc:function(){this.notify("gc")},fit:function(e,t){var a=this.getFitViewport(e,t);if(a){var n=this._private;n.zoom=a.zoom,n.pan=a.pan,this.emit("pan zoom viewport"),this.notify("viewport")}return this},getFitViewport:function(e,t){if(te(e)&&t===void 0&&(t=e,e=void 0),!(!this._private.panningEnabled||!this._private.zoomingEnabled)){var a;if(he(e)){var n=e;e=this.$(n)}else if(oc(e)){var i=e;a={x1:i.x1,y1:i.y1,x2:i.x2,y2:i.y2},a.w=a.x2-a.x1,a.h=a.y2-a.y1}else Dr(e)||(e=this.mutableElements());if(!(Dr(e)&&e.empty())){a=a||e.boundingBox();var s=this.width(),o=this.height(),l;if(t=te(t)?t:0,!isNaN(s)&&!isNaN(o)&&s>0&&o>0&&!isNaN(a.w)&&!isNaN(a.h)&&a.w>0&&a.h>0){l=Math.min((s-2*t)/a.w,(o-2*t)/a.h),l=l>this._private.maxZoom?this._private.maxZoom:l,l=l<this._private.minZoom?this._private.minZoom:l;var u={x:(s-l*(a.x1+a.x2))/2,y:(o-l*(a.y1+a.y2))/2};return{zoom:l,pan:u}}}}},zoomRange:function(e,t){var a=this._private;if(t==null){var n=e;e=n.min,t=n.max}return te(e)&&te(t)&&e<=t?(a.minZoom=e,a.maxZoom=t):te(e)&&t===void 0&&e<=a.maxZoom?a.minZoom=e:te(t)&&e===void 0&&t>=a.minZoom&&(a.maxZoom=t),this},minZoom:function(e){return e===void 0?this._private.minZoom:this.zoomRange({min:e})},maxZoom:function(e){return e===void 0?this._private.maxZoom:this.zoomRange({max:e})},getZoomedViewport:function(e){var t=this._private,a=t.pan,n=t.zoom,i,s,o=!1;if(t.zoomingEnabled||(o=!0),te(e)?s=e:Me(e)&&(s=e.level,e.position!=null?i=rn(e.position,n,a):e.renderedPosition!=null&&(i=e.renderedPosition),i!=null&&!t.panningEnabled&&(o=!0)),s=s>t.maxZoom?t.maxZoom:s,s=s<t.minZoom?t.minZoom:s,o||!te(s)||s===n||i!=null&&(!te(i.x)||!te(i.y)))return null;if(i!=null){var l=a,u=n,v=s,f={x:-v/u*(i.x-l.x)+i.x,y:-v/u*(i.y-l.y)+i.y};return{zoomed:!0,panned:!0,zoom:v,pan:f}}else return{zoomed:!0,panned:!1,zoom:s,pan:a}},zoom:function(e){if(e===void 0)return this._private.zoom;var t=this.getZoomedViewport(e),a=this._private;return t==null||!t.zoomed?this:(a.zoom=t.zoom,t.panned&&(a.pan.x=t.pan.x,a.pan.y=t.pan.y),this.emit("zoom"+(t.panned?" pan":"")+" viewport"),this.notify("viewport"),this)},viewport:function(e){var t=this._private,a=!0,n=!0,i=[],s=!1,o=!1;if(!e)return this;if(te(e.zoom)||(a=!1),Me(e.pan)||(n=!1),!a&&!n)return this;if(a){var l=e.zoom;l<t.minZoom||l>t.maxZoom||!t.zoomingEnabled?s=!0:(t.zoom=l,i.push("zoom"))}if(n&&(!s||!e.cancelOnFailedZoom)&&t.panningEnabled){var u=e.pan;te(u.x)&&(t.pan.x=u.x,o=!1),te(u.y)&&(t.pan.y=u.y,o=!1),o||i.push("pan")}return i.length>0&&(i.push("viewport"),this.emit(i.join(" ")),this.notify("viewport")),this},center:function(e){var t=this.getCenterPan(e);return t&&(this._private.pan=t,this.emit("pan viewport"),this.notify("viewport")),this},getCenterPan:function(e,t){if(this._private.panningEnabled){if(he(e)){var a=e;e=this.mutableElements().filter(a)}else Dr(e)||(e=this.mutableElements());if(e.length!==0){var n=e.boundingBox(),i=this.width(),s=this.height();t=t===void 0?this._private.zoom:t;var o={x:(i-t*(n.x1+n.x2))/2,y:(s-t*(n.y1+n.y2))/2};return o}}},reset:function(){return!this._private.panningEnabled||!this._private.zoomingEnabled?this:(this.viewport({pan:{x:0,y:0},zoom:1}),this)},invalidateSize:function(){this._private.sizeCache=null},size:function(){var e=this._private,t=e.container,a=this;return e.sizeCache=e.sizeCache||(t?function(){var n=a.window().getComputedStyle(t),i=function(o){return parseFloat(n.getPropertyValue(o))};return{width:t.clientWidth-i("padding-left")-i("padding-right"),height:t.clientHeight-i("padding-top")-i("padding-bottom")}}():{width:1,height:1})},width:function(){return this.size().width},height:function(){return this.size().height},extent:function(){var e=this._private.pan,t=this._private.zoom,a=this.renderedExtent(),n={x1:(a.x1-e.x)/t,x2:(a.x2-e.x)/t,y1:(a.y1-e.y)/t,y2:(a.y2-e.y)/t};return n.w=n.x2-n.x1,n.h=n.y2-n.y1,n},renderedExtent:function(){var e=this.width(),t=this.height();return{x1:0,y1:0,x2:e,y2:t,w:e,h:t}},multiClickDebounceTime:function(e){if(e)this._private.multiClickDebounceTime=e;else return this._private.multiClickDebounceTime;return this}};St.centre=St.center,St.autolockNodes=St.autolock,St.autoungrabifyNodes=St.autoungrabify;var Aa={data:Ne.data({field:"data",bindingEvent:"data",allowBinding:!0,allowSetting:!0,settingEvent:"data",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeData:Ne.removeData({field:"data",event:"data",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0}),scratch:Ne.data({field:"scratch",bindingEvent:"scratch",allowBinding:!0,allowSetting:!0,settingEvent:"scratch",settingTriggersEvent:!0,triggerFnName:"trigger",allowGetting:!0,updateStyle:!0}),removeScratch:Ne.removeData({field:"scratch",event:"scratch",triggerFnName:"trigger",triggerEvent:!0,updateStyle:!0})};Aa.attr=Aa.data,Aa.removeAttr=Aa.removeData;var Ra=function(e){var t=this;e=me({},e);var a=e.container;a&&!Ua(a)&&Ua(a[0])&&(a=a[0]);var n=a?a._cyreg:null;n=n||{},n&&n.cy&&(n.cy.destroy(),n={});var i=n.readies=n.readies||[];a&&(a._cyreg=n),n.cy=t;var s=er!==void 0&&a!==void 0&&!e.headless,o=e;o.layout=me({name:s?"grid":"null"},o.layout),o.renderer=me({name:s?"canvas":"null"},o.renderer);var l=function(d,y,g){return y!==void 0?y:g!==void 0?g:d},u=this._private={container:a,ready:!1,options:o,elements:new fr(this),listeners:[],aniEles:new fr(this),data:o.data||{},scratch:{},layout:null,renderer:null,destroyed:!1,notificationsEnabled:!0,minZoom:1e-50,maxZoom:1e50,zoomingEnabled:l(!0,o.zoomingEnabled),userZoomingEnabled:l(!0,o.userZoomingEnabled),panningEnabled:l(!0,o.panningEnabled),userPanningEnabled:l(!0,o.userPanningEnabled),boxSelectionEnabled:l(!0,o.boxSelectionEnabled),autolock:l(!1,o.autolock,o.autolockNodes),autoungrabify:l(!1,o.autoungrabify,o.autoungrabifyNodes),autounselectify:l(!1,o.autounselectify),styleEnabled:o.styleEnabled===void 0?s:o.styleEnabled,zoom:te(o.zoom)?o.zoom:1,pan:{x:Me(o.pan)&&te(o.pan.x)?o.pan.x:0,y:Me(o.pan)&&te(o.pan.y)?o.pan.y:0},animation:{current:[],queue:[]},hasCompoundNodes:!1,multiClickDebounceTime:l(250,o.multiClickDebounceTime)};this.createEmitter(),this.selectionType(o.selectionType),this.zoomRange({min:o.minZoom,max:o.maxZoom});var v=function(d,y){var g=d.some(uc);if(g)return Wt.all(d).then(y);y(d)};u.styleEnabled&&t.setStyle([]);var f=me({},o,o.renderer);t.initRenderer(f);var c=function(d,y,g){t.notifications(!1);var p=t.mutableElements();p.length>0&&p.remove(),d!=null&&(Me(d)||_e(d))&&t.add(d),t.one("layoutready",function(b){t.notifications(!0),t.emit(b),t.one("load",y),t.emitAndNotify("load")}).one("layoutstop",function(){t.one("done",g),t.emit("done")});var m=me({},t._private.options.layout);m.eles=t.elements(),t.layout(m).run()};v([o.style,o.elements],function(h){var d=h[0],y=h[1];u.styleEnabled&&t.style().append(d),c(y,function(){t.startAnimationLoop(),u.ready=!0,Ue(o.ready)&&t.on("ready",o.ready);for(var g=0;g<i.length;g++){var p=i[g];t.on("ready",p)}n&&(n.readies=[]),t.emit("ready")},o.done)})},Ln=Ra.prototype;me(Ln,{instanceString:function(){return"core"},isReady:function(){return this._private.ready},destroyed:function(){return this._private.destroyed},ready:function(e){return this.isReady()?this.emitter().emit("ready",[],e):this.on("ready",e),this},destroy:function(){var e=this;if(!e.destroyed())return e.stopAnimationLoop(),e.destroyRenderer(),this.emit("destroy"),e._private.destroyed=!0,e},hasElementWithId:function(e){return this._private.elements.hasElementWithId(e)},getElementById:function(e){return this._private.elements.getElementById(e)},hasCompoundNodes:function(){return this._private.hasCompoundNodes},headless:function(){return this._private.renderer.isHeadless()},styleEnabled:function(){return this._private.styleEnabled},addToPool:function(e){return this._private.elements.merge(e),this},removeFromPool:function(e){return this._private.elements.unmerge(e),this},container:function(){return this._private.container||null},window:function(){var e=this._private.container;if(e==null)return er;var t=this._private.container.ownerDocument;return t===void 0||t==null?er:t.defaultView||er},mount:function(e){if(e!=null){var t=this,a=t._private,n=a.options;return!Ua(e)&&Ua(e[0])&&(e=e[0]),t.stopAnimationLoop(),t.destroyRenderer(),a.container=e,a.styleEnabled=!0,t.invalidateSize(),t.initRenderer(me({},n,n.renderer,{name:n.renderer.name==="null"?"canvas":n.renderer.name})),t.startAnimationLoop(),t.style(n.style),t.emit("mount"),t}},unmount:function(){var e=this;return e.stopAnimationLoop(),e.destroyRenderer(),e.initRenderer({name:"null"}),e.emit("unmount"),e},options:function(){return zr(this._private.options)},json:function(e){var t=this,a=t._private,n=t.mutableElements(),i=function(w){return t.getElementById(w.id())};if(Me(e)){if(t.startBatch(),e.elements){var s={},o=function(w,E){for(var C=[],x=[],T=0;T<w.length;T++){var k=w[T];if(!k.data.id){Oe("cy.json() cannot handle elements without an ID attribute");continue}var D=""+k.data.id,B=t.getElementById(D);s[D]=!0,B.length!==0?x.push({ele:B,json:k}):(E&&(k.group=E),C.push(k))}t.add(C);for(var P=0;P<x.length;P++){var A=x[P],R=A.ele,L=A.json;R.json(L)}};if(_e(e.elements))o(e.elements);else for(var l=["nodes","edges"],u=0;u<l.length;u++){var v=l[u],f=e.elements[v];_e(f)&&o(f,v)}var c=t.collection();n.filter(function(b){return!s[b.id()]}).forEach(function(b){b.isParent()?c.merge(b):b.remove()}),c.forEach(function(b){return b.children().move({parent:null})}),c.forEach(function(b){return i(b).remove()})}e.style&&t.style(e.style),e.zoom!=null&&e.zoom!==a.zoom&&t.zoom(e.zoom),e.pan&&(e.pan.x!==a.pan.x||e.pan.y!==a.pan.y)&&t.pan(e.pan),e.data&&t.data(e.data);for(var h=["minZoom","maxZoom","zoomingEnabled","userZoomingEnabled","panningEnabled","userPanningEnabled","boxSelectionEnabled","autolock","autoungrabify","autounselectify","multiClickDebounceTime"],d=0;d<h.length;d++){var y=h[d];e[y]!=null&&t[y](e[y])}return t.endBatch(),this}else{var g=!!e,p={};g?p.elements=this.elements().map(function(b){return b.json()}):(p.elements={},n.forEach(function(b){var w=b.group();p.elements[w]||(p.elements[w]=[]),p.elements[w].push(b.json())})),this._private.styleEnabled&&(p.style=t.style().json()),p.data=zr(t.data());var m=a.options;return p.zoomingEnabled=a.zoomingEnabled,p.userZoomingEnabled=a.userZoomingEnabled,p.zoom=a.zoom,p.minZoom=a.minZoom,p.maxZoom=a.maxZoom,p.panningEnabled=a.panningEnabled,p.userPanningEnabled=a.userPanningEnabled,p.pan=zr(a.pan),p.boxSelectionEnabled=a.boxSelectionEnabled,p.renderer=zr(m.renderer),p.hideEdgesOnViewport=m.hideEdgesOnViewport,p.textureOnViewport=m.textureOnViewport,p.wheelSensitivity=m.wheelSensitivity,p.motionBlur=m.motionBlur,p.multiClickDebounceTime=m.multiClickDebounceTime,p}}}),Ln.$id=Ln.getElementById,[up,dp,Tv,Hs,Pn,gp,Ws,An,mp,St,Aa].forEach(function(r){me(Ln,r)});var wp={fit:!0,directed:!1,direction:"downward",padding:30,circle:!1,grid:!1,spacingFactor:1.75,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,roots:void 0,depthSort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}},xp={maximal:!1,acyclic:!1},Jt=function(e){return e.scratch("breadthfirst")},Sv=function(e,t){return e.scratch("breadthfirst",t)};function kv(r){this.options=me({},wp,xp,r)}kv.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=t.nodes().filter(function(ye){return ye.isChildless()}),n=t,i=r.directed,s=r.acyclic||r.maximal||r.maximalAdjustments>0,o=!!r.boundingBox,l=mr(o?r.boundingBox:structuredClone(e.extent())),u;if(Dr(r.roots))u=r.roots;else if(_e(r.roots)){for(var v=[],f=0;f<r.roots.length;f++){var c=r.roots[f],h=e.getElementById(c);v.push(h)}u=e.collection(v)}else if(he(r.roots))u=e.$(r.roots);else if(i)u=a.roots();else{var d=t.components();u=e.collection();for(var y=function(){var ie=d[g],de=ie.maxDegree(!1),ge=ie.filter(function(Ee){return Ee.degree(!1)===de});u=u.add(ge)},g=0;g<d.length;g++)y()}var p=[],m={},b=function(ie,de){p[de]==null&&(p[de]=[]);var ge=p[de].length;p[de].push(ie),Sv(ie,{index:ge,depth:de})},w=function(ie,de){var ge=Jt(ie),Ee=ge.depth,pe=ge.index;p[Ee][pe]=null,ie.isChildless()&&b(ie,de)};n.bfs({roots:u,directed:r.directed,visit:function(ie,de,ge,Ee,pe){var ke=ie[0],Re=ke.id();ke.isChildless()&&b(ke,pe),m[Re]=!0}});for(var E=[],C=0;C<a.length;C++){var x=a[C];m[x.id()]||E.push(x)}var T=function(ie){for(var de=p[ie],ge=0;ge<de.length;ge++){var Ee=de[ge];if(Ee==null){de.splice(ge,1),ge--;continue}Sv(Ee,{depth:ie,index:ge})}},k=function(ie,de){for(var ge=Jt(ie),Ee=ie.incomers().filter(function(xe){return xe.isNode()&&t.has(xe)}),pe=-1,ke=ie.id(),Re=0;Re<Ee.length;Re++){var ze=Ee[Re],Fe=Jt(ze);pe=Math.max(pe,Fe.depth)}if(ge.depth<=pe){if(!r.acyclic&&de[ke])return null;var Ve=pe+1;return w(ie,Ve),de[ke]=Ve,!0}return!1};if(i&&s){var D=[],B={},P=function(ie){return D.push(ie)},A=function(){return D.shift()};for(a.forEach(function(ye){return D.push(ye)});D.length>0;){var R=A(),L=k(R,B);if(L)R.outgoers().filter(function(ye){return ye.isNode()&&t.has(ye)}).forEach(P);else if(L===null){Oe("Detected double maximal shift for node `"+R.id()+"`.  Bailing maximal adjustment due to cycle.  Use `options.maximal: true` only on DAGs.");break}}}var I=0;if(r.avoidOverlap)for(var M=0;M<a.length;M++){var O=a[M],V=O.layoutDimensions(r),G=V.w,N=V.h;I=Math.max(I,G,N)}var F={},U=function(ie){if(F[ie.id()])return F[ie.id()];for(var de=Jt(ie).depth,ge=ie.neighborhood(),Ee=0,pe=0,ke=0;ke<ge.length;ke++){var Re=ge[ke];if(!(Re.isEdge()||Re.isParent()||!a.has(Re))){var ze=Jt(Re);if(ze!=null){var Fe=ze.index,Ve=ze.depth;if(!(Fe==null||Ve==null)){var xe=p[Ve].length;Ve<de&&(Ee+=Fe/xe,pe++)}}}}return pe=Math.max(1,pe),Ee=Ee/pe,pe===0&&(Ee=0),F[ie.id()]=Ee,Ee},Q=function(ie,de){var ge=U(ie),Ee=U(de),pe=ge-Ee;return pe===0?Bo(ie.id(),de.id()):pe};r.depthSort!==void 0&&(Q=r.depthSort);for(var K=p.length,j=0;j<K;j++)p[j].sort(Q),T(j);for(var re=[],ne=0;ne<E.length;ne++)re.push(E[ne]);var J=function(){for(var ie=0;ie<K;ie++)T(ie)};re.length&&(p.unshift(re),K=p.length,J());for(var z=0,q=0;q<K;q++)z=Math.max(p[q].length,z);var H={x:l.x1+l.w/2,y:l.y1+l.h/2},Y=a.reduce(function(ye,ie){return function(de){return{w:ye.w===-1?de.w:(ye.w+de.w)/2,h:ye.h===-1?de.h:(ye.h+de.h)/2}}(ie.boundingBox({includeLabels:r.nodeDimensionsIncludeLabels}))},{w:-1,h:-1}),ae=Math.max(K===1?0:o?(l.h-r.padding*2-Y.h)/(K-1):(l.h-r.padding*2-Y.h)/(K+1),I),ce=p.reduce(function(ye,ie){return Math.max(ye,ie.length)},0),Ae=function(ie){var de=Jt(ie),ge=de.depth,Ee=de.index;if(r.circle){var pe=Math.min(l.w/2/K,l.h/2/K);pe=Math.max(pe,I);var ke=pe*ge+pe-(K>0&&p[0].length<=3?pe/2:0),Re=2*Math.PI/p[ge].length*Ee;return ge===0&&p[0].length===1&&(ke=1),{x:H.x+ke*Math.cos(Re),y:H.y+ke*Math.sin(Re)}}else{var ze=p[ge].length,Fe=Math.max(ze===1?0:o?(l.w-r.padding*2-Y.w)/((r.grid?ce:ze)-1):(l.w-r.padding*2-Y.w)/((r.grid?ce:ze)+1),I),Ve={x:H.x+(Ee+1-(ze+1)/2)*Fe,y:H.y+(ge+1-(K+1)/2)*ae};return Ve}},Ce={downward:0,leftward:90,upward:180,rightward:-90};Object.keys(Ce).indexOf(r.direction)===-1&&$e("Invalid direction '".concat(r.direction,"' specified for breadthfirst layout. Valid values are: ").concat(Object.keys(Ce).join(", ")));var we=function(ie){return zc(Ae(ie),l,Ce[r.direction])};return t.nodes().layoutPositions(this,r,we),this};var Ep={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,radius:void 0,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Dv(r){this.options=me({},Ep,r)}Dv.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,i=a.nodes().not(":parent");e.sort&&(i=i.sort(e.sort));for(var s=mr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=e.sweep===void 0?2*Math.PI-2*Math.PI/i.length:e.sweep,u=l/Math.max(1,i.length-1),v,f=0,c=0;c<i.length;c++){var h=i[c],d=h.layoutDimensions(e),y=d.w,g=d.h;f=Math.max(f,y,g)}if(te(e.radius)?v=e.radius:i.length<=1?v=0:v=Math.min(s.h,s.w)/2-f,i.length>1&&e.avoidOverlap){f*=1.75;var p=Math.cos(u)-Math.cos(0),m=Math.sin(u)-Math.sin(0),b=Math.sqrt(f*f/(p*p+m*m));v=Math.max(b,v)}var w=function(C,x){var T=e.startAngle+x*u*(n?1:-1),k=v*Math.cos(T),D=v*Math.sin(T),B={x:o.x+k,y:o.y+D};return B};return a.nodes().layoutPositions(this,e,w),this};var Cp={fit:!0,padding:30,startAngle:3/2*Math.PI,sweep:void 0,clockwise:!0,equidistant:!1,minNodeSpacing:10,boundingBox:void 0,avoidOverlap:!0,nodeDimensionsIncludeLabels:!1,height:void 0,width:void 0,spacingFactor:void 0,concentric:function(e){return e.degree()},levelWidth:function(e){return e.maxDegree()/4},animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Bv(r){this.options=me({},Cp,r)}Bv.prototype.run=function(){for(var r=this.options,e=r,t=e.counterclockwise!==void 0?!e.counterclockwise:e.clockwise,a=r.cy,n=e.eles,i=n.nodes().not(":parent"),s=mr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:a.width(),h:a.height()}),o={x:s.x1+s.w/2,y:s.y1+s.h/2},l=[],u=0,v=0;v<i.length;v++){var f=i[v],c=void 0;c=e.concentric(f),l.push({value:c,node:f}),f._private.scratch.concentric=c}i.updateStyle();for(var h=0;h<i.length;h++){var d=i[h],y=d.layoutDimensions(e);u=Math.max(u,y.w,y.h)}l.sort(function(ae,ce){return ce.value-ae.value});for(var g=e.levelWidth(i),p=[[]],m=p[0],b=0;b<l.length;b++){var w=l[b];if(m.length>0){var E=Math.abs(m[0].value-w.value);E>=g&&(m=[],p.push(m))}m.push(w)}var C=u+e.minNodeSpacing;if(!e.avoidOverlap){var x=p.length>0&&p[0].length>1,T=Math.min(s.w,s.h)/2-C,k=T/(p.length+x?1:0);C=Math.min(C,k)}for(var D=0,B=0;B<p.length;B++){var P=p[B],A=e.sweep===void 0?2*Math.PI-2*Math.PI/P.length:e.sweep,R=P.dTheta=A/Math.max(1,P.length-1);if(P.length>1&&e.avoidOverlap){var L=Math.cos(R)-Math.cos(0),I=Math.sin(R)-Math.sin(0),M=Math.sqrt(C*C/(L*L+I*I));D=Math.max(M,D)}P.r=D,D+=C}if(e.equidistant){for(var O=0,V=0,G=0;G<p.length;G++){var N=p[G],F=N.r-V;O=Math.max(O,F)}V=0;for(var U=0;U<p.length;U++){var Q=p[U];U===0&&(V=Q.r),Q.r=V,V+=O}}for(var K={},j=0;j<p.length;j++)for(var re=p[j],ne=re.dTheta,J=re.r,z=0;z<re.length;z++){var q=re[z],H=e.startAngle+(t?1:-1)*ne*z,Y={x:o.x+J*Math.cos(H),y:o.y+J*Math.sin(H)};K[q.node.id()]=Y}return n.nodes().layoutPositions(this,e,function(ae){var ce=ae.id();return K[ce]}),this};var Ks,Tp={ready:function(){},stop:function(){},animate:!0,animationEasing:void 0,animationDuration:void 0,animateFilter:function(e,t){return!0},animationThreshold:250,refresh:20,fit:!0,padding:30,boundingBox:void 0,nodeDimensionsIncludeLabels:!1,randomize:!1,componentSpacing:40,nodeRepulsion:function(e){return 2048},nodeOverlap:4,idealEdgeLength:function(e){return 32},edgeElasticity:function(e){return 32},nestingFactor:1.2,gravity:1,numIter:1e3,initialTemp:1e3,coolingFactor:.99,minTemp:1};function In(r){this.options=me({},Tp,r),this.options.layout=this;var e=this.options.eles.nodes(),t=this.options.eles.edges(),a=t.filter(function(n){var i=n.source().data("id"),s=n.target().data("id"),o=e.some(function(u){return u.data("id")===i}),l=e.some(function(u){return u.data("id")===s});return!o||!l});this.options.eles=this.options.eles.not(a)}In.prototype.run=function(){var r=this.options,e=r.cy,t=this;t.stopped=!1,(r.animate===!0||r.animate===!1)&&t.emit({type:"layoutstart",layout:t}),r.debug===!0?Ks=!0:Ks=!1;var a=Sp(e,t,r);Ks&&Dp(a),r.randomize&&Bp(a);var n=Wr(),i=function(){Pp(a,e,r),r.fit===!0&&e.fit(r.padding)},s=function(c){return!(t.stopped||c>=r.numIter||(Ap(a,r),a.temperature=a.temperature*r.coolingFactor,a.temperature<r.minTemp))},o=function(){if(r.animate===!0||r.animate===!1)i(),t.one("layoutstop",r.stop),t.emit({type:"layoutstop",layout:t});else{var c=r.eles.nodes(),h=Av(a,r,c);c.layoutPositions(t,r,h)}},l=0,u=!0;if(r.animate===!0){var v=function(){for(var c=0;u&&c<r.refresh;)u=s(l),l++,c++;if(!u)Lv(a,r),o();else{var h=Wr();h-n>=r.animationThreshold&&i(),Za(v)}};v()}else{for(;u;)u=s(l),l++;Lv(a,r),o()}return this},In.prototype.stop=function(){return this.stopped=!0,this.thread&&this.thread.stop(),this.emit("layoutstop"),this},In.prototype.destroy=function(){return this.thread&&this.thread.stop(),this};var Sp=function(e,t,a){for(var n=a.eles.edges(),i=a.eles.nodes(),s=mr(a.boundingBox?a.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),o={isCompound:e.hasCompoundNodes(),layoutNodes:[],idToIndex:{},nodeSize:i.size(),graphSet:[],indexToGraph:[],layoutEdges:[],edgeSize:n.size(),temperature:a.initialTemp,clientWidth:s.w,clientHeight:s.h,boundingBox:s},l=a.eles.components(),u={},v=0;v<l.length;v++)for(var f=l[v],c=0;c<f.length;c++){var h=f[c];u[h.id()]=v}for(var v=0;v<o.nodeSize;v++){var d=i[v],y=d.layoutDimensions(a),g={};g.isLocked=d.locked(),g.id=d.data("id"),g.parentId=d.data("parent"),g.cmptId=u[d.id()],g.children=[],g.positionX=d.position("x"),g.positionY=d.position("y"),g.offsetX=0,g.offsetY=0,g.height=y.w,g.width=y.h,g.maxX=g.positionX+g.width/2,g.minX=g.positionX-g.width/2,g.maxY=g.positionY+g.height/2,g.minY=g.positionY-g.height/2,g.padLeft=parseFloat(d.style("padding")),g.padRight=parseFloat(d.style("padding")),g.padTop=parseFloat(d.style("padding")),g.padBottom=parseFloat(d.style("padding")),g.nodeRepulsion=Ue(a.nodeRepulsion)?a.nodeRepulsion(d):a.nodeRepulsion,o.layoutNodes.push(g),o.idToIndex[g.id]=v}for(var p=[],m=0,b=-1,w=[],v=0;v<o.nodeSize;v++){var d=o.layoutNodes[v],E=d.parentId;E!=null?o.layoutNodes[o.idToIndex[E]].children.push(d.id):(p[++b]=d.id,w.push(d.id))}for(o.graphSet.push(w);m<=b;){var C=p[m++],x=o.idToIndex[C],h=o.layoutNodes[x],T=h.children;if(T.length>0){o.graphSet.push(T);for(var v=0;v<T.length;v++)p[++b]=T[v]}}for(var v=0;v<o.graphSet.length;v++)for(var k=o.graphSet[v],c=0;c<k.length;c++){var D=o.idToIndex[k[c]];o.indexToGraph[D]=v}for(var v=0;v<o.edgeSize;v++){var B=n[v],P={};P.id=B.data("id"),P.sourceId=B.data("source"),P.targetId=B.data("target");var A=Ue(a.idealEdgeLength)?a.idealEdgeLength(B):a.idealEdgeLength,R=Ue(a.edgeElasticity)?a.edgeElasticity(B):a.edgeElasticity,L=o.idToIndex[P.sourceId],I=o.idToIndex[P.targetId],M=o.indexToGraph[L],O=o.indexToGraph[I];if(M!=O){for(var V=kp(P.sourceId,P.targetId,o),G=o.graphSet[V],N=0,g=o.layoutNodes[L];G.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;for(g=o.layoutNodes[I];G.indexOf(g.id)===-1;)g=o.layoutNodes[o.idToIndex[g.parentId]],N++;A*=N*a.nestingFactor}P.idealLength=A,P.elasticity=R,o.layoutEdges.push(P)}return o},kp=function(e,t,a){var n=Pv(e,t,0,a);return 2>n.count?0:n.graph},Pv=function(e,t,a,n){var i=n.graphSet[a];if(-1<i.indexOf(e)&&-1<i.indexOf(t))return{count:2,graph:a};for(var s=0,o=0;o<i.length;o++){var l=i[o],u=n.idToIndex[l],v=n.layoutNodes[u].children;if(v.length!==0){var f=n.indexToGraph[n.idToIndex[v[0]]],c=Pv(e,t,f,n);if(c.count!==0)if(c.count===1){if(s++,s===2)break}else return c}}return{count:s,graph:a}},Dp,Bp=function(e,t){for(var a=e.clientWidth,n=e.clientHeight,i=0;i<e.nodeSize;i++){var s=e.layoutNodes[i];s.children.length===0&&!s.isLocked&&(s.positionX=Math.random()*a,s.positionY=Math.random()*n)}},Av=function(e,t,a){var n=e.boundingBox,i={x1:1/0,x2:-1/0,y1:1/0,y2:-1/0};return t.boundingBox&&(a.forEach(function(s){var o=e.layoutNodes[e.idToIndex[s.data("id")]];i.x1=Math.min(i.x1,o.positionX),i.x2=Math.max(i.x2,o.positionX),i.y1=Math.min(i.y1,o.positionY),i.y2=Math.max(i.y2,o.positionY)}),i.w=i.x2-i.x1,i.h=i.y2-i.y1),function(s,o){var l=e.layoutNodes[e.idToIndex[s.data("id")]];if(t.boundingBox){var u=i.w===0?.5:(l.positionX-i.x1)/i.w,v=i.h===0?.5:(l.positionY-i.y1)/i.h;return{x:n.x1+u*n.w,y:n.y1+v*n.h}}else return{x:l.positionX,y:l.positionY}}},Pp=function(e,t,a){var n=a.layout,i=a.eles.nodes(),s=Av(e,a,i);i.positions(s),e.ready!==!0&&(e.ready=!0,n.one("layoutready",a.ready),n.emit({type:"layoutready",layout:this}))},Ap=function(e,t,a){Rp(e,t),Ip(e),Op(e,t),Np(e),zp(e)},Rp=function(e,t){for(var a=0;a<e.graphSet.length;a++)for(var n=e.graphSet[a],i=n.length,s=0;s<i;s++)for(var o=e.layoutNodes[e.idToIndex[n[s]]],l=s+1;l<i;l++){var u=e.layoutNodes[e.idToIndex[n[l]]];Mp(o,u,e,t)}},Rv=function(e){return-1+2*e*Math.random()},Mp=function(e,t,a,n){var i=e.cmptId,s=t.cmptId;if(!(i!==s&&!a.isCompound)){var o=t.positionX-e.positionX,l=t.positionY-e.positionY,u=1;o===0&&l===0&&(o=Rv(u),l=Rv(u));var v=Lp(e,t,o,l);if(v>0)var f=n.nodeOverlap*v,c=Math.sqrt(o*o+l*l),h=f*o/c,d=f*l/c;else var y=On(e,o,l),g=On(t,-1*o,-1*l),p=g.x-y.x,m=g.y-y.y,b=p*p+m*m,c=Math.sqrt(b),f=(e.nodeRepulsion+t.nodeRepulsion)/b,h=f*p/c,d=f*m/c;e.isLocked||(e.offsetX-=h,e.offsetY-=d),t.isLocked||(t.offsetX+=h,t.offsetY+=d)}},Lp=function(e,t,a,n){if(a>0)var i=e.maxX-t.minX;else var i=t.maxX-e.minX;if(n>0)var s=e.maxY-t.minY;else var s=t.maxY-e.minY;return i>=0&&s>=0?Math.sqrt(i*i+s*s):0},On=function(e,t,a){var n=e.positionX,i=e.positionY,s=e.height||1,o=e.width||1,l=a/t,u=s/o,v={};return t===0&&0<a||t===0&&0>a?(v.x=n,v.y=i+s/2,v):0<t&&-1*u<=l&&l<=u?(v.x=n+o/2,v.y=i+o*a/2/t,v):0>t&&-1*u<=l&&l<=u?(v.x=n-o/2,v.y=i-o*a/2/t,v):0<a&&(l<=-1*u||l>=u)?(v.x=n+s*t/2/a,v.y=i+s/2,v):(0>a&&(l<=-1*u||l>=u)&&(v.x=n-s*t/2/a,v.y=i-s/2),v)},Ip=function(e,t){for(var a=0;a<e.edgeSize;a++){var n=e.layoutEdges[a],i=e.idToIndex[n.sourceId],s=e.layoutNodes[i],o=e.idToIndex[n.targetId],l=e.layoutNodes[o],u=l.positionX-s.positionX,v=l.positionY-s.positionY;if(!(u===0&&v===0)){var f=On(s,u,v),c=On(l,-1*u,-1*v),h=c.x-f.x,d=c.y-f.y,y=Math.sqrt(h*h+d*d),g=Math.pow(n.idealLength-y,2)/n.elasticity;if(y!==0)var p=g*h/y,m=g*d/y;else var p=0,m=0;s.isLocked||(s.offsetX+=p,s.offsetY+=m),l.isLocked||(l.offsetX-=p,l.offsetY-=m)}}},Op=function(e,t){if(t.gravity!==0)for(var a=1,n=0;n<e.graphSet.length;n++){var i=e.graphSet[n],s=i.length;if(n===0)var o=e.clientHeight/2,l=e.clientWidth/2;else var u=e.layoutNodes[e.idToIndex[i[0]]],v=e.layoutNodes[e.idToIndex[u.parentId]],o=v.positionX,l=v.positionY;for(var f=0;f<s;f++){var c=e.layoutNodes[e.idToIndex[i[f]]];if(!c.isLocked){var h=o-c.positionX,d=l-c.positionY,y=Math.sqrt(h*h+d*d);if(y>a){var g=t.gravity*h/y,p=t.gravity*d/y;c.offsetX+=g,c.offsetY+=p}}}}},Np=function(e,t){var a=[],n=0,i=-1;for(a.push.apply(a,e.graphSet[0]),i+=e.graphSet[0].length;n<=i;){var s=a[n++],o=e.idToIndex[s],l=e.layoutNodes[o],u=l.children;if(0<u.length&&!l.isLocked){for(var v=l.offsetX,f=l.offsetY,c=0;c<u.length;c++){var h=e.layoutNodes[e.idToIndex[u[c]]];h.offsetX+=v,h.offsetY+=f,a[++i]=u[c]}l.offsetX=0,l.offsetY=0}}},zp=function(e,t){for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&(n.maxX=void 0,n.minX=void 0,n.maxY=void 0,n.minY=void 0)}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];if(!(0<n.children.length||n.isLocked)){var i=Fp(n.offsetX,n.offsetY,e.temperature);n.positionX+=i.x,n.positionY+=i.y,n.offsetX=0,n.offsetY=0,n.minX=n.positionX-n.width,n.maxX=n.positionX+n.width,n.minY=n.positionY-n.height,n.maxY=n.positionY+n.height,Mv(n,e)}}for(var a=0;a<e.nodeSize;a++){var n=e.layoutNodes[a];0<n.children.length&&!n.isLocked&&(n.positionX=(n.maxX+n.minX)/2,n.positionY=(n.maxY+n.minY)/2,n.width=n.maxX-n.minX,n.height=n.maxY-n.minY)}},Fp=function(e,t,a){var n=Math.sqrt(e*e+t*t);if(n>a)var i={x:a*e/n,y:a*t/n};else var i={x:e,y:t};return i},Mv=function(e,t){var a=e.parentId;if(a!=null){var n=t.layoutNodes[t.idToIndex[a]],i=!1;if((n.maxX==null||e.maxX+n.padRight>n.maxX)&&(n.maxX=e.maxX+n.padRight,i=!0),(n.minX==null||e.minX-n.padLeft<n.minX)&&(n.minX=e.minX-n.padLeft,i=!0),(n.maxY==null||e.maxY+n.padBottom>n.maxY)&&(n.maxY=e.maxY+n.padBottom,i=!0),(n.minY==null||e.minY-n.padTop<n.minY)&&(n.minY=e.minY-n.padTop,i=!0),i)return Mv(n,t)}},Lv=function(e,t){for(var a=e.layoutNodes,n=[],i=0;i<a.length;i++){var s=a[i],o=s.cmptId,l=n[o]=n[o]||[];l.push(s)}for(var u=0,i=0;i<n.length;i++){var v=n[i];if(v){v.x1=1/0,v.x2=-1/0,v.y1=1/0,v.y2=-1/0;for(var f=0;f<v.length;f++){var c=v[f];v.x1=Math.min(v.x1,c.positionX-c.width/2),v.x2=Math.max(v.x2,c.positionX+c.width/2),v.y1=Math.min(v.y1,c.positionY-c.height/2),v.y2=Math.max(v.y2,c.positionY+c.height/2)}v.w=v.x2-v.x1,v.h=v.y2-v.y1,u+=v.w*v.h}}n.sort(function(m,b){return b.w*b.h-m.w*m.h});for(var h=0,d=0,y=0,g=0,p=Math.sqrt(u)*e.clientWidth/e.clientHeight,i=0;i<n.length;i++){var v=n[i];if(v){for(var f=0;f<v.length;f++){var c=v[f];c.isLocked||(c.positionX+=h-v.x1,c.positionY+=d-v.y1)}h+=v.w+t.componentSpacing,y+=v.w+t.componentSpacing,g=Math.max(g,v.h),y>p&&(d+=g+t.componentSpacing,h=0,y=0,g=0)}}},Vp={fit:!0,padding:30,boundingBox:void 0,avoidOverlap:!0,avoidOverlapPadding:10,nodeDimensionsIncludeLabels:!1,spacingFactor:void 0,condense:!1,rows:void 0,cols:void 0,position:function(e){},sort:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Iv(r){this.options=me({},Vp,r)}Iv.prototype.run=function(){var r=this.options,e=r,t=r.cy,a=e.eles,n=a.nodes().not(":parent");e.sort&&(n=n.sort(e.sort));var i=mr(e.boundingBox?e.boundingBox:{x1:0,y1:0,w:t.width(),h:t.height()});if(i.h===0||i.w===0)a.nodes().layoutPositions(this,e,function(U){return{x:i.x1,y:i.y1}});else{var s=n.size(),o=Math.sqrt(s*i.h/i.w),l=Math.round(o),u=Math.round(i.w/i.h*o),v=function(Q){if(Q==null)return Math.min(l,u);var K=Math.min(l,u);K==l?l=Q:u=Q},f=function(Q){if(Q==null)return Math.max(l,u);var K=Math.max(l,u);K==l?l=Q:u=Q},c=e.rows,h=e.cols!=null?e.cols:e.columns;if(c!=null&&h!=null)l=c,u=h;else if(c!=null&&h==null)l=c,u=Math.ceil(s/l);else if(c==null&&h!=null)u=h,l=Math.ceil(s/u);else if(u*l>s){var d=v(),y=f();(d-1)*y>=s?v(d-1):(y-1)*d>=s&&f(y-1)}else for(;u*l<s;){var g=v(),p=f();(p+1)*g>=s?f(p+1):v(g+1)}var m=i.w/u,b=i.h/l;if(e.condense&&(m=0,b=0),e.avoidOverlap)for(var w=0;w<n.length;w++){var E=n[w],C=E._private.position;(C.x==null||C.y==null)&&(C.x=0,C.y=0);var x=E.layoutDimensions(e),T=e.avoidOverlapPadding,k=x.w+T,D=x.h+T;m=Math.max(m,k),b=Math.max(b,D)}for(var B={},P=function(Q,K){return!!B["c-"+Q+"-"+K]},A=function(Q,K){B["c-"+Q+"-"+K]=!0},R=0,L=0,I=function(){L++,L>=u&&(L=0,R++)},M={},O=0;O<n.length;O++){var V=n[O],G=e.position(V);if(G&&(G.row!==void 0||G.col!==void 0)){var N={row:G.row,col:G.col};if(N.col===void 0)for(N.col=0;P(N.row,N.col);)N.col++;else if(N.row===void 0)for(N.row=0;P(N.row,N.col);)N.row++;M[V.id()]=N,A(N.row,N.col)}}var F=function(Q,K){var j,re;if(Q.locked()||Q.isParent())return!1;var ne=M[Q.id()];if(ne)j=ne.col*m+m/2+i.x1,re=ne.row*b+b/2+i.y1;else{for(;P(R,L);)I();j=L*m+m/2+i.x1,re=R*b+b/2+i.y1,A(R,L),I()}return{x:j,y:re}};n.layoutPositions(this,e,F)}return this};var qp={ready:function(){},stop:function(){}};function Xs(r){this.options=me({},qp,r)}Xs.prototype.run=function(){var r=this.options,e=r.eles,t=this;return r.cy,t.emit("layoutstart"),e.nodes().positions(function(){return{x:0,y:0}}),t.one("layoutready",r.ready),t.emit("layoutready"),t.one("layoutstop",r.stop),t.emit("layoutstop"),this},Xs.prototype.stop=function(){return this};var _p={positions:void 0,zoom:void 0,pan:void 0,fit:!0,padding:30,spacingFactor:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Ov(r){this.options=me({},_p,r)}Ov.prototype.run=function(){var r=this.options,e=r.eles,t=e.nodes(),a=Ue(r.positions);function n(i){if(r.positions==null)return ld(i.position());if(a)return r.positions(i);var s=r.positions[i._private.data.id];return s==null?null:s}return t.layoutPositions(this,r,function(i,s){var o=n(i);return i.locked()||o==null?!1:o}),this};var Gp={fit:!0,padding:30,boundingBox:void 0,animate:!1,animationDuration:500,animationEasing:void 0,animateFilter:function(e,t){return!0},ready:void 0,stop:void 0,transform:function(e,t){return t}};function Nv(r){this.options=me({},Gp,r)}Nv.prototype.run=function(){var r=this.options,e=r.cy,t=r.eles,a=mr(r.boundingBox?r.boundingBox:{x1:0,y1:0,w:e.width(),h:e.height()}),n=function(s,o){return{x:a.x1+Math.round(Math.random()*a.w),y:a.y1+Math.round(Math.random()*a.h)}};return t.nodes().layoutPositions(this,r,n),this};var Hp=[{name:"breadthfirst",impl:kv},{name:"circle",impl:Dv},{name:"concentric",impl:Bv},{name:"cose",impl:In},{name:"grid",impl:Iv},{name:"null",impl:Xs},{name:"preset",impl:Ov},{name:"random",impl:Nv}];function zv(r){this.options=r,this.notifications=0}var Fv=function(){},Vv=function(){throw new Error("A headless instance can not render images")};zv.prototype={recalculateRenderedStyle:Fv,notify:function(){this.notifications++},init:Fv,isHeadless:function(){return!0},png:Vv,jpg:Vv};var Ys={};Ys.arrowShapeWidth=.3,Ys.registerArrowShapes=function(){var r=this.arrowShapes={},e=this,t=function(u,v,f,c,h,d,y){var g=h.x-f/2-y,p=h.x+f/2+y,m=h.y-f/2-y,b=h.y+f/2+y,w=g<=u&&u<=p&&m<=v&&v<=b;return w},a=function(u,v,f,c,h){var d=u*Math.cos(c)-v*Math.sin(c),y=u*Math.sin(c)+v*Math.cos(c),g=d*f,p=y*f,m=g+h.x,b=p+h.y;return{x:m,y:b}},n=function(u,v,f,c){for(var h=[],d=0;d<u.length;d+=2){var y=u[d],g=u[d+1];h.push(a(y,g,v,f,c))}return h},i=function(u){for(var v=[],f=0;f<u.length;f++){var c=u[f];v.push(c.x,c.y)}return v},s=function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").pfValue*2},o=function(u,v){he(v)&&(v=r[v]),r[u]=me({name:u,points:[-.15,-.3,.15,-.3,.15,.3,-.15,.3],collide:function(c,h,d,y,g,p){var m=i(n(this.points,d+2*p,y,g)),b=Tr(c,h,m);return b},roughCollide:t,draw:function(c,h,d,y){var g=n(this.points,h,d,y);e.arrowShapeImpl("polygon")(c,g)},spacing:function(c){return 0},gap:s},v)};o("none",{collide:Ja,roughCollide:Ja,draw:yi,spacing:eu,gap:eu}),o("triangle",{points:[-.15,-.3,0,0,.15,-.3]}),o("arrow","triangle"),o("triangle-backcurve",{points:r.triangle.points,controlPoint:[0,-.15],roughCollide:t,draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=this.controlPoint,g=a(y[0],y[1],v,f,c);e.arrowShapeImpl(this.name)(u,d,g)},gap:function(u){return s(u)*.8}}),o("triangle-tee",{points:[0,0,.15,-.3,-.15,-.3,0,0],pointsTee:[-.15,-.4,-.15,-.5,.15,-.5,.15,-.4],collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.pointsTee,f+2*y,c,h)),m=Tr(u,v,g)||Tr(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.pointsTee,v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("circle-triangle",{radius:.15,pointsTr:[0,-.15,.15,-.45,-.15,-.45,0,-.15],collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2),m=i(n(this.points,f+2*y,c,h));return Tr(u,v,m)||p},draw:function(u,v,f,c,h){var d=n(this.pointsTr,v,f,c);e.arrowShapeImpl(this.name)(u,d,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("triangle-cross",{points:[0,0,.15,-.3,-.15,-.3,0,0],baseCrossLinePts:[-.15,-.4,-.15,-.4,.15,-.4,.15,-.4],crossLinePts:function(u,v){var f=this.baseCrossLinePts.slice(),c=v/u,h=3,d=5;return f[h]=f[h]-c,f[d]=f[d]-c,f},collide:function(u,v,f,c,h,d,y){var g=i(n(this.points,f+2*y,c,h)),p=i(n(this.crossLinePts(f,d),f+2*y,c,h)),m=Tr(u,v,g)||Tr(u,v,p);return m},draw:function(u,v,f,c,h){var d=n(this.points,v,f,c),y=n(this.crossLinePts(v,h),v,f,c);e.arrowShapeImpl(this.name)(u,d,y)}}),o("vee",{points:[-.15,-.3,0,0,.15,-.3,0,-.15],gap:function(u){return s(u)*.525}}),o("circle",{radius:.15,collide:function(u,v,f,c,h,d,y){var g=h,p=Math.pow(g.x-u,2)+Math.pow(g.y-v,2)<=Math.pow((f+2*y)*this.radius,2);return p},draw:function(u,v,f,c,h){e.arrowShapeImpl(this.name)(u,c.x,c.y,this.radius*v)},spacing:function(u){return e.getArrowWidth(u.pstyle("width").pfValue,u.pstyle("arrow-scale").value)*this.radius}}),o("tee",{points:[-.15,0,-.15,-.1,.15,-.1,.15,0],spacing:function(u){return 1},gap:function(u){return 1}}),o("square",{points:[-.15,0,.15,0,.15,-.3,-.15,-.3]}),o("diamond",{points:[-.15,-.15,0,-.3,.15,-.15,0,0],gap:function(u){return u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}}),o("chevron",{points:[0,0,-.15,-.15,-.1,-.2,0,-.1,.1,-.2,.15,-.15],gap:function(u){return .95*u.pstyle("width").pfValue*u.pstyle("arrow-scale").value}})};var kt={};kt.projectIntoViewport=function(r,e){var t=this.cy,a=this.findContainerClientCoords(),n=a[0],i=a[1],s=a[4],o=t.pan(),l=t.zoom(),u=((r-n)/s-o.x)/l,v=((e-i)/s-o.y)/l;return[u,v]},kt.findContainerClientCoords=function(){if(this.containerBB)return this.containerBB;var r=this.container,e=r.getBoundingClientRect(),t=this.cy.window().getComputedStyle(r),a=function(p){return parseFloat(t.getPropertyValue(p))},n={left:a("padding-left"),right:a("padding-right"),top:a("padding-top"),bottom:a("padding-bottom")},i={left:a("border-left-width"),right:a("border-right-width"),top:a("border-top-width"),bottom:a("border-bottom-width")},s=r.clientWidth,o=r.clientHeight,l=n.left+n.right,u=n.top+n.bottom,v=i.left+i.right,f=e.width/(s+v),c=s-l,h=o-u,d=e.left+n.left+i.left,y=e.top+n.top+i.top;return this.containerBB=[d,y,c,h,f]},kt.invalidateContainerClientCoordsCache=function(){this.containerBB=null},kt.findNearestElement=function(r,e,t,a){return this.findNearestElements(r,e,t,a)[0]},kt.findNearestElements=function(r,e,t,a){var n=this,i=this,s=i.getCachedZSortedEles(),o=[],l=i.cy.zoom(),u=i.cy.hasCompoundNodes(),v=(a?24:8)/l,f=(a?8:2)/l,c=(a?8:2)/l,h=1/0,d,y;t&&(s=s.interactive);function g(x,T){if(x.isNode()){if(y)return;y=x,o.push(x)}if(x.isEdge()&&(T==null||T<h))if(d){if(d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value&&d.pstyle("z-compound-depth").value===x.pstyle("z-compound-depth").value){for(var k=0;k<o.length;k++)if(o[k].isEdge()){o[k]=x,d=x,h=T!=null?T:h;break}}}else o.push(x),d=x,h=T!=null?T:h}function p(x){var T=x.outerWidth()+2*f,k=x.outerHeight()+2*f,D=T/2,B=k/2,P=x.position(),A=x.pstyle("corner-radius").value==="auto"?"auto":x.pstyle("corner-radius").pfValue,R=x._private.rscratch;if(P.x-D<=r&&r<=P.x+D&&P.y-B<=e&&e<=P.y+B){var L=i.nodeShapes[n.getNodeShape(x)];if(L.checkPoint(r,e,0,T,k,P.x,P.y,A,R))return g(x,0),!0}}function m(x){var T=x._private,k=T.rscratch,D=x.pstyle("width").pfValue,B=x.pstyle("arrow-scale").value,P=D/2+v,A=P*P,R=P*2,O=T.source,V=T.target,L;if(k.edgeType==="segments"||k.edgeType==="straight"||k.edgeType==="haystack"){for(var I=k.allpts,M=0;M+3<I.length;M+=2)if(Cd(r,e,I[M],I[M+1],I[M+2],I[M+3],R)&&A>(L=Bd(r,e,I[M],I[M+1],I[M+2],I[M+3])))return g(x,L),!0}else if(k.edgeType==="bezier"||k.edgeType==="multibezier"||k.edgeType==="self"||k.edgeType==="compound"){for(var I=k.allpts,M=0;M+5<k.allpts.length;M+=4)if(Td(r,e,I[M],I[M+1],I[M+2],I[M+3],I[M+4],I[M+5],R)&&A>(L=Dd(r,e,I[M],I[M+1],I[M+2],I[M+3],I[M+4],I[M+5])))return g(x,L),!0}for(var O=O||T.source,V=V||T.target,G=n.getArrowWidth(D,B),N=[{name:"source",x:k.arrowStartX,y:k.arrowStartY,angle:k.srcArrowAngle},{name:"target",x:k.arrowEndX,y:k.arrowEndY,angle:k.tgtArrowAngle},{name:"mid-source",x:k.midX,y:k.midY,angle:k.midsrcArrowAngle},{name:"mid-target",x:k.midX,y:k.midY,angle:k.midtgtArrowAngle}],M=0;M<N.length;M++){var F=N[M],U=i.arrowShapes[x.pstyle(F.name+"-arrow-shape").value],Q=x.pstyle("width").pfValue;if(U.roughCollide(r,e,G,F.angle,{x:F.x,y:F.y},Q,v)&&U.collide(r,e,G,F.angle,{x:F.x,y:F.y},Q,v))return g(x),!0}u&&o.length>0&&(p(O),p(V))}function b(x,T,k){return Cr(x,T,k)}function w(x,T){var k=x._private,D=c,B;T?B=T+"-":B="",x.boundingBox();var P=k.labelBounds[T||"main"],A=x.pstyle(B+"label").value,R=x.pstyle("text-events").strValue==="yes";if(!(!R||!A)){var L=b(k.rscratch,"labelX",T),I=b(k.rscratch,"labelY",T),M=b(k.rscratch,"labelAngle",T),O=x.pstyle(B+"text-margin-x").pfValue,V=x.pstyle(B+"text-margin-y").pfValue,G=P.x1-D-O,N=P.x2+D-O,F=P.y1-D-V,U=P.y2+D-V;if(M){var Q=Math.cos(M),K=Math.sin(M),j=function(Y,ae){return Y=Y-L,ae=ae-I,{x:Y*Q-ae*K+L,y:Y*K+ae*Q+I}},re=j(G,F),ne=j(G,U),J=j(N,F),z=j(N,U),q=[re.x+O,re.y+V,J.x+O,J.y+V,z.x+O,z.y+V,ne.x+O,ne.y+V];if(Tr(r,e,q))return g(x),!0}else if(it(P,r,e))return g(x),!0}}for(var E=s.length-1;E>=0;E--){var C=s[E];C.isNode()?p(C)||w(C):m(C)||w(C)||w(C,"source")||w(C,"target")}return o},kt.getAllInBox=function(r,e,t,a){var n=this.getCachedZSortedEles().interactive,i=this.cy.zoom(),s=2/i,o=[],l=Math.min(r,t),u=Math.max(r,t),v=Math.min(e,a),f=Math.max(e,a);r=l,t=u,e=v,a=f;var c=mr({x1:r,y1:e,x2:t,y2:a}),h=[{x:c.x1,y:c.y1},{x:c.x2,y:c.y1},{x:c.x2,y:c.y2},{x:c.x1,y:c.y2}],d=[[h[0],h[1]],[h[1],h[2]],[h[2],h[3]],[h[3],h[0]]];function y(Y,ae,ce){return Cr(Y,ae,ce)}function g(Y,ae){var ce=Y._private,Ae=s,Ce="";Y.boundingBox();var we=ce.labelBounds.main;if(!we)return null;var ye=y(ce.rscratch,"labelX",ae),ie=y(ce.rscratch,"labelY",ae),de=y(ce.rscratch,"labelAngle",ae),ge=Y.pstyle(Ce+"text-margin-x").pfValue,Ee=Y.pstyle(Ce+"text-margin-y").pfValue,pe=we.x1-Ae-ge,ke=we.x2+Ae-ge,Re=we.y1-Ae-Ee,ze=we.y2+Ae-Ee;if(de){var Fe=Math.cos(de),Ve=Math.sin(de),xe=function(X,S){return X=X-ye,S=S-ie,{x:X*Fe-S*Ve+ye,y:X*Ve+S*Fe+ie}};return[xe(pe,Re),xe(ke,Re),xe(ke,ze),xe(pe,ze)]}else return[{x:pe,y:Re},{x:ke,y:Re},{x:ke,y:ze},{x:pe,y:ze}]}function p(Y,ae,ce,Ae){function Ce(we,ye,ie){return(ie.y-we.y)*(ye.x-we.x)>(ye.y-we.y)*(ie.x-we.x)}return Ce(Y,ce,Ae)!==Ce(ae,ce,Ae)&&Ce(Y,ae,ce)!==Ce(Y,ae,Ae)}for(var m=0;m<n.length;m++){var b=n[m];if(b.isNode()){var w=b,E=w.pstyle("text-events").strValue==="yes",C=w.pstyle("box-selection").strValue,x=w.pstyle("box-select-labels").strValue==="yes";if(C==="none")continue;var T=(C==="overlap"||x)&&E,k=w.boundingBox({includeNodes:!0,includeEdges:!1,includeLabels:T});if(C==="contain"){var D=!1;if(x&&E){var B=g(w);B&&Pi(B,h)&&(o.push(w),D=!0)}!D&&fu(c,k)&&o.push(w)}else if(C==="overlap"&&Ti(c,k)){var P=w.boundingBox({includeNodes:!0,includeEdges:!0,includeLabels:!1,includeMainLabels:!1,includeSourceLabels:!1,includeTargetLabels:!1}),A=[{x:P.x1,y:P.y1},{x:P.x2,y:P.y1},{x:P.x2,y:P.y2},{x:P.x1,y:P.y2}];if(Pi(A,h))o.push(w);else{var R=g(w);R&&Pi(R,h)&&o.push(w)}}}else{var L=b,I=L._private,M=I.rscratch,O=L.pstyle("box-selection").strValue;if(O==="none")continue;if(O==="contain"){if(M.startX!=null&&M.startY!=null&&!it(c,M.startX,M.startY)||M.endX!=null&&M.endY!=null&&!it(c,M.endX,M.endY))continue;if(M.edgeType==="bezier"||M.edgeType==="multibezier"||M.edgeType==="self"||M.edgeType==="compound"||M.edgeType==="segments"||M.edgeType==="haystack"){for(var V=I.rstyle.bezierPts||I.rstyle.linePts||I.rstyle.haystackPts,G=!0,N=0;N<V.length;N++)if(!vu(c,V[N])){G=!1;break}G&&o.push(L)}else M.edgeType==="straight"&&o.push(L)}else if(O==="overlap"){var F=!1;if(M.startX!=null&&M.startY!=null&&M.endX!=null&&M.endY!=null&&(it(c,M.startX,M.startY)||it(c,M.endX,M.endY)))o.push(L),F=!0;else if(!F&&M.edgeType==="haystack"){for(var U=I.rstyle.haystackPts,Q=0;Q<U.length;Q++)if(vu(c,U[Q])){o.push(L),F=!0;break}}if(!F){var K=I.rstyle.bezierPts||I.rstyle.linePts||I.rstyle.haystackPts;if((!K||K.length<2)&&M.edgeType==="straight"&&M.startX!=null&&M.startY!=null&&M.endX!=null&&M.endY!=null&&(K=[{x:M.startX,y:M.startY},{x:M.endX,y:M.endY}]),!K||K.length<2)continue;for(var j=0;j<K.length-1;j++){for(var re=K[j],ne=K[j+1],J=0;J<d.length;J++){var z=Ye(d[J],2),q=z[0],H=z[1];if(p(re,ne,q,H)){o.push(L),F=!0;break}}if(F)break}}}}}return o};var Nn={};Nn.calculateArrowAngles=function(r){var e=r._private.rscratch,t=e.edgeType==="haystack",a=e.edgeType==="bezier",n=e.edgeType==="multibezier",i=e.edgeType==="segments",s=e.edgeType==="compound",o=e.edgeType==="self",l,u,v,f,c,h,p,m;if(t?(v=e.haystackPts[0],f=e.haystackPts[1],c=e.haystackPts[2],h=e.haystackPts[3]):(v=e.arrowStartX,f=e.arrowStartY,c=e.arrowEndX,h=e.arrowEndY),p=e.midX,m=e.midY,i)l=v-e.segpts[0],u=f-e.segpts[1];else if(n||s||o||a){var d=e.allpts,y=ir(d[0],d[2],d[4],.1),g=ir(d[1],d[3],d[5],.1);l=v-y,u=f-g}else l=v-p,u=f-m;e.srcArrowAngle=tn(l,u);var p=e.midX,m=e.midY;if(t&&(p=(v+c)/2,m=(f+h)/2),l=c-v,u=h-f,i){var d=e.allpts;if(d.length/2%2===0){var b=d.length/2,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}else if(e.isRound)l=e.midVector[1],u=-e.midVector[0];else{var b=d.length/2-1,w=b-2;l=d[b]-d[w],u=d[b+1]-d[w+1]}}else if(n||s||o){var d=e.allpts,E=e.ctrlpts,C,x,T,k;if(E.length/2%2===0){var D=d.length/2-1,B=D+2,P=B+2;C=ir(d[D],d[B],d[P],0),x=ir(d[D+1],d[B+1],d[P+1],0),T=ir(d[D],d[B],d[P],1e-4),k=ir(d[D+1],d[B+1],d[P+1],1e-4)}else{var B=d.length/2-1,D=B-2,P=B+2;C=ir(d[D],d[B],d[P],.4999),x=ir(d[D+1],d[B+1],d[P+1],.4999),T=ir(d[D],d[B],d[P],.5),k=ir(d[D+1],d[B+1],d[P+1],.5)}l=T-C,u=k-x}if(e.midtgtArrowAngle=tn(l,u),e.midDispX=l,e.midDispY=u,l*=-1,u*=-1,i){var d=e.allpts;if(d.length/2%2!==0){if(!e.isRound){var b=d.length/2-1,A=b+2;l=-(d[A]-d[b]),u=-(d[A+1]-d[b+1])}}}if(e.midsrcArrowAngle=tn(l,u),i)l=c-e.segpts[e.segpts.length-2],u=h-e.segpts[e.segpts.length-1];else if(n||s||o||a){var d=e.allpts,R=d.length,y=ir(d[R-6],d[R-4],d[R-2],.9),g=ir(d[R-5],d[R-3],d[R-1],.9);l=c-y,u=h-g}else l=c-p,u=h-m;e.tgtArrowAngle=tn(l,u)},Nn.getArrowWidth=Nn.getArrowHeight=function(r,e){var t=this.arrowWidthCache=this.arrowWidthCache||{},a=t[r+", "+e];return a||(a=Math.max(Math.pow(r*13.37,.9),29)*e,t[r+", "+e]=a,a)};var Zs,Qs,Vr={},Ar={},qv,_v,Dt,zn,Xr,Bt,Pt,qr,jt,Fn,Gv,Hv,Js,js,Wv,$v=function(e,t,a){a.x=t.x-e.x,a.y=t.y-e.y,a.len=Math.sqrt(a.x*a.x+a.y*a.y),a.nx=a.x/a.len,a.ny=a.y/a.len,a.ang=Math.atan2(a.ny,a.nx)},Wp=function(e,t){t.x=e.x*-1,t.y=e.y*-1,t.nx=e.nx*-1,t.ny=e.ny*-1,t.ang=e.ang>0?-(Math.PI-e.ang):Math.PI+e.ang},$p=function(e,t,a,n,i){if(e!==Wv?$v(t,e,Vr):Wp(Ar,Vr),$v(t,a,Ar),qv=Vr.nx*Ar.ny-Vr.ny*Ar.nx,_v=Vr.nx*Ar.nx-Vr.ny*-Ar.ny,Xr=Math.asin(Math.max(-1,Math.min(1,qv))),Math.abs(Xr)<1e-6){Zs=t.x,Qs=t.y,Pt=jt=0;return}Dt=1,zn=!1,_v<0?Xr<0?Xr=Math.PI+Xr:(Xr=Math.PI-Xr,Dt=-1,zn=!0):Xr>0&&(Dt=-1,zn=!0),t.radius!==void 0?jt=t.radius:jt=n,Bt=Xr/2,Fn=Math.min(Vr.len/2,Ar.len/2),i?(qr=Math.abs(Math.cos(Bt)*jt/Math.sin(Bt)),qr>Fn?(qr=Fn,Pt=Math.abs(qr*Math.sin(Bt)/Math.cos(Bt))):Pt=jt):(qr=Math.min(Fn,jt),Pt=Math.abs(qr*Math.sin(Bt)/Math.cos(Bt))),Js=t.x+Ar.nx*qr,js=t.y+Ar.ny*qr,Zs=Js-Ar.ny*Pt*Dt,Qs=js+Ar.nx*Pt*Dt,Gv=t.x+Vr.nx*qr,Hv=t.y+Vr.ny*qr,Wv=t};function Uv(r,e){e.radius===0?r.lineTo(e.cx,e.cy):r.arc(e.cx,e.cy,e.radius,e.startAngle,e.endAngle,e.counterClockwise)}function eo(r,e,t,a){var n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0;return a===0||e.radius===0?{cx:e.x,cy:e.y,radius:0,startX:e.x,startY:e.y,stopX:e.x,stopY:e.y,startAngle:void 0,endAngle:void 0,counterClockwise:void 0}:($p(r,e,t,a,n),{cx:Zs,cy:Qs,radius:Pt,startX:Gv,startY:Hv,stopX:Js,stopY:js,startAngle:Vr.ang+Math.PI/2*Dt,endAngle:Ar.ang-Math.PI/2*Dt,counterClockwise:zn})}var Ma=.01,Up=Math.sqrt(2*Ma),pr={};pr.findMidptPtsEtc=function(r,e){var t=e.posPts,a=e.intersectionPts,n=e.vectorNormInverse,i,s=r.pstyle("source-endpoint"),o=r.pstyle("target-endpoint"),l=s.units!=null&&o.units!=null,u=function(E,C,x,T){var k=T-C,D=x-E,B=Math.sqrt(D*D+k*k);return{x:-k/B,y:D/B}},v=r.pstyle("edge-distances").value;switch(v){case"node-position":i=t;break;case"intersection":i=a;break;case"endpoints":{if(l){var f=this.manualEndptToPx(r.source()[0],s),c=Ye(f,2),h=c[0],d=c[1],y=this.manualEndptToPx(r.target()[0],o),g=Ye(y,2),p=g[0],m=g[1],b={x1:h,y1:d,x2:p,y2:m};n=u(h,d,p,m),i=b}else Oe("Edge ".concat(r.id()," has edge-distances:endpoints specified without manual endpoints specified via source-endpoint and target-endpoint.  Falling back on edge-distances:intersection (default).")),i=a;break}}return{midptPts:i,vectorNormInverse:n}},pr.findHaystackPoints=function(r){for(var e=0;e<r.length;e++){var t=r[e],a=t._private,n=a.rscratch;if(!n.haystack){var i=Math.random()*2*Math.PI;n.source={x:Math.cos(i),y:Math.sin(i)},i=Math.random()*2*Math.PI,n.target={x:Math.cos(i),y:Math.sin(i)}}var s=a.source,o=a.target,l=s.position(),u=o.position(),v=s.width(),f=o.width(),c=s.height(),h=o.height(),d=t.pstyle("haystack-radius").value,y=d/2;n.haystackPts=n.allpts=[n.source.x*v*y+l.x,n.source.y*c*y+l.y,n.target.x*f*y+u.x,n.target.y*h*y+u.y],n.midX=(n.allpts[0]+n.allpts[2])/2,n.midY=(n.allpts[1]+n.allpts[3])/2,n.edgeType="haystack",n.haystack=!0,this.storeEdgeProjections(t),this.calculateArrowAngles(t),this.recalculateEdgeLabelProjections(t),this.calculateLabelAngles(t)}},pr.findSegmentsPoints=function(r,e){var t=r._private.rscratch,a=r.pstyle("segment-weights"),n=r.pstyle("segment-distances"),i=r.pstyle("segment-radii"),s=r.pstyle("radius-type"),o=Math.min(a.pfValue.length,n.pfValue.length),l=i.pfValue[i.pfValue.length-1],u=s.pfValue[s.pfValue.length-1];t.edgeType="segments",t.segpts=[],t.radii=[],t.isArcRadius=[];for(var v=0;v<o;v++){var f=a.pfValue[v],c=n.pfValue[v],h=1-f,d=f,y=this.findMidptPtsEtc(r,e),g=y.midptPts,p=y.vectorNormInverse,m={x:g.x1*h+g.x2*d,y:g.y1*h+g.y2*d};t.segpts.push(m.x+p.x*c,m.y+p.y*c),t.radii.push(i.pfValue[v]!==void 0?i.pfValue[v]:l),t.isArcRadius.push((s.pfValue[v]!==void 0?s.pfValue[v]:u)==="arc-radius")}},pr.findLoopPoints=function(r,e,t,a){var n=r._private.rscratch,i=e.dirCounts,s=e.srcPos,o=r.pstyle("control-point-distances"),l=o?o.pfValue[0]:void 0,u=r.pstyle("loop-direction").pfValue,v=r.pstyle("loop-sweep").pfValue,f=r.pstyle("control-point-step-size").pfValue;n.edgeType="self";var c=t,h=f;a&&(c=0,h=l);var d=u-Math.PI/2,y=d-v/2,g=d+v/2,p=u+"_"+v;c=i[p]===void 0?i[p]=0:++i[p],n.ctrlpts=[s.x+Math.cos(y)*1.4*h*(c/3+1),s.y+Math.sin(y)*1.4*h*(c/3+1),s.x+Math.cos(g)*1.4*h*(c/3+1),s.y+Math.sin(g)*1.4*h*(c/3+1)]},pr.findCompoundLoopPoints=function(r,e,t,a){var n=r._private.rscratch;n.edgeType="compound";var i=e.srcPos,s=e.tgtPos,o=e.srcW,l=e.srcH,u=e.tgtW,v=e.tgtH,f=r.pstyle("control-point-step-size").pfValue,c=r.pstyle("control-point-distances"),h=c?c.pfValue[0]:void 0,d=t,y=f;a&&(d=0,y=h);var g=50,p={x:i.x-o/2,y:i.y-l/2},m={x:s.x-u/2,y:s.y-v/2},b={x:Math.min(p.x,m.x),y:Math.min(p.y,m.y)},w=.5,E=Math.max(w,Math.log(o*Ma)),C=Math.max(w,Math.log(u*Ma));n.ctrlpts=[b.x,b.y-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*E,b.x-(1+Math.pow(g,1.12)/100)*y*(d/3+1)*C,b.y]},pr.findStraightEdgePoints=function(r){r._private.rscratch.edgeType="straight"},pr.findBezierPoints=function(r,e,t,a,n){var i=r._private.rscratch,s=r.pstyle("control-point-step-size").pfValue,o=r.pstyle("control-point-distances"),l=r.pstyle("control-point-weights"),u=o&&l?Math.min(o.value.length,l.value.length):1,v=o?o.pfValue[0]:void 0,f=l.value[0],c=a;i.edgeType=c?"multibezier":"bezier",i.ctrlpts=[];for(var h=0;h<u;h++){var d=(.5-e.eles.length/2+t)*s*(n?-1:1),y=void 0,g=Ci(d);c&&(v=o?o.pfValue[h]:s,f=l.value[h]),a?y=v:y=v!==void 0?g*v:void 0;var p=y!==void 0?y:d,m=1-f,b=f,w=this.findMidptPtsEtc(r,e),E=w.midptPts,C=w.vectorNormInverse,x={x:E.x1*m+E.x2*b,y:E.y1*m+E.y2*b};i.ctrlpts.push(x.x+C.x*p,x.y+C.y*p)}},pr.findTaxiPoints=function(r,e){var t=r._private.rscratch;t.edgeType="segments";var a="vertical",n="horizontal",i="leftward",s="rightward",o="downward",l="upward",u="auto",v=e.posPts,f=e.srcW,c=e.srcH,h=e.tgtW,d=e.tgtH,y=r.pstyle("edge-distances").value,g=y!=="node-position",p=r.pstyle("taxi-direction").value,m=p,b=r.pstyle("taxi-turn"),w=b.units==="%",E=b.pfValue,C=E<0,x=r.pstyle("taxi-turn-min-distance").pfValue,T=g?(f+h)/2:0,k=g?(c+d)/2:0,D=v.x2-v.x1,B=v.y2-v.y1,P=function(S,_){return S>0?Math.max(S-_,0):Math.min(S+_,0)},A=P(D,T),R=P(B,k),L=!1;m===u?p=Math.abs(A)>Math.abs(R)?n:a:m===l||m===o?(p=a,L=!0):(m===i||m===s)&&(p=n,L=!0);var I=p===a,M=I?R:A,O=I?B:D,V=Ci(O),G=!1;!(L&&(w||C))&&(m===o&&O<0||m===l&&O>0||m===i&&O>0||m===s&&O<0)&&(V*=-1,M=V*Math.abs(M),G=!0);var N;if(w){var F=E<0?1+E:E;N=F*M}else{var U=E<0?M:0;N=U+E*V}var Q=function(S){return Math.abs(S)<x||Math.abs(S)>=Math.abs(M)},K=Q(N),j=Q(Math.abs(M)-Math.abs(N)),re=K||j;if(re&&!G)if(I){var ne=Math.abs(O)<=c/2,J=Math.abs(D)<=h/2;if(ne){var z=(v.x1+v.x2)/2,q=v.y1,H=v.y2;t.segpts=[z,q,z,H]}else if(J){var Y=(v.y1+v.y2)/2,ae=v.x1,ce=v.x2;t.segpts=[ae,Y,ce,Y]}else t.segpts=[v.x1,v.y2]}else{var Ae=Math.abs(O)<=f/2,Ce=Math.abs(B)<=d/2;if(Ae){var we=(v.y1+v.y2)/2,ye=v.x1,ie=v.x2;t.segpts=[ye,we,ie,we]}else if(Ce){var de=(v.x1+v.x2)/2,ge=v.y1,Ee=v.y2;t.segpts=[de,ge,de,Ee]}else t.segpts=[v.x2,v.y1]}else if(I){var pe=v.y1+N+(g?c/2*V:0),ke=v.x1,Re=v.x2;t.segpts=[ke,pe,Re,pe]}else{var ze=v.x1+N+(g?f/2*V:0),Fe=v.y1,Ve=v.y2;t.segpts=[ze,Fe,ze,Ve]}if(t.isRound){var xe=r.pstyle("taxi-radius").value,ue=r.pstyle("radius-type").value[0]==="arc-radius";t.radii=new Array(t.segpts.length/2).fill(xe),t.isArcRadius=new Array(t.segpts.length/2).fill(ue)}},pr.tryToCorrectInvalidPoints=function(r,e){var t=r._private.rscratch;if(t.edgeType==="bezier"){var a=e.srcPos,n=e.tgtPos,i=e.srcW,s=e.srcH,o=e.tgtW,l=e.tgtH,u=e.srcShape,v=e.tgtShape,f=e.srcCornerRadius,c=e.tgtCornerRadius,h=e.srcRs,d=e.tgtRs,y=!te(t.startX)||!te(t.startY),g=!te(t.arrowStartX)||!te(t.arrowStartY),p=!te(t.endX)||!te(t.endY),m=!te(t.arrowEndX)||!te(t.arrowEndY),b=3,w=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth,E=b*w,C=xt({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.startX,y:t.startY}),x=C<E,T=xt({x:t.ctrlpts[0],y:t.ctrlpts[1]},{x:t.endX,y:t.endY}),k=T<E,D=!1;if(y||g||x){D=!0;var B={x:t.ctrlpts[0]-a.x,y:t.ctrlpts[1]-a.y},P=Math.sqrt(B.x*B.x+B.y*B.y),A={x:B.x/P,y:B.y/P},R=Math.max(i,s),L={x:t.ctrlpts[0]+A.x*2*R,y:t.ctrlpts[1]+A.y*2*R},I=u.intersectLine(a.x,a.y,i,s,L.x,L.y,0,f,h);x?(t.ctrlpts[0]=t.ctrlpts[0]+A.x*(E-C),t.ctrlpts[1]=t.ctrlpts[1]+A.y*(E-C)):(t.ctrlpts[0]=I[0]+A.x*E,t.ctrlpts[1]=I[1]+A.y*E)}if(p||m||k){D=!0;var M={x:t.ctrlpts[0]-n.x,y:t.ctrlpts[1]-n.y},O=Math.sqrt(M.x*M.x+M.y*M.y),V={x:M.x/O,y:M.y/O},G=Math.max(i,s),N={x:t.ctrlpts[0]+V.x*2*G,y:t.ctrlpts[1]+V.y*2*G},F=v.intersectLine(n.x,n.y,o,l,N.x,N.y,0,c,d);k?(t.ctrlpts[0]=t.ctrlpts[0]+V.x*(E-T),t.ctrlpts[1]=t.ctrlpts[1]+V.y*(E-T)):(t.ctrlpts[0]=F[0]+V.x*E,t.ctrlpts[1]=F[1]+V.y*E)}D&&this.findEndpoints(r)}},pr.storeAllpts=function(r){var e=r._private.rscratch;if(e.edgeType==="multibezier"||e.edgeType==="bezier"||e.edgeType==="self"||e.edgeType==="compound"){e.allpts=[],e.allpts.push(e.startX,e.startY);for(var t=0;t+1<e.ctrlpts.length;t+=2)e.allpts.push(e.ctrlpts[t],e.ctrlpts[t+1]),t+3<e.ctrlpts.length&&e.allpts.push((e.ctrlpts[t]+e.ctrlpts[t+2])/2,(e.ctrlpts[t+1]+e.ctrlpts[t+3])/2);e.allpts.push(e.endX,e.endY);var a,n;e.ctrlpts.length/2%2===0?(a=e.allpts.length/2-1,e.midX=e.allpts[a],e.midY=e.allpts[a+1]):(a=e.allpts.length/2-3,n=.5,e.midX=ir(e.allpts[a],e.allpts[a+2],e.allpts[a+4],n),e.midY=ir(e.allpts[a+1],e.allpts[a+3],e.allpts[a+5],n))}else if(e.edgeType==="straight")e.allpts=[e.startX,e.startY,e.endX,e.endY],e.midX=(e.startX+e.endX+e.arrowStartX+e.arrowEndX)/4,e.midY=(e.startY+e.endY+e.arrowStartY+e.arrowEndY)/4;else if(e.edgeType==="segments"){if(e.allpts=[],e.allpts.push(e.startX,e.startY),e.allpts.push.apply(e.allpts,e.segpts),e.allpts.push(e.endX,e.endY),e.isRound){e.roundCorners=[];for(var i=2;i+3<e.allpts.length;i+=2){var s=e.radii[i/2-1],o=e.isArcRadius[i/2-1];e.roundCorners.push(eo({x:e.allpts[i-2],y:e.allpts[i-1]},{x:e.allpts[i],y:e.allpts[i+1],radius:s},{x:e.allpts[i+2],y:e.allpts[i+3]},s,o))}}if(e.segpts.length%4===0){var l=e.segpts.length/2,u=l-2;e.midX=(e.segpts[u]+e.segpts[l])/2,e.midY=(e.segpts[u+1]+e.segpts[l+1])/2}else{var v=e.segpts.length/2-1;if(!e.isRound)e.midX=e.segpts[v],e.midY=e.segpts[v+1];else{var f={x:e.segpts[v],y:e.segpts[v+1]},c=e.roundCorners[v/2];if(c.radius===0){var h={x:e.segpts[v+2],y:e.segpts[v+3]};e.midX=f.x,e.midY=f.y,e.midVector=[f.y-h.y,h.x-f.x]}else{var d=[f.x-c.cx,f.y-c.cy],y=c.radius/Math.sqrt(Math.pow(d[0],2)+Math.pow(d[1],2));d=d.map(function(g){return g*y}),e.midX=c.cx+d[0],e.midY=c.cy+d[1],e.midVector=d}}}}},pr.checkForInvalidEdgeWarning=function(r){var e=r[0]._private.rscratch;e.nodesOverlap||te(e.startX)&&te(e.startY)&&te(e.endX)&&te(e.endY)?e.loggedErr=!1:e.loggedErr||(e.loggedErr=!0,Oe("Edge `"+r.id()+"` has invalid endpoints and so it is impossible to draw.  Adjust your edge style (e.g. control points) accordingly or use an alternative edge type.  This is expected behaviour when the source node and the target node overlap."))},pr.findEdgeControlPoints=function(r){var e=this;if(!(!r||r.length===0)){for(var t=this,a=t.cy,n=a.hasCompoundNodes(),i=new Ur,s=function(k,D){return[].concat($a(k),[D?1:0]).join("-")},o=[],l=[],u=0;u<r.length;u++){var v=r[u],f=v._private,c=v.pstyle("curve-style").value;if(!(v.removed()||!v.takesUpSpace())){if(c==="haystack"){l.push(v);continue}var h=c==="unbundled-bezier"||tt(c,"segments")||c==="straight"||c==="straight-triangle"||tt(c,"taxi"),d=c==="unbundled-bezier"||c==="bezier",y=f.source,g=f.target,p=y.poolIndex(),m=g.poolIndex(),b=[p,m].sort(),w=s(b,h),E=i.get(w);E==null&&(E={eles:[]},o.push({pairId:b,edgeIsUnbundled:h}),i.set(w,E)),E.eles.push(v),h&&(E.hasUnbundled=!0),d&&(E.hasBezier=!0)}}for(var C=function(){var k=o[x],D=k.pairId,B=k.edgeIsUnbundled,P=s(D,B),A=i.get(P),R;if(!A.hasUnbundled){var L=A.eles[0].parallelEdges().filter(function(ue){return ue.isBundledBezier()});mi(A.eles),L.forEach(function(ue){return A.eles.push(ue)}),A.eles.sort(function(ue,X){return ue.poolIndex()-X.poolIndex()})}var I=A.eles[0],M=I.source(),O=I.target();if(M.poolIndex()>O.poolIndex()){var V=M;M=O,O=V}var G=A.srcPos=M.position(),N=A.tgtPos=O.position(),F=A.srcW=M.outerWidth(),U=A.srcH=M.outerHeight(),Q=A.tgtW=O.outerWidth(),K=A.tgtH=O.outerHeight(),j=A.srcShape=t.nodeShapes[e.getNodeShape(M)],re=A.tgtShape=t.nodeShapes[e.getNodeShape(O)],ne=A.srcCornerRadius=M.pstyle("corner-radius").value==="auto"?"auto":M.pstyle("corner-radius").pfValue,J=A.tgtCornerRadius=O.pstyle("corner-radius").value==="auto"?"auto":O.pstyle("corner-radius").pfValue,z=A.tgtRs=O._private.rscratch,q=A.srcRs=M._private.rscratch;A.dirCounts={north:0,west:0,south:0,east:0,northwest:0,southwest:0,northeast:0,southeast:0};for(var H=0;H<A.eles.length;H++){var Y=A.eles[H],ae=Y[0]._private.rscratch,ce=Y.pstyle("curve-style").value,Ae=ce==="unbundled-bezier"||tt(ce,"segments")||tt(ce,"taxi"),Ce=!M.same(Y.source());if(!A.calculatedIntersection&&M!==O&&(A.hasBezier||A.hasUnbundled)){A.calculatedIntersection=!0;var we=j.intersectLine(G.x,G.y,F,U,N.x,N.y,0,ne,q),ye=A.srcIntn=we,ie=re.intersectLine(N.x,N.y,Q,K,G.x,G.y,0,J,z),de=A.tgtIntn=ie,ge=A.intersectionPts={x1:we[0],x2:ie[0],y1:we[1],y2:ie[1]},Ee=A.posPts={x1:G.x,x2:N.x,y1:G.y,y2:N.y},pe=ie[1]-we[1],ke=ie[0]-we[0],Re=Math.sqrt(ke*ke+pe*pe);te(Re)&&Re>=Up||(Re=Math.sqrt(Math.max(ke*ke,Ma)+Math.max(pe*pe,Ma)));var ze=A.vector={x:ke,y:pe},Fe=A.vectorNorm={x:ze.x/Re,y:ze.y/Re},Ve={x:-Fe.y,y:Fe.x};A.nodesOverlap=!te(Re)||re.checkPoint(we[0],we[1],0,Q,K,N.x,N.y,J,z)||j.checkPoint(ie[0],ie[1],0,F,U,G.x,G.y,ne,q),A.vectorNormInverse=Ve,R={nodesOverlap:A.nodesOverlap,dirCounts:A.dirCounts,calculatedIntersection:!0,hasBezier:A.hasBezier,hasUnbundled:A.hasUnbundled,eles:A.eles,srcPos:N,srcRs:z,tgtPos:G,tgtRs:q,srcW:Q,srcH:K,tgtW:F,tgtH:U,srcIntn:de,tgtIntn:ye,srcShape:re,tgtShape:j,posPts:{x1:Ee.x2,y1:Ee.y2,x2:Ee.x1,y2:Ee.y1},intersectionPts:{x1:ge.x2,y1:ge.y2,x2:ge.x1,y2:ge.y1},vector:{x:-ze.x,y:-ze.y},vectorNorm:{x:-Fe.x,y:-Fe.y},vectorNormInverse:{x:-Ve.x,y:-Ve.y}}}var xe=Ce?R:A;ae.nodesOverlap=xe.nodesOverlap,ae.srcIntn=xe.srcIntn,ae.tgtIntn=xe.tgtIntn,ae.isRound=ce.startsWith("round"),n&&(M.isParent()||M.isChild()||O.isParent()||O.isChild())&&(M.parents().anySame(O)||O.parents().anySame(M)||M.same(O)&&M.isParent())?e.findCompoundLoopPoints(Y,xe,H,Ae):M===O?e.findLoopPoints(Y,xe,H,Ae):ce.endsWith("segments")?e.findSegmentsPoints(Y,xe):ce.endsWith("taxi")?e.findTaxiPoints(Y,xe):ce==="straight"||!Ae&&A.eles.length%2===1&&H===Math.floor(A.eles.length/2)?e.findStraightEdgePoints(Y):e.findBezierPoints(Y,xe,H,Ae,Ce),e.findEndpoints(Y),e.tryToCorrectInvalidPoints(Y,xe),e.checkForInvalidEdgeWarning(Y),e.storeAllpts(Y),e.storeEdgeProjections(Y),e.calculateArrowAngles(Y),e.recalculateEdgeLabelProjections(Y),e.calculateLabelAngles(Y)}},x=0;x<o.length;x++)C();this.findHaystackPoints(l)}};function Kv(r){var e=[];if(r!=null){for(var t=0;t<r.length;t+=2){var a=r[t],n=r[t+1];e.push({x:a,y:n})}return e}}pr.getSegmentPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="segments")return Kv(e.segpts)},pr.getControlPoints=function(r){var e=r[0]._private.rscratch;this.recalculateRenderedStyle(r);var t=e.edgeType;if(t==="bezier"||t==="multibezier"||t==="self"||t==="compound")return Kv(e.ctrlpts)},pr.getEdgeMidpoint=function(r){var e=r[0]._private.rscratch;return this.recalculateRenderedStyle(r),{x:e.midX,y:e.midY}};var La={};La.manualEndptToPx=function(r,e){var t=this,a=r.position(),n=r.outerWidth(),i=r.outerHeight(),s=r._private.rscratch;if(e.value.length===2){var o=[e.pfValue[0],e.pfValue[1]];return e.units[0]==="%"&&(o[0]=o[0]*n),e.units[1]==="%"&&(o[1]=o[1]*i),o[0]+=a.x,o[1]+=a.y,o}else{var l=e.pfValue[0];l=-Math.PI/2+l;var u=2*Math.max(n,i),v=[a.x+Math.cos(l)*u,a.y+Math.sin(l)*u];return t.nodeShapes[this.getNodeShape(r)].intersectLine(a.x,a.y,n,i,v[0],v[1],0,r.pstyle("corner-radius").value==="auto"?"auto":r.pstyle("corner-radius").pfValue,s)}},La.findEndpoints=function(r){var e,t,a,n,i=this,s,o=r.source()[0],l=r.target()[0],u=o.position(),v=l.position(),f=r.pstyle("target-arrow-shape").value,c=r.pstyle("source-arrow-shape").value,h=r.pstyle("target-distance-from-node").pfValue,d=r.pstyle("source-distance-from-node").pfValue,y=o._private.rscratch,g=l._private.rscratch,p=r.pstyle("curve-style").value,m=r._private.rscratch,b=m.edgeType,w=tt(p,"taxi"),E=b==="self"||b==="compound",C=b==="bezier"||b==="multibezier"||E,x=b!=="bezier",T=b==="straight"||b==="segments",k=b==="segments",D=C||x||T,B=E||w,P=r.pstyle("source-endpoint"),A=B?"outside-to-node":P.value,R=o.pstyle("corner-radius").value==="auto"?"auto":o.pstyle("corner-radius").pfValue,L=r.pstyle("target-endpoint"),I=B?"outside-to-node":L.value,M=l.pstyle("corner-radius").value==="auto"?"auto":l.pstyle("corner-radius").pfValue;m.srcManEndpt=P,m.tgtManEndpt=L;var O,V,G,N,F=(e=(L==null||(t=L.pfValue)===null||t===void 0?void 0:t.length)===2?L.pfValue:null)!==null&&e!==void 0?e:[0,0],U=(a=(P==null||(n=P.pfValue)===null||n===void 0?void 0:n.length)===2?P.pfValue:null)!==null&&a!==void 0?a:[0,0];if(C){var Q=[m.ctrlpts[0],m.ctrlpts[1]],K=x?[m.ctrlpts[m.ctrlpts.length-2],m.ctrlpts[m.ctrlpts.length-1]]:Q;O=K,V=Q}else if(T){var j=k?m.segpts.slice(0,2):[v.x+F[0],v.y+F[1]],re=k?m.segpts.slice(m.segpts.length-2):[u.x+U[0],u.y+U[1]];O=re,V=j}if(I==="inside-to-node")s=[v.x,v.y];else if(L.units)s=this.manualEndptToPx(l,L);else if(I==="outside-to-line")s=m.tgtIntn;else if(I==="outside-to-node"||I==="outside-to-node-or-label"?G=O:(I==="outside-to-line"||I==="outside-to-line-or-label")&&(G=[u.x,u.y]),s=i.nodeShapes[this.getNodeShape(l)].intersectLine(v.x,v.y,l.outerWidth(),l.outerHeight(),G[0],G[1],0,M,g),I==="outside-to-node-or-label"||I==="outside-to-line-or-label"){var ne=l._private.rscratch,J=ne.labelWidth,z=ne.labelHeight,q=ne.labelX,H=ne.labelY,Y=J/2,ae=z/2,ce=l.pstyle("text-valign").value;ce==="top"?H-=ae:ce==="bottom"&&(H+=ae);var Ae=l.pstyle("text-halign").value;Ae==="left"?q-=Y:Ae==="right"&&(q+=Y);var Ce=ba(G[0],G[1],[q-Y,H-ae,q+Y,H-ae,q+Y,H+ae,q-Y,H+ae],v.x,v.y);if(Ce.length>0){var we=u,ye=Et(we,Ft(s)),ie=Et(we,Ft(Ce)),de=ye;if(ie<ye&&(s=Ce,de=ie),Ce.length>2){var ge=Et(we,{x:Ce[2],y:Ce[3]});ge<de&&(s=[Ce[2],Ce[3]])}}}var Ee=un(s,O,i.arrowShapes[f].spacing(r)+h),pe=un(s,O,i.arrowShapes[f].gap(r)+h);if(m.endX=pe[0],m.endY=pe[1],m.arrowEndX=Ee[0],m.arrowEndY=Ee[1],A==="inside-to-node")s=[u.x,u.y];else if(P.units)s=this.manualEndptToPx(o,P);else if(A==="outside-to-line")s=m.srcIntn;else if(A==="outside-to-node"||A==="outside-to-node-or-label"?N=V:(A==="outside-to-line"||A==="outside-to-line-or-label")&&(N=[v.x,v.y]),s=i.nodeShapes[this.getNodeShape(o)].intersectLine(u.x,u.y,o.outerWidth(),o.outerHeight(),N[0],N[1],0,R,y),A==="outside-to-node-or-label"||A==="outside-to-line-or-label"){var ke=o._private.rscratch,Re=ke.labelWidth,ze=ke.labelHeight,Fe=ke.labelX,Ve=ke.labelY,xe=Re/2,ue=ze/2,X=o.pstyle("text-valign").value;X==="top"?Ve-=ue:X==="bottom"&&(Ve+=ue);var S=o.pstyle("text-halign").value;S==="left"?Fe-=xe:S==="right"&&(Fe+=xe);var _=ba(N[0],N[1],[Fe-xe,Ve-ue,Fe+xe,Ve-ue,Fe+xe,Ve+ue,Fe-xe,Ve+ue],u.x,u.y);if(_.length>0){var W=v,$=Et(W,Ft(s)),Z=Et(W,Ft(_)),oe=$;if(Z<$&&(s=[_[0],_[1]],oe=Z),_.length>2){var ee=Et(W,{x:_[2],y:_[3]});ee<oe&&(s=[_[2],_[3]])}}}var ve=un(s,V,i.arrowShapes[c].spacing(r)+d),le=un(s,V,i.arrowShapes[c].gap(r)+d);m.startX=le[0],m.startY=le[1],m.arrowStartX=ve[0],m.arrowStartY=ve[1],D&&(!te(m.startX)||!te(m.startY)||!te(m.endX)||!te(m.endY)?m.badLine=!0:m.badLine=!1)},La.getSourceEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[0],y:e.haystackPts[1]};default:return{x:e.arrowStartX,y:e.arrowStartY}}},La.getTargetEndpoint=function(r){var e=r[0]._private.rscratch;switch(this.recalculateRenderedStyle(r),e.edgeType){case"haystack":return{x:e.haystackPts[2],y:e.haystackPts[3]};default:return{x:e.arrowEndX,y:e.arrowEndY}}};var ro={};function Kp(r,e,t){for(var a=function(u,v,f,c){return ir(u,v,f,c)},n=e._private,i=n.rstyle.bezierPts,s=0;s<r.bezierProjPcts.length;s++){var o=r.bezierProjPcts[s];i.push({x:a(t[0],t[2],t[4],o),y:a(t[1],t[3],t[5],o)})}}ro.storeEdgeProjections=function(r){var e=r._private,t=e.rscratch,a=t.edgeType;if(e.rstyle.bezierPts=null,e.rstyle.linePts=null,e.rstyle.haystackPts=null,a==="multibezier"||a==="bezier"||a==="self"||a==="compound"){e.rstyle.bezierPts=[];for(var n=0;n+5<t.allpts.length;n+=4)Kp(this,r,t.allpts.slice(n,n+6))}else if(a==="segments")for(var i=e.rstyle.linePts=[],n=0;n+1<t.allpts.length;n+=2)i.push({x:t.allpts[n],y:t.allpts[n+1]});else if(a==="haystack"){var s=t.haystackPts;e.rstyle.haystackPts=[{x:s[0],y:s[1]},{x:s[2],y:s[3]}]}e.rstyle.arrowWidth=this.getArrowWidth(r.pstyle("width").pfValue,r.pstyle("arrow-scale").value)*this.arrowShapeWidth},ro.recalculateEdgeProjections=function(r){this.findEdgeControlPoints(r)};var _r={};_r.recalculateNodeLabelProjection=function(r){var e=r.pstyle("label").strValue;if(!rt(e)){var t,a,n=r._private,i=r.width(),s=r.height(),o=r.padding(),l=r.position(),u=r.pstyle("text-halign").strValue,v=r.pstyle("text-valign").strValue,f=n.rscratch,c=n.rstyle;switch(u){case"left":t=l.x-i/2-o;break;case"right":t=l.x+i/2+o;break;default:t=l.x}switch(v){case"top":a=l.y-s/2-o;break;case"bottom":a=l.y+s/2+o;break;default:a=l.y}f.labelX=t,f.labelY=a,c.labelX=t,c.labelY=a,this.calculateLabelAngles(r),this.applyLabelDimensions(r)}};var Xv=function(e,t){var a=Math.atan(t/e);return e===0&&a<0&&(a=a*-1),a},Yv=function(e,t){var a=t.x-e.x,n=t.y-e.y;return Xv(a,n)},Xp=function(e,t,a,n){var i=ya(0,n-.001,1),s=ya(0,n+.001,1),o=Vt(e,t,a,i),l=Vt(e,t,a,s);return Yv(o,l)};_r.recalculateEdgeLabelProjections=function(r){var e,t=r._private,a=t.rscratch,n=this,i={mid:r.pstyle("label").strValue,source:r.pstyle("source-label").strValue,target:r.pstyle("target-label").strValue};if(i.mid||i.source||i.target){e={x:a.midX,y:a.midY};var s=function(f,c,h){$r(t.rscratch,f,c,h),$r(t.rstyle,f,c,h)};s("labelX",null,e.x),s("labelY",null,e.y);var o=Xv(a.midDispX,a.midDispY);s("labelAutoAngle",null,o);var l=function(){if(l.cache)return l.cache;for(var f=[],c=0;c+5<a.allpts.length;c+=4){var h={x:a.allpts[c],y:a.allpts[c+1]},d={x:a.allpts[c+2],y:a.allpts[c+3]},y={x:a.allpts[c+4],y:a.allpts[c+5]};f.push({p0:h,p1:d,p2:y,startDist:0,length:0,segments:[]})}var g=t.rstyle.bezierPts,p=n.bezierProjPcts.length;function m(x,T,k,D,B){var P=xt(T,k),A=x.segments[x.segments.length-1],R={p0:T,p1:k,t0:D,t1:B,startDist:A?A.startDist+A.length:0,length:P};x.segments.push(R),x.length+=P}for(var b=0;b<f.length;b++){var w=f[b],E=f[b-1];E&&(w.startDist=E.startDist+E.length),m(w,w.p0,g[b*p],0,n.bezierProjPcts[0]);for(var C=0;C<p-1;C++)m(w,g[b*p+C],g[b*p+C+1],n.bezierProjPcts[C],n.bezierProjPcts[C+1]);m(w,g[b*p+p-1],w.p2,n.bezierProjPcts[p-1],1)}return l.cache=f},u=function(f){var c,h=f==="source";if(i[f]){var d=r.pstyle(f+"-text-offset").pfValue;switch(a.edgeType){case"self":case"compound":case"bezier":case"multibezier":{for(var y=l(),g,p=0,m=0,b=0;b<y.length;b++){for(var w=y[h?b:y.length-1-b],E=0;E<w.segments.length;E++){var C=w.segments[h?E:w.segments.length-1-E],x=b===y.length-1&&E===w.segments.length-1;if(p=m,m+=C.length,m>=d||x){g={cp:w,segment:C};break}}if(g)break}var T=g.cp,k=g.segment,D=(d-p)/k.length,B=k.t1-k.t0,P=h?k.t0+B*D:k.t1-B*D;P=ya(0,P,1),e=Vt(T.p0,T.p1,T.p2,P),c=Xp(T.p0,T.p1,T.p2,P);break}case"straight":case"segments":case"haystack":{for(var A=0,R,L,I,M,O=a.allpts.length,V=0;V+3<O&&(h?(I={x:a.allpts[V],y:a.allpts[V+1]},M={x:a.allpts[V+2],y:a.allpts[V+3]}):(I={x:a.allpts[O-2-V],y:a.allpts[O-1-V]},M={x:a.allpts[O-4-V],y:a.allpts[O-3-V]}),R=xt(I,M),L=A,A+=R,!(A>=d));V+=2);var G=d-L,N=G/R;N=ya(0,N,1),e=pd(I,M,N),c=Yv(I,M);break}}s("labelX",f,e.x),s("labelY",f,e.y),s("labelAutoAngle",f,c)}};u("source"),u("target"),this.applyLabelDimensions(r)}},_r.applyLabelDimensions=function(r){this.applyPrefixedLabelDimensions(r),r.isEdge()&&(this.applyPrefixedLabelDimensions(r,"source"),this.applyPrefixedLabelDimensions(r,"target"))},_r.applyPrefixedLabelDimensions=function(r,e){var t=r._private,a=this.getLabelText(r,e),n=wt(a,r._private.labelDimsKey);if(Cr(t.rscratch,"prefixedLabelDimsKey",e)!==n){$r(t.rscratch,"prefixedLabelDimsKey",e,n);var i=this.calculateLabelDimensions(r,a),s=r.pstyle("line-height").pfValue,o=r.pstyle("text-wrap").strValue,l=Cr(t.rscratch,"labelWrapCachedLines",e)||[],u=o!=="wrap"?1:Math.max(l.length,1),v=i.height/u,f=v*s,c=i.width,h=i.height+(u-1)*(s-1)*v;$r(t.rstyle,"labelWidth",e,c),$r(t.rscratch,"labelWidth",e,c),$r(t.rstyle,"labelHeight",e,h),$r(t.rscratch,"labelHeight",e,h),$r(t.rscratch,"labelLineHeight",e,f)}},_r.getLabelText=function(r,e){var t=r._private,a=e?e+"-":"",n=r.pstyle(a+"label").strValue,i=r.pstyle("text-transform").value,s=function(U,Q){return Q?($r(t.rscratch,U,e,Q),Q):Cr(t.rscratch,U,e)};if(!n)return"";i=="none"||(i=="uppercase"?n=n.toUpperCase():i=="lowercase"&&(n=n.toLowerCase()));var o=r.pstyle("text-wrap").value;if(o==="wrap"){var l=s("labelKey");if(l!=null&&s("labelWrapKey")===l)return s("labelWrapCachedText");for(var u="\u200B",v=n.split(`
`),f=r.pstyle("text-max-width").pfValue,c=r.pstyle("text-overflow-wrap").value,h=c==="anywhere",d=[],y=/[\s\u200b]+|$/g,g=0;g<v.length;g++){var p=v[g],m=this.calculateLabelDimensions(r,p),b=m.width;if(h){var w=p.split("").join(u);p=w}if(b>f){var E=p.matchAll(y),C="",x=0,T=Er(E),k;try{for(T.s();!(k=T.n()).done;){var D=k.value,B=D[0],P=p.substring(x,D.index);x=D.index+B.length;var A=C.length===0?P:C+P+B,R=this.calculateLabelDimensions(r,A),L=R.width;L<=f?C+=P+B:(C&&d.push(C),C=P+B)}}catch(F){T.e(F)}finally{T.f()}C.match(/^[\s\u200b]+$/)||d.push(C)}else d.push(p)}s("labelWrapCachedLines",d),n=s("labelWrapCachedText",d.join(`
`)),s("labelWrapKey",l)}else if(o==="ellipsis"){var I=r.pstyle("text-max-width").pfValue,M="",O="\u2026",V=!1;if(this.calculateLabelDimensions(r,n).width<I)return n;for(var G=0;G<n.length;G++){var N=this.calculateLabelDimensions(r,M+n[G]+O).width;if(N>I)break;M+=n[G],G===n.length-1&&(V=!0)}return V||(M+=O),M}return n},_r.getLabelJustification=function(r){var e=r.pstyle("text-justification").strValue,t=r.pstyle("text-halign").strValue;if(e==="auto")if(r.isNode())switch(t){case"left":return"right";case"right":return"left";default:return"center"}else return"center";else return e},_r.calculateLabelDimensions=function(r,e){var t=this,a=t.cy.window(),n=a.document,i=0,s=r.pstyle("font-style").strValue,o=r.pstyle("font-size").pfValue,l=r.pstyle("font-family").strValue,u=r.pstyle("font-weight").strValue,v=this.labelCalcCanvas,f=this.labelCalcCanvasContext;if(!v){v=this.labelCalcCanvas=n.createElement("canvas"),f=this.labelCalcCanvasContext=v.getContext("2d");var c=v.style;c.position="absolute",c.left="-9999px",c.top="-9999px",c.zIndex="-1",c.visibility="hidden",c.pointerEvents="none"}f.font="".concat(s," ").concat(u," ").concat(o,"px ").concat(l);for(var h=0,d=0,y=e.split(`
`),g=0;g<y.length;g++){var p=y[g],m=f.measureText(p),b=Math.ceil(m.width),w=o;h=Math.max(b,h),d+=w}return h+=i,d+=i,{width:h,height:d}},_r.calculateLabelAngle=function(r,e){var t=r._private,a=t.rscratch,n=r.isEdge(),i=e?e+"-":"",s=r.pstyle(i+"text-rotation"),o=s.strValue;return o==="none"?0:n&&o==="autorotate"?a.labelAutoAngle:o==="autorotate"?0:s.pfValue},_r.calculateLabelAngles=function(r){var e=this,t=r.isEdge(),a=r._private,n=a.rscratch;n.labelAngle=e.calculateLabelAngle(r),t&&(n.sourceLabelAngle=e.calculateLabelAngle(r,"source"),n.targetLabelAngle=e.calculateLabelAngle(r,"target"))};var Zv={},Qv=28,Jv=!1;Zv.getNodeShape=function(r){var e=this,t=r.pstyle("shape").value;if(t==="cutrectangle"&&(r.width()<Qv||r.height()<Qv))return Jv||(Oe("The `cutrectangle` node shape can not be used at small sizes so `rectangle` is used instead"),Jv=!0),"rectangle";if(r.isParent())return t==="rectangle"||t==="roundrectangle"||t==="round-rectangle"||t==="cutrectangle"||t==="cut-rectangle"||t==="barrel"?t:"rectangle";if(t==="polygon"){var a=r.pstyle("shape-polygon-points").value;return e.nodeShapes.makePolygon(a).name}return t};var Vn={};Vn.registerCalculationListeners=function(){var r=this.cy,e=r.collection(),t=this,a=function(s){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;if(e.merge(s),o)for(var l=0;l<s.length;l++){var u=s[l],v=u._private,f=v.rstyle;f.clean=!1,f.cleanConnected=!1}};t.binder(r).on("bounds.* dirty.*",function(s){var o=s.target;a(o)}).on("style.* background.*",function(s){var o=s.target;a(o,!1)});var n=function(s){if(s){var o=t.onUpdateEleCalcsFns;e.cleanStyle();for(var l=0;l<e.length;l++){var u=e[l],v=u._private.rstyle;u.isNode()&&!v.cleanConnected&&(a(u.connectedEdges()),v.cleanConnected=!0)}if(o)for(var f=0;f<o.length;f++){var c=o[f];c(s,e)}t.recalculateRenderedStyle(e),e=r.collection()}};t.flushRenderedStyleQueue=function(){n(!0)},t.beforeRender(n,t.beforeRenderPriorities.eleCalcs)},Vn.onUpdateEleCalcs=function(r){var e=this.onUpdateEleCalcsFns=this.onUpdateEleCalcsFns||[];e.push(r)},Vn.recalculateRenderedStyle=function(r,e){var t=function(w){return w._private.rstyle.cleanConnected};if(r.length!==0){var a=[],n=[];if(!this.destroyed){e===void 0&&(e=!0);for(var i=0;i<r.length;i++){var s=r[i],o=s._private,l=o.rstyle;s.isEdge()&&(!t(s.source())||!t(s.target()))&&(l.clean=!1),s.isEdge()&&s.isBundledBezier()&&s.parallelEdges().some(function(b){return!b._private.rstyle.clean&&b.isBundledBezier()})&&(l.clean=!1),!(e&&l.clean||s.removed())&&s.pstyle("display").value!=="none"&&(o.group==="nodes"?n.push(s):a.push(s),l.clean=!0)}for(var u=0;u<n.length;u++){var v=n[u],f=v._private,c=f.rstyle,h=v.position();this.recalculateNodeLabelProjection(v),c.nodeX=h.x,c.nodeY=h.y,c.nodeW=v.pstyle("width").pfValue,c.nodeH=v.pstyle("height").pfValue}this.recalculateEdgeProjections(a);for(var d=0;d<a.length;d++){var y=a[d],g=y._private,p=g.rstyle,m=g.rscratch;p.srcX=m.arrowStartX,p.srcY=m.arrowStartY,p.tgtX=m.arrowEndX,p.tgtY=m.arrowEndY,p.midX=m.midX,p.midY=m.midY,p.labelAngle=m.labelAngle,p.sourceLabelAngle=m.sourceLabelAngle,p.targetLabelAngle=m.targetLabelAngle}}}};var qn={};qn.updateCachedGrabbedEles=function(){var r=this.cachedZSortedEles;if(r){r.drag=[],r.nondrag=[];for(var e=[],t=0;t<r.length;t++){var a=r[t],n=a._private.rscratch;a.grabbed()&&!a.isParent()?e.push(a):n.inDragLayer?r.drag.push(a):r.nondrag.push(a)}for(var t=0;t<e.length;t++){var a=e[t];r.drag.push(a)}}},qn.invalidateCachedZSortedEles=function(){this.cachedZSortedEles=null},qn.getCachedZSortedEles=function(r){if(r||!this.cachedZSortedEles){var e=this.cy.mutableElements().toArray();e.sort(cv),e.interactive=e.filter(function(t){return t.interactive()}),this.cachedZSortedEles=e,this.updateCachedGrabbedEles()}else e=this.cachedZSortedEles;return e};var jv={};[kt,Nn,pr,La,ro,_r,Zv,Vn,qn].forEach(function(r){me(jv,r)});var ef={};ef.getCachedImage=function(r,e,t){var a=this,n=a.imageCache=a.imageCache||{},i=n[r];if(i)return i.image.complete||i.image.addEventListener("load",t),i.image;i=n[r]=n[r]||{};var s=i.image=new Image;s.addEventListener("load",t),s.addEventListener("error",function(){s.error=!0});var o="data:",l=r.substring(0,o.length).toLowerCase()===o;return l||(e=e==="null"?null:e,s.crossOrigin=e),s.src=r,s};var ea={};ea.registerBinding=function(r,e,t,a){var n=Array.prototype.slice.apply(arguments,[1]);if(Array.isArray(r)){for(var i=[],s=0;s<r.length;s++){var o=r[s];if(o!==void 0){var l=this.binder(o);i.push(l.on.apply(l,n))}}return i}var l=this.binder(r);return l.on.apply(l,n)},ea.binder=function(r){var e=this,t=e.cy.window(),a=r===t||r===t.document||r===t.document.body||sc(r);if(e.supportsPassiveEvents==null){var n=!1;try{var i=Object.defineProperty({},"passive",{get:function(){return n=!0,!0}});t.addEventListener("test",null,i)}catch(o){}e.supportsPassiveEvents=n}var s=function(l,u,v){var f=Array.prototype.slice.call(arguments);return a&&e.supportsPassiveEvents&&(f[2]={capture:v!=null?v:!1,passive:!1,once:!1}),e.bindings.push({target:r,args:f}),(r.addEventListener||r.on).apply(r,f),this};return{on:s,addEventListener:s,addListener:s,bind:s}},ea.nodeIsDraggable=function(r){return r&&r.isNode()&&!r.locked()&&r.grabbable()},ea.nodeIsGrabbable=function(r){return this.nodeIsDraggable(r)&&r.interactive()},ea.load=function(){var r=this,e=r.cy.window(),t=function(S){return S.selected()},a=function(S){var _=S.getRootNode();if(_&&_.nodeType===11&&_.host!==void 0)return _},n=function(S,_,W,$){S==null&&(S=r.cy);for(var Z=0;Z<_.length;Z++){var oe=_[Z];S.emit({originalEvent:W,type:oe,position:$})}},i=function(S){return S.shiftKey||S.metaKey||S.ctrlKey},s=function(S,_){var W=!0;if(r.cy.hasCompoundNodes()&&S&&S.pannable())for(var $=0;_&&$<_.length;$++){var S=_[$];if(S.isNode()&&S.isParent()&&!S.pannable()){W=!1;break}}else W=!0;return W},o=function(S){S[0]._private.grabbed=!0},l=function(S){S[0]._private.grabbed=!1},u=function(S){S[0]._private.rscratch.inDragLayer=!0},v=function(S){S[0]._private.rscratch.inDragLayer=!1},f=function(S){S[0]._private.rscratch.isGrabTarget=!0},c=function(S){S[0]._private.rscratch.isGrabTarget=!1},h=function(S,_){var W=_.addToList,$=W.has(S);!$&&S.grabbable()&&!S.locked()&&(W.merge(S),o(S))},d=function(S,_){if(S.cy().hasCompoundNodes()&&!(_.inDragLayer==null&&_.addToList==null)){var W=S.descendants();_.inDragLayer&&(W.forEach(u),W.connectedEdges().forEach(u)),_.addToList&&h(W,_)}},y=function(S,_){_=_||{};var W=S.cy().hasCompoundNodes();_.inDragLayer&&(S.forEach(u),S.neighborhood().stdFilter(function($){return!W||$.isEdge()}).forEach(u)),_.addToList&&S.forEach(function($){h($,_)}),d(S,_),m(S,{inDragLayer:_.inDragLayer}),r.updateCachedGrabbedEles()},g=y,p=function(S){S&&(r.getCachedZSortedEles().forEach(function(_){l(_),v(_),c(_)}),r.updateCachedGrabbedEles())},m=function(S,_){if(!(_.inDragLayer==null&&_.addToList==null)&&S.cy().hasCompoundNodes()){var W=S.ancestors().orphans();if(!W.same(S)){var $=W.descendants().spawnSelf().merge(W).unmerge(S).unmerge(S.descendants()),Z=$.connectedEdges();_.inDragLayer&&(Z.forEach(u),$.forEach(u)),_.addToList&&$.forEach(function(oe){h(oe,_)})}}},b=function(){document.activeElement!=null&&document.activeElement.blur!=null&&document.activeElement.blur()},w=typeof MutationObserver!="undefined",E=typeof ResizeObserver!="undefined";w?(r.removeObserver=new MutationObserver(function(X){for(var S=0;S<X.length;S++){var _=X[S],W=_.removedNodes;if(W)for(var $=0;$<W.length;$++){var Z=W[$];if(Z===r.container){r.destroy();break}}}}),r.container.parentNode&&r.removeObserver.observe(r.container.parentNode,{childList:!0})):r.registerBinding(r.container,"DOMNodeRemoved",function(X){r.destroy()});var C=ca(function(){r.cy.resize()},100);w&&(r.styleObserver=new MutationObserver(C),r.styleObserver.observe(r.container,{attributes:!0})),r.registerBinding(e,"resize",C),E&&(r.resizeObserver=new ResizeObserver(C),r.resizeObserver.observe(r.container));var x=function(S,_){for(;S!=null;)_(S),S=S.parentNode},T=function(){r.invalidateContainerClientCoordsCache()};x(r.container,function(X){r.registerBinding(X,"transitionend",T),r.registerBinding(X,"animationend",T),r.registerBinding(X,"scroll",T)}),r.registerBinding(r.container,"contextmenu",function(X){X.preventDefault()});var k=function(){return r.selection[4]!==0},D=function(S){for(var _=r.findContainerClientCoords(),W=_[0],$=_[1],Z=_[2],oe=_[3],ee=S.touches?S.touches:[S],ve=!1,le=0;le<ee.length;le++){var be=ee[le];if(W<=be.clientX&&be.clientX<=W+Z&&$<=be.clientY&&be.clientY<=$+oe){ve=!0;break}}if(!ve)return!1;for(var De=r.container,Te=S.target,fe=Te.parentNode,Pe=!1;fe;){if(fe===De){Pe=!0;break}fe=fe.parentNode}return!!Pe};r.registerBinding(r.container,"mousedown",function(S){if(D(S)&&!(r.hoverData.which===1&&S.which!==1)){S.preventDefault(),b(),r.hoverData.capture=!0,r.hoverData.which=S.which;var _=r.cy,W=[S.clientX,S.clientY],$=r.projectIntoViewport(W[0],W[1]),Z=r.selection,oe=r.findNearestElements($[0],$[1],!0,!1),ee=oe[0],ve=r.dragData.possibleDragElements;r.hoverData.mdownPos=$,r.hoverData.mdownGPos=W;var le=function(Be){return{originalEvent:S,type:Be,position:{x:$[0],y:$[1]}}},be=function(){r.hoverData.tapholdCancelled=!1,clearTimeout(r.hoverData.tapholdTimeout),r.hoverData.tapholdTimeout=setTimeout(function(){if(!r.hoverData.tapholdCancelled){var Be=r.hoverData.down;Be?Be.emit(le("taphold")):_.emit(le("taphold"))}},r.tapholdDuration)};if(S.which==3){r.hoverData.cxtStarted=!0;var De={originalEvent:S,type:"cxttapstart",position:{x:$[0],y:$[1]}};ee?(ee.activate(),ee.emit(De),r.hoverData.down=ee):_.emit(De),r.hoverData.downTime=new Date().getTime(),r.hoverData.cxtDragged=!1}else if(S.which==1){ee&&ee.activate();{if(ee!=null&&r.nodeIsGrabbable(ee)){var Te=function(Be){Be.emit(le("grab"))};if(f(ee),!ee.selected())ve=r.dragData.possibleDragElements=_.collection(),g(ee,{addToList:ve}),ee.emit(le("grabon")).emit(le("grab"));else{ve=r.dragData.possibleDragElements=_.collection();var fe=_.$(function(Pe){return Pe.isNode()&&Pe.selected()&&r.nodeIsGrabbable(Pe)});y(fe,{addToList:ve}),ee.emit(le("grabon")),fe.forEach(Te)}r.redrawHint("eles",!0),r.redrawHint("drag",!0)}r.hoverData.down=ee,r.hoverData.downs=oe,r.hoverData.downTime=new Date().getTime()}n(ee,["mousedown","tapstart","vmousedown"],S,{x:$[0],y:$[1]}),ee==null?(Z[4]=1,r.data.bgActivePosistion={x:$[0],y:$[1]},r.redrawHint("select",!0),r.redraw()):ee.pannable()&&(Z[4]=1),be()}Z[0]=Z[2]=$[0],Z[1]=Z[3]=$[1]}},!1);var B=a(r.container);r.registerBinding([e,B],"mousemove",function(S){var _=r.hoverData.capture;if(!(!_&&!D(S))){var W=!1,$=r.cy,Z=$.zoom(),oe=[S.clientX,S.clientY],ee=r.projectIntoViewport(oe[0],oe[1]),ve=r.hoverData.mdownPos,le=r.hoverData.mdownGPos,be=r.selection,De=null;!r.hoverData.draggingEles&&!r.hoverData.dragging&&!r.hoverData.selecting&&(De=r.findNearestElement(ee[0],ee[1],!0,!1));var Te=r.hoverData.last,fe=r.hoverData.down,Pe=[ee[0]-be[2],ee[1]-be[3]],Be=r.dragData.possibleDragElements,ar;if(le){var Xe=oe[0]-le[0],xr=Xe*Xe,Qe=oe[1]-le[1],ur=Qe*Qe,nr=xr+ur;r.hoverData.isOverThresholdDrag=ar=nr>=r.desktopTapThreshold2}var dr=i(S);ar&&(r.hoverData.tapholdCancelled=!0);var mt=function(){var Rr=r.hoverData.dragDelta=r.hoverData.dragDelta||[];Rr.length===0?(Rr.push(Pe[0]),Rr.push(Pe[1])):(Rr[0]+=Pe[0],Rr[1]+=Pe[1])};W=!0,n(De,["mousemove","vmousemove","tapdrag"],S,{x:ee[0],y:ee[1]});var Je=function(Rr){return{originalEvent:S,type:Rr,position:{x:ee[0],y:ee[1]}}},Qr=function(){r.data.bgActivePosistion=void 0,r.hoverData.selecting||$.emit(Je("boxstart")),be[4]=1,r.hoverData.selecting=!0,r.redrawHint("select",!0),r.redraw()};if(r.hoverData.which===3){if(ar){var Jr=Je("cxtdrag");fe?fe.emit(Jr):$.emit(Jr),r.hoverData.cxtDragged=!0,(!r.hoverData.cxtOver||De!==r.hoverData.cxtOver)&&(r.hoverData.cxtOver&&r.hoverData.cxtOver.emit(Je("cxtdragout")),r.hoverData.cxtOver=De,De&&De.emit(Je("cxtdragover")))}}else if(r.hoverData.dragging){if(W=!0,$.panningEnabled()&&$.userPanningEnabled()){var sa;if(r.hoverData.justStartedPan){var Xn=r.hoverData.mdownPos;sa={x:(ee[0]-Xn[0])*Z,y:(ee[1]-Xn[1])*Z},r.hoverData.justStartedPan=!1}else sa={x:Pe[0]*Z,y:Pe[1]*Z};$.panBy(sa),$.emit(Je("dragpan")),r.hoverData.dragged=!0}ee=r.projectIntoViewport(S.clientX,S.clientY)}else if(be[4]==1&&(fe==null||fe.pannable())){if(ar){if(!r.hoverData.dragging&&$.boxSelectionEnabled()&&(dr||!$.panningEnabled()||!$.userPanningEnabled()))Qr();else if(!r.hoverData.selecting&&$.panningEnabled()&&$.userPanningEnabled()){var It=s(fe,r.hoverData.downs);It&&(r.hoverData.dragging=!0,r.hoverData.justStartedPan=!0,be[4]=0,r.data.bgActivePosistion=Ft(ve),r.redrawHint("select",!0),r.redraw())}fe&&fe.pannable()&&fe.active()&&fe.unactivate()}}else{if(fe&&fe.pannable()&&fe.active()&&fe.unactivate(),(!fe||!fe.grabbed())&&De!=Te&&(Te&&n(Te,["mouseout","tapdragout"],S,{x:ee[0],y:ee[1]}),De&&n(De,["mouseover","tapdragover"],S,{x:ee[0],y:ee[1]}),r.hoverData.last=De),fe)if(ar){if($.boxSelectionEnabled()&&dr)fe&&fe.grabbed()&&(p(Be),fe.emit(Je("freeon")),Be.emit(Je("free")),r.dragData.didDrag&&(fe.emit(Je("dragfreeon")),Be.emit(Je("dragfree")))),Qr();else if(fe&&fe.grabbed()&&r.nodeIsDraggable(fe)){var Sr=!r.dragData.didDrag;Sr&&r.redrawHint("eles",!0),r.dragData.didDrag=!0,r.hoverData.draggingEles||y(Be,{inDragLayer:!0});var yr={x:0,y:0};if(te(Pe[0])&&te(Pe[1])&&(yr.x+=Pe[0],yr.y+=Pe[1],Sr)){var kr=r.hoverData.dragDelta;kr&&te(kr[0])&&te(kr[1])&&(yr.x+=kr[0],yr.y+=kr[1])}r.hoverData.draggingEles=!0,Be.silentShift(yr).emit(Je("position")).emit(Je("drag")),r.redrawHint("drag",!0),r.redraw()}}else mt();W=!0}if(be[2]=ee[0],be[3]=ee[1],W)return S.stopPropagation&&S.stopPropagation(),S.preventDefault&&S.preventDefault(),!1}},!1);var P,A,R;r.registerBinding(e,"mouseup",function(S){if(!(r.hoverData.which===1&&S.which!==1&&r.hoverData.capture)){var _=r.hoverData.capture;if(_){r.hoverData.capture=!1;var W=r.cy,$=r.projectIntoViewport(S.clientX,S.clientY),Z=r.selection,oe=r.findNearestElement($[0],$[1],!0,!1),ee=r.dragData.possibleDragElements,ve=r.hoverData.down,le=i(S);r.data.bgActivePosistion&&(r.redrawHint("select",!0),r.redraw()),r.hoverData.tapholdCancelled=!0,r.data.bgActivePosistion=void 0,ve&&ve.unactivate();var be=function(Xe){return{originalEvent:S,type:Xe,position:{x:$[0],y:$[1]}}};if(r.hoverData.which===3){var De=be("cxttapend");if(ve?ve.emit(De):W.emit(De),!r.hoverData.cxtDragged){var Te=be("cxttap");ve?ve.emit(Te):W.emit(Te)}r.hoverData.cxtDragged=!1,r.hoverData.which=null}else if(r.hoverData.which===1){if(n(oe,["mouseup","tapend","vmouseup"],S,{x:$[0],y:$[1]}),!r.dragData.didDrag&&!r.hoverData.dragged&&!r.hoverData.selecting&&!r.hoverData.isOverThresholdDrag&&(n(ve,["click","tap","vclick"],S,{x:$[0],y:$[1]}),A=!1,S.timeStamp-R<=W.multiClickDebounceTime()?(P&&clearTimeout(P),A=!0,R=null,n(ve,["dblclick","dbltap","vdblclick"],S,{x:$[0],y:$[1]})):(P=setTimeout(function(){A||n(ve,["oneclick","onetap","voneclick"],S,{x:$[0],y:$[1]})},W.multiClickDebounceTime()),R=S.timeStamp)),ve==null&&!r.dragData.didDrag&&!r.hoverData.selecting&&!r.hoverData.dragged&&!i(S)&&(W.$(t).unselect(["tapunselect"]),ee.length>0&&r.redrawHint("eles",!0),r.dragData.possibleDragElements=ee=W.collection()),oe==ve&&!r.dragData.didDrag&&!r.hoverData.selecting&&oe!=null&&oe._private.selectable&&(r.hoverData.dragging||(W.selectionType()==="additive"||le?oe.selected()?oe.unselect(["tapunselect"]):oe.select(["tapselect"]):le||(W.$(t).unmerge(oe).unselect(["tapunselect"]),oe.select(["tapselect"]))),r.redrawHint("eles",!0)),r.hoverData.selecting){var fe=W.collection(r.getAllInBox(Z[0],Z[1],Z[2],Z[3]));r.redrawHint("select",!0),fe.length>0&&r.redrawHint("eles",!0),W.emit(be("boxend"));var Pe=function(Xe){return Xe.selectable()&&!Xe.selected()};W.selectionType()==="additive"||le||W.$(t).unmerge(fe).unselect(),fe.emit(be("box")).stdFilter(Pe).select().emit(be("boxselect")),r.redraw()}if(r.hoverData.dragging&&(r.hoverData.dragging=!1,r.redrawHint("select",!0),r.redrawHint("eles",!0),r.redraw()),!Z[4]){r.redrawHint("drag",!0),r.redrawHint("eles",!0);var Be=ve&&ve.grabbed();p(ee),Be&&(ve.emit(be("freeon")),ee.emit(be("free")),r.dragData.didDrag&&(ve.emit(be("dragfreeon")),ee.emit(be("dragfree"))))}}Z[4]=0,r.hoverData.down=null,r.hoverData.cxtStarted=!1,r.hoverData.draggingEles=!1,r.hoverData.selecting=!1,r.hoverData.isOverThresholdDrag=!1,r.dragData.didDrag=!1,r.hoverData.dragged=!1,r.hoverData.dragDelta=[],r.hoverData.mdownPos=null,r.hoverData.mdownGPos=null,r.hoverData.which=null}}},!1);var L=[],I=4,M,O=1e5,V=function(S,_){for(var W=0;W<S.length;W++)if(S[W]%_!==0)return!1;return!0},G=function(S){for(var _=Math.abs(S[0]),W=1;W<S.length;W++)if(Math.abs(S[W])!==_)return!1;return!0},N=function(S){var _=!1,W=S.deltaY;if(W==null&&(S.wheelDeltaY!=null?W=S.wheelDeltaY/4:S.wheelDelta!=null&&(W=S.wheelDelta/4)),W!==0){if(M==null)if(L.length>=I){var $=L;if(M=V($,5),!M){var Z=Math.abs($[0]);M=G($)&&Z>5}if(M)for(var oe=0;oe<$.length;oe++)O=Math.min(Math.abs($[oe]),O)}else L.push(W),_=!0;else M&&(O=Math.min(Math.abs(W),O));if(!r.scrollingPage){var ee=r.cy,ve=ee.zoom(),le=ee.pan(),be=r.projectIntoViewport(S.clientX,S.clientY),De=[be[0]*ve+le.x,be[1]*ve+le.y];if(r.hoverData.draggingEles||r.hoverData.dragging||r.hoverData.cxtStarted||k()){S.preventDefault();return}if(ee.panningEnabled()&&ee.userPanningEnabled()&&ee.zoomingEnabled()&&ee.userZoomingEnabled()){S.preventDefault(),r.data.wheelZooming=!0,clearTimeout(r.data.wheelTimeout),r.data.wheelTimeout=setTimeout(function(){r.data.wheelZooming=!1,r.redrawHint("eles",!0),r.redraw()},150);var Te;_&&Math.abs(W)>5&&(W=Ci(W)*5),Te=W/-250,M&&(Te/=O,Te*=3),Te=Te*r.wheelSensitivity;var fe=S.deltaMode===1;fe&&(Te*=33);var Pe=ee.zoom()*Math.pow(10,Te);S.type==="gesturechange"&&(Pe=r.gestureStartZoom*S.scale),ee.zoom({level:Pe,renderedPosition:{x:De[0],y:De[1]}}),ee.emit({type:S.type==="gesturechange"?"pinchzoom":"scrollzoom",originalEvent:S,position:{x:be[0],y:be[1]}})}}}};r.registerBinding(r.container,"wheel",N,!0),r.registerBinding(e,"scroll",function(S){r.scrollingPage=!0,clearTimeout(r.scrollingPageTimeout),r.scrollingPageTimeout=setTimeout(function(){r.scrollingPage=!1},250)},!0),r.registerBinding(r.container,"gesturestart",function(S){r.gestureStartZoom=r.cy.zoom(),r.hasTouchStarted||S.preventDefault()},!0),r.registerBinding(r.container,"gesturechange",function(X){r.hasTouchStarted||N(X)},!0),r.registerBinding(r.container,"mouseout",function(S){var _=r.projectIntoViewport(S.clientX,S.clientY);r.cy.emit({originalEvent:S,type:"mouseout",position:{x:_[0],y:_[1]}})},!1),r.registerBinding(r.container,"mouseover",function(S){var _=r.projectIntoViewport(S.clientX,S.clientY);r.cy.emit({originalEvent:S,type:"mouseover",position:{x:_[0],y:_[1]}})},!1);var F,U,Q,K,j,re,ne,J,z,q,H,Y,ae,ce=function(S,_,W,$){return Math.sqrt((W-S)*(W-S)+($-_)*($-_))},Ae=function(S,_,W,$){return(W-S)*(W-S)+($-_)*($-_)},Ce;r.registerBinding(r.container,"touchstart",Ce=function(S){if(r.hasTouchStarted=!0,!!D(S)){b(),r.touchData.capture=!0,r.data.bgActivePosistion=void 0;var _=r.cy,W=r.touchData.now,$=r.touchData.earlier;if(S.touches[0]){var Z=r.projectIntoViewport(S.touches[0].clientX,S.touches[0].clientY);W[0]=Z[0],W[1]=Z[1]}if(S.touches[1]){var Z=r.projectIntoViewport(S.touches[1].clientX,S.touches[1].clientY);W[2]=Z[0],W[3]=Z[1]}if(S.touches[2]){var Z=r.projectIntoViewport(S.touches[2].clientX,S.touches[2].clientY);W[4]=Z[0],W[5]=Z[1]}var oe=function(dr){return{originalEvent:S,type:dr,position:{x:W[0],y:W[1]}}};if(S.touches[1]){r.touchData.singleTouchMoved=!0,p(r.dragData.touchDragEles);var ee=r.findContainerClientCoords();z=ee[0],q=ee[1],H=ee[2],Y=ee[3],F=S.touches[0].clientX-z,U=S.touches[0].clientY-q,Q=S.touches[1].clientX-z,K=S.touches[1].clientY-q,ae=0<=F&&F<=H&&0<=Q&&Q<=H&&0<=U&&U<=Y&&0<=K&&K<=Y;var ve=_.pan(),le=_.zoom();j=ce(F,U,Q,K),re=Ae(F,U,Q,K),ne=[(F+Q)/2,(U+K)/2],J=[(ne[0]-ve.x)/le,(ne[1]-ve.y)/le];var be=200,De=be*be;if(re<De&&!S.touches[2]){var Te=r.findNearestElement(W[0],W[1],!0,!0),fe=r.findNearestElement(W[2],W[3],!0,!0);Te&&Te.isNode()?(Te.activate().emit(oe("cxttapstart")),r.touchData.start=Te):fe&&fe.isNode()?(fe.activate().emit(oe("cxttapstart")),r.touchData.start=fe):_.emit(oe("cxttapstart")),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!0,r.touchData.cxtDragged=!1,r.data.bgActivePosistion=void 0,r.redraw();return}}if(S.touches[2])_.boxSelectionEnabled()&&S.preventDefault();else if(!S.touches[1]){if(S.touches[0]){var Pe=r.findNearestElements(W[0],W[1],!0,!0),Be=Pe[0];if(Be!=null&&(Be.activate(),r.touchData.start=Be,r.touchData.starts=Pe,r.nodeIsGrabbable(Be))){var ar=r.dragData.touchDragEles=_.collection(),Xe=null;r.redrawHint("eles",!0),r.redrawHint("drag",!0),Be.selected()?(Xe=_.$(function(nr){return nr.selected()&&r.nodeIsGrabbable(nr)}),y(Xe,{addToList:ar})):g(Be,{addToList:ar}),f(Be),Be.emit(oe("grabon")),Xe?Xe.forEach(function(nr){nr.emit(oe("grab"))}):Be.emit(oe("grab"))}n(Be,["touchstart","tapstart","vmousedown"],S,{x:W[0],y:W[1]}),Be==null&&(r.data.bgActivePosistion={x:Z[0],y:Z[1]},r.redrawHint("select",!0),r.redraw()),r.touchData.singleTouchMoved=!1,r.touchData.singleTouchStartTime=+new Date,clearTimeout(r.touchData.tapholdTimeout),r.touchData.tapholdTimeout=setTimeout(function(){r.touchData.singleTouchMoved===!1&&!r.pinching&&!r.touchData.selecting&&n(r.touchData.start,["taphold"],S,{x:W[0],y:W[1]})},r.tapholdDuration)}}if(S.touches.length>=1){for(var xr=r.touchData.startPosition=[null,null,null,null,null,null],Qe=0;Qe<W.length;Qe++)xr[Qe]=$[Qe]=W[Qe];var ur=S.touches[0];r.touchData.startGPosition=[ur.clientX,ur.clientY]}}},!1);var we;r.registerBinding(e,"touchmove",we=function(S){var _=r.touchData.capture;if(!(!_&&!D(S))){var W=r.selection,$=r.cy,Z=r.touchData.now,oe=r.touchData.earlier,ee=$.zoom();if(S.touches[0]){var ve=r.projectIntoViewport(S.touches[0].clientX,S.touches[0].clientY);Z[0]=ve[0],Z[1]=ve[1]}if(S.touches[1]){var ve=r.projectIntoViewport(S.touches[1].clientX,S.touches[1].clientY);Z[2]=ve[0],Z[3]=ve[1]}if(S.touches[2]){var ve=r.projectIntoViewport(S.touches[2].clientX,S.touches[2].clientY);Z[4]=ve[0],Z[5]=ve[1]}var le=function(Rm){return{originalEvent:S,type:Rm,position:{x:Z[0],y:Z[1]}}},be=r.touchData.startGPosition,De;if(_&&S.touches[0]&&be){for(var Te=[],fe=0;fe<Z.length;fe++)Te[fe]=Z[fe]-oe[fe];var Pe=S.touches[0].clientX-be[0],Be=Pe*Pe,ar=S.touches[0].clientY-be[1],Xe=ar*ar,xr=Be+Xe;De=xr>=r.touchTapThreshold2}if(_&&r.touchData.cxt){S.preventDefault();var Qe=S.touches[0].clientX-z,ur=S.touches[0].clientY-q,nr=S.touches[1].clientX-z,dr=S.touches[1].clientY-q,mt=Ae(Qe,ur,nr,dr),Je=mt/re,Qr=150,Jr=Qr*Qr,sa=1.5,Xn=sa*sa;if(Je>=Xn||mt>=Jr){r.touchData.cxt=!1,r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var It=le("cxttapend");r.touchData.start?(r.touchData.start.unactivate().emit(It),r.touchData.start=null):$.emit(It)}}if(_&&r.touchData.cxt){var It=le("cxtdrag");r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.touchData.start?r.touchData.start.emit(It):$.emit(It),r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxtDragged=!0;var Sr=r.findNearestElement(Z[0],Z[1],!0,!0);(!r.touchData.cxtOver||Sr!==r.touchData.cxtOver)&&(r.touchData.cxtOver&&r.touchData.cxtOver.emit(le("cxtdragout")),r.touchData.cxtOver=Sr,Sr&&Sr.emit(le("cxtdragover")))}else if(_&&S.touches[2]&&$.boxSelectionEnabled())S.preventDefault(),r.data.bgActivePosistion=void 0,this.lastThreeTouch=+new Date,r.touchData.selecting||$.emit(le("boxstart")),r.touchData.selecting=!0,r.touchData.didSelect=!0,W[4]=1,!W||W.length===0||W[0]===void 0?(W[0]=(Z[0]+Z[2]+Z[4])/3,W[1]=(Z[1]+Z[3]+Z[5])/3,W[2]=(Z[0]+Z[2]+Z[4])/3+1,W[3]=(Z[1]+Z[3]+Z[5])/3+1):(W[2]=(Z[0]+Z[2]+Z[4])/3,W[3]=(Z[1]+Z[3]+Z[5])/3),r.redrawHint("select",!0),r.redraw();else if(_&&S.touches[1]&&!r.touchData.didSelect&&$.zoomingEnabled()&&$.panningEnabled()&&$.userZoomingEnabled()&&$.userPanningEnabled()){S.preventDefault(),r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var yr=r.dragData.touchDragEles;if(yr){r.redrawHint("drag",!0);for(var kr=0;kr<yr.length;kr++){var Ga=yr[kr]._private;Ga.grabbed=!1,Ga.rscratch.inDragLayer=!1}}var Rr=r.touchData.start,Qe=S.touches[0].clientX-z,ur=S.touches[0].clientY-q,nr=S.touches[1].clientX-z,dr=S.touches[1].clientY-q,qf=ce(Qe,ur,nr,dr),Em=qf/j;if(ae){var Cm=Qe-F,Tm=ur-U,Sm=nr-Q,km=dr-K,Dm=(Cm+Sm)/2,Bm=(Tm+km)/2,Ha=$.zoom(),mo=Ha*Em,Yn=$.pan(),_f=J[0]*Ha+Yn.x,Gf=J[1]*Ha+Yn.y,Pm={x:-mo/Ha*(_f-Yn.x-Dm)+_f,y:-mo/Ha*(Gf-Yn.y-Bm)+Gf};if(Rr&&Rr.active()){var yr=r.dragData.touchDragEles;p(yr),r.redrawHint("drag",!0),r.redrawHint("eles",!0),Rr.unactivate().emit(le("freeon")),yr.emit(le("free")),r.dragData.didDrag&&(Rr.emit(le("dragfreeon")),yr.emit(le("dragfree")))}$.viewport({zoom:mo,pan:Pm,cancelOnFailedZoom:!0}),$.emit(le("pinchzoom")),j=qf,F=Qe,U=ur,Q=nr,K=dr,r.pinching=!0}if(S.touches[0]){var ve=r.projectIntoViewport(S.touches[0].clientX,S.touches[0].clientY);Z[0]=ve[0],Z[1]=ve[1]}if(S.touches[1]){var ve=r.projectIntoViewport(S.touches[1].clientX,S.touches[1].clientY);Z[2]=ve[0],Z[3]=ve[1]}if(S.touches[2]){var ve=r.projectIntoViewport(S.touches[2].clientX,S.touches[2].clientY);Z[4]=ve[0],Z[5]=ve[1]}}else if(S.touches[0]&&!r.touchData.didSelect){var Or=r.touchData.start,bo=r.touchData.last,Sr;if(!r.hoverData.draggingEles&&!r.swipePanning&&(Sr=r.findNearestElement(Z[0],Z[1],!0,!0)),_&&Or!=null&&S.preventDefault(),_&&Or!=null&&r.nodeIsDraggable(Or))if(De){var yr=r.dragData.touchDragEles,Hf=!r.dragData.didDrag;Hf&&y(yr,{inDragLayer:!0}),r.dragData.didDrag=!0;var Wa={x:0,y:0};if(te(Te[0])&&te(Te[1])&&(Wa.x+=Te[0],Wa.y+=Te[1],Hf)){r.redrawHint("eles",!0);var Nr=r.touchData.dragDelta;Nr&&te(Nr[0])&&te(Nr[1])&&(Wa.x+=Nr[0],Wa.y+=Nr[1])}r.hoverData.draggingEles=!0,yr.silentShift(Wa).emit(le("position")).emit(le("drag")),r.redrawHint("drag",!0),r.touchData.startPosition[0]==oe[0]&&r.touchData.startPosition[1]==oe[1]&&r.redrawHint("eles",!0),r.redraw()}else{var Nr=r.touchData.dragDelta=r.touchData.dragDelta||[];Nr.length===0?(Nr.push(Te[0]),Nr.push(Te[1])):(Nr[0]+=Te[0],Nr[1]+=Te[1])}if(n(Or||Sr,["touchmove","tapdrag","vmousemove"],S,{x:Z[0],y:Z[1]}),(!Or||!Or.grabbed())&&Sr!=bo&&(bo&&bo.emit(le("tapdragout")),Sr&&Sr.emit(le("tapdragover"))),r.touchData.last=Sr,_)for(var kr=0;kr<Z.length;kr++)Z[kr]&&r.touchData.startPosition[kr]&&De&&(r.touchData.singleTouchMoved=!0);if(_&&(Or==null||Or.pannable())&&$.panningEnabled()&&$.userPanningEnabled()){var Am=s(Or,r.touchData.starts);Am&&(S.preventDefault(),r.data.bgActivePosistion||(r.data.bgActivePosistion=Ft(r.touchData.startPosition)),r.swipePanning?($.panBy({x:Te[0]*ee,y:Te[1]*ee}),$.emit(le("dragpan"))):De&&(r.swipePanning=!0,$.panBy({x:Pe*ee,y:ar*ee}),$.emit(le("dragpan")),Or&&(Or.unactivate(),r.redrawHint("select",!0),r.touchData.start=null)));var ve=r.projectIntoViewport(S.touches[0].clientX,S.touches[0].clientY);Z[0]=ve[0],Z[1]=ve[1]}}for(var fe=0;fe<Z.length;fe++)oe[fe]=Z[fe];_&&S.touches.length>0&&!r.hoverData.draggingEles&&!r.swipePanning&&r.data.bgActivePosistion!=null&&(r.data.bgActivePosistion=void 0,r.redrawHint("select",!0),r.redraw())}},!1);var ye;r.registerBinding(e,"touchcancel",ye=function(S){var _=r.touchData.start;r.touchData.capture=!1,_&&_.unactivate()});var ie,de,ge,Ee;if(r.registerBinding(e,"touchend",ie=function(S){var _=r.touchData.start,W=r.touchData.capture;if(W)S.touches.length===0&&(r.touchData.capture=!1),S.preventDefault();else return;var $=r.selection;r.swipePanning=!1,r.hoverData.draggingEles=!1;var Z=r.cy,oe=Z.zoom(),ee=r.touchData.now,ve=r.touchData.earlier;if(S.touches[0]){var le=r.projectIntoViewport(S.touches[0].clientX,S.touches[0].clientY);ee[0]=le[0],ee[1]=le[1]}if(S.touches[1]){var le=r.projectIntoViewport(S.touches[1].clientX,S.touches[1].clientY);ee[2]=le[0],ee[3]=le[1]}if(S.touches[2]){var le=r.projectIntoViewport(S.touches[2].clientX,S.touches[2].clientY);ee[4]=le[0],ee[5]=le[1]}var be=function(Jr){return{originalEvent:S,type:Jr,position:{x:ee[0],y:ee[1]}}};_&&_.unactivate();var De;if(r.touchData.cxt){if(De=be("cxttapend"),_?_.emit(De):Z.emit(De),!r.touchData.cxtDragged){var Te=be("cxttap");_?_.emit(Te):Z.emit(Te)}r.touchData.start&&(r.touchData.start._private.grabbed=!1),r.touchData.cxt=!1,r.touchData.start=null,r.redraw();return}if(!S.touches[2]&&Z.boxSelectionEnabled()&&r.touchData.selecting){r.touchData.selecting=!1;var fe=Z.collection(r.getAllInBox($[0],$[1],$[2],$[3]));$[0]=void 0,$[1]=void 0,$[2]=void 0,$[3]=void 0,$[4]=0,r.redrawHint("select",!0),Z.emit(be("boxend"));var Pe=function(Jr){return Jr.selectable()&&!Jr.selected()};fe.emit(be("box")).stdFilter(Pe).select().emit(be("boxselect")),fe.nonempty()&&r.redrawHint("eles",!0),r.redraw()}if(_!=null&&_.unactivate(),S.touches[2])r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);else if(!S.touches[1]){if(!S.touches[0]){if(!S.touches[0]){r.data.bgActivePosistion=void 0,r.redrawHint("select",!0);var Be=r.dragData.touchDragEles;if(_!=null){var ar=_._private.grabbed;p(Be),r.redrawHint("drag",!0),r.redrawHint("eles",!0),ar&&(_.emit(be("freeon")),Be.emit(be("free")),r.dragData.didDrag&&(_.emit(be("dragfreeon")),Be.emit(be("dragfree")))),n(_,["touchend","tapend","vmouseup","tapdragout"],S,{x:ee[0],y:ee[1]}),_.unactivate(),r.touchData.start=null}else{var Xe=r.findNearestElement(ee[0],ee[1],!0,!0);n(Xe,["touchend","tapend","vmouseup","tapdragout"],S,{x:ee[0],y:ee[1]})}var xr=r.touchData.startPosition[0]-ee[0],Qe=xr*xr,ur=r.touchData.startPosition[1]-ee[1],nr=ur*ur,dr=Qe+nr,mt=dr*oe*oe;r.touchData.singleTouchMoved||(_||Z.$(":selected").unselect(["tapunselect"]),n(_,["tap","vclick"],S,{x:ee[0],y:ee[1]}),de=!1,S.timeStamp-Ee<=Z.multiClickDebounceTime()?(ge&&clearTimeout(ge),de=!0,Ee=null,n(_,["dbltap","vdblclick"],S,{x:ee[0],y:ee[1]})):(ge=setTimeout(function(){de||n(_,["onetap","voneclick"],S,{x:ee[0],y:ee[1]})},Z.multiClickDebounceTime()),Ee=S.timeStamp)),_!=null&&!r.dragData.didDrag&&_._private.selectable&&mt<r.touchTapThreshold2&&!r.pinching&&(Z.selectionType()==="single"?(Z.$(t).unmerge(_).unselect(["tapunselect"]),_.select(["tapselect"])):_.selected()?_.unselect(["tapunselect"]):_.select(["tapselect"]),r.redrawHint("eles",!0)),r.touchData.singleTouchMoved=!0}}}for(var Je=0;Je<ee.length;Je++)ve[Je]=ee[Je];r.dragData.didDrag=!1,S.touches.length===0&&(r.touchData.dragDelta=[],r.touchData.startPosition=[null,null,null,null,null,null],r.touchData.startGPosition=null,r.touchData.didSelect=!1),S.touches.length<2&&(S.touches.length===1&&(r.touchData.startGPosition=[S.touches[0].clientX,S.touches[0].clientY]),r.pinching=!1,r.redrawHint("eles",!0),r.redraw())},!1),typeof TouchEvent=="undefined"){var pe=[],ke=function(S){return{clientX:S.clientX,clientY:S.clientY,force:1,identifier:S.pointerId,pageX:S.pageX,pageY:S.pageY,radiusX:S.width/2,radiusY:S.height/2,screenX:S.screenX,screenY:S.screenY,target:S.target}},Re=function(S){return{event:S,touch:ke(S)}},ze=function(S){pe.push(Re(S))},Fe=function(S){for(var _=0;_<pe.length;_++){var W=pe[_];if(W.event.pointerId===S.pointerId){pe.splice(_,1);return}}},Ve=function(S){var _=pe.filter(function(W){return W.event.pointerId===S.pointerId})[0];_.event=S,_.touch=ke(S)},xe=function(S){S.touches=pe.map(function(_){return _.touch})},ue=function(S){return S.pointerType==="mouse"||S.pointerType===4};r.registerBinding(r.container,"pointerdown",function(X){ue(X)||(X.preventDefault(),ze(X),xe(X),Ce(X))}),r.registerBinding(r.container,"pointerup",function(X){ue(X)||(Fe(X),xe(X),ie(X))}),r.registerBinding(r.container,"pointercancel",function(X){ue(X)||(Fe(X),xe(X),ye(X))}),r.registerBinding(r.container,"pointermove",function(X){ue(X)||(X.preventDefault(),Ve(X),xe(X),we(X))})}};var Yr={};Yr.generatePolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,draw:function(a,n,i,s,o,l){this.renderer.nodeShapeImpl("polygon",a,n,i,s,o,this.points)},intersectLine:function(a,n,i,s,o,l,u,v){return ba(o,l,this.points,a,n,i/2,s/2,u)},checkPoint:function(a,n,i,s,o,l,u,v){return Kr(a,n,this.points,l,u,s,o,[0,-1],i)},hasMiterBounds:r!=="rectangle",miterBounds:function(a,n,i,s,o,l){return Ed(this.points,a,n,i,s,o)}}},Yr.generateEllipse=function(){return this.nodeShapes.ellipse={renderer:this,name:"ellipse",draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){return Ad(i,s,e,t,a/2+o,n/2+o)},checkPoint:function(e,t,a,n,i,s,o,l){return Ct(e,t,n,i,s,o,a)}}},Yr.generateRoundPolygon=function(r,e){return this.nodeShapes[r]={renderer:this,name:r,points:e,getOrCreateCorners:function(a,n,i,s,o,l,u){if(l[u]!==void 0&&l[u+"-cx"]===a&&l[u+"-cy"]===n)return l[u];l[u]=new Array(e.length/2),l[u+"-cx"]=a,l[u+"-cy"]=n;var v=i/2,f=s/2;o=o==="auto"?hu(i,s):o;for(var c=new Array(e.length/2),h=0;h<e.length/2;h++)c[h]={x:a+v*e[h*2],y:n+f*e[h*2+1]};var d,y,g,p,m=c.length;for(y=c[m-1],d=0;d<m;d++)g=c[d%m],p=c[(d+1)%m],l[u][d]=eo(y,g,p,o),y=g,g=p;return l[u]},draw:function(a,n,i,s,o,l,u){this.renderer.nodeShapeImpl("round-polygon",a,n,i,s,o,this.points,this.getOrCreateCorners(n,i,s,o,l,u,"drawCorners"))},intersectLine:function(a,n,i,s,o,l,u,v,f){return Md(o,l,this.points,a,n,i,s,u,this.getOrCreateCorners(a,n,i,s,v,f,"corners"))},checkPoint:function(a,n,i,s,o,l,u,v,f){return Pd(a,n,this.points,l,u,s,o,this.getOrCreateCorners(l,u,s,o,v,f,"corners"))}}},Yr.generateRoundRectangle=function(){return this.nodeShapes["round-rectangle"]=this.nodeShapes.roundrectangle={renderer:this,name:"round-rectangle",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){return cu(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){var u=n/2,v=i/2;l=l==="auto"?ot(n,i):l,l=Math.min(u,v,l);var f=l*2;return!!(Kr(e,t,this.points,s,o,n,i-f,[0,-1],a)||Kr(e,t,this.points,s,o,n-f,i,[0,-1],a)||Ct(e,t,f,f,s-u+l,o-v+l,a)||Ct(e,t,f,f,s+u-l,o-v+l,a)||Ct(e,t,f,f,s+u-l,o+v-l,a)||Ct(e,t,f,f,s-u+l,o+v-l,a))}}},Yr.generateCutRectangle=function(){return this.nodeShapes["cut-rectangle"]=this.nodeShapes.cutrectangle={renderer:this,name:"cut-rectangle",cornerLength:Di(),points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,null,s)},generateCutTrianglePts:function(e,t,a,n,i){var s=i==="auto"?this.cornerLength:i,o=t/2,l=e/2,u=a-l,v=a+l,f=n-o,c=n+o;return{topLeft:[u,f+s,u+s,f,u+s,f+s],topRight:[v-s,f,v,f+s,v-s,f+s],bottomRight:[v,c-s,v-s,c,v-s,c-s],bottomLeft:[u+s,c,u,c-s,u+s,c-s]}},intersectLine:function(e,t,a,n,i,s,o,l){var u=this.generateCutTrianglePts(a+2*o,n+2*o,e,t,l),v=[].concat.apply([],[u.topLeft.splice(0,4),u.topRight.splice(0,4),u.bottomRight.splice(0,4),u.bottomLeft.splice(0,4)]);return ba(i,s,v,e,t)},checkPoint:function(e,t,a,n,i,s,o,l){var u=l==="auto"?this.cornerLength:l;if(Kr(e,t,this.points,s,o,n,i-2*u,[0,-1],a)||Kr(e,t,this.points,s,o,n-2*u,i,[0,-1],a))return!0;var v=this.generateCutTrianglePts(n,i,s,o);return Tr(e,t,v.topLeft)||Tr(e,t,v.topRight)||Tr(e,t,v.bottomRight)||Tr(e,t,v.bottomLeft)}}},Yr.generateBarrel=function(){return this.nodeShapes.barrel={renderer:this,name:"barrel",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i)},intersectLine:function(e,t,a,n,i,s,o,l){var u=.15,v=.5,f=.85,c=this.generateBarrelBezierPts(a+2*o,n+2*o,e,t),h=function(g){var p=Vt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},u),m=Vt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},v),b=Vt({x:g[0],y:g[1]},{x:g[2],y:g[3]},{x:g[4],y:g[5]},f);return[g[0],g[1],p.x,p.y,m.x,m.y,b.x,b.y,g[4],g[5]]},d=[].concat(h(c.topLeft),h(c.topRight),h(c.bottomRight),h(c.bottomLeft));return ba(i,s,d,e,t)},generateBarrelBezierPts:function(e,t,a,n){var i=t/2,s=e/2,o=a-s,l=a+s,u=n-i,v=n+i,f=Bi(e,t),c=f.heightOffset,h=f.widthOffset,d=f.ctrlPtOffsetPct*e,y={topLeft:[o,u+c,o+d,u,o+h,u],topRight:[l-h,u,l-d,u,l,u+c],bottomRight:[l,v-c,l-d,v,l-h,v],bottomLeft:[o+h,v,o+d,v,o,v-c]};return y.topLeft.isTop=!0,y.topRight.isTop=!0,y.bottomLeft.isBottom=!0,y.bottomRight.isBottom=!0,y},checkPoint:function(e,t,a,n,i,s,o,l){var u=Bi(n,i),v=u.heightOffset,f=u.widthOffset;if(Kr(e,t,this.points,s,o,n,i-2*v,[0,-1],a)||Kr(e,t,this.points,s,o,n-2*f,i,[0,-1],a))return!0;for(var c=this.generateBarrelBezierPts(n,i,s,o),h=function(T,k,D){var B=D[4],P=D[2],A=D[0],R=D[5],L=D[1],I=Math.min(B,A),M=Math.max(B,A),O=Math.min(R,L),V=Math.max(R,L);if(I<=T&&T<=M&&O<=k&&k<=V){var G=Ld(B,P,A),N=Sd(G[0],G[1],G[2],T),F=N.filter(function(U){return 0<=U&&U<=1});if(F.length>0)return F[0]}return null},d=Object.keys(c),y=0;y<d.length;y++){var g=d[y],p=c[g],m=h(e,t,p);if(m!=null){var b=p[5],w=p[3],E=p[1],C=ir(b,w,E,m);if(p.isTop&&C<=t||p.isBottom&&t<=C)return!0}}return!1}}},Yr.generateBottomRoundrectangle=function(){return this.nodeShapes["bottom-round-rectangle"]=this.nodeShapes.bottomroundrectangle={renderer:this,name:"bottom-round-rectangle",points:br(4,0),draw:function(e,t,a,n,i,s){this.renderer.nodeShapeImpl(this.name,e,t,a,n,i,this.points,s)},intersectLine:function(e,t,a,n,i,s,o,l){var u=e-(a/2+o),v=t-(n/2+o),f=v,c=e+(a/2+o),h=st(i,s,e,t,u,v,c,f,!1);return h.length>0?h:cu(i,s,e,t,a,n,o,l)},checkPoint:function(e,t,a,n,i,s,o,l){l=l==="auto"?ot(n,i):l;var u=2*l;if(Kr(e,t,this.points,s,o,n,i-u,[0,-1],a)||Kr(e,t,this.points,s,o,n-u,i,[0,-1],a))return!0;var v=n/2+2*a,f=i/2+2*a,c=[s-v,o-f,s-v,o,s+v,o,s+v,o-f];return!!(Tr(e,t,c)||Ct(e,t,u,u,s+n/2-l,o+i/2-l,a)||Ct(e,t,u,u,s-n/2+l,o+i/2-l,a))}}},Yr.registerNodeShapes=function(){var r=this.nodeShapes={},e=this;this.generateEllipse(),this.generatePolygon("triangle",br(3,0)),this.generateRoundPolygon("round-triangle",br(3,0)),this.generatePolygon("rectangle",br(4,0)),r.square=r.rectangle,this.generateRoundRectangle(),this.generateCutRectangle(),this.generateBarrel(),this.generateBottomRoundrectangle();{var t=[0,1,1,0,0,-1,-1,0];this.generatePolygon("diamond",t),this.generateRoundPolygon("round-diamond",t)}this.generatePolygon("pentagon",br(5,0)),this.generateRoundPolygon("round-pentagon",br(5,0)),this.generatePolygon("hexagon",br(6,0)),this.generateRoundPolygon("round-hexagon",br(6,0)),this.generatePolygon("heptagon",br(7,0)),this.generateRoundPolygon("round-heptagon",br(7,0)),this.generatePolygon("octagon",br(8,0)),this.generateRoundPolygon("round-octagon",br(8,0));var a=new Array(20);{var n=ki(5,0),i=ki(5,Math.PI/5),s=.5*(3-Math.sqrt(5));s*=1.57;for(var o=0;o<i.length/2;o++)i[o*2]*=s,i[o*2+1]*=s;for(var o=0;o<20/4;o++)a[o*4]=n[o*2],a[o*4+1]=n[o*2+1],a[o*4+2]=i[o*2],a[o*4+3]=i[o*2+1]}a=du(a),this.generatePolygon("star",a),this.generatePolygon("vee",[-1,-1,0,-.333,1,-1,0,1]),this.generatePolygon("rhomboid",[-1,-1,.333,-1,1,1,-.333,1]),this.generatePolygon("right-rhomboid",[-.333,-1,1,-1,.333,1,-1,1]),this.nodeShapes.concavehexagon=this.generatePolygon("concave-hexagon",[-1,-.95,-.75,0,-1,.95,1,.95,.75,0,1,-.95]);{var l=[-1,-1,.25,-1,1,0,.25,1,-1,1];this.generatePolygon("tag",l),this.generateRoundPolygon("round-tag",l)}r.makePolygon=function(u){var v=u.join("$"),f="polygon-"+v,c;return(c=this[f])?c:e.generatePolygon(f,u)}};var Ia={};Ia.timeToRender=function(){return this.redrawTotalTime/this.redrawCount},Ia.redraw=function(r){r=r||au();var e=this;e.averageRedrawTime===void 0&&(e.averageRedrawTime=0),e.lastRedrawTime===void 0&&(e.lastRedrawTime=0),e.lastDrawTime===void 0&&(e.lastDrawTime=0),e.requestedFrame=!0,e.renderOptions=r},Ia.beforeRender=function(r,e){if(!this.destroyed){e==null&&$e("Priority is not optional for beforeRender");var t=this.beforeRenderCallbacks;t.push({fn:r,priority:e}),t.sort(function(a,n){return n.priority-a.priority})}};var rf=function(e,t,a){for(var n=e.beforeRenderCallbacks,i=0;i<n.length;i++)n[i].fn(t,a)};Ia.startRenderLoop=function(){var r=this,e=r.cy;if(!r.renderLoopStarted){r.renderLoopStarted=!0;var t=function(n){if(!r.destroyed){if(!e.batching())if(r.requestedFrame&&!r.skipFrame){rf(r,!0,n);var i=Wr();r.render(r.renderOptions);var s=r.lastDrawTime=Wr();r.averageRedrawTime===void 0&&(r.averageRedrawTime=s-i),r.redrawCount===void 0&&(r.redrawCount=0),r.redrawCount++,r.redrawTotalTime===void 0&&(r.redrawTotalTime=0);var o=s-i;r.redrawTotalTime+=o,r.lastRedrawTime=o,r.averageRedrawTime=r.averageRedrawTime/2+o/2,r.requestedFrame=!1}else rf(r,!1,n);r.skipFrame=!1,Za(t)}};Za(t)}};var Yp=function(e){this.init(e)},tf=Yp,ra=tf.prototype;ra.clientFunctions=["redrawHint","render","renderTo","matchCanvasSize","nodeShapeImpl","arrowShapeImpl"],ra.init=function(r){var e=this;e.options=r,e.cy=r.cy;var t=e.container=r.cy.container(),a=e.cy.window();if(a){var n=a.document,i=n.head,s="__________cytoscape_stylesheet",o="__________cytoscape_container",l=n.getElementById(s)!=null;if(t.className.indexOf(o)<0&&(t.className=(t.className||"")+" "+o),!l){var u=n.createElement("style");u.id=s,u.textContent="."+o+" { position: relative; }",i.insertBefore(u,i.children[0])}var v=a.getComputedStyle(t),f=v.getPropertyValue("position");f==="static"&&Oe("A Cytoscape container has style position:static and so can not use UI extensions properly")}e.selection=[void 0,void 0,void 0,void 0,0],e.bezierProjPcts=[.05,.225,.4,.5,.6,.775,.95],e.hoverData={down:null,last:null,downTime:null,triggerMode:null,dragging:!1,initialPan:[null,null],capture:!1},e.dragData={possibleDragElements:[]},e.touchData={start:null,capture:!1,startPosition:[null,null,null,null,null,null],singleTouchStartTime:null,singleTouchMoved:!0,now:[null,null,null,null,null,null],earlier:[null,null,null,null,null,null]},e.redraws=0,e.showFps=r.showFps,e.debug=r.debug,e.webgl=r.webgl,e.hideEdgesOnViewport=r.hideEdgesOnViewport,e.textureOnViewport=r.textureOnViewport,e.wheelSensitivity=r.wheelSensitivity,e.motionBlurEnabled=r.motionBlur,e.forcedPixelRatio=te(r.pixelRatio)?r.pixelRatio:null,e.motionBlur=r.motionBlur,e.motionBlurOpacity=r.motionBlurOpacity,e.motionBlurTransparency=1-e.motionBlurOpacity,e.motionBlurPxRatio=1,e.mbPxRBlurry=1,e.minMbLowQualFrames=4,e.fullQualityMb=!1,e.clearedForMotionBlur=[],e.desktopTapThreshold=r.desktopTapThreshold,e.desktopTapThreshold2=r.desktopTapThreshold*r.desktopTapThreshold,e.touchTapThreshold=r.touchTapThreshold,e.touchTapThreshold2=r.touchTapThreshold*r.touchTapThreshold,e.tapholdDuration=500,e.bindings=[],e.beforeRenderCallbacks=[],e.beforeRenderPriorities={animations:400,eleCalcs:300,eleTxrDeq:200,lyrTxrDeq:150,lyrTxrSkip:100},e.registerNodeShapes(),e.registerArrowShapes(),e.registerCalculationListeners()},ra.notify=function(r,e){var t=this,a=t.cy;if(!this.destroyed){if(r==="init"){t.load();return}if(r==="destroy"){t.destroy();return}(r==="add"||r==="remove"||r==="move"&&a.hasCompoundNodes()||r==="load"||r==="zorder"||r==="mount")&&t.invalidateCachedZSortedEles(),r==="viewport"&&t.redrawHint("select",!0),r==="gc"&&t.redrawHint("gc",!0),(r==="load"||r==="resize"||r==="mount")&&(t.invalidateContainerClientCoordsCache(),t.matchCanvasSize(t.container)),t.redrawHint("eles",!0),t.redrawHint("drag",!0),this.startRenderLoop(),this.redraw()}},ra.destroy=function(){var r=this;r.destroyed=!0,r.cy.stopAnimationLoop();for(var e=0;e<r.bindings.length;e++){var t=r.bindings[e],a=t,n=a.target;(n.off||n.removeEventListener).apply(n,a.args)}if(r.bindings=[],r.beforeRenderCallbacks=[],r.onUpdateEleCalcsFns=[],r.removeObserver&&r.removeObserver.disconnect(),r.styleObserver&&r.styleObserver.disconnect(),r.resizeObserver&&r.resizeObserver.disconnect(),r.labelCalcDiv)try{document.body.removeChild(r.labelCalcDiv)}catch(i){}},ra.isHeadless=function(){return!1},[Ys,jv,ef,ea,Yr,Ia].forEach(function(r){me(ra,r)});var to=1e3/60,af={setupDequeueing:function(e){return function(){var a=this,n=this.renderer;if(!a.dequeueingSetup){a.dequeueingSetup=!0;var i=ca(function(){n.redrawHint("eles",!0),n.redrawHint("drag",!0),n.redraw()},e.deqRedrawThreshold),s=function(u,v){var f=Wr(),c=n.averageRedrawTime,h=n.lastRedrawTime,d=[],y=n.cy.extent(),g=n.getPixelRatio();for(u||n.flushRenderedStyleQueue();;){var p=Wr(),m=p-f,b=p-v;if(h<to){var w=to-(u?c:0);if(b>=e.deqFastCost*w)break}else if(u){if(m>=e.deqCost*h||m>=e.deqAvgCost*c)break}else if(b>=e.deqNoDrawCost*to)break;var E=e.deq(a,g,y);if(E.length>0)for(var C=0;C<E.length;C++)d.push(E[C]);else break}d.length>0&&(e.onDeqd(a,d),!u&&e.shouldRedraw(a,d,g,y)&&i())},o=e.priority||yi;n.beforeRender(s,o(a))}}}},Zp=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ja;jr(this,r),this.idsByKey=new Ur,this.keyForId=new Ur,this.cachesByLvl=new Ur,this.lvls=[],this.getKey=e,this.doesEleInvalidateKey=t}return et(r,[{key:"getIdsFor",value:function(t){t==null&&$e("Can not get id list for null key");var a=this.idsByKey,n=this.idsByKey.get(t);return n||(n=new zt,a.set(t,n)),n}},{key:"addIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).add(a)}},{key:"deleteIdForKey",value:function(t,a){t!=null&&this.getIdsFor(t).delete(a)}},{key:"getNumberOfIdsForKey",value:function(t){return t==null?0:this.getIdsFor(t).size}},{key:"updateKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);this.deleteIdForKey(n,a),this.addIdForKey(i,a),this.keyForId.set(a,i)}},{key:"deleteKeyMappingFor",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteIdForKey(n,a),this.keyForId.delete(a)}},{key:"keyHasChangedFor",value:function(t){var a=t.id(),n=this.keyForId.get(a),i=this.getKey(t);return n!==i}},{key:"isInvalid",value:function(t){return this.keyHasChangedFor(t)||this.doesEleInvalidateKey(t)}},{key:"getCachesAt",value:function(t){var a=this.cachesByLvl,n=this.lvls,i=a.get(t);return i||(i=new Ur,a.set(t,i),n.push(t)),i}},{key:"getCache",value:function(t,a){return this.getCachesAt(a).get(t)}},{key:"get",value:function(t,a){var n=this.getKey(t),i=this.getCache(n,a);return i!=null&&this.updateKeyMappingFor(t),i}},{key:"getForCachedKey",value:function(t,a){var n=this.keyForId.get(t.id()),i=this.getCache(n,a);return i}},{key:"hasCache",value:function(t,a){return this.getCachesAt(a).has(t)}},{key:"has",value:function(t,a){var n=this.getKey(t);return this.hasCache(n,a)}},{key:"setCache",value:function(t,a,n){n.key=t,this.getCachesAt(a).set(t,n)}},{key:"set",value:function(t,a,n){var i=this.getKey(t);this.setCache(i,a,n),this.updateKeyMappingFor(t)}},{key:"deleteCache",value:function(t,a){this.getCachesAt(a).delete(t)}},{key:"delete",value:function(t,a){var n=this.getKey(t);this.deleteCache(n,a)}},{key:"invalidateKey",value:function(t){var a=this;this.lvls.forEach(function(n){return a.deleteCache(t,n)})}},{key:"invalidate",value:function(t){var a=t.id(),n=this.keyForId.get(a);this.deleteKeyMappingFor(t);var i=this.doesEleInvalidateKey(t);return i&&this.invalidateKey(n),i||this.getNumberOfIdsForKey(n)===0}}])}(),nf=25,_n=50,Gn=-4,ao=3,sf=7.99,Qp=8,Jp=1024,jp=1024,ey=1024,ry=.2,ty=.8,ay=10,ny=.15,iy=.1,sy=.9,oy=.9,uy=100,ly=1,ta={dequeue:"dequeue",downscale:"downscale",highQuality:"highQuality"},vy=lr({getKey:null,doesEleInvalidateKey:Ja,drawElement:null,getBoundingBox:null,getRotationPoint:null,getRotationOffset:null,isVisible:jo,allowEdgeTxrCaching:!0,allowParentTxrCaching:!0}),Oa=function(e,t){var a=this;a.renderer=e,a.onDequeues=[];var n=vy(t);me(a,n),a.lookup=new Zp(n.getKey,n.doesEleInvalidateKey),a.setupDequeueing()},tr=Oa.prototype;tr.reasons=ta,tr.getTextureQueue=function(r){var e=this;return e.eleImgCaches=e.eleImgCaches||{},e.eleImgCaches[r]=e.eleImgCaches[r]||[]},tr.getRetiredTextureQueue=function(r){var e=this,t=e.eleImgCaches.retired=e.eleImgCaches.retired||{},a=t[r]=t[r]||[];return a},tr.getElementQueue=function(){var r=this,e=r.eleCacheQueue=r.eleCacheQueue||new pa(function(t,a){return a.reqs-t.reqs});return e},tr.getElementKeyToQueue=function(){var r=this,e=r.eleKeyToCacheQueue=r.eleKeyToCacheQueue||{};return e},tr.getElement=function(r,e,t,a,n){var i=this,s=this.renderer,o=s.cy.zoom(),l=this.lookup;if(!e||e.w===0||e.h===0||isNaN(e.w)||isNaN(e.h)||!r.visible()||r.removed()||!i.allowEdgeTxrCaching&&r.isEdge()||!i.allowParentTxrCaching&&r.isParent())return null;if(a==null&&(a=Math.ceil(Ei(o*t))),a<Gn)a=Gn;else if(o>=sf||a>ao)return null;var u=Math.pow(2,a),v=e.h*u,f=e.w*u,c=s.eleTextBiggerThanMin(r,u);if(!this.isVisible(r,c))return null;var h=l.get(r,a);if(h&&h.invalidated&&(h.invalidated=!1,h.texture.invalidatedWidth-=h.width),h)return h;var d;if(v<=nf?d=nf:v<=_n?d=_n:d=Math.ceil(v/_n)*_n,v>ey||f>jp)return null;var y=i.getTextureQueue(d),g=y[y.length-2],p=function(){return i.recycleTexture(d,f)||i.addTexture(d,f)};g||(g=y[y.length-1]),g||(g=p()),g.width-g.usedWidth<f&&(g=p());for(var m=function(I){return I&&I.scaledLabelShown===c},b=n&&n===ta.dequeue,w=n&&n===ta.highQuality,E=n&&n===ta.downscale,C,x=a+1;x<=ao;x++){var T=l.get(r,x);if(T){C=T;break}}var k=C&&C.level===a+1?C:null,D=function(){g.context.drawImage(k.texture.canvas,k.x,0,k.width,k.height,g.usedWidth,0,f,v)};if(g.context.setTransform(1,0,0,1,0,0),g.context.clearRect(g.usedWidth,0,f,d),m(k))D();else if(m(C))if(w){for(var B=C.level;B>a;B--)k=i.getElement(r,e,t,B,ta.downscale);D()}else return i.queueElement(r,C.level-1),C;else{var P;if(!b&&!w&&!E)for(var A=a-1;A>=Gn;A--){var R=l.get(r,A);if(R){P=R;break}}if(m(P))return i.queueElement(r,a),P;g.context.translate(g.usedWidth,0),g.context.scale(u,u),this.drawElement(g.context,r,e,c,!1),g.context.scale(1/u,1/u),g.context.translate(-g.usedWidth,0)}return h={x:g.usedWidth,texture:g,level:a,scale:u,width:f,height:v,scaledLabelShown:c},g.usedWidth+=Math.ceil(f+Qp),g.eleCaches.push(h),l.set(r,a,h),i.checkTextureFullness(g),h},tr.invalidateElements=function(r){for(var e=0;e<r.length;e++)this.invalidateElement(r[e])},tr.invalidateElement=function(r){var e=this,t=e.lookup,a=[],n=t.isInvalid(r);if(n){for(var i=Gn;i<=ao;i++){var s=t.getForCachedKey(r,i);s&&a.push(s)}var o=t.invalidate(r);if(o)for(var l=0;l<a.length;l++){var u=a[l],v=u.texture;v.invalidatedWidth+=u.width,u.invalidated=!0,e.checkTextureUtility(v)}e.removeFromQueue(r)}},tr.checkTextureUtility=function(r){r.invalidatedWidth>=ry*r.width&&this.retireTexture(r)},tr.checkTextureFullness=function(r){var e=this,t=e.getTextureQueue(r.height);r.usedWidth/r.width>ty&&r.fullnessChecks>=ay?nt(t,r):r.fullnessChecks++},tr.retireTexture=function(r){var e=this,t=r.height,a=e.getTextureQueue(t),n=this.lookup;nt(a,r),r.retired=!0;for(var i=r.eleCaches,s=0;s<i.length;s++){var o=i[s];n.deleteCache(o.key,o.level)}mi(i);var l=e.getRetiredTextureQueue(t);l.push(r)},tr.addTexture=function(r,e){var t=this,a=t.getTextureQueue(r),n={};return a.push(n),n.eleCaches=[],n.height=r,n.width=Math.max(Jp,e),n.usedWidth=0,n.invalidatedWidth=0,n.fullnessChecks=0,n.canvas=t.renderer.makeOffscreenCanvas(n.width,n.height),n.context=n.canvas.getContext("2d"),n},tr.recycleTexture=function(r,e){for(var t=this,a=t.getTextureQueue(r),n=t.getRetiredTextureQueue(r),i=0;i<n.length;i++){var s=n[i];if(s.width>=e)return s.retired=!1,s.usedWidth=0,s.invalidatedWidth=0,s.fullnessChecks=0,mi(s.eleCaches),s.context.setTransform(1,0,0,1,0,0),s.context.clearRect(0,0,s.width,s.height),nt(n,s),a.push(s),s}},tr.queueElement=function(r,e){var t=this,a=t.getElementQueue(),n=t.getElementKeyToQueue(),i=this.getKey(r),s=n[i];if(s)s.level=Math.max(s.level,e),s.eles.merge(r),s.reqs++,a.updateItem(s);else{var o={eles:r.spawn().merge(r),level:e,reqs:1,key:i};a.push(o),n[i]=o}},tr.dequeue=function(r){for(var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=[],i=e.lookup,s=0;s<ly&&t.size()>0;s++){var o=t.pop(),l=o.key,u=o.eles[0],v=i.hasCache(u,o.level);if(a[l]=null,v)continue;n.push(o);var f=e.getBoundingBox(u);e.getElement(u,f,r,o.level,ta.dequeue)}return n},tr.removeFromQueue=function(r){var e=this,t=e.getElementQueue(),a=e.getElementKeyToQueue(),n=this.getKey(r),i=a[n];i!=null&&(i.eles.length===1?(i.reqs=pi,t.updateItem(i),t.pop(),a[n]=null):i.eles.unmerge(r))},tr.onDequeue=function(r){this.onDequeues.push(r)},tr.offDequeue=function(r){nt(this.onDequeues,r)},tr.setupDequeueing=af.setupDequeueing({deqRedrawThreshold:uy,deqCost:ny,deqAvgCost:iy,deqNoDrawCost:sy,deqFastCost:oy,deq:function(e,t,a){return e.dequeue(t,a)},onDeqd:function(e,t){for(var a=0;a<e.onDequeues.length;a++){var n=e.onDequeues[a];n(t)}},shouldRedraw:function(e,t,a,n){for(var i=0;i<t.length;i++)for(var s=t[i].eles,o=0;o<s.length;o++){var l=s[o].boundingBox();if(Ti(l,n))return!0}return!1},priority:function(e){return e.renderer.beforeRenderPriorities.eleTxrDeq}});var fy=1,Na=-4,Hn=2,cy=3.99,dy=50,hy=50,gy=.15,py=.1,yy=.9,my=.9,by=1,of=250,wy=4e3*4e3,uf=32767,xy=!0,lf=function(e){var t=this,a=t.renderer=e,n=a.cy;t.layersByLevel={},t.firstGet=!0,t.lastInvalidationTime=Wr()-2*of,t.skipping=!1,t.eleTxrDeqs=n.collection(),t.scheduleElementRefinement=ca(function(){t.refineElementTextures(t.eleTxrDeqs),t.eleTxrDeqs.unmerge(t.eleTxrDeqs)},hy),a.beforeRender(function(s,o){o-t.lastInvalidationTime<=of?t.skipping=!0:t.skipping=!1},a.beforeRenderPriorities.lyrTxrSkip);var i=function(o,l){return l.reqs-o.reqs};t.layersQueue=new pa(i),t.setupDequeueing()},cr=lf.prototype,vf=0,Ey=Math.pow(2,53)-1;cr.makeLayer=function(r,e){var t=Math.pow(2,e),a=Math.ceil(r.w*t),n=Math.ceil(r.h*t),i=this.renderer.makeOffscreenCanvas(a,n),s={id:vf=++vf%Ey,bb:r,level:e,width:a,height:n,canvas:i,context:i.getContext("2d"),eles:[],elesQueue:[],reqs:0},o=s.context,l=-s.bb.x1,u=-s.bb.y1;return o.scale(t,t),o.translate(l,u),s},cr.getLayers=function(r,e,t){var a=this,n=a.renderer,i=n.cy,s=i.zoom(),o=a.firstGet;if(a.firstGet=!1,t==null){if(t=Math.ceil(Ei(s*e)),t<Na)t=Na;else if(s>=cy||t>Hn)return null}a.validateLayersElesOrdering(t,r);var l=a.layersByLevel,u=Math.pow(2,t),v=l[t]=l[t]||[],f,c=a.levelIsComplete(t,r),h,d=function(){var D=function(L){if(a.validateLayersElesOrdering(L,r),a.levelIsComplete(L,r))return h=l[L],!0},B=function(L){if(!h)for(var I=t+L;Na<=I&&I<=Hn&&!D(I);I+=L);};B(1),B(-1);for(var P=v.length-1;P>=0;P--){var A=v[P];A.invalid&&nt(v,A)}};if(!c)d();else return v;var y=function(){if(!f){f=mr();for(var D=0;D<r.length;D++)bd(f,r[D].boundingBox())}return f},g=function(D){D=D||{};var B=D.after;y();var P=Math.ceil(f.w*u),A=Math.ceil(f.h*u);if(P>uf||A>uf)return null;var R=P*A;if(R>wy)return null;var L=a.makeLayer(f,t);if(B!=null){var I=v.indexOf(B)+1;v.splice(I,0,L)}else(D.insert===void 0||D.insert)&&v.unshift(L);return L};if(a.skipping&&!o)return null;for(var p=null,m=r.length/fy,b=!o,w=0;w<r.length;w++){var E=r[w],C=E._private.rscratch,x=C.imgLayerCaches=C.imgLayerCaches||{},T=x[t];if(T){p=T;continue}if((!p||p.eles.length>=m||!fu(p.bb,E.boundingBox()))&&(p=g({insert:!0,after:p}),!p))return null;h||b?a.queueLayer(p,E):a.drawEleInLayer(p,E,t,e),p.eles.push(E),x[t]=p}return h||(b?null:v)},cr.getEleLevelForLayerLevel=function(r,e){return r},cr.drawEleInLayer=function(r,e,t,a){var n=this,i=this.renderer,s=r.context,o=e.boundingBox();o.w===0||o.h===0||!e.visible()||(t=n.getEleLevelForLayerLevel(t,a),i.setImgSmoothing(s,!1),i.drawCachedElement(s,e,null,null,t,xy),i.setImgSmoothing(s,!0))},cr.levelIsComplete=function(r,e){var t=this,a=t.layersByLevel[r];if(!a||a.length===0)return!1;for(var n=0,i=0;i<a.length;i++){var s=a[i];if(s.reqs>0||s.invalid)return!1;n+=s.eles.length}return n===e.length},cr.validateLayersElesOrdering=function(r,e){var t=this.layersByLevel[r];if(t)for(var a=0;a<t.length;a++){for(var n=t[a],i=-1,s=0;s<e.length;s++)if(n.eles[0]===e[s]){i=s;break}if(i<0){this.invalidateLayer(n);continue}for(var o=i,s=0;s<n.eles.length;s++)if(n.eles[s]!==e[o+s]){this.invalidateLayer(n);break}}},cr.updateElementsInLayers=function(r,e){for(var t=this,a=ua(r[0]),n=0;n<r.length;n++)for(var i=a?null:r[n],s=a?r[n]:r[n].ele,o=s._private.rscratch,l=o.imgLayerCaches=o.imgLayerCaches||{},u=Na;u<=Hn;u++){var v=l[u];v&&(i&&t.getEleLevelForLayerLevel(v.level)!==i.level||e(v,s,i))}},cr.haveLayers=function(){for(var r=this,e=!1,t=Na;t<=Hn;t++){var a=r.layersByLevel[t];if(a&&a.length>0){e=!0;break}}return e},cr.invalidateElements=function(r){var e=this;r.length!==0&&(e.lastInvalidationTime=Wr(),!(r.length===0||!e.haveLayers())&&e.updateElementsInLayers(r,function(a,n,i){e.invalidateLayer(a)}))},cr.invalidateLayer=function(r){if(this.lastInvalidationTime=Wr(),!r.invalid){var e=r.level,t=r.eles,a=this.layersByLevel[e];nt(a,r),r.elesQueue=[],r.invalid=!0,r.replacement&&(r.replacement.invalid=!0);for(var n=0;n<t.length;n++){var i=t[n]._private.rscratch.imgLayerCaches;i&&(i[e]=null)}}},cr.refineElementTextures=function(r){var e=this;e.updateElementsInLayers(r,function(a,n,i){var s=a.replacement;if(s||(s=a.replacement=e.makeLayer(a.bb,a.level),s.replaces=a,s.eles=a.eles),!s.reqs)for(var o=0;o<s.eles.length;o++)e.queueLayer(s,s.eles[o])})},cr.enqueueElementRefinement=function(r){this.eleTxrDeqs.merge(r),this.scheduleElementRefinement()},cr.queueLayer=function(r,e){var t=this,a=t.layersQueue,n=r.elesQueue,i=n.hasId=n.hasId||{};if(!r.replacement){if(e){if(i[e.id()])return;n.push(e),i[e.id()]=!0}r.reqs?(r.reqs++,a.updateItem(r)):(r.reqs=1,a.push(r))}},cr.dequeue=function(r){for(var e=this,t=e.layersQueue,a=[],n=0;n<by&&t.size()!==0;){var i=t.peek();if(i.replacement){t.pop();continue}if(i.replaces&&i!==i.replaces.replacement){t.pop();continue}if(i.invalid){t.pop();continue}var s=i.elesQueue.shift();s&&(e.drawEleInLayer(i,s,i.level,r),n++),a.length===0&&a.push(!0),i.elesQueue.length===0&&(t.pop(),i.reqs=0,i.replaces&&e.applyLayerReplacement(i),e.requestRedraw())}return a},cr.applyLayerReplacement=function(r){var e=this,t=e.layersByLevel[r.level],a=r.replaces,n=t.indexOf(a);if(!(n<0||a.invalid)){t[n]=r;for(var i=0;i<r.eles.length;i++){var s=r.eles[i]._private,o=s.imgLayerCaches=s.imgLayerCaches||{};o&&(o[r.level]=r)}e.requestRedraw()}},cr.requestRedraw=ca(function(){var r=this.renderer;r.redrawHint("eles",!0),r.redrawHint("drag",!0),r.redraw()},100),cr.setupDequeueing=af.setupDequeueing({deqRedrawThreshold:dy,deqCost:gy,deqAvgCost:py,deqNoDrawCost:yy,deqFastCost:my,deq:function(e,t){return e.dequeue(t)},onDeqd:yi,shouldRedraw:jo,priority:function(e){return e.renderer.beforeRenderPriorities.lyrTxrDeq}});var ff={},cf;function Cy(r,e){for(var t=0;t<e.length;t++){var a=e[t];r.lineTo(a.x,a.y)}}function Ty(r,e,t){for(var a,n=0;n<e.length;n++){var i=e[n];n===0&&(a=i),r.lineTo(i.x,i.y)}r.quadraticCurveTo(t.x,t.y,a.x,a.y)}function df(r,e,t){r.beginPath&&r.beginPath();for(var a=e,n=0;n<a.length;n++){var i=a[n];r.lineTo(i.x,i.y)}var s=t,o=t[0];r.moveTo(o.x,o.y);for(var n=1;n<s.length;n++){var i=s[n];r.lineTo(i.x,i.y)}r.closePath&&r.closePath()}function Sy(r,e,t,a,n){r.beginPath&&r.beginPath(),r.arc(t,a,n,0,Math.PI*2,!1);var i=e,s=i[0];r.moveTo(s.x,s.y);for(var o=0;o<i.length;o++){var l=i[o];r.lineTo(l.x,l.y)}r.closePath&&r.closePath()}function ky(r,e,t,a){r.arc(e,t,a,0,Math.PI*2,!1)}ff.arrowShapeImpl=function(r){return(cf||(cf={polygon:Cy,"triangle-backcurve":Ty,"triangle-tee":df,"circle-triangle":Sy,"triangle-cross":df,circle:ky}))[r]};var Gr={};Gr.drawElement=function(r,e,t,a,n,i){var s=this;e.isNode()?s.drawNode(r,e,t,a,n,i):s.drawEdge(r,e,t,a,n,i)},Gr.drawElementOverlay=function(r,e){var t=this;e.isNode()?t.drawNodeOverlay(r,e):t.drawEdgeOverlay(r,e)},Gr.drawElementUnderlay=function(r,e){var t=this;e.isNode()?t.drawNodeUnderlay(r,e):t.drawEdgeUnderlay(r,e)},Gr.drawCachedElementPortion=function(r,e,t,a,n,i,s,o){var l=this,u=t.getBoundingBox(e);if(!(u.w===0||u.h===0)){var v=t.getElement(e,u,a,n,i);if(v!=null){var f=o(l,e);if(f===0)return;var c=s(l,e),h=u.x1,d=u.y1,y=u.w,g=u.h,p,m,b,w,E;if(c!==0){var C=t.getRotationPoint(e);b=C.x,w=C.y,r.translate(b,w),r.rotate(c),E=l.getImgSmoothing(r),E||l.setImgSmoothing(r,!0);var x=t.getRotationOffset(e);p=x.x,m=x.y}else p=h,m=d;var T;f!==1&&(T=r.globalAlpha,r.globalAlpha=T*f),r.drawImage(v.texture.canvas,v.x,0,v.width,v.height,p,m,y,g),f!==1&&(r.globalAlpha=T),c!==0&&(r.rotate(-c),r.translate(-b,-w),E||l.setImgSmoothing(r,!1))}else t.drawElement(r,e)}};var Dy=function(){return 0},By=function(e,t){return e.getTextAngle(t,null)},Py=function(e,t){return e.getTextAngle(t,"source")},Ay=function(e,t){return e.getTextAngle(t,"target")},Ry=function(e,t){return t.effectiveOpacity()},no=function(e,t){return t.pstyle("text-opacity").pfValue*t.effectiveOpacity()};Gr.drawCachedElement=function(r,e,t,a,n,i){var s=this,o=s.data,l=o.eleTxrCache,u=o.lblTxrCache,v=o.slbTxrCache,f=o.tlbTxrCache,c=e.boundingBox(),h=i===!0?l.reasons.highQuality:null;if(!(c.w===0||c.h===0||!e.visible())&&(!a||Ti(c,a))){var d=e.isEdge(),y=e.element()._private.rscratch.badLine;s.drawElementUnderlay(r,e),s.drawCachedElementPortion(r,e,l,t,n,h,Dy,Ry),(!d||!y)&&s.drawCachedElementPortion(r,e,u,t,n,h,By,no),d&&!y&&(s.drawCachedElementPortion(r,e,v,t,n,h,Py,no),s.drawCachedElementPortion(r,e,f,t,n,h,Ay,no)),s.drawElementOverlay(r,e)}},Gr.drawElements=function(r,e){for(var t=this,a=0;a<e.length;a++){var n=e[a];t.drawElement(r,n)}},Gr.drawCachedElements=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];n.drawCachedElement(r,s,t,a)}},Gr.drawCachedNodes=function(r,e,t,a){for(var n=this,i=0;i<e.length;i++){var s=e[i];s.isNode()&&n.drawCachedElement(r,s,t,a)}},Gr.drawLayeredElements=function(r,e,t,a){var n=this,i=n.data.lyrTxrCache.getLayers(e,t);if(i)for(var s=0;s<i.length;s++){var o=i[s],l=o.bb;l.w===0||l.h===0||r.drawImage(o.canvas,l.x1,l.y1,l.w,l.h)}else n.drawCachedElements(r,e,t,a)};var Zr={};Zr.drawEdge=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o=e._private.rscratch;if(!(i&&!e.visible())&&!(o.badLine||o.allpts==null||isNaN(o.allpts[0]))){var l;t&&(l=t,r.translate(-l.x1,-l.y1));var u=i?e.pstyle("opacity").value:1,v=i?e.pstyle("line-opacity").value:1,f=e.pstyle("curve-style").value,c=e.pstyle("line-style").value,h=e.pstyle("width").pfValue,d=e.pstyle("line-cap").value,y=e.pstyle("line-outline-width").value,g=e.pstyle("line-outline-color").value,p=u*v,m=u*v,b=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;f==="straight-triangle"?(s.eleStrokeStyle(r,e,L),s.drawEdgeTrianglePath(e,r,o.allpts)):(r.lineWidth=h,r.lineCap=d,s.eleStrokeStyle(r,e,L),s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},w=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p;if(r.lineWidth=h+y,r.lineCap=d,y>0)s.colorStrokeStyle(r,g[0],g[1],g[2],L);else{r.lineCap="butt";return}f==="straight-triangle"?s.drawEdgeTrianglePath(e,r,o.allpts):(s.drawEdgePath(e,r,o.allpts,c),r.lineCap="butt")},E=function(){n&&s.drawEdgeOverlay(r,e)},C=function(){n&&s.drawEdgeUnderlay(r,e)},x=function(){var L=arguments.length>0&&arguments[0]!==void 0?arguments[0]:m;s.drawArrowheads(r,e,L)},T=function(){s.drawElementText(r,e,null,a)};r.lineJoin="round";var k=e.pstyle("ghost").value==="yes";if(k){var D=e.pstyle("ghost-offset-x").pfValue,B=e.pstyle("ghost-offset-y").pfValue,P=e.pstyle("ghost-opacity").value,A=p*P;r.translate(D,B),b(A),x(A),r.translate(-D,-B)}else w();C(),b(),x(),E(),T(),t&&r.translate(l.x1,l.y1)}};var hf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a){if(a.visible()){var n=a.pstyle("".concat(e,"-opacity")).value;if(n!==0){var i=this,s=i.usePaths(),o=a._private.rscratch,l=a.pstyle("".concat(e,"-padding")).pfValue,u=2*l,v=a.pstyle("".concat(e,"-color")).value;t.lineWidth=u,o.edgeType==="self"&&!s?t.lineCap="butt":t.lineCap="round",i.colorStrokeStyle(t,v[0],v[1],v[2],n),i.drawEdgePath(a,t,o.allpts,"solid")}}}};Zr.drawEdgeOverlay=hf("overlay"),Zr.drawEdgeUnderlay=hf("underlay"),Zr.drawEdgePath=function(r,e,t,a){var n=r._private.rscratch,i=e,s,o=!1,l=this.usePaths(),u=r.pstyle("line-dash-pattern").pfValue,v=r.pstyle("line-dash-offset").pfValue;if(l){var f=t.join("$"),c=n.pathCacheKey&&n.pathCacheKey===f;c?(s=e=n.pathCache,o=!0):(s=e=new Path2D,n.pathCacheKey=f,n.pathCache=s)}if(i.setLineDash)switch(a){case"dotted":i.setLineDash([1,1]);break;case"dashed":i.setLineDash(u),i.lineDashOffset=v;break;case"solid":i.setLineDash([]);break}if(!o&&!n.badLine)switch(e.beginPath&&e.beginPath(),e.moveTo(t[0],t[1]),n.edgeType){case"bezier":case"self":case"compound":case"multibezier":for(var h=2;h+3<t.length;h+=4)e.quadraticCurveTo(t[h],t[h+1],t[h+2],t[h+3]);break;case"straight":case"haystack":for(var d=2;d+1<t.length;d+=2)e.lineTo(t[d],t[d+1]);break;case"segments":if(n.isRound){var y=Er(n.roundCorners),g;try{for(y.s();!(g=y.n()).done;){var p=g.value;Uv(e,p)}}catch(b){y.e(b)}finally{y.f()}e.lineTo(t[t.length-2],t[t.length-1])}else for(var m=2;m+1<t.length;m+=2)e.lineTo(t[m],t[m+1]);break}e=i,l?e.stroke(s):e.stroke(),e.setLineDash&&e.setLineDash([])},Zr.drawEdgeTrianglePath=function(r,e,t){e.fillStyle=e.strokeStyle;for(var a=r.pstyle("width").pfValue,n=0;n+1<t.length;n+=2){var i=[t[n+2]-t[n],t[n+3]-t[n+1]],s=Math.sqrt(i[0]*i[0]+i[1]*i[1]),o=[i[1]/s,-i[0]/s],l=[o[0]*a/2,o[1]*a/2];e.beginPath(),e.moveTo(t[n]-l[0],t[n+1]-l[1]),e.lineTo(t[n]+l[0],t[n+1]+l[1]),e.lineTo(t[n+2],t[n+3]),e.closePath(),e.fill()}},Zr.drawArrowheads=function(r,e,t){var a=e._private.rscratch,n=a.edgeType==="haystack";n||this.drawArrowhead(r,e,"source",a.arrowStartX,a.arrowStartY,a.srcArrowAngle,t),this.drawArrowhead(r,e,"mid-target",a.midX,a.midY,a.midtgtArrowAngle,t),this.drawArrowhead(r,e,"mid-source",a.midX,a.midY,a.midsrcArrowAngle,t),n||this.drawArrowhead(r,e,"target",a.arrowEndX,a.arrowEndY,a.tgtArrowAngle,t)},Zr.drawArrowhead=function(r,e,t,a,n,i,s){if(!(isNaN(a)||a==null||isNaN(n)||n==null||isNaN(i)||i==null)){var o=this,l=e.pstyle(t+"-arrow-shape").value;if(l!=="none"){var u=e.pstyle(t+"-arrow-fill").value==="hollow"?"both":"filled",v=e.pstyle(t+"-arrow-fill").value,f=e.pstyle("width").pfValue,c=e.pstyle(t+"-arrow-width"),h=c.value==="match-line"?f:c.pfValue;c.units==="%"&&(h*=f);var d=e.pstyle("opacity").value;s===void 0&&(s=d);var y=r.globalCompositeOperation;(s!==1||v==="hollow")&&(r.globalCompositeOperation="destination-out",o.colorFillStyle(r,255,255,255,1),o.colorStrokeStyle(r,255,255,255,1),o.drawArrowShape(e,r,u,f,l,h,a,n,i),r.globalCompositeOperation=y);var g=e.pstyle(t+"-arrow-color").value;o.colorFillStyle(r,g[0],g[1],g[2],s),o.colorStrokeStyle(r,g[0],g[1],g[2],s),o.drawArrowShape(e,r,v,f,l,h,a,n,i)}}},Zr.drawArrowShape=function(r,e,t,a,n,i,s,o,l){var u=this,v=this.usePaths()&&n!=="triangle-cross",f=!1,c,h=e,d={x:s,y:o},y=r.pstyle("arrow-scale").value,g=this.getArrowWidth(a,y),p=u.arrowShapes[n];if(v){var m=u.arrowPathCache=u.arrowPathCache||[],b=wt(n),w=m[b];w!=null?(c=e=w,f=!0):(c=e=new Path2D,m[b]=c)}f||(e.beginPath&&e.beginPath(),v?p.draw(e,1,0,{x:0,y:0},1):p.draw(e,g,l,d,a),e.closePath&&e.closePath()),e=h,v&&(e.translate(s,o),e.rotate(l),e.scale(g,g)),(t==="filled"||t==="both")&&(v?e.fill(c):e.fill()),(t==="hollow"||t==="both")&&(e.lineWidth=i/(v?g:1),e.lineJoin="miter",v?e.stroke(c):e.stroke()),v&&(e.scale(1/g,1/g),e.rotate(-l),e.translate(-s,-o))};var io={};io.safeDrawImage=function(r,e,t,a,n,i,s,o,l,u){if(!(n<=0||i<=0||l<=0||u<=0))try{r.drawImage(e,t,a,n,i,s,o,l,u)}catch(v){Oe(v)}},io.drawInscribedImage=function(r,e,t,a,n){var i=this,s=t.position(),o=s.x,l=s.y,u=t.cy().style(),v=u.getIndexedStyle.bind(u),f=v(t,"background-fit","value",a),c=v(t,"background-repeat","value",a),h=t.width(),d=t.height(),y=t.padding()*2,g=h+(v(t,"background-width-relative-to","value",a)==="inner"?0:y),p=d+(v(t,"background-height-relative-to","value",a)==="inner"?0:y),m=t._private.rscratch,b=v(t,"background-clip","value",a),w=b==="node",E=v(t,"background-image-opacity","value",a)*n,C=v(t,"background-image-smoothing","value",a),x=t.pstyle("corner-radius").value;x!=="auto"&&(x=t.pstyle("corner-radius").pfValue);var T=e.width||e.cachedW,k=e.height||e.cachedH;(T==null||k==null)&&(document.body.appendChild(e),T=e.cachedW=e.width||e.offsetWidth,k=e.cachedH=e.height||e.offsetHeight,document.body.removeChild(e));var D=T,B=k;if(v(t,"background-width","value",a)!=="auto"&&(v(t,"background-width","units",a)==="%"?D=v(t,"background-width","pfValue",a)*g:D=v(t,"background-width","pfValue",a)),v(t,"background-height","value",a)!=="auto"&&(v(t,"background-height","units",a)==="%"?B=v(t,"background-height","pfValue",a)*p:B=v(t,"background-height","pfValue",a)),!(D===0||B===0)){if(f==="contain"){var P=Math.min(g/D,p/B);D*=P,B*=P}else if(f==="cover"){var P=Math.max(g/D,p/B);D*=P,B*=P}var A=o-g/2,R=v(t,"background-position-x","units",a),L=v(t,"background-position-x","pfValue",a);R==="%"?A+=(g-D)*L:A+=L;var I=v(t,"background-offset-x","units",a),M=v(t,"background-offset-x","pfValue",a);I==="%"?A+=(g-D)*M:A+=M;var O=l-p/2,V=v(t,"background-position-y","units",a),G=v(t,"background-position-y","pfValue",a);V==="%"?O+=(p-B)*G:O+=G;var N=v(t,"background-offset-y","units",a),F=v(t,"background-offset-y","pfValue",a);N==="%"?O+=(p-B)*F:O+=F,m.pathCache&&(A-=o,O-=l,o=0,l=0);var U=r.globalAlpha;r.globalAlpha=E;var Q=i.getImgSmoothing(r),K=!1;if(C==="no"&&Q?(i.setImgSmoothing(r,!1),K=!0):C==="yes"&&!Q&&(i.setImgSmoothing(r,!0),K=!0),c==="no-repeat")w&&(r.save(),m.pathCache?r.clip(m.pathCache):(i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,m),r.clip())),i.safeDrawImage(r,e,0,0,T,k,A,O,D,B),w&&r.restore();else{var j=r.createPattern(e,c);r.fillStyle=j,i.nodeShapes[i.getNodeShape(t)].draw(r,o,l,g,p,x,m),r.translate(A,O),r.fill(),r.translate(-A,-O)}r.globalAlpha=U,K&&i.setImgSmoothing(r,Q)}};var At={};At.eleTextBiggerThanMin=function(r,e){if(!e){var t=r.cy().zoom(),a=this.getPixelRatio(),n=Math.ceil(Ei(t*a));e=Math.pow(2,n)}var i=r.pstyle("font-size").pfValue*e,s=r.pstyle("min-zoomed-font-size").pfValue;return!(i<s)},At.drawElementText=function(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this;if(a==null){if(i&&!s.eleTextBiggerThanMin(e))return}else if(a===!1)return;if(e.isNode()){var o=e.pstyle("label");if(!o||!o.value)return;var l=s.getLabelJustification(e);r.textAlign=l,r.textBaseline="bottom"}else{var u=e.element()._private.rscratch.badLine,v=e.pstyle("label"),f=e.pstyle("source-label"),c=e.pstyle("target-label");if(u||(!v||!v.value)&&(!f||!f.value)&&(!c||!c.value))return;r.textAlign="center",r.textBaseline="bottom"}var h=!t,d;t&&(d=t,r.translate(-d.x1,-d.y1)),n==null?(s.drawText(r,e,null,h,i),e.isEdge()&&(s.drawText(r,e,"source",h,i),s.drawText(r,e,"target",h,i))):s.drawText(r,e,n,h,i),t&&r.translate(d.x1,d.y1)},At.getFontCache=function(r){var e;this.fontCaches=this.fontCaches||[];for(var t=0;t<this.fontCaches.length;t++)if(e=this.fontCaches[t],e.context===r)return e;return e={context:r},this.fontCaches.push(e),e},At.setupTextStyle=function(r,e){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,a=e.pstyle("font-style").strValue,n=e.pstyle("font-size").pfValue+"px",i=e.pstyle("font-family").strValue,s=e.pstyle("font-weight").strValue,o=t?e.effectiveOpacity()*e.pstyle("text-opacity").value:1,l=e.pstyle("text-outline-opacity").value*o,u=e.pstyle("color").value,v=e.pstyle("text-outline-color").value;r.font=a+" "+s+" "+n+" "+i,r.lineJoin="round",this.colorFillStyle(r,u[0],u[1],u[2],o),this.colorStrokeStyle(r,v[0],v[1],v[2],l)};function My(r,e,t,a,n){var i=Math.min(a,n),s=i/2,o=e+a/2,l=t+n/2;r.beginPath(),r.arc(o,l,s,0,Math.PI*2),r.closePath()}function gf(r,e,t,a,n){var i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:5,s=Math.min(i,a/2,n/2);r.beginPath(),r.moveTo(e+s,t),r.lineTo(e+a-s,t),r.quadraticCurveTo(e+a,t,e+a,t+s),r.lineTo(e+a,t+n-s),r.quadraticCurveTo(e+a,t+n,e+a-s,t+n),r.lineTo(e+s,t+n),r.quadraticCurveTo(e,t+n,e,t+n-s),r.lineTo(e,t+s),r.quadraticCurveTo(e,t,e+s,t),r.closePath()}At.getTextAngle=function(r,e){var t,a=r._private,n=a.rscratch,i=e?e+"-":"",s=r.pstyle(i+"text-rotation");if(s.strValue==="autorotate"){var o=Cr(n,"labelAngle",e);t=r.isEdge()?o:0}else s.strValue==="none"?t=0:t=s.pfValue;return t},At.drawText=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=e._private,s=i.rscratch,o=n?e.effectiveOpacity():1;if(!(n&&(o===0||e.pstyle("text-opacity").value===0))){t==="main"&&(t=null);var l=Cr(s,"labelX",t),u=Cr(s,"labelY",t),v,f,c=this.getLabelText(e,t);if(c!=null&&c!==""&&!isNaN(l)&&!isNaN(u)){this.setupTextStyle(r,e,n);var h=t?t+"-":"",d=Cr(s,"labelWidth",t),y=Cr(s,"labelHeight",t),g=e.pstyle(h+"text-margin-x").pfValue,p=e.pstyle(h+"text-margin-y").pfValue,m=e.isEdge(),b=e.pstyle("text-halign").value,w=e.pstyle("text-valign").value;m&&(b="center",w="center"),l+=g,u+=p;var E;switch(a?E=this.getTextAngle(e,t):E=0,E!==0&&(v=l,f=u,r.translate(v,f),r.rotate(E),l=0,u=0),w){case"top":break;case"center":u+=y/2;break;case"bottom":u+=y;break}var C=e.pstyle("text-background-opacity").value,x=e.pstyle("text-border-opacity").value,T=e.pstyle("text-border-width").pfValue,k=e.pstyle("text-background-padding").pfValue,D=e.pstyle("text-background-shape").strValue,B=D==="round-rectangle"||D==="roundrectangle",P=D==="circle",A=2;if(C>0||T>0&&x>0){var R=r.fillStyle,L=r.strokeStyle,I=r.lineWidth,M=e.pstyle("text-background-color").value,O=e.pstyle("text-border-color").value,V=e.pstyle("text-border-style").value,G=C>0,N=T>0&&x>0,F=l-k;switch(b){case"left":F-=d;break;case"center":F-=d/2;break}var U=u-y-k,Q=d+2*k,K=y+2*k;if(G&&(r.fillStyle="rgba(".concat(M[0],",").concat(M[1],",").concat(M[2],",").concat(C*o,")")),N&&(r.strokeStyle="rgba(".concat(O[0],",").concat(O[1],",").concat(O[2],",").concat(x*o,")"),r.lineWidth=T,r.setLineDash))switch(V){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"double":r.lineWidth=T/4,r.setLineDash([]);break;case"solid":default:r.setLineDash([]);break}if(B?(r.beginPath(),gf(r,F,U,Q,K,A)):P?(r.beginPath(),My(r,F,U,Q,K)):(r.beginPath(),r.rect(F,U,Q,K)),G&&r.fill(),N&&r.stroke(),N&&V==="double"){var j=T/2;r.beginPath(),B?gf(r,F+j,U+j,Q-2*j,K-2*j,A):r.rect(F+j,U+j,Q-2*j,K-2*j),r.stroke()}r.fillStyle=R,r.strokeStyle=L,r.lineWidth=I,r.setLineDash&&r.setLineDash([])}var re=2*e.pstyle("text-outline-width").pfValue;if(re>0&&(r.lineWidth=re),e.pstyle("text-wrap").value==="wrap"){var ne=Cr(s,"labelWrapCachedLines",t),J=Cr(s,"labelLineHeight",t),z=d/2,q=this.getLabelJustification(e);switch(q==="auto"||(b==="left"?q==="left"?l+=-d:q==="center"&&(l+=-z):b==="center"?q==="left"?l+=-z:q==="right"&&(l+=z):b==="right"&&(q==="center"?l+=z:q==="right"&&(l+=d))),w){case"top":u-=(ne.length-1)*J;break;case"center":case"bottom":u-=(ne.length-1)*J;break}for(var H=0;H<ne.length;H++)re>0&&r.strokeText(ne[H],l,u),r.fillText(ne[H],l,u),u+=J}else re>0&&r.strokeText(c,l,u),r.fillText(c,l,u);E!==0&&(r.rotate(-E),r.translate(-v,-f))}}};var pt={};pt.drawNode=function(r,e,t){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!0,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!0,s=this,o,l,u=e._private,v=u.rscratch,f=e.position();if(!(!te(f.x)||!te(f.y))&&!(i&&!e.visible())){var c=i?e.effectiveOpacity():1,h=s.usePaths(),d,y=!1,g=e.padding();o=e.width()+2*g,l=e.height()+2*g;var p;t&&(p=t,r.translate(-p.x1,-p.y1));for(var m=e.pstyle("background-image"),b=m.value,w=new Array(b.length),E=new Array(b.length),C=0,x=0;x<b.length;x++){var T=b[x],k=w[x]=T!=null&&T!=="none";if(k){var D=e.cy().style().getIndexedStyle(e,"background-image-crossorigin","value",x);C++,E[x]=s.getCachedImage(T,D,function(){u.backgroundTimestamp=Date.now(),e.emitAndNotify("background")})}}var B=e.pstyle("background-blacken").value,P=e.pstyle("border-width").pfValue,A=e.pstyle("background-opacity").value*c,R=e.pstyle("border-color").value,L=e.pstyle("border-style").value,I=e.pstyle("border-join").value,M=e.pstyle("border-cap").value,O=e.pstyle("border-position").value,V=e.pstyle("border-dash-pattern").pfValue,G=e.pstyle("border-dash-offset").pfValue,N=e.pstyle("border-opacity").value*c,F=e.pstyle("outline-width").pfValue,U=e.pstyle("outline-color").value,Q=e.pstyle("outline-style").value,K=e.pstyle("outline-opacity").value*c,j=e.pstyle("outline-offset").value,re=e.pstyle("corner-radius").value;re!=="auto"&&(re=e.pstyle("corner-radius").pfValue);var ne=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:A;s.eleFillStyle(r,e,ue)},J=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:N;s.colorStrokeStyle(r,R[0],R[1],R[2],ue)},z=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:K;s.colorStrokeStyle(r,U[0],U[1],U[2],ue)},q=function(ue,X,S,_){var W=s.nodePathCache=s.nodePathCache||[],$=Qo(S==="polygon"?S+","+_.join(","):S,""+X,""+ue,""+re),Z=W[$],oe,ee=!1;return Z!=null?(oe=Z,ee=!0,v.pathCache=oe):(oe=new Path2D,W[$]=v.pathCache=oe),{path:oe,cacheHit:ee}},H=e.pstyle("shape").strValue,Y=e.pstyle("shape-polygon-points").pfValue;if(h){r.translate(f.x,f.y);var ae=q(o,l,H,Y);d=ae.path,y=ae.cacheHit}var ce=function(){if(!y){var ue=f;h&&(ue={x:0,y:0}),s.nodeShapes[s.getNodeShape(e)].draw(d||r,ue.x,ue.y,o,l,re,v)}h?r.fill(d):r.fill()},Ae=function(){for(var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,S=u.backgrounding,_=0,W=0;W<E.length;W++){var $=e.cy().style().getIndexedStyle(e,"background-image-containment","value",W);if(X&&$==="over"||!X&&$==="inside"){_++;continue}w[W]&&E[W].complete&&!E[W].error&&(_++,s.drawInscribedImage(r,E[W],e,W,ue))}u.backgrounding=_!==C,S!==u.backgrounding&&e.updateStyle(!1)},Ce=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasPie(e)&&(s.drawPie(r,e,X),ue&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},we=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1,X=arguments.length>1&&arguments[1]!==void 0?arguments[1]:c;s.hasStripe(e)&&(r.save(),h?r.clip(v.pathCache):(s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v),r.clip()),s.drawStripe(r,e,X),r.restore(),ue&&(h||s.nodeShapes[s.getNodeShape(e)].draw(r,f.x,f.y,o,l,re,v)))},ye=function(){var ue=arguments.length>0&&arguments[0]!==void 0?arguments[0]:c,X=(B>0?B:-B)*ue,S=B>0?0:255;B!==0&&(s.colorFillStyle(r,S,S,S,X),h?r.fill(d):r.fill())},ie=function(){if(P>0){if(r.lineWidth=P,r.lineCap=M,r.lineJoin=I,r.setLineDash)switch(L){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash(V),r.lineDashOffset=G;break;case"solid":case"double":r.setLineDash([]);break}if(O!=="center"){if(r.save(),r.lineWidth*=2,O==="inside")h?r.clip(d):r.clip();else{var ue=new Path2D;ue.rect(-o/2-P,-l/2-P,o+2*P,l+2*P),ue.addPath(d),r.clip(ue,"evenodd")}h?r.stroke(d):r.stroke(),r.restore()}else h?r.stroke(d):r.stroke();if(L==="double"){r.lineWidth=P/3;var X=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(d):r.stroke(),r.globalCompositeOperation=X}r.setLineDash&&r.setLineDash([])}},de=function(){if(F>0){if(r.lineWidth=F,r.lineCap="butt",r.setLineDash)switch(Q){case"dotted":r.setLineDash([1,1]);break;case"dashed":r.setLineDash([4,2]);break;case"solid":case"double":r.setLineDash([]);break}var ue=f;h&&(ue={x:0,y:0});var X=s.getNodeShape(e),S=P;O==="inside"&&(S=0),O==="outside"&&(S*=2);var _=(o+S+(F+j))/o,W=(l+S+(F+j))/l,$=o*_,Z=l*W,oe=s.nodeShapes[X].points,ee;if(h){var ve=q($,Z,X,oe);ee=ve.path}if(X==="ellipse")s.drawEllipsePath(ee||r,ue.x,ue.y,$,Z);else if(["round-diamond","round-heptagon","round-hexagon","round-octagon","round-pentagon","round-polygon","round-triangle","round-tag"].includes(X)){var le=0,be=0,De=0;X==="round-diamond"?le=(S+j+F)*1.4:X==="round-heptagon"?(le=(S+j+F)*1.075,De=-(S/2+j+F)/35):X==="round-hexagon"?le=(S+j+F)*1.12:X==="round-pentagon"?(le=(S+j+F)*1.13,De=-(S/2+j+F)/15):X==="round-tag"?(le=(S+j+F)*1.12,be=(S/2+F+j)*.07):X==="round-triangle"&&(le=(S+j+F)*(Math.PI/2),De=-(S+j/2+F)/Math.PI),le!==0&&(_=(o+le)/o,$=o*_,["round-hexagon","round-tag"].includes(X)||(W=(l+le)/l,Z=l*W)),re=re==="auto"?hu($,Z):re;for(var Te=$/2,fe=Z/2,Pe=re+(S+F+j)/2,Be=new Array(oe.length/2),ar=new Array(oe.length/2),Xe=0;Xe<oe.length/2;Xe++)Be[Xe]={x:ue.x+be+Te*oe[Xe*2],y:ue.y+De+fe*oe[Xe*2+1]};var xr,Qe,ur,nr,dr=Be.length;for(Qe=Be[dr-1],xr=0;xr<dr;xr++)ur=Be[xr%dr],nr=Be[(xr+1)%dr],ar[xr]=eo(Qe,ur,nr,Pe),Qe=ur,ur=nr;s.drawRoundPolygonPath(ee||r,ue.x+be,ue.y+De,o*_,l*W,oe,ar)}else if(["roundrectangle","round-rectangle"].includes(X))re=re==="auto"?ot($,Z):re,s.drawRoundRectanglePath(ee||r,ue.x,ue.y,$,Z,re+(S+F+j)/2);else if(["cutrectangle","cut-rectangle"].includes(X))re=re==="auto"?Di():re,s.drawCutRectanglePath(ee||r,ue.x,ue.y,$,Z,null,re+(S+F+j)/4);else if(["bottomroundrectangle","bottom-round-rectangle"].includes(X))re=re==="auto"?ot($,Z):re,s.drawBottomRoundRectanglePath(ee||r,ue.x,ue.y,$,Z,re+(S+F+j)/2);else if(X==="barrel")s.drawBarrelPath(ee||r,ue.x,ue.y,$,Z);else if(X.startsWith("polygon")||["rhomboid","right-rhomboid","round-tag","tag","vee"].includes(X)){var mt=(S+F+j)/o;oe=sn(on(oe,mt)),s.drawPolygonPath(ee||r,ue.x,ue.y,o,l,oe)}else{var Je=(S+F+j)/o;oe=sn(on(oe,-Je)),s.drawPolygonPath(ee||r,ue.x,ue.y,o,l,oe)}if(h?r.stroke(ee):r.stroke(),Q==="double"){r.lineWidth=S/3;var Qr=r.globalCompositeOperation;r.globalCompositeOperation="destination-out",h?r.stroke(ee):r.stroke(),r.globalCompositeOperation=Qr}r.setLineDash&&r.setLineDash([])}},ge=function(){n&&s.drawNodeOverlay(r,e,f,o,l)},Ee=function(){n&&s.drawNodeUnderlay(r,e,f,o,l)},pe=function(){s.drawElementText(r,e,null,a)},ke=e.pstyle("ghost").value==="yes";if(ke){var Re=e.pstyle("ghost-offset-x").pfValue,ze=e.pstyle("ghost-offset-y").pfValue,Fe=e.pstyle("ghost-opacity").value,Ve=Fe*c;r.translate(Re,ze),z(),de(),ne(Fe*A),ce(),Ae(Ve,!0),J(Fe*N),ie(),Ce(B!==0||P!==0),we(B!==0||P!==0),Ae(Ve,!1),ye(Ve),r.translate(-Re,-ze)}h&&r.translate(-f.x,-f.y),Ee(),h&&r.translate(f.x,f.y),z(),de(),ne(),ce(),Ae(c,!0),J(),ie(),Ce(B!==0||P!==0),we(B!==0||P!==0),Ae(c,!1),ye(),h&&r.translate(-f.x,-f.y),pe(),ge(),t&&r.translate(p.x1,p.y1)}};var pf=function(e){if(!["overlay","underlay"].includes(e))throw new Error("Invalid state");return function(t,a,n,i,s){var o=this;if(a.visible()){var l=a.pstyle("".concat(e,"-padding")).pfValue,u=a.pstyle("".concat(e,"-opacity")).value,v=a.pstyle("".concat(e,"-color")).value,f=a.pstyle("".concat(e,"-shape")).value,c=a.pstyle("".concat(e,"-corner-radius")).value;if(u>0){if(n=n||a.position(),i==null||s==null){var h=a.padding();i=a.width()+2*h,s=a.height()+2*h}o.colorFillStyle(t,v[0],v[1],v[2],u),o.nodeShapes[f].draw(t,n.x,n.y,i+l*2,s+l*2,c),t.fill()}}}};pt.drawNodeOverlay=pf("overlay"),pt.drawNodeUnderlay=pf("underlay"),pt.hasPie=function(r){return r=r[0],r._private.hasPie},pt.hasStripe=function(r){return r=r[0],r._private.hasStripe},pt.drawPie=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=e.pstyle("pie-size"),s=e.pstyle("pie-hole"),o=e.pstyle("pie-start-angle").pfValue,l=a.x,u=a.y,v=e.width(),f=e.height(),c=Math.min(v,f)/2,h,d=0,y=this.usePaths();if(y&&(l=0,u=0),i.units==="%"?c=c*i.pfValue:i.pfValue!==void 0&&(c=i.pfValue/2),s.units==="%"?h=c*s.pfValue:s.pfValue!==void 0&&(h=s.pfValue/2),!(h>=c))for(var g=1;g<=n.pieBackgroundN;g++){var p=e.pstyle("pie-"+g+"-background-size").value,m=e.pstyle("pie-"+g+"-background-color").value,b=e.pstyle("pie-"+g+"-background-opacity").value*t,w=p/100;w+d>1&&(w=1-d);var E=1.5*Math.PI+2*Math.PI*d;E+=o;var C=2*Math.PI*w,x=E+C;p===0||d>=1||d+w>1||(h===0?(r.beginPath(),r.moveTo(l,u),r.arc(l,u,c,E,x),r.closePath()):(r.beginPath(),r.arc(l,u,c,E,x),r.arc(l,u,h,x,E,!0),r.closePath()),this.colorFillStyle(r,m[0],m[1],m[2],b),r.fill(),d+=w)}},pt.drawStripe=function(r,e,t,a){e=e[0],a=a||e.position();var n=e.cy().style(),i=a.x,s=a.y,o=e.width(),l=e.height(),u=0,v=this.usePaths();r.save();var f=e.pstyle("stripe-direction").value,c=e.pstyle("stripe-size");switch(f){case"vertical":break;case"righward":r.rotate(-Math.PI/2);break}var h=o,d=l;c.units==="%"?(h=h*c.pfValue,d=d*c.pfValue):c.pfValue!==void 0&&(h=c.pfValue,d=c.pfValue),v&&(i=0,s=0),s-=h/2,i-=d/2;for(var y=1;y<=n.stripeBackgroundN;y++){var g=e.pstyle("stripe-"+y+"-background-size").value,p=e.pstyle("stripe-"+y+"-background-color").value,m=e.pstyle("stripe-"+y+"-background-opacity").value*t,b=g/100;b+u>1&&(b=1-u),!(g===0||u>=1||u+b>1)&&(r.beginPath(),r.rect(i,s+d*u,h,d*b),r.closePath(),this.colorFillStyle(r,p[0],p[1],p[2],m),r.fill(),u+=b)}r.restore()};var wr={},Ly=100;wr.getPixelRatio=function(){var r=this.data.contexts[0];if(this.forcedPixelRatio!=null)return this.forcedPixelRatio;var e=this.cy.window(),t=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||r.backingStorePixelRatio||1;return(e.devicePixelRatio||1)/t},wr.paintCache=function(r){for(var e=this.paintCaches=this.paintCaches||[],t=!0,a,n=0;n<e.length;n++)if(a=e[n],a.context===r){t=!1;break}return t&&(a={context:r},e.push(a)),a},wr.createGradientStyleFor=function(r,e,t,a,n){var i,s=this.usePaths(),o=t.pstyle(e+"-gradient-stop-colors").value,l=t.pstyle(e+"-gradient-stop-positions").pfValue;if(a==="radial-gradient")if(t.isEdge()){var u=t.sourceEndpoint(),v=t.targetEndpoint(),f=t.midpoint(),c=xt(u,f),h=xt(v,f);i=r.createRadialGradient(f.x,f.y,0,f.x,f.y,Math.max(c,h))}else{var d=s?{x:0,y:0}:t.position(),y=t.paddedWidth(),g=t.paddedHeight();i=r.createRadialGradient(d.x,d.y,0,d.x,d.y,Math.max(y,g))}else if(t.isEdge()){var p=t.sourceEndpoint(),m=t.targetEndpoint();i=r.createLinearGradient(p.x,p.y,m.x,m.y)}else{var b=s?{x:0,y:0}:t.position(),w=t.paddedWidth(),E=t.paddedHeight(),C=w/2,x=E/2,T=t.pstyle("background-gradient-direction").value;switch(T){case"to-bottom":i=r.createLinearGradient(b.x,b.y-x,b.x,b.y+x);break;case"to-top":i=r.createLinearGradient(b.x,b.y+x,b.x,b.y-x);break;case"to-left":i=r.createLinearGradient(b.x+C,b.y,b.x-C,b.y);break;case"to-right":i=r.createLinearGradient(b.x-C,b.y,b.x+C,b.y);break;case"to-bottom-right":case"to-right-bottom":i=r.createLinearGradient(b.x-C,b.y-x,b.x+C,b.y+x);break;case"to-top-right":case"to-right-top":i=r.createLinearGradient(b.x-C,b.y+x,b.x+C,b.y-x);break;case"to-bottom-left":case"to-left-bottom":i=r.createLinearGradient(b.x+C,b.y-x,b.x-C,b.y+x);break;case"to-top-left":case"to-left-top":i=r.createLinearGradient(b.x+C,b.y+x,b.x-C,b.y-x);break}}if(!i)return null;for(var k=l.length===o.length,D=o.length,B=0;B<D;B++)i.addColorStop(k?l[B]:B/(D-1),"rgba("+o[B][0]+","+o[B][1]+","+o[B][2]+","+n+")");return i},wr.gradientFillStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"background",e,t,a);if(!n)return null;r.fillStyle=n},wr.colorFillStyle=function(r,e,t,a,n){r.fillStyle="rgba("+e+","+t+","+a+","+n+")"},wr.eleFillStyle=function(r,e,t){var a=e.pstyle("background-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientFillStyle(r,e,a,t);else{var n=e.pstyle("background-color").value;this.colorFillStyle(r,n[0],n[1],n[2],t)}},wr.gradientStrokeStyle=function(r,e,t,a){var n=this.createGradientStyleFor(r,"line",e,t,a);if(!n)return null;r.strokeStyle=n},wr.colorStrokeStyle=function(r,e,t,a,n){r.strokeStyle="rgba("+e+","+t+","+a+","+n+")"},wr.eleStrokeStyle=function(r,e,t){var a=e.pstyle("line-fill").value;if(a==="linear-gradient"||a==="radial-gradient")this.gradientStrokeStyle(r,e,a,t);else{var n=e.pstyle("line-color").value;this.colorStrokeStyle(r,n[0],n[1],n[2],t)}},wr.matchCanvasSize=function(r){var e=this,t=e.data,a=e.findContainerClientCoords(),n=a[2],i=a[3],s=e.getPixelRatio(),o=e.motionBlurPxRatio;(r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE]||r===e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG])&&(s=o);var l=n*s,u=i*s,v;if(!(l===e.canvasWidth&&u===e.canvasHeight)){e.fontCaches=null;var f=t.canvasContainer;f.style.width=n+"px",f.style.height=i+"px";for(var c=0;c<e.CANVAS_LAYERS;c++)v=t.canvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";for(var c=0;c<e.BUFFER_COUNT;c++)v=t.bufferCanvases[c],v.width=l,v.height=u,v.style.width=n+"px",v.style.height=i+"px";e.textureMult=1,s<=1&&(v=t.bufferCanvases[e.TEXTURE_BUFFER],e.textureMult=2,v.width=l*e.textureMult,v.height=u*e.textureMult),e.canvasWidth=l,e.canvasHeight=u,e.pixelRatio=s}},wr.renderTo=function(r,e,t,a){this.render({forcedContext:r,forcedZoom:e,forcedPan:t,drawAllLayers:!0,forcedPxRatio:a})},wr.clearCanvas=function(){var r=this,e=r.data;function t(a){a.clearRect(0,0,r.canvasWidth,r.canvasHeight)}t(e.contexts[r.NODE]),t(e.contexts[r.DRAG])},wr.render=function(r){var e=this;r=r||au();var t=e.cy,a=r.forcedContext,n=r.drawAllLayers,i=r.drawOnlyNodeLayer,s=r.forcedZoom,o=r.forcedPan,l=r.forcedPxRatio===void 0?this.getPixelRatio():r.forcedPxRatio,u=e.data,v=u.canvasNeedsRedraw,f=e.textureOnViewport&&!a&&(e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming),c=r.motionBlur!==void 0?r.motionBlur:e.motionBlur,h=e.motionBlurPxRatio,d=t.hasCompoundNodes(),y=e.hoverData.draggingEles,g=!!(e.hoverData.selecting||e.touchData.selecting);c=c&&!a&&e.motionBlurEnabled&&!g;var p=c;a||(e.prevPxRatio!==l&&(e.invalidateContainerClientCoordsCache(),e.matchCanvasSize(e.container),e.redrawHint("eles",!0),e.redrawHint("drag",!0)),e.prevPxRatio=l),!a&&e.motionBlurTimeout&&clearTimeout(e.motionBlurTimeout),c&&(e.mbFrames==null&&(e.mbFrames=0),e.mbFrames++,e.mbFrames<3&&(p=!1),e.mbFrames>e.minMbLowQualFrames&&(e.motionBlurPxRatio=e.mbPxRBlurry)),e.clearingMotionBlur&&(e.motionBlurPxRatio=1),e.textureDrawLastFrame&&!f&&(v[e.NODE]=!0,v[e.SELECT_BOX]=!0);var m=t.style(),b=t.zoom(),w=s!==void 0?s:b,E=t.pan(),C={x:E.x,y:E.y},x={zoom:b,pan:{x:E.x,y:E.y}},T=e.prevViewport,k=T===void 0||x.zoom!==T.zoom||x.pan.x!==T.pan.x||x.pan.y!==T.pan.y;!k&&!(y&&!d)&&(e.motionBlurPxRatio=1),o&&(C=o),w*=l,C.x*=l,C.y*=l;var D=e.getCachedZSortedEles();function B(J,z,q,H,Y){var ae=J.globalCompositeOperation;J.globalCompositeOperation="destination-out",e.colorFillStyle(J,255,255,255,e.motionBlurTransparency),J.fillRect(z,q,H,Y),J.globalCompositeOperation=ae}function P(J,z){var q,H,Y,ae;!e.clearingMotionBlur&&(J===u.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]||J===u.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG])?(q={x:E.x*h,y:E.y*h},H=b*h,Y=e.canvasWidth*h,ae=e.canvasHeight*h):(q=C,H=w,Y=e.canvasWidth,ae=e.canvasHeight),J.setTransform(1,0,0,1,0,0),z==="motionBlur"?B(J,0,0,Y,ae):!a&&(z===void 0||z)&&J.clearRect(0,0,Y,ae),n||(J.translate(q.x,q.y),J.scale(H,H)),o&&J.translate(o.x,o.y),s&&J.scale(s,s)}if(f||(e.textureDrawLastFrame=!1),f){if(e.textureDrawLastFrame=!0,!e.textureCache){e.textureCache={},e.textureCache.bb=t.mutableElements().boundingBox(),e.textureCache.texture=e.data.bufferCanvases[e.TEXTURE_BUFFER];var A=e.data.bufferContexts[e.TEXTURE_BUFFER];A.setTransform(1,0,0,1,0,0),A.clearRect(0,0,e.canvasWidth*e.textureMult,e.canvasHeight*e.textureMult),e.render({forcedContext:A,drawOnlyNodeLayer:!0,forcedPxRatio:l*e.textureMult});var x=e.textureCache.viewport={zoom:t.zoom(),pan:t.pan(),width:e.canvasWidth,height:e.canvasHeight};x.mpan={x:(0-x.pan.x)/x.zoom,y:(0-x.pan.y)/x.zoom}}v[e.DRAG]=!1,v[e.NODE]=!1;var R=u.contexts[e.NODE],L=e.textureCache.texture,x=e.textureCache.viewport;R.setTransform(1,0,0,1,0,0),c?B(R,0,0,x.width,x.height):R.clearRect(0,0,x.width,x.height);var I=m.core("outside-texture-bg-color").value,M=m.core("outside-texture-bg-opacity").value;e.colorFillStyle(R,I[0],I[1],I[2],M),R.fillRect(0,0,x.width,x.height);var b=t.zoom();P(R,!1),R.clearRect(x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l),R.drawImage(L,x.mpan.x,x.mpan.y,x.width/x.zoom/l,x.height/x.zoom/l)}else e.textureOnViewport&&!a&&(e.textureCache=null);var O=t.extent(),V=e.pinching||e.hoverData.dragging||e.swipePanning||e.data.wheelZooming||e.hoverData.draggingEles||e.cy.animated(),G=e.hideEdgesOnViewport&&V,N=[];if(N[e.NODE]=!v[e.NODE]&&c&&!e.clearedForMotionBlur[e.NODE]||e.clearingMotionBlur,N[e.NODE]&&(e.clearedForMotionBlur[e.NODE]=!0),N[e.DRAG]=!v[e.DRAG]&&c&&!e.clearedForMotionBlur[e.DRAG]||e.clearingMotionBlur,N[e.DRAG]&&(e.clearedForMotionBlur[e.DRAG]=!0),v[e.NODE]||n||i||N[e.NODE]){var F=c&&!N[e.NODE]&&h!==1,R=a||(F?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_NODE]:u.contexts[e.NODE]),U=c&&!F?"motionBlur":void 0;P(R,U),G?e.drawCachedNodes(R,D.nondrag,l,O):e.drawLayeredElements(R,D.nondrag,l,O),e.debug&&e.drawDebugPoints(R,D.nondrag),!n&&!c&&(v[e.NODE]=!1)}if(!i&&(v[e.DRAG]||n||N[e.DRAG])){var F=c&&!N[e.DRAG]&&h!==1,R=a||(F?e.data.bufferContexts[e.MOTIONBLUR_BUFFER_DRAG]:u.contexts[e.DRAG]);P(R,c&&!F?"motionBlur":void 0),G?e.drawCachedNodes(R,D.drag,l,O):e.drawCachedElements(R,D.drag,l,O),e.debug&&e.drawDebugPoints(R,D.drag),!n&&!c&&(v[e.DRAG]=!1)}if(this.drawSelectionRectangle(r,P),c&&h!==1){var Q=u.contexts[e.NODE],K=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_NODE],j=u.contexts[e.DRAG],re=e.data.bufferCanvases[e.MOTIONBLUR_BUFFER_DRAG],ne=function(z,q,H){z.setTransform(1,0,0,1,0,0),H||!p?z.clearRect(0,0,e.canvasWidth,e.canvasHeight):B(z,0,0,e.canvasWidth,e.canvasHeight);var Y=h;z.drawImage(q,0,0,e.canvasWidth*Y,e.canvasHeight*Y,0,0,e.canvasWidth,e.canvasHeight)};(v[e.NODE]||N[e.NODE])&&(ne(Q,K,N[e.NODE]),v[e.NODE]=!1),(v[e.DRAG]||N[e.DRAG])&&(ne(j,re,N[e.DRAG]),v[e.DRAG]=!1)}e.prevViewport=x,e.clearingMotionBlur&&(e.clearingMotionBlur=!1,e.motionBlurCleared=!0,e.motionBlur=!0),c&&(e.motionBlurTimeout=setTimeout(function(){e.motionBlurTimeout=null,e.clearedForMotionBlur[e.NODE]=!1,e.clearedForMotionBlur[e.DRAG]=!1,e.motionBlur=!1,e.clearingMotionBlur=!f,e.mbFrames=0,v[e.NODE]=!0,v[e.DRAG]=!0,e.redraw()},Ly)),a||t.emit("render")};var za;wr.drawSelectionRectangle=function(r,e){var t=this,a=t.cy,n=t.data,i=a.style(),s=r.drawOnlyNodeLayer,o=r.drawAllLayers,l=n.canvasNeedsRedraw,u=r.forcedContext;if(t.showFps||!s&&l[t.SELECT_BOX]&&!o){var v=u||n.contexts[t.SELECT_BOX];if(e(v),t.selection[4]==1&&(t.hoverData.selecting||t.touchData.selecting)){var f=t.cy.zoom(),c=i.core("selection-box-border-width").value/f;v.lineWidth=c,v.fillStyle="rgba("+i.core("selection-box-color").value[0]+","+i.core("selection-box-color").value[1]+","+i.core("selection-box-color").value[2]+","+i.core("selection-box-opacity").value+")",v.fillRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]),c>0&&(v.strokeStyle="rgba("+i.core("selection-box-border-color").value[0]+","+i.core("selection-box-border-color").value[1]+","+i.core("selection-box-border-color").value[2]+","+i.core("selection-box-opacity").value+")",v.strokeRect(t.selection[0],t.selection[1],t.selection[2]-t.selection[0],t.selection[3]-t.selection[1]))}if(n.bgActivePosistion&&!t.hoverData.selecting){var f=t.cy.zoom(),h=n.bgActivePosistion;v.fillStyle="rgba("+i.core("active-bg-color").value[0]+","+i.core("active-bg-color").value[1]+","+i.core("active-bg-color").value[2]+","+i.core("active-bg-opacity").value+")",v.beginPath(),v.arc(h.x,h.y,i.core("active-bg-size").pfValue/f,0,2*Math.PI),v.fill()}var d=t.lastRedrawTime;if(t.showFps&&d){d=Math.round(d);var y=Math.round(1e3/d),g="1 frame = "+d+" ms = "+y+" fps";if(v.setTransform(1,0,0,1,0,0),v.fillStyle="rgba(255, 0, 0, 0.75)",v.strokeStyle="rgba(255, 0, 0, 0.75)",v.font="30px Arial",!za){var p=v.measureText(g);za=p.actualBoundingBoxAscent}v.fillText(g,0,za);var m=60;v.strokeRect(0,za+10,250,20),v.fillRect(0,za+10,250*Math.min(y/m,1),20)}o||(l[t.SELECT_BOX]=!1)}};function yf(r,e,t){var a=r.createShader(e);if(r.shaderSource(a,t),r.compileShader(a),!r.getShaderParameter(a,r.COMPILE_STATUS))throw new Error(r.getShaderInfoLog(a));return a}function Iy(r,e,t){var a=yf(r,r.VERTEX_SHADER,e),n=yf(r,r.FRAGMENT_SHADER,t),i=r.createProgram();if(r.attachShader(i,a),r.attachShader(i,n),r.linkProgram(i),!r.getProgramParameter(i,r.LINK_STATUS))throw new Error("Could not initialize shaders");return i}function Oy(r,e,t){t===void 0&&(t=e);var a=r.makeOffscreenCanvas(e,t),n=a.context=a.getContext("2d");return a.clear=function(){return n.clearRect(0,0,a.width,a.height)},a.clear(),a}function so(r){var e=r.pixelRatio,t=r.cy.zoom(),a=r.cy.pan();return{zoom:t*e,pan:{x:a.x*e,y:a.y*e}}}function Ny(r){var e=r.pixelRatio,t=r.cy.zoom();return t*e}function zy(r,e,t,a,n){var i=a*t+e.x,s=n*t+e.y;return s=Math.round(r.canvasHeight-s),[i,s]}function Fy(r){return r.pstyle("background-fill").value!=="solid"||r.pstyle("background-image").strValue!=="none"?!1:r.pstyle("border-width").value===0||r.pstyle("border-opacity").value===0?!0:r.pstyle("border-style").value==="solid"}function Vy(r,e){if(r.length!==e.length)return!1;for(var t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}function Rt(r,e,t){var a=r[0]/255,n=r[1]/255,i=r[2]/255,s=e,o=t||new Array(4);return o[0]=a*s,o[1]=n*s,o[2]=i*s,o[3]=s,o}function aa(r,e){var t=e||new Array(4);return t[0]=(r>>0&255)/255,t[1]=(r>>8&255)/255,t[2]=(r>>16&255)/255,t[3]=(r>>24&255)/255,t}function qy(r){return r[0]+(r[1]<<8)+(r[2]<<16)+(r[3]<<24)}function _y(r,e){var t=r.createTexture();return t.buffer=function(a){r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MAG_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR_MIPMAP_NEAREST),r.pixelStorei(r.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,r.RGBA,r.UNSIGNED_BYTE,a),r.generateMipmap(r.TEXTURE_2D),r.bindTexture(r.TEXTURE_2D,null)},t.deleteTexture=function(){r.deleteTexture(t)},t}function mf(r,e){switch(e){case"float":return[1,r.FLOAT,4];case"vec2":return[2,r.FLOAT,4];case"vec3":return[3,r.FLOAT,4];case"vec4":return[4,r.FLOAT,4];case"int":return[1,r.INT,4];case"ivec2":return[2,r.INT,4]}}function bf(r,e,t){switch(e){case r.FLOAT:return new Float32Array(t);case r.INT:return new Int32Array(t)}}function Gy(r,e,t,a,n,i){switch(e){case r.FLOAT:return new Float32Array(t.buffer,i*a,n);case r.INT:return new Int32Array(t.buffer,i*a,n)}}function Hy(r,e,t,a){var n=mf(r,e),i=Ye(n,2),s=i[0],o=i[1],l=bf(r,o,a),u=r.createBuffer();return r.bindBuffer(r.ARRAY_BUFFER,u),r.bufferData(r.ARRAY_BUFFER,l,r.STATIC_DRAW),o===r.FLOAT?r.vertexAttribPointer(t,s,o,!1,0,0):o===r.INT&&r.vertexAttribIPointer(t,s,o,0,0),r.enableVertexAttribArray(t),r.bindBuffer(r.ARRAY_BUFFER,null),u}function Hr(r,e,t,a){var n=mf(r,t),i=Ye(n,3),s=i[0],o=i[1],l=i[2],u=bf(r,o,e*s),v=s*l,f=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,f),r.bufferData(r.ARRAY_BUFFER,e*v,r.DYNAMIC_DRAW),r.enableVertexAttribArray(a),o===r.FLOAT?r.vertexAttribPointer(a,s,o,!1,v,0):o===r.INT&&r.vertexAttribIPointer(a,s,o,v,0),r.vertexAttribDivisor(a,1),r.bindBuffer(r.ARRAY_BUFFER,null);for(var c=new Array(e),h=0;h<e;h++)c[h]=Gy(r,o,u,v,s,h);return f.dataArray=u,f.stride=v,f.size=s,f.getView=function(d){return c[d]},f.setPoint=function(d,y,g){var p=c[d];p[0]=y,p[1]=g},f.bufferSubData=function(d){r.bindBuffer(r.ARRAY_BUFFER,f),d?r.bufferSubData(r.ARRAY_BUFFER,0,u,0,d*s):r.bufferSubData(r.ARRAY_BUFFER,0,u)},f}function Wy(r,e,t){for(var a=9,n=new Float32Array(e*a),i=new Array(e),s=0;s<e;s++){var o=s*a*4;i[s]=new Float32Array(n.buffer,o,a)}var l=r.createBuffer();r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferData(r.ARRAY_BUFFER,n.byteLength,r.DYNAMIC_DRAW);for(var u=0;u<3;u++){var v=t+u;r.enableVertexAttribArray(v),r.vertexAttribPointer(v,3,r.FLOAT,!1,3*12,u*12),r.vertexAttribDivisor(v,1)}return r.bindBuffer(r.ARRAY_BUFFER,null),l.getMatrixView=function(f){return i[f]},l.setData=function(f,c){i[c].set(f,0)},l.bufferSubData=function(){r.bindBuffer(r.ARRAY_BUFFER,l),r.bufferSubData(r.ARRAY_BUFFER,0,n)},l}function $y(r){var e=r.createFramebuffer();r.bindFramebuffer(r.FRAMEBUFFER,e);var t=r.createTexture();return r.bindTexture(r.TEXTURE_2D,t),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_MIN_FILTER,r.LINEAR),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_S,r.CLAMP_TO_EDGE),r.texParameteri(r.TEXTURE_2D,r.TEXTURE_WRAP_T,r.CLAMP_TO_EDGE),r.framebufferTexture2D(r.FRAMEBUFFER,r.COLOR_ATTACHMENT0,r.TEXTURE_2D,t,0),r.bindFramebuffer(r.FRAMEBUFFER,null),e.setFramebufferAttachmentSizes=function(a,n){r.bindTexture(r.TEXTURE_2D,t),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,a,n,0,r.RGBA,r.UNSIGNED_BYTE,null)},e}var wf=typeof Float32Array!="undefined"?Float32Array:Array;Math.hypot||(Math.hypot=function(){for(var r=0,e=arguments.length;e--;)r+=arguments[e]*arguments[e];return Math.sqrt(r)});function oo(){var r=new wf(9);return wf!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[5]=0,r[6]=0,r[7]=0),r[0]=1,r[4]=1,r[8]=1,r}function xf(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Uy(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1],d=t[2],y=t[3],g=t[4],p=t[5],m=t[6],b=t[7],w=t[8];return r[0]=c*a+h*s+d*u,r[1]=c*n+h*o+d*v,r[2]=c*i+h*l+d*f,r[3]=y*a+g*s+p*u,r[4]=y*n+g*o+p*v,r[5]=y*i+g*l+p*f,r[6]=m*a+b*s+w*u,r[7]=m*n+b*o+w*v,r[8]=m*i+b*l+w*f,r}function Wn(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=t[0],h=t[1];return r[0]=a,r[1]=n,r[2]=i,r[3]=s,r[4]=o,r[5]=l,r[6]=c*a+h*s+u,r[7]=c*n+h*o+v,r[8]=c*i+h*l+f,r}function Ef(r,e,t){var a=e[0],n=e[1],i=e[2],s=e[3],o=e[4],l=e[5],u=e[6],v=e[7],f=e[8],c=Math.sin(t),h=Math.cos(t);return r[0]=h*a+c*s,r[1]=h*n+c*o,r[2]=h*i+c*l,r[3]=h*s-c*a,r[4]=h*o-c*n,r[5]=h*l-c*i,r[6]=u,r[7]=v,r[8]=f,r}function uo(r,e,t){var a=t[0],n=t[1];return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=n*e[3],r[4]=n*e[4],r[5]=n*e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function Ky(r,e,t){return r[0]=2/e,r[1]=0,r[2]=0,r[3]=0,r[4]=-2/t,r[5]=0,r[6]=-1,r[7]=1,r[8]=1,r}var Xy=function(){function r(e,t,a,n){jr(this,r),this.debugID=Math.floor(Math.random()*1e4),this.r=e,this.texSize=t,this.texRows=a,this.texHeight=Math.floor(t/a),this.enableWrapping=!0,this.locked=!1,this.texture=null,this.needsBuffer=!0,this.freePointer={x:0,row:0},this.keyToLocation=new Map,this.canvas=n(e,t,t),this.scratch=n(e,t,this.texHeight,"scratch")}return et(r,[{key:"lock",value:function(){this.locked=!0}},{key:"getKeys",value:function(){return new Set(this.keyToLocation.keys())}},{key:"getScale",value:function(t){var a=t.w,n=t.h,i=this.texHeight,s=this.texSize,o=i/n,l=a*o,u=n*o;return l>s&&(o=s/a,l=a*o,u=n*o),{scale:o,texW:l,texH:u}}},{key:"draw",value:function(t,a,n){var i=this;if(this.locked)throw new Error("can't draw, atlas is locked");var s=this.texSize,o=this.texRows,l=this.texHeight,u=this.getScale(a),v=u.scale,f=u.texW,c=u.texH,h=function(b,w){if(n&&w){var E=w.context,C=b.x,x=b.row,T=C,k=l*x;E.save(),E.translate(T,k),E.scale(v,v),n(E,a),E.restore()}},d=[null,null],y=function(){h(i.freePointer,i.canvas),d[0]={x:i.freePointer.x,y:i.freePointer.row*l,w:f,h:c},d[1]={x:i.freePointer.x+f,y:i.freePointer.row*l,w:0,h:c},i.freePointer.x+=f,i.freePointer.x==s&&(i.freePointer.x=0,i.freePointer.row++)},g=function(){var b=i.scratch,w=i.canvas;b.clear(),h({x:0,row:0},b);var E=s-i.freePointer.x,C=f-E,x=l;{var T=i.freePointer.x,k=i.freePointer.row*l,D=E;w.context.drawImage(b,0,0,D,x,T,k,D,x),d[0]={x:T,y:k,w:D,h:c}}{var B=E,P=(i.freePointer.row+1)*l,A=C;w&&w.context.drawImage(b,B,0,A,x,0,P,A,x),d[1]={x:0,y:P,w:A,h:c}}i.freePointer.x=C,i.freePointer.row++},p=function(){i.freePointer.x=0,i.freePointer.row++};if(this.freePointer.x+f<=s)y();else{if(this.freePointer.row>=o-1)return!1;this.freePointer.x===s?(p(),y()):this.enableWrapping?g():(p(),y())}return this.keyToLocation.set(t,d),this.needsBuffer=!0,d}},{key:"getOffsets",value:function(t){return this.keyToLocation.get(t)}},{key:"isEmpty",value:function(){return this.freePointer.x===0&&this.freePointer.row===0}},{key:"canFit",value:function(t){if(this.locked)return!1;var a=this.texSize,n=this.texRows,i=this.getScale(t),s=i.texW;return this.freePointer.x+s>a?this.freePointer.row<n-1:!0}},{key:"bufferIfNeeded",value:function(t){this.texture||(this.texture=_y(t,this.debugID)),this.needsBuffer&&(this.texture.buffer(this.canvas),this.needsBuffer=!1,this.locked&&(this.canvas=null,this.scratch=null))}},{key:"dispose",value:function(){this.texture&&(this.texture.deleteTexture(),this.texture=null),this.canvas=null,this.scratch=null,this.locked=!0}}])}(),Yy=function(){function r(e,t,a,n){jr(this,r),this.r=e,this.texSize=t,this.texRows=a,this.createTextureCanvas=n,this.atlases=[],this.styleKeyToAtlas=new Map,this.markedKeys=new Set}return et(r,[{key:"getKeys",value:function(){return new Set(this.styleKeyToAtlas.keys())}},{key:"_createAtlas",value:function(){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas;return new Xy(t,a,n,i)}},{key:"_getScratchCanvas",value:function(){if(!this.scratch){var t=this.r,a=this.texSize,n=this.texRows,i=this.createTextureCanvas,s=Math.floor(a/n);this.scratch=i(t,a,s,"scratch")}return this.scratch}},{key:"draw",value:function(t,a,n){var i=this.styleKeyToAtlas.get(t);return i||(i=this.atlases[this.atlases.length-1],(!i||!i.canFit(a))&&(i&&i.lock(),i=this._createAtlas(),this.atlases.push(i)),i.draw(t,a,n),this.styleKeyToAtlas.set(t,i)),i}},{key:"getAtlas",value:function(t){return this.styleKeyToAtlas.get(t)}},{key:"hasAtlas",value:function(t){return this.styleKeyToAtlas.has(t)}},{key:"markKeyForGC",value:function(t){this.markedKeys.add(t)}},{key:"gc",value:function(){var t=this,a=this.markedKeys;if(a.size===0){console.log("nothing to garbage collect");return}var n=[],i=new Map,s=null,o=Er(this.atlases),l;try{var u=function(){var f=l.value,c=f.getKeys(),h=Zy(a,c);if(h.size===0)return n.push(f),c.forEach(function(E){return i.set(E,f)}),1;s||(s=t._createAtlas(),n.push(s));var d=Er(c),y;try{for(d.s();!(y=d.n()).done;){var g=y.value;if(!h.has(g)){var p=f.getOffsets(g),m=Ye(p,2),b=m[0],w=m[1];s.canFit({w:b.w+w.w,h:b.h})||(s.lock(),s=t._createAtlas(),n.push(s)),f.canvas&&(t._copyTextureToNewAtlas(g,f,s),i.set(g,s))}}}catch(E){d.e(E)}finally{d.f()}f.dispose()};for(o.s();!(l=o.n()).done;)u()}catch(v){o.e(v)}finally{o.f()}this.atlases=n,this.styleKeyToAtlas=i,this.markedKeys=new Set}},{key:"_copyTextureToNewAtlas",value:function(t,a,n){var i=a.getOffsets(t),s=Ye(i,2),o=s[0],l=s[1];if(l.w===0)n.draw(t,o,function(c){c.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h)});else{var u=this._getScratchCanvas();u.clear(),u.context.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,0,o.w,o.h),u.context.drawImage(a.canvas,l.x,l.y,l.w,l.h,o.w,0,l.w,l.h);var v=o.w+l.w,f=o.h;n.draw(t,{w:v,h:f},function(c){c.drawImage(u,0,0,v,f,0,0,v,f)})}}},{key:"getCounts",value:function(){return{keyCount:this.styleKeyToAtlas.size,atlasCount:new Set(this.styleKeyToAtlas.values()).size}}}])}();function Zy(r,e){return r.intersection?r.intersection(e):new Set($a(r).filter(function(t){return e.has(t)}))}var Qy=function(){function r(e,t){jr(this,r),this.r=e,this.globalOptions=t,this.atlasSize=t.webglTexSize,this.maxAtlasesPerBatch=t.webglTexPerBatch,this.renderTypes=new Map,this.collections=new Map,this.typeAndIdToKey=new Map}return et(r,[{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"addAtlasCollection",value:function(t,a){var n=this.globalOptions,i=n.webglTexSize,s=n.createTextureCanvas,o=a.texRows,l=this._cacheScratchCanvas(s),u=new Yy(this.r,i,o,l);this.collections.set(t,u)}},{key:"addRenderType",value:function(t,a){var n=a.collection;if(!this.collections.has(n))throw new Error("invalid atlas collection name '".concat(n,"'"));var i=this.collections.get(n),s=me({type:t,atlasCollection:i},a);this.renderTypes.set(t,s)}},{key:"getRenderTypeOpts",value:function(t){return this.renderTypes.get(t)}},{key:"getAtlasCollection",value:function(t){return this.collections.get(t)}},{key:"_cacheScratchCanvas",value:function(t){var a=-1,n=-1,i=null;return function(s,o,l,u){return u?((!i||o!=a||l!=n)&&(a=o,n=l,i=t(s,o,l)),i):t(s,o,l)}}},{key:"_key",value:function(t,a){return"".concat(t,"-").concat(a)}},{key:"invalidate",value:function(t){var a=this,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.forceRedraw,s=i===void 0?!1:i,o=n.filterEle,l=o===void 0?function(){return!0}:o,u=n.filterType,v=u===void 0?function(){return!0}:u,f=!1,c=!1,h=Er(t),d;try{for(h.s();!(d=h.n()).done;){var y=d.value;if(l(y)){var g=Er(this.renderTypes.values()),p;try{var m=function(){var w=p.value,E=w.type;if(v(E)){var C=a.collections.get(w.collection),x=w.getKey(y),T=Array.isArray(x)?x:[x];if(s)T.forEach(function(P){return C.markKeyForGC(P)}),c=!0;else{var k=w.getID?w.getID(y):y.id(),D=a._key(E,k),B=a.typeAndIdToKey.get(D);B!==void 0&&!Vy(T,B)&&(f=!0,a.typeAndIdToKey.delete(D),B.forEach(function(P){return C.markKeyForGC(P)}))}}};for(g.s();!(p=g.n()).done;)m()}catch(b){g.e(b)}finally{g.f()}}}}catch(b){h.e(b)}finally{h.f()}return c&&(this.gc(),f=!1),f}},{key:"gc",value:function(){var t=Er(this.collections.values()),a;try{for(t.s();!(a=t.n()).done;){var n=a.value;n.gc()}}catch(i){t.e(i)}finally{t.f()}}},{key:"getOrCreateAtlas",value:function(t,a,n,i){var s=this.renderTypes.get(a),o=this.collections.get(s.collection),l=!1,u=o.draw(i,n,function(c){s.drawClipped?(c.save(),c.beginPath(),c.rect(0,0,n.w,n.h),c.clip(),s.drawElement(c,t,n,!0,!0),c.restore()):s.drawElement(c,t,n,!0,!0),l=!0});if(l){var v=s.getID?s.getID(t):t.id(),f=this._key(a,v);this.typeAndIdToKey.has(f)?this.typeAndIdToKey.get(f).push(i):this.typeAndIdToKey.set(f,[i])}return u}},{key:"getAtlasInfo",value:function(t,a){var n=this,i=this.renderTypes.get(a),s=i.getKey(t),o=Array.isArray(s)?s:[s];return o.map(function(l){var u=i.getBoundingBox(t,l),v=n.getOrCreateAtlas(t,a,u,l),f=v.getOffsets(l),c=Ye(f,2),h=c[0],d=c[1];return{atlas:v,tex:h,tex1:h,tex2:d,bb:u}})}},{key:"getDebugInfo",value:function(){var t=[],a=Er(this.collections),n;try{for(a.s();!(n=a.n()).done;){var i=Ye(n.value,2),s=i[0],o=i[1],l=o.getCounts(),u=l.keyCount,v=l.atlasCount;t.push({type:s,keyCount:u,atlasCount:v})}}catch(f){a.e(f)}finally{a.f()}return t}}])}(),Jy=function(){function r(e){jr(this,r),this.globalOptions=e,this.atlasSize=e.webglTexSize,this.maxAtlasesPerBatch=e.webglTexPerBatch,this.batchAtlases=[]}return et(r,[{key:"getMaxAtlasesPerBatch",value:function(){return this.maxAtlasesPerBatch}},{key:"getAtlasSize",value:function(){return this.atlasSize}},{key:"getIndexArray",value:function(){return Array.from({length:this.maxAtlasesPerBatch},function(t,a){return a})}},{key:"startBatch",value:function(){this.batchAtlases=[]}},{key:"getAtlasCount",value:function(){return this.batchAtlases.length}},{key:"getAtlases",value:function(){return this.batchAtlases}},{key:"canAddToCurrentBatch",value:function(t){return this.batchAtlases.length===this.maxAtlasesPerBatch?this.batchAtlases.includes(t):!0}},{key:"getAtlasIndexForBatch",value:function(t){var a=this.batchAtlases.indexOf(t);if(a<0){if(this.batchAtlases.length===this.maxAtlasesPerBatch)throw new Error("cannot add more atlases to batch");this.batchAtlases.push(t),a=this.batchAtlases.length-1}return a}}])}(),jy=`
  float circleSD(vec2 p, float r) {
    return distance(vec2(0), p) - r; // signed distance
  }
`,em=`
  float rectangleSD(vec2 p, vec2 b) {
    vec2 d = abs(p)-b;
    return distance(vec2(0),max(d,0.0)) + min(max(d.x,d.y),0.0);
  }
`,rm=`
  float roundRectangleSD(vec2 p, vec2 b, vec4 cr) {
    cr.xy = (p.x > 0.0) ? cr.xy : cr.zw;
    cr.x  = (p.y > 0.0) ? cr.x  : cr.y;
    vec2 q = abs(p) - b + cr.x;
    return min(max(q.x, q.y), 0.0) + distance(vec2(0), max(q, 0.0)) - cr.x;
  }
`,tm=`
  float ellipseSD(vec2 p, vec2 ab) {
    p = abs( p ); // symmetry

    // find root with Newton solver
    vec2 q = ab*(p-ab);
    float w = (q.x<q.y)? 1.570796327 : 0.0;
    for( int i=0; i<5; i++ ) {
      vec2 cs = vec2(cos(w),sin(w));
      vec2 u = ab*vec2( cs.x,cs.y);
      vec2 v = ab*vec2(-cs.y,cs.x);
      w = w + dot(p-u,v)/(dot(p-u,u)+dot(v,v));
    }
    
    // compute final point and distance
    float d = length(p-ab*vec2(cos(w),sin(w)));
    
    // return signed distance
    return (dot(p/ab,p/ab)>1.0) ? d : -d;
  }
`,Fa={SCREEN:{name:"screen",screen:!0},PICKING:{name:"picking",picking:!0}},$n={IGNORE:1,USE_BB:2},lo=0,Cf=1,Tf=2,vo=3,na=4,Un=5,Va=6,qa=7,am=function(){function r(e,t,a){jr(this,r),this.r=e,this.gl=t,this.maxInstances=a.webglBatchSize,this.atlasSize=a.webglTexSize,this.bgColor=a.bgColor,this.debug=a.webglDebug,this.batchDebugInfo=[],a.enableWrapping=!0,a.createTextureCanvas=Oy,this.atlasManager=new Qy(e,a),this.batchManager=new Jy(a),this.simpleShapeOptions=new Map,this.program=this._createShaderProgram(Fa.SCREEN),this.pickingProgram=this._createShaderProgram(Fa.PICKING),this.vao=this._createVAO()}return et(r,[{key:"addAtlasCollection",value:function(t,a){this.atlasManager.addAtlasCollection(t,a)}},{key:"addTextureAtlasRenderType",value:function(t,a){this.atlasManager.addRenderType(t,a)}},{key:"addSimpleShapeRenderType",value:function(t,a){this.simpleShapeOptions.set(t,a)}},{key:"invalidate",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=a.type,i=this.atlasManager;return n?i.invalidate(t,{filterType:function(o){return o===n},forceRedraw:!0}):i.invalidate(t)}},{key:"gc",value:function(){this.atlasManager.gc()}},{key:"_createShaderProgram",value:function(t){var a=this.gl,n=`#version 300 es
      precision highp float;

      uniform mat3 uPanZoomMatrix;
      uniform int  uAtlasSize;
      
      // instanced
      in vec2 aPosition; // a vertex from the unit square
      
      in mat3 aTransform; // used to transform verticies, eg into a bounding box
      in int aVertType; // the type of thing we are rendering

      // the z-index that is output when using picking mode
      in vec4 aIndex;
      
      // For textures
      in int aAtlasId; // which shader unit/atlas to use
      in vec4 aTex; // x/y/w/h of texture in atlas

      // for edges
      in vec4 aPointAPointB;
      in vec4 aPointCPointD;
      in vec2 aLineWidth; // also used for node border width

      // simple shapes
      in vec4 aCornerRadius; // for round-rectangle [top-right, bottom-right, top-left, bottom-left]
      in vec4 aColor; // also used for edges
      in vec4 aBorderColor; // aLineWidth is used for border width

      // output values passed to the fragment shader
      out vec2 vTexCoord;
      out vec4 vColor;
      out vec2 vPosition;
      // flat values are not interpolated
      flat out int vAtlasId; 
      flat out int vVertType;
      flat out vec2 vTopRight;
      flat out vec2 vBotLeft;
      flat out vec4 vCornerRadius;
      flat out vec4 vBorderColor;
      flat out vec2 vBorderWidth;
      flat out vec4 vIndex;
      
      void main(void) {
        int vid = gl_VertexID;
        vec2 position = aPosition; // TODO make this a vec3, simplifies some code below

        if(aVertType == `.concat(lo,`) {
          float texX = aTex.x; // texture coordinates
          float texY = aTex.y;
          float texW = aTex.z;
          float texH = aTex.w;

          if(vid == 1 || vid == 2 || vid == 4) {
            texX += texW;
          }
          if(vid == 2 || vid == 4 || vid == 5) {
            texY += texH;
          }

          float d = float(uAtlasSize);
          vTexCoord = vec2(texX / d, texY / d); // tex coords must be between 0 and 1

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(na," || aVertType == ").concat(qa,` 
             || aVertType == `).concat(Un," || aVertType == ").concat(Va,`) { // simple shapes

          // the bounding box is needed by the fragment shader
          vBotLeft  = (aTransform * vec3(0, 0, 1)).xy; // flat
          vTopRight = (aTransform * vec3(1, 1, 1)).xy; // flat
          vPosition = (aTransform * vec3(position, 1)).xy; // will be interpolated

          // calculations are done in the fragment shader, just pass these along
          vColor = aColor;
          vCornerRadius = aCornerRadius;
          vBorderColor = aBorderColor;
          vBorderWidth = aLineWidth;

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
        }
        else if(aVertType == `).concat(Cf,`) {
          vec2 source = aPointAPointB.xy;
          vec2 target = aPointAPointB.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          // stretch the unit square into a long skinny rectangle
          vec2 xBasis = target - source;
          vec2 yBasis = normalize(vec2(-xBasis.y, xBasis.x));
          vec2 point = source + xBasis * position.x + yBasis * aLineWidth[0] * position.y;

          gl_Position = vec4(uPanZoomMatrix * vec3(point, 1.0), 1.0);
          vColor = aColor;
        } 
        else if(aVertType == `).concat(Tf,`) {
          vec2 pointA = aPointAPointB.xy;
          vec2 pointB = aPointAPointB.zw;
          vec2 pointC = aPointCPointD.xy;
          vec2 pointD = aPointCPointD.zw;

          // adjust the geometry so that the line is centered on the edge
          position.y = position.y - 0.5;

          vec2 p0, p1, p2, pos;
          if(position.x == 0.0) { // The left side of the unit square
            p0 = pointA;
            p1 = pointB;
            p2 = pointC;
            pos = position;
          } else { // The right side of the unit square, use same approach but flip the geometry upside down
            p0 = pointD;
            p1 = pointC;
            p2 = pointB;
            pos = vec2(0.0, -position.y);
          }

          vec2 p01 = p1 - p0;
          vec2 p12 = p2 - p1;
          vec2 p21 = p1 - p2;

          // Find the normal vector.
          vec2 tangent = normalize(normalize(p12) + normalize(p01));
          vec2 normal = vec2(-tangent.y, tangent.x);

          // Find the vector perpendicular to p0 -> p1.
          vec2 p01Norm = normalize(vec2(-p01.y, p01.x));

          // Determine the bend direction.
          float sigma = sign(dot(p01 + p21, normal));
          float width = aLineWidth[0];

          if(sign(pos.y) == -sigma) {
            // This is an intersecting vertex. Adjust the position so that there's no overlap.
            vec2 point = 0.5 * width * normal * -sigma / dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          } else {
            // This is a non-intersecting vertex. Treat it like a mitre join.
            vec2 point = 0.5 * width * normal * sigma * dot(normal, p01Norm);
            gl_Position = vec4(uPanZoomMatrix * vec3(p1 + point, 1.0), 1.0);
          }

          vColor = aColor;
        } 
        else if(aVertType == `).concat(vo,` && vid < 3) {
          // massage the first triangle into an edge arrow
          if(vid == 0)
            position = vec2(-0.15, -0.3);
          if(vid == 1)
            position = vec2(  0.0,  0.0);
          if(vid == 2)
            position = vec2( 0.15, -0.3);

          gl_Position = vec4(uPanZoomMatrix * aTransform * vec3(position, 1.0), 1.0);
          vColor = aColor;
        }
        else {
          gl_Position = vec4(2.0, 0.0, 0.0, 1.0); // discard vertex by putting it outside webgl clip space
        }

        vAtlasId = aAtlasId;
        vVertType = aVertType;
        vIndex = aIndex;
      }
    `),i=this.batchManager.getIndexArray(),s=`#version 300 es
      precision highp float;

      // declare texture unit for each texture atlas in the batch
      `.concat(i.map(function(u){return"uniform sampler2D uTexture".concat(u,";")}).join(`
	`),`

      uniform vec4 uBGColor;
      uniform float uZoom;

      in vec2 vTexCoord;
      in vec4 vColor;
      in vec2 vPosition; // model coordinates

      flat in int vAtlasId;
      flat in vec4 vIndex;
      flat in int vVertType;
      flat in vec2 vTopRight;
      flat in vec2 vBotLeft;
      flat in vec4 vCornerRadius;
      flat in vec4 vBorderColor;
      flat in vec2 vBorderWidth;

      out vec4 outColor;

      `).concat(jy,`
      `).concat(em,`
      `).concat(rm,`
      `).concat(tm,`

      vec4 blend(vec4 top, vec4 bot) { // blend colors with premultiplied alpha
        return vec4( 
          top.rgb + (bot.rgb * (1.0 - top.a)),
          top.a   + (bot.a   * (1.0 - top.a)) 
        );
      }

      vec4 distInterp(vec4 cA, vec4 cB, float d) { // interpolate color using Signed Distance
        // scale to the zoom level so that borders don't look blurry when zoomed in
        // note 1.5 is an aribitrary value chosen because it looks good
        return mix(cA, cB, 1.0 - smoothstep(0.0, 1.5 / uZoom, abs(d))); 
      }

      void main(void) {
        if(vVertType == `).concat(lo,`) {
          // look up the texel from the texture unit
          `).concat(i.map(function(u){return"if(vAtlasId == ".concat(u,") outColor = texture(uTexture").concat(u,", vTexCoord);")}).join(`
	else `),`
        } 
        else if(vVertType == `).concat(vo,`) {
          // mimics how canvas renderer uses context.globalCompositeOperation = 'destination-out';
          outColor = blend(vColor, uBGColor);
          outColor.a = 1.0; // make opaque, masks out line under arrow
        }
        else if(vVertType == `).concat(na,` && vBorderWidth == vec2(0.0)) { // simple rectangle with no border
          outColor = vColor; // unit square is already transformed to the rectangle, nothing else needs to be done
        }
        else if(vVertType == `).concat(na," || vVertType == ").concat(qa,` 
          || vVertType == `).concat(Un," || vVertType == ").concat(Va,`) { // use SDF

          float outerBorder = vBorderWidth[0];
          float innerBorder = vBorderWidth[1];
          float borderPadding = outerBorder * 2.0;
          float w = vTopRight.x - vBotLeft.x - borderPadding;
          float h = vTopRight.y - vBotLeft.y - borderPadding;
          vec2 b = vec2(w/2.0, h/2.0); // half width, half height
          vec2 p = vPosition - vec2(vTopRight.x - b[0] - outerBorder, vTopRight.y - b[1] - outerBorder); // translate to center

          float d; // signed distance
          if(vVertType == `).concat(na,`) {
            d = rectangleSD(p, b);
          } else if(vVertType == `).concat(qa,` && w == h) {
            d = circleSD(p, b.x); // faster than ellipse
          } else if(vVertType == `).concat(qa,`) {
            d = ellipseSD(p, b);
          } else {
            d = roundRectangleSD(p, b, vCornerRadius.wzyx);
          }

          // use the distance to interpolate a color to smooth the edges of the shape, doesn't need multisampling
          // we must smooth colors inwards, because we can't change pixels outside the shape's bounding box
          if(d > 0.0) {
            if(d > outerBorder) {
              discard;
            } else {
              outColor = distInterp(vBorderColor, vec4(0), d - outerBorder);
            }
          } else {
            if(d > innerBorder) {
              vec4 outerColor = outerBorder == 0.0 ? vec4(0) : vBorderColor;
              vec4 innerBorderColor = blend(vBorderColor, vColor);
              outColor = distInterp(innerBorderColor, outerColor, d);
            } 
            else {
              vec4 outerColor;
              if(innerBorder == 0.0 && outerBorder == 0.0) {
                outerColor = vec4(0);
              } else if(innerBorder == 0.0) {
                outerColor = vBorderColor;
              } else {
                outerColor = blend(vBorderColor, vColor);
              }
              outColor = distInterp(vColor, outerColor, d - innerBorder);
            }
          }
        }
        else {
          outColor = vColor;
        }

        `).concat(t.picking?`if(outColor.a == 0.0) discard;
             else outColor = vIndex;`:"",`
      }
    `),o=Iy(a,n,s);o.aPosition=a.getAttribLocation(o,"aPosition"),o.aIndex=a.getAttribLocation(o,"aIndex"),o.aVertType=a.getAttribLocation(o,"aVertType"),o.aTransform=a.getAttribLocation(o,"aTransform"),o.aAtlasId=a.getAttribLocation(o,"aAtlasId"),o.aTex=a.getAttribLocation(o,"aTex"),o.aPointAPointB=a.getAttribLocation(o,"aPointAPointB"),o.aPointCPointD=a.getAttribLocation(o,"aPointCPointD"),o.aLineWidth=a.getAttribLocation(o,"aLineWidth"),o.aColor=a.getAttribLocation(o,"aColor"),o.aCornerRadius=a.getAttribLocation(o,"aCornerRadius"),o.aBorderColor=a.getAttribLocation(o,"aBorderColor"),o.uPanZoomMatrix=a.getUniformLocation(o,"uPanZoomMatrix"),o.uAtlasSize=a.getUniformLocation(o,"uAtlasSize"),o.uBGColor=a.getUniformLocation(o,"uBGColor"),o.uZoom=a.getUniformLocation(o,"uZoom"),o.uTextures=[];for(var l=0;l<this.batchManager.getMaxAtlasesPerBatch();l++)o.uTextures.push(a.getUniformLocation(o,"uTexture".concat(l)));return o}},{key:"_createVAO",value:function(){var t=[0,0,1,0,1,1,0,0,1,1,0,1];this.vertexCount=t.length/2;var a=this.maxInstances,n=this.gl,i=this.program,s=n.createVertexArray();return n.bindVertexArray(s),Hy(n,"vec2",i.aPosition,t),this.transformBuffer=Wy(n,a,i.aTransform),this.indexBuffer=Hr(n,a,"vec4",i.aIndex),this.vertTypeBuffer=Hr(n,a,"int",i.aVertType),this.atlasIdBuffer=Hr(n,a,"int",i.aAtlasId),this.texBuffer=Hr(n,a,"vec4",i.aTex),this.pointAPointBBuffer=Hr(n,a,"vec4",i.aPointAPointB),this.pointCPointDBuffer=Hr(n,a,"vec4",i.aPointCPointD),this.lineWidthBuffer=Hr(n,a,"vec2",i.aLineWidth),this.colorBuffer=Hr(n,a,"vec4",i.aColor),this.cornerRadiusBuffer=Hr(n,a,"vec4",i.aCornerRadius),this.borderColorBuffer=Hr(n,a,"vec4",i.aBorderColor),n.bindVertexArray(null),s}},{key:"buffers",get:function(){var t=this;return this._buffers||(this._buffers=Object.keys(this).filter(function(a){return tt(a,"Buffer")}).map(function(a){return t[a]})),this._buffers}},{key:"startFrame",value:function(t){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Fa.SCREEN;this.panZoomMatrix=t,this.renderTarget=a,this.batchDebugInfo=[],this.wrappedCount=0,this.simpleCount=0,this.startBatch()}},{key:"startBatch",value:function(){this.instanceCount=0,this.batchManager.startBatch()}},{key:"endFrame",value:function(){this.endBatch()}},{key:"_isVisible",value:function(t,a){return t.visible()?a&&a.isVisible?a.isVisible(t):!0:!1}},{key:"drawTexture",value:function(t,a,n){var i=this.atlasManager,s=this.batchManager,o=i.getRenderTypeOpts(n);if(this._isVisible(t,o)){if(this.renderTarget.picking&&o.getTexPickingMode){var l=o.getTexPickingMode(t);if(l===$n.IGNORE)return;if(l==$n.USE_BB){this.drawPickingRectangle(t,a,n);return}}var u=i.getAtlasInfo(t,n),v=Er(u),f;try{for(v.s();!(f=v.n()).done;){var c=f.value,h=c.atlas,d=c.tex1,y=c.tex2;s.canAddToCurrentBatch(h)||this.endBatch();for(var g=s.getAtlasIndexForBatch(h),p=0,m=[[d,!0],[y,!1]];p<m.length;p++){var b=Ye(m[p],2),w=b[0],E=b[1];if(w.w!=0){var C=this.instanceCount;this.vertTypeBuffer.getView(C)[0]=lo;var x=this.indexBuffer.getView(C);aa(a,x);var T=this.atlasIdBuffer.getView(C);T[0]=g;var k=this.texBuffer.getView(C);k[0]=w.x,k[1]=w.y,k[2]=w.w,k[3]=w.h;var D=this.transformBuffer.getMatrixView(C);this.setTransformMatrix(t,D,o,c,E),this.instanceCount++,E||this.wrappedCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}catch(B){v.e(B)}finally{v.f()}}}},{key:"setTransformMatrix",value:function(t,a,n,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,o=0;if(n.shapeProps&&n.shapeProps.padding&&(o=t.pstyle(n.shapeProps.padding).pfValue),i){var l=i.bb,u=i.tex1,v=i.tex2,f=u.w/(u.w+v.w);s||(f=1-f);var c=this._getAdjustedBB(l,o,s,f);this._applyTransformMatrix(a,c,n,t)}else{var h=n.getBoundingBox(t),d=this._getAdjustedBB(h,o,!0,1);this._applyTransformMatrix(a,d,n,t)}}},{key:"_applyTransformMatrix",value:function(t,a,n,i){var s,o;xf(t);var l=n.getRotation?n.getRotation(i):0;if(l!==0){var u=n.getRotationPoint(i),v=u.x,f=u.y;Wn(t,t,[v,f]),Ef(t,t,l);var c=n.getRotationOffset(i);s=c.x+(a.xOffset||0),o=c.y+(a.yOffset||0)}else s=a.x1,o=a.y1;Wn(t,t,[s,o]),uo(t,t,[a.w,a.h])}},{key:"_getAdjustedBB",value:function(t,a,n,i){var s=t.x1,o=t.y1,l=t.w,u=t.h,v=t.yOffset;a&&(s-=a,o-=a,l+=2*a,u+=2*a);var f=0,c=l*i;return n&&i<1?l=c:!n&&i<1&&(f=l-c,s+=f,l=c),{x1:s,y1:o,w:l,h:u,xOffset:f,yOffset:v}}},{key:"drawPickingRectangle",value:function(t,a,n){var i=this.atlasManager.getRenderTypeOpts(n),s=this.instanceCount;this.vertTypeBuffer.getView(s)[0]=na;var o=this.indexBuffer.getView(s);aa(a,o);var l=this.colorBuffer.getView(s);Rt([0,0,0],1,l);var u=this.transformBuffer.getMatrixView(s);this.setTransformMatrix(t,u,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}},{key:"drawNode",value:function(t,a,n){var i=this.simpleShapeOptions.get(n);if(this._isVisible(t,i)){var s=i.shapeProps,o=this._getVertTypeForShape(t,s.shape);if(o===void 0||i.isSimple&&!i.isSimple(t)){this.drawTexture(t,a,n);return}var l=this.instanceCount;if(this.vertTypeBuffer.getView(l)[0]=o,o===Un||o===Va){var u=i.getBoundingBox(t),v=this._getCornerRadius(t,s.radius,u),f=this.cornerRadiusBuffer.getView(l);f[0]=v,f[1]=v,f[2]=v,f[3]=v,o===Va&&(f[0]=0,f[2]=0)}var c=this.indexBuffer.getView(l);aa(a,c);var h=t.pstyle(s.color).value,d=t.pstyle(s.opacity).value,y=this.colorBuffer.getView(l);Rt(h,d,y);var g=this.lineWidthBuffer.getView(l);if(g[0]=0,g[1]=0,s.border){var p=t.pstyle("border-width").value;if(p>0){var m=t.pstyle("border-color").value,b=t.pstyle("border-opacity").value,w=this.borderColorBuffer.getView(l);Rt(m,b,w);var E=t.pstyle("border-position").value;if(E==="inside")g[0]=0,g[1]=-p;else if(E==="outside")g[0]=p,g[1]=0;else{var C=p/2;g[0]=C,g[1]=-C}}}var x=this.transformBuffer.getMatrixView(l);this.setTransformMatrix(t,x,i),this.simpleCount++,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}},{key:"_getVertTypeForShape",value:function(t,a){var n=t.pstyle(a).value;switch(n){case"rectangle":return na;case"ellipse":return qa;case"roundrectangle":case"round-rectangle":return Un;case"bottom-round-rectangle":return Va;default:return}}},{key:"_getCornerRadius",value:function(t,a,n){var i=n.w,s=n.h;if(t.pstyle(a).value==="auto")return ot(i,s);var o=t.pstyle(a).pfValue,l=i/2,u=s/2;return Math.min(o,u,l)}},{key:"drawEdgeArrow",value:function(t,a,n){if(t.visible()){var i=t._private.rscratch,s,o,l;if(n==="source"?(s=i.arrowStartX,o=i.arrowStartY,l=i.srcArrowAngle):(s=i.arrowEndX,o=i.arrowEndY,l=i.tgtArrowAngle),!(isNaN(s)||s==null||isNaN(o)||o==null||isNaN(l)||l==null)){var u=t.pstyle(n+"-arrow-shape").value;if(u!=="none"){var v=t.pstyle(n+"-arrow-color").value,f=t.pstyle("opacity").value,c=t.pstyle("line-opacity").value,h=f*c,d=t.pstyle("width").pfValue,y=t.pstyle("arrow-scale").value,g=this.r.getArrowWidth(d,y),p=this.instanceCount,m=this.transformBuffer.getMatrixView(p);xf(m),Wn(m,m,[s,o]),uo(m,m,[g,g]),Ef(m,m,l),this.vertTypeBuffer.getView(p)[0]=vo;var b=this.indexBuffer.getView(p);aa(a,b);var w=this.colorBuffer.getView(p);Rt(v,h,w),this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"drawEdgeLine",value:function(t,a){if(t.visible()){var n=this._getEdgePoints(t);if(n){var i=t.pstyle("opacity").value,s=t.pstyle("line-opacity").value,o=t.pstyle("width").pfValue,l=t.pstyle("line-color").value,u=i*s;if(n.length/2+this.instanceCount>this.maxInstances&&this.endBatch(),n.length==4){var v=this.instanceCount;this.vertTypeBuffer.getView(v)[0]=Cf;var f=this.indexBuffer.getView(v);aa(a,f);var c=this.colorBuffer.getView(v);Rt(l,u,c);var h=this.lineWidthBuffer.getView(v);h[0]=o;var d=this.pointAPointBBuffer.getView(v);d[0]=n[0],d[1]=n[1],d[2]=n[2],d[3]=n[3],this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}else for(var y=0;y<n.length-2;y+=2){var g=this.instanceCount;this.vertTypeBuffer.getView(g)[0]=Tf;var p=this.indexBuffer.getView(g);aa(a,p);var m=this.colorBuffer.getView(g);Rt(l,u,m);var b=this.lineWidthBuffer.getView(g);b[0]=o;var w=n[y-2],E=n[y-1],C=n[y],x=n[y+1],T=n[y+2],k=n[y+3],D=n[y+4],B=n[y+5];y==0&&(w=2*C-T+.001,E=2*x-k+.001),y==n.length-4&&(D=2*T-C+.001,B=2*k-x+.001);var P=this.pointAPointBBuffer.getView(g);P[0]=w,P[1]=E,P[2]=C,P[3]=x;var A=this.pointCPointDBuffer.getView(g);A[0]=T,A[1]=k,A[2]=D,A[3]=B,this.instanceCount++,this.instanceCount>=this.maxInstances&&this.endBatch()}}}}},{key:"_getEdgePoints",value:function(t){var a=t._private.rscratch;if(!(a.badLine||a.allpts==null||isNaN(a.allpts[0]))){var n=a.allpts;if(n.length==4)return n;var i=this._getNumSegments(t);return this._getCurveSegmentPoints(n,i)}}},{key:"_getNumSegments",value:function(t){var a=15;return Math.min(Math.max(a,5),this.maxInstances)}},{key:"_getCurveSegmentPoints",value:function(t,a){if(t.length==4)return t;for(var n=Array((a+1)*2),i=0;i<=a;i++)if(i==0)n[0]=t[0],n[1]=t[1];else if(i==a)n[i*2]=t[t.length-2],n[i*2+1]=t[t.length-1];else{var s=i/a;this._setCurvePoint(t,s,n,i*2)}return n}},{key:"_setCurvePoint",value:function(t,a,n,i){if(t.length<=2)n[i]=t[0],n[i+1]=t[1];else{for(var s=Array(t.length-2),o=0;o<s.length;o+=2){var l=(1-a)*t[o]+a*t[o+2],u=(1-a)*t[o+1]+a*t[o+3];s[o]=l,s[o+1]=u}return this._setCurvePoint(s,a,n,i)}}},{key:"endBatch",value:function(){var t=this.gl,a=this.vao,n=this.vertexCount,i=this.instanceCount;if(i!==0){var s=this.renderTarget.picking?this.pickingProgram:this.program;t.useProgram(s),t.bindVertexArray(a);var o=Er(this.buffers),l;try{for(o.s();!(l=o.n()).done;){var u=l.value;u.bufferSubData(i)}}catch(d){o.e(d)}finally{o.f()}for(var v=this.batchManager.getAtlases(),f=0;f<v.length;f++)v[f].bufferIfNeeded(t);for(var c=0;c<v.length;c++)t.activeTexture(t.TEXTURE0+c),t.bindTexture(t.TEXTURE_2D,v[c].texture),t.uniform1i(s.uTextures[c],c);t.uniform1f(s.uZoom,Ny(this.r)),t.uniformMatrix3fv(s.uPanZoomMatrix,!1,this.panZoomMatrix),t.uniform1i(s.uAtlasSize,this.batchManager.getAtlasSize());var h=Rt(this.bgColor,1);t.uniform4fv(s.uBGColor,h),t.drawArraysInstanced(t.TRIANGLES,0,n,i),t.bindVertexArray(null),t.bindTexture(t.TEXTURE_2D,null),this.debug&&this.batchDebugInfo.push({count:i,atlasCount:v.length}),this.startBatch()}}},{key:"getDebugInfo",value:function(){var t=this.atlasManager.getDebugInfo(),a=t.reduce(function(s,o){return s+o.atlasCount},0),n=this.batchDebugInfo,i=n.reduce(function(s,o){return s+o.count},0);return{atlasInfo:t,totalAtlases:a,wrappedCount:this.wrappedCount,simpleCount:this.simpleCount,batchCount:n.length,batchInfo:n,totalInstances:i}}}])}(),Sf={};Sf.initWebgl=function(r,e){var t=this,a=t.data.contexts[t.WEBGL];r.bgColor=nm(t),r.webglTexSize=Math.min(r.webglTexSize,a.getParameter(a.MAX_TEXTURE_SIZE)),r.webglTexRows=Math.min(r.webglTexRows,54),r.webglTexRowsNodes=Math.min(r.webglTexRowsNodes,54),r.webglBatchSize=Math.min(r.webglBatchSize,16384),r.webglTexPerBatch=Math.min(r.webglTexPerBatch,a.getParameter(a.MAX_TEXTURE_IMAGE_UNITS)),t.webglDebug=r.webglDebug,t.webglDebugShowAtlases=r.webglDebugShowAtlases,t.pickingFrameBuffer=$y(a),t.pickingFrameBuffer.needsDraw=!0,t.drawing=new am(t,a,r);var n=function(f){return function(c){return t.getTextAngle(c,f)}},i=function(f){return function(c){var h=c.pstyle(f);return h&&h.value}},s=function(f){return function(c){return c.pstyle("".concat(f,"-opacity")).value>0}},o=function(f){var c=f.pstyle("text-events").strValue==="yes";return c?$n.USE_BB:$n.IGNORE},l=function(f){var c=f.position(),h=c.x,d=c.y,y=f.outerWidth(),g=f.outerHeight();return{w:y,h:g,x1:h-y/2,y1:d-g/2}};t.drawing.addAtlasCollection("node",{texRows:r.webglTexRowsNodes}),t.drawing.addAtlasCollection("label",{texRows:r.webglTexRows}),t.drawing.addTextureAtlasRenderType("node-body",{collection:"node",getKey:e.getStyleKey,getBoundingBox:e.getElementBox,drawElement:e.drawElement}),t.drawing.addSimpleShapeRenderType("node-body",{getBoundingBox:l,isSimple:Fy,shapeProps:{shape:"shape",color:"background-color",opacity:"background-opacity",radius:"corner-radius",border:!0}}),t.drawing.addSimpleShapeRenderType("node-overlay",{getBoundingBox:l,isVisible:s("overlay"),shapeProps:{shape:"overlay-shape",color:"overlay-color",opacity:"overlay-opacity",padding:"overlay-padding",radius:"overlay-corner-radius"}}),t.drawing.addSimpleShapeRenderType("node-underlay",{getBoundingBox:l,isVisible:s("underlay"),shapeProps:{shape:"underlay-shape",color:"underlay-color",opacity:"underlay-opacity",padding:"underlay-padding",radius:"underlay-corner-radius"}}),t.drawing.addTextureAtlasRenderType("label",{collection:"label",getTexPickingMode:o,getKey:fo(e.getLabelKey,null),getBoundingBox:co(e.getLabelBox,null),drawClipped:!0,drawElement:e.drawLabel,getRotation:n(null),getRotationPoint:e.getLabelRotationPoint,getRotationOffset:e.getLabelRotationOffset,isVisible:i("label")}),t.drawing.addTextureAtlasRenderType("edge-source-label",{collection:"label",getTexPickingMode:o,getKey:fo(e.getSourceLabelKey,"source"),getBoundingBox:co(e.getSourceLabelBox,"source"),drawClipped:!0,drawElement:e.drawSourceLabel,getRotation:n("source"),getRotationPoint:e.getSourceLabelRotationPoint,getRotationOffset:e.getSourceLabelRotationOffset,isVisible:i("source-label")}),t.drawing.addTextureAtlasRenderType("edge-target-label",{collection:"label",getTexPickingMode:o,getKey:fo(e.getTargetLabelKey,"target"),getBoundingBox:co(e.getTargetLabelBox,"target"),drawClipped:!0,drawElement:e.drawTargetLabel,getRotation:n("target"),getRotationPoint:e.getTargetLabelRotationPoint,getRotationOffset:e.getTargetLabelRotationOffset,isVisible:i("target-label")});var u=ca(function(){console.log("garbage collect flag set"),t.data.gc=!0},1e4);t.onUpdateEleCalcs(function(v,f){var c=!1;f&&f.length>0&&(c|=t.drawing.invalidate(f)),c&&u()}),im(t)};function nm(r){var e=r.cy.container(),t=e&&e.style&&e.style.backgroundColor||"white";return Po(t)}function kf(r,e){var t=r._private.rscratch;return Cr(t,"labelWrapCachedLines",e)||[]}var fo=function(e,t){return function(a){var n=e(a),i=kf(a,t);return i.length>1?i.map(function(s,o){return"".concat(n,"_").concat(o)}):n}},co=function(e,t){return function(a,n){var i=e(a);if(typeof n=="string"){var s=n.indexOf("_");if(s>0){var o=Number(n.substring(s+1)),l=kf(a,t),u=i.h/l.length,v=u*o,f=i.y1+v;return{x1:i.x1,w:i.w,y1:f,h:u,yOffset:v}}}return i}};function im(r){{var e=r.render;r.render=function(i){i=i||{};var s=r.cy;r.webgl&&(s.zoom()>sf?(sm(r),e.call(r,i)):(om(r),Bf(r,i,Fa.SCREEN)))}}{var t=r.matchCanvasSize;r.matchCanvasSize=function(i){t.call(r,i),r.pickingFrameBuffer.setFramebufferAttachmentSizes(r.canvasWidth,r.canvasHeight),r.pickingFrameBuffer.needsDraw=!0}}r.findNearestElements=function(i,s,o,l){return dm(r,i,s)};{var a=r.invalidateCachedZSortedEles;r.invalidateCachedZSortedEles=function(){a.call(r),r.pickingFrameBuffer.needsDraw=!0}}{var n=r.notify;r.notify=function(i,s){n.call(r,i,s),i==="viewport"||i==="bounds"?r.pickingFrameBuffer.needsDraw=!0:i==="background"&&r.drawing.invalidate(s,{type:"node-body"})}}}function sm(r){var e=r.data.contexts[r.WEBGL];e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT)}function om(r){var e=function(a){a.save(),a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,r.canvasWidth,r.canvasHeight),a.restore()};e(r.data.contexts[r.NODE]),e(r.data.contexts[r.DRAG])}function um(r){var e=r.canvasWidth,t=r.canvasHeight,a=so(r),n=a.pan,i=a.zoom,s=oo();Wn(s,s,[n.x,n.y]),uo(s,s,[i,i]);var o=oo();Ky(o,e,t);var l=oo();return Uy(l,o,s),l}function Df(r,e){var t=r.canvasWidth,a=r.canvasHeight,n=so(r),i=n.pan,s=n.zoom;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t,a),e.translate(i.x,i.y),e.scale(s,s)}function lm(r,e){r.drawSelectionRectangle(e,function(t){return Df(r,t)})}function vm(r){var e=r.data.contexts[r.NODE];e.save(),Df(r,e),e.strokeStyle="rgba(0, 0, 0, 0.3)",e.beginPath(),e.moveTo(-1e3,0),e.lineTo(1e3,0),e.stroke(),e.beginPath(),e.moveTo(0,-1e3),e.lineTo(0,1e3),e.stroke(),e.restore()}function fm(r){var e=function(n,i,s){for(var o=n.atlasManager.getAtlasCollection(i),l=r.data.contexts[r.NODE],u=o.atlases,v=0;v<u.length;v++){var f=u[v],c=f.canvas;if(c){var h=c.width,d=c.height,y=h*v,g=c.height*s,p=.4;l.save(),l.scale(p,p),l.drawImage(c,y,g),l.strokeStyle="black",l.rect(y,g,h,d),l.stroke(),l.restore()}}},t=0;e(r.drawing,"node",t++),e(r.drawing,"label",t++)}function cm(r,e,t,a,n){var i,s,o,l,u=so(r),v=u.pan,f=u.zoom;{var c=zy(r,v,f,e,t),h=Ye(c,2),d=h[0],y=h[1],g=6;i=d-g/2,s=y-g/2,o=g,l=g}if(o===0||l===0)return[];var p=r.data.contexts[r.WEBGL];p.bindFramebuffer(p.FRAMEBUFFER,r.pickingFrameBuffer),r.pickingFrameBuffer.needsDraw&&(p.viewport(0,0,p.canvas.width,p.canvas.height),Bf(r,null,Fa.PICKING),r.pickingFrameBuffer.needsDraw=!1);var m=o*l,b=new Uint8Array(m*4);p.readPixels(i,s,o,l,p.RGBA,p.UNSIGNED_BYTE,b),p.bindFramebuffer(p.FRAMEBUFFER,null);for(var w=new Set,E=0;E<m;E++){var C=b.slice(E*4,E*4+4),x=qy(C)-1;x>=0&&w.add(x)}return w}function dm(r,e,t){var a=cm(r,e,t),n=r.getCachedZSortedEles(),i,s,o=Er(a),l;try{for(o.s();!(l=o.n()).done;){var u=l.value,v=n[u];if(!i&&v.isNode()&&(i=v),!s&&v.isEdge()&&(s=v),i&&s)break}}catch(f){o.e(f)}finally{o.f()}return[i,s].filter(Boolean)}function ho(r,e,t){var a=r.drawing;e+=1,t.isNode()?(a.drawNode(t,e,"node-underlay"),a.drawNode(t,e,"node-body"),a.drawTexture(t,e,"label"),a.drawNode(t,e,"node-overlay")):(a.drawEdgeLine(t,e),a.drawEdgeArrow(t,e,"source"),a.drawEdgeArrow(t,e,"target"),a.drawTexture(t,e,"label"),a.drawTexture(t,e,"edge-source-label"),a.drawTexture(t,e,"edge-target-label"))}function Bf(r,e,t){var a;r.webglDebug&&(a=performance.now());var n=r.drawing,i=0;if(t.screen&&r.data.canvasNeedsRedraw[r.SELECT_BOX]&&lm(r,e),r.data.canvasNeedsRedraw[r.NODE]||t.picking){var s=r.data.contexts[r.WEBGL];t.screen?(s.clearColor(0,0,0,0),s.enable(s.BLEND),s.blendFunc(s.ONE,s.ONE_MINUS_SRC_ALPHA)):s.disable(s.BLEND),s.clear(s.COLOR_BUFFER_BIT|s.DEPTH_BUFFER_BIT),s.viewport(0,0,s.canvas.width,s.canvas.height);var o=um(r),l=r.getCachedZSortedEles();if(i=l.length,n.startFrame(o,t),t.screen){for(var u=0;u<l.nondrag.length;u++)ho(r,u,l.nondrag[u]);for(var v=0;v<l.drag.length;v++)ho(r,v,l.drag[v])}else if(t.picking)for(var f=0;f<l.length;f++)ho(r,f,l[f]);n.endFrame(),t.screen&&r.webglDebugShowAtlases&&(vm(r),fm(r)),r.data.canvasNeedsRedraw[r.NODE]=!1,r.data.canvasNeedsRedraw[r.DRAG]=!1}if(r.webglDebug){var c=performance.now(),h=!1,d=Math.ceil(c-a),y=n.getDebugInfo(),g=["".concat(i," elements"),"".concat(y.totalInstances," instances"),"".concat(y.batchCount," batches"),"".concat(y.totalAtlases," atlases"),"".concat(y.wrappedCount," wrapped textures"),"".concat(y.simpleCount," simple shapes")].join(", ");if(h)console.log("WebGL (".concat(t.name,") - time ").concat(d,"ms, ").concat(g));else{console.log("WebGL (".concat(t.name,") - frame time ").concat(d,"ms")),console.log("Totals:"),console.log("  ".concat(g)),console.log("Texture Atlases Used:");var p=y.atlasInfo,m=Er(p),b;try{for(m.s();!(b=m.n()).done;){var w=b.value;console.log("  ".concat(w.type,": ").concat(w.keyCount," keys, ").concat(w.atlasCount," atlases"))}}catch(E){m.e(E)}finally{m.f()}console.log("")}}r.data.gc&&(console.log("Garbage Collect!"),r.data.gc=!1,n.gc())}var yt={};yt.drawPolygonPath=function(r,e,t,a,n,i){var s=a/2,o=n/2;r.beginPath&&r.beginPath(),r.moveTo(e+s*i[0],t+o*i[1]);for(var l=1;l<i.length/2;l++)r.lineTo(e+s*i[l*2],t+o*i[l*2+1]);r.closePath()},yt.drawRoundPolygonPath=function(r,e,t,a,n,i,s){s.forEach(function(o){return Uv(r,o)}),r.closePath()},yt.drawRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?ot(a,n):Math.min(i,o,s);r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.arcTo(e+s,t-o,e+s,t,l),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.arcTo(e-s,t-o,e,t-o,l),r.lineTo(e,t-o),r.closePath()},yt.drawBottomRoundRectanglePath=function(r,e,t,a,n,i){var s=a/2,o=n/2,l=i==="auto"?ot(a,n):i;r.beginPath&&r.beginPath(),r.moveTo(e,t-o),r.lineTo(e+s,t-o),r.lineTo(e+s,t),r.arcTo(e+s,t+o,e,t+o,l),r.arcTo(e-s,t+o,e-s,t,l),r.lineTo(e-s,t-o),r.lineTo(e,t-o),r.closePath()},yt.drawCutRectanglePath=function(r,e,t,a,n,i,s){var o=a/2,l=n/2,u=s==="auto"?Di():s;r.beginPath&&r.beginPath(),r.moveTo(e-o+u,t-l),r.lineTo(e+o-u,t-l),r.lineTo(e+o,t-l+u),r.lineTo(e+o,t+l-u),r.lineTo(e+o-u,t+l),r.lineTo(e-o+u,t+l),r.lineTo(e-o,t+l-u),r.lineTo(e-o,t-l+u),r.closePath()},yt.drawBarrelPath=function(r,e,t,a,n){var i=a/2,s=n/2,o=e-i,l=e+i,u=t-s,v=t+s,f=Bi(a,n),c=f.widthOffset,h=f.heightOffset,d=f.ctrlPtOffsetPct*c;r.beginPath&&r.beginPath(),r.moveTo(o,u+h),r.lineTo(o,v-h),r.quadraticCurveTo(o+d,v,o+c,v),r.lineTo(l-c,v),r.quadraticCurveTo(l-d,v,l,v-h),r.lineTo(l,u+h),r.quadraticCurveTo(l-d,u,l-c,u),r.lineTo(o+c,u),r.quadraticCurveTo(o+d,u,o,u+h),r.closePath()};for(var Pf=Math.sin(0),Af=Math.cos(0),go={},po={},Rf=Math.PI/40,ia=0*Math.PI;ia<2*Math.PI;ia+=Rf)go[ia]=Math.sin(ia),po[ia]=Math.cos(ia);yt.drawEllipsePath=function(r,e,t,a,n){if(r.beginPath&&r.beginPath(),r.ellipse)r.ellipse(e,t,a/2,n/2,0,0,2*Math.PI);else for(var i,s,o=a/2,l=n/2,u=0*Math.PI;u<2*Math.PI;u+=Rf)i=e-o*go[u]*Pf+o*po[u]*Af,s=t+l*po[u]*Pf+l*go[u]*Af,u===0?r.moveTo(i,s):r.lineTo(i,s);r.closePath()};var _a={};_a.createBuffer=function(r,e){var t=document.createElement("canvas");return t.width=r,t.height=e,[t,t.getContext("2d")]},_a.bufferCanvasImage=function(r){var e=this.cy,t=e.mutableElements(),a=t.boundingBox(),n=this.findContainerClientCoords(),i=r.full?Math.ceil(a.w):n[2],s=r.full?Math.ceil(a.h):n[3],o=te(r.maxWidth)||te(r.maxHeight),l=this.getPixelRatio(),u=1;if(r.scale!==void 0)i*=r.scale,s*=r.scale,u=r.scale;else if(o){var v=1/0,f=1/0;te(r.maxWidth)&&(v=u*r.maxWidth/i),te(r.maxHeight)&&(f=u*r.maxHeight/s),u=Math.min(v,f),i*=u,s*=u}o||(i*=l,s*=l,u*=l);var c=document.createElement("canvas");c.width=i,c.height=s,c.style.width=i+"px",c.style.height=s+"px";var h=c.getContext("2d");if(i>0&&s>0){h.clearRect(0,0,i,s),h.globalCompositeOperation="source-over";var d=this.getCachedZSortedEles();if(r.full)h.translate(-a.x1*u,-a.y1*u),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(a.x1*u,a.y1*u);else{var y=e.pan(),g={x:y.x*u,y:y.y*u};u*=e.zoom(),h.translate(g.x,g.y),h.scale(u,u),this.drawElements(h,d),h.scale(1/u,1/u),h.translate(-g.x,-g.y)}r.bg&&(h.globalCompositeOperation="destination-over",h.fillStyle=r.bg,h.rect(0,0,i,s),h.fill())}return c};function hm(r,e){for(var t=atob(r),a=new ArrayBuffer(t.length),n=new Uint8Array(a),i=0;i<t.length;i++)n[i]=t.charCodeAt(i);return new Blob([a],{type:e})}function Mf(r){var e=r.indexOf(",");return r.substr(e+1)}function Lf(r,e,t){var a=function(){return e.toDataURL(t,r.quality)};switch(r.output){case"blob-promise":return new Wt(function(n,i){try{e.toBlob(function(s){s!=null?n(s):i(new Error("`canvas.toBlob()` sent a null value in its callback"))},t,r.quality)}catch(s){i(s)}});case"blob":return hm(Mf(a()),t);case"base64":return Mf(a());case"base64uri":default:return a()}}_a.png=function(r){return Lf(r,this.bufferCanvasImage(r),"image/png")},_a.jpg=function(r){return Lf(r,this.bufferCanvasImage(r),"image/jpeg")};var If={};If.nodeShapeImpl=function(r,e,t,a,n,i,s,o){switch(r){case"ellipse":return this.drawEllipsePath(e,t,a,n,i);case"polygon":return this.drawPolygonPath(e,t,a,n,i,s);case"round-polygon":return this.drawRoundPolygonPath(e,t,a,n,i,s,o);case"roundrectangle":case"round-rectangle":return this.drawRoundRectanglePath(e,t,a,n,i,o);case"cutrectangle":case"cut-rectangle":return this.drawCutRectanglePath(e,t,a,n,i,s,o);case"bottomroundrectangle":case"bottom-round-rectangle":return this.drawBottomRoundRectanglePath(e,t,a,n,i,o);case"barrel":return this.drawBarrelPath(e,t,a,n,i)}};var gm=Of,Se=Of.prototype;Se.CANVAS_LAYERS=3,Se.SELECT_BOX=0,Se.DRAG=1,Se.NODE=2,Se.WEBGL=3,Se.CANVAS_TYPES=["2d","2d","2d","webgl2"],Se.BUFFER_COUNT=3,Se.TEXTURE_BUFFER=0,Se.MOTIONBLUR_BUFFER_NODE=1,Se.MOTIONBLUR_BUFFER_DRAG=2;function Of(r){var e=this,t=e.cy.window(),a=t.document;r.webgl&&(Se.CANVAS_LAYERS=e.CANVAS_LAYERS=4,console.log("webgl rendering enabled")),e.data={canvases:new Array(Se.CANVAS_LAYERS),contexts:new Array(Se.CANVAS_LAYERS),canvasNeedsRedraw:new Array(Se.CANVAS_LAYERS),bufferCanvases:new Array(Se.BUFFER_COUNT),bufferContexts:new Array(Se.CANVAS_LAYERS)};var n="-webkit-tap-highlight-color",i="rgba(0,0,0,0)";e.data.canvasContainer=a.createElement("div");var s=e.data.canvasContainer.style;e.data.canvasContainer.style[n]=i,s.position="relative",s.zIndex="0",s.overflow="hidden";var o=r.cy.container();o.appendChild(e.data.canvasContainer),o.style[n]=i;var l={"-webkit-user-select":"none","-moz-user-select":"-moz-none","user-select":"none","-webkit-tap-highlight-color":"rgba(0,0,0,0)","outline-style":"none"};lc()&&(l["-ms-touch-action"]="none",l["touch-action"]="none");for(var u=0;u<Se.CANVAS_LAYERS;u++){var v=e.data.canvases[u]=a.createElement("canvas"),f=Se.CANVAS_TYPES[u];e.data.contexts[u]=v.getContext(f),e.data.contexts[u]||$e("Could not create canvas of type "+f),Object.keys(l).forEach(function(J){v.style[J]=l[J]}),v.style.position="absolute",v.setAttribute("data-id","layer"+u),v.style.zIndex=String(Se.CANVAS_LAYERS-u),e.data.canvasContainer.appendChild(v),e.data.canvasNeedsRedraw[u]=!1}e.data.topCanvas=e.data.canvases[0],e.data.canvases[Se.NODE].setAttribute("data-id","layer"+Se.NODE+"-node"),e.data.canvases[Se.SELECT_BOX].setAttribute("data-id","layer"+Se.SELECT_BOX+"-selectbox"),e.data.canvases[Se.DRAG].setAttribute("data-id","layer"+Se.DRAG+"-drag"),e.data.canvases[Se.WEBGL]&&e.data.canvases[Se.WEBGL].setAttribute("data-id","layer"+Se.WEBGL+"-webgl");for(var u=0;u<Se.BUFFER_COUNT;u++)e.data.bufferCanvases[u]=a.createElement("canvas"),e.data.bufferContexts[u]=e.data.bufferCanvases[u].getContext("2d"),e.data.bufferCanvases[u].style.position="absolute",e.data.bufferCanvases[u].setAttribute("data-id","buffer"+u),e.data.bufferCanvases[u].style.zIndex=String(-u-1),e.data.bufferCanvases[u].style.visibility="hidden";e.pathsEnabled=!0;var c=mr(),h=function(z){return{x:(z.x1+z.x2)/2,y:(z.y1+z.y2)/2}},d=function(z){return{x:-z.w/2,y:-z.h/2}},y=function(z){var q=z[0]._private,H=q.oldBackgroundTimestamp===q.backgroundTimestamp;return!H},g=function(z){return z[0]._private.nodeKey},p=function(z){return z[0]._private.labelStyleKey},m=function(z){return z[0]._private.sourceLabelStyleKey},b=function(z){return z[0]._private.targetLabelStyleKey},w=function(z,q,H,Y,ae){return e.drawElement(z,q,H,!1,!1,ae)},E=function(z,q,H,Y,ae){return e.drawElementText(z,q,H,Y,"main",ae)},C=function(z,q,H,Y,ae){return e.drawElementText(z,q,H,Y,"source",ae)},x=function(z,q,H,Y,ae){return e.drawElementText(z,q,H,Y,"target",ae)},T=function(z){return z.boundingBox(),z[0]._private.bodyBounds},k=function(z){return z.boundingBox(),z[0]._private.labelBounds.main||c},D=function(z){return z.boundingBox(),z[0]._private.labelBounds.source||c},B=function(z){return z.boundingBox(),z[0]._private.labelBounds.target||c},P=function(z,q){return q},A=function(z){return h(T(z))},R=function(z,q,H){var Y=z?z+"-":"";return{x:q.x+H.pstyle(Y+"text-margin-x").pfValue,y:q.y+H.pstyle(Y+"text-margin-y").pfValue}},L=function(z,q,H){var Y=z[0]._private.rscratch;return{x:Y[q],y:Y[H]}},I=function(z){return R("",L(z,"labelX","labelY"),z)},M=function(z){return R("source",L(z,"sourceLabelX","sourceLabelY"),z)},O=function(z){return R("target",L(z,"targetLabelX","targetLabelY"),z)},V=function(z){return d(T(z))},G=function(z){return d(D(z))},N=function(z){return d(B(z))},F=function(z){var q=k(z),H=d(k(z));if(z.isNode()){switch(z.pstyle("text-halign").value){case"left":H.x=-q.w-(q.leftPad||0);break;case"right":H.x=-(q.rightPad||0);break}switch(z.pstyle("text-valign").value){case"top":H.y=-q.h-(q.topPad||0);break;case"bottom":H.y=-(q.botPad||0);break}}return H},U=e.data.eleTxrCache=new Oa(e,{getKey:g,doesEleInvalidateKey:y,drawElement:w,getBoundingBox:T,getRotationPoint:A,getRotationOffset:V,allowEdgeTxrCaching:!1,allowParentTxrCaching:!1}),Q=e.data.lblTxrCache=new Oa(e,{getKey:p,drawElement:E,getBoundingBox:k,getRotationPoint:I,getRotationOffset:F,isVisible:P}),K=e.data.slbTxrCache=new Oa(e,{getKey:m,drawElement:C,getBoundingBox:D,getRotationPoint:M,getRotationOffset:G,isVisible:P}),j=e.data.tlbTxrCache=new Oa(e,{getKey:b,drawElement:x,getBoundingBox:B,getRotationPoint:O,getRotationOffset:N,isVisible:P}),re=e.data.lyrTxrCache=new lf(e);e.onUpdateEleCalcs(function(z,q){U.invalidateElements(q),Q.invalidateElements(q),K.invalidateElements(q),j.invalidateElements(q),re.invalidateElements(q);for(var H=0;H<q.length;H++){var Y=q[H]._private;Y.oldBackgroundTimestamp=Y.backgroundTimestamp}});var ne=function(z){for(var q=0;q<z.length;q++)re.enqueueElementRefinement(z[q].ele)};U.onDequeue(ne),Q.onDequeue(ne),K.onDequeue(ne),j.onDequeue(ne),r.webgl&&e.initWebgl(r,{getStyleKey:g,getLabelKey:p,getSourceLabelKey:m,getTargetLabelKey:b,drawElement:w,drawLabel:E,drawSourceLabel:C,drawTargetLabel:x,getElementBox:T,getLabelBox:k,getSourceLabelBox:D,getTargetLabelBox:B,getElementRotationPoint:A,getElementRotationOffset:V,getLabelRotationPoint:I,getSourceLabelRotationPoint:M,getTargetLabelRotationPoint:O,getLabelRotationOffset:F,getSourceLabelRotationOffset:G,getTargetLabelRotationOffset:N})}Se.redrawHint=function(r,e){var t=this;switch(r){case"eles":t.data.canvasNeedsRedraw[Se.NODE]=e;break;case"drag":t.data.canvasNeedsRedraw[Se.DRAG]=e;break;case"select":t.data.canvasNeedsRedraw[Se.SELECT_BOX]=e;break;case"gc":t.data.gc=!0;break}};var pm=typeof Path2D!="undefined";Se.path2dEnabled=function(r){if(r===void 0)return this.pathsEnabled;this.pathsEnabled=!!r},Se.usePaths=function(){return pm&&this.pathsEnabled},Se.setImgSmoothing=function(r,e){r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled=e:(r.webkitImageSmoothingEnabled=e,r.mozImageSmoothingEnabled=e,r.msImageSmoothingEnabled=e)},Se.getImgSmoothing=function(r){return r.imageSmoothingEnabled!=null?r.imageSmoothingEnabled:r.webkitImageSmoothingEnabled||r.mozImageSmoothingEnabled||r.msImageSmoothingEnabled},Se.makeOffscreenCanvas=function(r,e){var t;if((typeof OffscreenCanvas=="undefined"?"undefined":je(OffscreenCanvas))!=="undefined")t=new OffscreenCanvas(r,e);else{var a=this.cy.window(),n=a.document;t=n.createElement("canvas"),t.width=r,t.height=e}return t},[ff,Gr,Zr,io,At,pt,wr,Sf,yt,_a,If].forEach(function(r){me(Se,r)});var ym=[{name:"null",impl:zv},{name:"base",impl:tf},{name:"canvas",impl:gm}],mm=[{type:"layout",extensions:Hp},{type:"renderer",extensions:ym}],Nf={},zf={};function Ff(r,e,t){var a=t,n=function(T){Oe("Can not register `"+e+"` for `"+r+"` since `"+T+"` already exists in the prototype and can not be overridden")};if(r==="core"){if(Ra.prototype[e])return n(e);Ra.prototype[e]=t}else if(r==="collection"){if(fr.prototype[e])return n(e);fr.prototype[e]=t}else if(r==="layout"){for(var i=function(T){this.options=T,t.call(this,T),Me(this._private)||(this._private={}),this._private.cy=T.cy,this._private.listeners=[],this.createEmitter()},s=i.prototype=Object.create(t.prototype),o=[],l=0;l<o.length;l++){var u=o[l];s[u]=s[u]||function(){return this}}s.start&&!s.run?s.run=function(){return this.start(),this}:!s.start&&s.run&&(s.start=function(){return this.run(),this});var v=t.prototype.stop;s.stop=function(){var x=this.options;if(x&&x.animate){var T=this.animations;if(T)for(var k=0;k<T.length;k++)T[k].stop()}return v?v.call(this):this.emit("layoutstop"),this},s.destroy||(s.destroy=function(){return this}),s.cy=function(){return this._private.cy};var f=function(T){return T._private.cy},c={addEventFields:function(T,k){k.layout=T,k.cy=f(T),k.target=T},bubble:function(){return!0},parent:function(T){return f(T)}};me(s,{createEmitter:function(){return this._private.emitter=new xn(c,this),this},emitter:function(){return this._private.emitter},on:function(T,k){return this.emitter().on(T,k),this},one:function(T,k){return this.emitter().one(T,k),this},once:function(T,k){return this.emitter().one(T,k),this},removeListener:function(T,k){return this.emitter().removeListener(T,k),this},removeAllListeners:function(){return this.emitter().removeAllListeners(),this},emit:function(T,k){return this.emitter().emit(T,k),this}}),Ne.eventAliasesOn(s),a=i}else if(r==="renderer"&&e!=="null"&&e!=="base"){var h=Vf("renderer","base"),d=h.prototype,y=t,g=t.prototype,p=function(){h.apply(this,arguments),y.apply(this,arguments)},m=p.prototype;for(var b in d){var w=d[b],E=g[b]!=null;if(E)return n(b);m[b]=w}for(var C in g)m[C]=g[C];d.clientFunctions.forEach(function(x){m[x]=m[x]||function(){$e("Renderer does not implement `renderer."+x+"()` on its prototype")}}),a=p}else if(r==="__proto__"||r==="constructor"||r==="prototype")return $e(r+" is an illegal type to be registered, possibly lead to prototype pollutions");return Ao({map:Nf,keys:[r,e],value:a})}function Vf(r,e){return Ro({map:Nf,keys:[r,e]})}function bm(r,e,t,a,n){return Ao({map:zf,keys:[r,e,t,a],value:n})}function wm(r,e,t,a){return Ro({map:zf,keys:[r,e,t,a]})}var yo=function(){if(arguments.length===2)return Vf.apply(null,arguments);if(arguments.length===3)return Ff.apply(null,arguments);if(arguments.length===4)return wm.apply(null,arguments);if(arguments.length===5)return bm.apply(null,arguments);$e("Invalid extension access syntax")};Ra.prototype.extension=yo,mm.forEach(function(r){r.extensions.forEach(function(e){Ff(r.type,e.name,e.impl)})});var Kn=function(){if(!(this instanceof Kn))return new Kn;this.length=0},Mt=Kn.prototype;Mt.instanceString=function(){return"stylesheet"},Mt.selector=function(r){var e=this.length++;return this[e]={selector:r,properties:[]},this},Mt.css=function(r,e){var t=this.length-1;if(he(r))this[t].properties.push({name:r,value:e});else if(Me(r))for(var a=r,n=Object.keys(a),i=0;i<n.length;i++){var s=n[i],o=a[s];if(o!=null){var l=or.properties[s]||or.properties[Ka(s)];if(l!=null){var u=l.name,v=o;this[t].properties.push({name:u,value:v})}}}return this},Mt.style=Mt.css,Mt.generateStyle=function(r){var e=new or(r);return this.appendToStyle(e)},Mt.appendToStyle=function(r){for(var e=0;e<this.length;e++){var t=this[e],a=t.selector,n=t.properties;r.selector(a);for(var i=0;i<n.length;i++){var s=n[i];r.css(s.name,s.value)}}return r};var xm="3.33.0",Lt=function(e){if(e===void 0&&(e={}),Me(e))return new Ra(e);if(he(e))return yo.apply(yo,arguments)};Lt.use=function(r){var e=Array.prototype.slice.call(arguments,1);return e.unshift(Lt),r.apply(null,e),this},Lt.warnings=function(r){return ru(r)},Lt.version=xm,Lt.stylesheet=Lt.Stylesheet=Kn}}]);
