"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3968],{53968:function(pe,ue,m){m.d(ue,{Z:function(){return he}});var de=m(73193),Y=m.n(de),oe=m(90819),Q=m.n(oe),ce=m(76711),d=m.n(ce),le=m(89933),ee=m.n(le),fe=m(45332),O=m.n(fe),re=m(7477),h=m(44194),n=m(57605),ne=m(5525),G=m(88054),b=m(31549),ve=function(T){var F=T.modelId,C=T.toolbarSolt,V=T.selectedTransferKeys,y=T.metricList,I=T.dimensionList,K=T.onSubmit,j=(0,h.useState)([]),B=O()(j,2),U=B[0],H=B[1],J=(0,h.useState)([]),Z=O()(J,2),W=Z[0],k=Z[1],L=function(l,f){var A=l.id,t=(0,G.pV)(f,A);return Y()(Y()({},l),{},{transType:f,key:t})};return(0,h.useEffect)(function(){if(!(!I||!y)){var p=I.reduce(function(t,i){return t.push(L(i,n.Kr.DIMENSION)),t},[]),l=W.filter(function(t){return t.typeEnum===n.Kr.DIMENSION}).reduce(function(t,i){var S=p.find(function(N){return N.id===i.id});return S||t.push(L(i,n.Kr.DIMENSION)),t},[]),f=y.reduce(function(t,i){return t.push(L(i,n.Kr.METRIC)),t},[]),A=W.filter(function(t){return t.typeEnum===n.Kr.METRIC}).reduce(function(t,i){var S=f.find(function(N){return N.id===i.id});return S||t.push(L(i,n.Kr.METRIC)),t},[]);H([].concat(d()(p),d()(f),d()(l),d()(A)))}},[I,y]),(0,b.jsx)(ne.Z,{titles:[(0,b.jsx)(b.Fragment,{children:C}),"\u5DF2\u52A0\u5165\u7EF4\u5EA6/\u6307\u6807"],listStyle:{width:520,height:600},targetList:V,sourceList:U,onChange:function(l){var f=[],A=[],t=Array.isArray(I)?I.reduce(function(o,g){return l.includes((0,G.pV)(n.Kr.DIMENSION,g.id))?o.push(g):f.push(g.id),o},[]):[],i=Array.isArray(y)?y.reduce(function(o,g){return l.includes((0,G.pV)(n.Kr.METRIC,g.id))?o.push(g):A.push(g.id),o},[]):[];k([].concat(d()(t),d()(i)));var S=U.filter(function(o){var g=o.typeEnum,R=o.id;return!(g===n.Kr.DIMENSION&&F!==o.modelId&&f.includes(R)||g===n.Kr.METRIC&&F!==o.modelId&&A.includes(R))});H(d()(S));var N=[].concat(d()(t),d()(i)).reduce(function(o,g){var R=g.modelId,X=g.id,x=g.typeEnum;return o[R]?(x===n.Kr.DIMENSION&&o[R].dimensions.push(X),x===n.Kr.METRIC&&o[R].metrics.push(X)):o[R]={id:R,metrics:x===n.Kr.METRIC?[X]:[],dimensions:x===n.Kr.DIMENSION?[X]:[]},o},{});K==null||K(N,l)}})},ye=ve,me=function(T){var F=T.modelId,C=T.toolbarSolt,V=T.selectedTransferKeys,y=T.tagList,I=T.onSubmit,K=(0,h.useState)([]),j=O()(K,2),B=j[0],U=j[1],H=(0,h.useState)([]),J=O()(H,2),Z=J[0],W=J[1],k=function(p,l){var f=p.id,A=(0,G.pV)(l,f);return Y()(Y()({},p),{},{transType:l,key:A})};return(0,h.useEffect)(function(){if(y){var L=y.reduce(function(l,f){return l.push(k(f,n.Kr.TAG)),l},[]),p=Z.reduce(function(l,f){var A=L.find(function(t){return t.id===f.id});return A||l.push(k(f,n.Kr.TAG)),l},[]);U([].concat(d()(L),d()(p)))}},[y]),(0,b.jsx)(ne.Z,{titles:[(0,b.jsx)(b.Fragment,{children:C}),"\u5DF2\u52A0\u5165\u6807\u7B7E"],listStyle:{width:520,height:600},targetList:V,sourceList:B,onChange:function(p){var l=[],f=Array.isArray(y)?y.reduce(function(i,S){return p.includes((0,G.pV)(n.Kr.TAG,S.id))?i.push(S):l.push(S.id),i},[]):[];W(d()(f));var A=B.filter(function(i){var S=i.id;return!(F!==i.modelId&&l.includes(S))});U(d()(A));var t=d()(f).reduce(function(i,S){var N=S.modelId,o=S.id;return i[N]?i[N].tagIds.push(o):i[N]={id:N,tagIds:[o]},i},{});I==null||I(t,p)}})},Ie=me,te=m(9113),$=m(94202),Se=(0,h.forwardRef)(function(P,T){var F=P.queryType,C=F===void 0?n.Kr.METRIC:F,V=P.viewItem,y=P.modelItem,I=P.dimensionList,K=P.metricList,j=P.tagList,B=P.toolbarSolt,U=(0,h.useState)([]),H=O()(U,2),J=H[0],Z=H[1],W=(0,h.useState)({}),k=O()(W,2),L=k[0],p=k[1],l=(0,h.useState)(),f=O()(l,2),A=f[0],t=f[1],i=(0,h.useState)(),S=O()(i,2),N=S[0],o=S[1],g=(0,h.useState)(),R=O()(g,2),X=R[0],x=R[1];(0,h.useImperativeHandle)(T,function(){return{getViewModelConfigs:function(){return L}}});var ae=function(){var v=ee()(Q()().mark(function u(s){var r,c,e,M,D;return Q()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if((0,$.gP)(s)){a.next=3;break}return x(j),a.abrupt("return");case 3:return a.next=5,(0,te.kV)({ids:s});case 5:r=a.sent,c=r.code,e=r.data,M=r.msg,c===200&&Array.isArray(e==null?void 0:e.list)?(D=e==null?void 0:e.list.reduce(function(E,w){var q=Array.isArray(j)?j.find(function(_){return _.id===w.id}):[];return q?E:[w].concat(d()(E))},j),x(D)):re.ZP.error(M);case 10:case"end":return a.stop()}},u)}));return function(s){return v.apply(this,arguments)}}(),ie=function(){var v=ee()(Q()().mark(function u(s){var r,c,e,M,D;return Q()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if((0,$.gP)(s)){a.next=3;break}return t(I),a.abrupt("return");case 3:return a.next=5,(0,te.dt)({ids:s});case 5:r=a.sent,c=r.code,e=r.data,M=r.msg,c===200&&Array.isArray(e==null?void 0:e.list)?(D=e==null?void 0:e.list.reduce(function(E,w){var q=Array.isArray(I)?I.find(function(_){return _.id===w.id}):[];return q?E:[w].concat(d()(E))},I),t(D)):re.ZP.error(M);case 10:case"end":return a.stop()}},u)}));return function(s){return v.apply(this,arguments)}}(),se=function(){var v=ee()(Q()().mark(function u(s){var r,c,e,M,D;return Q()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if((0,$.gP)(s)){a.next=3;break}return o(K),a.abrupt("return");case 3:return a.next=5,(0,te.VJ)({ids:s});case 5:r=a.sent,c=r.code,e=r.data,M=r.msg,c===200&&Array.isArray(e==null?void 0:e.list)?(D=e.list.reduce(function(E,w){var q=Array.isArray(K)?K.find(function(_){return _.id===w.id}):[];return q?E:[w].concat(d()(E))},K),o(D)):re.ZP.error(M);case 10:case"end":return a.stop()}},u)}));return function(s){return v.apply(this,arguments)}}();return(0,h.useEffect)(function(){var v,u=V==null||(v=V.dataSetDetail)===null||v===void 0?void 0:v.dataSetModelConfigs;if(Array.isArray(u)){var s=[],r=[],c={};u.forEach(function(e){var M=e.id,D=e.metrics,z=e.dimensions,a=e.tagIds;s.push(M),c[M]=Y()({},e),C===n.Kr.METRIC&&(Array.isArray(D)&&D.forEach(function(E){r.push((0,G.pV)(n.Kr.METRIC,E))}),Array.isArray(z)&&z.forEach(function(E){r.push((0,G.pV)(n.Kr.DIMENSION,E))})),C===n.Kr.TAG&&Array.isArray(a)&&a.forEach(function(E){r.push((0,G.pV)(n.Kr.TAG,E))})}),Z(r),p(c)}},[C,V]),(0,h.useEffect)(function(){if(C===n.Kr.METRIC&&!(!I||!K)){var v=Object.values(L);if((0,$.gP)(v)){var u=[],s=[];v.forEach(function(r){var c=r.metrics,e=r.dimensions;u.push.apply(u,d()(c)),s.push.apply(s,d()(e))}),ie(s),se(u)}else t(I),o(K)}},[C,y,I,K]),(0,h.useEffect)(function(){var v;if(C===n.Kr.TAG&&j){var u=(0,$.gP)(Object.values(L))?Object.values(L):V==null||(v=V.dataSetDetail)===null||v===void 0?void 0:v.dataSetModelConfigs;if((0,$.gP)(u)){var s=[];u.forEach(function(r){var c=r.tagIds;s.push.apply(s,d()(c))}),ae(s)}else x(j)}},[C,y,j]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{style:{display:C===n.Kr.TAG?"none":"block"},children:(0,b.jsx)(ye,{toolbarSolt:B,modelId:y==null?void 0:y.id,dimensionList:A,metricList:N,selectedTransferKeys:J,onSubmit:function(u,s){var r=Object.values(u);if((0,$.gP)(r)){var c=[],e=[];r.forEach(function(M){var D=M.metrics,z=M.dimensions;c.push.apply(c,d()(D)),e.push.apply(e,d()(z))}),ie(e),se(c)}p(u),Z(s)},onCancel:function(){}})}),(0,b.jsx)("div",{style:{display:C!==n.Kr.TAG?"none":"block"},children:(0,b.jsx)(Ie,{toolbarSolt:B,modelId:y==null?void 0:y.id,tagList:X,selectedTransferKeys:J,onSubmit:function(u,s){var r=Object.values(u);if((0,$.gP)(r)){var c=[];r.forEach(function(e){var M=e.tagIds;c.push.apply(c,d()(M))}),ae(c)}p(u),Z(s)},onCancel:function(){}})})]})}),he=Se}}]);
