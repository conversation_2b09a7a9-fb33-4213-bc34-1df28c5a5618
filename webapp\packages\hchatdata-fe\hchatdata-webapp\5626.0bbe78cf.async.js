"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5626],{95626:function(Te,re,G){var q,Z,J,tt,et,it,st,at,nt,rt;G.d(re,{diagram:function(){return _e}});var oe=G(70919),It=G(50854),he=G(44133),a=G(29134),K=G(69471),yt=function(){var s=(0,a.eW)(function(N,o,c,g){for(c=c||{},g=N.length;g--;c[N[g]]=o);return c},"o"),t=[1,10,12,14,16,18,19,21,23],e=[2,6],i=[1,3],n=[1,5],h=[1,6],d=[1,7],b=[1,5,10,12,14,16,18,19,21,23,34,35,36],A=[1,25],<PERSON>=[1,26],M=[1,28],T=[1,29],L=[1,30],O=[1,31],z=[1,32],D=[1,33],B=[1,34],F=[1,35],X=[1,36],p=[1,37],W=[1,43],l=[1,42],V=[1,47],P=[1,50],k=[1,10,12,14,16,18,19,21,23,34,35,36],H=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36],u=[1,10,12,14,16,18,19,21,23,24,26,27,28,34,35,36,41,42,43,44,45,46,47,48,49,50],C=[1,64],R={trace:(0,a.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,eol:4,XYCHART:5,chartConfig:6,document:7,CHART_ORIENTATION:8,statement:9,title:10,text:11,X_AXIS:12,parseXAxis:13,Y_AXIS:14,parseYAxis:15,LINE:16,plotData:17,BAR:18,acc_title:19,acc_title_value:20,acc_descr:21,acc_descr_value:22,acc_descr_multiline_value:23,SQUARE_BRACES_START:24,commaSeparatedNumbers:25,SQUARE_BRACES_END:26,NUMBER_WITH_DECIMAL:27,COMMA:28,xAxisData:29,bandData:30,ARROW_DELIMITER:31,commaSeparatedTexts:32,yAxisData:33,NEWLINE:34,SEMI:35,EOF:36,alphaNum:37,STR:38,MD_STR:39,alphaNumToken:40,AMP:41,NUM:42,ALPHA:43,PLUS:44,EQUALS:45,MULT:46,DOT:47,BRKT:48,MINUS:49,UNDERSCORE:50,$accept:0,$end:1},terminals_:{2:"error",5:"XYCHART",8:"CHART_ORIENTATION",10:"title",12:"X_AXIS",14:"Y_AXIS",16:"LINE",18:"BAR",19:"acc_title",20:"acc_title_value",21:"acc_descr",22:"acc_descr_value",23:"acc_descr_multiline_value",24:"SQUARE_BRACES_START",26:"SQUARE_BRACES_END",27:"NUMBER_WITH_DECIMAL",28:"COMMA",31:"ARROW_DELIMITER",34:"NEWLINE",35:"SEMI",36:"EOF",38:"STR",39:"MD_STR",41:"AMP",42:"NUM",43:"ALPHA",44:"PLUS",45:"EQUALS",46:"MULT",47:"DOT",48:"BRKT",49:"MINUS",50:"UNDERSCORE"},productions_:[0,[3,2],[3,3],[3,2],[3,1],[6,1],[7,0],[7,2],[9,2],[9,2],[9,2],[9,2],[9,2],[9,3],[9,2],[9,3],[9,2],[9,2],[9,1],[17,3],[25,3],[25,1],[13,1],[13,2],[13,1],[29,1],[29,3],[30,3],[32,3],[32,1],[15,1],[15,2],[15,1],[33,3],[4,1],[4,1],[4,1],[11,1],[11,1],[11,1],[37,1],[37,2],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1],[40,1]],performAction:(0,a.eW)(function(o,c,g,x,m,r,ut){var f=r.length-1;switch(m){case 5:x.setOrientation(r[f]);break;case 9:x.setDiagramTitle(r[f].text.trim());break;case 12:x.setLineData({text:"",type:"text"},r[f]);break;case 13:x.setLineData(r[f-1],r[f]);break;case 14:x.setBarData({text:"",type:"text"},r[f]);break;case 15:x.setBarData(r[f-1],r[f]);break;case 16:this.$=r[f].trim(),x.setAccTitle(this.$);break;case 17:case 18:this.$=r[f].trim(),x.setAccDescription(this.$);break;case 19:this.$=r[f-1];break;case 20:this.$=[Number(r[f-2]),...r[f]];break;case 21:this.$=[Number(r[f])];break;case 22:x.setXAxisTitle(r[f]);break;case 23:x.setXAxisTitle(r[f-1]);break;case 24:x.setXAxisTitle({type:"text",text:""});break;case 25:x.setXAxisBand(r[f]);break;case 26:x.setXAxisRangeData(Number(r[f-2]),Number(r[f]));break;case 27:this.$=r[f-1];break;case 28:this.$=[r[f-2],...r[f]];break;case 29:this.$=[r[f]];break;case 30:x.setYAxisTitle(r[f]);break;case 31:x.setYAxisTitle(r[f-1]);break;case 32:x.setYAxisTitle({type:"text",text:""});break;case 33:x.setYAxisRangeData(Number(r[f-2]),Number(r[f]));break;case 37:this.$={text:r[f],type:"text"};break;case 38:this.$={text:r[f],type:"text"};break;case 39:this.$={text:r[f],type:"markdown"};break;case 40:this.$=r[f];break;case 41:this.$=r[f-1]+""+r[f];break}},"anonymous"),table:[s(t,e,{3:1,4:2,7:4,5:i,34:n,35:h,36:d}),{1:[3]},s(t,e,{4:2,7:4,3:8,5:i,34:n,35:h,36:d}),s(t,e,{4:2,7:4,6:9,3:10,5:i,8:[1,11],34:n,35:h,36:d}),{1:[2,4],9:12,10:[1,13],12:[1,14],14:[1,15],16:[1,16],18:[1,17],19:[1,18],21:[1,19],23:[1,20]},s(b,[2,34]),s(b,[2,35]),s(b,[2,36]),{1:[2,1]},s(t,e,{4:2,7:4,3:21,5:i,34:n,35:h,36:d}),{1:[2,3]},s(b,[2,5]),s(t,[2,7],{4:22,34:n,35:h,36:d}),{11:23,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},{11:39,13:38,24:W,27:l,29:40,30:41,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},{11:45,15:44,27:V,33:46,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},{11:49,17:48,24:P,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},{11:52,17:51,24:P,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},{20:[1,53]},{22:[1,54]},s(k,[2,18]),{1:[2,2]},s(k,[2,8]),s(k,[2,9]),s(H,[2,37],{40:55,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p}),s(H,[2,38]),s(H,[2,39]),s(u,[2,40]),s(u,[2,42]),s(u,[2,43]),s(u,[2,44]),s(u,[2,45]),s(u,[2,46]),s(u,[2,47]),s(u,[2,48]),s(u,[2,49]),s(u,[2,50]),s(u,[2,51]),s(k,[2,10]),s(k,[2,22],{30:41,29:56,24:W,27:l}),s(k,[2,24]),s(k,[2,25]),{31:[1,57]},{11:59,32:58,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},s(k,[2,11]),s(k,[2,30],{33:60,27:V}),s(k,[2,32]),{31:[1,61]},s(k,[2,12]),{17:62,24:P},{25:63,27:C},s(k,[2,14]),{17:65,24:P},s(k,[2,16]),s(k,[2,17]),s(u,[2,41]),s(k,[2,23]),{27:[1,66]},{26:[1,67]},{26:[2,29],28:[1,68]},s(k,[2,31]),{27:[1,69]},s(k,[2,13]),{26:[1,70]},{26:[2,21],28:[1,71]},s(k,[2,15]),s(k,[2,26]),s(k,[2,27]),{11:59,32:72,37:24,38:A,39:E,40:27,41:M,42:T,43:L,44:O,45:z,46:D,47:B,48:F,49:X,50:p},s(k,[2,33]),s(k,[2,19]),{25:73,27:C},{26:[2,28]},{26:[2,20]}],defaultActions:{8:[2,1],10:[2,3],21:[2,2],72:[2,28],73:[2,20]},parseError:(0,a.eW)(function(o,c){if(c.recoverable)this.trace(o);else{var g=new Error(o);throw g.hash=c,g}},"parseError"),parse:(0,a.eW)(function(o){var c=this,g=[0],x=[],m=[null],r=[],ut=this.table,f="",dt=0,ee=0,ie=0,we=2,se=1,Ce=r.slice.call(arguments,1),_=Object.create(this.lexer),$={yy:{}};for(var Wt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Wt)&&($.yy[Wt]=this.yy[Wt]);_.setInput(o,$.yy),$.yy.lexer=_,$.yy.parser=this,typeof _.yylloc=="undefined"&&(_.yylloc={});var Pt=_.yylloc;r.push(Pt);var Se=_.options&&_.options.ranges;typeof $.yy.parseError=="function"?this.parseError=$.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Re(v){g.length=g.length-2*v,m.length=m.length-v,r.length=r.length-v}(0,a.eW)(Re,"popStack");function ae(){var v;return v=x.pop()||_.lex()||se,typeof v!="number"&&(v instanceof Array&&(x=v,v=x.pop()),v=c.symbols_[v]||v),v}(0,a.eW)(ae,"lex");for(var S,vt,j,I,De,Et,ot={},ft,Y,ne,pt;;){if(j=g[g.length-1],this.defaultActions[j]?I=this.defaultActions[j]:((S===null||typeof S=="undefined")&&(S=ae()),I=ut[j]&&ut[j][S]),typeof I=="undefined"||!I.length||!I[0]){var Lt="";pt=[];for(ft in ut[j])this.terminals_[ft]&&ft>we&&pt.push("'"+this.terminals_[ft]+"'");_.showPosition?Lt="Parse error on line "+(dt+1)+`:
`+_.showPosition()+`
Expecting `+pt.join(", ")+", got '"+(this.terminals_[S]||S)+"'":Lt="Parse error on line "+(dt+1)+": Unexpected "+(S==se?"end of input":"'"+(this.terminals_[S]||S)+"'"),this.parseError(Lt,{text:_.match,token:this.terminals_[S]||S,line:_.yylineno,loc:Pt,expected:pt})}if(I[0]instanceof Array&&I.length>1)throw new Error("Parse Error: multiple actions possible at state: "+j+", token: "+S);switch(I[0]){case 1:g.push(S),m.push(_.yytext),r.push(_.yylloc),g.push(I[1]),S=null,vt?(S=vt,vt=null):(ee=_.yyleng,f=_.yytext,dt=_.yylineno,Pt=_.yylloc,ie>0&&ie--);break;case 2:if(Y=this.productions_[I[1]][1],ot.$=m[m.length-Y],ot._$={first_line:r[r.length-(Y||1)].first_line,last_line:r[r.length-1].last_line,first_column:r[r.length-(Y||1)].first_column,last_column:r[r.length-1].last_column},Se&&(ot._$.range=[r[r.length-(Y||1)].range[0],r[r.length-1].range[1]]),Et=this.performAction.apply(ot,[f,ee,dt,$.yy,I[1],m,r].concat(Ce)),typeof Et!="undefined")return Et;Y&&(g=g.slice(0,-1*Y*2),m=m.slice(0,-1*Y),r=r.slice(0,-1*Y)),g.push(this.productions_[I[1]][0]),m.push(ot.$),r.push(ot._$),ne=ut[g[g.length-2]][g[g.length-1]],g.push(ne);break;case 3:return!0}}return!0},"parse")},w=function(){var N={EOF:1,parseError:(0,a.eW)(function(c,g){if(this.yy.parser)this.yy.parser.parseError(c,g);else throw new Error(c)},"parseError"),setInput:(0,a.eW)(function(o,c){return this.yy=c||this.yy||{},this._input=o,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,a.eW)(function(){var o=this._input[0];this.yytext+=o,this.yyleng++,this.offset++,this.match+=o,this.matched+=o;var c=o.match(/(?:\r\n?|\n).*/g);return c?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),o},"input"),unput:(0,a.eW)(function(o){var c=o.length,g=o.split(/(?:\r\n?|\n)/g);this._input=o+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-c),this.offset-=c;var x=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var m=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===x.length?this.yylloc.first_column:0)+x[x.length-g.length].length-g[0].length:this.yylloc.first_column-c},this.options.ranges&&(this.yylloc.range=[m[0],m[0]+this.yyleng-c]),this.yyleng=this.yytext.length,this},"unput"),more:(0,a.eW)(function(){return this._more=!0,this},"more"),reject:(0,a.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,a.eW)(function(o){this.unput(this.match.slice(o))},"less"),pastInput:(0,a.eW)(function(){var o=this.matched.substr(0,this.matched.length-this.match.length);return(o.length>20?"...":"")+o.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,a.eW)(function(){var o=this.match;return o.length<20&&(o+=this._input.substr(0,20-o.length)),(o.substr(0,20)+(o.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,a.eW)(function(){var o=this.pastInput(),c=new Array(o.length+1).join("-");return o+this.upcomingInput()+`
`+c+"^"},"showPosition"),test_match:(0,a.eW)(function(o,c){var g,x,m;if(this.options.backtrack_lexer&&(m={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(m.yylloc.range=this.yylloc.range.slice(0))),x=o[0].match(/(?:\r\n?|\n).*/g),x&&(this.yylineno+=x.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:x?x[x.length-1].length-x[x.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+o[0].length},this.yytext+=o[0],this.match+=o[0],this.matches=o,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(o[0].length),this.matched+=o[0],g=this.performAction.call(this,this.yy,this,c,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var r in m)this[r]=m[r];return!1}return!1},"test_match"),next:(0,a.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var o,c,g,x;this._more||(this.yytext="",this.match="");for(var m=this._currentRules(),r=0;r<m.length;r++)if(g=this._input.match(this.rules[m[r]]),g&&(!c||g[0].length>c[0].length)){if(c=g,x=r,this.options.backtrack_lexer){if(o=this.test_match(g,m[r]),o!==!1)return o;if(this._backtrack){c=!1;continue}else return!1}else if(!this.options.flex)break}return c?(o=this.test_match(c,m[x]),o!==!1?o:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,a.eW)(function(){var c=this.next();return c||this.lex()},"lex"),begin:(0,a.eW)(function(c){this.conditionStack.push(c)},"begin"),popState:(0,a.eW)(function(){var c=this.conditionStack.length-1;return c>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,a.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,a.eW)(function(c){return c=this.conditionStack.length-1-Math.abs(c||0),c>=0?this.conditionStack[c]:"INITIAL"},"topState"),pushState:(0,a.eW)(function(c){this.begin(c)},"pushState"),stateStackSize:(0,a.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:(0,a.eW)(function(c,g,x,m){var r=m;switch(x){case 0:break;case 1:break;case 2:return this.popState(),34;break;case 3:return this.popState(),34;break;case 4:return 34;case 5:break;case 6:return 10;case 7:return this.pushState("acc_title"),19;break;case 8:return this.popState(),"acc_title_value";break;case 9:return this.pushState("acc_descr"),21;break;case 10:return this.popState(),"acc_descr_value";break;case 11:this.pushState("acc_descr_multiline");break;case 12:this.popState();break;case 13:return"acc_descr_multiline_value";case 14:return 5;case 15:return 8;case 16:return this.pushState("axis_data"),"X_AXIS";break;case 17:return this.pushState("axis_data"),"Y_AXIS";break;case 18:return this.pushState("axis_band_data"),24;break;case 19:return 31;case 20:return this.pushState("data"),16;break;case 21:return this.pushState("data"),18;break;case 22:return this.pushState("data_inner"),24;break;case 23:return 27;case 24:return this.popState(),26;break;case 25:this.popState();break;case 26:this.pushState("string");break;case 27:this.popState();break;case 28:return"STR";case 29:return 24;case 30:return 26;case 31:return 43;case 32:return"COLON";case 33:return 44;case 34:return 28;case 35:return 45;case 36:return 46;case 37:return 48;case 38:return 50;case 39:return 47;case 40:return 41;case 41:return 49;case 42:return 42;case 43:break;case 44:return 35;case 45:return 36}},"anonymous"),rules:[/^(?:%%(?!\{)[^\n]*)/i,/^(?:[^\}]%%[^\n]*)/i,/^(?:(\r?\n))/i,/^(?:(\r?\n))/i,/^(?:[\n\r]+)/i,/^(?:%%[^\n]*)/i,/^(?:title\b)/i,/^(?:accTitle\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*:\s*)/i,/^(?:(?!\n||)*[^\n]*)/i,/^(?:accDescr\s*\{\s*)/i,/^(?:\{)/i,/^(?:[^\}]*)/i,/^(?:xychart-beta\b)/i,/^(?:(?:vertical|horizontal))/i,/^(?:x-axis\b)/i,/^(?:y-axis\b)/i,/^(?:\[)/i,/^(?:-->)/i,/^(?:line\b)/i,/^(?:bar\b)/i,/^(?:\[)/i,/^(?:[+-]?(?:\d+(?:\.\d+)?|\.\d+))/i,/^(?:\])/i,/^(?:(?:`\)                                    \{ this\.pushState\(md_string\); \}\n<md_string>\(\?:\(\?!`"\)\.\)\+                  \{ return MD_STR; \}\n<md_string>\(\?:`))/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^"]*)/i,/^(?:\[)/i,/^(?:\])/i,/^(?:[A-Za-z]+)/i,/^(?::)/i,/^(?:\+)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\*)/i,/^(?:#)/i,/^(?:[\_])/i,/^(?:\.)/i,/^(?:&)/i,/^(?:-)/i,/^(?:[0-9]+)/i,/^(?:\s+)/i,/^(?:;)/i,/^(?:$)/i],conditions:{data_inner:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,23,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},data:{rules:[0,1,3,4,5,6,7,9,11,14,15,16,17,20,21,22,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_band_data:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,24,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},axis_data:{rules:[0,1,2,4,5,6,7,9,11,14,15,16,17,18,19,20,21,23,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0},acc_descr_multiline:{rules:[12,13],inclusive:!1},acc_descr:{rules:[10],inclusive:!1},acc_title:{rules:[8],inclusive:!1},title:{rules:[],inclusive:!1},md_string:{rules:[],inclusive:!1},string:{rules:[27,28],inclusive:!1},INITIAL:{rules:[0,1,4,5,6,7,9,11,14,15,16,17,20,21,25,26,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45],inclusive:!0}}};return N}();R.lexer=w;function U(){this.yy={}}return(0,a.eW)(U,"Parser"),U.prototype=R,R.Parser=U,new U}();yt.parser=yt;var le=yt;function mt(s){return s.type==="bar"}(0,a.eW)(mt,"isBarPlot");function bt(s){return s.type==="band"}(0,a.eW)(bt,"isBandAxisData");function Q(s){return s.type==="linear"}(0,a.eW)(Q,"isLinearAxisData");var Mt=(q=class{constructor(t){this.parentGroup=t}getMaxDimension(t,e){if(!this.parentGroup)return{width:t.reduce((h,d)=>Math.max(d.length,h),0)*e,height:e};const i={width:0,height:0},n=this.parentGroup.append("g").attr("visibility","hidden").attr("font-size",e);for(const h of t){const d=(0,oe.QA)(n,1,h),b=d?d.width:h.length*e,A=d?d.height:e;i.width=Math.max(i.width,b),i.height=Math.max(i.height,A)}return n.remove(),i}},(0,a.eW)(q,"TextDimensionCalculatorWithFont"),q),Bt=.7,Vt=.2,Ot=(Z=class{constructor(t,e,i,n){this.axisConfig=t,this.title=e,this.textDimensionCalculator=i,this.axisThemeConfig=n,this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left",this.showTitle=!1,this.showLabel=!1,this.showTick=!1,this.showAxisLine=!1,this.outerPadding=0,this.titleTextHeight=0,this.labelTextHeight=0,this.range=[0,10],this.boundingRect={x:0,y:0,width:0,height:0},this.axisPosition="left"}setRange(t){this.range=t,this.axisPosition==="left"||this.axisPosition==="right"?this.boundingRect.height=t[1]-t[0]:this.boundingRect.width=t[1]-t[0],this.recalculateScale()}getRange(){return[this.range[0]+this.outerPadding,this.range[1]-this.outerPadding]}setAxisPosition(t){this.axisPosition=t,this.setRange(this.range)}getTickDistance(){const t=this.getRange();return Math.abs(t[0]-t[1])/this.getTickValues().length}getAxisOuterPadding(){return this.outerPadding}getLabelDimension(){return this.textDimensionCalculator.getMaxDimension(this.getTickValues().map(t=>t.toString()),this.axisConfig.labelFontSize)}recalculateOuterPaddingToDrawBar(){Bt*this.getTickDistance()>this.outerPadding*2&&(this.outerPadding=Math.floor(Bt*this.getTickDistance()/2)),this.recalculateScale()}calculateSpaceIfDrawnHorizontally(t){let e=t.height;if(this.axisConfig.showAxisLine&&e>this.axisConfig.axisLineWidth&&(e-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){const i=this.getLabelDimension(),n=Vt*t.width;this.outerPadding=Math.min(i.width/2,n);const h=i.height+this.axisConfig.labelPadding*2;this.labelTextHeight=i.height,h<=e&&(e-=h,this.showLabel=!0)}if(this.axisConfig.showTick&&e>=this.axisConfig.tickLength&&(this.showTick=!0,e-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){const i=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=i.height+this.axisConfig.titlePadding*2;this.titleTextHeight=i.height,n<=e&&(e-=n,this.showTitle=!0)}this.boundingRect.width=t.width,this.boundingRect.height=t.height-e}calculateSpaceIfDrawnVertical(t){let e=t.width;if(this.axisConfig.showAxisLine&&e>this.axisConfig.axisLineWidth&&(e-=this.axisConfig.axisLineWidth,this.showAxisLine=!0),this.axisConfig.showLabel){const i=this.getLabelDimension(),n=Vt*t.height;this.outerPadding=Math.min(i.height/2,n);const h=i.width+this.axisConfig.labelPadding*2;h<=e&&(e-=h,this.showLabel=!0)}if(this.axisConfig.showTick&&e>=this.axisConfig.tickLength&&(this.showTick=!0,e-=this.axisConfig.tickLength),this.axisConfig.showTitle&&this.title){const i=this.textDimensionCalculator.getMaxDimension([this.title],this.axisConfig.titleFontSize),n=i.height+this.axisConfig.titlePadding*2;this.titleTextHeight=i.height,n<=e&&(e-=n,this.showTitle=!0)}this.boundingRect.width=t.width-e,this.boundingRect.height=t.height}calculateSpace(t){return this.axisPosition==="left"||this.axisPosition==="right"?this.calculateSpaceIfDrawnVertical(t):this.calculateSpaceIfDrawnHorizontally(t),this.recalculateScale(),{width:this.boundingRect.width,height:this.boundingRect.height}}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}getDrawableElementsForLeftAxis(){const t=[];if(this.showAxisLine){const e=this.boundingRect.x+this.boundingRect.width-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["left-axis","axisl-line"],data:[{path:`M ${e},${this.boundingRect.y} L ${e},${this.boundingRect.y+this.boundingRect.height} `,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["left-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.boundingRect.x+this.boundingRect.width-(this.showLabel?this.axisConfig.labelPadding:0)-(this.showTick?this.axisConfig.tickLength:0)-(this.showAxisLine?this.axisConfig.axisLineWidth:0),y:this.getScaleValue(e),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"middle",horizontalPos:"right"}))}),this.showTick){const e=this.boundingRect.x+this.boundingRect.width-(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["left-axis","ticks"],data:this.getTickValues().map(i=>({path:`M ${e},${this.getScaleValue(i)} L ${e-this.axisConfig.tickLength},${this.getScaleValue(i)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["left-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.axisConfig.titlePadding,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:270,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForBottomAxis(){const t=[];if(this.showAxisLine){const e=this.boundingRect.y+this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["bottom-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${e} L ${this.boundingRect.x+this.boundingRect.width},${e}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["bottom-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.getScaleValue(e),y:this.boundingRect.y+this.axisConfig.labelPadding+(this.showTick?this.axisConfig.tickLength:0)+(this.showAxisLine?this.axisConfig.axisLineWidth:0),fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){const e=this.boundingRect.y+(this.showAxisLine?this.axisConfig.axisLineWidth:0);t.push({type:"path",groupTexts:["bottom-axis","ticks"],data:this.getTickValues().map(i=>({path:`M ${this.getScaleValue(i)},${e} L ${this.getScaleValue(i)},${e+this.axisConfig.tickLength}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["bottom-axis","title"],data:[{text:this.title,x:this.range[0]+(this.range[1]-this.range[0])/2,y:this.boundingRect.y+this.boundingRect.height-this.axisConfig.titlePadding-this.titleTextHeight,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElementsForTopAxis(){const t=[];if(this.showAxisLine){const e=this.boundingRect.y+this.boundingRect.height-this.axisConfig.axisLineWidth/2;t.push({type:"path",groupTexts:["top-axis","axis-line"],data:[{path:`M ${this.boundingRect.x},${e} L ${this.boundingRect.x+this.boundingRect.width},${e}`,strokeFill:this.axisThemeConfig.axisLineColor,strokeWidth:this.axisConfig.axisLineWidth}]})}if(this.showLabel&&t.push({type:"text",groupTexts:["top-axis","label"],data:this.getTickValues().map(e=>({text:e.toString(),x:this.getScaleValue(e),y:this.boundingRect.y+(this.showTitle?this.titleTextHeight+this.axisConfig.titlePadding*2:0)+this.axisConfig.labelPadding,fill:this.axisThemeConfig.labelColor,fontSize:this.axisConfig.labelFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}))}),this.showTick){const e=this.boundingRect.y;t.push({type:"path",groupTexts:["top-axis","ticks"],data:this.getTickValues().map(i=>({path:`M ${this.getScaleValue(i)},${e+this.boundingRect.height-(this.showAxisLine?this.axisConfig.axisLineWidth:0)} L ${this.getScaleValue(i)},${e+this.boundingRect.height-this.axisConfig.tickLength-(this.showAxisLine?this.axisConfig.axisLineWidth:0)}`,strokeFill:this.axisThemeConfig.tickColor,strokeWidth:this.axisConfig.tickWidth}))})}return this.showTitle&&t.push({type:"text",groupTexts:["top-axis","title"],data:[{text:this.title,x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.axisConfig.titlePadding,fill:this.axisThemeConfig.titleColor,fontSize:this.axisConfig.titleFontSize,rotation:0,verticalPos:"top",horizontalPos:"center"}]}),t}getDrawableElements(){if(this.axisPosition==="left")return this.getDrawableElementsForLeftAxis();if(this.axisPosition==="right")throw Error("Drawing of right axis is not implemented");return this.axisPosition==="bottom"?this.getDrawableElementsForBottomAxis():this.axisPosition==="top"?this.getDrawableElementsForTopAxis():[]}},(0,a.eW)(Z,"BaseAxis"),Z),ce=(J=class extends Ot{constructor(t,e,i,n,h){super(t,n,h,e),this.categories=i,this.scale=(0,K.tiA)().domain(this.categories).range(this.getRange())}setRange(t){super.setRange(t)}recalculateScale(){this.scale=(0,K.tiA)().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(.5),a.cM.trace("BandAxis axis final categories, range: ",this.categories,this.getRange())}getTickValues(){return this.categories}getScaleValue(t){var e;return(e=this.scale(t))!=null?e:this.getRange()[0]}},(0,a.eW)(J,"BandAxis"),J),ue=(tt=class extends Ot{constructor(t,e,i,n,h){super(t,n,h,e),this.domain=i,this.scale=(0,K.BYU)().domain(this.domain).range(this.getRange())}getTickValues(){return this.scale.ticks()}recalculateScale(){const t=[...this.domain];this.axisPosition==="left"&&t.reverse(),this.scale=(0,K.BYU)().domain(t).range(this.getRange())}getScaleValue(t){return this.scale(t)}},(0,a.eW)(tt,"LinearAxis"),tt);function At(s,t,e,i){const n=new Mt(i);return bt(s)?new ce(t,e,s.categories,s.title,n):new ue(t,e,[s.min,s.max],s.title,n)}(0,a.eW)(At,"getAxis");var ge=(et=class{constructor(t,e,i,n){this.textDimensionCalculator=t,this.chartConfig=e,this.chartData=i,this.chartThemeConfig=n,this.boundingRect={x:0,y:0,width:0,height:0},this.showChartTitle=!1}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){const e=this.textDimensionCalculator.getMaxDimension([this.chartData.title],this.chartConfig.titleFontSize),i=Math.max(e.width,t.width),n=e.height+2*this.chartConfig.titlePadding;return e.width<=i&&e.height<=n&&this.chartConfig.showTitle&&this.chartData.title&&(this.boundingRect.width=i,this.boundingRect.height=n,this.showChartTitle=!0),{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){const t=[];return this.showChartTitle&&t.push({groupTexts:["chart-title"],type:"text",data:[{fontSize:this.chartConfig.titleFontSize,text:this.chartData.title,verticalPos:"middle",horizontalPos:"center",x:this.boundingRect.x+this.boundingRect.width/2,y:this.boundingRect.y+this.boundingRect.height/2,fill:this.chartThemeConfig.titleColor,rotation:0}]}),t}},(0,a.eW)(et,"ChartTitle"),et);function zt(s,t,e,i){const n=new Mt(i);return new ge(n,s,t,e)}(0,a.eW)(zt,"getChartTitleComponent");var xe=(it=class{constructor(t,e,i,n,h){this.plotData=t,this.xAxis=e,this.yAxis=i,this.orientation=n,this.plotIndex=h}getDrawableElement(){const t=this.plotData.data.map(i=>[this.xAxis.getScaleValue(i[0]),this.yAxis.getScaleValue(i[1])]);let e;return this.orientation==="horizontal"?e=(0,K.jvg)().y(i=>i[0]).x(i=>i[1])(t):e=(0,K.jvg)().x(i=>i[0]).y(i=>i[1])(t),e?[{groupTexts:["plot",`line-plot-${this.plotIndex}`],type:"path",data:[{path:e,strokeFill:this.plotData.strokeFill,strokeWidth:this.plotData.strokeWidth}]}]:[]}},(0,a.eW)(it,"LinePlot"),it),de=(st=class{constructor(t,e,i,n,h,d){this.barData=t,this.boundingRect=e,this.xAxis=i,this.yAxis=n,this.orientation=h,this.plotIndex=d}getDrawableElement(){const t=this.barData.data.map(h=>[this.xAxis.getScaleValue(h[0]),this.yAxis.getScaleValue(h[1])]),i=Math.min(this.xAxis.getAxisOuterPadding()*2,this.xAxis.getTickDistance())*(1-.05),n=i/2;return this.orientation==="horizontal"?[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(h=>({x:this.boundingRect.x,y:h[0]-n,height:i,width:h[1]-this.boundingRect.x,fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]:[{groupTexts:["plot",`bar-plot-${this.plotIndex}`],type:"rect",data:t.map(h=>({x:h[0]-n,y:h[1],width:i,height:this.boundingRect.y+this.boundingRect.height-h[1],fill:this.barData.fill,strokeWidth:0,strokeFill:this.barData.fill}))}]}},(0,a.eW)(st,"BarPlot"),st),fe=(at=class{constructor(t,e,i){this.chartConfig=t,this.chartData=e,this.chartThemeConfig=i,this.boundingRect={x:0,y:0,width:0,height:0}}setAxes(t,e){this.xAxis=t,this.yAxis=e}setBoundingBoxXY(t){this.boundingRect.x=t.x,this.boundingRect.y=t.y}calculateSpace(t){return this.boundingRect.width=t.width,this.boundingRect.height=t.height,{width:this.boundingRect.width,height:this.boundingRect.height}}getDrawableElements(){if(!(this.xAxis&&this.yAxis))throw Error("Axes must be passed to render Plots");const t=[];for(const[e,i]of this.chartData.plots.entries())switch(i.type){case"line":{const n=new xe(i,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,e);t.push(...n.getDrawableElement())}break;case"bar":{const n=new de(i,this.boundingRect,this.xAxis,this.yAxis,this.chartConfig.chartOrientation,e);t.push(...n.getDrawableElement())}break}return t}},(0,a.eW)(at,"BasePlot"),at);function Ft(s,t,e){return new fe(s,t,e)}(0,a.eW)(Ft,"getPlotComponent");var pe=(nt=class{constructor(t,e,i,n){this.chartConfig=t,this.chartData=e,this.componentStore={title:zt(t,e,i,n),plot:Ft(t,e,i),xAxis:At(e.xAxis,t.xAxis,{titleColor:i.xAxisTitleColor,labelColor:i.xAxisLabelColor,tickColor:i.xAxisTickColor,axisLineColor:i.xAxisLineColor},n),yAxis:At(e.yAxis,t.yAxis,{titleColor:i.yAxisTitleColor,labelColor:i.yAxisLabelColor,tickColor:i.yAxisTickColor,axisLineColor:i.yAxisLineColor},n)}}calculateVerticalSpace(){let t=this.chartConfig.width,e=this.chartConfig.height,i=0,n=0,h=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),d=Math.floor(e*this.chartConfig.plotReservedSpacePercent/100),b=this.componentStore.plot.calculateSpace({width:h,height:d});t-=b.width,e-=b.height,b=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:e}),n=b.height,e-=b.height,this.componentStore.xAxis.setAxisPosition("bottom"),b=this.componentStore.xAxis.calculateSpace({width:t,height:e}),e-=b.height,this.componentStore.yAxis.setAxisPosition("left"),b=this.componentStore.yAxis.calculateSpace({width:t,height:e}),i=b.width,t-=b.width,t>0&&(h+=t,t=0),e>0&&(d+=e,e=0),this.componentStore.plot.calculateSpace({width:h,height:d}),this.componentStore.plot.setBoundingBoxXY({x:i,y:n}),this.componentStore.xAxis.setRange([i,i+h]),this.componentStore.xAxis.setBoundingBoxXY({x:i,y:n+d}),this.componentStore.yAxis.setRange([n,n+d]),this.componentStore.yAxis.setBoundingBoxXY({x:0,y:n}),this.chartData.plots.some(A=>mt(A))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateHorizontalSpace(){let t=this.chartConfig.width,e=this.chartConfig.height,i=0,n=0,h=0,d=Math.floor(t*this.chartConfig.plotReservedSpacePercent/100),b=Math.floor(e*this.chartConfig.plotReservedSpacePercent/100),A=this.componentStore.plot.calculateSpace({width:d,height:b});t-=A.width,e-=A.height,A=this.componentStore.title.calculateSpace({width:this.chartConfig.width,height:e}),i=A.height,e-=A.height,this.componentStore.xAxis.setAxisPosition("left"),A=this.componentStore.xAxis.calculateSpace({width:t,height:e}),t-=A.width,n=A.width,this.componentStore.yAxis.setAxisPosition("top"),A=this.componentStore.yAxis.calculateSpace({width:t,height:e}),e-=A.height,h=i+A.height,t>0&&(d+=t,t=0),e>0&&(b+=e,e=0),this.componentStore.plot.calculateSpace({width:d,height:b}),this.componentStore.plot.setBoundingBoxXY({x:n,y:h}),this.componentStore.yAxis.setRange([n,n+d]),this.componentStore.yAxis.setBoundingBoxXY({x:n,y:i}),this.componentStore.xAxis.setRange([h,h+b]),this.componentStore.xAxis.setBoundingBoxXY({x:0,y:h}),this.chartData.plots.some(E=>mt(E))&&this.componentStore.xAxis.recalculateOuterPaddingToDrawBar()}calculateSpace(){this.chartConfig.chartOrientation==="horizontal"?this.calculateHorizontalSpace():this.calculateVerticalSpace()}getDrawableElement(){this.calculateSpace();const t=[];this.componentStore.plot.setAxes(this.componentStore.xAxis,this.componentStore.yAxis);for(const e of Object.values(this.componentStore))t.push(...e.getDrawableElements());return t}},(0,a.eW)(nt,"Orchestrator"),nt),ye=(rt=class{static build(t,e,i,n){return new pe(t,e,i,n).getDrawableElement()}},(0,a.eW)(rt,"XYChartBuilder"),rt),ht=0,Xt,lt=Ct(),ct=wt(),y=St(),kt=ct.plotColorPalette.split(",").map(s=>s.trim()),gt=!1,_t=!1;function wt(){const s=(0,a.xN)(),t=(0,a.iE)();return(0,It.Rb)(s.xyChart,t.themeVariables.xyChart)}(0,a.eW)(wt,"getChartDefaultThemeConfig");function Ct(){const s=(0,a.iE)();return(0,It.Rb)(a.vZ.xyChart,s.xyChart)}(0,a.eW)(Ct,"getChartDefaultConfig");function St(){return{yAxis:{type:"linear",title:"",min:1/0,max:-1/0},xAxis:{type:"band",title:"",categories:[]},title:"",plots:[]}}(0,a.eW)(St,"getChartDefaultData");function xt(s){const t=(0,a.iE)();return(0,a.oO)(s.trim(),t)}(0,a.eW)(xt,"textSanitizer");function Nt(s){Xt=s}(0,a.eW)(Nt,"setTmpSVGG");function Yt(s){s==="horizontal"?lt.chartOrientation="horizontal":lt.chartOrientation="vertical"}(0,a.eW)(Yt,"setOrientation");function Ht(s){y.xAxis.title=xt(s.text)}(0,a.eW)(Ht,"setXAxisTitle");function Rt(s,t){y.xAxis={type:"linear",title:y.xAxis.title,min:s,max:t},gt=!0}(0,a.eW)(Rt,"setXAxisRangeData");function Ut(s){y.xAxis={type:"band",title:y.xAxis.title,categories:s.map(t=>xt(t.text))},gt=!0}(0,a.eW)(Ut,"setXAxisBand");function $t(s){y.yAxis.title=xt(s.text)}(0,a.eW)($t,"setYAxisTitle");function jt(s,t){y.yAxis={type:"linear",title:y.yAxis.title,min:s,max:t},_t=!0}(0,a.eW)(jt,"setYAxisRangeData");function Gt(s){const t=Math.min(...s),e=Math.max(...s),i=Q(y.yAxis)?y.yAxis.min:1/0,n=Q(y.yAxis)?y.yAxis.max:-1/0;y.yAxis={type:"linear",title:y.yAxis.title,min:Math.min(i,t),max:Math.max(n,e)}}(0,a.eW)(Gt,"setYAxisRangeFromPlotData");function Tt(s){let t=[];if(s.length===0)return t;if(!gt){const e=Q(y.xAxis)?y.xAxis.min:1/0,i=Q(y.xAxis)?y.xAxis.max:-1/0;Rt(Math.min(e,1),Math.max(i,s.length))}if(_t||Gt(s),bt(y.xAxis)&&(t=y.xAxis.categories.map((e,i)=>[e,s[i]])),Q(y.xAxis)){const e=y.xAxis.min,i=y.xAxis.max,n=(i-e)/(s.length-1),h=[];for(let d=e;d<=i;d+=n)h.push(`${d}`);t=h.map((d,b)=>[d,s[b]])}return t}(0,a.eW)(Tt,"transformDataWithoutCategory");function Dt(s){return kt[s===0?0:s%kt.length]}(0,a.eW)(Dt,"getPlotColorFromPalette");function Kt(s,t){const e=Tt(t);y.plots.push({type:"line",strokeFill:Dt(ht),strokeWidth:2,data:e}),ht++}(0,a.eW)(Kt,"setLineData");function Qt(s,t){const e=Tt(t);y.plots.push({type:"bar",fill:Dt(ht),data:e}),ht++}(0,a.eW)(Qt,"setBarData");function qt(){if(y.plots.length===0)throw Error("No Plot to render, please provide a plot with some data");return y.title=(0,a.Kr)(),ye.build(lt,y,ct,Xt)}(0,a.eW)(qt,"getDrawableElem");function Zt(){return ct}(0,a.eW)(Zt,"getChartThemeConfig");function Jt(){return lt}(0,a.eW)(Jt,"getChartConfig");function te(){return y}(0,a.eW)(te,"getXYChartData");var me=(0,a.eW)(function(){(0,a.ZH)(),ht=0,lt=Ct(),y=St(),ct=wt(),kt=ct.plotColorPalette.split(",").map(s=>s.trim()),gt=!1,_t=!1},"clear"),be={getDrawableElem:qt,clear:me,setAccTitle:a.GN,getAccTitle:a.eu,setDiagramTitle:a.g2,getDiagramTitle:a.Kr,getAccDescription:a.Mx,setAccDescription:a.U$,setOrientation:Yt,setXAxisTitle:Ht,setXAxisRangeData:Rt,setXAxisBand:Ut,setYAxisTitle:$t,setYAxisRangeData:jt,setLineData:Kt,setBarData:Qt,setTmpSVGG:Nt,getChartThemeConfig:Zt,getChartConfig:Jt,getXYChartData:te},Ae=(0,a.eW)((s,t,e,i)=>{const n=i.db,h=n.getChartThemeConfig(),d=n.getChartConfig(),b=n.getXYChartData().plots[0].data.map(p=>p[1]);function A(p){return p==="top"?"text-before-edge":"middle"}(0,a.eW)(A,"getDominantBaseLine");function E(p){return p==="left"?"start":p==="right"?"end":"middle"}(0,a.eW)(E,"getTextAnchor");function M(p){return`translate(${p.x}, ${p.y}) rotate(${p.rotation||0})`}(0,a.eW)(M,"getTextTransformation"),a.cM.debug(`Rendering xychart chart
`+s);const T=(0,he.P)(t),L=T.append("g").attr("class","main"),O=L.append("rect").attr("width",d.width).attr("height",d.height).attr("class","background");(0,a.v2)(T,d.height,d.width,!0),T.attr("viewBox",`0 0 ${d.width} ${d.height}`),O.attr("fill",h.backgroundColor),n.setTmpSVGG(T.append("g").attr("class","mermaid-tmp-group"));const z=n.getDrawableElem(),D={};function B(p){let W=L,l="";for(const[V]of p.entries()){let P=L;V>0&&D[l]&&(P=D[l]),l+=p[V],W=D[l],W||(W=D[l]=P.append("g").attr("class",p[V]))}return W}(0,a.eW)(B,"getGroup");for(const p of z){if(p.data.length===0)continue;const W=B(p.groupTexts);switch(p.type){case"rect":if(W.selectAll("rect").data(p.data).enter().append("rect").attr("x",l=>l.x).attr("y",l=>l.y).attr("width",l=>l.width).attr("height",l=>l.height).attr("fill",l=>l.fill).attr("stroke",l=>l.strokeFill).attr("stroke-width",l=>l.strokeWidth),d.showDataLabel)if(d.chartOrientation==="horizontal"){let l=function(u,C){const{data:R,label:w}=u;return C*w.length*V<=R.width-10};var F=l;(0,a.eW)(l,"fitsHorizontally");const V=.7,P=p.data.map((u,C)=>({data:u,label:b[C].toString()})).filter(u=>u.data.width>0&&u.data.height>0),k=P.map(u=>{const{data:C}=u;let R=C.height*.7;for(;!l(u,R)&&R>0;)R-=1;return R}),H=Math.floor(Math.min(...k));W.selectAll("text").data(P).enter().append("text").attr("x",u=>u.data.x+u.data.width-10).attr("y",u=>u.data.y+u.data.height/2).attr("text-anchor","end").attr("dominant-baseline","middle").attr("fill","black").attr("font-size",`${H}px`).text(u=>u.label)}else{let l=function(u,C,R){const{data:w,label:U}=u,o=C*U.length*.7,c=w.x+w.width/2,g=c-o/2,x=c+o/2,m=g>=w.x&&x<=w.x+w.width,r=w.y+R+C<=w.y+w.height;return m&&r};var X=l;(0,a.eW)(l,"fitsInBar");const V=10,P=p.data.map((u,C)=>({data:u,label:b[C].toString()})).filter(u=>u.data.width>0&&u.data.height>0),k=P.map(u=>{const{data:C,label:R}=u;let w=C.width/(R.length*.7);for(;!l(u,w,V)&&w>0;)w-=1;return w}),H=Math.floor(Math.min(...k));W.selectAll("text").data(P).enter().append("text").attr("x",u=>u.data.x+u.data.width/2).attr("y",u=>u.data.y+V).attr("text-anchor","middle").attr("dominant-baseline","hanging").attr("fill","black").attr("font-size",`${H}px`).text(u=>u.label)}break;case"text":W.selectAll("text").data(p.data).enter().append("text").attr("x",0).attr("y",0).attr("fill",l=>l.fill).attr("font-size",l=>l.fontSize).attr("dominant-baseline",l=>A(l.verticalPos)).attr("text-anchor",l=>E(l.horizontalPos)).attr("transform",l=>M(l)).text(l=>l.text);break;case"path":W.selectAll("path").data(p.data).enter().append("path").attr("d",l=>l.path).attr("fill",l=>l.fill?l.fill:"none").attr("stroke",l=>l.strokeFill).attr("stroke-width",l=>l.strokeWidth);break}}},"draw"),ke={draw:Ae},_e={parser:le,db:be,renderer:ke}}}]);
