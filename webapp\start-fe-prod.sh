#!/bin/bash

start=$(date +%s)

node_version=$(node -v)

major_version=$(echo $node_version | cut -d'.' -f1 | tr -d 'v')

if [ $major_version -ge 17 ]; then
  export NODE_OPTIONS=--openssl-legacy-provider
fi

if ! command -v pnpm >/dev/null 2>&1; then
  npm i -g pnpm
fi

rm -rf hchatdata-webapp.tar.gz

rm -rf ./packages/hchatdata-fe/src/.umi ./packages/hchatdata-fe/src/.umi-production

echo "Building genie-ui..."
cd ./packages/genie-ui

pnpm i

pnpm run build
if [ $? -ne 0 ]; then
    echo "Failed to build genie-ui."
    exit 1
fi

pnpm link --global

echo "Building chat-sdk..."
cd ../chat-sdk

pnpm link genie-ui

pnpm i

pnpm run build
if [ $? -ne 0 ]; then
    echo "Failed to build chat sdk."
    exit 1
fi

pnpm link --global

echo "Building hchatdata-fe..."
cd ../hchatdata-fe

pnpm link ../chat-sdk

pnpm i

pnpm run build:os-local
if [ $? -ne 0 ]; then
    echo "Failed to build hchatdata-fe."
    exit 1
fi

tar -zcvf hchatdata-webapp.tar.gz ./hchatdata-webapp

mv hchatdata-webapp.tar.gz ../../

cd ../../

end=$(date +%s)

take=$(( end - start ))

echo Time taken to execute commands is ${take} seconds.
