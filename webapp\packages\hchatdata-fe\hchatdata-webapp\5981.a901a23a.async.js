!(function(){"use strict";var Xr=Object.defineProperty,Gr=Object.defineProperties;var Jr=Object.getOwnPropertyDescriptors;var De=Object.getOwnPropertySymbols;var Qr=Object.prototype.hasOwnProperty,$r=Object.prototype.propertyIsEnumerable;var Ce=(J,Y,v)=>Y in J?Xr(J,Y,{enumerable:!0,configurable:!0,writable:!0,value:v}):J[Y]=v,Ct=(J,Y)=>{for(var v in Y||(Y={}))Qr.call(Y,v)&&Ce(J,v,Y[v]);if(De)for(var v of De(Y))$r.call(Y,v)&&Ce(J,v,Y[v]);return J},Nt=(J,Y)=>Gr(J,Jr(Y));var I=(J,Y,v)=>new Promise(($,rt)=>{var at=n=>{try{X(v.next(n))}catch(lt){rt(lt)}},st=n=>{try{X(v.throw(n))}catch(lt){rt(lt)}},X=n=>n.done?$(n.value):Promise.resolve(n.value).then(at,st);X((v=v.apply(J,Y)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5981],{55133:function(J,Y,v){v.d(Y,{k:function(){return vt}});var $=v(61606),rt=v(89477),at=v(92769),st=v(97175),X=v(67127),n=v(24285),lt=v(80155),Pt=v(3148),Yt=v(93092),F=v(57031),Ft=v(45408),wt=(0,Yt.Z)(function(Q){return(0,F.Z)((0,Pt.Z)(Q,1,Ft.Z,!0))}),jt=wt,tt=v(10541),Lt=v(6613),_t="\0",ut="\0",St="";class vt{constructor(u={}){this._isDirected=Object.prototype.hasOwnProperty.call(u,"directed")?u.directed:!0,this._isMultigraph=Object.prototype.hasOwnProperty.call(u,"multigraph")?u.multigraph:!1,this._isCompound=Object.prototype.hasOwnProperty.call(u,"compound")?u.compound:!1,this._label=void 0,this._defaultNodeLabelFn=$.Z(void 0),this._defaultEdgeLabelFn=$.Z(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[ut]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}isDirected(){return this._isDirected}isMultigraph(){return this._isMultigraph}isCompound(){return this._isCompound}setGraph(u){return this._label=u,this}graph(){return this._label}setDefaultNodeLabel(u){return rt.Z(u)||(u=$.Z(u)),this._defaultNodeLabelFn=u,this}nodeCount(){return this._nodeCount}nodes(){return at.Z(this._nodes)}sources(){var u=this;return st.Z(this.nodes(),function(y){return X.Z(u._in[y])})}sinks(){var u=this;return st.Z(this.nodes(),function(y){return X.Z(u._out[y])})}setNodes(u,y){var E=arguments,N=this;return n.Z(u,function(B){E.length>1?N.setNode(B,y):N.setNode(B)}),this}setNode(u,y){return Object.prototype.hasOwnProperty.call(this._nodes,u)?(arguments.length>1&&(this._nodes[u]=y),this):(this._nodes[u]=arguments.length>1?y:this._defaultNodeLabelFn(u),this._isCompound&&(this._parent[u]=ut,this._children[u]={},this._children[ut][u]=!0),this._in[u]={},this._preds[u]={},this._out[u]={},this._sucs[u]={},++this._nodeCount,this)}node(u){return this._nodes[u]}hasNode(u){return Object.prototype.hasOwnProperty.call(this._nodes,u)}removeNode(u){if(Object.prototype.hasOwnProperty.call(this._nodes,u)){var y=E=>this.removeEdge(this._edgeObjs[E]);delete this._nodes[u],this._isCompound&&(this._removeFromParentsChildList(u),delete this._parent[u],n.Z(this.children(u),E=>{this.setParent(E)}),delete this._children[u]),n.Z(at.Z(this._in[u]),y),delete this._in[u],delete this._preds[u],n.Z(at.Z(this._out[u]),y),delete this._out[u],delete this._sucs[u],--this._nodeCount}return this}setParent(u,y){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(lt.Z(y))y=ut;else{y+="";for(var E=y;!lt.Z(E);E=this.parent(E))if(E===u)throw new Error("Setting "+y+" as parent of "+u+" would create a cycle");this.setNode(y)}return this.setNode(u),this._removeFromParentsChildList(u),this._parent[u]=y,this._children[y][u]=!0,this}_removeFromParentsChildList(u){delete this._children[this._parent[u]][u]}parent(u){if(this._isCompound){var y=this._parent[u];if(y!==ut)return y}}children(u){if(lt.Z(u)&&(u=ut),this._isCompound){var y=this._children[u];if(y)return at.Z(y)}else{if(u===ut)return this.nodes();if(this.hasNode(u))return[]}}predecessors(u){var y=this._preds[u];if(y)return at.Z(y)}successors(u){var y=this._sucs[u];if(y)return at.Z(y)}neighbors(u){var y=this.predecessors(u);if(y)return jt(y,this.successors(u))}isLeaf(u){var y;return this.isDirected()?y=this.successors(u):y=this.neighbors(u),y.length===0}filterNodes(u){var y=new this.constructor({directed:this._isDirected,multigraph:this._isMultigraph,compound:this._isCompound});y.setGraph(this.graph());var E=this;n.Z(this._nodes,function(R,Z){u(Z)&&y.setNode(Z,R)}),n.Z(this._edgeObjs,function(R){y.hasNode(R.v)&&y.hasNode(R.w)&&y.setEdge(R,E.edge(R))});var N={};function B(R){var Z=E.parent(R);return Z===void 0||y.hasNode(Z)?(N[R]=Z,Z):Z in N?N[Z]:B(Z)}return this._isCompound&&n.Z(y.nodes(),function(R){y.setParent(R,B(R))}),y}setDefaultEdgeLabel(u){return rt.Z(u)||(u=$.Z(u)),this._defaultEdgeLabelFn=u,this}edgeCount(){return this._edgeCount}edges(){return tt.Z(this._edgeObjs)}setPath(u,y){var E=this,N=arguments;return Lt.Z(u,function(B,R){return N.length>1?E.setEdge(B,R,y):E.setEdge(B,R),R}),this}setEdge(){var u,y,E,N,B=!1,R=arguments[0];typeof R=="object"&&R!==null&&"v"in R?(u=R.v,y=R.w,E=R.name,arguments.length===2&&(N=arguments[1],B=!0)):(u=R,y=arguments[1],E=arguments[3],arguments.length>2&&(N=arguments[2],B=!0)),u=""+u,y=""+y,lt.Z(E)||(E=""+E);var Z=gt(this._isDirected,u,y,E);if(Object.prototype.hasOwnProperty.call(this._edgeLabels,Z))return B&&(this._edgeLabels[Z]=N),this;if(!lt.Z(E)&&!this._isMultigraph)throw new Error("Cannot set a named edge when isMultigraph = false");this.setNode(u),this.setNode(y),this._edgeLabels[Z]=B?N:this._defaultEdgeLabelFn(u,y,E);var ft=Zt(this._isDirected,u,y,E);return u=ft.v,y=ft.w,Object.freeze(ft),this._edgeObjs[Z]=ft,Et(this._preds[y],u),Et(this._sucs[u],y),this._in[y][Z]=ft,this._out[u][Z]=ft,this._edgeCount++,this}edge(u,y,E){var N=arguments.length===1?kt(this._isDirected,arguments[0]):gt(this._isDirected,u,y,E);return this._edgeLabels[N]}hasEdge(u,y,E){var N=arguments.length===1?kt(this._isDirected,arguments[0]):gt(this._isDirected,u,y,E);return Object.prototype.hasOwnProperty.call(this._edgeLabels,N)}removeEdge(u,y,E){var N=arguments.length===1?kt(this._isDirected,arguments[0]):gt(this._isDirected,u,y,E),B=this._edgeObjs[N];return B&&(u=B.v,y=B.w,delete this._edgeLabels[N],delete this._edgeObjs[N],Wt(this._preds[y],u),Wt(this._sucs[u],y),delete this._in[y][N],delete this._out[u][N],this._edgeCount--),this}inEdges(u,y){var E=this._in[u];if(E){var N=tt.Z(E);return y?st.Z(N,function(B){return B.v===y}):N}}outEdges(u,y){var E=this._out[u];if(E){var N=tt.Z(E);return y?st.Z(N,function(B){return B.w===y}):N}}nodeEdges(u,y){var E=this.inEdges(u,y);if(E)return E.concat(this.outEdges(u,y))}}vt.prototype._nodeCount=0,vt.prototype._edgeCount=0;function Et(Q,u){Q[u]?Q[u]++:Q[u]=1}function Wt(Q,u){--Q[u]||delete Q[u]}function gt(Q,u,y,E){var N=""+u,B=""+y;if(!Q&&N>B){var R=N;N=B,B=R}return N+St+B+St+(lt.Z(E)?_t:E)}function Zt(Q,u,y,E){var N=""+u,B=""+y;if(!Q&&N>B){var R=N;N=B,B=R}var Z={v:N,w:B};return E&&(Z.name=E),Z}function kt(Q,u){return gt(Q,u.v,u.w,u.name)}},56561:function(J,Y,v){v.d(Y,{k:function(){return $.k}});var $=v(55133);const rt="2.1.9-pre"},74523:function(J,Y,v){var $=v(50834),rt=v(38456);const at=(st,X)=>$.Z.lang.round(rt.Z.parse(st)[X]);Y.Z=at},24220:function(J,Y,v){var $=v(68194),rt=4;function at(st){return(0,$.Z)(st,rt)}Y.Z=at},45981:function(J,Y,v){var _e,Se,ve;v.d(Y,{diagram:function(){return Ur}});var $=v(70982),rt=v(61150),at=v(50538),st=v(70919),X=v(50854),n=v(29134),lt=v(24220),Pt=v(74523),Yt=v(85712),F=v(69471),Ft=v(56561),wt=function(){var e=(0,n.eW)(function(A,m,g,x){for(g=g||{},x=A.length;x--;g[A[x]]=m);return g},"o"),t=[1,7],a=[1,13],i=[1,14],c=[1,15],s=[1,19],r=[1,16],l=[1,17],o=[1,18],f=[8,30],d=[8,21,28,29,30,31,32,40,44,47],b=[1,23],w=[1,24],_=[8,15,16,21,28,29,30,31,32,40,44,47],k=[8,15,16,21,27,28,29,30,31,32,40,44,47],T=[1,49],S={trace:(0,n.eW)(function(){},"trace"),yy:{},symbols_:{error:2,spaceLines:3,SPACELINE:4,NL:5,separator:6,SPACE:7,EOF:8,start:9,BLOCK_DIAGRAM_KEY:10,document:11,stop:12,statement:13,link:14,LINK:15,START_LINK:16,LINK_LABEL:17,STR:18,nodeStatement:19,columnsStatement:20,SPACE_BLOCK:21,blockStatement:22,classDefStatement:23,cssClassStatement:24,styleStatement:25,node:26,SIZE:27,COLUMNS:28,"id-block":29,end:30,block:31,NODE_ID:32,nodeShapeNLabel:33,dirList:34,DIR:35,NODE_DSTART:36,NODE_DEND:37,BLOCK_ARROW_START:38,BLOCK_ARROW_END:39,classDef:40,CLASSDEF_ID:41,CLASSDEF_STYLEOPTS:42,DEFAULT:43,class:44,CLASSENTITY_IDS:45,STYLECLASS:46,style:47,STYLE_ENTITY_IDS:48,STYLE_DEFINITION_DATA:49,$accept:0,$end:1},terminals_:{2:"error",4:"SPACELINE",5:"NL",7:"SPACE",8:"EOF",10:"BLOCK_DIAGRAM_KEY",15:"LINK",16:"START_LINK",17:"LINK_LABEL",18:"STR",21:"SPACE_BLOCK",27:"SIZE",28:"COLUMNS",29:"id-block",30:"end",31:"block",32:"NODE_ID",35:"DIR",36:"NODE_DSTART",37:"NODE_DEND",38:"BLOCK_ARROW_START",39:"BLOCK_ARROW_END",40:"classDef",41:"CLASSDEF_ID",42:"CLASSDEF_STYLEOPTS",43:"DEFAULT",44:"class",45:"CLASSENTITY_IDS",46:"STYLECLASS",47:"style",48:"STYLE_ENTITY_IDS",49:"STYLE_DEFINITION_DATA"},productions_:[0,[3,1],[3,2],[3,2],[6,1],[6,1],[6,1],[9,3],[12,1],[12,1],[12,2],[12,2],[11,1],[11,2],[14,1],[14,4],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[13,1],[19,3],[19,2],[19,1],[20,1],[22,4],[22,3],[26,1],[26,2],[34,1],[34,2],[33,3],[33,4],[23,3],[23,3],[24,3],[25,3]],performAction:(0,n.eW)(function(m,g,x,L,W,h,M){var p=h.length-1;switch(W){case 4:L.getLogger().debug("Rule: separator (NL) ");break;case 5:L.getLogger().debug("Rule: separator (Space) ");break;case 6:L.getLogger().debug("Rule: separator (EOF) ");break;case 7:L.getLogger().debug("Rule: hierarchy: ",h[p-1]),L.setHierarchy(h[p-1]);break;case 8:L.getLogger().debug("Stop NL ");break;case 9:L.getLogger().debug("Stop EOF ");break;case 10:L.getLogger().debug("Stop NL2 ");break;case 11:L.getLogger().debug("Stop EOF2 ");break;case 12:L.getLogger().debug("Rule: statement: ",h[p]),typeof h[p].length=="number"?this.$=h[p]:this.$=[h[p]];break;case 13:L.getLogger().debug("Rule: statement #2: ",h[p-1]),this.$=[h[p-1]].concat(h[p]);break;case 14:L.getLogger().debug("Rule: link: ",h[p],m),this.$={edgeTypeStr:h[p],label:""};break;case 15:L.getLogger().debug("Rule: LABEL link: ",h[p-3],h[p-1],h[p]),this.$={edgeTypeStr:h[p],label:h[p-1]};break;case 18:const D=parseInt(h[p]),P=L.generateId();this.$={id:P,type:"space",label:"",width:D,children:[]};break;case 23:L.getLogger().debug("Rule: (nodeStatement link node) ",h[p-2],h[p-1],h[p]," typestr: ",h[p-1].edgeTypeStr);const C=L.edgeStrToEdgeData(h[p-1].edgeTypeStr);this.$=[{id:h[p-2].id,label:h[p-2].label,type:h[p-2].type,directions:h[p-2].directions},{id:h[p-2].id+"-"+h[p].id,start:h[p-2].id,end:h[p].id,label:h[p-1].label,type:"edge",directions:h[p].directions,arrowTypeEnd:C,arrowTypeStart:"arrow_open"},{id:h[p].id,label:h[p].label,type:L.typeStr2Type(h[p].typeStr),directions:h[p].directions}];break;case 24:L.getLogger().debug("Rule: nodeStatement (abc88 node size) ",h[p-1],h[p]),this.$={id:h[p-1].id,label:h[p-1].label,type:L.typeStr2Type(h[p-1].typeStr),directions:h[p-1].directions,widthInColumns:parseInt(h[p],10)};break;case 25:L.getLogger().debug("Rule: nodeStatement (node) ",h[p]),this.$={id:h[p].id,label:h[p].label,type:L.typeStr2Type(h[p].typeStr),directions:h[p].directions,widthInColumns:1};break;case 26:L.getLogger().debug("APA123",this?this:"na"),L.getLogger().debug("COLUMNS: ",h[p]),this.$={type:"column-setting",columns:h[p]==="auto"?-1:parseInt(h[p])};break;case 27:L.getLogger().debug("Rule: id-block statement : ",h[p-2],h[p-1]);const V=L.generateId();this.$=Nt(Ct({},h[p-2]),{type:"composite",children:h[p-1]});break;case 28:L.getLogger().debug("Rule: blockStatement : ",h[p-2],h[p-1],h[p]);const pt=L.generateId();this.$={id:pt,type:"composite",label:"",children:h[p-1]};break;case 29:L.getLogger().debug("Rule: node (NODE_ID separator): ",h[p]),this.$={id:h[p]};break;case 30:L.getLogger().debug("Rule: node (NODE_ID nodeShapeNLabel separator): ",h[p-1],h[p]),this.$={id:h[p-1],label:h[p].label,typeStr:h[p].typeStr,directions:h[p].directions};break;case 31:L.getLogger().debug("Rule: dirList: ",h[p]),this.$=[h[p]];break;case 32:L.getLogger().debug("Rule: dirList: ",h[p-1],h[p]),this.$=[h[p-1]].concat(h[p]);break;case 33:L.getLogger().debug("Rule: nodeShapeNLabel: ",h[p-2],h[p-1],h[p]),this.$={typeStr:h[p-2]+h[p],label:h[p-1]};break;case 34:L.getLogger().debug("Rule: BLOCK_ARROW nodeShapeNLabel: ",h[p-3],h[p-2]," #3:",h[p-1],h[p]),this.$={typeStr:h[p-3]+h[p],label:h[p-2],directions:h[p-1]};break;case 35:case 36:this.$={type:"classDef",id:h[p-1].trim(),css:h[p].trim()};break;case 37:this.$={type:"applyClass",id:h[p-1].trim(),styleClass:h[p].trim()};break;case 38:this.$={type:"applyStyles",id:h[p-1].trim(),stylesStr:h[p].trim()};break}},"anonymous"),table:[{9:1,10:[1,2]},{1:[3]},{11:3,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:c,32:s,40:r,44:l,47:o},{8:[1,20]},e(f,[2,12],{13:4,19:5,20:6,22:8,23:9,24:10,25:11,26:12,11:21,21:t,28:a,29:i,31:c,32:s,40:r,44:l,47:o}),e(d,[2,16],{14:22,15:b,16:w}),e(d,[2,17]),e(d,[2,18]),e(d,[2,19]),e(d,[2,20]),e(d,[2,21]),e(d,[2,22]),e(_,[2,25],{27:[1,25]}),e(d,[2,26]),{19:26,26:12,32:s},{11:27,13:4,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:c,32:s,40:r,44:l,47:o},{41:[1,28],43:[1,29]},{45:[1,30]},{48:[1,31]},e(k,[2,29],{33:32,36:[1,33],38:[1,34]}),{1:[2,7]},e(f,[2,13]),{26:35,32:s},{32:[2,14]},{17:[1,36]},e(_,[2,24]),{11:37,13:4,14:22,15:b,16:w,19:5,20:6,21:t,22:8,23:9,24:10,25:11,26:12,28:a,29:i,31:c,32:s,40:r,44:l,47:o},{30:[1,38]},{42:[1,39]},{42:[1,40]},{46:[1,41]},{49:[1,42]},e(k,[2,30]),{18:[1,43]},{18:[1,44]},e(_,[2,23]),{18:[1,45]},{30:[1,46]},e(d,[2,28]),e(d,[2,35]),e(d,[2,36]),e(d,[2,37]),e(d,[2,38]),{37:[1,47]},{34:48,35:T},{15:[1,50]},e(d,[2,27]),e(k,[2,33]),{39:[1,51]},{34:52,35:T,39:[2,31]},{32:[2,15]},e(k,[2,34]),{39:[2,32]}],defaultActions:{20:[2,7],23:[2,14],50:[2,15],52:[2,32]},parseError:(0,n.eW)(function(m,g){if(g.recoverable)this.trace(m);else{var x=new Error(m);throw x.hash=g,x}},"parseError"),parse:(0,n.eW)(function(m){var g=this,x=[0],L=[],W=[null],h=[],M=this.table,p="",D=0,P=0,C=0,V=2,pt=1,Rt=h.slice.call(arguments,1),H=Object.create(this.lexer),ot={yy:{}};for(var xt in this.yy)Object.prototype.hasOwnProperty.call(this.yy,xt)&&(ot.yy[xt]=this.yy[xt]);H.setInput(m,ot.yy),ot.yy.lexer=H,ot.yy.parser=this,typeof H.yylloc=="undefined"&&(H.yylloc={});var Jt=H.yylloc;h.push(Jt);var Hr=H.options&&H.options.ranges;typeof ot.yy.parseError=="function"?this.parseError=ot.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function Vr(nt){x.length=x.length-2*nt,W.length=W.length-nt,h.length=h.length-nt}(0,n.eW)(Vr,"popStack");function Ee(){var nt;return nt=L.pop()||H.lex()||pt,typeof nt!="number"&&(nt instanceof Array&&(L=nt,nt=L.pop()),nt=g.symbols_[nt]||nt),nt}(0,n.eW)(Ee,"lex");for(var et,Qt,bt,ht,qr,$t,mt={},At,yt,ke,zt;;){if(bt=x[x.length-1],this.defaultActions[bt]?ht=this.defaultActions[bt]:((et===null||typeof et=="undefined")&&(et=Ee()),ht=M[bt]&&M[bt][et]),typeof ht=="undefined"||!ht.length||!ht[0]){var qt="";zt=[];for(At in M[bt])this.terminals_[At]&&At>V&&zt.push("'"+this.terminals_[At]+"'");H.showPosition?qt="Parse error on line "+(D+1)+`:
`+H.showPosition()+`
Expecting `+zt.join(", ")+", got '"+(this.terminals_[et]||et)+"'":qt="Parse error on line "+(D+1)+": Unexpected "+(et==pt?"end of input":"'"+(this.terminals_[et]||et)+"'"),this.parseError(qt,{text:H.match,token:this.terminals_[et]||et,line:H.yylineno,loc:Jt,expected:zt})}if(ht[0]instanceof Array&&ht.length>1)throw new Error("Parse Error: multiple actions possible at state: "+bt+", token: "+et);switch(ht[0]){case 1:x.push(et),W.push(H.yytext),h.push(H.yylloc),x.push(ht[1]),et=null,Qt?(et=Qt,Qt=null):(P=H.yyleng,p=H.yytext,D=H.yylineno,Jt=H.yylloc,C>0&&C--);break;case 2:if(yt=this.productions_[ht[1]][1],mt.$=W[W.length-yt],mt._$={first_line:h[h.length-(yt||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(yt||1)].first_column,last_column:h[h.length-1].last_column},Hr&&(mt._$.range=[h[h.length-(yt||1)].range[0],h[h.length-1].range[1]]),$t=this.performAction.apply(mt,[p,P,D,ot.yy,ht[1],W,h].concat(Rt)),typeof $t!="undefined")return $t;yt&&(x=x.slice(0,-1*yt*2),W=W.slice(0,-1*yt),h=h.slice(0,-1*yt)),x.push(this.productions_[ht[1]][0]),W.push(mt.$),h.push(mt._$),ke=M[x[x.length-2]][x[x.length-1]],x.push(ke);break;case 3:return!0}}return!0},"parse")},z=function(){var A={EOF:1,parseError:(0,n.eW)(function(g,x){if(this.yy.parser)this.yy.parser.parseError(g,x);else throw new Error(g)},"parseError"),setInput:(0,n.eW)(function(m,g){return this.yy=g||this.yy||{},this._input=m,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,n.eW)(function(){var m=this._input[0];this.yytext+=m,this.yyleng++,this.offset++,this.match+=m,this.matched+=m;var g=m.match(/(?:\r\n?|\n).*/g);return g?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),m},"input"),unput:(0,n.eW)(function(m){var g=m.length,x=m.split(/(?:\r\n?|\n)/g);this._input=m+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-g),this.offset-=g;var L=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),x.length-1&&(this.yylineno-=x.length-1);var W=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:x?(x.length===L.length?this.yylloc.first_column:0)+L[L.length-x.length].length-x[0].length:this.yylloc.first_column-g},this.options.ranges&&(this.yylloc.range=[W[0],W[0]+this.yyleng-g]),this.yyleng=this.yytext.length,this},"unput"),more:(0,n.eW)(function(){return this._more=!0,this},"more"),reject:(0,n.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,n.eW)(function(m){this.unput(this.match.slice(m))},"less"),pastInput:(0,n.eW)(function(){var m=this.matched.substr(0,this.matched.length-this.match.length);return(m.length>20?"...":"")+m.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,n.eW)(function(){var m=this.match;return m.length<20&&(m+=this._input.substr(0,20-m.length)),(m.substr(0,20)+(m.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,n.eW)(function(){var m=this.pastInput(),g=new Array(m.length+1).join("-");return m+this.upcomingInput()+`
`+g+"^"},"showPosition"),test_match:(0,n.eW)(function(m,g){var x,L,W;if(this.options.backtrack_lexer&&(W={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(W.yylloc.range=this.yylloc.range.slice(0))),L=m[0].match(/(?:\r\n?|\n).*/g),L&&(this.yylineno+=L.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:L?L[L.length-1].length-L[L.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+m[0].length},this.yytext+=m[0],this.match+=m[0],this.matches=m,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(m[0].length),this.matched+=m[0],x=this.performAction.call(this,this.yy,this,g,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),x)return x;if(this._backtrack){for(var h in W)this[h]=W[h];return!1}return!1},"test_match"),next:(0,n.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var m,g,x,L;this._more||(this.yytext="",this.match="");for(var W=this._currentRules(),h=0;h<W.length;h++)if(x=this._input.match(this.rules[W[h]]),x&&(!g||x[0].length>g[0].length)){if(g=x,L=h,this.options.backtrack_lexer){if(m=this.test_match(x,W[h]),m!==!1)return m;if(this._backtrack){g=!1;continue}else return!1}else if(!this.options.flex)break}return g?(m=this.test_match(g,W[L]),m!==!1?m:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,n.eW)(function(){var g=this.next();return g||this.lex()},"lex"),begin:(0,n.eW)(function(g){this.conditionStack.push(g)},"begin"),popState:(0,n.eW)(function(){var g=this.conditionStack.length-1;return g>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,n.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,n.eW)(function(g){return g=this.conditionStack.length-1-Math.abs(g||0),g>=0?this.conditionStack[g]:"INITIAL"},"topState"),pushState:(0,n.eW)(function(g){this.begin(g)},"pushState"),stateStackSize:(0,n.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,n.eW)(function(g,x,L,W){var h=W;switch(L){case 0:return 10;case 1:return g.getLogger().debug("Found space-block"),31;break;case 2:return g.getLogger().debug("Found nl-block"),31;break;case 3:return g.getLogger().debug("Found space-block"),29;break;case 4:g.getLogger().debug(".",x.yytext);break;case 5:g.getLogger().debug("_",x.yytext);break;case 6:return 5;case 7:return x.yytext=-1,28;break;case 8:return x.yytext=x.yytext.replace(/columns\s+/,""),g.getLogger().debug("COLUMNS (LEX)",x.yytext),28;break;case 9:this.pushState("md_string");break;case 10:return"MD_STR";case 11:this.popState();break;case 12:this.pushState("string");break;case 13:g.getLogger().debug("LEX: POPPING STR:",x.yytext),this.popState();break;case 14:return g.getLogger().debug("LEX: STR end:",x.yytext),"STR";break;case 15:return x.yytext=x.yytext.replace(/space\:/,""),g.getLogger().debug("SPACE NUM (LEX)",x.yytext),21;break;case 16:return x.yytext="1",g.getLogger().debug("COLUMNS (LEX)",x.yytext),21;break;case 17:return 43;case 18:return"LINKSTYLE";case 19:return"INTERPOLATE";case 20:return this.pushState("CLASSDEF"),40;break;case 21:return this.popState(),this.pushState("CLASSDEFID"),"DEFAULT_CLASSDEF_ID";break;case 22:return this.popState(),this.pushState("CLASSDEFID"),41;break;case 23:return this.popState(),42;break;case 24:return this.pushState("CLASS"),44;break;case 25:return this.popState(),this.pushState("CLASS_STYLE"),45;break;case 26:return this.popState(),46;break;case 27:return this.pushState("STYLE_STMNT"),47;break;case 28:return this.popState(),this.pushState("STYLE_DEFINITION"),48;break;case 29:return this.popState(),49;break;case 30:return this.pushState("acc_title"),"acc_title";break;case 31:return this.popState(),"acc_title_value";break;case 32:return this.pushState("acc_descr"),"acc_descr";break;case 33:return this.popState(),"acc_descr_value";break;case 34:this.pushState("acc_descr_multiline");break;case 35:this.popState();break;case 36:return"acc_descr_multiline_value";case 37:return 30;case 38:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 39:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 40:return this.popState(),g.getLogger().debug("Lex: ))"),"NODE_DEND";break;case 41:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 42:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 43:return this.popState(),g.getLogger().debug("Lex: (-"),"NODE_DEND";break;case 44:return this.popState(),g.getLogger().debug("Lex: -)"),"NODE_DEND";break;case 45:return this.popState(),g.getLogger().debug("Lex: (("),"NODE_DEND";break;case 46:return this.popState(),g.getLogger().debug("Lex: ]]"),"NODE_DEND";break;case 47:return this.popState(),g.getLogger().debug("Lex: ("),"NODE_DEND";break;case 48:return this.popState(),g.getLogger().debug("Lex: ])"),"NODE_DEND";break;case 49:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 50:return this.popState(),g.getLogger().debug("Lex: /]"),"NODE_DEND";break;case 51:return this.popState(),g.getLogger().debug("Lex: )]"),"NODE_DEND";break;case 52:return this.popState(),g.getLogger().debug("Lex: )"),"NODE_DEND";break;case 53:return this.popState(),g.getLogger().debug("Lex: ]>"),"NODE_DEND";break;case 54:return this.popState(),g.getLogger().debug("Lex: ]"),"NODE_DEND";break;case 55:return g.getLogger().debug("Lexa: -)"),this.pushState("NODE"),36;break;case 56:return g.getLogger().debug("Lexa: (-"),this.pushState("NODE"),36;break;case 57:return g.getLogger().debug("Lexa: ))"),this.pushState("NODE"),36;break;case 58:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 59:return g.getLogger().debug("Lex: ((("),this.pushState("NODE"),36;break;case 60:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 61:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 62:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 63:return g.getLogger().debug("Lexc: >"),this.pushState("NODE"),36;break;case 64:return g.getLogger().debug("Lexa: (["),this.pushState("NODE"),36;break;case 65:return g.getLogger().debug("Lexa: )"),this.pushState("NODE"),36;break;case 66:return this.pushState("NODE"),36;break;case 67:return this.pushState("NODE"),36;break;case 68:return this.pushState("NODE"),36;break;case 69:return this.pushState("NODE"),36;break;case 70:return this.pushState("NODE"),36;break;case 71:return this.pushState("NODE"),36;break;case 72:return this.pushState("NODE"),36;break;case 73:return g.getLogger().debug("Lexa: ["),this.pushState("NODE"),36;break;case 74:return this.pushState("BLOCK_ARROW"),g.getLogger().debug("LEX ARR START"),38;break;case 75:return g.getLogger().debug("Lex: NODE_ID",x.yytext),32;break;case 76:return g.getLogger().debug("Lex: EOF",x.yytext),8;break;case 77:this.pushState("md_string");break;case 78:this.pushState("md_string");break;case 79:return"NODE_DESCR";case 80:this.popState();break;case 81:g.getLogger().debug("Lex: Starting string"),this.pushState("string");break;case 82:g.getLogger().debug("LEX ARR: Starting string"),this.pushState("string");break;case 83:return g.getLogger().debug("LEX: NODE_DESCR:",x.yytext),"NODE_DESCR";break;case 84:g.getLogger().debug("LEX POPPING"),this.popState();break;case 85:g.getLogger().debug("Lex: =>BAE"),this.pushState("ARROW_DIR");break;case 86:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (right): dir:",x.yytext),"DIR";break;case 87:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (left):",x.yytext),"DIR";break;case 88:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (x):",x.yytext),"DIR";break;case 89:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (y):",x.yytext),"DIR";break;case 90:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (up):",x.yytext),"DIR";break;case 91:return x.yytext=x.yytext.replace(/^,\s*/,""),g.getLogger().debug("Lex (down):",x.yytext),"DIR";break;case 92:return x.yytext="]>",g.getLogger().debug("Lex (ARROW_DIR end):",x.yytext),this.popState(),this.popState(),"BLOCK_ARROW_END";break;case 93:return g.getLogger().debug("Lex: LINK","#"+x.yytext+"#"),15;break;case 94:return g.getLogger().debug("Lex: LINK",x.yytext),15;break;case 95:return g.getLogger().debug("Lex: LINK",x.yytext),15;break;case 96:return g.getLogger().debug("Lex: LINK",x.yytext),15;break;case 97:return g.getLogger().debug("Lex: START_LINK",x.yytext),this.pushState("LLABEL"),16;break;case 98:return g.getLogger().debug("Lex: START_LINK",x.yytext),this.pushState("LLABEL"),16;break;case 99:return g.getLogger().debug("Lex: START_LINK",x.yytext),this.pushState("LLABEL"),16;break;case 100:this.pushState("md_string");break;case 101:return g.getLogger().debug("Lex: Starting string"),this.pushState("string"),"LINK_LABEL";break;case 102:return this.popState(),g.getLogger().debug("Lex: LINK","#"+x.yytext+"#"),15;break;case 103:return this.popState(),g.getLogger().debug("Lex: LINK",x.yytext),15;break;case 104:return this.popState(),g.getLogger().debug("Lex: LINK",x.yytext),15;break;case 105:return g.getLogger().debug("Lex: COLON",x.yytext),x.yytext=x.yytext.slice(1),27;break}},"anonymous"),rules:[/^(?:block-beta\b)/,/^(?:block\s+)/,/^(?:block\n+)/,/^(?:block:)/,/^(?:[\s]+)/,/^(?:[\n]+)/,/^(?:((\u000D\u000A)|(\u000A)))/,/^(?:columns\s+auto\b)/,/^(?:columns\s+[\d]+)/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:space[:]\d+)/,/^(?:space\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\s+)/,/^(?:DEFAULT\s+)/,/^(?:\w+\s+)/,/^(?:[^\n]*)/,/^(?:class\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:style\s+)/,/^(?:(\w+)+((,\s*\w+)*))/,/^(?:[^\n]*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:end\b\s*)/,/^(?:\(\(\()/,/^(?:\)\)\))/,/^(?:[\)]\))/,/^(?:\}\})/,/^(?:\})/,/^(?:\(-)/,/^(?:-\))/,/^(?:\(\()/,/^(?:\]\])/,/^(?:\()/,/^(?:\]\))/,/^(?:\\\])/,/^(?:\/\])/,/^(?:\)\])/,/^(?:[\)])/,/^(?:\]>)/,/^(?:[\]])/,/^(?:-\))/,/^(?:\(-)/,/^(?:\)\))/,/^(?:\))/,/^(?:\(\(\()/,/^(?:\(\()/,/^(?:\{\{)/,/^(?:\{)/,/^(?:>)/,/^(?:\(\[)/,/^(?:\()/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\[\\)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:\[)/,/^(?:<\[)/,/^(?:[^\(\[\n\-\)\{\}\s\<\>:]+)/,/^(?:$)/,/^(?:["][`])/,/^(?:["][`])/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["])/,/^(?:["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:\]>\s*\()/,/^(?:,?\s*right\s*)/,/^(?:,?\s*left\s*)/,/^(?:,?\s*x\s*)/,/^(?:,?\s*y\s*)/,/^(?:,?\s*up\s*)/,/^(?:,?\s*down\s*)/,/^(?:\)\s*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*~~[\~]+\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:["][`])/,/^(?:["])/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?::\d+)/],conditions:{STYLE_DEFINITION:{rules:[29],inclusive:!1},STYLE_STMNT:{rules:[28],inclusive:!1},CLASSDEFID:{rules:[23],inclusive:!1},CLASSDEF:{rules:[21,22],inclusive:!1},CLASS_STYLE:{rules:[26],inclusive:!1},CLASS:{rules:[25],inclusive:!1},LLABEL:{rules:[100,101,102,103,104],inclusive:!1},ARROW_DIR:{rules:[86,87,88,89,90,91,92],inclusive:!1},BLOCK_ARROW:{rules:[77,82,85],inclusive:!1},NODE:{rules:[38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,78,81],inclusive:!1},md_string:{rules:[10,11,79,80],inclusive:!1},space:{rules:[],inclusive:!1},string:{rules:[13,14,83,84],inclusive:!1},acc_descr_multiline:{rules:[35,36],inclusive:!1},acc_descr:{rules:[33],inclusive:!1},acc_title:{rules:[31],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,12,15,16,17,18,19,20,24,27,30,32,34,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,93,94,95,96,97,98,99,105],inclusive:!0}}};return A}();S.lexer=z;function O(){this.yy={}}return(0,n.eW)(O,"Parser"),O.prototype=S,S.Parser=O,new O}();wt.parser=wt;var jt=wt,tt=new Map,Lt=[],_t=new Map,ut="color",St="fill",vt="bgFill",Et=",",Wt=(0,n.nV)(),gt=new Map,Zt=(0,n.eW)(e=>n.SY.sanitizeText(e,Wt),"sanitizeText"),kt=(0,n.eW)(function(e,t=""){let a=gt.get(e);a||(a={id:e,styles:[],textStyles:[]},gt.set(e,a)),t!=null&&t.split(Et).forEach(i=>{const c=i.replace(/([^;]*);/,"$1").trim();if(RegExp(ut).exec(i)){const r=c.replace(St,vt).replace(ut,St);a.textStyles.push(r)}a.styles.push(c)})},"addStyleClass"),Q=(0,n.eW)(function(e,t=""){const a=tt.get(e);t!=null&&(a.styles=t.split(Et))},"addStyle2Node"),u=(0,n.eW)(function(e,t){e.split(",").forEach(function(a){let i=tt.get(a);if(i===void 0){const c=a.trim();i={id:c,type:"na",children:[]},tt.set(c,i)}i.classes||(i.classes=[]),i.classes.push(t)})},"setCssClass"),y=(0,n.eW)((e,t)=>{var c,s,r,l;const a=e.flat(),i=[];for(const o of a){if(o.label&&(o.label=Zt(o.label)),o.type==="classDef"){kt(o.id,o.css);continue}if(o.type==="applyClass"){u(o.id,(c=o==null?void 0:o.styleClass)!=null?c:"");continue}if(o.type==="applyStyles"){o!=null&&o.stylesStr&&Q(o.id,o==null?void 0:o.stylesStr);continue}if(o.type==="column-setting")t.columns=(s=o.columns)!=null?s:-1;else if(o.type==="edge"){const f=((r=_t.get(o.id))!=null?r:0)+1;_t.set(o.id,f),o.id=f+"-"+o.id,Lt.push(o)}else{o.label||(o.type==="composite"?o.label="":o.label=o.id);const f=tt.get(o.id);if(f===void 0?tt.set(o.id,o):(o.type!=="na"&&(f.type=o.type),o.label!==o.id&&(f.label=o.label)),o.children&&y(o.children,o),o.type==="space"){const d=(l=o.width)!=null?l:1;for(let b=0;b<d;b++){const w=(0,lt.Z)(o);w.id=w.id+"-"+b,tt.set(w.id,w),i.push(w)}}else f===void 0&&i.push(o)}}t.children=i},"populateBlockDatabase"),E=[],N={id:"root",type:"composite",children:[],columns:-1},B=(0,n.eW)(()=>{n.cM.debug("Clear called"),(0,n.ZH)(),N={id:"root",type:"composite",children:[],columns:-1},tt=new Map([["root",N]]),E=[],gt=new Map,Lt=[],_t=new Map},"clear");function R(e){switch(n.cM.debug("typeStr2Type",e),e){case"[]":return"square";case"()":return n.cM.debug("we have a round"),"round";case"(())":return"circle";case">]":return"rect_left_inv_arrow";case"{}":return"diamond";case"{{}}":return"hexagon";case"([])":return"stadium";case"[[]]":return"subroutine";case"[()]":return"cylinder";case"((()))":return"doublecircle";case"[//]":return"lean_right";case"[\\\\]":return"lean_left";case"[/\\]":return"trapezoid";case"[\\/]":return"inv_trapezoid";case"<[]>":return"block_arrow";default:return"na"}}(0,n.eW)(R,"typeStr2Type");function Z(e){switch(n.cM.debug("typeStr2Type",e),e){case"==":return"thick";default:return"normal"}}(0,n.eW)(Z,"edgeTypeStr2Type");function ft(e){switch(e.trim()){case"--x":return"arrow_cross";case"--o":return"arrow_circle";default:return"arrow_point"}}(0,n.eW)(ft,"edgeStrToEdgeData");var te=0,Ne=(0,n.eW)(()=>(te++,"id-"+Math.random().toString(36).substr(2,12)+"-"+te),"generateId"),We=(0,n.eW)(e=>{N.children=e,y(e,N),E=N.children},"setHierarchy"),Me=(0,n.eW)(e=>{const t=tt.get(e);return t?t.columns?t.columns:t.children?t.children.length:-1:-1},"getColumns"),Oe=(0,n.eW)(()=>[...tt.values()],"getBlocksFlat"),Te=(0,n.eW)(()=>E||[],"getBlocks"),Ie=(0,n.eW)(()=>Lt,"getEdges"),Be=(0,n.eW)(e=>tt.get(e),"getBlock"),Re=(0,n.eW)(e=>{tt.set(e.id,e)},"setBlock"),Ae=(0,n.eW)(()=>n.cM,"getLogger"),ze=(0,n.eW)(function(){return gt},"getClasses"),Pe={getConfig:(0,n.eW)(()=>(0,n.iE)().block,"getConfig"),typeStr2Type:R,edgeTypeStr2Type:Z,edgeStrToEdgeData:ft,getLogger:Ae,getBlocksFlat:Oe,getBlocks:Te,getEdges:Ie,setHierarchy:We,getBlock:Be,setBlock:Re,getColumns:Me,getClasses:ze,clear:B,generateId:Ne},Ye=Pe,Mt=(0,n.eW)((e,t)=>{const a=Pt.Z,i=a(e,"r"),c=a(e,"g"),s=a(e,"b");return Yt.Z(i,c,s,t)},"fade"),Fe=(0,n.eW)(e=>`.label {
    font-family: ${e.fontFamily};
    color: ${e.nodeTextColor||e.textColor};
  }
  .cluster-label text {
    fill: ${e.titleColor};
  }
  .cluster-label span,p {
    color: ${e.titleColor};
  }



  .label text,span,p {
    fill: ${e.nodeTextColor||e.textColor};
    color: ${e.nodeTextColor||e.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${e.mainBkg};
    stroke: ${e.nodeBorder};
    stroke-width: 1px;
  }
  .flowchart-label text {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }

  .arrowheadPath {
    fill: ${e.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${e.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${e.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${e.edgeLabelBackground};
    rect {
      opacity: 0.5;
      background-color: ${e.edgeLabelBackground};
      fill: ${e.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${Mt(e.edgeLabelBackground,.5)};
    // background-color:
  }

  .node .cluster {
    // fill: ${Mt(e.mainBkg,.5)};
    fill: ${Mt(e.clusterBkg,.5)};
    stroke: ${Mt(e.clusterBorder,.2)};
    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${e.titleColor};
  }

  .cluster span,p {
    color: ${e.titleColor};
  }
  /* .cluster div {
    color: ${e.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${e.fontFamily};
    font-size: 12px;
    background: ${e.tertiaryColor};
    border: 1px solid ${e.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${e.textColor};
  }
  ${(0,$.G)()}
`,"getStyles"),je=Fe,Ze=(0,n.eW)((e,t,a,i)=>{t.forEach(c=>{qe[c](e,a,i)})},"insertMarkers"),Ke=(0,n.eW)((e,t,a)=>{n.cM.trace("Making markers for ",a),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionStart").attr("class","marker extension "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 1,7 L18,13 V 1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-extensionEnd").attr("class","marker extension "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 1,1 V 13 L18,7 Z")},"extension"),Ue=(0,n.eW)((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionStart").attr("class","marker composition "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-compositionEnd").attr("class","marker composition "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"composition"),He=(0,n.eW)((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationStart").attr("class","marker aggregation "+t).attr("refX",18).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-aggregationEnd").attr("class","marker aggregation "+t).attr("refX",1).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L1,7 L9,1 Z")},"aggregation"),Ve=(0,n.eW)((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyStart").attr("class","marker dependency "+t).attr("refX",6).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("path").attr("d","M 5,7 L9,13 L1,7 L9,1 Z"),e.append("defs").append("marker").attr("id",a+"_"+t+"-dependencyEnd").attr("class","marker dependency "+t).attr("refX",13).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"dependency"),Xe=(0,n.eW)((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopStart").attr("class","marker lollipop "+t).attr("refX",13).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6),e.append("defs").append("marker").attr("id",a+"_"+t+"-lollipopEnd").attr("class","marker lollipop "+t).attr("refX",1).attr("refY",7).attr("markerWidth",190).attr("markerHeight",240).attr("orient","auto").append("circle").attr("stroke","black").attr("fill","transparent").attr("cx",7).attr("cy",7).attr("r",6)},"lollipop"),Ge=(0,n.eW)((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-pointEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",6).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-pointStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",4.5).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 5 L 10 10 L 10 0 z").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"point"),Je=(0,n.eW)((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-circleEnd").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",11).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-circleStart").attr("class","marker "+t).attr("viewBox","0 0 10 10").attr("refX",-1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("circle").attr("cx","5").attr("cy","5").attr("r","5").attr("class","arrowMarkerPath").style("stroke-width",1).style("stroke-dasharray","1,0")},"circle"),Qe=(0,n.eW)((e,t,a)=>{e.append("marker").attr("id",a+"_"+t+"-crossEnd").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",12).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0"),e.append("marker").attr("id",a+"_"+t+"-crossStart").attr("class","marker cross "+t).attr("viewBox","0 0 11 11").attr("refX",-1).attr("refY",5.2).attr("markerUnits","userSpaceOnUse").attr("markerWidth",11).attr("markerHeight",11).attr("orient","auto").append("path").attr("d","M 1,1 l 9,9 M 10,1 l -9,9").attr("class","arrowMarkerPath").style("stroke-width",2).style("stroke-dasharray","1,0")},"cross"),$e=(0,n.eW)((e,t,a)=>{e.append("defs").append("marker").attr("id",a+"_"+t+"-barbEnd").attr("refX",19).attr("refY",7).attr("markerWidth",20).attr("markerHeight",14).attr("markerUnits","strokeWidth").attr("orient","auto").append("path").attr("d","M 19,7 L9,13 L14,7 L9,1 Z")},"barb"),qe={extension:Ke,composition:Ue,aggregation:He,dependency:Ve,lollipop:Xe,point:Ge,circle:Je,cross:Qe,barb:$e},tr=Ze,U=(ve=(Se=(_e=(0,n.nV)())==null?void 0:_e.block)==null?void 0:Se.padding)!=null?ve:8;function ee(e,t){if(e===0||!Number.isInteger(e))throw new Error("Columns must be an integer !== 0.");if(t<0||!Number.isInteger(t))throw new Error("Position must be a non-negative integer."+t);if(e<0)return{px:t,py:0};if(e===1)return{px:0,py:t};const a=t%e,i=Math.floor(t/e);return{px:a,py:i}}(0,n.eW)(ee,"calculateBlockPosition");var er=(0,n.eW)(e=>{var i,c;let t=0,a=0;for(const s of e.children){const{width:r,height:l,x:o,y:f}=(i=s.size)!=null?i:{width:0,height:0,x:0,y:0};n.cM.debug("getMaxChildSize abc95 child:",s.id,"width:",r,"height:",l,"x:",o,"y:",f,s.type),s.type!=="space"&&(r>t&&(t=r/((c=e.widthInColumns)!=null?c:1)),l>a&&(a=l))}return{width:t,height:a}},"getMaxChildSize");function Ot(e,t,a=0,i=0){var r,l,o,f,d,b,w,_,k,T,S,z,O,A,m;n.cM.debug("setBlockSizes abc95 (start)",e.id,(r=e==null?void 0:e.size)==null?void 0:r.x,"block width =",e==null?void 0:e.size,"siblingWidth",a),(l=e==null?void 0:e.size)!=null&&l.width||(e.size={width:a,height:i,x:0,y:0});let c=0,s=0;if(((o=e.children)==null?void 0:o.length)>0){for(const D of e.children)Ot(D,t);const g=er(e);c=g.width,s=g.height,n.cM.debug("setBlockSizes abc95 maxWidth of",e.id,":s children is ",c,s);for(const D of e.children)D.size&&(n.cM.debug(`abc95 Setting size of children of ${e.id} id=${D.id} ${c} ${s} ${JSON.stringify(D.size)}`),D.size.width=c*((f=D.widthInColumns)!=null?f:1)+U*(((d=D.widthInColumns)!=null?d:1)-1),D.size.height=s,D.size.x=0,D.size.y=0,n.cM.debug(`abc95 updating size of ${e.id} children child:${D.id} maxWidth:${c} maxHeight:${s}`));for(const D of e.children)Ot(D,t,c,s);const x=(b=e.columns)!=null?b:-1;let L=0;for(const D of e.children)L+=(w=D.widthInColumns)!=null?w:1;let W=e.children.length;x>0&&x<L&&(W=x);const h=Math.ceil(L/W);let M=W*(c+U)+U,p=h*(s+U)+U;if(M<a){n.cM.debug(`Detected to small sibling: abc95 ${e.id} siblingWidth ${a} siblingHeight ${i} width ${M}`),M=a,p=i;const D=(a-W*U-U)/W,P=(i-h*U-U)/h;n.cM.debug("Size indata abc88",e.id,"childWidth",D,"maxWidth",c),n.cM.debug("Size indata abc88",e.id,"childHeight",P,"maxHeight",s),n.cM.debug("Size indata abc88 xSize",W,"padding",U);for(const C of e.children)C.size&&(C.size.width=D,C.size.height=P,C.size.x=0,C.size.y=0)}if(n.cM.debug(`abc95 (finale calc) ${e.id} xSize ${W} ySize ${h} columns ${x}${e.children.length} width=${Math.max(M,((_=e.size)==null?void 0:_.width)||0)}`),M<(((k=e==null?void 0:e.size)==null?void 0:k.width)||0)){M=((T=e==null?void 0:e.size)==null?void 0:T.width)||0;const D=x>0?Math.min(e.children.length,x):e.children.length;if(D>0){const P=(M-D*U-U)/D;n.cM.debug("abc95 (growing to fit) width",e.id,M,(S=e.size)==null?void 0:S.width,P);for(const C of e.children)C.size&&(C.size.width=P)}}e.size={width:M,height:p,x:0,y:0}}n.cM.debug("setBlockSizes abc94 (done)",e.id,(z=e==null?void 0:e.size)==null?void 0:z.x,(O=e==null?void 0:e.size)==null?void 0:O.width,(A=e==null?void 0:e.size)==null?void 0:A.y,(m=e==null?void 0:e.size)==null?void 0:m.height)}(0,n.eW)(Ot,"setBlockSizes");function Kt(e,t){var i,c,s,r,l,o,f,d,b,w,_,k,T,S,z,O,A,m,g,x,L,W;n.cM.debug(`abc85 layout blocks (=>layoutBlocks) ${e.id} x: ${(i=e==null?void 0:e.size)==null?void 0:i.x} y: ${(c=e==null?void 0:e.size)==null?void 0:c.y} width: ${(s=e==null?void 0:e.size)==null?void 0:s.width}`);const a=(r=e.columns)!=null?r:-1;if(n.cM.debug("layoutBlocks columns abc95",e.id,"=>",a,e),e.children&&e.children.length>0){const h=(f=(o=(l=e==null?void 0:e.children[0])==null?void 0:l.size)==null?void 0:o.width)!=null?f:0,M=e.children.length*h+(e.children.length-1)*U;n.cM.debug("widthOfChildren 88",M,"posX");let p=0;n.cM.debug("abc91 block?.size?.x",e.id,(d=e==null?void 0:e.size)==null?void 0:d.x);let D=(b=e==null?void 0:e.size)!=null&&b.x?((w=e==null?void 0:e.size)==null?void 0:w.x)+(-((_=e==null?void 0:e.size)==null?void 0:_.width)/2||0):-U,P=0;for(const C of e.children){const V=e;if(!C.size)continue;const{width:pt,height:Rt}=C.size,{px:H,py:ot}=ee(a,p);if(ot!=P&&(P=ot,D=(k=e==null?void 0:e.size)!=null&&k.x?((T=e==null?void 0:e.size)==null?void 0:T.x)+(-((S=e==null?void 0:e.size)==null?void 0:S.width)/2||0):-U,n.cM.debug("New row in layout for block",e.id," and child ",C.id,P)),n.cM.debug(`abc89 layout blocks (child) id: ${C.id} Pos: ${p} (px, py) ${H},${ot} (${(z=V==null?void 0:V.size)==null?void 0:z.x},${(O=V==null?void 0:V.size)==null?void 0:O.y}) parent: ${V.id} width: ${pt}${U}`),V.size){const xt=pt/2;C.size.x=D+U+xt,n.cM.debug(`abc91 layout blocks (calc) px, pyid:${C.id} startingPos=X${D} new startingPosX${C.size.x} ${xt} padding=${U} width=${pt} halfWidth=${xt} => x:${C.size.x} y:${C.size.y} ${C.widthInColumns} (width * (child?.w || 1)) / 2 ${pt*((A=C==null?void 0:C.widthInColumns)!=null?A:1)/2}`),D=C.size.x+xt,C.size.y=V.size.y-V.size.height/2+ot*(Rt+U)+Rt/2+U,n.cM.debug(`abc88 layout blocks (calc) px, pyid:${C.id}startingPosX${D}${U}${xt}=>x:${C.size.x}y:${C.size.y}${C.widthInColumns}(width * (child?.w || 1)) / 2${pt*((m=C==null?void 0:C.widthInColumns)!=null?m:1)/2}`)}C.children&&Kt(C,t),p+=(g=C==null?void 0:C.widthInColumns)!=null?g:1,n.cM.debug("abc88 columnsPos",C,p)}}n.cM.debug(`layout blocks (<==layoutBlocks) ${e.id} x: ${(x=e==null?void 0:e.size)==null?void 0:x.x} y: ${(L=e==null?void 0:e.size)==null?void 0:L.y} width: ${(W=e==null?void 0:e.size)==null?void 0:W.width}`)}(0,n.eW)(Kt,"layoutBlocks");function Ut(e,{minX:t,minY:a,maxX:i,maxY:c}={minX:0,minY:0,maxX:0,maxY:0}){if(e.size&&e.id!=="root"){const{x:s,y:r,width:l,height:o}=e.size;s-l/2<t&&(t=s-l/2),r-o/2<a&&(a=r-o/2),s+l/2>i&&(i=s+l/2),r+o/2>c&&(c=r+o/2)}if(e.children)for(const s of e.children)({minX:t,minY:a,maxX:i,maxY:c}=Ut(s,{minX:t,minY:a,maxX:i,maxY:c}));return{minX:t,minY:a,maxX:i,maxY:c}}(0,n.eW)(Ut,"findBounds");function re(e){const t=e.getBlock("root");if(!t)return;Ot(t,e,0,0),Kt(t,e),n.cM.debug("getBlocks",JSON.stringify(t,null,2));const{minX:a,minY:i,maxX:c,maxY:s}=Ut(t),r=s-i,l=c-a;return{x:a,y:i,width:l,height:r}}(0,n.eW)(re,"layout");function Ht(e,t){t&&e.attr("style",t)}(0,n.eW)(Ht,"applyStyle");function ae(e){const t=(0,F.Ys)(document.createElementNS("http://www.w3.org/2000/svg","foreignObject")),a=t.append("xhtml:div"),i=e.label,c=e.isNode?"nodeLabel":"edgeLabel",s=a.append("span");return s.html(i),Ht(s,e.labelStyle),s.attr("class",c),Ht(a,e.labelStyle),a.style("display","inline-block"),a.style("white-space","nowrap"),a.attr("xmlns","http://www.w3.org/1999/xhtml"),t.node()}(0,n.eW)(ae,"addHtmlLabel");var rr=(0,n.eW)((e,t,a,i)=>I(this,null,function*(){let c=e||"";if(typeof c=="object"&&(c=c[0]),(0,n.ku)((0,n.nV)().flowchart.htmlLabels)){c=c.replace(/\\n|\n/g,"<br />"),n.cM.debug("vertexText"+c);const s=yield(0,st.EY)((0,X.SH)(c)),r={isNode:i,label:s,labelStyle:t.replace("fill:","color:")};return ae(r)}else{const s=document.createElementNS("http://www.w3.org/2000/svg","text");s.setAttribute("style",t.replace("color:","fill:"));let r=[];typeof c=="string"?r=c.split(/\\n|\n|<br\s*\/?>/gi):Array.isArray(c)?r=c:r=[];for(const l of r){const o=document.createElementNS("http://www.w3.org/2000/svg","tspan");o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),o.setAttribute("dy","1em"),o.setAttribute("x","0"),a?o.setAttribute("class","title-row"):o.setAttribute("class","row"),o.textContent=l.trim(),s.appendChild(o)}return s}}),"createLabel"),ct=rr,ar=(0,n.eW)((e,t,a,i,c)=>{t.arrowTypeStart&&se(e,"start",t.arrowTypeStart,a,i,c),t.arrowTypeEnd&&se(e,"end",t.arrowTypeEnd,a,i,c)},"addEdgeMarkers"),sr={arrow_cross:"cross",arrow_point:"point",arrow_barb:"barb",arrow_circle:"circle",aggregation:"aggregation",extension:"extension",composition:"composition",dependency:"dependency",lollipop:"lollipop"},se=(0,n.eW)((e,t,a,i,c,s)=>{const r=sr[a];if(!r){n.cM.warn(`Unknown arrow type: ${a}`);return}const l=t==="start"?"Start":"End";e.attr(`marker-${t}`,`url(${i}#${c}_${s}-${r}${l})`)},"addEdgeMarker"),Vt={},q={},ir=(0,n.eW)((e,t)=>I(this,null,function*(){const a=(0,n.nV)(),i=(0,n.ku)(a.flowchart.htmlLabels),c=t.labelType==="markdown"?(0,st.rw)(e,t.label,{style:t.labelStyle,useHtmlLabels:i,addSvgBackground:!0},a):yield ct(t.label,t.labelStyle),s=e.insert("g").attr("class","edgeLabel"),r=s.insert("g").attr("class","label");r.node().appendChild(c);let l=c.getBBox();if(i){const f=c.children[0],d=(0,F.Ys)(c);l=f.getBoundingClientRect(),d.attr("width",l.width),d.attr("height",l.height)}r.attr("transform","translate("+-l.width/2+", "+-l.height/2+")"),Vt[t.id]=s,t.width=l.width,t.height=l.height;let o;if(t.startLabelLeft){const f=yield ct(t.startLabelLeft,t.labelStyle),d=e.insert("g").attr("class","edgeTerminals"),b=d.insert("g").attr("class","inner");o=b.node().appendChild(f);const w=f.getBBox();b.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),q[t.id]||(q[t.id]={}),q[t.id].startLeft=d,Dt(o,t.startLabelLeft)}if(t.startLabelRight){const f=yield ct(t.startLabelRight,t.labelStyle),d=e.insert("g").attr("class","edgeTerminals"),b=d.insert("g").attr("class","inner");o=d.node().appendChild(f),b.node().appendChild(f);const w=f.getBBox();b.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),q[t.id]||(q[t.id]={}),q[t.id].startRight=d,Dt(o,t.startLabelRight)}if(t.endLabelLeft){const f=yield ct(t.endLabelLeft,t.labelStyle),d=e.insert("g").attr("class","edgeTerminals"),b=d.insert("g").attr("class","inner");o=b.node().appendChild(f);const w=f.getBBox();b.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),d.node().appendChild(f),q[t.id]||(q[t.id]={}),q[t.id].endLeft=d,Dt(o,t.endLabelLeft)}if(t.endLabelRight){const f=yield ct(t.endLabelRight,t.labelStyle),d=e.insert("g").attr("class","edgeTerminals"),b=d.insert("g").attr("class","inner");o=b.node().appendChild(f);const w=f.getBBox();b.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),d.node().appendChild(f),q[t.id]||(q[t.id]={}),q[t.id].endRight=d,Dt(o,t.endLabelRight)}return c}),"insertEdgeLabel");function Dt(e,t){(0,n.nV)().flowchart.htmlLabels&&e&&(e.style.width=t.length*9+"px",e.style.height="12px")}(0,n.eW)(Dt,"setTerminalWidth");var nr=(0,n.eW)((e,t)=>{n.cM.debug("Moving label abc88 ",e.id,e.label,Vt[e.id],t);let a=t.updatedPath?t.updatedPath:t.originalPath;const i=(0,n.nV)(),{subGraphTitleTotalMargin:c}=(0,at.L)(i);if(e.label){const s=Vt[e.id];let r=e.x,l=e.y;if(a){const o=X.w8.calcLabelPosition(a);n.cM.debug("Moving label "+e.label+" from (",r,",",l,") to (",o.x,",",o.y,") abc88"),t.updatedPath&&(r=o.x,l=o.y)}s.attr("transform",`translate(${r}, ${l+c/2})`)}if(e.startLabelLeft){const s=q[e.id].startLeft;let r=e.x,l=e.y;if(a){const o=X.w8.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_left",a);r=o.x,l=o.y}s.attr("transform",`translate(${r}, ${l})`)}if(e.startLabelRight){const s=q[e.id].startRight;let r=e.x,l=e.y;if(a){const o=X.w8.calcTerminalLabelPosition(e.arrowTypeStart?10:0,"start_right",a);r=o.x,l=o.y}s.attr("transform",`translate(${r}, ${l})`)}if(e.endLabelLeft){const s=q[e.id].endLeft;let r=e.x,l=e.y;if(a){const o=X.w8.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_left",a);r=o.x,l=o.y}s.attr("transform",`translate(${r}, ${l})`)}if(e.endLabelRight){const s=q[e.id].endRight;let r=e.x,l=e.y;if(a){const o=X.w8.calcTerminalLabelPosition(e.arrowTypeEnd?10:0,"end_right",a);r=o.x,l=o.y}s.attr("transform",`translate(${r}, ${l})`)}},"positionEdgeLabel"),lr=(0,n.eW)((e,t)=>{const a=e.x,i=e.y,c=Math.abs(t.x-a),s=Math.abs(t.y-i),r=e.width/2,l=e.height/2;return c>=r||s>=l},"outsideNode"),cr=(0,n.eW)((e,t,a)=>{n.cM.debug(`intersection calc abc89:
  outsidePoint: ${JSON.stringify(t)}
  insidePoint : ${JSON.stringify(a)}
  node        : x:${e.x} y:${e.y} w:${e.width} h:${e.height}`);const i=e.x,c=e.y,s=Math.abs(i-a.x),r=e.width/2;let l=a.x<t.x?r-s:r+s;const o=e.height/2,f=Math.abs(t.y-a.y),d=Math.abs(t.x-a.x);if(Math.abs(c-t.y)*r>Math.abs(i-t.x)*o){let b=a.y<t.y?t.y-o-c:c-o-t.y;l=d*b/f;const w={x:a.x<t.x?a.x+l:a.x-d+l,y:a.y<t.y?a.y+f-b:a.y-f+b};return l===0&&(w.x=t.x,w.y=t.y),d===0&&(w.x=t.x),f===0&&(w.y=t.y),n.cM.debug(`abc89 topp/bott calc, Q ${f}, q ${b}, R ${d}, r ${l}`,w),w}else{a.x<t.x?l=t.x-r-i:l=i-r-t.x;let b=f*l/d,w=a.x<t.x?a.x+d-l:a.x-d+l,_=a.y<t.y?a.y+b:a.y-b;return n.cM.debug(`sides calc abc89, Q ${f}, q ${b}, R ${d}, r ${l}`,{_x:w,_y:_}),l===0&&(w=t.x,_=t.y),d===0&&(w=t.x),f===0&&(_=t.y),{x:w,y:_}}},"intersection"),ie=(0,n.eW)((e,t)=>{n.cM.debug("abc88 cutPathAtIntersect",e,t);let a=[],i=e[0],c=!1;return e.forEach(s=>{if(!lr(t,s)&&!c){const r=cr(t,i,s);let l=!1;a.forEach(o=>{l=l||o.x===r.x&&o.y===r.y}),a.some(o=>o.x===r.x&&o.y===r.y)||a.push(r),c=!0}else i=s,c||a.push(s)}),a},"cutPathAtIntersect"),or=(0,n.eW)(function(e,t,a,i,c,s,r){let l=a.points;n.cM.debug("abc88 InsertEdge: edge=",a,"e=",t);let o=!1;const f=s.node(t.v);var d=s.node(t.w);d!=null&&d.intersect&&(f!=null&&f.intersect)&&(l=l.slice(1,a.points.length-1),l.unshift(f.intersect(l[0])),l.push(d.intersect(l[l.length-1]))),a.toCluster&&(n.cM.debug("to cluster abc88",i[a.toCluster]),l=ie(a.points,i[a.toCluster].node),o=!0),a.fromCluster&&(n.cM.debug("from cluster abc88",i[a.fromCluster]),l=ie(l.reverse(),i[a.fromCluster].node).reverse(),o=!0);const b=l.filter(m=>!Number.isNaN(m.y));let w=F.$0Z;a.curve&&(c==="graph"||c==="flowchart")&&(w=a.curve);const{x:_,y:k}=(0,rt.o)(a),T=(0,F.jvg)().x(_).y(k).curve(w);let S;switch(a.thickness){case"normal":S="edge-thickness-normal";break;case"thick":S="edge-thickness-thick";break;case"invisible":S="edge-thickness-thick";break;default:S=""}switch(a.pattern){case"solid":S+=" edge-pattern-solid";break;case"dotted":S+=" edge-pattern-dotted";break;case"dashed":S+=" edge-pattern-dashed";break}const z=e.append("path").attr("d",T(b)).attr("id",a.id).attr("class"," "+S+(a.classes?" "+a.classes:"")).attr("style",a.style);let O="";((0,n.nV)().flowchart.arrowMarkerAbsolute||(0,n.nV)().state.arrowMarkerAbsolute)&&(O=(0,n.Gr)(!0)),ar(z,a,O,r,c);let A={};return o&&(A.updatedPath=l),A.originalPath=a.points,A},"insertEdge"),hr=(0,n.eW)(e=>{const t=new Set;for(const a of e)switch(a){case"x":t.add("right"),t.add("left");break;case"y":t.add("up"),t.add("down");break;default:t.add(a);break}return t},"expandAndDeduplicateDirections"),dr=(0,n.eW)((e,t,a)=>{const i=hr(e),c=2,s=t.height+2*a.padding,r=s/c,l=t.width+2*r+a.padding,o=a.padding/2;return i.has("right")&&i.has("left")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:r,y:0},{x:l/2,y:2*o},{x:l-r,y:0},{x:l,y:0},{x:l,y:-s/3},{x:l+2*o,y:-s/2},{x:l,y:-2*s/3},{x:l,y:-s},{x:l-r,y:-s},{x:l/2,y:-s-2*o},{x:r,y:-s},{x:0,y:-s},{x:0,y:-2*s/3},{x:-2*o,y:-s/2},{x:0,y:-s/3}]:i.has("right")&&i.has("left")&&i.has("up")?[{x:r,y:0},{x:l-r,y:0},{x:l,y:-s/2},{x:l-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}]:i.has("right")&&i.has("left")&&i.has("down")?[{x:0,y:0},{x:r,y:-s},{x:l-r,y:-s},{x:l,y:0}]:i.has("right")&&i.has("up")&&i.has("down")?[{x:0,y:0},{x:l,y:-r},{x:l,y:-s+r},{x:0,y:-s}]:i.has("left")&&i.has("up")&&i.has("down")?[{x:l,y:0},{x:0,y:-r},{x:0,y:-s+r},{x:l,y:-s}]:i.has("right")&&i.has("left")?[{x:r,y:0},{x:r,y:-o},{x:l-r,y:-o},{x:l-r,y:0},{x:l,y:-s/2},{x:l-r,y:-s},{x:l-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")&&i.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:r,y:-o},{x:r,y:-s+o},{x:0,y:-s+o},{x:l/2,y:-s},{x:l,y:-s+o},{x:l-r,y:-s+o},{x:l-r,y:-o},{x:l,y:-o}]:i.has("right")&&i.has("up")?[{x:0,y:0},{x:l,y:-r},{x:0,y:-s}]:i.has("right")&&i.has("down")?[{x:0,y:0},{x:l,y:0},{x:0,y:-s}]:i.has("left")&&i.has("up")?[{x:l,y:0},{x:0,y:-r},{x:l,y:-s}]:i.has("left")&&i.has("down")?[{x:l,y:0},{x:0,y:0},{x:l,y:-s}]:i.has("right")?[{x:r,y:-o},{x:r,y:-o},{x:l-r,y:-o},{x:l-r,y:0},{x:l,y:-s/2},{x:l-r,y:-s},{x:l-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s+o}]:i.has("left")?[{x:r,y:0},{x:r,y:-o},{x:l-r,y:-o},{x:l-r,y:-s+o},{x:r,y:-s+o},{x:r,y:-s},{x:0,y:-s/2}]:i.has("up")?[{x:r,y:-o},{x:r,y:-s+o},{x:0,y:-s+o},{x:l/2,y:-s},{x:l,y:-s+o},{x:l-r,y:-s+o},{x:l-r,y:-o}]:i.has("down")?[{x:l/2,y:0},{x:0,y:-o},{x:r,y:-o},{x:r,y:-s+o},{x:l-r,y:-s+o},{x:l-r,y:-o},{x:l,y:-o}]:[{x:0,y:0}]},"getArrowPoints");function ne(e,t){return e.intersect(t)}(0,n.eW)(ne,"intersectNode");var ur=ne;function le(e,t,a,i){var c=e.x,s=e.y,r=c-i.x,l=s-i.y,o=Math.sqrt(t*t*l*l+a*a*r*r),f=Math.abs(t*a*r/o);i.x<c&&(f=-f);var d=Math.abs(t*a*l/o);return i.y<s&&(d=-d),{x:c+f,y:s+d}}(0,n.eW)(le,"intersectEllipse");var ce=le;function oe(e,t,a){return ce(e,t,t,a)}(0,n.eW)(oe,"intersectCircle");var gr=oe;function he(e,t,a,i){var c,s,r,l,o,f,d,b,w,_,k,T,S,z,O;if(c=t.y-e.y,r=e.x-t.x,o=t.x*e.y-e.x*t.y,w=c*a.x+r*a.y+o,_=c*i.x+r*i.y+o,!(w!==0&&_!==0&&Xt(w,_))&&(s=i.y-a.y,l=a.x-i.x,f=i.x*a.y-a.x*i.y,d=s*e.x+l*e.y+f,b=s*t.x+l*t.y+f,!(d!==0&&b!==0&&Xt(d,b))&&(k=c*l-s*r,k!==0)))return T=Math.abs(k/2),S=r*f-l*o,z=S<0?(S-T)/k:(S+T)/k,S=s*o-c*f,O=S<0?(S-T)/k:(S+T)/k,{x:z,y:O}}(0,n.eW)(he,"intersectLine");function Xt(e,t){return e*t>0}(0,n.eW)(Xt,"sameSign");var pr=he,fr=de;function de(e,t,a){var i=e.x,c=e.y,s=[],r=Number.POSITIVE_INFINITY,l=Number.POSITIVE_INFINITY;typeof t.forEach=="function"?t.forEach(function(k){r=Math.min(r,k.x),l=Math.min(l,k.y)}):(r=Math.min(r,t.x),l=Math.min(l,t.y));for(var o=i-e.width/2-r,f=c-e.height/2-l,d=0;d<t.length;d++){var b=t[d],w=t[d<t.length-1?d+1:0],_=pr(e,a,{x:o+b.x,y:f+b.y},{x:o+w.x,y:f+w.y});_&&s.push(_)}return s.length?(s.length>1&&s.sort(function(k,T){var S=k.x-a.x,z=k.y-a.y,O=Math.sqrt(S*S+z*z),A=T.x-a.x,m=T.y-a.y,g=Math.sqrt(A*A+m*m);return O<g?-1:O===g?0:1}),s[0]):e}(0,n.eW)(de,"intersectPolygon");var xr=(0,n.eW)((e,t)=>{var a=e.x,i=e.y,c=t.x-a,s=t.y-i,r=e.width/2,l=e.height/2,o,f;return Math.abs(s)*r>Math.abs(c)*l?(s<0&&(l=-l),o=s===0?0:l*c/s,f=l):(c<0&&(r=-r),o=r,f=c===0?0:r*s/c),{x:a+o,y:i+f}},"intersectRect"),yr=xr,j={node:ur,circle:gr,ellipse:ce,polygon:fr,rect:yr},G=(0,n.eW)((e,t,a,i)=>I(this,null,function*(){const c=(0,n.nV)();let s;const r=t.useHtmlLabels||(0,n.ku)(c.flowchart.htmlLabels);a?s=a:s="node default";const l=e.insert("g").attr("class",s).attr("id",t.domId||t.id),o=l.insert("g").attr("class","label").attr("style",t.labelStyle);let f;t.labelText===void 0?f="":f=typeof t.labelText=="string"?t.labelText:t.labelText[0];const d=o.node();let b;t.labelType==="markdown"?b=(0,st.rw)(o,(0,n.oO)((0,X.SH)(f),c),{useHtmlLabels:r,width:t.width||c.flowchart.wrappingWidth,classes:"markdown-node-label"},c):b=d.appendChild(yield ct((0,n.oO)((0,X.SH)(f),c),t.labelStyle,!1,i));let w=b.getBBox();const _=t.padding/2;if((0,n.ku)(c.flowchart.htmlLabels)){const k=b.children[0],T=(0,F.Ys)(b),S=k.getElementsByTagName("img");if(S){const z=f.replace(/<img[^>]*>/g,"").trim()==="";yield Promise.all([...S].map(O=>new Promise(A=>{function m(){if(O.style.display="flex",O.style.flexDirection="column",z){const g=c.fontSize?c.fontSize:window.getComputedStyle(document.body).fontSize,L=parseInt(g,10)*5+"px";O.style.minWidth=L,O.style.maxWidth=L}else O.style.width="100%";A(O)}(0,n.eW)(m,"setupImage"),setTimeout(()=>{O.complete&&m()}),O.addEventListener("error",m),O.addEventListener("load",m)})))}w=k.getBoundingClientRect(),T.attr("width",w.width),T.attr("height",w.height)}return r?o.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"):o.attr("transform","translate(0, "+-w.height/2+")"),t.centerLabel&&o.attr("transform","translate("+-w.width/2+", "+-w.height/2+")"),o.insert("rect",":first-child"),{shapeSvg:l,bbox:w,halfPadding:_,label:o}}),"labelHelper"),K=(0,n.eW)((e,t)=>{const a=t.node().getBBox();e.width=a.width,e.height=a.height},"updateNodeBounds");function dt(e,t,a,i){return e.insert("polygon",":first-child").attr("points",i.map(function(c){return c.x+","+c.y}).join(" ")).attr("class","label-container").attr("transform","translate("+-t/2+","+a/2+")")}(0,n.eW)(dt,"insertPolygonShape");var br=(0,n.eW)((e,t)=>I(this,null,function*(){t.useHtmlLabels||(0,n.nV)().flowchart.htmlLabels||(t.centerLabel=!0);const{shapeSvg:i,bbox:c,halfPadding:s}=yield G(e,t,"node "+t.classes,!0);n.cM.info("Classes = ",t.classes);const r=i.insert("rect",":first-child");return r.attr("rx",t.rx).attr("ry",t.ry).attr("x",-c.width/2-s).attr("y",-c.height/2-s).attr("width",c.width+t.padding).attr("height",c.height+t.padding),K(t,r),t.intersect=function(l){return j.rect(t,l)},i}),"note"),mr=br,ue=(0,n.eW)(e=>e?" "+e:"","formatClass"),it=(0,n.eW)((e,t)=>`${t||"node default"}${ue(e.classes)} ${ue(e.class)}`,"getClassesFromNode"),ge=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=c+s,l=[{x:r/2,y:0},{x:r,y:-r/2},{x:r/2,y:-r},{x:0,y:-r/2}];n.cM.info("Question main (Circle)");const o=dt(a,r,r,l);return o.attr("style",t.style),K(t,o),t.intersect=function(f){return n.cM.warn("Intersect called"),j.polygon(t,l,f)},a}),"question"),wr=(0,n.eW)((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=28,c=[{x:0,y:i/2},{x:i/2,y:0},{x:0,y:-i/2},{x:-i/2,y:0}];return a.insert("polygon",":first-child").attr("points",c.map(function(r){return r.x+","+r.y}).join(" ")).attr("class","state-start").attr("r",7).attr("width",28).attr("height",28),t.width=28,t.height=28,t.intersect=function(r){return j.circle(t,14,r)},a},"choice"),Lr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=4,s=i.height+t.padding,r=s/c,l=i.width+2*r+t.padding,o=[{x:r,y:0},{x:l-r,y:0},{x:l,y:-s/2},{x:l-r,y:-s},{x:r,y:-s},{x:0,y:-s/2}],f=dt(a,l,s,o);return f.attr("style",t.style),K(t,f),t.intersect=function(d){return j.polygon(t,o,d)},a}),"hexagon"),_r=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,void 0,!0),c=2,s=i.height+2*t.padding,r=s/c,l=i.width+2*r+t.padding,o=dr(t.directions,i,t),f=dt(a,l,s,o);return f.attr("style",t.style),K(t,f),t.intersect=function(d){return j.polygon(t,o,d)},a}),"block_arrow"),Sr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:-s/2,y:0},{x:c,y:0},{x:c,y:-s},{x:-s/2,y:-s},{x:0,y:-s/2}];return dt(a,c,s,r).attr("style",t.style),t.width=c+s,t.height=s,t.intersect=function(o){return j.polygon(t,r,o)},a}),"rect_left_inv_arrow"),vr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:c-s/6,y:0},{x:c+2*s/6,y:-s},{x:s/6,y:-s}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"lean_right"),Er=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:2*s/6,y:0},{x:c+s/6,y:0},{x:c-2*s/6,y:-s},{x:-s/6,y:-s}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"lean_left"),kr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:-2*s/6,y:0},{x:c+2*s/6,y:0},{x:c-s/6,y:-s},{x:s/6,y:-s}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"trapezoid"),Dr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:s/6,y:0},{x:c-s/6,y:0},{x:c+2*s/6,y:-s},{x:-2*s/6,y:-s}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"inv_trapezoid"),Cr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:c+s/2,y:0},{x:c,y:-s/2},{x:c+s/2,y:-s},{x:0,y:-s}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"rect_right_inv_arrow"),Nr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=c/2,r=s/(2.5+c/50),l=i.height+r+t.padding,o="M 0,"+r+" a "+s+","+r+" 0,0,0 "+c+" 0 a "+s+","+r+" 0,0,0 "+-c+" 0 l 0,"+l+" a "+s+","+r+" 0,0,0 "+c+" 0 l 0,"+-l,f=a.attr("label-offset-y",r).insert("path",":first-child").attr("style",t.style).attr("d",o).attr("transform","translate("+-c/2+","+-(l/2+r)+")");return K(t,f),t.intersect=function(d){const b=j.rect(t,d),w=b.x-t.x;if(s!=0&&(Math.abs(w)<t.width/2||Math.abs(w)==t.width/2&&Math.abs(b.y-t.y)>t.height/2-r)){let _=r*r*(1-w*w/(s*s));_!=0&&(_=Math.sqrt(_)),_=r-_,d.y-t.y>0&&(_=-_),b.y+=_}return b},a}),"cylinder"),Wr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i,halfPadding:c}=yield G(e,t,"node "+t.classes+" "+t.class,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,l=t.positioned?t.height:i.height+t.padding,o=t.positioned?-r/2:-i.width/2-c,f=t.positioned?-l/2:-i.height/2-c;if(s.attr("class","basic label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",f).attr("width",r).attr("height",l),t.props){const d=new Set(Object.keys(t.props));t.props.borders&&(Tt(s,t.props.borders,r,l),d.delete("borders")),d.forEach(b=>{n.cM.warn(`Unknown node property ${b}`)})}return K(t,s),t.intersect=function(d){return j.rect(t,d)},a}),"rect"),Mr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i,halfPadding:c}=yield G(e,t,"node "+t.classes,!0),s=a.insert("rect",":first-child"),r=t.positioned?t.width:i.width+t.padding,l=t.positioned?t.height:i.height+t.padding,o=t.positioned?-r/2:-i.width/2-c,f=t.positioned?-l/2:-i.height/2-c;if(s.attr("class","basic cluster composite label-container").attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("x",o).attr("y",f).attr("width",r).attr("height",l),t.props){const d=new Set(Object.keys(t.props));t.props.borders&&(Tt(s,t.props.borders,r,l),d.delete("borders")),d.forEach(b=>{n.cM.warn(`Unknown node property ${b}`)})}return K(t,s),t.intersect=function(d){return j.rect(t,d)},a}),"composite"),Or=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a}=yield G(e,t,"label",!0);n.cM.trace("Classes = ",t.class);const i=a.insert("rect",":first-child"),c=0,s=0;if(i.attr("width",c).attr("height",s),a.attr("class","label edgeLabel"),t.props){const r=new Set(Object.keys(t.props));t.props.borders&&(Tt(i,t.props.borders,c,s),r.delete("borders")),r.forEach(l=>{n.cM.warn(`Unknown node property ${l}`)})}return K(t,i),t.intersect=function(r){return j.rect(t,r)},a}),"labelRect");function Tt(e,t,a,i){const c=[],s=(0,n.eW)(l=>{c.push(l,0)},"addBorder"),r=(0,n.eW)(l=>{c.push(0,l)},"skipBorder");t.includes("t")?(n.cM.debug("add top border"),s(a)):r(a),t.includes("r")?(n.cM.debug("add right border"),s(i)):r(i),t.includes("b")?(n.cM.debug("add bottom border"),s(a)):r(a),t.includes("l")?(n.cM.debug("add left border"),s(i)):r(i),e.attr("stroke-dasharray",c.join(" "))}(0,n.eW)(Tt,"applyNodePropertyBorders");var Tr=(0,n.eW)((e,t)=>I(this,null,function*(){let a;t.classes?a="node "+t.classes:a="node default";const i=e.insert("g").attr("class",a).attr("id",t.domId||t.id),c=i.insert("rect",":first-child"),s=i.insert("line"),r=i.insert("g").attr("class","label"),l=t.labelText.flat?t.labelText.flat():t.labelText;let o="";typeof l=="object"?o=l[0]:o=l,n.cM.info("Label text abc79",o,l,typeof l=="object");const f=r.node().appendChild(yield ct(o,t.labelStyle,!0,!0));let d={width:0,height:0};if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const T=f.children[0],S=(0,F.Ys)(f);d=T.getBoundingClientRect(),S.attr("width",d.width),S.attr("height",d.height)}n.cM.info("Text 2",l);const b=l.slice(1,l.length);let w=f.getBBox();const _=r.node().appendChild(yield ct(b.join?b.join("<br/>"):b,t.labelStyle,!0,!0));if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const T=_.children[0],S=(0,F.Ys)(_);d=T.getBoundingClientRect(),S.attr("width",d.width),S.attr("height",d.height)}const k=t.padding/2;return(0,F.Ys)(_).attr("transform","translate( "+(d.width>w.width?0:(w.width-d.width)/2)+", "+(w.height+k+5)+")"),(0,F.Ys)(f).attr("transform","translate( "+(d.width<w.width?0:-(w.width-d.width)/2)+", 0)"),d=r.node().getBBox(),r.attr("transform","translate("+-d.width/2+", "+(-d.height/2-k+3)+")"),c.attr("class","outer title-state").attr("x",-d.width/2-k).attr("y",-d.height/2-k).attr("width",d.width+t.padding).attr("height",d.height+t.padding),s.attr("class","divider").attr("x1",-d.width/2-k).attr("x2",d.width/2+k).attr("y1",-d.height/2-k+w.height+k).attr("y2",-d.height/2-k+w.height+k),K(t,c),t.intersect=function(T){return j.rect(t,T)},i}),"rectWithTitle"),Ir=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.height+t.padding,s=i.width+c/4+t.padding,r=a.insert("rect",":first-child").attr("style",t.style).attr("rx",c/2).attr("ry",c/2).attr("x",-s/2).attr("y",-c/2).attr("width",s).attr("height",c);return K(t,r),t.intersect=function(l){return j.rect(t,l)},a}),"stadium"),Br=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i,halfPadding:c}=yield G(e,t,it(t,void 0),!0),s=a.insert("circle",":first-child");return s.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+c).attr("width",i.width+t.padding).attr("height",i.height+t.padding),n.cM.info("Circle main"),K(t,s),t.intersect=function(r){return n.cM.info("Circle intersect",t,i.width/2+c,r),j.circle(t,i.width/2+c,r)},a}),"circle"),Rr=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i,halfPadding:c}=yield G(e,t,it(t,void 0),!0),s=5,r=a.insert("g",":first-child"),l=r.insert("circle"),o=r.insert("circle");return r.attr("class",t.class),l.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+c+s).attr("width",i.width+t.padding+s*2).attr("height",i.height+t.padding+s*2),o.attr("style",t.style).attr("rx",t.rx).attr("ry",t.ry).attr("r",i.width/2+c).attr("width",i.width+t.padding).attr("height",i.height+t.padding),n.cM.info("DoubleCircle main"),K(t,l),t.intersect=function(f){return n.cM.info("DoubleCircle intersect",t,i.width/2+c+s,f),j.circle(t,i.width/2+c+s,f)},a}),"doublecircle"),Ar=(0,n.eW)((e,t)=>I(this,null,function*(){const{shapeSvg:a,bbox:i}=yield G(e,t,it(t,void 0),!0),c=i.width+t.padding,s=i.height+t.padding,r=[{x:0,y:0},{x:c,y:0},{x:c,y:-s},{x:0,y:-s},{x:0,y:0},{x:-8,y:0},{x:c+8,y:0},{x:c+8,y:-s},{x:-8,y:-s},{x:-8,y:0}],l=dt(a,c,s,r);return l.attr("style",t.style),K(t,l),t.intersect=function(o){return j.polygon(t,r,o)},a}),"subroutine"),zr=(0,n.eW)((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child");return i.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),K(t,i),t.intersect=function(c){return j.circle(t,7,c)},a},"start"),pe=(0,n.eW)((e,t,a)=>{const i=e.insert("g").attr("class","node default").attr("id",t.domId||t.id);let c=70,s=10;a==="LR"&&(c=10,s=70);const r=i.append("rect").attr("x",-1*c/2).attr("y",-1*s/2).attr("width",c).attr("height",s).attr("class","fork-join");return K(t,r),t.height=t.height+t.padding/2,t.width=t.width+t.padding/2,t.intersect=function(l){return j.rect(t,l)},i},"forkJoin"),Pr=(0,n.eW)((e,t)=>{const a=e.insert("g").attr("class","node default").attr("id",t.domId||t.id),i=a.insert("circle",":first-child"),c=a.insert("circle",":first-child");return c.attr("class","state-start").attr("r",7).attr("width",14).attr("height",14),i.attr("class","state-end").attr("r",5).attr("width",10).attr("height",10),K(t,c),t.intersect=function(s){return j.circle(t,7,s)},a},"end"),Yr=(0,n.eW)((e,t)=>I(this,null,function*(){var W;const a=t.padding/2,i=4,c=8;let s;t.classes?s="node "+t.classes:s="node default";const r=e.insert("g").attr("class",s).attr("id",t.domId||t.id),l=r.insert("rect",":first-child"),o=r.insert("line"),f=r.insert("line");let d=0,b=i;const w=r.insert("g").attr("class","label");let _=0;const k=(W=t.classData.annotations)==null?void 0:W[0],T=t.classData.annotations[0]?"\xAB"+t.classData.annotations[0]+"\xBB":"",S=w.node().appendChild(yield ct(T,t.labelStyle,!0,!0));let z=S.getBBox();if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const h=S.children[0],M=(0,F.Ys)(S);z=h.getBoundingClientRect(),M.attr("width",z.width),M.attr("height",z.height)}t.classData.annotations[0]&&(b+=z.height+i,d+=z.width);let O=t.classData.label;t.classData.type!==void 0&&t.classData.type!==""&&((0,n.nV)().flowchart.htmlLabels?O+="&lt;"+t.classData.type+"&gt;":O+="<"+t.classData.type+">");const A=w.node().appendChild(yield ct(O,t.labelStyle,!0,!0));(0,F.Ys)(A).attr("class","classTitle");let m=A.getBBox();if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const h=A.children[0],M=(0,F.Ys)(A);m=h.getBoundingClientRect(),M.attr("width",m.width),M.attr("height",m.height)}b+=m.height+i,m.width>d&&(d=m.width);const g=[];t.classData.members.forEach(h=>I(this,null,function*(){const M=h.getDisplayDetails();let p=M.displayText;(0,n.nV)().flowchart.htmlLabels&&(p=p.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const D=w.node().appendChild(yield ct(p,M.cssStyle?M.cssStyle:t.labelStyle,!0,!0));let P=D.getBBox();if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const C=D.children[0],V=(0,F.Ys)(D);P=C.getBoundingClientRect(),V.attr("width",P.width),V.attr("height",P.height)}P.width>d&&(d=P.width),b+=P.height+i,g.push(D)})),b+=c;const x=[];if(t.classData.methods.forEach(h=>I(this,null,function*(){const M=h.getDisplayDetails();let p=M.displayText;(0,n.nV)().flowchart.htmlLabels&&(p=p.replace(/</g,"&lt;").replace(/>/g,"&gt;"));const D=w.node().appendChild(yield ct(p,M.cssStyle?M.cssStyle:t.labelStyle,!0,!0));let P=D.getBBox();if((0,n.ku)((0,n.nV)().flowchart.htmlLabels)){const C=D.children[0],V=(0,F.Ys)(D);P=C.getBoundingClientRect(),V.attr("width",P.width),V.attr("height",P.height)}P.width>d&&(d=P.width),b+=P.height+i,x.push(D)})),b+=c,k){let h=(d-z.width)/2;(0,F.Ys)(S).attr("transform","translate( "+(-1*d/2+h)+", "+-1*b/2+")"),_=z.height+i}let L=(d-m.width)/2;return(0,F.Ys)(A).attr("transform","translate( "+(-1*d/2+L)+", "+(-1*b/2+_)+")"),_+=m.height+i,o.attr("class","divider").attr("x1",-d/2-a).attr("x2",d/2+a).attr("y1",-b/2-a+c+_).attr("y2",-b/2-a+c+_),_+=c,g.forEach(h=>{var p;(0,F.Ys)(h).attr("transform","translate( "+-d/2+", "+(-1*b/2+_+c/2)+")");const M=h==null?void 0:h.getBBox();_+=((p=M==null?void 0:M.height)!=null?p:0)+i}),_+=c,f.attr("class","divider").attr("x1",-d/2-a).attr("x2",d/2+a).attr("y1",-b/2-a+c+_).attr("y2",-b/2-a+c+_),_+=c,x.forEach(h=>{var p;(0,F.Ys)(h).attr("transform","translate( "+-d/2+", "+(-1*b/2+_)+")");const M=h==null?void 0:h.getBBox();_+=((p=M==null?void 0:M.height)!=null?p:0)+i}),l.attr("style",t.style).attr("class","outer title-state").attr("x",-d/2-a).attr("y",-(b/2)-a).attr("width",d+t.padding).attr("height",b+t.padding),K(t,l),t.intersect=function(h){return j.rect(t,h)},r}),"class_box"),fe={rhombus:ge,composite:Mr,question:ge,rect:Wr,labelRect:Or,rectWithTitle:Tr,choice:wr,circle:Br,doublecircle:Rr,stadium:Ir,hexagon:Lr,block_arrow:_r,rect_left_inv_arrow:Sr,lean_right:vr,lean_left:Er,trapezoid:kr,inv_trapezoid:Dr,rect_right_inv_arrow:Cr,cylinder:Nr,start:zr,end:Pr,note:mr,subroutine:Ar,fork:pe,join:pe,class_box:Yr},It={},xe=(0,n.eW)((e,t,a)=>I(this,null,function*(){let i,c;if(t.link){let s;(0,n.nV)().securityLevel==="sandbox"?s="_top":t.linkTarget&&(s=t.linkTarget||"_blank"),i=e.insert("svg:a").attr("xlink:href",t.link).attr("target",s),c=yield fe[t.shape](i,t,a)}else c=yield fe[t.shape](e,t,a),i=c;return t.tooltip&&c.attr("title",t.tooltip),t.class&&c.attr("class","node default "+t.class),It[t.id]=i,t.haveCallback&&It[t.id].attr("class",It[t.id].attr("class")+" clickable"),i}),"insertNode"),Fr=(0,n.eW)(e=>{const t=It[e.id];n.cM.trace("Transforming node",e.diff,e,"translate("+(e.x-e.width/2-5)+", "+e.width/2+")");const a=8,i=e.diff||0;return e.clusterNode?t.attr("transform","translate("+(e.x+i-e.width/2)+", "+(e.y-e.height/2-a)+")"):t.attr("transform","translate("+e.x+", "+e.y+")"),i},"positionNode");function Gt(e,t,a=!1){var w,_,k,T,S,z,O;const i=e;let c="default";(((w=i==null?void 0:i.classes)==null?void 0:w.length)||0)>0&&(c=((_=i==null?void 0:i.classes)!=null?_:[]).join(" ")),c=c+" flowchart-label";let s=0,r="",l;switch(i.type){case"round":s=5,r="rect";break;case"composite":s=0,r="composite",l=0;break;case"square":r="rect";break;case"diamond":r="question";break;case"hexagon":r="hexagon";break;case"block_arrow":r="block_arrow";break;case"odd":r="rect_left_inv_arrow";break;case"lean_right":r="lean_right";break;case"lean_left":r="lean_left";break;case"trapezoid":r="trapezoid";break;case"inv_trapezoid":r="inv_trapezoid";break;case"rect_left_inv_arrow":r="rect_left_inv_arrow";break;case"circle":r="circle";break;case"ellipse":r="ellipse";break;case"stadium":r="stadium";break;case"subroutine":r="subroutine";break;case"cylinder":r="cylinder";break;case"group":r="rect";break;case"doublecircle":r="doublecircle";break;default:r="rect"}const o=(0,X.be)((k=i==null?void 0:i.styles)!=null?k:[]),f=i.label,d=(T=i.size)!=null?T:{width:0,height:0,x:0,y:0};return{labelStyle:o.labelStyle,shape:r,labelText:f,rx:s,ry:s,class:c,style:o.style,id:i.id,directions:i.directions,width:d.width,height:d.height,x:d.x,y:d.y,positioned:a,intersect:void 0,type:i.type,padding:(O=l!=null?l:(z=(S=(0,n.iE)())==null?void 0:S.block)==null?void 0:z.padding)!=null?O:0}}(0,n.eW)(Gt,"getNodeFromBlock");function ye(e,t,a){return I(this,null,function*(){const i=Gt(t,a,!1);if(i.type==="group")return;const c=(0,n.iE)(),s=yield xe(e,i,{config:c}),r=s.node().getBBox(),l=a.getBlock(i.id);l.size={width:r.width,height:r.height,x:0,y:0,node:s},a.setBlock(l),s.remove()})}(0,n.eW)(ye,"calculateBlockSize");function be(e,t,a){return I(this,null,function*(){const i=Gt(t,a,!0);if(a.getBlock(i.id).type!=="space"){const s=(0,n.iE)();yield xe(e,i,{config:s}),t.intersect=i==null?void 0:i.intersect,Fr(i)}})}(0,n.eW)(be,"insertBlockPositioned");function Bt(e,t,a,i){return I(this,null,function*(){for(const c of t)yield i(e,c,a),c.children&&(yield Bt(e,c.children,a,i))})}(0,n.eW)(Bt,"performOperations");function me(e,t,a){return I(this,null,function*(){yield Bt(e,t,a,ye)})}(0,n.eW)(me,"calculateBlockSizes");function we(e,t,a){return I(this,null,function*(){yield Bt(e,t,a,be)})}(0,n.eW)(we,"insertBlocks");function Le(e,t,a,i,c){return I(this,null,function*(){const s=new Ft.k({multigraph:!0,compound:!0});s.setGraph({rankdir:"TB",nodesep:10,ranksep:10,marginx:8,marginy:8});for(const r of a)r.size&&s.setNode(r.id,{width:r.size.width,height:r.size.height,intersect:r.intersect});for(const r of t)if(r.start&&r.end){const l=i.getBlock(r.start),o=i.getBlock(r.end);if(l!=null&&l.size&&(o!=null&&o.size)){const f=l.size,d=o.size,b=[{x:f.x,y:f.y},{x:f.x+(d.x-f.x)/2,y:f.y+(d.y-f.y)/2},{x:d.x,y:d.y}];or(e,{v:r.start,w:r.end,name:r.id},Nt(Ct({},r),{arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:b,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"}),void 0,"block",s,c),r.label&&(yield ir(e,Nt(Ct({},r),{label:r.label,labelStyle:"stroke: #333; stroke-width: 1.5px;fill:none;",arrowTypeEnd:r.arrowTypeEnd,arrowTypeStart:r.arrowTypeStart,points:b,classes:"edge-thickness-normal edge-pattern-solid flowchart-link LS-a1 LE-b1"})),nr(Nt(Ct({},r),{x:b[1].x,y:b[1].y}),{originalPath:b}))}}})}(0,n.eW)(Le,"insertEdges");var jr=(0,n.eW)(function(e,t){return t.db.getClasses()},"getClasses"),Zr=(0,n.eW)(function(e,t,a,i){return I(this,null,function*(){const{securityLevel:c,block:s}=(0,n.iE)(),r=i.db;let l;c==="sandbox"&&(l=(0,F.Ys)("#i"+t));const o=c==="sandbox"?(0,F.Ys)(l.nodes()[0].contentDocument.body):(0,F.Ys)("body"),f=c==="sandbox"?o.select(`[id="${t}"]`):(0,F.Ys)(`[id="${t}"]`);tr(f,["point","circle","cross"],i.type,t);const b=r.getBlocks(),w=r.getBlocksFlat(),_=r.getEdges(),k=f.insert("g").attr("class","block");yield me(k,b,r);const T=re(r);if(yield we(k,b,r),yield Le(k,_,w,r,t),T){const S=T,z=Math.max(1,Math.round(.125*(S.width/S.height))),O=S.height+z+10,A=S.width+10,{useMaxWidth:m}=s;(0,n.v2)(f,O,A,!!m),n.cM.debug("Here Bounds",T,S),f.attr("viewBox",`${S.x-5} ${S.y-5} ${S.width+10} ${S.height+10}`)}})},"draw"),Kr={draw:Zr,getClasses:jr},Ur={parser:jt,db:Ye,renderer:Kr,styles:je}},70982:function(J,Y,v){v.d(Y,{G:function(){return rt}});var $=v(29134),rt=(0,$.eW)(()=>`
  /* Font Awesome icon styling - consolidated */
  .label-icon {
    display: inline-block;
    height: 1em;
    overflow: visible;
    vertical-align: -0.125em;
  }
  
  .node .label-icon path {
    fill: currentColor;
    stroke: revert;
    stroke-width: revert;
  }
`,"getIconStyles")}}]);
}());