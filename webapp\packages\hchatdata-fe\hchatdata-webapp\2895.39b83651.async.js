(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[2895],{22590:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M301.3 496.7c-23.8 0-40.2-10.5-41.6-26.9H205c.9 43.4 36.9 70.3 93.9 70.3 59.1 0 95-28.4 95-75.5 0-35.8-20-55.9-64.5-64.5l-29.1-5.6c-23.8-4.7-33.8-11.9-33.8-24.2 0-15 13.3-24.5 33.4-24.5 20.1 0 35.3 11.1 36.6 27h53c-.9-41.7-37.5-70.3-90.3-70.3-54.4 0-89.7 28.9-89.7 73 0 35.5 21.2 58 62.5 65.8l29.7 5.9c25.8 5.2 35.6 11.9 35.6 24.4.1 14.7-14.5 25.1-36 25.1z"}},{tag:"path",attrs:{d:"M928 140H96c-17.7 0-32 14.3-32 32v496c0 17.7 14.3 32 32 32h380v112H304c-8.8 0-16 7.2-16 16v48c0 4.4 3.6 8 8 8h432c4.4 0 8-3.6 8-8v-48c0-8.8-7.2-16-16-16H548V700h380c17.7 0 32-14.3 32-32V172c0-17.7-14.3-32-32-32zm-40 488H136V212h752v416z"}},{tag:"path",attrs:{d:"M828.5 486.7h-95.8V308.5h-57.4V534h153.2zm-298.6 53.4c14.1 0 27.2-2 39.1-5.8l13.3 20.3h53.3L607.9 511c21.1-20 33-51.1 33-89.8 0-73.3-43.3-118.8-110.9-118.8s-111.2 45.3-111.2 118.8c-.1 73.7 43 118.9 111.1 118.9zm0-190c31.6 0 52.7 27.7 52.7 71.1 0 16.7-3.6 30.6-10 40.5l-5.2-6.9h-48.8L542 491c-3.9.9-8 1.4-12.2 1.4-31.7 0-52.8-27.5-52.8-71.2.1-43.6 21.2-71.1 52.9-71.1z"}}]},name:"console-sql",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},11090:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},9835:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M640.6 429.8h257.1c7.9 0 14.3-6.4 14.3-14.3V158.3c0-7.9-6.4-14.3-14.3-14.3H640.6c-7.9 0-14.3 6.4-14.3 14.3v92.9H490.6c-3.9 0-7.1 3.2-7.1 7.1v221.5h-85.7v-96.5c0-7.9-6.4-14.3-14.3-14.3H126.3c-7.9 0-14.3 6.4-14.3 14.3v257.2c0 7.9 6.4 14.3 14.3 14.3h257.1c7.9 0 14.3-6.4 14.3-14.3V544h85.7v221.5c0 3.9 3.2 7.1 7.1 7.1h135.7v92.9c0 7.9 6.4 14.3 14.3 14.3h257.1c7.9 0 14.3-6.4 14.3-14.3v-257c0-7.9-6.4-14.3-14.3-14.3h-257c-7.9 0-14.3 6.4-14.3 14.3v100h-78.6v-393h78.6v100c0 7.9 6.4 14.3 14.3 14.3zm53.5-217.9h150V362h-150V211.9zM329.9 587h-150V437h150v150zm364.2 75.1h150v150.1h-150V662.1z"}}]},name:"partition",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},24423:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M280 752h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8zm192-280h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v184c0 4.4 3.6 8 8 8zm192 72h80c4.4 0 8-3.6 8-8V280c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8v256c0 4.4 3.6 8 8 8zm216-432H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"project",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},27995:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 264c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48zm-8 136H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM480 544H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 308H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm356.8-74.4c29-26.3 47.2-64.3 47.2-106.6 0-79.5-64.5-144-144-144s-144 64.5-144 144c0 42.3 18.2 80.3 47.2 106.6-57 32.5-96.2 92.7-99.2 162.1-.2 4.5 3.5 8.3 8 8.3h48.1c4.2 0 7.7-3.3 8-7.6C564 871.2 621.7 816 692 816s128 55.2 131.9 124.4c.2 4.2 3.7 7.6 8 7.6H880c4.6 0 8.2-3.8 8-8.3-2.9-69.5-42.2-129.6-99.2-162.1zM692 591c44.2 0 80 35.8 80 80s-35.8 80-80 80-80-35.8-80-80 35.8-80 80-80z"}}]},name:"solution",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},12322:function(p,s,e){"use strict";e.d(s,{Z:function(){return h}});var r=e(95687),c=e(44194),i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},f=i,u=e(54183),d=function(v,m){return c.createElement(u.Z,(0,r.Z)({},v,{ref:m,icon:f}))},g=c.forwardRef(d),h=g},17258:function(p,s,e){"use strict";e.d(s,{Z:function(){return lt}});var r=e(44194),c=e(51865),i=e.n(c),f=e(34573),u=e(9695),d=e(71841),g=e(11778),h=e(26833),y=e(47506),v=e(40044),m=e(87471),K=e(19107),Y=e(77167),Z=e(88370);const k=t=>{const{paddingXXS:a,lineWidth:l,tagPaddingHorizontal:n,componentCls:o,calc:O}=t,C=O(n).sub(l).equal(),$=O(a).sub(l).equal();return{[o]:Object.assign(Object.assign({},(0,K.Wf)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:C,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:`${(0,v.unit)(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:t.borderRadiusSM,opacity:1,transition:`all ${t.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},[`${o}-close-icon`]:{marginInlineStart:$,fontSize:t.tagIconSize,color:t.colorIcon,cursor:"pointer",transition:`all ${t.motionDurationMid}`,"&:hover":{color:t.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${t.iconCls}-close, ${t.iconCls}-close:hover`]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${t.iconCls} + span, > span + ${t.iconCls}`]:{marginInlineStart:C}}),[`${o}-borderless`]:{borderColor:"transparent",background:t.tagBorderlessBg}}},M=t=>{const{lineWidth:a,fontSizeIcon:l,calc:n}=t,o=t.fontSizeSM;return(0,Y.mergeToken)(t,{tagFontSize:o,tagLineHeight:(0,v.unit)(n(t.lineHeightSM).mul(o).equal()),tagIconSize:n(l).sub(n(a).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},B=t=>({defaultBg:new m.FastColor(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText});var F=(0,Z.I$)("Tag",t=>{const a=M(t);return k(a)},B),q=function(t,a){var l={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&a.indexOf(n)<0&&(l[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)a.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(l[n[o]]=t[n[o]]);return l},_=r.forwardRef((t,a)=>{const{prefixCls:l,style:n,className:o,checked:O,onChange:C,onClick:$}=t,x=q(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:H,tag:z}=r.useContext(y.E_),P=j=>{C==null||C(!O),$==null||$(j)},I=H("tag",l),[N,R,b]=F(I),L=i()(I,`${I}-checkable`,{[`${I}-checkable-checked`]:O},z==null?void 0:z.className,o,R,b);return N(r.createElement("span",Object.assign({},x,{ref:a,style:Object.assign(Object.assign({},n),z==null?void 0:z.style),className:L,onClick:P})))}),tt=e(14325);const et=t=>(0,tt.Z)(t,(a,{textColor:l,lightBorderColor:n,lightColor:o,darkColor:O})=>({[`${t.componentCls}${t.componentCls}-${a}`]:{color:l,background:o,borderColor:n,"&-inverse":{color:t.colorTextLightSolid,background:O,borderColor:O},[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}));var nt=(0,Z.bk)(["Tag","preset"],t=>{const a=M(t);return et(a)},B);function ot(t){return typeof t!="string"?t:t.charAt(0).toUpperCase()+t.slice(1)}const V=(t,a,l)=>{const n=ot(l);return{[`${t.componentCls}${t.componentCls}-${a}`]:{color:t[`color${l}`],background:t[`color${n}Bg`],borderColor:t[`color${n}Border`],[`&${t.componentCls}-borderless`]:{borderColor:"transparent"}}}};var rt=(0,Z.bk)(["Tag","status"],t=>{const a=M(t);return[V(a,"success","Success"),V(a,"processing","Info"),V(a,"error","Error"),V(a,"warning","Warning")]},B),at=function(t,a){var l={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&a.indexOf(n)<0&&(l[n]=t[n]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)a.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(l[n[o]]=t[n[o]]);return l};const W=r.forwardRef((t,a)=>{const{prefixCls:l,className:n,rootClassName:o,style:O,children:C,icon:$,color:x,onClose:H,bordered:z=!0,visible:P}=t,I=at(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:N,direction:R,tag:b}=r.useContext(y.E_),[L,j]=r.useState(!0),ct=(0,f.Z)(I,["closeIcon","closable"]);r.useEffect(()=>{P!==void 0&&j(P)},[P]);const D=(0,u.o2)(x),X=(0,u.yT)(x),A=D||X,st=Object.assign(Object.assign({backgroundColor:x&&!A?x:void 0},b==null?void 0:b.style),O),S=N("tag",l),[it,dt,ut]=F(S),vt=i()(S,b==null?void 0:b.className,{[`${S}-${x}`]:A,[`${S}-has-color`]:x&&!A,[`${S}-hidden`]:!L,[`${S}-rtl`]:R==="rtl",[`${S}-borderless`]:!z},n,o,dt,ut),U=T=>{T.stopPropagation(),H==null||H(T),!T.defaultPrevented&&j(!1)},[,ft]=(0,d.Z)((0,d.w)(t),(0,d.w)(b),{closable:!1,closeIconRender:T=>{const mt=r.createElement("span",{className:`${S}-close-icon`,onClick:U},T);return(0,g.wm)(T,mt,E=>({onClick:J=>{var w;(w=E==null?void 0:E.onClick)===null||w===void 0||w.call(E,J),U(J)},className:i()(E==null?void 0:E.className,`${S}-close-icon`)}))}}),gt=typeof I.onClick=="function"||C&&C.type==="a",Q=$||null,ht=Q?r.createElement(r.Fragment,null,Q,C&&r.createElement("span",null,C)):C,G=r.createElement("span",Object.assign({},ct,{ref:a,className:vt,style:st}),ht,ft,D&&r.createElement(nt,{key:"preset",prefixCls:S}),X&&r.createElement(rt,{key:"status",prefixCls:S}));return it(gt?r.createElement(h.Z,{component:"Tag"},G):G)});W.CheckableTag=_;var lt=W},49841:function(p){function s(e){if(e==null)throw new TypeError("Cannot destructure "+e)}p.exports=s,p.exports.__esModule=!0,p.exports.default=p.exports}}]);
