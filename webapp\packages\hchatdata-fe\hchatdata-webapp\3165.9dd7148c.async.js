(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3165],{15600:function(Fe,we,x){"use strict";var s=x(28659),he=x(44194),le=x(29),fe=x(51937),Y=function(me,g){return he.createElement(fe.Z,(0,s.Z)((0,s.Z)({},me),{},{ref:g,icon:le.Z}))},u=he.forwardRef(Y);we.Z=u},96144:function(Fe,we,x){"use strict";x.d(we,{Z:function(){return H}});var s=x(95687),he=x(60505),le=x(44194),fe=x(28659),Y=x(94450),u=x(51865),ce=x.n(u),me=x(65489),g=x(22230),be=x(25425),Se=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],We=le.forwardRef(function(W,T){var I=W.className,A=W.component,G=W.viewBox,V=W.spin,$=W.rotate,U=W.tabIndex,re=W.onClick,J=W.children,B=(0,he.Z)(W,Se),ne=le.useRef(),j=(0,me.x1)(ne,T);(0,be.Kp)(!!(A||J),"Should have `component` prop or `children`."),(0,be.C3)(ne);var de=le.useContext(g.Z),Pe=de.prefixCls,Ee=Pe===void 0?"anticon":Pe,_e=de.rootClassName,Oe=ce()(_e,Ee,(0,Y.Z)({},"".concat(Ee,"-spin"),!!V&&!!A),I),Ae=ce()((0,Y.Z)({},"".concat(Ee,"-spin"),!!V)),Be=$?{msTransform:"rotate(".concat($,"deg)"),transform:"rotate(".concat($,"deg)")}:void 0,je=(0,fe.Z)((0,fe.Z)({},be.vD),{},{className:Ae,style:Be,viewBox:G});G||delete je.viewBox;var Ye=function(){return A?le.createElement(A,je,J):J?((0,be.Kp)(!!G||le.Children.count(J)===1&&le.isValidElement(J)&&le.Children.only(J).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),le.createElement("svg",(0,s.Z)({},je,{viewBox:G}),J)):null},Ve=U;return Ve===void 0&&re&&(Ve=-1),le.createElement("span",(0,s.Z)({role:"img"},B,{ref:j,tabIndex:Ve,onClick:re,className:Oe}),Ye())});We.displayName="AntdIcon";var z=We,q=["type","children"],h=new Set;function M(W){return!!(typeof W=="string"&&W.length&&!h.has(W))}function C(W){var T=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,I=W[T];if(M(I)){var A=document.createElement("script");A.setAttribute("src",I),A.setAttribute("data-namespace",I),W.length>T+1&&(A.onload=function(){C(W,T+1)},A.onerror=function(){C(W,T+1)}),h.add(I),document.body.appendChild(A)}}function H(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},T=W.scriptUrl,I=W.extraCommonProps,A=I===void 0?{}:I;T&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(T)?C(T.reverse()):C([T]));var G=le.forwardRef(function(V,$){var U=V.type,re=V.children,J=(0,he.Z)(V,q),B=null;return V.type&&(B=le.createElement("use",{xlinkHref:"#".concat(U)})),re&&(B=re),le.createElement(z,(0,s.Z)({},A,J,{ref:$}),B)});return G.displayName="Iconfont",G}},93363:function(Fe,we,x){"use strict";x.d(we,{f:function(){return Bo}});var s=x(94450),he=x(91842),le=x(40571),fe=x(60505),Y=x(56840),u=x(28659),ce=x(55529),me=x(81424),g=x(44194);function be(a){var e=typeof window=="undefined",n=(0,g.useState)(function(){return e?!1:window.matchMedia(a).matches}),t=(0,Y.Z)(n,2),r=t[0],i=t[1];return(0,g.useLayoutEffect)(function(){if(!e){var o=window.matchMedia(a),c=function(p){return i(p.matches)};return o.addListener(c),function(){return o.removeListener(c)}}},[a]),r}var Se={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},We=function(){var e=void 0;if(typeof window=="undefined")return e;var n=Object.keys(Se).find(function(t){var r=Se[t].matchMedia;return!!window.matchMedia(r).matches});return e=n,e},z=function(){var e=be(Se.md.matchMedia),n=be(Se.lg.matchMedia),t=be(Se.xxl.matchMedia),r=be(Se.xl.matchMedia),i=be(Se.sm.matchMedia),o=be(Se.xs.matchMedia),c=(0,g.useState)(We()),d=(0,Y.Z)(c,2),p=d[0],m=d[1];return(0,g.useEffect)(function(){if(t){m("xxl");return}if(r){m("xl");return}if(n){m("lg");return}if(e){m("md");return}if(i){m("sm");return}if(o){m("xs");return}m("md")},[e,n,t,r,i,o]),p},q=x(32241);function h(a,e){var n=typeof a.pageName=="string"?a.title:e;(0,g.useEffect)(function(){(0,q.j)()&&n&&(document.title=n)},[a.title,n])}var M=x(2618),C=x(67095);function H(a){if((0,M.n)((0,C.b)(),"5.6.0")<0)return a;var e={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},n=(0,u.Z)({},a);return Object.keys(e).forEach(function(t){n[t]!==void 0&&(n[e[t]]=n[t],delete n[t])}),n}var W=x(41029);function T(a,e){return e>>>a|e<<32-a}function I(a,e,n){return a&e^~a&n}function A(a,e,n){return a&e^a&n^e&n}function G(a){return T(2,a)^T(13,a)^T(22,a)}function V(a){return T(6,a)^T(11,a)^T(25,a)}function $(a){return T(7,a)^T(18,a)^a>>>3}function U(a){return T(17,a)^T(19,a)^a>>>10}function re(a,e){return a[e&15]+=U(a[e+14&15])+a[e+9&15]+$(a[e+1&15])}var J=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],B,ne,j,de="0123456789abcdef";function Pe(a,e){var n=(a&65535)+(e&65535),t=(a>>16)+(e>>16)+(n>>16);return t<<16|n&65535}function Ee(){B=new Array(8),ne=new Array(2),j=new Array(64),ne[0]=ne[1]=0,B[0]=1779033703,B[1]=3144134277,B[2]=1013904242,B[3]=2773480762,B[4]=1359893119,B[5]=2600822924,B[6]=528734635,B[7]=1541459225}function _e(){var a,e,n,t,r,i,o,c,d,p,m=new Array(16);a=B[0],e=B[1],n=B[2],t=B[3],r=B[4],i=B[5],o=B[6],c=B[7];for(var _=0;_<16;_++)m[_]=j[(_<<2)+3]|j[(_<<2)+2]<<8|j[(_<<2)+1]<<16|j[_<<2]<<24;for(var v=0;v<64;v++)d=c+V(r)+I(r,i,o)+J[v],v<16?d+=m[v]:d+=re(m,v),p=G(a)+A(a,e,n),c=o,o=i,i=r,r=Pe(t,d),t=n,n=e,e=a,a=Pe(d,p);B[0]+=a,B[1]+=e,B[2]+=n,B[3]+=t,B[4]+=r,B[5]+=i,B[6]+=o,B[7]+=c}function Oe(a,e){var n,t,r=0;t=ne[0]>>3&63;var i=e&63;for((ne[0]+=e<<3)<e<<3&&ne[1]++,ne[1]+=e>>29,n=0;n+63<e;n+=64){for(var o=t;o<64;o++)j[o]=a.charCodeAt(r++);_e(),t=0}for(var c=0;c<i;c++)j[c]=a.charCodeAt(r++)}function Ae(){var a=ne[0]>>3&63;if(j[a++]=128,a<=56)for(var e=a;e<56;e++)j[e]=0;else{for(var n=a;n<64;n++)j[n]=0;_e();for(var t=0;t<56;t++)j[t]=0}j[56]=ne[1]>>>24&255,j[57]=ne[1]>>>16&255,j[58]=ne[1]>>>8&255,j[59]=ne[1]&255,j[60]=ne[0]>>>24&255,j[61]=ne[0]>>>16&255,j[62]=ne[0]>>>8&255,j[63]=ne[0]&255,_e()}function Be(){for(var a=0,e=new Array(32),n=0;n<8;n++)e[a++]=B[n]>>>24&255,e[a++]=B[n]>>>16&255,e[a++]=B[n]>>>8&255,e[a++]=B[n]&255;return e}function je(){for(var a=new String,e=0;e<8;e++)for(var n=28;n>=0;n-=4)a+=de.charAt(B[e]>>>n&15);return a}function Ye(a){return Ee(),Oe(a,a.length),Ae(),je()}var Ve=Ye;function Qe(a){"@babel/helpers - typeof";return Qe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qe(a)}var Ft=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function Wt(a,e){return Ut(a)||zt(a,e)||Tn(a,e)||$t()}function $t(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zt(a,e){var n=a==null?null:typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(n!=null){var t=[],r=!0,i=!1,o,c;try{for(n=n.call(a);!(r=(o=n.next()).done)&&(t.push(o.value),!(e&&t.length===e));r=!0);}catch(d){i=!0,c=d}finally{try{!r&&n.return!=null&&n.return()}finally{if(i)throw c}}return t}}function Ut(a){if(Array.isArray(a))return a}function Gt(a,e){var n=typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(!n){if(Array.isArray(a)||(n=Tn(a))||e&&a&&typeof a.length=="number"){n&&(a=n);var t=0,r=function(){};return{s:r,n:function(){return t>=a.length?{done:!0}:{done:!1,value:a[t++]}},e:function(p){throw p},f:r}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,o=!1,c;return{s:function(){n=n.call(a)},n:function(){var p=n.next();return i=p.done,p},e:function(p){o=!0,c=p},f:function(){try{!i&&n.return!=null&&n.return()}finally{if(o)throw c}}}}function Vt(a,e){if(!(a instanceof e))throw new TypeError("Cannot call a class as a function")}function Vn(a,e){for(var n=0;n<e.length;n++){var t=e[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(a,t.key,t)}}function Kt(a,e,n){return e&&Vn(a.prototype,e),n&&Vn(a,n),Object.defineProperty(a,"prototype",{writable:!1}),a}function Xt(a,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(e&&e.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),e&&on(a,e)}function Yt(a){var e=Kn();return function(){var t=ln(a),r;if(e){var i=ln(this).constructor;r=Reflect.construct(t,arguments,i)}else r=t.apply(this,arguments);return Qt(this,r)}}function Qt(a,e){if(e&&(Qe(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Jt(a)}function Jt(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function Rn(a){var e=typeof Map=="function"?new Map:void 0;return Rn=function(t){if(t===null||!qt(t))return t;if(typeof t!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return _n(t,arguments,ln(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),on(r,t)},Rn(a)}function _n(a,e,n){return Kn()?_n=Reflect.construct.bind():_n=function(r,i,o){var c=[null];c.push.apply(c,i);var d=Function.bind.apply(r,c),p=new d;return o&&on(p,o.prototype),p},_n.apply(null,arguments)}function Kn(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}function qt(a){return Function.toString.call(a).indexOf("[native code]")!==-1}function on(a,e){return on=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},on(a,e)}function ln(a){return ln=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(n){return n.__proto__||Object.getPrototypeOf(n)},ln(a)}function Xn(a){return ta(a)||na(a)||Tn(a)||ea()}function ea(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Tn(a,e){if(a){if(typeof a=="string")return En(a,e);var n=Object.prototype.toString.call(a).slice(8,-1);if(n==="Object"&&a.constructor&&(n=a.constructor.name),n==="Map"||n==="Set")return Array.from(a);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return En(a,e)}}function na(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function ta(a){if(Array.isArray(a))return En(a)}function En(a,e){(e==null||e>a.length)&&(e=a.length);for(var n=0,t=new Array(e);n<e;n++)t[n]=a[n];return t}function aa(a,e){if(a==null)return{};var n=ra(a,e),t,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);for(r=0;r<i.length;r++)t=i[r],!(e.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(a,t)&&(n[t]=a[t])}return n}function ra(a,e){if(a==null)return{};var n={},t=Object.keys(a),r,i;for(i=0;i<t.length;i++)r=t[i],!(e.indexOf(r)>=0)&&(n[r]=a[r]);return n}function Yn(a,e){var n=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);e&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),n.push.apply(n,t)}return n}function ue(a){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Yn(Object(n),!0).forEach(function(t){oa(a,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(n)):Yn(Object(n)).forEach(function(t){Object.defineProperty(a,t,Object.getOwnPropertyDescriptor(n,t))})}return a}function oa(a,e,n){return e in a?Object.defineProperty(a,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[e]=n,a}var Re="routes";function sn(a){return a.split("?")[0].split("#")[0]}var Zn=function(e){if(!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},ia=function(e){var n=e.path;if(!n||n==="/")try{return"/".concat(Ve(JSON.stringify(e)))}catch(t){}return n&&sn(n)},la=function(e,n){var t=e.name,r=e.locale;return"locale"in e&&r===!1||!t?!1:e.locale||"".concat(n,".").concat(t)},Qn=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||n).startsWith("/")||Zn(e)?e:"/".concat(n,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},sa=function(e,n){var t=e.menu,r=t===void 0?{}:t,i=e.indexRoute,o=e.path,c=o===void 0?"":o,d=e.children||[],p=r.name,m=p===void 0?e.name:p,_=r.icon,v=_===void 0?e.icon:_,S=r.hideChildren,N=S===void 0?e.hideChildren:S,Z=r.flatMenu,P=Z===void 0?e.flatMenu:Z,L=i&&Object.keys(i).join(",")!=="redirect"?[ue({path:c,menu:r},i)].concat(d||[]):d,k=ue({},e);if(m&&(k.name=m),v&&(k.icon=v),L&&L.length){if(N)return delete k.children,k;var F=Dn(ue(ue({},n),{},{data:L}),e);if(P)return F;delete k[Re]}return k},Ke=function(e){return Array.isArray(e)&&e.length>0};function Dn(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},n=a.data,t=a.formatMessage,r=a.parentName,i=a.locale;return!n||!Array.isArray(n)?[]:n.filter(function(o){return o?Ke(o.children)||o.path||o.originPath||o.layout?!0:(o.redirect||o.unaccessible,!1):!1}).filter(function(o){var c,d;return!(o==null||(c=o.menu)===null||c===void 0)&&c.name||o!=null&&o.flatMenu||!(o==null||(d=o.menu)===null||d===void 0)&&d.flatMenu?!0:o.menu!==!1}).map(function(o){var c=ue(ue({},o),{},{path:o.path||o.originPath});return!c.children&&c[Re]&&(c.children=c[Re],delete c[Re]),c.unaccessible&&delete c.name,c.path==="*"&&(c.path="."),c.path==="/*"&&(c.path="."),!c.path&&c.originPath&&(c.path=c.originPath),c}).map(function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},c=o.children||o[Re]||[],d=Qn(o.path,e?e.path:"/"),p=o.name,m=la(o,r||"menu"),_=m!==!1&&i!==!1&&t&&m?t({id:m,defaultMessage:p}):p,v=e.pro_layout_parentKeys,S=v===void 0?[]:v,N=e.children,Z=e.icon,P=e.flatMenu,L=e.indexRoute,k=e.routes,F=aa(e,Ft),D=new Set([].concat(Xn(S),Xn(o.parentKeys||[])));e.key&&D.add(e.key);var O=ue(ue(ue({},F),{},{menu:void 0},o),{},{path:d,locale:m,key:o.key||ia(ue(ue({},o),{},{path:d})),pro_layout_parentKeys:Array.from(D).filter(function(R){return R&&R!=="/"})});if(_?O.name=_:delete O.name,O.menu===void 0&&delete O.menu,Ke(c)){var y=Dn(ue(ue({},a),{},{data:c,parentName:m||""}),O);Ke(y)&&(O.children=y)}return sa(O,a)}).flat(1)}var ca=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(n){return n&&(n.name||Ke(n.children))&&!n.hideInMenu&&!n.redirect}).map(function(n){var t=ue({},n),r=t.children||n[Re]||[];if(delete t[Re],Ke(r)&&!t.hideChildrenInMenu&&r.some(function(o){return o&&!!o.name})){var i=a(r);if(i.length)return ue(ue({},t),{},{children:i})}return ue({},n)}).filter(function(n){return n})},da=function(a){Xt(n,a);var e=Yt(n);function n(){return Vt(this,n),e.apply(this,arguments)}return Kt(n,[{key:"get",value:function(r){var i;try{var o=Gt(this.entries()),c;try{for(o.s();!(c=o.n()).done;){var d=Wt(c.value,2),p=d[0],m=d[1],_=sn(p);if(!Zn(p)&&(0,W.Bo)(_,[]).test(r)){i=m;break}}}catch(v){o.e(v)}finally{o.f()}}catch(v){i=void 0}return i}}]),n}(Rn(Map)),ua=function(e){var n=new da,t=function r(i,o){i.forEach(function(c){var d=c.children||c[Re]||[];Ke(d)&&r(d,c);var p=Qn(c.path,o?o.path:"/");n.set(sn(p),c)})};return t(e),n},pa=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(n){var t=n.children||n[Re];if(Ke(t)){var r=a(t);if(r.length)return ue({},n)}var i=ue({},n);return delete i[Re],delete i.children,i}).filter(function(n){return n})},ma=function(e,n,t,r){var i=Dn({data:e,formatMessage:t,locale:n}),o=r?pa(i):ca(i),c=ua(i);return{breadcrumb:c,menuData:o}},va=ma;function Jn(a,e){var n=Object.keys(a);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(a);e&&(t=t.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),n.push.apply(n,t)}return n}function cn(a){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Jn(Object(n),!0).forEach(function(t){ga(a,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(n)):Jn(Object(n)).forEach(function(t){Object.defineProperty(a,t,Object.getOwnPropertyDescriptor(n,t))})}return a}function ga(a,e,n){return e in a?Object.defineProperty(a,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[e]=n,a}var fa=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n={};return e.forEach(function(t){var r=cn({},t);if(!(!r||!r.key)){!r.children&&r[Re]&&(r.children=r[Re],delete r[Re]);var i=r.children||[];n[sn(r.path||r.key||"/")]=cn({},r),n[r.key||r.path||"/"]=cn({},r),i&&(n=cn(cn({},n),a(i)))}}),n},ha=fa,_a=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,t=arguments.length>2?arguments[2]:void 0;return e.filter(function(r){if(r==="/"&&n==="/")return!0;if(r!=="/"&&r!=="/*"&&r&&!Zn(r)){var i=sn(r);try{if(t&&(0,W.Bo)("".concat(i)).test(n)||(0,W.Bo)("".concat(i),[]).test(n)||(0,W.Bo)("".concat(i,"/(.*)")).test(n))return!0}catch(o){}}return!1}).sort(function(r,i){return r===n?10:i===n?-10:r.substr(1).split("/").length-i.substr(1).split("/").length})},ya=function(e,n,t,r){var i=ha(n),o=Object.keys(i),c=_a(o,e||"/",r);return!c||c.length<1?[]:(t||(c=[c[c.length-1]]),c.map(function(d){var p=i[d]||{pro_layout_parentKeys:"",key:""},m=new Map,_=(p.pro_layout_parentKeys||[]).map(function(v){return m.has(v)?null:(m.set(v,!0),i[v])}).filter(function(v){return v});return p.key&&_.push(p),_}).flat(1))},xa=ya,Ne=x(27634),dn=x(18191),Ca=x(51865),ee=x.n(Ca),ba=x(34573),wn=x(47506),Sa=x(86618),Pa=x(54387),An=x(88265);function Ma(a,e,n){return typeof n=="boolean"?n:a.length?!0:(0,Pa.Z)(e).some(r=>r.type===An.Z)}var qn=x(50029),et=function(a,e){var n={};for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&e.indexOf(t)<0&&(n[t]=a[t]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(a);r<t.length;r++)e.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(a,t[r])&&(n[t[r]]=a[t[r]]);return n};function yn({suffixCls:a,tagName:e,displayName:n}){return t=>g.forwardRef((i,o)=>g.createElement(t,Object.assign({ref:o,suffixCls:a,tagName:e},i)))}const jn=g.forwardRef((a,e)=>{const{prefixCls:n,suffixCls:t,className:r,tagName:i}=a,o=et(a,["prefixCls","suffixCls","className","tagName"]),{getPrefixCls:c}=g.useContext(wn.E_),d=c("layout",n),[p,m,_]=(0,qn.ZP)(d),v=t?`${d}-${t}`:d;return p(g.createElement(i,Object.assign({className:ee()(n||v,r,m,_),ref:e},o)))}),Ia=g.forwardRef((a,e)=>{const{direction:n}=g.useContext(wn.E_),[t,r]=g.useState([]),{prefixCls:i,className:o,rootClassName:c,children:d,hasSider:p,tagName:m,style:_}=a,v=et(a,["prefixCls","className","rootClassName","children","hasSider","tagName","style"]),S=(0,ba.Z)(v,["suffixCls"]),{getPrefixCls:N,className:Z,style:P}=(0,wn.dj)("layout"),L=N("layout",i),k=Ma(t,d,p),[F,D,O]=(0,qn.ZP)(L),y=ee()(L,{[`${L}-has-sider`]:k,[`${L}-rtl`]:n==="rtl"},Z,o,c,D,O),R=g.useMemo(()=>({siderHook:{addSider:K=>{r(w=>[].concat((0,dn.Z)(w),[K]))},removeSider:K=>{r(w=>w.filter(E=>E!==K))}}}),[]);return F(g.createElement(Sa.V.Provider,{value:R},g.createElement(m,Object.assign({ref:e,className:y,style:Object.assign(Object.assign({},P),_)},S),d)))}),Ra=yn({tagName:"div",displayName:"Layout"})(Ia),Ta=yn({suffixCls:"header",tagName:"header",displayName:"Header"})(jn),Ea=yn({suffixCls:"footer",tagName:"footer",displayName:"Footer"})(jn),Za=yn({suffixCls:"content",tagName:"main",displayName:"Content"})(jn);var Da=Ra;const Je=Da;Je.Header=Ta,Je.Footer=Ea,Je.Content=Za,Je.Sider=An.Z,Je._InternalSiderContext=An.D;var Xe=Je,nt=x(36490),wa=x(56049),Aa=x(21228),ja=x(3842),Na=x(2001),l=x(31549),ka=function(e){var n=(0,g.useContext)(ce.L_),t=n.hashId,r=e.style,i=e.prefixCls,o=e.children,c=e.hasPageContainer,d=c===void 0?0:c,p=ee()("".concat(i,"-content"),t,(0,s.Z)((0,s.Z)({},"".concat(i,"-has-header"),e.hasHeader),"".concat(i,"-content-has-page-container"),d>0)),m=e.ErrorBoundary||Na.S;return e.ErrorBoundary===!1?(0,l.jsx)(Xe.Content,{className:p,style:r,children:o}):(0,l.jsx)(m,{children:(0,l.jsx)(Xe.Content,{className:p,style:r,children:o})})},Oa=function(){return(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,l.jsxs)("defs",{children:[(0,l.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,l.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,l.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,l.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,l.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,l.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,l.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,l.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,l.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,l.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,l.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,l.jsxs)("g",{children:[(0,l.jsxs)("g",{fillRule:"nonzero",children:[(0,l.jsxs)("g",{children:[(0,l.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,l.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,l.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,l.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},tt=x(95687),Ba={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm5.6-532.7c53 0 89 33.8 93 83.4.3 4.2 3.8 7.4 8 7.4h56.7c2.6 0 4.7-2.1 4.7-4.7 0-86.7-68.4-147.4-162.7-147.4C407.4 290 344 364.2 344 486.8v52.3C344 660.8 407.4 734 517.3 734c94 0 162.7-58.8 162.7-141.4 0-2.6-2.1-4.7-4.7-4.7h-56.8c-4.2 0-7.6 3.2-8 7.3-4.2 46.1-40.1 77.8-93 77.8-65.3 0-102.1-47.9-102.1-133.6v-52.6c.1-87 37-135.5 102.2-135.5z"}}]},name:"copyright",theme:"outlined"},La=Ba,at=x(54183),Ha=function(e,n){return g.createElement(at.Z,(0,tt.Z)({},e,{ref:n,icon:La}))},Fa=g.forwardRef(Ha),Wa=Fa,Me=x(67447),$a=function(e){return(0,s.Z)({},e.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:e.colorTextSecondary,"&-link":{color:e.colorTextSecondary,textDecoration:e.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:e.colorPrimary}},"&-copyright":{fontSize:"14px",color:e.colorText}})};function za(a){return(0,Me.Xj)("ProLayoutFooter",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[$a(n)]})}var Ua=function(e){var n=e.className,t=e.prefixCls,r=e.links,i=e.copyright,o=e.style,c=(0,g.useContext)(Ne.ZP.ConfigContext),d=c.getPrefixCls(t||"pro-global-footer"),p=za(d),m=p.wrapSSR,_=p.hashId;return(r==null||r===!1||Array.isArray(r)&&r.length===0)&&(i==null||i===!1)?null:m((0,l.jsxs)("div",{className:ee()(d,_,n),style:o,children:[r&&(0,l.jsx)("div",{className:"".concat(d,"-list ").concat(_).trim(),children:r.map(function(v){return(0,l.jsx)("a",{className:"".concat(d,"-list-link ").concat(_).trim(),title:v.key,target:v.blankTarget?"_blank":"_self",href:v.href,rel:"noreferrer",children:v.title},v.key)})}),i&&(0,l.jsx)("div",{className:"".concat(d,"-copyright ").concat(_).trim(),children:i})]}))},Ga=Xe.Footer,Va=function(e){var n=e.links,t=e.copyright,r=e.style,i=e.className,o=e.prefixCls;return(0,l.jsx)(Ga,{className:i,style:(0,u.Z)({padding:0},r),children:(0,l.jsx)(Ua,{links:n,prefixCls:o,copyright:t===!1?null:(0,l.jsxs)(g.Fragment,{children:[(0,l.jsx)(Wa,{})," ",t]})})})},rt=function a(e){return(e||[]).reduce(function(n,t){if(t.key&&n.push(t.key),t.children||t.routes){var r=n.concat(a(t.children||t.routes)||[]);return r}return n},[])},ot={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function ni(a){return a&&ot[a]?ot[a]:a||""}function xn(a){return a.map(function(e){var n=e.children||[],t=(0,u.Z)({},e);if(!t.children&&t.routes&&(t.children=t.routes),!t.name||t.hideInMenu)return null;if(t&&t!==null&&t!==void 0&&t.children){if(!t.hideChildrenInMenu&&n.some(function(r){return r&&r.name&&!r.hideInMenu}))return(0,u.Z)((0,u.Z)({},e),{},{children:xn(n)});delete t.children}return delete t.routes,t}).filter(function(e){return e})}var Ka={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},Xa=Ka,Ya=function(e,n){return g.createElement(at.Z,(0,tt.Z)({},e,{ref:n,icon:Xa}))},Qa=g.forwardRef(Ya),Ja=Qa,qa=x(15154),er=function(){return(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,l.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},nr=function a(e){var n=e.appList,t=e.baseClassName,r=e.hashId,i=e.itemClick;return(0,l.jsx)("div",{className:"".concat(t,"-content ").concat(r).trim(),children:(0,l.jsx)("ul",{className:"".concat(t,"-content-list ").concat(r).trim(),children:n==null?void 0:n.map(function(o,c){var d;return o!=null&&(d=o.children)!==null&&d!==void 0&&d.length?(0,l.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(r).trim(),children:[(0,l.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(r).trim(),children:o.title}),(0,l.jsx)(a,{hashId:r,itemClick:i,appList:o==null?void 0:o.children,baseClassName:t})]},c):(0,l.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(r).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,l.jsxs)("a",{href:i?void 0:o.url,target:o.target,rel:"noreferrer",children:[kn(o.icon),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:o.title}),o.desc?(0,l.jsx)("span",{children:o.desc}):null]})]})},c)})})})},Nn=function(e){if(!e||!e.startsWith("http"))return!1;try{var n=new URL(e);return!!n}catch(t){return!1}},tr=function(e,n){if(e&&typeof e=="string"&&Nn(e))return(0,l.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,l.jsx)("div",{id:"avatarLogo",children:e});if(!e&&n&&typeof n=="string"){var t=n.substring(0,1);return(0,l.jsx)("div",{id:"avatarLogo",children:t})}return e},ar=function a(e){var n=e.appList,t=e.baseClassName,r=e.hashId,i=e.itemClick;return(0,l.jsx)("div",{className:"".concat(t,"-content ").concat(r).trim(),children:(0,l.jsx)("ul",{className:"".concat(t,"-content-list ").concat(r).trim(),children:n==null?void 0:n.map(function(o,c){var d;return o!=null&&(d=o.children)!==null&&d!==void 0&&d.length?(0,l.jsxs)("div",{className:"".concat(t,"-content-list-item-group ").concat(r).trim(),children:[(0,l.jsx)("div",{className:"".concat(t,"-content-list-item-group-title ").concat(r).trim(),children:o.title}),(0,l.jsx)(a,{hashId:r,itemClick:i,appList:o==null?void 0:o.children,baseClassName:t})]},c):(0,l.jsx)("li",{className:"".concat(t,"-content-list-item ").concat(r).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,l.jsxs)("a",{href:i?"javascript:;":o.url,target:o.target,rel:"noreferrer",children:[tr(o.icon,o.title),(0,l.jsx)("div",{children:(0,l.jsx)("div",{children:o.title})})]})},c)})})})},rr=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":Me.Wf===null||Me.Wf===void 0?void 0:(0,Me.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},or=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},ir=function(e){var n,t,r,i,o;return(0,s.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:(n=e.layout)===null||n===void 0?void 0:n.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:(t=e.layout)===null||t===void 0?void 0:t.colorTextAppListIconHover,backgroundColor:(r=e.layout)===null||r===void 0?void 0:r.colorBgAppListIconHover},"&-active":{color:(i=e.layout)===null||i===void 0?void 0:i.colorTextAppListIconHover,backgroundColor:(o=e.layout)===null||o===void 0?void 0:o.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,s.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":or(e),"&-default":rr(e)})};function lr(a){return(0,Me.Xj)("AppsLogoComponents",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[ir(n)]})}var kn=function(e){return typeof e=="string"?(0,l.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):typeof e=="function"?e():e},On=function(e){var n,t=e.appList,r=e.appListRender,i=e.prefixCls,o=i===void 0?"ant-pro":i,c=e.onItemClick,d=g.useRef(null),p=g.useRef(null),m="".concat(o,"-layout-apps"),_=lr(m),v=_.wrapSSR,S=_.hashId,N=(0,g.useState)(!1),Z=(0,Y.Z)(N,2),P=Z[0],L=Z[1],k=function(R){c==null||c(R,p)},F=(0,g.useMemo)(function(){var y=t==null?void 0:t.some(function(R){return!(R!=null&&R.desc)});return y?(0,l.jsx)(ar,{hashId:S,appList:t,itemClick:c?k:void 0,baseClassName:"".concat(m,"-simple")}):(0,l.jsx)(nr,{hashId:S,appList:t,itemClick:c?k:void 0,baseClassName:"".concat(m,"-default")})},[t,m,S]);if(!(e!=null&&(n=e.appList)!==null&&n!==void 0&&n.length))return null;var D=r?r(e==null?void 0:e.appList,F):F,O=(0,C.X)(void 0,function(y){return L(y)});return v((0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{ref:d,onClick:function(R){R.stopPropagation(),R.preventDefault()}}),(0,l.jsx)(qa.Z,(0,u.Z)((0,u.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},O),{},{overlayClassName:"".concat(m,"-popover ").concat(S).trim(),content:D,getPopupContainer:function(){return d.current||document.body},children:(0,l.jsx)("span",{ref:p,onClick:function(R){R.stopPropagation()},className:ee()("".concat(m,"-icon"),S,(0,s.Z)({},"".concat(m,"-icon-active"),P)),children:(0,l.jsx)(er,{})})}))]}))},it=x(44723),sr=x(2206),lt=x(62178);function cr(){return(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,l.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var dr=function(e){var n,t,r;return(0,s.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorTextCollapsedButton,backgroundColor:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function ur(a){return(0,Me.Xj)("SiderMenuCollapsedIcon",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[dr(n)]})}var pr=["isMobile","collapsed"],mr=function(e){var n=e.isMobile,t=e.collapsed,r=(0,fe.Z)(e,pr),i=ur(e.className),o=i.wrapSSR,c=i.hashId;return n&&t?null:o((0,l.jsx)("div",(0,u.Z)((0,u.Z)({},r),{},{className:ee()(e.className,c,(0,s.Z)((0,s.Z)({},"".concat(e.className,"-collapsed"),t),"".concat(e.className,"-is-mobile"),n)),children:(0,l.jsx)(cr,{})})))},vr=x(46509),gr=x(65096),st=x(96144);function fr(a){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(a)}var hr=x(16156),_r=x(57285),ct={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},yr=function(e,n){var t,r,i=n.includes("horizontal")?(t=e.layout)===null||t===void 0?void 0:t.header:(r=e.layout)===null||r===void 0?void 0:r.sider;return(0,u.Z)((0,u.Z)((0,s.Z)({},"".concat(e.componentCls),(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({background:"transparent",color:i==null?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,s.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:i==null?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,s.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,s.Z)((0,s.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,s.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,s.Z)((0,s.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,s.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),n.includes("horizontal")?{}:(0,s.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,s.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,s.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};function xr(a,e){return(0,Me.Xj)("ProLayoutBaseMenu"+e,function(n){var t=(0,u.Z)((0,u.Z)({},n),{},{componentCls:".".concat(a)});return[yr(t,e||"inline")]})}var dt=function(e){var n=(0,g.useState)(e.collapsed),t=(0,Y.Z)(n,2),r=t[0],i=t[1],o=(0,g.useState)(!1),c=(0,Y.Z)(o,2),d=c[0],p=c[1];return(0,g.useEffect)(function(){p(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable?e.children:(0,l.jsx)(hr.Z,{title:e.title,open:r&&e.collapsed?d:!1,placement:"right",onOpenChange:p,children:e.children})},ut=(0,st.Z)({scriptUrl:ct.iconfontUrl}),pt=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",t=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if(Nn(e)||fr(e))return(0,l.jsx)("img",{width:16,src:e,alt:"icon",className:t},e);if(e.startsWith(n))return(0,l.jsx)(ut,{type:e})}return e},mt=function(e){if(e&&typeof e=="string"){var n=e.substring(0,1).toUpperCase();return n}return null},Cr=(0,vr.Z)(function a(e){var n=this;(0,gr.Z)(this,a),(0,s.Z)(this,"props",void 0),(0,s.Z)(this,"getNavMenuItems",function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return t.map(function(o){return n.getSubMenuOrItem(o,r,i)}).filter(function(o){return o}).flat(1)}),(0,s.Z)(this,"getSubMenuOrItem",function(t,r,i){var o=n.props,c=o.subMenuItemRender,d=o.baseClassName,p=o.prefixCls,m=o.collapsed,_=o.menu,v=o.iconPrefixes,S=o.layout,N=(_==null?void 0:_.type)==="group"&&S!=="top",Z=n.props.token,P=n.getIntlName(t),L=(t==null?void 0:t.children)||(t==null?void 0:t.routes),k=N&&r===0?"group":void 0;if(Array.isArray(L)&&L.length>0){var F,D,O,y,R,K=r===0||N&&r===1,w=pt(t.icon,v,"".concat(d,"-icon ").concat((F=n.props)===null||F===void 0?void 0:F.hashId)),E=m&&K?mt(P):null,te=(0,l.jsxs)("div",{className:ee()("".concat(d,"-item-title"),(D=n.props)===null||D===void 0?void 0:D.hashId,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(d,"-item-title-collapsed"),m),"".concat(d,"-item-title-collapsed-level-").concat(i),m),"".concat(d,"-group-item-title"),k==="group"),"".concat(d,"-item-collapsed-show-title"),(_==null?void 0:_.collapsedShowTitle)&&m)),children:[k==="group"&&m?null:K&&w?(0,l.jsx)("span",{className:"".concat(d,"-item-icon ").concat((O=n.props)===null||O===void 0?void 0:O.hashId).trim(),children:w}):E,(0,l.jsx)("span",{className:ee()("".concat(d,"-item-text"),(y=n.props)===null||y===void 0?void 0:y.hashId,(0,s.Z)({},"".concat(d,"-item-text-has-icon"),k!=="group"&&K&&(w||E))),children:P})]}),ie=c?c((0,u.Z)((0,u.Z)({},t),{},{isUrl:!1}),te,n.props):te;if(N&&r===0&&n.props.collapsed&&!_.collapsedShowGroupTitle)return n.getNavMenuItems(L,r+1,r);var f=n.getNavMenuItems(L,r+1,N&&r===0&&n.props.collapsed?r:r+1);return[{type:k,key:t.key||t.path,label:ie,onClick:N?void 0:t.onTitleClick,children:f,className:ee()((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(d,"-group"),k==="group"),"".concat(d,"-submenu"),k!=="group"),"".concat(d,"-submenu-has-icon"),k!=="group"&&K&&w))},N&&r===0?{type:"divider",prefixCls:p,className:"".concat(d,"-divider"),key:(t.key||t.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:n.props.collapsed?"4px":"6px 16px",marginBlockStart:n.props.collapsed?4:8,borderColor:Z==null||(R=Z.layout)===null||R===void 0||(R=R.sider)===null||R===void 0?void 0:R.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(d,"-menu-item"),disabled:t.disabled,key:t.key||t.path,onClick:t.onTitleClick,label:n.getMenuItemPath(t,r,i)}}),(0,s.Z)(this,"getIntlName",function(t){var r=t.name,i=t.locale,o=n.props,c=o.menu,d=o.formatMessage;return i&&(c==null?void 0:c.locale)!==!1?d==null?void 0:d({id:i,defaultMessage:r}):r}),(0,s.Z)(this,"getMenuItemPath",function(t,r,i){var o,c,d,p,m=n.conversionPath(t.path||"/"),_=n.props,v=_.location,S=v===void 0?{pathname:"/"}:v,N=_.isMobile,Z=_.onCollapse,P=_.menuItemRender,L=_.iconPrefixes,k=n.getIntlName(t),F=n.props,D=F.baseClassName,O=F.menu,y=F.collapsed,R=(O==null?void 0:O.type)==="group",K=r===0||R&&r===1,w=K?pt(t.icon,L,"".concat(D,"-icon ").concat((o=n.props)===null||o===void 0?void 0:o.hashId)):null,E=y&&K?mt(k):null,te=(0,l.jsxs)("div",{className:ee()("".concat(D,"-item-title"),(c=n.props)===null||c===void 0?void 0:c.hashId,(0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(D,"-item-title-collapsed"),y),"".concat(D,"-item-title-collapsed-level-").concat(i),y),"".concat(D,"-item-collapsed-show-title"),(O==null?void 0:O.collapsedShowTitle)&&y)),children:[(0,l.jsx)("span",{className:"".concat(D,"-item-icon ").concat((d=n.props)===null||d===void 0?void 0:d.hashId).trim(),style:{display:E===null&&!w?"none":""},children:w||(0,l.jsx)("span",{className:"anticon",children:E})}),(0,l.jsx)("span",{className:ee()("".concat(D,"-item-text"),(p=n.props)===null||p===void 0?void 0:p.hashId,(0,s.Z)({},"".concat(D,"-item-text-has-icon"),K&&(w||E))),children:k})]},m),ie=Nn(m);if(ie){var f,se,b;te=(0,l.jsxs)("span",{onClick:function(){var pe,oe;(pe=window)===null||pe===void 0||(oe=pe.open)===null||oe===void 0||oe.call(pe,m,"_blank")},className:ee()("".concat(D,"-item-title"),(f=n.props)===null||f===void 0?void 0:f.hashId,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(D,"-item-title-collapsed"),y),"".concat(D,"-item-title-collapsed-level-").concat(i),y),"".concat(D,"-item-link"),!0),"".concat(D,"-item-collapsed-show-title"),(O==null?void 0:O.collapsedShowTitle)&&y)),children:[(0,l.jsx)("span",{className:"".concat(D,"-item-icon ").concat((se=n.props)===null||se===void 0?void 0:se.hashId).trim(),style:{display:E===null&&!w?"none":""},children:w||(0,l.jsx)("span",{className:"anticon",children:E})}),(0,l.jsx)("span",{className:ee()("".concat(D,"-item-text"),(b=n.props)===null||b===void 0?void 0:b.hashId,(0,s.Z)({},"".concat(D,"-item-text-has-icon"),K&&(w||E))),children:k})]},m)}if(P){var Q=(0,u.Z)((0,u.Z)({},t),{},{isUrl:ie,itemPath:m,isMobile:N,replace:m===S.pathname,onClick:function(){return Z&&Z(!0)},children:void 0});return r===0?(0,l.jsx)(dt,{collapsed:y,title:k,disable:t.disabledTooltip,children:P(Q,te,n.props)}):P(Q,te,n.props)}return r===0?(0,l.jsx)(dt,{collapsed:y,title:k,disable:t.disabledTooltip,children:te}):te}),(0,s.Z)(this,"conversionPath",function(t){return t&&t.indexOf("http")===0?t:"/".concat(t||"").replace(/\/+/g,"/")}),this.props=e}),br=function(e,n){var t=n.layout,r=n.collapsed,i={};return e&&!r&&["side","mix"].includes(t||"mix")&&(i={openKeys:e}),i},vt=function(e){var n=e.mode,t=e.className,r=e.handleOpenChange,i=e.style,o=e.menuData,c=e.prefixCls,d=e.menu,p=e.matchMenuKeys,m=e.iconfontUrl,_=e.selectedKeys,v=e.onSelect,S=e.menuRenderType,N=e.openKeys,Z=(0,g.useContext)(ce.L_),P=Z.dark,L=Z.token,k="".concat(c,"-base-menu-").concat(n),F=(0,g.useRef)([]),D=(0,me.Z)(d==null?void 0:d.defaultOpenAll),O=(0,Y.Z)(D,2),y=O[0],R=O[1],K=(0,me.Z)(function(){return d!=null&&d.defaultOpenAll?rt(o)||[]:N===!1?!1:[]},{value:N===!1?void 0:N,onChange:r}),w=(0,Y.Z)(K,2),E=w[0],te=w[1],ie=(0,me.Z)([],{value:_,onChange:v?function(ge){v&&ge&&v(ge)}:void 0}),f=(0,Y.Z)(ie,2),se=f[0],b=f[1];(0,g.useEffect)(function(){d!=null&&d.defaultOpenAll||N===!1||p&&(te(p),b(p))},[p.join("-")]),(0,g.useEffect)(function(){m&&(ut=(0,st.Z)({scriptUrl:m}))},[m]),(0,g.useEffect)(function(){if(p.join("-")!==(se||[]).join("-")&&b(p),!y&&N!==!1&&p.join("-")!==(E||[]).join("-")){var ge=p;(d==null?void 0:d.autoClose)===!1&&(ge=Array.from(new Set([].concat((0,dn.Z)(p),(0,dn.Z)(E||[]))))),te(ge)}else d!=null&&d.ignoreFlatMenu&&y?te(rt(o)):R(!1)},[p.join("-")]);var Q=(0,g.useMemo)(function(){return br(E,e)},[E&&E.join(","),e.layout,e.collapsed]),ae=xr(k,n),pe=ae.wrapSSR,oe=ae.hashId,ye=(0,g.useMemo)(function(){return new Cr((0,u.Z)((0,u.Z)({},e),{},{token:L,menuRenderType:S,baseClassName:k,hashId:oe}))},[e,L,S,k,oe]);if(d!=null&&d.loading)return(0,l.jsx)("div",{style:n!=null&&n.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,l.jsx)(_r.Z,{active:!0,title:!1,paragraph:{rows:n!=null&&n.includes("inline")?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(F.current=p);var ve=e.postMenuData?e.postMenuData(o):o;return ve&&(ve==null?void 0:ve.length)<1?null:pe((0,g.createElement)(lt.Z,(0,u.Z)((0,u.Z)({},Q),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:n,inlineIndent:16,defaultOpenKeys:F.current,theme:P?"dark":"light",selectedKeys:se,style:(0,u.Z)({backgroundColor:"transparent",border:"none"},i),className:ee()(t,oe,k,(0,s.Z)((0,s.Z)({},"".concat(k,"-horizontal"),n==="horizontal"),"".concat(k,"-collapsed"),e.collapsed)),items:ye.getNavMenuItems(ve,0,0),onOpenChange:function(Ze){e.collapsed||te(Ze)}},e.menuProps)))};function Sr(a,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Me.Xj)("ProLayoutSiderMenuStylish",function(r){var i=(0,u.Z)((0,u.Z)({},r),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:t});return n?[(0,s.Z)({},"div".concat(r.proComponentsCls,"-layout"),(0,s.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var Pr=["title","render"],Mr=g.memo(function(a){return(0,l.jsx)(l.Fragment,{children:a.children})}),Ir=Xe.Sider,gt=Xe._InternalSiderContext,Rr=gt===void 0?{Provider:Mr}:gt,Bn=function(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",t=e.logo,r=e.title,i=e.layout,o=e[n];if(o===!1)return null;var c=kn(t),d=(0,l.jsx)("h1",{children:r!=null?r:"Ant Design Pro"});return o?o(c,e.collapsed?null:d,e):e.isMobile?null:i==="mix"&&n==="menuHeaderRender"?!1:e.collapsed?(0,l.jsx)("a",{children:c},"title"):(0,l.jsxs)("a",{children:[c,d]},"title")},ft=function(e){var n,t=e.collapsed,r=e.originCollapsed,i=e.fixSiderbar,o=e.menuFooterRender,c=e.onCollapse,d=e.theme,p=e.siderWidth,m=e.isMobile,_=e.onMenuHeaderClick,v=e.breakpoint,S=v===void 0?"lg":v,N=e.style,Z=e.layout,P=e.menuExtraRender,L=P===void 0?!1:P,k=e.links,F=e.menuContentRender,D=e.collapsedButtonRender,O=e.prefixCls,y=e.avatarProps,R=e.rightContentRender,K=e.actionsRender,w=e.onOpenChange,E=e.stylish,te=e.logoStyle,ie=(0,g.useContext)(ce.L_),f=ie.hashId,se=(0,g.useMemo)(function(){return!(m||Z==="mix")},[m,Z]),b="".concat(O,"-sider"),Q=64,ae=Sr("".concat(b,".").concat(b,"-stylish"),{stylish:E,proLayoutCollapsedWidth:Q}),pe=ee()("".concat(b),f,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(b,"-fixed"),i),"".concat(b,"-fixed-mix"),Z==="mix"&&!m&&i),"".concat(b,"-collapsed"),e.collapsed),"".concat(b,"-layout-").concat(Z),Z&&!m),"".concat(b,"-light"),d!=="dark"),"".concat(b,"-mix"),Z==="mix"&&!m),"".concat(b,"-stylish"),!!E)),oe=Bn(e),ye=L&&L(e),ve=(0,g.useMemo)(function(){return F!==!1&&(0,g.createElement)(vt,(0,u.Z)((0,u.Z)({},e),{},{key:"base-menu",mode:t&&!m?"vertical":"inline",handleOpenChange:w,style:{width:"100%"},className:"".concat(b,"-menu ").concat(f).trim()}))},[b,f,F,w,e]),ge=(k||[]).map(function(xe,ke){return{className:"".concat(b,"-link"),label:xe,key:ke}}),Ze=(0,g.useMemo)(function(){return F?F(e,ve):ve},[F,ve,e]),Ie=(0,g.useMemo)(function(){if(!y)return null;var xe=y.title,ke=y.render,De=(0,fe.Z)(y,Pr),Sn=(0,l.jsxs)("div",{className:"".concat(b,"-actions-avatar"),children:[De!=null&&De.src||De!=null&&De.srcSet||De.icon||De.children?(0,l.jsx)(it.Z,(0,u.Z)({size:28},De)):null,y.title&&!t&&(0,l.jsx)("span",{children:xe})]});return ke?ke(y,Sn,e):Sn},[y,b,t]),Te=(0,g.useMemo)(function(){return K?(0,l.jsx)(sr.Z,{align:"center",size:4,direction:t?"vertical":"horizontal",className:ee()(["".concat(b,"-actions-list"),t&&"".concat(b,"-actions-list-collapsed"),f]),children:[K==null?void 0:K(e)].flat(1).map(function(xe,ke){return(0,l.jsx)("div",{className:"".concat(b,"-actions-list-item ").concat(f).trim(),children:xe},ke)})}):null},[K,b,t]),Le=(0,g.useMemo)(function(){return(0,l.jsx)(On,{onItemClick:e.itemClick,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.prefixCls]),$e=(0,g.useMemo)(function(){if(D===!1)return null;var xe=(0,l.jsx)(mr,{isMobile:m,collapsed:r,className:"".concat(b,"-collapsed-button"),onClick:function(){c==null||c(!r)}});return D?D(t,xe):xe},[D,m,r,b,t,c]),ze=(0,g.useMemo)(function(){return!Ie&&!Te?null:(0,l.jsxs)("div",{className:ee()("".concat(b,"-actions"),f,t&&"".concat(b,"-actions-collapsed")),children:[Ie,Te]})},[Te,Ie,b,t,f]),Ue=(0,g.useMemo)(function(){var xe;return e!=null&&(xe=e.menu)!==null&&xe!==void 0&&xe.hideMenuWhenCollapsed&&t?"".concat(b,"-hide-menu-collapsed"):null},[b,t,e==null||(n=e.menu)===null||n===void 0?void 0:n.hideMenuWhenCollapsed]),un=o&&(o==null?void 0:o(e)),bn=(0,l.jsxs)(l.Fragment,{children:[oe&&(0,l.jsxs)("div",{className:ee()([ee()("".concat(b,"-logo"),f,(0,s.Z)({},"".concat(b,"-logo-collapsed"),t))]),onClick:se?_:void 0,id:"logo",style:te,children:[oe,Le]}),ye&&(0,l.jsx)("div",{className:ee()(["".concat(b,"-extra"),!oe&&"".concat(b,"-extra-no-logo"),f]),children:ye}),(0,l.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:Ze}),(0,l.jsxs)(Rr.Provider,{value:{},children:[k?(0,l.jsx)("div",{className:"".concat(b,"-links ").concat(f).trim(),children:(0,l.jsx)(lt.Z,{inlineIndent:16,className:"".concat(b,"-link-menu ").concat(f).trim(),selectedKeys:[],openKeys:[],theme:d,mode:"inline",items:ge})}):null,se&&(0,l.jsxs)(l.Fragment,{children:[ze,!Te&&R?(0,l.jsx)("div",{className:ee()("".concat(b,"-actions"),f,(0,s.Z)({},"".concat(b,"-actions-collapsed"),t)),children:R==null?void 0:R(e)}):null]}),un&&(0,l.jsx)("div",{className:ee()(["".concat(b,"-footer"),f,(0,s.Z)({},"".concat(b,"-footer-collapsed"),t)]),children:un})]})]});return ae.wrapSSR((0,l.jsxs)(l.Fragment,{children:[i&&!m&&!Ue&&(0,l.jsx)("div",{style:(0,u.Z)({width:t?Q:p,overflow:"hidden",flex:"0 0 ".concat(t?Q:p,"px"),maxWidth:t?Q:p,minWidth:t?Q:p,transition:"all 0.2s ease 0s"},N)}),(0,l.jsxs)(Ir,{collapsible:!0,trigger:null,collapsed:t,breakpoint:S===!1?void 0:S,onCollapse:function(ke){m||c==null||c(ke)},collapsedWidth:Q,style:N,theme:d,width:p,className:ee()(pe,f,Ue),children:[Ue?(0,l.jsx)("div",{className:"".concat(b,"-hide-when-collapsed ").concat(f).trim(),style:{height:"100%",width:"100%",opacity:Ue?0:1},children:bn}):bn,$e]})]}))},Tr=x(67902),Er=x(20488),Zr=function(e){var n,t,r,i,o;return(0,s.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:(r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorTextRightActionsItem,"> div":{height:"44px",color:(i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:(o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgRightActionsItemHover}}}}})};function Dr(a){return(0,Me.Xj)("ProLayoutRightContent",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[Zr(n)]})}var wr=["rightContentRender","avatarProps","actionsRender","headerContentRender"],Ar=["title","render"],ht=function(e){var n=e.rightContentRender,t=e.avatarProps,r=e.actionsRender,i=e.headerContentRender,o=(0,fe.Z)(e,wr),c=(0,g.useContext)(Ne.ZP.ConfigContext),d=c.getPrefixCls,p="".concat(d(),"-pro-global-header"),m=Dr(p),_=m.wrapSSR,v=m.hashId,S=(0,g.useState)("auto"),N=(0,Y.Z)(S,2),Z=N[0],P=N[1],L=(0,g.useMemo)(function(){if(!t)return null;var O=t.title,y=t.render,R=(0,fe.Z)(t,Ar),K=[R!=null&&R.src||R!=null&&R.srcSet||R.icon||R.children?(0,g.createElement)(it.Z,(0,u.Z)((0,u.Z)({},R),{},{size:28,key:"avatar"})):null,O?(0,l.jsx)("span",{style:{marginInlineStart:8},children:O},"name"):void 0];return y?y(t,(0,l.jsx)("div",{children:K}),o):(0,l.jsx)("div",{children:K})},[t]),k=r||L?function(O){var y=r&&(r==null?void 0:r(O));return!y&&!L?null:Array.isArray(y)?_((0,l.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(v).trim(),children:[y.filter(Boolean).map(function(R,K){var w=!1;if(g.isValidElement(R)){var E;w=!!(R!=null&&(E=R.props)!==null&&E!==void 0&&E["aria-hidden"])}return(0,l.jsx)("div",{className:ee()("".concat(p,"-header-actions-item ").concat(v),(0,s.Z)({},"".concat(p,"-header-actions-hover"),!w)),children:R},K)}),L&&(0,l.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(v).trim(),children:L})]})):_((0,l.jsxs)("div",{className:"".concat(p,"-header-actions ").concat(v).trim(),children:[y,L&&(0,l.jsx)("span",{className:"".concat(p,"-header-actions-avatar ").concat(v).trim(),children:L})]}))}:void 0,F=(0,Tr.D)(function(){var O=(0,le.Z)((0,he.Z)().mark(function y(R){return(0,he.Z)().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:P(R);case 1:case"end":return w.stop()}},y)}));return function(y){return O.apply(this,arguments)}}(),160),D=k||n;return(0,l.jsx)("div",{className:"".concat(p,"-right-content ").concat(v).trim(),style:{minWidth:Z,height:"100%"},children:(0,l.jsx)("div",{style:{height:"100%"},children:(0,l.jsx)(Er.Z,{onResize:function(y){var R=y.width;F.run(R)},children:D?(0,l.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:D((0,u.Z)((0,u.Z)({},o),{},{rightContentSize:Z}))}):null})})})},jr=function(e){var n,t;return(0,s.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,s.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max((((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56)-12,40),"px")}})};function Nr(a){return(0,Me.Xj)("ProLayoutTopNavHeader",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[jr(n)]})}var _t=function(e){var n,t,r,i,o,c,d,p=(0,g.useRef)(null),m=e.onMenuHeaderClick,_=e.contentWidth,v=e.rightContentRender,S=e.className,N=e.style,Z=e.headerContentRender,P=e.layout,L=e.actionsRender,k=(0,g.useContext)(Ne.ZP.ConfigContext),F=k.getPrefixCls,D=(0,g.useContext)(ce.L_),O=D.dark,y="".concat(e.prefixCls||F("pro"),"-top-nav-header"),R=Nr(y),K=R.wrapSSR,w=R.hashId,E=void 0;e.menuHeaderRender!==void 0?E="menuHeaderRender":(P==="mix"||P==="top")&&(E="headerTitleRender");var te=Bn((0,u.Z)((0,u.Z)({},e),{},{collapsed:!1}),E),ie=(0,g.useContext)(ce.L_),f=ie.token,se=(0,g.useMemo)(function(){var b,Q,ae,pe,oe,ye,ve,ge,Ze,Ie,Te,Le,$e,ze=(0,l.jsx)(Ne.ZP,{theme:{hashed:(0,ce.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,u.Z)({},H({colorItemBg:((b=f.layout)===null||b===void 0||(b=b.header)===null||b===void 0?void 0:b.colorBgHeader)||"transparent",colorSubItemBg:((Q=f.layout)===null||Q===void 0||(Q=Q.header)===null||Q===void 0?void 0:Q.colorBgHeader)||"transparent",radiusItem:f.borderRadius,colorItemBgSelected:((ae=f.layout)===null||ae===void 0||(ae=ae.header)===null||ae===void 0?void 0:ae.colorBgMenuItemSelected)||(f==null?void 0:f.colorBgTextHover),itemHoverBg:((pe=f.layout)===null||pe===void 0||(pe=pe.header)===null||pe===void 0?void 0:pe.colorBgMenuItemHover)||(f==null?void 0:f.colorBgTextHover),colorItemBgSelectedHorizontal:((oe=f.layout)===null||oe===void 0||(oe=oe.header)===null||oe===void 0?void 0:oe.colorBgMenuItemSelected)||(f==null?void 0:f.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((ye=f.layout)===null||ye===void 0||(ye=ye.header)===null||ye===void 0?void 0:ye.colorTextMenu)||(f==null?void 0:f.colorTextSecondary),colorItemTextHoverHorizontal:((ve=f.layout)===null||ve===void 0||(ve=ve.header)===null||ve===void 0?void 0:ve.colorTextMenuActive)||(f==null?void 0:f.colorText),colorItemTextSelectedHorizontal:((ge=f.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.colorTextMenuSelected)||(f==null?void 0:f.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:((Ze=f.layout)===null||Ze===void 0||(Ze=Ze.header)===null||Ze===void 0?void 0:Ze.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:((Ie=f.layout)===null||Ie===void 0||(Ie=Ie.header)===null||Ie===void 0?void 0:Ie.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:((Te=f.layout)===null||Te===void 0||(Te=Te.header)===null||Te===void 0?void 0:Te.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:f==null?void 0:f.colorBgElevated,subMenuItemBg:f==null?void 0:f.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:f==null?void 0:f.colorBgElevated}))},token:{colorBgElevated:((Le=f.layout)===null||Le===void 0||(Le=Le.header)===null||Le===void 0?void 0:Le.colorBgHeader)||"transparent"}},children:(0,l.jsx)(vt,(0,u.Z)((0,u.Z)((0,u.Z)({theme:O?"dark":"light"},e),{},{className:"".concat(y,"-base-menu ").concat(w).trim()},e.menuProps),{},{style:(0,u.Z)({width:"100%"},($e=e.menuProps)===null||$e===void 0?void 0:$e.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return Z?Z(e,ze):ze},[(n=f.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgHeader,(t=f.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgMenuItemSelected,(r=f.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorBgMenuItemHover,(i=f.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextMenu,(o=f.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorTextMenuActive,(c=f.layout)===null||c===void 0||(c=c.header)===null||c===void 0?void 0:c.colorTextMenuSelected,(d=f.layout)===null||d===void 0||(d=d.header)===null||d===void 0?void 0:d.colorBgMenuElevated,f.borderRadius,f==null?void 0:f.colorBgTextHover,f==null?void 0:f.colorTextSecondary,f==null?void 0:f.colorText,f==null?void 0:f.colorTextBase,f.colorBgElevated,O,e,y,w,Z]);return K((0,l.jsx)("div",{className:ee()(y,w,S,(0,s.Z)({},"".concat(y,"-light"),!0)),style:N,children:(0,l.jsxs)("div",{ref:p,className:ee()("".concat(y,"-main"),w,(0,s.Z)({},"".concat(y,"-wide"),_==="Fixed"&&P==="top")),children:[te&&(0,l.jsxs)("div",{className:ee()("".concat(y,"-main-left ").concat(w)),onClick:m,children:[(0,l.jsx)(On,(0,u.Z)({},e)),(0,l.jsx)("div",{className:"".concat(y,"-logo ").concat(w).trim(),id:"logo",children:te},"logo")]}),(0,l.jsx)("div",{style:{flex:1},className:"".concat(y,"-menu ").concat(w).trim(),children:se}),(v||L||e.avatarProps)&&(0,l.jsx)(ht,(0,u.Z)((0,u.Z)({rightContentRender:v},e),{},{prefixCls:y}))]})}))},kr=function(e){var n,t,r;return(0,s.Z)({},e.componentCls,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:((r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};function Or(a){return(0,Me.Xj)("ProLayoutGlobalHeader",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[kr(n)]})}var Br=function(e,n){return e===!1?null:e?e(n,null):n},Lr=function(e){var n=e.isMobile,t=e.logo,r=e.collapsed,i=e.onCollapse,o=e.rightContentRender,c=e.menuHeaderRender,d=e.onMenuHeaderClick,p=e.className,m=e.style,_=e.layout,v=e.children,S=e.splitMenus,N=e.menuData,Z=e.prefixCls,P=(0,g.useContext)(Ne.ZP.ConfigContext),L=P.getPrefixCls,k=P.direction,F="".concat(Z||L("pro"),"-global-header"),D=Or(F),O=D.wrapSSR,y=D.hashId,R=ee()(p,F,y);if(_==="mix"&&!n&&S){var K=(N||[]).map(function(ie){return(0,u.Z)((0,u.Z)({},ie),{},{children:void 0,routes:void 0})}),w=xn(K);return(0,l.jsx)(_t,(0,u.Z)((0,u.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:w}))}var E=ee()("".concat(F,"-logo"),y,(0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(F,"-logo-rtl"),k==="rtl"),"".concat(F,"-logo-mix"),_==="mix"),"".concat(F,"-logo-mobile"),n)),te=(0,l.jsx)("span",{className:E,children:(0,l.jsx)("a",{children:kn(t)})},"logo");return O((0,l.jsxs)("div",{className:R,style:(0,u.Z)({},m),children:[n&&(0,l.jsx)("span",{className:"".concat(F,"-collapsed-button ").concat(y).trim(),onClick:function(){i==null||i(!r)},children:(0,l.jsx)(Ja,{})}),n&&Br(c,te),_==="mix"&&!n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(On,(0,u.Z)({},e)),(0,l.jsx)("div",{className:E,onClick:d,children:Bn((0,u.Z)((0,u.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,l.jsx)("div",{style:{flex:1},children:v}),(o||e.actionsRender||e.avatarProps)&&(0,l.jsx)(ht,(0,u.Z)({rightContentRender:o},e))]}))},Hr=function(e){var n,t,r,i;return(0,s.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,s.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat(((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:((r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:((i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function Fr(a){return(0,Me.Xj)("ProLayoutHeader",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[Hr(n)]})}function Wr(a,e){var n=e.stylish,t=e.proLayoutCollapsedWidth;return(0,Me.Xj)("ProLayoutHeaderStylish",function(r){var i=(0,u.Z)((0,u.Z)({},r),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:t});return n?[(0,s.Z)({},"div".concat(r.proComponentsCls,"-layout"),(0,s.Z)({},"".concat(i.componentCls),n==null?void 0:n(i)))]:[]})}var yt=Xe.Header,$r=function(e){var n,t,r,i=e.isMobile,o=e.fixedHeader,c=e.className,d=e.style,p=e.collapsed,m=e.prefixCls,_=e.onCollapse,v=e.layout,S=e.headerRender,N=e.headerContentRender,Z=(0,g.useContext)(ce.L_),P=Z.token,L=(0,g.useContext)(Ne.ZP.ConfigContext),k=(0,g.useState)(!1),F=(0,Y.Z)(k,2),D=F[0],O=F[1],y=o||v==="mix",R=(0,g.useCallback)(function(){var b=v==="top",Q=xn(e.menuData||[]),ae=(0,l.jsx)(Lr,(0,u.Z)((0,u.Z)({onCollapse:_},e),{},{menuData:Q,children:N&&N(e,null)}));return b&&!i&&(ae=(0,l.jsx)(_t,(0,u.Z)((0,u.Z)({mode:"horizontal",onCollapse:_},e),{},{menuData:Q}))),S&&typeof S=="function"?S(e,ae):ae},[N,S,i,v,_,e]);(0,g.useEffect)(function(){var b,Q=(L==null||(b=L.getTargetContainer)===null||b===void 0?void 0:b.call(L))||document.body,ae=function(){var oe,ye=Q.scrollTop;return ye>(((oe=P.layout)===null||oe===void 0||(oe=oe.header)===null||oe===void 0?void 0:oe.heightLayoutHeader)||56)&&!D?(O(!0),!0):(D&&O(!1),!1)};if(y&&typeof window!="undefined")return Q.addEventListener("scroll",ae,{passive:!0}),function(){Q.removeEventListener("scroll",ae)}},[(n=P.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader,y,D]);var K=v==="top",w="".concat(m,"-layout-header"),E=Fr(w),te=E.wrapSSR,ie=E.hashId,f=Wr("".concat(w,".").concat(w,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),se=ee()(c,ie,w,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(w,"-fixed-header"),y),"".concat(w,"-fixed-header-scroll"),D),"".concat(w,"-mix"),v==="mix"),"".concat(w,"-fixed-header-action"),!p),"".concat(w,"-top-menu"),K),"".concat(w,"-header"),!0),"".concat(w,"-stylish"),!!e.stylish));return v==="side"&&!i?null:f.wrapSSR(te((0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(Ne.ZP,{theme:{hashed:(0,ce.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[y&&(0,l.jsx)(yt,{style:(0,u.Z)({height:((t=P.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat(((r=P.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},d)}),(0,l.jsx)(yt,{className:se,style:d,children:R()})]})})))},zr=x(58184),Ur=["isLoading","pastDelay","timedOut","error","retry"],Gr=function(e){var n=e.isLoading,t=e.pastDelay,r=e.timedOut,i=e.error,o=e.retry,c=(0,fe.Z)(e,Ur);return(0,l.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,l.jsx)(zr.Z,(0,u.Z)({size:"large"},c))})},Vr=x(27150),Kr=x(40044),xt=new Kr.Keyframes("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),Xr=function(e){var n,t,r,i,o,c,d,p,m,_,v,S;return(0,s.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:((n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground)||"transparent"}),e.componentCls,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.paddingInlineLayoutMenu,paddingBlock:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,s.Z)((0,s.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:hover"),{color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat((c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:(d=e.layout)===null||d===void 0||(d=d.sider)===null||d===void 0?void 0:d.colorTextMenuTitle,animationName:xt,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,s.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:(p=e.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:(m=e.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:(_=e.layout)===null||_===void 0||(_=_.sider)===null||_===void 0?void 0:_.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:xt,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat(((v=e.layout)===null||v===void 0||(v=v.header)===null||v===void 0?void 0:v.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat(((S=e.layout)===null||S===void 0||(S=S.header)===null||S===void 0?void 0:S.heightLayoutHeader)||56,"px")}}))};function Yr(a,e){var n=e.proLayoutCollapsedWidth;return(0,Me.Xj)("ProLayoutSiderMenu",function(t){var r=(0,u.Z)((0,u.Z)({},t),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:n});return[Xr(r)]})}var Ct=function(e){var n,t=e.isMobile,r=e.siderWidth,i=e.collapsed,o=e.onCollapse,c=e.style,d=e.className,p=e.hide,m=e.prefixCls,_=(0,g.useContext)(ce.L_),v=_.token;(0,g.useEffect)(function(){t===!0&&(o==null||o(!0))},[t]);var S=(0,nt.Z)(e,["className","style"]),N=g.useContext(Ne.ZP.ConfigContext),Z=N.direction,P=Yr("".concat(m,"-sider"),{proLayoutCollapsedWidth:64}),L=P.wrapSSR,k=P.hashId,F=ee()("".concat(m,"-sider"),d,k);if(p)return null;var D=(0,C.X)(!i,function(){return o==null?void 0:o(!0)});return L(t?(0,l.jsx)(Vr.Z,(0,u.Z)((0,u.Z)({placement:Z==="rtl"?"right":"left",className:ee()("".concat(m,"-drawer-sider"),d)},D),{},{style:(0,u.Z)({padding:0,height:"100vh"},c),onClose:function(){o==null||o(!0)},maskClosable:!0,closable:!1,width:r,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:(n=v.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorMenuBackground}},children:(0,l.jsx)(ft,(0,u.Z)((0,u.Z)({},S),{},{isMobile:!0,className:F,collapsed:t?!1:i,splitMenus:!1,originCollapsed:i}))})):(0,l.jsx)(ft,(0,u.Z)((0,u.Z)({className:F,originCollapsed:i},S),{},{style:c})))},bt=(0,g.createContext)({}),Qr=x(36497),Ln=x.n(Qr),Jr=function(e,n,t){if(t){var r=(0,dn.Z)(t.keys()).find(function(o){return Ln()(o).test(e)});if(r)return t.get(r)}if(n){var i=Object.keys(n).find(function(o){return Ln()(o).test(e)});if(i)return n[i]}return{path:""}},Hn=function(e,n){var t=e.pathname,r=t===void 0?"/":t,i=e.breadcrumb,o=e.breadcrumbMap,c=e.formatMessage,d=e.title,p=e.menu,m=p===void 0?{locale:!1}:p,_=n?"":d||"",v=Jr(r,i,o);if(!v)return{title:_,id:"",pageName:_};var S=v.name;return m.locale!==!1&&v.locale&&c&&(S=c({id:v.locale||"",defaultMessage:v.name})),S?n||!d?{title:S,id:v.locale||"",pageName:S}:{title:"".concat(S," - ").concat(d),id:v.locale||"",pageName:S}:{title:_,id:v.locale||"",pageName:_}},ti=function(e,n){return Hn(e,n).title},qr={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},eo=(0,u.Z)({},qr),no={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},to=(0,u.Z)({},no),ao={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},ro=(0,u.Z)({},ao),oo={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},io=(0,u.Z)({},oo),lo={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},so=(0,u.Z)({},lo),St={"zh-CN":io,"zh-TW":so,"en-US":eo,"it-IT":to,"ko-KR":ro},co=function(){if(!(0,q.j)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},uo=function(){var e=co();return St[e]||St["zh-CN"]},Cn=x(84581),qe=x(73656),po=function(){var e;return typeof qe=="undefined"?Cn.Z:((e=qe)===null||qe===void 0||(qe={AISE_MINIO_API_PORT:"19000",AISE_MYSQL_PORT:"13306",AISE_NACOS_PORT:"18848",AISE_REDIS_PORT:"16379",ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",APP_TARGET:"opensource","asl.log":"Destination=file",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"ISS",ComSpec:"C:\\Windows\\system32\\cmd.exe",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",HOME:"C:\\Users\\<USER>\\Users\\issuser",INIT_CWD:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\ISS",major_version:"18",NODE:"E:\\node\\node.exe",NODE_ENV:"production",NODE_OPTIONS:"--openssl-legacy-provider",NODE_PATH:"D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\bin\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\bin\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules",node_version:"v20.18.1",npm_command:"run-script",npm_config_cache:"E:\\nvm\\nvm\\node_cache",npm_config_frozen_lockfile:"",npm_config_node_gyp:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_prefix:"E:\\nvm\\nvm\\node_global",npm_config_registry:"https://registry.npmmirror.com",npm_config_user_agent:"pnpm/9.12.3 npm/? node/v20.18.1 win32 x64",npm_config__tiptap_pro_registry:"https://registry.tiptap.dev/",npm_execpath:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\bin\\pnpm.cjs",npm_lifecycle_event:"build:os-local",npm_lifecycle_script:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build",npm_node_execpath:"E:\\node\\node.exe",npm_package_browserslist_0:"> 1%",npm_package_browserslist_1:"last 2 versions",npm_package_browserslist_2:"not ie <= 10",npm_package_dependencies_ace_builds:"^1.4.12",npm_package_dependencies_ahooks:"^3.7.7",npm_package_dependencies_antd:"^5.17.4",npm_package_dependencies_classnames:"^2.2.6",npm_package_dependencies_compression_webpack_plugin:"^11.0.0",npm_package_dependencies_copy_to_clipboard:"^3.3.1",npm_package_dependencies_cross_env:"^7.0.3",npm_package_dependencies_crypto_js:"^4.2.0",npm_package_dependencies_dayjs:"^1.11.10",npm_package_dependencies_echarts:"^5.0.2",npm_package_dependencies_echarts_for_react:"^3.0.1",npm_package_dependencies_eslint_config_tencent:"^1.0.4",npm_package_dependencies_hchatdata_chat_sdk:"workspace:*",npm_package_dependencies_jsencrypt:"^3.0.1",npm_package_dependencies_lodash:"^4.17.11",npm_package_dependencies_moment:"^2.29.1",npm_package_dependencies_nprogress:"^0.2.0",npm_package_dependencies_numeral:"^2.0.6",npm_package_dependencies_omit_js:"^2.0.2",npm_package_dependencies_path_to_regexp:"^2.4.0",npm_package_dependencies_qs:"^6.9.0",npm_package_dependencies_query_string:"^9.0.0",npm_package_dependencies_react:"^18.3.1",npm_package_dependencies_react_ace:"^9.4.1",npm_package_dependencies_react_dom:"^18.3.1",npm_package_dependencies_react_spinners:"^0.13.8",npm_package_dependencies_react_split_pane:"^2.0.3",npm_package_dependencies_react_syntax_highlighter:"^15.4.3",npm_package_dependencies_sql_formatter:"^15.6.1",npm_package_dependencies_supersonic_insights_flow_components:"^1.4.9",npm_package_dependencies_umi_request:"1.4.0",npm_package_dependencies__antv_dom_util:"^2.0.4",npm_package_dependencies__antv_g6:"^4.8.23",npm_package_dependencies__antv_g6_core:"^0.8.23",npm_package_dependencies__antv_layout:"^0.3.20",npm_package_dependencies__antv_x6:"1.30.1",npm_package_dependencies__ant_design_icons:"^5.2.6",npm_package_dependencies__ant_design_pro_components:"2.7.0",npm_package_dependencies__babel_runtime:"^7.22.5",npm_package_dependencies__types_numeral:"^2.0.2",npm_package_dependencies__types_react_draft_wysiwyg:"^1.13.2",npm_package_dependencies__types_react_syntax_highlighter:"^13.5.0",npm_package_dependencies__umijs_route_utils:"2.2.2",npm_package_description:"data chat",npm_package_devDependencies_carlo:"^0.9.46",npm_package_devDependencies_compression:"^1.8.1",npm_package_devDependencies_cross_port_killer:"^1.1.1",npm_package_devDependencies_detect_installer:"^1.0.1",npm_package_devDependencies_eslint:"^7.1.0",npm_package_devDependencies_eslint_plugin_chalk:"^1.0.0",npm_package_devDependencies_eslint_plugin_import:"^2.27.5",npm_package_devDependencies_express:"^4.21.2",npm_package_devDependencies_gh_pages:"^3.0.0",npm_package_devDependencies_http_proxy_middleware:"^2.0.6",npm_package_devDependencies_jsdom_global:"^3.0.2",npm_package_devDependencies_lint_staged:"^10.0.0",npm_package_devDependencies_prettier:"^2.3.1",npm_package_devDependencies_pro_download:"1.0.1",npm_package_devDependencies_puppeteer_core:"^5.0.0",npm_package_devDependencies_stylelint:"^13.0.0",npm_package_devDependencies_typescript:"^4.0.3",npm_package_devDependencies_umi_presets_pro:"2.0.2",npm_package_devDependencies__ant_design_pro_cli:"^2.0.2",npm_package_devDependencies__types_classnames:"^2.2.7",npm_package_devDependencies__types_crypto_js:"^4.0.1",npm_package_devDependencies__types_draftjs_to_html:"^0.8.0",npm_package_devDependencies__types_echarts:"^4.9.4",npm_package_devDependencies__types_express:"^4.17.0",npm_package_devDependencies__types_history:"^4.7.2",npm_package_devDependencies__types_jest:"^26.0.0",npm_package_devDependencies__types_lodash:"^4.14.144",npm_package_devDependencies__types_pinyin:"^2.8.3",npm_package_devDependencies__types_qs:"^6.5.3",npm_package_devDependencies__types_react:"18.3.1",npm_package_devDependencies__types_react_dom:"^18.3.0",npm_package_devDependencies__types_react_helmet:"^6.1.0",npm_package_devDependencies__umijs_fabric:"^4.0.1",npm_package_devDependencies__umijs_max:"^4.2.5",npm_package_devDependencies__umijs_plugin_model:"^2.6.2",npm_package_engines_node:">=16.0.0",npm_package_lint_staged______less:"stylelint --syntax less",npm_package_lint_staged_______js_jsx_tsx_ts_less_md_json__0:"prettier --write",npm_package_lint_staged_______js_jsx_ts_tsx_:"npm run lint-staged:js",npm_package_name:"hchatdata-fe",npm_package_packageManager:"pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee",npm_package_private:"true",npm_package_scripts_analyze:"cross-env ANALYZE=1 max build",npm_package_scripts_build:"npm run build:os",npm_package_scripts_build_inner:"cross-env REACT_APP_ENV=prod APP_TARGET=inner max build",npm_package_scripts_build_os:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build",npm_package_scripts_build_os_local:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build",npm_package_scripts_build_test:"cross-env REACT_APP_ENV=test max build",npm_package_scripts_deploy:"npm run site && npm run gh-pages",npm_package_scripts_dev:"npm run start:osdev",npm_package_scripts_dev_inner:"npm run start:dev",npm_package_scripts_dev_os:"npm run start:osdev",npm_package_scripts_gh_pages:"gh-pages -d dist",npm_package_scripts_i18n_remove:"pro i18n-remove --locale=zh-CN --write",npm_package_scripts_lint:"max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier",npm_package_scripts_lint_fix:"eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style",npm_package_scripts_lint_js:"eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src",npm_package_scripts_lint_prettier:'prettier --check "src/**/*" --end-of-line auto',npm_package_scripts_lint_staged:"lint-staged",npm_package_scripts_lint_staged_js:"eslint --ext .js,.jsx,.ts,.tsx ",npm_package_scripts_lint_style:'stylelint --fix "src/**/*.less" --syntax less',npm_package_scripts_no_build_inner:"cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build",npm_package_scripts_no_dev_inner:"NODE_OPTIONS=--openssl-legacy-provider npm run start:dev",npm_package_scripts_no_dev_os:"NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev",npm_package_scripts_postinstall:"max setup",npm_package_scripts_precommit:"lint-staged",npm_package_scripts_pretest:"node ./tests/beforeTest",npm_package_scripts_prettier:'prettier -c --write "src/**/*"',npm_package_scripts_start:"npm run start:osdev",npm_package_scripts_start_dev:"cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev",npm_package_scripts_start_no_mock:"cross-env MOCK=none max dev",npm_package_scripts_start_no_ui:"cross-env UMI_UI=none max dev",npm_package_scripts_start_osdev:"cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev",npm_package_scripts_start_pre:"cross-env REACT_APP_ENV=pre max dev",npm_package_scripts_start_test:"cross-env REACT_APP_ENV=test MOCK=none max dev",npm_package_scripts_test:"max test",npm_package_scripts_test_all:"node ./tests/run-tests.js",npm_package_scripts_test_component:"max test ./src/components",npm_package_scripts_tsc:"tsc --noEmit",npm_package_version:"0.1.0",NUMBER_OF_PROCESSORS:"8",NVM_HOME:"E:\\nvm\\nvm",NVM_NODE_GLOBAL:"E:\\nvm\\nvm\\node_global",NVM_SYMLINK:"E:\\node",OneDrive:"C:\\Users\\<USER>\\OneDrive",OS:"Windows_NT",Path:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node-gyp-bin;D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;E:\\nvm\\nvm;E:\\node;C:\\Program Files\\CorpLink\\current\\module\\mdm\\x64\\policy\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\python311\\Scripts\\;C:\\python311\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\nvm\\nvm;E:\\node;D:\\\u8F6F\u901A\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC",PNPM_SCRIPT_SRC_DIR:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 142 Stepping 12, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"8e0c",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Windows",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",UMI_DIR:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq\\node_modules\\umi",UMI_PRESETS:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"ISS",USERDOMAIN_ROAMINGPROFILE:"ISS",USERNAME:"issuser",USERPROFILE:"C:\\Users\\<USER>\\Windows",API_BASE_URL:"/api/semantic/",CHAT_API_BASE_URL:"/api/chat/",AUTH_API_BASE_URL:"/api/auth/",SHOW_TAG:!1,tmeAvatarUrl:""})===null||qe===void 0?void 0:qe.ANTD_VERSION)||Cn.Z},mo=function(e){var n,t,r,i,o,c,d,p,m,_,v,S,N,Z,P,L,k,F,D,O,y,R,K,w,E,te,ie,f,se,b,Q,ae;return(n=po())!==null&&n!==void 0&&n.startsWith("5")?{}:(0,s.Z)((0,s.Z)((0,s.Z)({},e.componentCls,(0,s.Z)((0,s.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(y={color:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorTextMenu},(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)(y,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,s.Z)((0,s.Z)({color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,s.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,s.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,s.Z)({color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,s.Z)((0,s.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:(d=e.layout)===null||d===void 0||(d=d.sider)===null||d===void 0?void 0:d.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,s.Z)({color:(p=e.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat((m=e.layout)===null||m===void 0||(m=m.header)===null||m===void 0?void 0:m.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(_=e.layout)===null||_===void 0||(_=_.sider)===null||_===void 0?void 0:_.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:(v=e.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorTextMenuSelected}),(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)(y,"".concat(e.antCls,"-menu-submenu-selected"),{color:(S=e.layout)===null||S===void 0||(S=S.sider)===null||S===void 0?void 0:S.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:(N=e.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,s.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:(Z=e.layout)===null||Z===void 0||(Z=Z.sider)===null||Z===void 0?void 0:Z.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:(P=e.layout)===null||P===void 0||(P=P.sider)===null||P===void 0?void 0:P.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:(L=e.layout)===null||L===void 0||(L=L.header)===null||L===void 0?void 0:L.colorTextMenuActive,backgroundColor:"".concat((k=e.layout)===null||k===void 0||(k=k.header)===null||k===void 0?void 0:k.colorBgMenuItemHover," !important")}),"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,s.Z)({backgroundColor:(F=e.layout)===null||F===void 0||(F=F.header)===null||F===void 0?void 0:F.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat((D=e.layout)===null||D===void 0||(D=D.header)===null||D===void 0?void 0:D.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat((O=e.layout)===null||O===void 0||(O=O.header)===null||O===void 0?void 0:O.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,s.Z)((0,s.Z)({},"&".concat(e.antCls,"-menu"),(0,s.Z)({color:(R=e.layout)===null||R===void 0||(R=R.header)===null||R===void 0?void 0:R.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,s.Z)((0,s.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,s.Z)({color:(K=e.layout)===null||K===void 0||(K=K.header)===null||K===void 0?void 0:K.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:(w=e.layout)===null||w===void 0||(w=w.header)===null||w===void 0?void 0:w.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(E=e.layout)===null||E===void 0||(E=E.header)===null||E===void 0?void 0:E.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:(te=e.layout)===null||te===void 0||(te=te.header)===null||te===void 0?void 0:te.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:(ie=e.layout)===null||ie===void 0||(ie=ie.header)===null||ie===void 0?void 0:ie.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,s.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:(se=e.layout)===null||se===void 0||(se=se.sider)===null||se===void 0?void 0:se.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,s.Z)((0,s.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:(b=e.layout)===null||b===void 0||(b=b.sider)===null||b===void 0?void 0:b.colorTextMenuSelected}),"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,s.Z)({color:(Q=e.layout)===null||Q===void 0||(Q=Q.sider)===null||Q===void 0?void 0:Q.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(ae=e.layout)===null||ae===void 0||(ae=ae.sider)===null||ae===void 0?void 0:ae.colorTextMenuActive}))))},vo=function(e){var n,t,r,i;return(0,s.Z)((0,s.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:((n=e.layout)===null||n===void 0||(n=n.pageContainer)===null||n===void 0?void 0:n.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:(t=e.layout)===null||t===void 0||(t=t.pageContainer)===null||t===void 0?void 0:t.paddingBlockPageContainerContent,paddingInline:(r=e.layout)===null||r===void 0||(r=r.pageContainer)===null||r===void 0?void 0:r.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:(i=e.layout)===null||i===void 0?void 0:i.bgLayout}))};function go(a){return(0,Me.Xj)("ProLayout",function(e){var n=(0,u.Z)((0,u.Z)({},e),{},{componentCls:".".concat(a)});return[vo(n),mo(n)]})}function fo(a){if(!a||a==="/")return["/"];var e=a.split("/").filter(function(n){return n});return e.map(function(n,t){return"/".concat(e.slice(0,t+1).join("/"))})}var en=x(73656),ho=function(){var e;return typeof en=="undefined"?Cn.Z:((e=en)===null||en===void 0||(en={AISE_MINIO_API_PORT:"19000",AISE_MYSQL_PORT:"13306",AISE_NACOS_PORT:"18848",AISE_REDIS_PORT:"16379",ALLUSERSPROFILE:"C:\\ProgramData",APPDATA:"C:\\Users\\<USER>\\AppData\\Roaming",APP_TARGET:"opensource","asl.log":"Destination=file",CommonProgramFiles:"C:\\Program Files\\Common Files","CommonProgramFiles(x86)":"C:\\Program Files (x86)\\Common Files",CommonProgramW6432:"C:\\Program Files\\Common Files",COMPUTERNAME:"ISS",ComSpec:"C:\\Windows\\system32\\cmd.exe",DriverData:"C:\\Windows\\System32\\Drivers\\DriverData",FPS_BROWSER_APP_PROFILE_STRING:"Internet Explorer",FPS_BROWSER_USER_PROFILE_STRING:"Default",HOME:"C:\\Users\\<USER>\\Users\\issuser",INIT_CWD:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe",LOCALAPPDATA:"C:\\Users\\<USER>\\AppData\\Local",LOGONSERVER:"\\\\ISS",major_version:"18",NODE:"E:\\node\\node.exe",NODE_ENV:"production",NODE_OPTIONS:"--openssl-legacy-provider",NODE_PATH:"D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\bin\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\bin\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\src\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules\\cross-env\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\cross-env@7.0.3\\node_modules;D:\\\u6924\u572D\u6D30\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\node_modules",node_version:"v20.18.1",npm_command:"run-script",npm_config_cache:"E:\\nvm\\nvm\\node_cache",npm_config_frozen_lockfile:"",npm_config_node_gyp:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node_modules\\node-gyp\\bin\\node-gyp.js",npm_config_prefix:"E:\\nvm\\nvm\\node_global",npm_config_registry:"https://registry.npmmirror.com",npm_config_user_agent:"pnpm/9.12.3 npm/? node/v20.18.1 win32 x64",npm_config__tiptap_pro_registry:"https://registry.tiptap.dev/",npm_execpath:"C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\bin\\pnpm.cjs",npm_lifecycle_event:"build:os-local",npm_lifecycle_script:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build",npm_node_execpath:"E:\\node\\node.exe",npm_package_browserslist_0:"> 1%",npm_package_browserslist_1:"last 2 versions",npm_package_browserslist_2:"not ie <= 10",npm_package_dependencies_ace_builds:"^1.4.12",npm_package_dependencies_ahooks:"^3.7.7",npm_package_dependencies_antd:"^5.17.4",npm_package_dependencies_classnames:"^2.2.6",npm_package_dependencies_compression_webpack_plugin:"^11.0.0",npm_package_dependencies_copy_to_clipboard:"^3.3.1",npm_package_dependencies_cross_env:"^7.0.3",npm_package_dependencies_crypto_js:"^4.2.0",npm_package_dependencies_dayjs:"^1.11.10",npm_package_dependencies_echarts:"^5.0.2",npm_package_dependencies_echarts_for_react:"^3.0.1",npm_package_dependencies_eslint_config_tencent:"^1.0.4",npm_package_dependencies_hchatdata_chat_sdk:"workspace:*",npm_package_dependencies_jsencrypt:"^3.0.1",npm_package_dependencies_lodash:"^4.17.11",npm_package_dependencies_moment:"^2.29.1",npm_package_dependencies_nprogress:"^0.2.0",npm_package_dependencies_numeral:"^2.0.6",npm_package_dependencies_omit_js:"^2.0.2",npm_package_dependencies_path_to_regexp:"^2.4.0",npm_package_dependencies_qs:"^6.9.0",npm_package_dependencies_query_string:"^9.0.0",npm_package_dependencies_react:"^18.3.1",npm_package_dependencies_react_ace:"^9.4.1",npm_package_dependencies_react_dom:"^18.3.1",npm_package_dependencies_react_spinners:"^0.13.8",npm_package_dependencies_react_split_pane:"^2.0.3",npm_package_dependencies_react_syntax_highlighter:"^15.4.3",npm_package_dependencies_sql_formatter:"^15.6.1",npm_package_dependencies_supersonic_insights_flow_components:"^1.4.9",npm_package_dependencies_umi_request:"1.4.0",npm_package_dependencies__antv_dom_util:"^2.0.4",npm_package_dependencies__antv_g6:"^4.8.23",npm_package_dependencies__antv_g6_core:"^0.8.23",npm_package_dependencies__antv_layout:"^0.3.20",npm_package_dependencies__antv_x6:"1.30.1",npm_package_dependencies__ant_design_icons:"^5.2.6",npm_package_dependencies__ant_design_pro_components:"2.7.0",npm_package_dependencies__babel_runtime:"^7.22.5",npm_package_dependencies__types_numeral:"^2.0.2",npm_package_dependencies__types_react_draft_wysiwyg:"^1.13.2",npm_package_dependencies__types_react_syntax_highlighter:"^13.5.0",npm_package_dependencies__umijs_route_utils:"2.2.2",npm_package_description:"data chat",npm_package_devDependencies_carlo:"^0.9.46",npm_package_devDependencies_compression:"^1.8.1",npm_package_devDependencies_cross_port_killer:"^1.1.1",npm_package_devDependencies_detect_installer:"^1.0.1",npm_package_devDependencies_eslint:"^7.1.0",npm_package_devDependencies_eslint_plugin_chalk:"^1.0.0",npm_package_devDependencies_eslint_plugin_import:"^2.27.5",npm_package_devDependencies_express:"^4.21.2",npm_package_devDependencies_gh_pages:"^3.0.0",npm_package_devDependencies_http_proxy_middleware:"^2.0.6",npm_package_devDependencies_jsdom_global:"^3.0.2",npm_package_devDependencies_lint_staged:"^10.0.0",npm_package_devDependencies_prettier:"^2.3.1",npm_package_devDependencies_pro_download:"1.0.1",npm_package_devDependencies_puppeteer_core:"^5.0.0",npm_package_devDependencies_stylelint:"^13.0.0",npm_package_devDependencies_typescript:"^4.0.3",npm_package_devDependencies_umi_presets_pro:"2.0.2",npm_package_devDependencies__ant_design_pro_cli:"^2.0.2",npm_package_devDependencies__types_classnames:"^2.2.7",npm_package_devDependencies__types_crypto_js:"^4.0.1",npm_package_devDependencies__types_draftjs_to_html:"^0.8.0",npm_package_devDependencies__types_echarts:"^4.9.4",npm_package_devDependencies__types_express:"^4.17.0",npm_package_devDependencies__types_history:"^4.7.2",npm_package_devDependencies__types_jest:"^26.0.0",npm_package_devDependencies__types_lodash:"^4.14.144",npm_package_devDependencies__types_pinyin:"^2.8.3",npm_package_devDependencies__types_qs:"^6.5.3",npm_package_devDependencies__types_react:"18.3.1",npm_package_devDependencies__types_react_dom:"^18.3.0",npm_package_devDependencies__types_react_helmet:"^6.1.0",npm_package_devDependencies__umijs_fabric:"^4.0.1",npm_package_devDependencies__umijs_max:"^4.2.5",npm_package_devDependencies__umijs_plugin_model:"^2.6.2",npm_package_engines_node:">=16.0.0",npm_package_lint_staged______less:"stylelint --syntax less",npm_package_lint_staged_______js_jsx_tsx_ts_less_md_json__0:"prettier --write",npm_package_lint_staged_______js_jsx_ts_tsx_:"npm run lint-staged:js",npm_package_name:"hchatdata-fe",npm_package_packageManager:"pnpm@9.12.3+sha512.cce0f9de9c5a7c95bef944169cc5dfe8741abfb145078c0d508b868056848a87c81e626246cb60967cbd7fd29a6c062ef73ff840d96b3c86c40ac92cf4a813ee",npm_package_private:"true",npm_package_scripts_analyze:"cross-env ANALYZE=1 max build",npm_package_scripts_build:"npm run build:os",npm_package_scripts_build_inner:"cross-env REACT_APP_ENV=prod APP_TARGET=inner max build",npm_package_scripts_build_os:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource max build",npm_package_scripts_build_os_local:"cross-env REACT_APP_ENV=prod APP_TARGET=opensource RUN_TYPE=local max build",npm_package_scripts_build_test:"cross-env REACT_APP_ENV=test max build",npm_package_scripts_deploy:"npm run site && npm run gh-pages",npm_package_scripts_dev:"npm run start:osdev",npm_package_scripts_dev_inner:"npm run start:dev",npm_package_scripts_dev_os:"npm run start:osdev",npm_package_scripts_gh_pages:"gh-pages -d dist",npm_package_scripts_i18n_remove:"pro i18n-remove --locale=zh-CN --write",npm_package_scripts_lint:"max g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier",npm_package_scripts_lint_fix:"eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style",npm_package_scripts_lint_js:"eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src",npm_package_scripts_lint_prettier:'prettier --check "src/**/*" --end-of-line auto',npm_package_scripts_lint_staged:"lint-staged",npm_package_scripts_lint_staged_js:"eslint --ext .js,.jsx,.ts,.tsx ",npm_package_scripts_lint_style:'stylelint --fix "src/**/*.less" --syntax less',npm_package_scripts_no_build_inner:"cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=prod APP_TARGET=inner max build",npm_package_scripts_no_dev_inner:"NODE_OPTIONS=--openssl-legacy-provider npm run start:dev",npm_package_scripts_no_dev_os:"NODE_OPTIONS=--openssl-legacy-provider npm run start:osdev",npm_package_scripts_postinstall:"max setup",npm_package_scripts_precommit:"lint-staged",npm_package_scripts_pretest:"node ./tests/beforeTest",npm_package_scripts_prettier:'prettier -c --write "src/**/*"',npm_package_scripts_start:"npm run start:osdev",npm_package_scripts_start_dev:"cross-env PORT=8002 REACT_APP_ENV=dev MOCK=none APP_TARGET=inner max dev",npm_package_scripts_start_no_mock:"cross-env MOCK=none max dev",npm_package_scripts_start_no_ui:"cross-env UMI_UI=none max dev",npm_package_scripts_start_osdev:"cross-env REACT_APP_ENV=dev PORT=9000 MOCK=none APP_TARGET=opensource max dev",npm_package_scripts_start_pre:"cross-env REACT_APP_ENV=pre max dev",npm_package_scripts_start_test:"cross-env REACT_APP_ENV=test MOCK=none max dev",npm_package_scripts_test:"max test",npm_package_scripts_test_all:"node ./tests/run-tests.js",npm_package_scripts_test_component:"max test ./src/components",npm_package_scripts_tsc:"tsc --noEmit",npm_package_version:"0.1.0",NUMBER_OF_PROCESSORS:"8",NVM_HOME:"E:\\nvm\\nvm",NVM_NODE_GLOBAL:"E:\\nvm\\nvm\\node_global",NVM_SYMLINK:"E:\\node",OneDrive:"C:\\Users\\<USER>\\OneDrive",OS:"Windows_NT",Path:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\pnpm\\dist\\node-gyp-bin;D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\TortoiseGit\\bin;E:\\nvm\\nvm;E:\\node;C:\\Program Files\\CorpLink\\current\\module\\mdm\\x64\\policy\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\python311\\Scripts\\;C:\\python311\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;E:\\nvm\\nvm;E:\\node;D:\\\u8F6F\u901A\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;",PATHEXT:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC",PNPM_SCRIPT_SRC_DIR:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\packages\\hchatdata-fe",PROCESSOR_ARCHITECTURE:"AMD64",PROCESSOR_IDENTIFIER:"Intel64 Family 6 Model 142 Stepping 12, GenuineIntel",PROCESSOR_LEVEL:"6",PROCESSOR_REVISION:"8e0c",ProgramData:"C:\\ProgramData",ProgramFiles:"C:\\Program Files","ProgramFiles(x86)":"C:\\Program Files (x86)",ProgramW6432:"C:\\Program Files",PROMPT:"$P$G",PSModulePath:"C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules",PUBLIC:"C:\\Users\\<USER>\\Windows",TEMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",TMP:"C:\\Users\\<USER>\\AppData\\Local\\Temp",UMI_DIR:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\umi@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react@18.3.1_eslint@8.35.0_jest@27._d5pvzqomnp5az5zle5uryp6pkq\\node_modules\\umi",UMI_PRESETS:"D:\\\u9879\u76EE\\xx\\HChatData\\webapp\\node_modules\\.pnpm\\@umijs+max@4.4.11_@babel+core@7.27.1_@types+node@22.15.21_@types+react-dom@18.3.7_@types+reac_mwyitsm7pze2hn4xoqhktvqqlu\\node_modules\\@umijs\\max\\dist\\preset.js",USERDOMAIN:"ISS",USERDOMAIN_ROAMINGPROFILE:"ISS",USERNAME:"issuser",USERPROFILE:"C:\\Users\\<USER>\\Windows",API_BASE_URL:"/api/semantic/",CHAT_API_BASE_URL:"/api/chat/",AUTH_API_BASE_URL:"/api/auth/",SHOW_TAG:!1,tmeAvatarUrl:""})===null||en===void 0?void 0:en.ANTD_VERSION)||Cn.Z},_o=function(e,n,t){var r=e,i=r.breadcrumbName,o=r.title,c=r.path,d=t.findIndex(function(p){return p.linkPath===e.path})===t.length-1;return d?(0,l.jsx)("span",{children:o||i}):(0,l.jsx)("span",{onClick:c?function(){return location.href=c}:void 0,children:o||i})},yo=function(e,n){var t=n.formatMessage,r=n.menu;return e.locale&&t&&(r==null?void 0:r.locale)!==!1?t({id:e.locale,defaultMessage:e.name}):e.name},xo=function(e,n){var t=e.get(n);if(!t){var r=Array.from(e.keys())||[],i=r.find(function(o){return Ln()(o.replace("?","")).test(n)});i&&(t=e.get(i))}return t||{path:""}},Co=function(e){var n=e.location,t=e.breadcrumbMap;return{location:n,breadcrumbMap:t}},bo=function(e,n,t){var r=fo(e==null?void 0:e.pathname),i=r.map(function(o){var c=xo(n,o),d=yo(c,t),p=c.hideInBreadcrumb;return d&&!p?{linkPath:o,breadcrumbName:d,title:d,component:c.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(o){return o&&o.linkPath});return i},So=function(e){var n=Co(e),t=n.location,r=n.breadcrumbMap;return t&&t.pathname&&r?bo(t,r,e):[]},Po=function(e,n){var t=e.breadcrumbRender,r=e.itemRender,i=n.breadcrumbProps||{},o=i.minLength,c=o===void 0?2:o,d=So(e),p=function(v){for(var S=r||_o,N=arguments.length,Z=new Array(N>1?N-1:0),P=1;P<N;P++)Z[P-1]=arguments[P];return S==null?void 0:S.apply(void 0,[(0,u.Z)((0,u.Z)({},v),{},{path:v.linkPath||v.path})].concat(Z))},m=d;return t&&(m=t(m||[])||void 0),(m&&m.length<c||t===!1)&&(m=void 0),(0,M.n)(ho(),"5.3.0")>-1?{items:m,itemRender:p}:{routes:m,itemRender:p}};function Mo(a){return(0,dn.Z)(a).reduce(function(e,n){var t=(0,Y.Z)(n,2),r=t[0],i=t[1];return e[r]=i,e},{})}var Io=function a(e,n,t,r){var i=va(e,(n==null?void 0:n.locale)||!1,t,!0),o=i.menuData,c=i.breadcrumb;return r?a(r(o),n,t,void 0):{breadcrumb:Mo(c),breadcrumbMap:c,menuData:o}},Ro=x(68874),To=x(8452),Eo=function(e){var n=(0,g.useState)({}),t=(0,Y.Z)(n,2),r=t[0],i=t[1];return(0,g.useEffect)(function(){i((0,To.Y)({layout:(0,Ro.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),r},Zo=["id","defaultMessage"],Do=["fixSiderbar","navTheme","layout"],Pt=0,wo=function(e,n){var t;return e.headerRender===!1||e.pure?null:(0,l.jsx)($r,(0,u.Z)((0,u.Z)({matchMenuKeys:n},e),{},{stylish:(t=e.stylish)===null||t===void 0?void 0:t.header}))},Ao=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,u.Z)({},e),(0,l.jsx)(Va,{})):null},jo=function(e,n){var t,r=e.layout,i=e.isMobile,o=e.selectedKeys,c=e.openKeys,d=e.splitMenus,p=e.suppressSiderWhenMenuEmpty,m=e.menuRender;if(e.menuRender===!1||e.pure)return null;var _=e.menuData;if(d&&(c!==!1||r==="mix")&&!i){var v=o||n,S=(0,Y.Z)(v,1),N=S[0];if(N){var Z;_=((Z=e.menuData)===null||Z===void 0||(Z=Z.find(function(F){return F.key===N}))===null||Z===void 0?void 0:Z.children)||[]}else _=[]}var P=xn(_||[]);if(P&&(P==null?void 0:P.length)<1&&(d||p))return null;if(r==="top"&&!i){var L;return(0,l.jsx)(Ct,(0,u.Z)((0,u.Z)({matchMenuKeys:n},e),{},{hide:!0,stylish:(L=e.stylish)===null||L===void 0?void 0:L.sider}))}var k=(0,l.jsx)(Ct,(0,u.Z)((0,u.Z)({matchMenuKeys:n},e),{},{menuData:P,stylish:(t=e.stylish)===null||t===void 0?void 0:t.sider}));return m?m(e,k):k},No=function(e,n){var t=n.pageTitleRender,r=Hn(e);if(t===!1)return{title:n.title||"",id:"",pageName:""};if(t){var i=t(e,r.title,r);if(typeof i=="string")return Hn((0,u.Z)((0,u.Z)({},r),{},{title:i}));(0,wa.ZP)(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return r},ko=function(e,n,t){return e?n?64:t:0},Oo=function(e){var n,t,r,i,o,c,d,p,m,_,v,S,N,Z,P=e||{},L=P.children,k=P.onCollapse,F=P.location,D=F===void 0?{pathname:"/"}:F,O=P.contentStyle,y=P.route,R=P.defaultCollapsed,K=P.style,w=P.siderWidth,E=P.menu,te=P.siderMenuType,ie=P.isChildrenLayout,f=P.menuDataRender,se=P.actionRef,b=P.bgLayoutImgList,Q=P.formatMessage,ae=P.loading,pe=(0,g.useMemo)(function(){return w||(e.layout==="mix"?215:256)},[e.layout,w]),oe=(0,g.useContext)(Ne.ZP.ConfigContext),ye=(n=e.prefixCls)!==null&&n!==void 0?n:oe.getPrefixCls("pro"),ve=(0,me.Z)(!1,{value:E==null?void 0:E.loading,onChange:E==null?void 0:E.onLoadingChange}),ge=(0,Y.Z)(ve,2),Ze=ge[0],Ie=ge[1],Te=(0,g.useState)(function(){return Pt+=1,"pro-layout-".concat(Pt)}),Le=(0,Y.Z)(Te,1),$e=Le[0],ze=(0,g.useCallback)(function(Ce){var Ge=Ce.id,In=Ce.defaultMessage,fn=(0,fe.Z)(Ce,Zo);if(Q)return Q((0,u.Z)({id:Ge,defaultMessage:In},fn));var hn=uo();return hn[Ge]?hn[Ge]:In},[Q]),Ue=(0,Aa.ZP)([$e,E==null?void 0:E.params],function(){var Ce=(0,le.Z)((0,he.Z)().mark(function Ge(In){var fn,hn,Lt,Ht;return(0,he.Z)().wrap(function(rn){for(;;)switch(rn.prev=rn.next){case 0:return hn=(0,Y.Z)(In,2),Lt=hn[1],Ie(!0),rn.next=4,E==null||(fn=E.request)===null||fn===void 0?void 0:fn.call(E,Lt||{},(y==null?void 0:y.children)||(y==null?void 0:y.routes)||[]);case 4:return Ht=rn.sent,Ie(!1),rn.abrupt("return",Ht);case 7:case"end":return rn.stop()}},Ge)}));return function(Ge){return Ce.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),un=Ue.data,bn=Ue.mutate,xe=Ue.isLoading;(0,g.useEffect)(function(){Ie(xe)},[xe]);var ke=(0,ja.kY)(),De=ke.cache;(0,g.useEffect)(function(){return function(){De instanceof Map&&De.delete($e)}},[]);var Sn=(0,g.useMemo)(function(){return Io(un||(y==null?void 0:y.children)||(y==null?void 0:y.routes)||[],E,ze,f)},[ze,E,f,un,y==null?void 0:y.children,y==null?void 0:y.routes]),Fn=Sn||{},Lo=Fn.breadcrumb,Mt=Fn.breadcrumbMap,It=Fn.menuData,pn=It===void 0?[]:It;se&&E!==null&&E!==void 0&&E.request&&(se.current={reload:function(){bn()}});var mn=(0,g.useMemo)(function(){return xa(D.pathname||"/",pn||[],!0)},[D.pathname,pn]),Wn=(0,g.useMemo)(function(){return Array.from(new Set(mn.map(function(Ce){return Ce.key||Ce.path||""})))},[mn]),Rt=mn[mn.length-1]||{},Tt=Eo(Rt),Pn=(0,u.Z)((0,u.Z)({},e),Tt),Ho=Pn.fixSiderbar,ai=Pn.navTheme,vn=Pn.layout,Fo=(0,fe.Z)(Pn,Do),nn=z(),tn=(0,g.useMemo)(function(){return(nn==="sm"||nn==="xs")&&!e.disableMobile},[nn,e.disableMobile]),Wo=vn!=="top"&&!tn,$o=(0,me.Z)(function(){return R!==void 0?R:!!(tn||nn==="md")},{value:e.collapsed,onChange:k}),Et=(0,Y.Z)($o,2),gn=Et[0],Zt=Et[1],an=(0,nt.Z)((0,u.Z)((0,u.Z)((0,u.Z)({prefixCls:ye},e),{},{siderWidth:pe},Tt),{},{formatMessage:ze,breadcrumb:Lo,menu:(0,u.Z)((0,u.Z)({},E),{},{type:te||(E==null?void 0:E.type),loading:Ze}),layout:vn}),["className","style","breadcrumbRender"]),$n=No((0,u.Z)((0,u.Z)({pathname:D.pathname},an),{},{breadcrumbMap:Mt}),e),zo=Po((0,u.Z)((0,u.Z)({},an),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:Mt}),e),Mn=jo((0,u.Z)((0,u.Z)({},an),{},{menuData:pn,onCollapse:Zt,isMobile:tn,collapsed:gn}),Wn),zn=wo((0,u.Z)((0,u.Z)({},an),{},{children:null,hasSiderMenu:!!Mn,menuData:pn,isMobile:tn,collapsed:gn,onCollapse:Zt}),Wn),Dt=Ao((0,u.Z)({isMobile:tn,collapsed:gn},an)),Uo=(0,g.useContext)(bt),Go=Uo.isChildrenLayout,Un=ie!==void 0?ie:Go,He="".concat(ye,"-layout"),wt=go(He),Vo=wt.wrapSSR,Gn=wt.hashId,Ko=ee()(e.className,Gn,"ant-design-pro",He,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"screen-".concat(nn),nn),"".concat(He,"-top-menu"),vn==="top"),"".concat(He,"-is-children"),Un),"".concat(He,"-fix-siderbar"),Ho),"".concat(He,"-").concat(vn),vn)),Xo=ko(!!Wo,gn,pe),At={position:"relative"};(Un||O&&O.minHeight)&&(At.minHeight=0),(0,g.useEffect)(function(){var Ce;(Ce=e.onPageChange)===null||Ce===void 0||Ce.call(e,e.location)},[D.pathname,(t=D.pathname)===null||t===void 0?void 0:t.search]);var Yo=(0,g.useState)(!1),jt=(0,Y.Z)(Yo,2),Nt=jt[0],Qo=jt[1],Jo=(0,g.useState)(0),kt=(0,Y.Z)(Jo,2),Ot=kt[0],qo=kt[1];h($n,e.title||!1);var ei=(0,g.useContext)(ce.L_),X=ei.token,Bt=(0,g.useMemo)(function(){return b&&b.length>0?b==null?void 0:b.map(function(Ce,Ge){return(0,l.jsx)("img",{src:Ce.src,style:(0,u.Z)({position:"absolute"},Ce)},Ge)}):null},[b]);return Vo((0,l.jsx)(bt.Provider,{value:(0,u.Z)((0,u.Z)({},an),{},{breadcrumb:zo,menuData:pn,isMobile:tn,collapsed:gn,hasPageContainer:Ot,setHasPageContainer:qo,isChildrenLayout:!0,title:$n.pageName,hasSiderMenu:!!Mn,hasHeader:!!zn,siderWidth:Xo,hasFooter:!!Dt,hasFooterToolbar:Nt,setHasFooterToolbar:Qo,pageTitleInfo:$n,matchMenus:mn,matchMenuKeys:Wn,currentMenu:Rt}),children:e.pure?(0,l.jsx)(l.Fragment,{children:L}):(0,l.jsxs)("div",{className:Ko,children:[Bt||(r=X.layout)!==null&&r!==void 0&&r.bgLayout?(0,l.jsx)("div",{className:ee()("".concat(He,"-bg-list"),Gn),children:Bt}):null,(0,l.jsxs)(Xe,{style:(0,u.Z)({minHeight:"100%",flexDirection:Mn?"row":void 0},K),children:[(0,l.jsx)(Ne.ZP,{theme:{hashed:(0,ce.nu)(),token:{controlHeightLG:((i=X.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.menuHeight)||(X==null?void 0:X.controlHeightLG)},components:{Menu:H({colorItemBg:((o=X.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorMenuBackground)||"transparent",colorSubItemBg:((c=X.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuBackground)||"transparent",radiusItem:X.borderRadius,colorItemBgSelected:((d=X.layout)===null||d===void 0||(d=d.sider)===null||d===void 0?void 0:d.colorBgMenuItemSelected)||(X==null?void 0:X.colorBgTextHover),colorItemBgHover:((p=X.layout)===null||p===void 0||(p=p.sider)===null||p===void 0?void 0:p.colorBgMenuItemHover)||(X==null?void 0:X.colorBgTextHover),colorItemBgActive:((m=X.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorBgMenuItemActive)||(X==null?void 0:X.colorBgTextActive),colorItemBgSelectedHorizontal:((_=X.layout)===null||_===void 0||(_=_.sider)===null||_===void 0?void 0:_.colorBgMenuItemSelected)||(X==null?void 0:X.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((v=X.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorTextMenu)||(X==null?void 0:X.colorTextSecondary),colorItemTextHover:((S=X.layout)===null||S===void 0||(S=S.sider)===null||S===void 0?void 0:S.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:((N=X.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:X==null?void 0:X.colorBgElevated,subMenuItemBg:X==null?void 0:X.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:X==null?void 0:X.colorBgElevated})}},children:Mn}),(0,l.jsxs)("div",{style:At,className:"".concat(He,"-container ").concat(Gn).trim(),children:[zn,(0,l.jsx)(ka,(0,u.Z)((0,u.Z)({hasPageContainer:Ot,isChildrenLayout:Un},Fo),{},{hasHeader:!!zn,prefixCls:He,style:O,children:ae?(0,l.jsx)(Gr,{}):L})),Dt,Nt&&(0,l.jsx)("div",{className:"".concat(He,"-has-footer"),style:{height:64,marginBlockStart:(Z=X.layout)===null||Z===void 0||(Z=Z.pageContainer)===null||Z===void 0?void 0:Z.paddingBlockPageContainerContent}})]})]})]})}))},Bo=function(e){var n=e.colorPrimary,t=e.navTheme!==void 0?{dark:e.navTheme==="realDark"}:{};return(0,l.jsx)(Ne.ZP,{theme:n?{token:{colorPrimary:n}}:void 0,children:(0,l.jsx)(ce._Y,(0,u.Z)((0,u.Z)({autoClearCache:!0},t),{},{token:e.token,prefixCls:e.prefixCls,children:(0,l.jsx)(Oo,(0,u.Z)((0,u.Z)({logo:(0,l.jsx)(Oa,{})},ct),{},{location:(0,q.j)()?window.location:void 0},e))}))})}},41029:function(Fe,we){var x;function s(h){"@babel/helpers - typeof";return s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(M){return typeof M}:function(M){return M&&typeof Symbol=="function"&&M.constructor===Symbol&&M!==Symbol.prototype?"symbol":typeof M},s(h)}x={value:!0},we.Bo=x=x=x=x=x=x=void 0;function he(h){for(var M=[],C=0;C<h.length;){var H=h[C];if(H==="*"||H==="+"||H==="?"){M.push({type:"MODIFIER",index:C,value:h[C++]});continue}if(H==="\\"){M.push({type:"ESCAPED_CHAR",index:C++,value:h[C++]});continue}if(H==="{"){M.push({type:"OPEN",index:C,value:h[C++]});continue}if(H==="}"){M.push({type:"CLOSE",index:C,value:h[C++]});continue}if(H===":"){for(var W="",T=C+1;T<h.length;){var I=h.charCodeAt(T);if(I>=48&&I<=57||I>=65&&I<=90||I>=97&&I<=122||I===95){W+=h[T++];continue}break}if(!W)throw new TypeError("Missing parameter name at "+C);M.push({type:"NAME",index:C,value:W}),C=T;continue}if(H==="("){var A=1,G="",T=C+1;if(h[T]==="?")throw new TypeError('Pattern cannot start with "?" at '+T);for(;T<h.length;){if(h[T]==="\\"){G+=h[T++]+h[T++];continue}if(h[T]===")"){if(A--,A===0){T++;break}}else if(h[T]==="("&&(A++,h[T+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+T);G+=h[T++]}if(A)throw new TypeError("Unbalanced pattern at "+C);if(!G)throw new TypeError("Missing pattern at "+C);M.push({type:"PATTERN",index:C,value:G}),C=T;continue}M.push({type:"CHAR",index:C,value:h[C++]})}return M.push({type:"END",index:C,value:""}),M}function le(h,M){M===void 0&&(M={});for(var C=he(h),H=M.prefixes,W=H===void 0?"./":H,T="[^"+me(M.delimiter||"/#?")+"]+?",I=[],A=0,G=0,V="",$=function(Be){if(G<C.length&&C[G].type===Be)return C[G++].value},U=function(Be){var je=$(Be);if(je!==void 0)return je;var Ye=C[G],Ve=Ye.type,Qe=Ye.index;throw new TypeError("Unexpected "+Ve+" at "+Qe+", expected "+Be)},re=function(){for(var Be="",je;je=$("CHAR")||$("ESCAPED_CHAR");)Be+=je;return Be};G<C.length;){var J=$("CHAR"),B=$("NAME"),ne=$("PATTERN");if(B||ne){var j=J||"";W.indexOf(j)===-1&&(V+=j,j=""),V&&(I.push(V),V=""),I.push({name:B||A++,prefix:j,suffix:"",pattern:ne||T,modifier:$("MODIFIER")||""});continue}var de=J||$("ESCAPED_CHAR");if(de){V+=de;continue}V&&(I.push(V),V="");var Pe=$("OPEN");if(Pe){var j=re(),Ee=$("NAME")||"",_e=$("PATTERN")||"",Oe=re();U("CLOSE"),I.push({name:Ee||(_e?A++:""),pattern:Ee&&!_e?T:_e,prefix:j,suffix:Oe,modifier:$("MODIFIER")||""});continue}U("END")}return I}x=le;function fe(h,M){return Y(le(h,M),M)}x=fe;function Y(h,M){M===void 0&&(M={});var C=g(M),H=M.encode,W=H===void 0?function(G){return G}:H,T=M.validate,I=T===void 0?!0:T,A=h.map(function(G){if(s(G)==="object")return new RegExp("^(?:"+G.pattern+")$",C)});return function(G){for(var V="",$=0;$<h.length;$++){var U=h[$];if(typeof U=="string"){V+=U;continue}var re=G?G[U.name]:void 0,J=U.modifier==="?"||U.modifier==="*",B=U.modifier==="*"||U.modifier==="+";if(Array.isArray(re)){if(!B)throw new TypeError('Expected "'+U.name+'" to not repeat, but got an array');if(re.length===0){if(J)continue;throw new TypeError('Expected "'+U.name+'" to not be empty')}for(var ne=0;ne<re.length;ne++){var j=W(re[ne],U);if(I&&!A[$].test(j))throw new TypeError('Expected all "'+U.name+'" to match "'+U.pattern+'", but got "'+j+'"');V+=U.prefix+j+U.suffix}continue}if(typeof re=="string"||typeof re=="number"){var j=W(String(re),U);if(I&&!A[$].test(j))throw new TypeError('Expected "'+U.name+'" to match "'+U.pattern+'", but got "'+j+'"');V+=U.prefix+j+U.suffix;continue}if(!J){var de=B?"an array":"a string";throw new TypeError('Expected "'+U.name+'" to be '+de)}}return V}}x=Y;function u(h,M){var C=[],H=q(h,C,M);return ce(H,C,M)}x=u;function ce(h,M,C){C===void 0&&(C={});var H=C.decode,W=H===void 0?function(T){return T}:H;return function(T){var I=h.exec(T);if(!I)return!1;for(var A=I[0],G=I.index,V=Object.create(null),$=function(J){if(I[J]===void 0)return"continue";var B=M[J-1];B.modifier==="*"||B.modifier==="+"?V[B.name]=I[J].split(B.prefix+B.suffix).map(function(ne){return W(ne,B)}):V[B.name]=W(I[J],B)},U=1;U<I.length;U++)$(U);return{path:A,index:G,params:V}}}x=ce;function me(h){return h.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function g(h){return h&&h.sensitive?"":"i"}function be(h,M){if(!M)return h;var C=h.source.match(/\((?!\?)/g);if(C)for(var H=0;H<C.length;H++)M.push({name:H,prefix:"",suffix:"",modifier:"",pattern:""});return h}function Se(h,M,C){var H=h.map(function(W){return q(W,M,C).source});return new RegExp("(?:"+H.join("|")+")",g(C))}function We(h,M,C){return z(le(h,C),M,C)}function z(h,M,C){C===void 0&&(C={});for(var H=C.strict,W=H===void 0?!1:H,T=C.start,I=T===void 0?!0:T,A=C.end,G=A===void 0?!0:A,V=C.encode,$=V===void 0?function(Ae){return Ae}:V,U="["+me(C.endsWith||"")+"]|$",re="["+me(C.delimiter||"/#?")+"]",J=I?"^":"",B=0,ne=h;B<ne.length;B++){var j=ne[B];if(typeof j=="string")J+=me($(j));else{var de=me($(j.prefix)),Pe=me($(j.suffix));if(j.pattern)if(M&&M.push(j),de||Pe)if(j.modifier==="+"||j.modifier==="*"){var Ee=j.modifier==="*"?"?":"";J+="(?:"+de+"((?:"+j.pattern+")(?:"+Pe+de+"(?:"+j.pattern+"))*)"+Pe+")"+Ee}else J+="(?:"+de+"("+j.pattern+")"+Pe+")"+j.modifier;else J+="("+j.pattern+")"+j.modifier;else J+="(?:"+de+Pe+")"+j.modifier}}if(G)W||(J+=re+"?"),J+=C.endsWith?"(?="+U+")":"$";else{var _e=h[h.length-1],Oe=typeof _e=="string"?re.indexOf(_e[_e.length-1])>-1:_e===void 0;W||(J+="(?:"+re+"(?="+U+"))?"),Oe||(J+="(?="+re+"|"+U+")")}return new RegExp(J,g(C))}x=z;function q(h,M,C){return h instanceof RegExp?be(h,M):Array.isArray(h)?Se(h,M,C):We(h,M,C)}we.Bo=q},36497:function(Fe){Fe.exports=We,Fe.exports.parse=he,Fe.exports.compile=le,Fe.exports.tokensToFunction=fe,Fe.exports.tokensToRegExp=Se;var we="/",x="./",s=new RegExp(["(\\\\.)","(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?"].join("|"),"g");function he(z,q){for(var h=[],M=0,C=0,H="",W=q&&q.delimiter||we,T=q&&q.delimiters||x,I=!1,A;(A=s.exec(z))!==null;){var G=A[0],V=A[1],$=A.index;if(H+=z.slice(C,$),C=$+G.length,V){H+=V[1],I=!0;continue}var U="",re=z[C],J=A[2],B=A[3],ne=A[4],j=A[5];if(!I&&H.length){var de=H.length-1;T.indexOf(H[de])>-1&&(U=H[de],H=H.slice(0,de))}H&&(h.push(H),H="",I=!1);var Pe=U!==""&&re!==void 0&&re!==U,Ee=j==="+"||j==="*",_e=j==="?"||j==="*",Oe=U||W,Ae=B||ne;h.push({name:J||M++,prefix:U,delimiter:Oe,optional:_e,repeat:Ee,partial:Pe,pattern:Ae?u(Ae):"[^"+Y(Oe)+"]+?"})}return(H||C<z.length)&&h.push(H+z.substr(C)),h}function le(z,q){return fe(he(z,q))}function fe(z){for(var q=new Array(z.length),h=0;h<z.length;h++)typeof z[h]=="object"&&(q[h]=new RegExp("^(?:"+z[h].pattern+")$"));return function(M,C){for(var H="",W=C&&C.encode||encodeURIComponent,T=0;T<z.length;T++){var I=z[T];if(typeof I=="string"){H+=I;continue}var A=M?M[I.name]:void 0,G;if(Array.isArray(A)){if(!I.repeat)throw new TypeError('Expected "'+I.name+'" to not repeat, but got array');if(A.length===0){if(I.optional)continue;throw new TypeError('Expected "'+I.name+'" to not be empty')}for(var V=0;V<A.length;V++){if(G=W(A[V],I),!q[T].test(G))throw new TypeError('Expected all "'+I.name+'" to match "'+I.pattern+'"');H+=(V===0?I.prefix:I.delimiter)+G}continue}if(typeof A=="string"||typeof A=="number"||typeof A=="boolean"){if(G=W(String(A),I),!q[T].test(G))throw new TypeError('Expected "'+I.name+'" to match "'+I.pattern+'", but got "'+G+'"');H+=I.prefix+G;continue}if(I.optional){I.partial&&(H+=I.prefix);continue}throw new TypeError('Expected "'+I.name+'" to be '+(I.repeat?"an array":"a string"))}return H}}function Y(z){return z.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function u(z){return z.replace(/([=!:$/()])/g,"\\$1")}function ce(z){return z&&z.sensitive?"":"i"}function me(z,q){if(!q)return z;var h=z.source.match(/\((?!\?)/g);if(h)for(var M=0;M<h.length;M++)q.push({name:M,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,pattern:null});return z}function g(z,q,h){for(var M=[],C=0;C<z.length;C++)M.push(We(z[C],q,h).source);return new RegExp("(?:"+M.join("|")+")",ce(h))}function be(z,q,h){return Se(he(z,h),q,h)}function Se(z,q,h){h=h||{};for(var M=h.strict,C=h.start!==!1,H=h.end!==!1,W=Y(h.delimiter||we),T=h.delimiters||x,I=[].concat(h.endsWith||[]).map(Y).concat("$").join("|"),A=C?"^":"",G=z.length===0,V=0;V<z.length;V++){var $=z[V];if(typeof $=="string")A+=Y($),G=V===z.length-1&&T.indexOf($[$.length-1])>-1;else{var U=$.repeat?"(?:"+$.pattern+")(?:"+Y($.delimiter)+"(?:"+$.pattern+"))*":$.pattern;q&&q.push($),$.optional?$.partial?A+=Y($.prefix)+"("+U+")?":A+="(?:"+Y($.prefix)+"("+U+"))?":A+=Y($.prefix)+"("+U+")"}}return H?(M||(A+="(?:"+W+")?"),A+=I==="$"?"$":"(?="+I+")"):(M||(A+="(?:"+W+"(?="+I+"))?"),G||(A+="(?="+W+"|"+I+")")),new RegExp(A,ce(h))}function We(z,q,h){return z instanceof RegExp?me(z,q):Array.isArray(z)?g(z,q,h):be(z,q,h)}}}]);
