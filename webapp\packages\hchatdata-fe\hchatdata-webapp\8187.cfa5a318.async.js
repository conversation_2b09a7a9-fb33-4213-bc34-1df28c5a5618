"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[8187],{82392:function(y,e,t){var l=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.devUseWarning=e.default=e.WarningContext=void 0,e.noop=a,e.resetWarned=c;var o=l(t(44194)),r=l(t(13037));function a(){}let d=null;function c(){d=null,(0,r.resetWarned)()}let i=a;const s=e.WarningContext=o.createContext({}),S=e.devUseWarning=()=>{const n=()=>{};return n.deprecated=a,n};var f=e.default=i},59344:function(y,e,t){"use client";var l=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.DisabledContextProvider=void 0;var o=l(t(44194));const r=o.createContext(!1),a=({children:c,disabled:i})=>{const s=o.useContext(r);return o.createElement(r.Provider,{value:i!=null?i:s},c)};e.DisabledContextProvider=a;var d=e.default=r},8712:function(y,e,t){"use client";var l=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.SizeContextProvider=void 0;var o=l(t(44194));const r=o.createContext(void 0),a=({children:c,size:i})=>{const s=o.useContext(r);return o.createElement(r.Provider,{value:i||s},c)};e.SizeContextProvider=a;var d=e.default=r},68208:function(y,e,t){var l=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.defaultPrefixCls=e.defaultIconPrefixCls=e.Variants=e.ConfigContext=e.ConfigConsumer=void 0,e.useComponentConfig=f;var o=l(t(44194));const r=e.defaultPrefixCls="ant",a=e.defaultIconPrefixCls="anticon",d=e.Variants=["outlined","borderless","filled","underlined"],c=(n,u)=>u||(n?`${r}-${n}`:r),i=e.ConfigContext=o.createContext({getPrefixCls:c,iconPrefixCls:a}),{Consumer:s}=i;e.ConfigConsumer=s;const S={};function f(n){const u=o.useContext(i),{getPrefixCls:v,direction:g,getPopupContainer:m}=u,C=u[n];return Object.assign(Object.assign({classNames:S,styles:S},C),{getPrefixCls:v,direction:g,getPopupContainer:m})}},88470:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var l=t(8512);const o=a=>{const[,,,,d]=(0,l.useToken)();return d?`${a}-css-var`:""};var r=e.default=o},15208:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=l(t(44194)),r=l(t(8712));const a=c=>{const i=o.default.useContext(r.default);return o.default.useMemo(()=>c?typeof c=="string"?c!=null?c:i:typeof c=="function"?c(i):i:i,[c,i])};var d=e.default=a},68862:function(y,e,t){"use client";var l=t(42642).default,o=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.VariantContext=e.NoStyleItemContext=e.NoFormStyle=e.FormProvider=e.FormItemPrefixContext=e.FormItemInputContext=e.FormContext=void 0;var r=o(t(44194)),a=t(68577),d=l(t(37982));const c=e.FormContext=r.createContext({labelAlign:"right",vertical:!1,itemRef:()=>{}}),i=e.NoStyleItemContext=r.createContext(null),s=v=>{const g=(0,d.default)(v,["prefixCls"]);return r.createElement(a.FormProvider,Object.assign({},g))};e.FormProvider=s;const S=e.FormItemPrefixContext=r.createContext({prefixCls:""}),f=e.FormItemInputContext=r.createContext({}),n=({children:v,status:g,override:m})=>{const C=r.useContext(f),b=r.useMemo(()=>{const p=Object.assign({},C);return m&&delete p.isFormItemInput,g&&(delete p.status,delete p.hasFeedback,delete p.feedbackIcon),p},[g,m,C]);return r.createElement(f.Provider,{value:b},v)};e.NoFormStyle=n;const u=e.VariantContext=r.createContext(void 0)},8023:function(y,e,t){"use client";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var l=t(44194),o=e.default=(0,l.createContext)(void 0)},22435:function(y,e,t){"use client";Object.defineProperty(e,"__esModule",{value:!0}),e.textEllipsis=e.resetIcon=e.resetComponent=e.operationUnit=e.genLinkStyle=e.genIconStyle=e.genFocusStyle=e.genFocusOutline=e.genCommonStyle=e.clearFix=void 0;var l=t(40044);const o=e.textEllipsis={overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"},r=(u,v=!1)=>({boxSizing:"border-box",margin:0,padding:0,color:u.colorText,fontSize:u.fontSize,lineHeight:u.lineHeight,listStyle:"none",fontFamily:v?"inherit":u.fontFamily});e.resetComponent=r;const a=()=>({display:"inline-flex",alignItems:"center",color:"inherit",fontStyle:"normal",lineHeight:0,textAlign:"center",textTransform:"none",verticalAlign:"-0.125em",textRendering:"optimizeLegibility","-webkit-font-smoothing":"antialiased","-moz-osx-font-smoothing":"grayscale","> *":{lineHeight:1},svg:{display:"inline-block"}});e.resetIcon=a;const d=()=>({"&::before":{display:"table",content:'""'},"&::after":{display:"table",clear:"both",content:'""'}});e.clearFix=d;const c=u=>({a:{color:u.colorLink,textDecoration:u.linkDecoration,backgroundColor:"transparent",outline:"none",cursor:"pointer",transition:`color ${u.motionDurationSlow}`,"-webkit-text-decoration-skip":"objects","&:hover":{color:u.colorLinkHover},"&:active":{color:u.colorLinkActive},"&:active, &:hover":{textDecoration:u.linkHoverDecoration,outline:0},"&:focus":{textDecoration:u.linkFocusDecoration,outline:0},"&[disabled]":{color:u.colorTextDisabled,cursor:"not-allowed"}}});e.genLinkStyle=c;const i=(u,v,g,m)=>{const C=`[class^="${v}"], [class*=" ${v}"]`,b=g?`.${g}`:C,p={boxSizing:"border-box","&::before, &::after":{boxSizing:"border-box"}};let h={};return m!==!1&&(h={fontFamily:u.fontFamily,fontSize:u.fontSize}),{[b]:Object.assign(Object.assign(Object.assign({},h),p),{[C]:p})}};e.genCommonStyle=i;const s=(u,v)=>({outline:`${(0,l.unit)(u.lineWidthFocus)} solid ${u.colorPrimaryBorder}`,outlineOffset:v!=null?v:1,transition:"outline-offset 0s, outline 0s"});e.genFocusOutline=s;const S=(u,v)=>({"&:focus-visible":Object.assign({},s(u,v))});e.genFocusStyle=S;const f=u=>({[`.${u}`]:Object.assign(Object.assign({},a()),{[`.${u} .${u}-icon`]:{display:"block"}})});e.genIconStyle=f;const n=u=>Object.assign(Object.assign({color:u.colorLink,textDecoration:u.linkDecoration,outline:"none",cursor:"pointer",transition:`all ${u.motionDurationSlow}`,border:0,padding:0,background:"none",userSelect:"none"},S(u)),{"&:focus, &:hover":{color:u.colorLinkHover},"&:active":{color:u.colorLinkActive}});e.operationUnit=n},10215:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.defaultConfig=e.DesignTokenContext=void 0,Object.defineProperty(e,"defaultTheme",{enumerable:!0,get:function(){return a.default}});var o=l(t(44194)),r=l(t(81368)),a=l(t(71179));const d=e.defaultConfig={token:r.default,override:{override:r.default},hashed:!0},c=e.DesignTokenContext=o.default.createContext(d)},66251:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PresetColors",{enumerable:!0,get:function(){return l.PresetColors}});var l=t(99190)},99190:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.PresetColors=void 0;const t=e.PresetColors=["blue","purple","cyan","green","magenta","pink","red","orange","yellow","volcano","geekblue","lime","gold"]},8512:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"DesignTokenContext",{enumerable:!0,get:function(){return f.DesignTokenContext}}),Object.defineProperty(e,"PresetColors",{enumerable:!0,get:function(){return a.PresetColors}}),Object.defineProperty(e,"calc",{enumerable:!0,get:function(){return r.genCalc}}),Object.defineProperty(e,"defaultConfig",{enumerable:!0,get:function(){return f.defaultConfig}}),Object.defineProperty(e,"genComponentStyleHook",{enumerable:!0,get:function(){return i.genComponentStyleHook}}),Object.defineProperty(e,"genPresetColor",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(e,"genStyleHooks",{enumerable:!0,get:function(){return i.genStyleHooks}}),Object.defineProperty(e,"genSubStyleComponent",{enumerable:!0,get:function(){return i.genSubStyleComponent}}),Object.defineProperty(e,"getLineHeight",{enumerable:!0,get:function(){return d.getLineHeight}}),Object.defineProperty(e,"mergeToken",{enumerable:!0,get:function(){return r.mergeToken}}),Object.defineProperty(e,"statistic",{enumerable:!0,get:function(){return r.statistic}}),Object.defineProperty(e,"statisticToken",{enumerable:!0,get:function(){return r.statisticToken}}),Object.defineProperty(e,"useResetIconStyle",{enumerable:!0,get:function(){return S.default}}),Object.defineProperty(e,"useStyleRegister",{enumerable:!0,get:function(){return o.useStyleRegister}}),Object.defineProperty(e,"useToken",{enumerable:!0,get:function(){return c.default}});var o=t(40044),r=t(77167),a=t(66251),d=t(33238),c=l(t(76494)),i=t(20186),s=l(t(5114)),S=l(t(76512)),f=t(10215)},57194:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.getSolidColor=e.getAlphaColor=void 0;var l=t(87471);const o=(a,d)=>new l.FastColor(a).setA(d).toRgbString();e.getAlphaColor=o;const r=(a,d)=>new l.FastColor(a).darken(d).toHexString();e.getSolidColor=r},5464:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.generateNeutralColorPalettes=e.generateColorPalettes=void 0;var l=t(64970),o=t(57194);const r=d=>{const c=(0,l.generate)(d);return{1:c[0],2:c[1],3:c[2],4:c[3],5:c[4],6:c[5],7:c[6],8:c[4],9:c[5],10:c[6]}};e.generateColorPalettes=r;const a=(d,c)=>{const i=d||"#fff",s=c||"#000";return{colorBgBase:i,colorTextBase:s,colorText:(0,o.getAlphaColor)(s,.88),colorTextSecondary:(0,o.getAlphaColor)(s,.65),colorTextTertiary:(0,o.getAlphaColor)(s,.45),colorTextQuaternary:(0,o.getAlphaColor)(s,.25),colorFill:(0,o.getAlphaColor)(s,.15),colorFillSecondary:(0,o.getAlphaColor)(s,.06),colorFillTertiary:(0,o.getAlphaColor)(s,.04),colorFillQuaternary:(0,o.getAlphaColor)(s,.02),colorBgSolid:(0,o.getAlphaColor)(s,1),colorBgSolidHover:(0,o.getAlphaColor)(s,.75),colorBgSolidActive:(0,o.getAlphaColor)(s,.95),colorBgLayout:(0,o.getSolidColor)(i,4),colorBgContainer:(0,o.getSolidColor)(i,0),colorBgElevated:(0,o.getSolidColor)(i,0),colorBgSpotlight:(0,o.getAlphaColor)(s,.85),colorBgBlur:"transparent",colorBorder:(0,o.getSolidColor)(i,15),colorBorderSecondary:(0,o.getSolidColor)(i,6)}};e.generateNeutralColorPalettes=a},35298:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=f;var o=t(64970),r=t(81368),a=l(t(14050)),d=l(t(22928)),c=l(t(45589)),i=l(t(84760)),s=l(t(46181)),S=t(5464);function f(n){o.presetPrimaryColors.pink=o.presetPrimaryColors.magenta,o.presetPalettes.pink=o.presetPalettes.magenta;const u=Object.keys(r.defaultPresetColors).map(v=>{const g=n[v]===o.presetPrimaryColors[v]?o.presetPalettes[v]:(0,o.generate)(n[v]);return Array.from({length:10},()=>1).reduce((m,C,b)=>(m[`${v}-${b+1}`]=g[b],m[`${v}${b+1}`]=g[b],m),{})}).reduce((v,g)=>(v=Object.assign(Object.assign({},v),g),v),{});return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},n),u),(0,a.default)(n,{generateColorPalettes:S.generateColorPalettes,generateNeutralColorPalettes:S.generateNeutralColorPalettes})),(0,i.default)(n.fontSize)),(0,s.default)(n)),(0,c.default)(n)),(0,d.default)(n))}},71179:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=t(40044),r=l(t(35298));const a=(0,o.createTheme)(r.default);var d=e.default=a},81368:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.defaultPresetColors=e.default=void 0;const t=e.defaultPresetColors={blue:"#1677FF",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#EB2F96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911"},l=Object.assign(Object.assign({},t),{colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff4d4f",colorInfo:"#1677ff",colorLink:"",colorTextBase:"",colorBgBase:"",fontFamily:`-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
'Noto Color Emoji'`,fontFamilyCode:"'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInBack:"cubic-bezier(0.71, -0.46, 0.88, 0.6)",motionEaseInQuint:"cubic-bezier(0.755, 0.05, 0.855, 0.06)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:6,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,motion:!0});var o=e.default=l},14050:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=o;var l=t(87471);function o(r,{generateColorPalettes:a,generateNeutralColorPalettes:d}){const{colorSuccess:c,colorWarning:i,colorError:s,colorInfo:S,colorPrimary:f,colorBgBase:n,colorTextBase:u}=r,v=a(f),g=a(c),m=a(i),C=a(s),b=a(S),p=d(n,u),h=r.colorLink||r.colorInfo,P=a(h),O=new l.FastColor(C[1]).mix(new l.FastColor(C[3]),50).toHexString();return Object.assign(Object.assign({},p),{colorPrimaryBg:v[1],colorPrimaryBgHover:v[2],colorPrimaryBorder:v[3],colorPrimaryBorderHover:v[4],colorPrimaryHover:v[5],colorPrimary:v[6],colorPrimaryActive:v[7],colorPrimaryTextHover:v[8],colorPrimaryText:v[9],colorPrimaryTextActive:v[10],colorSuccessBg:g[1],colorSuccessBgHover:g[2],colorSuccessBorder:g[3],colorSuccessBorderHover:g[4],colorSuccessHover:g[4],colorSuccess:g[6],colorSuccessActive:g[7],colorSuccessTextHover:g[8],colorSuccessText:g[9],colorSuccessTextActive:g[10],colorErrorBg:C[1],colorErrorBgHover:C[2],colorErrorBgFilledHover:O,colorErrorBgActive:C[3],colorErrorBorder:C[3],colorErrorBorderHover:C[4],colorErrorHover:C[5],colorError:C[6],colorErrorActive:C[7],colorErrorTextHover:C[8],colorErrorText:C[9],colorErrorTextActive:C[10],colorWarningBg:m[1],colorWarningBgHover:m[2],colorWarningBorder:m[3],colorWarningBorderHover:m[4],colorWarningHover:m[4],colorWarning:m[6],colorWarningActive:m[7],colorWarningTextHover:m[8],colorWarningText:m[9],colorWarningTextActive:m[10],colorInfoBg:b[1],colorInfoBgHover:b[2],colorInfoBorder:b[3],colorInfoBorderHover:b[4],colorInfoHover:b[4],colorInfo:b[6],colorInfoActive:b[7],colorInfoTextHover:b[8],colorInfoText:b[9],colorInfoTextActive:b[10],colorLinkHover:P[4],colorLink:P[6],colorLinkActive:P[7],colorBgMask:new l.FastColor("#000").setA(.45).toRgbString(),colorWhite:"#fff"})}},22928:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=r;var o=l(t(12112));function r(a){const{motionUnit:d,motionBase:c,borderRadius:i,lineWidth:s}=a;return Object.assign({motionDurationFast:`${(c+d).toFixed(1)}s`,motionDurationMid:`${(c+d*2).toFixed(1)}s`,motionDurationSlow:`${(c+d*3).toFixed(1)}s`,lineWidthBold:s+1},(0,o.default)(i))}},45589:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;const t=o=>{const{controlHeight:r}=o;return{controlHeightSM:r*.75,controlHeightXS:r*.5,controlHeightLG:r*1.25}};var l=e.default=t},84760:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=l(t(33238));const r=d=>{const c=(0,o.default)(d),i=c.map(m=>m.size),s=c.map(m=>m.lineHeight),S=i[1],f=i[0],n=i[2],u=s[1],v=s[0],g=s[2];return{fontSizeSM:f,fontSize:S,fontSizeLG:n,fontSizeXL:i[3],fontSizeHeading1:i[6],fontSizeHeading2:i[5],fontSizeHeading3:i[4],fontSizeHeading4:i[3],fontSizeHeading5:i[2],lineHeight:u,lineHeightLG:g,lineHeightSM:v,fontHeight:Math.round(u*S),fontHeightLG:Math.round(g*n),fontHeightSM:Math.round(v*f),lineHeightHeading1:s[6],lineHeightHeading2:s[5],lineHeightHeading3:s[4],lineHeightHeading4:s[3],lineHeightHeading5:s[2]}};var a=e.default=r},33238:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=l,e.getLineHeight=t;function t(o){return(o+8)/o}function l(o){const r=Array.from({length:10}).map((a,d)=>{const c=d-1,i=o*Math.pow(Math.E,c/5),s=d>1?Math.floor(i):Math.ceil(i);return Math.floor(s/2)*2});return r[1]=o,r.map(a=>({size:a,lineHeight:t(a)}))}},12112:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;const t=o=>{let r=o,a=o,d=o,c=o;return o<6&&o>=5?r=o+1:o<16&&o>=6?r=o+2:o>=16&&(r=16),o<7&&o>=5?a=4:o<8&&o>=7?a=5:o<14&&o>=8?a=6:o<16&&o>=14?a=7:o>=16&&(a=8),o<6&&o>=2?d=1:o>=6&&(d=2),o>4&&o<8?c=4:o>=8&&(c=6),{borderRadius:o,borderRadiusXS:d,borderRadiusSM:a,borderRadiusLG:r,borderRadiusOuter:c}};var l=e.default=t},46181:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=t;function t(l){const{sizeUnit:o,sizeStep:r}=l;return{sizeXXL:o*(r+8),sizeXL:o*(r+4),sizeLG:o*(r+2),sizeMD:o*(r+1),sizeMS:o*r,size:o*r,sizeSM:o*(r-1),sizeXS:o*(r-2),sizeXXS:o*(r-3)}}},76494:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=v,e.unitless=e.ignore=e.getComputedToken=void 0;var o=l(t(44194)),r=t(40044),a=l(t(33947)),d=t(10215),c=l(t(81368)),i=l(t(25474)),s=function(g,m){var C={};for(var b in g)Object.prototype.hasOwnProperty.call(g,b)&&m.indexOf(b)<0&&(C[b]=g[b]);if(g!=null&&typeof Object.getOwnPropertySymbols=="function")for(var p=0,b=Object.getOwnPropertySymbols(g);p<b.length;p++)m.indexOf(b[p])<0&&Object.prototype.propertyIsEnumerable.call(g,b[p])&&(C[b[p]]=g[b[p]]);return C};const S=e.unitless={lineHeight:!0,lineHeightSM:!0,lineHeightLG:!0,lineHeightHeading1:!0,lineHeightHeading2:!0,lineHeightHeading3:!0,lineHeightHeading4:!0,lineHeightHeading5:!0,opacityLoading:!0,fontWeightStrong:!0,zIndexPopupBase:!0,zIndexBase:!0,opacityImage:!0},f=e.ignore={size:!0,sizeSM:!0,sizeLG:!0,sizeMD:!0,sizeXS:!0,sizeXXS:!0,sizeMS:!0,sizeXL:!0,sizeXXL:!0,sizeUnit:!0,sizeStep:!0,motionBase:!0,motionUnit:!0},n={screenXS:!0,screenXSMin:!0,screenXSMax:!0,screenSM:!0,screenSMMin:!0,screenSMMax:!0,screenMD:!0,screenMDMin:!0,screenMDMax:!0,screenLG:!0,screenLGMin:!0,screenLGMax:!0,screenXL:!0,screenXLMin:!0,screenXLMax:!0,screenXXL:!0,screenXXLMin:!0},u=(g,m,C)=>{const b=C.getDerivativeToken(g),{override:p}=m,h=s(m,["override"]);let P=Object.assign(Object.assign({},b),{override:p});return P=(0,i.default)(P),h&&Object.entries(h).forEach(([O,M])=>{const{theme:j}=M,x=s(M,["theme"]);let T=x;j&&(T=u(Object.assign(Object.assign({},P),x),{override:x},j)),P[O]=T}),P};e.getComputedToken=u;function v(){const{token:g,hashed:m,theme:C,override:b,cssVar:p}=o.default.useContext(d.DesignTokenContext),h=`${a.default}-${m||""}`,P=C||d.defaultTheme,[O,M,j]=(0,r.useCacheToken)(P,[c.default,g],{salt:h,override:b,getComputedToken:u,formatToken:i.default,cssVar:p&&{prefix:p.prefix,key:p.key,unitless:S,ignore:f,preserve:n}});return[P,j,m?M:"",O,p]}},25474:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=c;var o=t(87471),r=l(t(81368)),a=l(t(98666)),d=function(i,s){var S={};for(var f in i)Object.prototype.hasOwnProperty.call(i,f)&&s.indexOf(f)<0&&(S[f]=i[f]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,f=Object.getOwnPropertySymbols(i);n<f.length;n++)s.indexOf(f[n])<0&&Object.prototype.propertyIsEnumerable.call(i,f[n])&&(S[f[n]]=i[f[n]]);return S};function c(i){const{override:s}=i,S=d(i,["override"]),f=Object.assign({},s);Object.keys(r.default).forEach(h=>{delete f[h]});const n=Object.assign(Object.assign({},S),f),u=480,v=576,g=768,m=992,C=1200,b=1600;if(n.motion===!1){const h="0s";n.motionDurationFast=h,n.motionDurationMid=h,n.motionDurationSlow=h}return Object.assign(Object.assign(Object.assign({},n),{colorFillContent:n.colorFillSecondary,colorFillContentHover:n.colorFill,colorFillAlter:n.colorFillQuaternary,colorBgContainerDisabled:n.colorFillTertiary,colorBorderBg:n.colorBgContainer,colorSplit:(0,a.default)(n.colorBorderSecondary,n.colorBgContainer),colorTextPlaceholder:n.colorTextQuaternary,colorTextDisabled:n.colorTextQuaternary,colorTextHeading:n.colorText,colorTextLabel:n.colorTextSecondary,colorTextDescription:n.colorTextTertiary,colorTextLightSolid:n.colorWhite,colorHighlight:n.colorError,colorBgTextHover:n.colorFillSecondary,colorBgTextActive:n.colorFill,colorIcon:n.colorTextTertiary,colorIconHover:n.colorText,colorErrorOutline:(0,a.default)(n.colorErrorBg,n.colorBgContainer),colorWarningOutline:(0,a.default)(n.colorWarningBg,n.colorBgContainer),fontSizeIcon:n.fontSizeSM,lineWidthFocus:n.lineWidth*3,lineWidth:n.lineWidth,controlOutlineWidth:n.lineWidth*2,controlInteractiveSize:n.controlHeight/2,controlItemBgHover:n.colorFillTertiary,controlItemBgActive:n.colorPrimaryBg,controlItemBgActiveHover:n.colorPrimaryBgHover,controlItemBgActiveDisabled:n.colorFill,controlTmpOutline:n.colorFillQuaternary,controlOutline:(0,a.default)(n.colorPrimaryBg,n.colorBgContainer),lineType:n.lineType,borderRadius:n.borderRadius,borderRadiusXS:n.borderRadiusXS,borderRadiusSM:n.borderRadiusSM,borderRadiusLG:n.borderRadiusLG,fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:n.sizeXXS,paddingXS:n.sizeXS,paddingSM:n.sizeSM,padding:n.size,paddingMD:n.sizeMD,paddingLG:n.sizeLG,paddingXL:n.sizeXL,paddingContentHorizontalLG:n.sizeLG,paddingContentVerticalLG:n.sizeMS,paddingContentHorizontal:n.sizeMS,paddingContentVertical:n.sizeSM,paddingContentHorizontalSM:n.size,paddingContentVerticalSM:n.sizeXS,marginXXS:n.sizeXXS,marginXS:n.sizeXS,marginSM:n.sizeSM,margin:n.size,marginMD:n.sizeMD,marginLG:n.sizeLG,marginXL:n.sizeXL,marginXXL:n.sizeXXL,boxShadow:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowSecondary:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTertiary:`
      0 1px 2px 0 rgba(0, 0, 0, 0.03),
      0 1px 6px -1px rgba(0, 0, 0, 0.02),
      0 2px 4px 0 rgba(0, 0, 0, 0.02)
    `,screenXS:u,screenXSMin:u,screenXSMax:v-1,screenSM:v,screenSMMin:v,screenSMMax:g-1,screenMD:g,screenMDMin:g,screenMDMax:m-1,screenLG:m,screenLGMin:m,screenLGMax:C-1,screenXL:C,screenXLMin:C,screenXLMax:b-1,screenXXL:b,screenXXLMin:b,boxShadowPopoverArrow:"2px 2px 5px rgba(0, 0, 0, 0.05)",boxShadowCard:`
      0 1px 2px -2px ${new o.FastColor("rgba(0, 0, 0, 0.16)").toRgbString()},
      0 3px 6px 0 ${new o.FastColor("rgba(0, 0, 0, 0.12)").toRgbString()},
      0 5px 12px 4px ${new o.FastColor("rgba(0, 0, 0, 0.09)").toRgbString()}
    `,boxShadowDrawerRight:`
      -6px 0 16px 0 rgba(0, 0, 0, 0.08),
      -3px 0 6px -4px rgba(0, 0, 0, 0.12),
      -9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerLeft:`
      6px 0 16px 0 rgba(0, 0, 0, 0.08),
      3px 0 6px -4px rgba(0, 0, 0, 0.12),
      9px 0 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerUp:`
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowDrawerDown:`
      0 -6px 16px 0 rgba(0, 0, 0, 0.08),
      0 -3px 6px -4px rgba(0, 0, 0, 0.12),
      0 -9px 28px 8px rgba(0, 0, 0, 0.05)
    `,boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)"}),f)}},5114:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=o;var l=t(66251);function o(r,a){return l.PresetColors.reduce((d,c)=>{const i=r[`${c}1`],s=r[`${c}3`],S=r[`${c}6`],f=r[`${c}7`];return Object.assign(Object.assign({},d),a(c,{lightColor:i,lightBorderColor:s,darkColor:S,textColor:f}))},{})}},20186:function(y,e,t){var l=t(53023).default;Object.defineProperty(e,"__esModule",{value:!0}),e.genSubStyleComponent=e.genStyleHooks=e.genComponentStyleHook=void 0;var o=t(44194),r=t(77167),a=t(68208),d=t(22435),c=l(t(76494));const{genStyleHooks:i,genComponentStyleHook:s,genSubStyleComponent:S}=(0,r.genStyleUtils)({usePrefix:()=>{const{getPrefixCls:f,iconPrefixCls:n}=(0,o.useContext)(a.ConfigContext);return{rootPrefixCls:f(),iconPrefixCls:n}},useToken:()=>{const[f,n,u,v,g]=(0,c.default)();return{theme:f,realToken:n,hashId:u,token:v,cssVar:g}},useCSP:()=>{const{csp:f}=(0,o.useContext)(a.ConfigContext);return f!=null?f:{}},getResetStyles:(f,n)=>{var u;const v=(0,d.genLinkStyle)(f);return[v,{"&":v},(0,d.genIconStyle)((u=n==null?void 0:n.prefix.iconPrefixCls)!==null&&u!==void 0?u:a.defaultIconPrefixCls)]},getCommonStyle:d.genCommonStyle,getCompUnitless:()=>c.unitless});e.genSubStyleComponent=S,e.genComponentStyleHook=s,e.genStyleHooks=i},98666:function(y,e,t){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var l=t(87471);function o(d){return d>=0&&d<=255}function r(d,c){const{r:i,g:s,b:S,a:f}=new l.FastColor(d).toRgb();if(f<1)return d;const{r:n,g:u,b:v}=new l.FastColor(c).toRgb();for(let g=.01;g<=1;g+=.01){const m=Math.round((i-n*(1-g))/g),C=Math.round((s-u*(1-g))/g),b=Math.round((S-v*(1-g))/g);if(o(m)&&o(C)&&o(b))return new l.FastColor({r:m,g:C,b,a:Math.round(g*100)/100}).toRgbString()}return new l.FastColor({r:i,g:s,b:S,a:1}).toRgbString()}var a=e.default=r},76512:function(y,e,t){var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=t(40044),r=t(22435),a=l(t(76494));const d=(i,s)=>{const[S,f]=(0,a.default)();return(0,o.useStyleRegister)({theme:S,token:f,hashId:"",path:["ant-design-icons",i],nonce:()=>s==null?void 0:s.nonce,layer:{name:"antd"}},()=>[(0,r.genIconStyle)(i)])};var c=e.default=d},33947:function(y,e,t){"use client";var l=t(42642).default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=l(t(65945)),r=e.default=o.default},65945:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=e.default="5.25.2"},37982:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=t;function t(l,o){var r=Object.assign({},l);return Array.isArray(o)&&o.forEach(function(a){delete r[a]}),r}},13037:function(y,e){Object.defineProperty(e,"__esModule",{value:!0}),e.call=c,e.default=void 0,e.note=a,e.noteOnce=s,e.preMessage=void 0,e.resetWarned=d,e.warning=r,e.warningOnce=i;var t={},l=[],o=e.preMessage=function(n){l.push(n)};function r(f,n){if(0)var u}function a(f,n){if(0)var u}function d(){t={}}function c(f,n,u){!n&&!t[u]&&(f(!1,u),t[u]=!0)}function i(f,n){c(r,f,n)}function s(f,n){c(a,f,n)}i.preMessage=o,i.resetWarned=d,i.noteOnce=s;var S=e.default=i}}]);
