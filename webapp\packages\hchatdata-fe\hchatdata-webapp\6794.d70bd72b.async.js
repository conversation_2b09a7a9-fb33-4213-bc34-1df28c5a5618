!(function(){"use strict";var G=Object.defineProperty;var D=Object.getOwnPropertySymbols;var Q=Object.prototype.hasOwnProperty,Z=Object.prototype.propertyIsEnumerable;var $=(k,i,r)=>i in k?G(k,i,{enumerable:!0,configurable:!0,writable:!0,value:r}):k[i]=r,B=(k,i)=>{for(var r in i||(i={}))Q.call(i,r)&&$(k,r,i[r]);if(D)for(var r of D(i))Z.call(i,r)&&$(k,r,i[r]);return k};var y=(k,i,r)=>new Promise((W,v)=>{var h=l=>{try{m(r.next(l))}catch(_){v(_)}},t=l=>{try{m(r.throw(l))}catch(_){v(_)}},m=l=>l.done?W(l.value):Promise.resolve(l.value).then(h,t);m((r=r.apply(k,i)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6794],{96547:function(k,i,r){r.d(i,{A:function(){return v}});var W=r(29134);function v(h,t){var m,l,_;h.accDescr&&((m=t.setAccDescription)==null||m.call(t,h.accDescr)),h.accTitle&&((l=t.setAccTitle)==null||l.call(t,h.accTitle)),h.title&&((_=t.setDiagramTitle)==null||_.call(t,h.title))}(0,W.eW)(v,"populateCommonDb")},36794:function(k,i,r){r.d(i,{diagram:function(){return Y}});var W=r(96547),v=r(50854),h=r(44133),t=r(29134),m=r(38663),l={packet:[]},_=structuredClone(l),T=t.vZ.packet,A=(0,t.eW)(()=>{const e=(0,v.Rb)(B(B({},T),(0,t.iE)().packet));return e.showBits&&(e.paddingY+=10),e},"getConfig"),M=(0,t.eW)(()=>_.packet,"getPacket"),O=(0,t.eW)(e=>{e.length>0&&_.packet.push(e)},"pushWord"),S=(0,t.eW)(()=>{(0,t.ZH)(),_=structuredClone(l)},"clear"),x={pushWord:O,getPacket:M,getConfig:A,clear:S,setAccTitle:t.GN,getAccTitle:t.eu,setDiagramTitle:t.g2,getDiagramTitle:t.Kr,getAccDescription:t.Mx,setAccDescription:t.U$},F=1e4,L=(0,t.eW)(e=>{(0,W.A)(e,x);let a=-1,s=[],d=1;const{bitsPerRow:p}=x.getConfig();for(let{start:o,end:n,bits:c,label:E}of e.blocks){if(o!==void 0&&n!==void 0&&n<o)throw new Error(`Packet block ${o} - ${n} is invalid. End must be greater than start.`);if(o!=null||(o=a+1),o!==a+1)throw new Error(`Packet block ${o} - ${n!=null?n:o} is not contiguous. It should start from ${a+1}.`);if(c===0)throw new Error(`Packet block ${o} is invalid. Cannot have a zero bit field.`);for(n!=null||(n=o+(c!=null?c:1)-1),c!=null||(c=n-o+1),a=n,t.cM.debug(`Packet block ${o} - ${a} with label ${E}`);s.length<=p+1&&x.getPacket().length<F;){const[u,g]=z({start:o,end:n,bits:c,label:E},d,p);if(s.push(u),u.end+1===d*p&&(x.pushWord(s),s=[],d++),!g)break;({start:o,end:n,bits:c,label:E}=g)}}x.pushWord(s)},"populate"),z=(0,t.eW)((e,a,s)=>{if(e.start===void 0)throw new Error("start should have been set during first phase");if(e.end===void 0)throw new Error("end should have been set during first phase");if(e.start>e.end)throw new Error(`Block start ${e.start} is greater than block end ${e.end}.`);if(e.end+1<=a*s)return[e,void 0];const d=a*s-1,p=a*s;return[{start:e.start,end:d,label:e.label,bits:d-e.start},{start:p,end:e.end,label:e.label,bits:e.end-p}]},"getNextFittingBlock"),R={parse:(0,t.eW)(e=>y(this,null,function*(){const a=yield(0,m.Qc)("packet",e);t.cM.debug(a),L(a)}),"parse")},I=(0,t.eW)((e,a,s,d)=>{const p=d.db,o=p.getConfig(),{rowHeight:n,paddingY:c,bitWidth:E,bitsPerRow:u}=o,g=p.getPacket(),f=p.getDiagramTitle(),P=n+c,C=P*(g.length+1)-(f?0:n),b=E*u+2,w=(0,h.P)(a);w.attr("viewbox",`0 0 ${b} ${C}`),(0,t.v2)(w,C,b,o.useMaxWidth);for(const[H,X]of g.entries())K(w,X,H,o);w.append("text").text(f).attr("x",b/2).attr("y",C-P/2).attr("dominant-baseline","middle").attr("text-anchor","middle").attr("class","packetTitle")},"draw"),K=(0,t.eW)((e,a,s,{rowHeight:d,paddingX:p,paddingY:o,bitWidth:n,bitsPerRow:c,showBits:E})=>{const u=e.append("g"),g=s*(d+o)+o;for(const f of a){const P=f.start%c*n+1,C=(f.end-f.start+1)*n-p;if(u.append("rect").attr("x",P).attr("y",g).attr("width",C).attr("height",d).attr("class","packetBlock"),u.append("text").attr("x",P+C/2).attr("y",g+d/2).attr("class","packetLabel").attr("dominant-baseline","middle").attr("text-anchor","middle").text(f.label),!E)continue;const b=f.end===f.start,w=g-2;u.append("text").attr("x",P+(b?C/2:0)).attr("y",w).attr("class","packetByte start").attr("dominant-baseline","auto").attr("text-anchor",b?"middle":"start").text(f.start),b||u.append("text").attr("x",P+C).attr("y",w).attr("class","packetByte end").attr("dominant-baseline","auto").attr("text-anchor","end").text(f.end)}},"drawWord"),U={draw:I},j={byteFontSize:"10px",startByteColor:"black",endByteColor:"black",labelColor:"black",labelFontSize:"12px",titleColor:"black",titleFontSize:"14px",blockStrokeColor:"black",blockStrokeWidth:"1",blockFillColor:"#efefef"},N=(0,t.eW)(({packet:e}={})=>{const a=(0,v.Rb)(j,e);return`
	.packetByte {
		font-size: ${a.byteFontSize};
	}
	.packetByte.start {
		fill: ${a.startByteColor};
	}
	.packetByte.end {
		fill: ${a.endByteColor};
	}
	.packetLabel {
		fill: ${a.labelColor};
		font-size: ${a.labelFontSize};
	}
	.packetTitle {
		fill: ${a.titleColor};
		font-size: ${a.titleFontSize};
	}
	.packetBlock {
		stroke: ${a.blockStrokeColor};
		stroke-width: ${a.blockStrokeWidth};
		fill: ${a.blockFillColor};
	}
	`},"styles"),Y={parser:R,db:x,renderer:U,styles:N}}}]);
}());