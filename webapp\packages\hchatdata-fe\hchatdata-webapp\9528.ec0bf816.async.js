"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[9528],{20034:function(L,C,n){n.d(C,{Z:function(){return x}});var r=n(95687),o=n(44194),P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 289.1a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1zm0-617.2c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 551c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 439c0-29.9 11.7-58 32.8-79.2C454 338.6 482.1 327 512 327c29.9 0 58 11.6 79.2 32.8C612.4 381 624 409.1 624 439c0 29.9-11.6 58-32.8 79.2z"}}]},name:"environment",theme:"outlined"},M=P,f=n(54183),p=function(h,U){return o.createElement(f.Z,(0,r.Z)({},h,{ref:U,icon:M}))},d=o.forwardRef(p),x=d},9313:function(L,C,n){n.d(C,{Z:function(){return fe}});var r=n(94450),o=n(28659),P=n(60505),M=n(56840),f=n(81424),p=n(27634),d=n(44723),x=n(51865),s=n.n(x),h=n(44194),U=n(25371),Y=n(18191),O=n(69367),b=n(60654),_=n(36490),a=n(31549),q=["prefixCls","className","style","options","loading","multiple","bordered","onChange"],F=function(e){var j=e.prefixCls,g="".concat(j,"-loading-block");return(0,a.jsxs)("div",{className:"".concat(j,"-loading-content"),children:[(0,a.jsx)(O.Z,{gutter:{xs:8,sm:8,md:8,lg:12},children:(0,a.jsx)(b.Z,{span:22,children:(0,a.jsx)("div",{className:g})})}),(0,a.jsxs)(O.Z,{gutter:8,children:[(0,a.jsx)(b.Z,{span:8,children:(0,a.jsx)("div",{className:g})}),(0,a.jsx)(b.Z,{span:14,children:(0,a.jsx)("div",{className:g})})]}),(0,a.jsxs)(O.Z,{gutter:8,children:[(0,a.jsx)(b.Z,{span:6,children:(0,a.jsx)("div",{className:g})}),(0,a.jsx)(b.Z,{span:16,children:(0,a.jsx)("div",{className:g})})]}),(0,a.jsxs)(O.Z,{gutter:8,children:[(0,a.jsx)(b.Z,{span:13,children:(0,a.jsx)("div",{className:g})}),(0,a.jsx)(b.Z,{span:9,children:(0,a.jsx)("div",{className:g})})]}),(0,a.jsxs)(O.Z,{gutter:8,children:[(0,a.jsx)(b.Z,{span:4,children:(0,a.jsx)("div",{className:g})}),(0,a.jsx)(b.Z,{span:3,children:(0,a.jsx)("div",{className:g})}),(0,a.jsx)(b.Z,{span:14,children:(0,a.jsx)("div",{className:g})})]})]})},ee=(0,h.createContext)(null),W=function(e){var j=e.prefixCls,g=e.className,ne=e.style,$=e.options,t=$===void 0?[]:$,re=e.loading,te=re===void 0?!1:re,ie=e.multiple,K=ie===void 0?!1:ie,le=e.bordered,ge=le===void 0?!0:le,Ze=e.onChange,z=(0,P.Z)(e,q),B=(0,h.useContext)(p.ZP.ConfigContext),A=(0,h.useCallback)(function(){return t==null?void 0:t.map(function(m){return typeof m=="string"?{title:m,value:m}:m})},[t]),V=B.getPrefixCls("pro-checkcard",j),w="".concat(V,"-group"),de=(0,_.Z)(z,["children","defaultValue","value","disabled","size"]),me=(0,f.Z)(e.defaultValue,{value:e.value,onChange:e.onChange}),ce=(0,M.Z)(me,2),u=ce[0],i=ce[1],G=(0,h.useRef)(new Map),he=function(l){var c;(c=G.current)===null||c===void 0||c.set(l,!0)},Z=function(l){var c;(c=G.current)===null||c===void 0||c.delete(l)},oe=function(l){if(!K){var c;c=u,c===l.value?c=void 0:c=l.value,i==null||i(c)}if(K){var S,I=[],k=u,se=k==null?void 0:k.includes(l.value);I=(0,Y.Z)(k||[]),se||I.push(l.value),se&&(I=I.filter(function(T){return T!==l.value}));var Q=A(),xe=(S=I)===null||S===void 0||(S=S.filter(function(T){return G.current.has(T)}))===null||S===void 0?void 0:S.sort(function(T,E){var v=Q.findIndex(function(N){return N.value===T}),y=Q.findIndex(function(N){return N.value===E});return v-y});i(xe)}},J=(0,h.useMemo)(function(){if(te)return new Array(t.length||h.Children.toArray(e.children).length||1).fill(0).map(function(l,c){return(0,a.jsx)(fe,{loading:!0},c)});if(t&&t.length>0){var m=u;return A().map(function(l){var c;return(0,a.jsx)(fe,{disabled:l.disabled,size:(c=l.size)!==null&&c!==void 0?c:e.size,value:l.value,checked:K?m==null?void 0:m.includes(l.value):m===l.value,onChange:l.onChange,title:l.title,avatar:l.avatar,description:l.description,cover:l.cover},l.value.toString())})}return e.children},[A,te,K,t,e.children,e.size,u]),Ce=s()(w,g);return(0,a.jsx)(ee.Provider,{value:{toggleOption:oe,bordered:ge,value:u,disabled:e.disabled,size:e.size,loading:e.loading,multiple:e.multiple,registerValue:he,cancelValue:Z},children:(0,a.jsx)("div",(0,o.Z)((0,o.Z)({className:Ce,style:ne},de),{},{children:J}))})},ue=W,ve=n(40044),H=n(67447),X=function(e){return{backgroundColor:e.colorPrimaryBg,borderColor:e.colorPrimary}},R=function(e){return(0,r.Z)({backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"},e.componentCls,{"&-description":{color:e.colorTextDisabled},"&-title":{color:e.colorTextDisabled},"&-avatar":{opacity:"0.25"}})},ae=new ve.Keyframes("card-loading",{"0%":{backgroundPosition:"0 50%"},"50%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),ye=function(e){var j;return(0,r.Z)({},e.componentCls,(j={position:"relative",display:"inline-block",width:"320px",marginInlineEnd:"16px",marginBlockEnd:"16px",color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight,verticalAlign:"top",backgroundColor:e.colorBgContainer,borderRadius:e.borderRadius,overflow:"auto",cursor:"pointer",transition:"all 0.3s","&:last-child":{marginInlineEnd:0},"& + &":{marginInlineStart:"0 !important"},"&-bordered":{border:"".concat(e.lineWidth,"px solid ").concat(e.colorBorder)},"&-group":{display:"inline-block"}},(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)(j,"".concat(e.componentCls,"-loading"),{overflow:"hidden",userSelect:"none","&-content":(0,r.Z)({paddingInline:e.padding,paddingBlock:e.paddingSM,p:{marginBlock:0,marginInline:0}},"".concat(e.componentCls,"-loading-block"),{height:"14px",marginBlock:"4px",background:"linear-gradient(90deg, rgba(54, 61, 64, 0.2), rgba(54, 61, 64, 0.4), rgba(54, 61, 64, 0.2))",animationName:ae,animationDuration:"1.4s",animationTimingFunction:"ease",animationIterationCount:"infinite"})}),"&:focus",X(e)),"&-checked",(0,o.Z)((0,o.Z)({},X(e)),{},{"&:after":{position:"absolute",insetBlockStart:2,insetInlineEnd:2,width:0,height:0,border:"".concat(e.borderRadius+4,"px solid ").concat(e.colorPrimary),borderBlockEnd:"".concat(e.borderRadius+4,"px  solid transparent"),borderInlineStart:"".concat(e.borderRadius+4,"px  solid transparent"),borderStartEndRadius:"".concat(e.borderRadius,"px"),content:"''"}})),"&-disabled",R(e)),"&[disabled]",R(e)),"&-checked&-disabled",{"&:after":{position:"absolute",insetBlockStart:2,insetInlineEnd:2,width:0,height:0,border:"".concat(e.borderRadius+4,"px solid ").concat(e.colorTextDisabled),borderBlockEnd:"".concat(e.borderRadius+4,"px  solid transparent"),borderInlineStart:"".concat(e.borderRadius+4,"px  solid transparent"),borderStartEndRadius:"".concat(e.borderRadius,"px"),content:"''"}}),"&-lg",{width:440}),"&-sm",{width:212}),"&-cover",{paddingInline:e.paddingXXS,paddingBlock:e.paddingXXS,img:{width:"100%",height:"100%",overflow:"hidden",borderRadius:e.borderRadius}}),"&-content",{display:"flex",paddingInline:e.paddingSM,paddingBlock:e.padding}),(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)(j,"&-body",{paddingInline:e.paddingSM,paddingBlock:e.padding}),"&-avatar-header",{display:"flex",alignItems:"center"}),"&-avatar",{paddingInlineEnd:8}),"&-detail",{overflow:"hidden",width:"100%","> div:not(:last-child)":{marginBlockEnd:4}}),"&-header",{display:"flex",alignItems:"center",justifyContent:"space-between",lineHeight:e.lineHeight,"&-left":{display:"flex",alignItems:"center",gap:e.sizeSM}}),"&-title",{overflow:"hidden",color:e.colorTextHeading,fontWeight:"500",fontSize:e.fontSize,whiteSpace:"nowrap",textOverflow:"ellipsis",display:"flex",alignItems:"center",justifyContent:"space-between"}),"&-description",{color:e.colorTextSecondary}),"&:not(".concat(e.componentCls,"-disabled)"),{"&:hover":{borderColor:e.colorPrimary}})))};function je(D){return(0,H.Xj)("CheckCard",function(e){var j=(0,o.Z)((0,o.Z)({},e),{},{componentCls:".".concat(D)});return[ye(j)]})}var Ee=["prefixCls","className","avatar","title","description","cover","extra","style"],be=function(e){var j=(0,f.Z)(e.defaultChecked||!1,{value:e.checked,onChange:e.onChange}),g=(0,M.Z)(j,2),ne=g[0],$=g[1],t=(0,h.useContext)(ee),re=(0,h.useContext)(p.ZP.ConfigContext),te=re.getPrefixCls,ie=function(v){var y,N;e==null||(y=e.onClick)===null||y===void 0||y.call(e,v);var Pe=!ne;t==null||(N=t.toggleOption)===null||N===void 0||N.call(t,{value:e.value}),$==null||$(Pe)},K=function(v){return v==="large"?"lg":v==="small"?"sm":""};(0,h.useEffect)(function(){var E;return t==null||(E=t.registerValue)===null||E===void 0||E.call(t,e.value),function(){var v;return t==null||(v=t.cancelValue)===null||v===void 0?void 0:v.call(t,e.value)}},[e.value]);var le=function(v,y){return(0,a.jsx)("div",{className:"".concat(v,"-cover"),children:typeof y=="string"?(0,a.jsx)("img",{src:y,alt:"checkcard"}):y})},ge=e.prefixCls,Ze=e.className,z=e.avatar,B=e.title,A=e.description,V=e.cover,w=e.extra,de=e.style,me=de===void 0?{}:de,ce=(0,P.Z)(e,Ee),u=(0,o.Z)({},ce),i=te("pro-checkcard",ge),G=je(i),he=G.wrapSSR,Z=G.hashId;u.checked=ne;var oe=!1;if(t){var J;u.disabled=e.disabled||t.disabled,u.loading=e.loading||t.loading,u.bordered=e.bordered||t.bordered,oe=t.multiple;var Ce=t.multiple?(J=t.value)===null||J===void 0?void 0:J.includes(e.value):t.value===e.value;u.checked=u.loading?!1:Ce,u.size=e.size||t.size}var m=u.disabled,l=m===void 0?!1:m,c=u.size,S=u.loading,I=u.bordered,k=I===void 0?!0:I,se=u.checked,Q=K(c),xe=s()(i,Ze,Z,(0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)((0,r.Z)({},"".concat(i,"-loading"),S),"".concat(i,"-").concat(Q),Q),"".concat(i,"-checked"),se),"".concat(i,"-multiple"),oe),"".concat(i,"-disabled"),l),"".concat(i,"-bordered"),k),"".concat(i,"-ghost"),e.ghost)),T=(0,h.useMemo)(function(){if(S)return(0,a.jsx)(F,{prefixCls:i||""});if(V)return le(i||"",V);var E=z?(0,a.jsx)("div",{className:"".concat(i,"-avatar ").concat(Z).trim(),children:typeof z=="string"?(0,a.jsx)(d.Z,{size:48,shape:"square",src:z}):z}):null,v=(B!=null?B:w)!=null&&(0,a.jsxs)("div",{className:"".concat(i,"-header ").concat(Z).trim(),children:[(0,a.jsxs)("div",{className:"".concat(i,"-header-left ").concat(Z).trim(),children:[(0,a.jsx)("div",{className:"".concat(i,"-title ").concat(Z).trim(),children:B}),e.subTitle?(0,a.jsx)("div",{className:"".concat(i,"-subTitle ").concat(Z).trim(),children:e.subTitle}):null]}),w&&(0,a.jsx)("div",{className:"".concat(i,"-extra ").concat(Z).trim(),children:w})]}),y=A?(0,a.jsx)("div",{className:"".concat(i,"-description ").concat(Z).trim(),children:A}):null,N=s()("".concat(i,"-content"),Z,(0,r.Z)({},"".concat(i,"-avatar-header"),E&&v&&!y));return(0,a.jsxs)("div",{className:N,children:[E,v||y?(0,a.jsxs)("div",{className:"".concat(i,"-detail ").concat(Z).trim(),children:[v,y]}):null]})},[z,S,V,A,w,Z,i,e.subTitle,B]);return he((0,a.jsxs)("div",{className:xe,style:me,onClick:function(v){!S&&!l&&ie(v)},onMouseEnter:e.onMouseEnter,children:[T,e.children?(0,a.jsx)("div",{className:s()("".concat(i,"-body")),style:e.bodyStyle,children:e.children}):null,e.actions?(0,a.jsx)(U.Z,{actions:e.actions,prefixCls:i}):null]}))};be.Group=ue;var fe=be},15645:function(L,C,n){var r=n(92336),o=n(44194),P=n(68154),M=n(18258),f=n(91355);function p(d,x){x===void 0&&(x={});var s=d!=null?d:{},h=x.defaultValue,U=x.defaultValuePropName,Y=U===void 0?"defaultValue":U,O=x.valuePropName,b=O===void 0?"value":O,_=x.trigger,a=_===void 0?"onChange":_,q=s[b],F=Object.prototype.hasOwnProperty.call(s,b),ee=(0,o.useMemo)(function(){return F?q:Object.prototype.hasOwnProperty.call(s,Y)?s[Y]:h},[]),W=(0,o.useRef)(ee);F&&(W.current=q);var ue=(0,f.Z)();function ve(H){for(var X=[],R=1;R<arguments.length;R++)X[R-1]=arguments[R];var ae=(0,P.mf)(H)?H(W.current):H;F||(W.current=ae,ue()),s[a]&&s[a].apply(s,(0,r.__spreadArray)([ae],(0,r.__read)(X),!1))}return[W.current,(0,M.Z)(ve)]}C.Z=p},18258:function(L,C,n){var r=n(44194),o=n(68154),P=n(29836);function M(f){P.Z&&((0,o.mf)(f)||console.error("useMemoizedFn expected parameter is a function, got ".concat(typeof f)));var p=(0,r.useRef)(f);p.current=(0,r.useMemo)(function(){return f},[f]);var d=(0,r.useRef)();return d.current||(d.current=function(){for(var x=[],s=0;s<arguments.length;s++)x[s]=arguments[s];return p.current.apply(this,x)}),d.current}C.Z=M},91355:function(L,C,n){var r=n(92336),o=n(44194),P=function(){var M=(0,r.__read)((0,o.useState)({}),2),f=M[1];return(0,o.useCallback)(function(){return f({})},[])};C.Z=P},68154:function(L,C,n){n.d(C,{mf:function(){return o}});var r=function(d){return d!==null&&typeof d=="object"},o=function(d){return typeof d=="function"},P=function(d){return typeof d=="string"},M=function(d){return typeof d=="boolean"},f=function(d){return typeof d=="number"},p=function(d){return typeof d=="undefined"}},29836:function(L,C){var n=!1;C.Z=n}}]);
