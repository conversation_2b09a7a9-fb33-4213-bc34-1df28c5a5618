!(function(){"use strict";var m1=Object.defineProperty,C1=Object.defineProperties;var S1=Object.getOwnPropertyDescriptors;var s1=Object.getOwnPropertySymbols;var T1=Object.prototype.hasOwnProperty,x1=Object.prototype.propertyIsEnumerable;var i1=(X,v,k)=>v in X?m1(X,v,{enumerable:!0,configurable:!0,writable:!0,value:k}):X[v]=k,kt=(X,v)=>{for(var k in v||(v={}))T1.call(v,k)&&i1(X,k,v[k]);if(s1)for(var k of s1(v))x1.call(v,k)&&i1(X,k,v[k]);return X},gt=(X,v)=>C1(X,S1(v));var r1=(X,v,k)=>new Promise(($,ee)=>{var ce=R=>{try{I(k.next(R))}catch(se){ee(se)}},ue=R=>{try{I(k.throw(R))}catch(se){ee(se)}},I=R=>R.done?$(R.value):Promise.resolve(R.value).then(ce,ue);I((k=k.apply(X,v)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[9909],{74523:function(X,v,k){var $=k(50834),ee=k(38456);const ce=(ue,I)=>$.Z.lang.round(ee.Z.parse(ue)[I]);v.Z=ce},3027:function(X,v,k){k.d(v,{q:function(){return ce}});var $=k(29134),ee=k(69471),ce=(0,$.eW)((ue,I)=>{let R;return I==="sandbox"&&(R=(0,ee.Ys)("#i"+ue)),(I==="sandbox"?(0,ee.Ys)(R.nodes()[0].contentDocument.body):(0,ee.Ys)("body")).select(`[id="${ue}"]`)},"getDiagramElement")},70982:function(X,v,k){k.d(v,{G:function(){return ee}});var $=k(29134),ee=(0,$.eW)(()=>`
  /* Font Awesome icon styling - consolidated */
  .label-icon {
    display: inline-block;
    height: 1em;
    overflow: visible;
    vertical-align: -0.125em;
  }
  
  .node .label-icon path {
    fill: currentColor;
    stroke: revert;
    stroke-width: revert;
  }
`,"getIconStyles")},8031:function(X,v,k){k.d(v,{j:function(){return ee}});var $=k(29134),ee=(0,$.eW)((I,R,se,he)=>{I.attr("class",se);const{width:Re,height:ut,x:At,y:Se}=ce(I,R);(0,$.v2)(I,ut,Re,he);const h=ue(At,Se,Re,ut,R);I.attr("viewBox",h),$.cM.debug(`viewBox configured: ${h} with padding: ${R}`)},"setupViewPortForSVG"),ce=(0,$.eW)((I,R)=>{var he;const se=((he=I.node())==null?void 0:he.getBBox())||{width:0,height:0,x:0,y:0};return{width:se.width+R*2,height:se.height+R*2,x:se.x,y:se.y}},"calculateDimensionsWithPadding"),ue=(0,$.eW)((I,R,se,he,Re)=>`${I-Re} ${R-Re} ${se} ${he}`,"createViewBox")},29909:function(X,v,k){var we;k.d(v,{diagram:function(){return k1}});var $=k(70982),ee=k(29367),ce=k(3027),ue=k(8031),I=k(23064),R=k(78933),se=k(61150),he=k(52387),Re=k(50538),ut=k(57956),At=k(70919),Se=k(50854),h=k(29134),Be=k(69471),a1=k(74523),n1=k(85712),u1="flowchart-",o1=(we=class{constructor(){this.vertexCounter=0,this.config=(0,h.nV)(),this.vertices=new Map,this.edges=[],this.classes=new Map,this.subGraphs=[],this.subGraphLookup=new Map,this.tooltips=new Map,this.subCount=0,this.firstGraphFlag=!0,this.secCount=-1,this.posCrossRef=[],this.funs=[],this.setAccTitle=h.GN,this.setAccDescription=h.U$,this.setDiagramTitle=h.g2,this.getAccTitle=h.eu,this.getAccDescription=h.Mx,this.getDiagramTitle=h.Kr,this.funs.push(this.setupToolTips.bind(this)),this.addVertex=this.addVertex.bind(this),this.firstGraph=this.firstGraph.bind(this),this.setDirection=this.setDirection.bind(this),this.addSubGraph=this.addSubGraph.bind(this),this.addLink=this.addLink.bind(this),this.setLink=this.setLink.bind(this),this.updateLink=this.updateLink.bind(this),this.addClass=this.addClass.bind(this),this.setClass=this.setClass.bind(this),this.destructLink=this.destructLink.bind(this),this.setClickEvent=this.setClickEvent.bind(this),this.setTooltip=this.setTooltip.bind(this),this.updateLinkInterpolate=this.updateLinkInterpolate.bind(this),this.setClickFun=this.setClickFun.bind(this),this.bindFunctions=this.bindFunctions.bind(this),this.lex={firstGraph:this.firstGraph.bind(this)},this.clear(),this.setGen("gen-2")}sanitizeText(i){return h.SY.sanitizeText(i,this.config)}lookUpDomId(i){for(const a of this.vertices.values())if(a.id===i)return a.domId;return i}addVertex(i,a,n,u,o,p,c={},A){var j,x;if(!i||i.trim().length===0)return;let r;if(A!==void 0){let f;A.includes(`
`)?f=A+`
`:f=`{
`+A+`
}`,r=(0,ee.z)(f,{schema:ee.A})}const E=this.edges.find(f=>f.id===i);if(E){const f=r;(f==null?void 0:f.animate)!==void 0&&(E.animate=f.animate),(f==null?void 0:f.animation)!==void 0&&(E.animation=f.animation);return}let D,_=this.vertices.get(i);if(_===void 0&&(_={id:i,labelType:"text",domId:u1+i+"-"+this.vertexCounter,styles:[],classes:[]},this.vertices.set(i,_)),this.vertexCounter++,a!==void 0?(this.config=(0,h.nV)(),D=this.sanitizeText(a.text.trim()),_.labelType=a.type,D.startsWith('"')&&D.endsWith('"')&&(D=D.substring(1,D.length-1)),_.text=D):_.text===void 0&&(_.text=i),n!==void 0&&(_.type=n),u!=null&&u.forEach(f=>{_.styles.push(f)}),o!=null&&o.forEach(f=>{_.classes.push(f)}),p!==void 0&&(_.dir=p),_.props===void 0?_.props=c:c!==void 0&&Object.assign(_.props,c),r!==void 0){if(r.shape){if(r.shape!==r.shape.toLowerCase()||r.shape.includes("_"))throw new Error(`No such shape: ${r.shape}. Shape names should be lowercase.`);if(!(0,he.dW)(r.shape))throw new Error(`No such shape: ${r.shape}.`);_.type=r==null?void 0:r.shape}r!=null&&r.label&&(_.text=r==null?void 0:r.label),r!=null&&r.icon&&(_.icon=r==null?void 0:r.icon,!((j=r.label)!=null&&j.trim())&&_.text===i&&(_.text="")),r!=null&&r.form&&(_.form=r==null?void 0:r.form),r!=null&&r.pos&&(_.pos=r==null?void 0:r.pos),r!=null&&r.img&&(_.img=r==null?void 0:r.img,!((x=r.label)!=null&&x.trim())&&_.text===i&&(_.text="")),r!=null&&r.constraint&&(_.constraint=r.constraint),r.w&&(_.assetWidth=Number(r.w)),r.h&&(_.assetHeight=Number(r.h))}}addSingleLink(i,a,n,u){var r;const c={start:i,end:a,type:void 0,text:"",labelType:"text",classes:[],isUserDefinedId:!1,interpolate:this.edges.defaultInterpolate};h.cM.info("abc78 Got edge...",c);const A=n.text;if(A!==void 0&&(c.text=this.sanitizeText(A.text.trim()),c.text.startsWith('"')&&c.text.endsWith('"')&&(c.text=c.text.substring(1,c.text.length-1)),c.labelType=A.type),n!==void 0&&(c.type=n.type,c.stroke=n.stroke,c.length=n.length>10?10:n.length),u&&!this.edges.some(E=>E.id===u))c.id=u,c.isUserDefinedId=!0;else{const E=this.edges.filter(D=>D.start===c.start&&D.end===c.end);E.length===0?c.id=(0,Se.Ln)(c.start,c.end,{counter:0,prefix:"L"}):c.id=(0,Se.Ln)(c.start,c.end,{counter:E.length+1,prefix:"L"})}if(this.edges.length<((r=this.config.maxEdges)!=null?r:500))h.cM.info("Pushing edge..."),this.edges.push(c);else throw new Error(`Edge limit exceeded. ${this.edges.length} edges found, but the limit is ${this.config.maxEdges}.

Initialize mermaid with maxEdges set to a higher number to allow more edges.
You cannot set this config via configuration inside the diagram as it is a secure config.
You have to call mermaid.initialize.`)}isLinkData(i){return i!==null&&typeof i=="object"&&"id"in i&&typeof i.id=="string"}addLink(i,a,n){const u=this.isLinkData(n)?n.id.replace("@",""):void 0;h.cM.info("addLink",i,a,u);for(const o of i)for(const p of a){const c=o===i[i.length-1],A=p===a[0];c&&A?this.addSingleLink(o,p,n,u):this.addSingleLink(o,p,n,void 0)}}updateLinkInterpolate(i,a){i.forEach(n=>{n==="default"?this.edges.defaultInterpolate=a:this.edges[n].interpolate=a})}updateLink(i,a){i.forEach(n=>{var u,o,p,c,A,r,E;if(typeof n=="number"&&n>=this.edges.length)throw new Error(`The index ${n} for linkStyle is out of bounds. Valid indices for linkStyle are between 0 and ${this.edges.length-1}. (Help: Ensure that the index is within the range of existing edges.)`);n==="default"?this.edges.defaultStyle=a:(this.edges[n].style=a,((p=(o=(u=this.edges[n])==null?void 0:u.style)==null?void 0:o.length)!=null?p:0)>0&&!((A=(c=this.edges[n])==null?void 0:c.style)!=null&&A.some(D=>D==null?void 0:D.startsWith("fill")))&&((E=(r=this.edges[n])==null?void 0:r.style)==null||E.push("fill:none")))})}addClass(i,a){const n=a.join().replace(/\\,/g,"\xA7\xA7\xA7").replace(/,/g,";").replace(/§§§/g,",").split(";");i.split(",").forEach(u=>{let o=this.classes.get(u);o===void 0&&(o={id:u,styles:[],textStyles:[]},this.classes.set(u,o)),n!=null&&n.forEach(p=>{if(/color/.exec(p)){const c=p.replace("fill","bgFill");o.textStyles.push(c)}o.styles.push(p)})})}setDirection(i){this.direction=i,/.*</.exec(this.direction)&&(this.direction="RL"),/.*\^/.exec(this.direction)&&(this.direction="BT"),/.*>/.exec(this.direction)&&(this.direction="LR"),/.*v/.exec(this.direction)&&(this.direction="TB"),this.direction==="TD"&&(this.direction="TB")}setClass(i,a){for(const n of i.split(",")){const u=this.vertices.get(n);u&&u.classes.push(a);const o=this.edges.find(c=>c.id===n);o&&o.classes.push(a);const p=this.subGraphLookup.get(n);p&&p.classes.push(a)}}setTooltip(i,a){if(a!==void 0){a=this.sanitizeText(a);for(const n of i.split(","))this.tooltips.set(this.version==="gen-1"?this.lookUpDomId(n):n,a)}}setClickFun(i,a,n){const u=this.lookUpDomId(i);if((0,h.nV)().securityLevel!=="loose"||a===void 0)return;let o=[];if(typeof n=="string"){o=n.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let c=0;c<o.length;c++){let A=o[c].trim();A.startsWith('"')&&A.endsWith('"')&&(A=A.substr(1,A.length-2)),o[c]=A}}o.length===0&&o.push(i);const p=this.vertices.get(i);p&&(p.haveCallback=!0,this.funs.push(()=>{const c=document.querySelector(`[id="${u}"]`);c!==null&&c.addEventListener("click",()=>{Se.w8.runFunc(a,...o)},!1)}))}setLink(i,a,n){i.split(",").forEach(u=>{const o=this.vertices.get(u);o!==void 0&&(o.link=Se.w8.formatUrl(a,this.config),o.linkTarget=n)}),this.setClass(i,"clickable")}getTooltip(i){return this.tooltips.get(i)}setClickEvent(i,a,n){i.split(",").forEach(u=>{this.setClickFun(u,a,n)}),this.setClass(i,"clickable")}bindFunctions(i){this.funs.forEach(a=>{a(i)})}getDirection(){var i;return(i=this.direction)==null?void 0:i.trim()}getVertices(){return this.vertices}getEdges(){return this.edges}getClasses(){return this.classes}setupToolTips(i){let a=(0,Be.Ys)(".mermaidTooltip");(a._groups||a)[0][0]===null&&(a=(0,Be.Ys)("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),(0,Be.Ys)(i).select("svg").selectAll("g.node").on("mouseover",o=>{var r;const p=(0,Be.Ys)(o.currentTarget);if(p.attr("title")===null)return;const A=(r=o.currentTarget)==null?void 0:r.getBoundingClientRect();a.transition().duration(200).style("opacity",".9"),a.text(p.attr("title")).style("left",window.scrollX+A.left+(A.right-A.left)/2+"px").style("top",window.scrollY+A.bottom+"px"),a.html(a.html().replace(/&lt;br\/&gt;/g,"<br/>")),p.classed("hover",!0)}).on("mouseout",o=>{a.transition().duration(500).style("opacity",0),(0,Be.Ys)(o.currentTarget).classed("hover",!1)})}clear(i="gen-2"){this.vertices=new Map,this.classes=new Map,this.edges=[],this.funs=[this.setupToolTips.bind(this)],this.subGraphs=[],this.subGraphLookup=new Map,this.subCount=0,this.tooltips=new Map,this.firstGraphFlag=!0,this.version=i,this.config=(0,h.nV)(),(0,h.ZH)()}setGen(i){this.version=i||"gen-2"}defaultStyle(){return"fill:#ffa;stroke: #f66; stroke-width: 3px; stroke-dasharray: 5, 5;fill:#ffa;stroke: #666;"}addSubGraph(i,a,n){var _,j,x;let u=i.text.trim(),o=n.text;i===n&&/\s/.exec(n.text)&&(u=void 0);const c=(0,h.eW)(f=>{const Z={boolean:{},number:{},string:{}},Q=[];let oe;return{nodeList:f.filter(function(H){const V=typeof H;return H.stmt&&H.stmt==="dir"?(oe=H.value,!1):H.trim()===""?!1:V in Z?Z[V].hasOwnProperty(H)?!1:Z[V][H]=!0:Q.includes(H)?!1:Q.push(H)}),dir:oe}},"uniq")(a.flat()),A=c.nodeList;let r=c.dir;const E=(_=(0,h.nV)().flowchart)!=null?_:{};if(r=r!=null?r:E.inheritDir&&(x=(j=this.getDirection())!=null?j:(0,h.nV)().direction)!=null?x:void 0,this.version==="gen-1")for(let f=0;f<A.length;f++)A[f]=this.lookUpDomId(A[f]);u=u!=null?u:"subGraph"+this.subCount,o=o||"",o=this.sanitizeText(o),this.subCount=this.subCount+1;const D={id:u,nodes:A,title:o.trim(),classes:[],dir:r,labelType:n.type};return h.cM.info("Adding",D.id,D.nodes,D.dir),D.nodes=this.makeUniq(D,this.subGraphs).nodes,this.subGraphs.push(D),this.subGraphLookup.set(u,D),u}getPosForId(i){for(const[a,n]of this.subGraphs.entries())if(n.id===i)return a;return-1}indexNodes2(i,a){const n=this.subGraphs[a].nodes;if(this.secCount=this.secCount+1,this.secCount>2e3)return{result:!1,count:0};if(this.posCrossRef[this.secCount]=a,this.subGraphs[a].id===i)return{result:!0,count:0};let u=0,o=1;for(;u<n.length;){const p=this.getPosForId(n[u]);if(p>=0){const c=this.indexNodes2(i,p);if(c.result)return{result:!0,count:o+c.count};o=o+c.count}u=u+1}return{result:!1,count:o}}getDepthFirstPos(i){return this.posCrossRef[i]}indexNodes(){this.secCount=-1,this.subGraphs.length>0&&this.indexNodes2("none",this.subGraphs.length-1)}getSubGraphs(){return this.subGraphs}firstGraph(){return this.firstGraphFlag?(this.firstGraphFlag=!1,!0):!1}destructStartLink(i){let a=i.trim(),n="arrow_open";switch(a[0]){case"<":n="arrow_point",a=a.slice(1);break;case"x":n="arrow_cross",a=a.slice(1);break;case"o":n="arrow_circle",a=a.slice(1);break}let u="normal";return a.includes("=")&&(u="thick"),a.includes(".")&&(u="dotted"),{type:n,stroke:u}}countChar(i,a){const n=a.length;let u=0;for(let o=0;o<n;++o)a[o]===i&&++u;return u}destructEndLink(i){const a=i.trim();let n=a.slice(0,-1),u="arrow_open";switch(a.slice(-1)){case"x":u="arrow_cross",a.startsWith("x")&&(u="double_"+u,n=n.slice(1));break;case">":u="arrow_point",a.startsWith("<")&&(u="double_"+u,n=n.slice(1));break;case"o":u="arrow_circle",a.startsWith("o")&&(u="double_"+u,n=n.slice(1));break}let o="normal",p=n.length-1;n.startsWith("=")&&(o="thick"),n.startsWith("~")&&(o="invisible");const c=this.countChar(".",n);return c&&(o="dotted",p=c),{type:u,stroke:o,length:p}}destructLink(i,a){const n=this.destructEndLink(i);let u;if(a){if(u=this.destructStartLink(a),u.stroke!==n.stroke)return{type:"INVALID",stroke:"INVALID"};if(u.type==="arrow_open")u.type=n.type;else{if(u.type!==n.type)return{type:"INVALID",stroke:"INVALID"};u.type="double_"+u.type}return u.type==="double_arrow"&&(u.type="double_arrow_point"),u.length=n.length,u}return n}exists(i,a){for(const n of i)if(n.nodes.includes(a))return!0;return!1}makeUniq(i,a){const n=[];return i.nodes.forEach((u,o)=>{this.exists(a,u)||n.push(i.nodes[o])}),{nodes:n}}getTypeFromVertex(i){if(i.img)return"imageSquare";if(i.icon)return i.form==="circle"?"iconCircle":i.form==="square"?"iconSquare":i.form==="rounded"?"iconRounded":"icon";switch(i.type){case"square":case void 0:return"squareRect";case"round":return"roundedRect";case"ellipse":return"ellipse";default:return i.type}}findNode(i,a){return i.find(n=>n.id===a)}destructEdgeType(i){let a="none",n="arrow_point";switch(i){case"arrow_point":case"arrow_circle":case"arrow_cross":n=i;break;case"double_arrow_point":case"double_arrow_circle":case"double_arrow_cross":a=i.replace("double_",""),n=a;break}return{arrowTypeStart:a,arrowTypeEnd:n}}addNodeFromVertex(i,a,n,u,o,p){var E,D;const c=n.get(i.id),A=(E=u.get(i.id))!=null?E:!1,r=this.findNode(a,i.id);if(r)r.cssStyles=i.styles,r.cssCompiledStyles=this.getCompiledStyles(i.classes),r.cssClasses=i.classes.join(" ");else{const _={id:i.id,label:i.text,labelStyle:"",parentId:c,padding:((D=o.flowchart)==null?void 0:D.padding)||8,cssStyles:i.styles,cssCompiledStyles:this.getCompiledStyles(["default","node",...i.classes]),cssClasses:"default "+i.classes.join(" "),dir:i.dir,domId:i.domId,look:p,link:i.link,linkTarget:i.linkTarget,tooltip:this.getTooltip(i.id),icon:i.icon,pos:i.pos,img:i.img,assetWidth:i.assetWidth,assetHeight:i.assetHeight,constraint:i.constraint};A?a.push(gt(kt({},_),{isGroup:!0,shape:"rect"})):a.push(gt(kt({},_),{isGroup:!1,shape:this.getTypeFromVertex(i)}))}}getCompiledStyles(i){var n,u;let a=[];for(const o of i){const p=this.classes.get(o);p!=null&&p.styles&&(a=[...a,...(n=p.styles)!=null?n:[]].map(c=>c.trim())),p!=null&&p.textStyles&&(a=[...a,...(u=p.textStyles)!=null?u:[]].map(c=>c.trim()))}return a}getData(){const i=(0,h.nV)(),a=[],n=[],u=this.getSubGraphs(),o=new Map,p=new Map;for(let r=u.length-1;r>=0;r--){const E=u[r];E.nodes.length>0&&p.set(E.id,!0);for(const D of E.nodes)o.set(D,E.id)}for(let r=u.length-1;r>=0;r--){const E=u[r];a.push({id:E.id,label:E.title,labelStyle:"",parentId:o.get(E.id),padding:8,cssCompiledStyles:this.getCompiledStyles(E.classes),cssClasses:E.classes.join(" "),shape:"rect",dir:E.dir,isGroup:!0,look:i.look})}this.getVertices().forEach(r=>{this.addNodeFromVertex(r,a,o,p,i,i.look||"classic")});const A=this.getEdges();return A.forEach((r,E)=>{var f,Z,Q;const{arrowTypeStart:D,arrowTypeEnd:_}=this.destructEdgeType(r.type),j=[...(f=A.defaultStyle)!=null?f:[]];r.style&&j.push(...r.style);const x={id:(0,Se.Ln)(r.start,r.end,{counter:E,prefix:"L"},r.id),isUserDefinedId:r.isUserDefinedId,start:r.start,end:r.end,type:(Z=r.type)!=null?Z:"normal",label:r.text,labelpos:"c",thickness:r.stroke,minlen:r.length,classes:(r==null?void 0:r.stroke)==="invisible"?"":"edge-thickness-normal edge-pattern-solid flowchart-link",arrowTypeStart:(r==null?void 0:r.stroke)==="invisible"||(r==null?void 0:r.type)==="arrow_open"?"none":D,arrowTypeEnd:(r==null?void 0:r.stroke)==="invisible"||(r==null?void 0:r.type)==="arrow_open"?"none":_,arrowheadStyle:"fill: #333",cssCompiledStyles:this.getCompiledStyles(r.classes),labelStyle:j,style:j,pattern:r.stroke,look:i.look,animate:r.animate,animation:r.animation,curve:r.interpolate||this.edges.defaultInterpolate||((Q=i.flowchart)==null?void 0:Q.curve)};n.push(x)}),{nodes:a,edges:n,other:{},config:i}}defaultConfig(){return h.Fy.flowchart}},(0,h.eW)(we,"FlowDB"),we),l1=(0,h.eW)(function(s,i){return i.db.getClasses()},"getClasses"),c1=(0,h.eW)(function(s,i,a,n){return r1(this,null,function*(){var j,x;h.cM.info("REF0:"),h.cM.info("Drawing state diagram (v2)",i);const{securityLevel:u,flowchart:o,layout:p}=(0,h.nV)();let c;u==="sandbox"&&(c=(0,Be.Ys)("#i"+i));const A=u==="sandbox"?c.nodes()[0].contentDocument:document;h.cM.debug("Before getData: ");const r=n.db.getData();h.cM.debug("Data: ",r);const E=(0,ce.q)(i,u),D=n.db.getDirection();r.type=n.type,r.layoutAlgorithm=(0,I._b)(p),r.layoutAlgorithm==="dagre"&&p==="elk"&&h.cM.warn("flowchart-elk was moved to an external package in Mermaid v11. Please refer [release notes](https://github.com/mermaid-js/mermaid/releases/tag/v11.0.0) for more details. This diagram will be rendered using `dagre` layout as a fallback."),r.direction=D,r.nodeSpacing=(o==null?void 0:o.nodeSpacing)||50,r.rankSpacing=(o==null?void 0:o.rankSpacing)||50,r.markers=["point","circle","cross"],r.diagramId=i,h.cM.debug("REF1:",r),yield(0,I.sY)(r,E);const _=(x=(j=r.config.flowchart)==null?void 0:j.diagramPadding)!=null?x:8;Se.w8.insertTitle(E,"flowchartTitleText",(o==null?void 0:o.titleTopMargin)||0,n.db.getDiagramTitle()),(0,ue.j)(E,_,"flowchart",(o==null?void 0:o.useMaxWidth)||!1);for(const f of r.nodes){const Z=(0,Be.Ys)(`#${i} [id="${f.id}"]`);if(!Z||!f.link)continue;const Q=A.createElementNS("http://www.w3.org/2000/svg","a");Q.setAttributeNS("http://www.w3.org/2000/svg","class",f.cssClasses),Q.setAttributeNS("http://www.w3.org/2000/svg","rel","noopener"),u==="sandbox"?Q.setAttributeNS("http://www.w3.org/2000/svg","target","_top"):f.linkTarget&&Q.setAttributeNS("http://www.w3.org/2000/svg","target",f.linkTarget);const oe=Z.insert(function(){return Q},":first-child"),Te=Z.select(".label-container");Te&&oe.append(function(){return Te.node()});const H=Z.select(".label");H&&oe.append(function(){return H.node()})}})},"draw"),h1={getClasses:l1,draw:c1},ot=function(){var s=(0,h.eW)(function(Fe,d,b,g){for(b=b||{},g=Fe.length;g--;b[Fe[g]]=d);return b},"o"),i=[1,4],a=[1,3],n=[1,5],u=[1,8,9,10,11,27,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],o=[2,2],p=[1,13],c=[1,14],A=[1,15],r=[1,16],E=[1,23],D=[1,25],_=[1,26],j=[1,27],x=[1,49],f=[1,48],Z=[1,29],Q=[1,30],oe=[1,31],Te=[1,32],H=[1,33],V=[1,44],P=[1,46],M=[1,42],N=[1,47],O=[1,43],W=[1,50],G=[1,45],U=[1,51],K=[1,52],qe=[1,34],$e=[1,35],et=[1,36],tt=[1,37],xe=[1,57],y=[1,8,9,10,11,27,32,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],de=[1,61],pe=[1,60],fe=[1,62],Pe=[8,9,11,75,77,78],Dt=[1,78],Me=[1,91],Ne=[1,96],Oe=[1,95],We=[1,92],Ge=[1,88],Ue=[1,94],Ke=[1,90],Ye=[1,97],je=[1,93],He=[1,98],ze=[1,89],ve=[8,9,10,11,40,75,77,78],z=[8,9,10,11,40,46,75,77,78],ie=[8,9,10,11,29,40,44,46,48,50,52,54,56,58,60,63,65,67,68,70,75,77,78,89,102,105,106,109,111,114,115,116],mt=[8,9,11,44,60,75,77,78,89,102,105,106,109,111,114,115,116],Xe=[44,60,89,102,105,106,109,111,114,115,116],Ct=[1,121],St=[1,122],st=[1,124],it=[1,123],Tt=[44,60,62,74,89,102,105,106,109,111,114,115,116],xt=[1,133],yt=[1,147],Ft=[1,148],Bt=[1,149],vt=[1,150],Lt=[1,135],Vt=[1,137],It=[1,141],Rt=[1,142],wt=[1,143],Pt=[1,144],Mt=[1,145],Nt=[1,146],Ot=[1,151],Wt=[1,152],Gt=[1,131],Ut=[1,132],Kt=[1,139],Yt=[1,134],jt=[1,138],Ht=[1,136],lt=[8,9,10,11,27,32,34,36,38,44,60,84,85,86,87,88,89,102,105,106,109,111,114,115,116,121,122,123,124],zt=[1,154],Xt=[1,156],L=[8,9,11],re=[8,9,10,11,14,44,60,89,105,106,109,111,114,115,116],m=[1,176],J=[1,172],q=[1,173],C=[1,177],S=[1,174],T=[1,175],Ze=[77,116,119],F=[8,9,10,11,12,14,27,29,32,44,60,75,84,85,86,87,88,89,90,105,109,111,114,115,116],Zt=[10,106],ye=[31,49,51,53,55,57,62,64,66,67,69,71,116,117,118],be=[1,247],ke=[1,245],ge=[1,249],Ae=[1,243],_e=[1,244],Ee=[1,246],De=[1,248],me=[1,250],Qe=[1,268],Qt=[8,9,11,106],le=[8,9,10,11,60,84,105,106,109,110,111,112],ct={trace:(0,h.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,graphConfig:4,document:5,line:6,statement:7,SEMI:8,NEWLINE:9,SPACE:10,EOF:11,GRAPH:12,NODIR:13,DIR:14,FirstStmtSeparator:15,ending:16,endToken:17,spaceList:18,spaceListNewline:19,vertexStatement:20,separator:21,styleStatement:22,linkStyleStatement:23,classDefStatement:24,classStatement:25,clickStatement:26,subgraph:27,textNoTags:28,SQS:29,text:30,SQE:31,end:32,direction:33,acc_title:34,acc_title_value:35,acc_descr:36,acc_descr_value:37,acc_descr_multiline_value:38,shapeData:39,SHAPE_DATA:40,link:41,node:42,styledVertex:43,AMP:44,vertex:45,STYLE_SEPARATOR:46,idString:47,DOUBLECIRCLESTART:48,DOUBLECIRCLEEND:49,PS:50,PE:51,"(-":52,"-)":53,STADIUMSTART:54,STADIUMEND:55,SUBROUTINESTART:56,SUBROUTINEEND:57,VERTEX_WITH_PROPS_START:58,"NODE_STRING[field]":59,COLON:60,"NODE_STRING[value]":61,PIPE:62,CYLINDERSTART:63,CYLINDEREND:64,DIAMOND_START:65,DIAMOND_STOP:66,TAGEND:67,TRAPSTART:68,TRAPEND:69,INVTRAPSTART:70,INVTRAPEND:71,linkStatement:72,arrowText:73,TESTSTR:74,START_LINK:75,edgeText:76,LINK:77,LINK_ID:78,edgeTextToken:79,STR:80,MD_STR:81,textToken:82,keywords:83,STYLE:84,LINKSTYLE:85,CLASSDEF:86,CLASS:87,CLICK:88,DOWN:89,UP:90,textNoTagsToken:91,stylesOpt:92,"idString[vertex]":93,"idString[class]":94,CALLBACKNAME:95,CALLBACKARGS:96,HREF:97,LINK_TARGET:98,"STR[link]":99,"STR[tooltip]":100,alphaNum:101,DEFAULT:102,numList:103,INTERPOLATE:104,NUM:105,COMMA:106,style:107,styleComponent:108,NODE_STRING:109,UNIT:110,BRKT:111,PCT:112,idStringToken:113,MINUS:114,MULT:115,UNICODE_TEXT:116,TEXT:117,TAGSTART:118,EDGE_TEXT:119,alphaNumToken:120,direction_tb:121,direction_bt:122,direction_rl:123,direction_lr:124,$accept:0,$end:1},terminals_:{2:"error",8:"SEMI",9:"NEWLINE",10:"SPACE",11:"EOF",12:"GRAPH",13:"NODIR",14:"DIR",27:"subgraph",29:"SQS",31:"SQE",32:"end",34:"acc_title",35:"acc_title_value",36:"acc_descr",37:"acc_descr_value",38:"acc_descr_multiline_value",40:"SHAPE_DATA",44:"AMP",46:"STYLE_SEPARATOR",48:"DOUBLECIRCLESTART",49:"DOUBLECIRCLEEND",50:"PS",51:"PE",52:"(-",53:"-)",54:"STADIUMSTART",55:"STADIUMEND",56:"SUBROUTINESTART",57:"SUBROUTINEEND",58:"VERTEX_WITH_PROPS_START",59:"NODE_STRING[field]",60:"COLON",61:"NODE_STRING[value]",62:"PIPE",63:"CYLINDERSTART",64:"CYLINDEREND",65:"DIAMOND_START",66:"DIAMOND_STOP",67:"TAGEND",68:"TRAPSTART",69:"TRAPEND",70:"INVTRAPSTART",71:"INVTRAPEND",74:"TESTSTR",75:"START_LINK",77:"LINK",78:"LINK_ID",80:"STR",81:"MD_STR",84:"STYLE",85:"LINKSTYLE",86:"CLASSDEF",87:"CLASS",88:"CLICK",89:"DOWN",90:"UP",93:"idString[vertex]",94:"idString[class]",95:"CALLBACKNAME",96:"CALLBACKARGS",97:"HREF",98:"LINK_TARGET",99:"STR[link]",100:"STR[tooltip]",102:"DEFAULT",104:"INTERPOLATE",105:"NUM",106:"COMMA",109:"NODE_STRING",110:"UNIT",111:"BRKT",112:"PCT",114:"MINUS",115:"MULT",116:"UNICODE_TEXT",117:"TEXT",118:"TAGSTART",119:"EDGE_TEXT",121:"direction_tb",122:"direction_bt",123:"direction_rl",124:"direction_lr"},productions_:[0,[3,2],[5,0],[5,2],[6,1],[6,1],[6,1],[6,1],[6,1],[4,2],[4,2],[4,2],[4,3],[16,2],[16,1],[17,1],[17,1],[17,1],[15,1],[15,1],[15,2],[19,2],[19,2],[19,1],[19,1],[18,2],[18,1],[7,2],[7,2],[7,2],[7,2],[7,2],[7,2],[7,9],[7,6],[7,4],[7,1],[7,2],[7,2],[7,1],[21,1],[21,1],[21,1],[39,2],[39,1],[20,4],[20,3],[20,4],[20,2],[20,2],[20,1],[42,1],[42,6],[42,5],[43,1],[43,3],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,8],[45,4],[45,4],[45,4],[45,6],[45,4],[45,4],[45,4],[45,4],[45,4],[45,1],[41,2],[41,3],[41,3],[41,1],[41,3],[41,4],[76,1],[76,2],[76,1],[76,1],[72,1],[72,2],[73,3],[30,1],[30,2],[30,1],[30,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[28,1],[28,2],[28,1],[28,1],[24,5],[25,5],[26,2],[26,4],[26,3],[26,5],[26,3],[26,5],[26,5],[26,7],[26,2],[26,4],[26,2],[26,4],[26,4],[26,6],[22,5],[23,5],[23,5],[23,9],[23,9],[23,7],[23,7],[103,1],[103,3],[92,1],[92,3],[107,1],[107,2],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[108,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[113,1],[82,1],[82,1],[82,1],[82,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[79,1],[79,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[120,1],[47,1],[47,2],[101,1],[101,2],[33,1],[33,1],[33,1],[33,1]],performAction:(0,h.eW)(function(d,b,g,l,B,e,Le){var t=e.length-1;switch(B){case 2:this.$=[];break;case 3:(!Array.isArray(e[t])||e[t].length>0)&&e[t-1].push(e[t]),this.$=e[t-1];break;case 4:case 183:this.$=e[t];break;case 11:l.setDirection("TB"),this.$="TB";break;case 12:l.setDirection(e[t-1]),this.$=e[t-1];break;case 27:this.$=e[t-1].nodes;break;case 28:case 29:case 30:case 31:case 32:this.$=[];break;case 33:this.$=l.addSubGraph(e[t-6],e[t-1],e[t-4]);break;case 34:this.$=l.addSubGraph(e[t-3],e[t-1],e[t-3]);break;case 35:this.$=l.addSubGraph(void 0,e[t-1],void 0);break;case 37:this.$=e[t].trim(),l.setAccTitle(this.$);break;case 38:case 39:this.$=e[t].trim(),l.setAccDescription(this.$);break;case 43:this.$=e[t-1]+e[t];break;case 44:this.$=e[t];break;case 45:l.addVertex(e[t-1][e[t-1].length-1],void 0,void 0,void 0,void 0,void 0,void 0,e[t]),l.addLink(e[t-3].stmt,e[t-1],e[t-2]),this.$={stmt:e[t-1],nodes:e[t-1].concat(e[t-3].nodes)};break;case 46:l.addLink(e[t-2].stmt,e[t],e[t-1]),this.$={stmt:e[t],nodes:e[t].concat(e[t-2].nodes)};break;case 47:l.addLink(e[t-3].stmt,e[t-1],e[t-2]),this.$={stmt:e[t-1],nodes:e[t-1].concat(e[t-3].nodes)};break;case 48:this.$={stmt:e[t-1],nodes:e[t-1]};break;case 49:l.addVertex(e[t-1][e[t-1].length-1],void 0,void 0,void 0,void 0,void 0,void 0,e[t]),this.$={stmt:e[t-1],nodes:e[t-1],shapeData:e[t]};break;case 50:this.$={stmt:e[t],nodes:e[t]};break;case 51:this.$=[e[t]];break;case 52:l.addVertex(e[t-5][e[t-5].length-1],void 0,void 0,void 0,void 0,void 0,void 0,e[t-4]),this.$=e[t-5].concat(e[t]);break;case 53:this.$=e[t-4].concat(e[t]);break;case 54:this.$=e[t];break;case 55:this.$=e[t-2],l.setClass(e[t-2],e[t]);break;case 56:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"square");break;case 57:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"doublecircle");break;case 58:this.$=e[t-5],l.addVertex(e[t-5],e[t-2],"circle");break;case 59:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"ellipse");break;case 60:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"stadium");break;case 61:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"subroutine");break;case 62:this.$=e[t-7],l.addVertex(e[t-7],e[t-1],"rect",void 0,void 0,void 0,Object.fromEntries([[e[t-5],e[t-3]]]));break;case 63:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"cylinder");break;case 64:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"round");break;case 65:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"diamond");break;case 66:this.$=e[t-5],l.addVertex(e[t-5],e[t-2],"hexagon");break;case 67:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"odd");break;case 68:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"trapezoid");break;case 69:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"inv_trapezoid");break;case 70:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"lean_right");break;case 71:this.$=e[t-3],l.addVertex(e[t-3],e[t-1],"lean_left");break;case 72:this.$=e[t],l.addVertex(e[t]);break;case 73:e[t-1].text=e[t],this.$=e[t-1];break;case 74:case 75:e[t-2].text=e[t-1],this.$=e[t-2];break;case 76:this.$=e[t];break;case 77:var w=l.destructLink(e[t],e[t-2]);this.$={type:w.type,stroke:w.stroke,length:w.length,text:e[t-1]};break;case 78:var w=l.destructLink(e[t],e[t-2]);this.$={type:w.type,stroke:w.stroke,length:w.length,text:e[t-1],id:e[t-3]};break;case 79:this.$={text:e[t],type:"text"};break;case 80:this.$={text:e[t-1].text+""+e[t],type:e[t-1].type};break;case 81:this.$={text:e[t],type:"string"};break;case 82:this.$={text:e[t],type:"markdown"};break;case 83:var w=l.destructLink(e[t]);this.$={type:w.type,stroke:w.stroke,length:w.length};break;case 84:var w=l.destructLink(e[t]);this.$={type:w.type,stroke:w.stroke,length:w.length,id:e[t-1]};break;case 85:this.$=e[t-1];break;case 86:this.$={text:e[t],type:"text"};break;case 87:this.$={text:e[t-1].text+""+e[t],type:e[t-1].type};break;case 88:this.$={text:e[t],type:"string"};break;case 89:case 104:this.$={text:e[t],type:"markdown"};break;case 101:this.$={text:e[t],type:"text"};break;case 102:this.$={text:e[t-1].text+""+e[t],type:e[t-1].type};break;case 103:this.$={text:e[t],type:"text"};break;case 105:this.$=e[t-4],l.addClass(e[t-2],e[t]);break;case 106:this.$=e[t-4],l.setClass(e[t-2],e[t]);break;case 107:case 115:this.$=e[t-1],l.setClickEvent(e[t-1],e[t]);break;case 108:case 116:this.$=e[t-3],l.setClickEvent(e[t-3],e[t-2]),l.setTooltip(e[t-3],e[t]);break;case 109:this.$=e[t-2],l.setClickEvent(e[t-2],e[t-1],e[t]);break;case 110:this.$=e[t-4],l.setClickEvent(e[t-4],e[t-3],e[t-2]),l.setTooltip(e[t-4],e[t]);break;case 111:this.$=e[t-2],l.setLink(e[t-2],e[t]);break;case 112:this.$=e[t-4],l.setLink(e[t-4],e[t-2]),l.setTooltip(e[t-4],e[t]);break;case 113:this.$=e[t-4],l.setLink(e[t-4],e[t-2],e[t]);break;case 114:this.$=e[t-6],l.setLink(e[t-6],e[t-4],e[t]),l.setTooltip(e[t-6],e[t-2]);break;case 117:this.$=e[t-1],l.setLink(e[t-1],e[t]);break;case 118:this.$=e[t-3],l.setLink(e[t-3],e[t-2]),l.setTooltip(e[t-3],e[t]);break;case 119:this.$=e[t-3],l.setLink(e[t-3],e[t-2],e[t]);break;case 120:this.$=e[t-5],l.setLink(e[t-5],e[t-4],e[t]),l.setTooltip(e[t-5],e[t-2]);break;case 121:this.$=e[t-4],l.addVertex(e[t-2],void 0,void 0,e[t]);break;case 122:this.$=e[t-4],l.updateLink([e[t-2]],e[t]);break;case 123:this.$=e[t-4],l.updateLink(e[t-2],e[t]);break;case 124:this.$=e[t-8],l.updateLinkInterpolate([e[t-6]],e[t-2]),l.updateLink([e[t-6]],e[t]);break;case 125:this.$=e[t-8],l.updateLinkInterpolate(e[t-6],e[t-2]),l.updateLink(e[t-6],e[t]);break;case 126:this.$=e[t-6],l.updateLinkInterpolate([e[t-4]],e[t]);break;case 127:this.$=e[t-6],l.updateLinkInterpolate(e[t-4],e[t]);break;case 128:case 130:this.$=[e[t]];break;case 129:case 131:e[t-2].push(e[t]),this.$=e[t-2];break;case 133:this.$=e[t-1]+e[t];break;case 181:this.$=e[t];break;case 182:this.$=e[t-1]+""+e[t];break;case 184:this.$=e[t-1]+""+e[t];break;case 185:this.$={stmt:"dir",value:"TB"};break;case 186:this.$={stmt:"dir",value:"BT"};break;case 187:this.$={stmt:"dir",value:"RL"};break;case 188:this.$={stmt:"dir",value:"LR"};break}},"anonymous"),table:[{3:1,4:2,9:i,10:a,12:n},{1:[3]},s(u,o,{5:6}),{4:7,9:i,10:a,12:n},{4:8,9:i,10:a,12:n},{13:[1,9],14:[1,10]},{1:[2,1],6:11,7:12,8:p,9:c,10:A,11:r,20:17,22:18,23:19,24:20,25:21,26:22,27:E,33:24,34:D,36:_,38:j,42:28,43:38,44:x,45:39,47:40,60:f,84:Z,85:Q,86:oe,87:Te,88:H,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K,121:qe,122:$e,123:et,124:tt},s(u,[2,9]),s(u,[2,10]),s(u,[2,11]),{8:[1,54],9:[1,55],10:xe,15:53,18:56},s(y,[2,3]),s(y,[2,4]),s(y,[2,5]),s(y,[2,6]),s(y,[2,7]),s(y,[2,8]),{8:de,9:pe,11:fe,21:58,41:59,72:63,75:[1,64],77:[1,66],78:[1,65]},{8:de,9:pe,11:fe,21:67},{8:de,9:pe,11:fe,21:68},{8:de,9:pe,11:fe,21:69},{8:de,9:pe,11:fe,21:70},{8:de,9:pe,11:fe,21:71},{8:de,9:pe,10:[1,72],11:fe,21:73},s(y,[2,36]),{35:[1,74]},{37:[1,75]},s(y,[2,39]),s(Pe,[2,50],{18:76,39:77,10:xe,40:Dt}),{10:[1,79]},{10:[1,80]},{10:[1,81]},{10:[1,82]},{14:Me,44:Ne,60:Oe,80:[1,86],89:We,95:[1,83],97:[1,84],101:85,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze,120:87},s(y,[2,185]),s(y,[2,186]),s(y,[2,187]),s(y,[2,188]),s(ve,[2,51]),s(ve,[2,54],{46:[1,99]}),s(z,[2,72],{113:112,29:[1,100],44:x,48:[1,101],50:[1,102],52:[1,103],54:[1,104],56:[1,105],58:[1,106],60:f,63:[1,107],65:[1,108],67:[1,109],68:[1,110],70:[1,111],89:V,102:P,105:M,106:N,109:O,111:W,114:G,115:U,116:K}),s(ie,[2,181]),s(ie,[2,142]),s(ie,[2,143]),s(ie,[2,144]),s(ie,[2,145]),s(ie,[2,146]),s(ie,[2,147]),s(ie,[2,148]),s(ie,[2,149]),s(ie,[2,150]),s(ie,[2,151]),s(ie,[2,152]),s(u,[2,12]),s(u,[2,18]),s(u,[2,19]),{9:[1,113]},s(mt,[2,26],{18:114,10:xe}),s(y,[2,27]),{42:115,43:38,44:x,45:39,47:40,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},s(y,[2,40]),s(y,[2,41]),s(y,[2,42]),s(Xe,[2,76],{73:116,62:[1,118],74:[1,117]}),{76:119,79:120,80:Ct,81:St,116:st,119:it},{75:[1,125],77:[1,126]},s(Tt,[2,83]),s(y,[2,28]),s(y,[2,29]),s(y,[2,30]),s(y,[2,31]),s(y,[2,32]),{10:xt,12:yt,14:Ft,27:Bt,28:127,32:vt,44:Lt,60:Vt,75:It,80:[1,129],81:[1,130],83:140,84:Rt,85:wt,86:Pt,87:Mt,88:Nt,89:Ot,90:Wt,91:128,105:Gt,109:Ut,111:Kt,114:Yt,115:jt,116:Ht},s(lt,o,{5:153}),s(y,[2,37]),s(y,[2,38]),s(Pe,[2,48],{44:zt}),s(Pe,[2,49],{18:155,10:xe,40:Xt}),s(ve,[2,44]),{44:x,47:157,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},{102:[1,158],103:159,105:[1,160]},{44:x,47:161,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},{44:x,47:162,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},s(L,[2,107],{10:[1,163],96:[1,164]}),{80:[1,165]},s(L,[2,115],{120:167,10:[1,166],14:Me,44:Ne,60:Oe,89:We,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze}),s(L,[2,117],{10:[1,168]}),s(re,[2,183]),s(re,[2,170]),s(re,[2,171]),s(re,[2,172]),s(re,[2,173]),s(re,[2,174]),s(re,[2,175]),s(re,[2,176]),s(re,[2,177]),s(re,[2,178]),s(re,[2,179]),s(re,[2,180]),{44:x,47:169,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},{30:170,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:178,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:180,50:[1,179],67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:181,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:182,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:183,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{109:[1,184]},{30:185,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:186,65:[1,187],67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:188,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:189,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{30:190,67:m,80:J,81:q,82:171,116:C,117:S,118:T},s(ie,[2,182]),s(u,[2,20]),s(mt,[2,25]),s(Pe,[2,46],{39:191,18:192,10:xe,40:Dt}),s(Xe,[2,73],{10:[1,193]}),{10:[1,194]},{30:195,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{77:[1,196],79:197,116:st,119:it},s(Ze,[2,79]),s(Ze,[2,81]),s(Ze,[2,82]),s(Ze,[2,168]),s(Ze,[2,169]),{76:198,79:120,80:Ct,81:St,116:st,119:it},s(Tt,[2,84]),{8:de,9:pe,10:xt,11:fe,12:yt,14:Ft,21:200,27:Bt,29:[1,199],32:vt,44:Lt,60:Vt,75:It,83:140,84:Rt,85:wt,86:Pt,87:Mt,88:Nt,89:Ot,90:Wt,91:201,105:Gt,109:Ut,111:Kt,114:Yt,115:jt,116:Ht},s(F,[2,101]),s(F,[2,103]),s(F,[2,104]),s(F,[2,157]),s(F,[2,158]),s(F,[2,159]),s(F,[2,160]),s(F,[2,161]),s(F,[2,162]),s(F,[2,163]),s(F,[2,164]),s(F,[2,165]),s(F,[2,166]),s(F,[2,167]),s(F,[2,90]),s(F,[2,91]),s(F,[2,92]),s(F,[2,93]),s(F,[2,94]),s(F,[2,95]),s(F,[2,96]),s(F,[2,97]),s(F,[2,98]),s(F,[2,99]),s(F,[2,100]),{6:11,7:12,8:p,9:c,10:A,11:r,20:17,22:18,23:19,24:20,25:21,26:22,27:E,32:[1,202],33:24,34:D,36:_,38:j,42:28,43:38,44:x,45:39,47:40,60:f,84:Z,85:Q,86:oe,87:Te,88:H,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K,121:qe,122:$e,123:et,124:tt},{10:xe,18:203},{44:[1,204]},s(ve,[2,43]),{10:[1,205],44:x,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:112,114:G,115:U,116:K},{10:[1,206]},{10:[1,207],106:[1,208]},s(Zt,[2,128]),{10:[1,209],44:x,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:112,114:G,115:U,116:K},{10:[1,210],44:x,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:112,114:G,115:U,116:K},{80:[1,211]},s(L,[2,109],{10:[1,212]}),s(L,[2,111],{10:[1,213]}),{80:[1,214]},s(re,[2,184]),{80:[1,215],98:[1,216]},s(ve,[2,55],{113:112,44:x,60:f,89:V,102:P,105:M,106:N,109:O,111:W,114:G,115:U,116:K}),{31:[1,217],67:m,82:218,116:C,117:S,118:T},s(ye,[2,86]),s(ye,[2,88]),s(ye,[2,89]),s(ye,[2,153]),s(ye,[2,154]),s(ye,[2,155]),s(ye,[2,156]),{49:[1,219],67:m,82:218,116:C,117:S,118:T},{30:220,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{51:[1,221],67:m,82:218,116:C,117:S,118:T},{53:[1,222],67:m,82:218,116:C,117:S,118:T},{55:[1,223],67:m,82:218,116:C,117:S,118:T},{57:[1,224],67:m,82:218,116:C,117:S,118:T},{60:[1,225]},{64:[1,226],67:m,82:218,116:C,117:S,118:T},{66:[1,227],67:m,82:218,116:C,117:S,118:T},{30:228,67:m,80:J,81:q,82:171,116:C,117:S,118:T},{31:[1,229],67:m,82:218,116:C,117:S,118:T},{67:m,69:[1,230],71:[1,231],82:218,116:C,117:S,118:T},{67:m,69:[1,233],71:[1,232],82:218,116:C,117:S,118:T},s(Pe,[2,45],{18:155,10:xe,40:Xt}),s(Pe,[2,47],{44:zt}),s(Xe,[2,75]),s(Xe,[2,74]),{62:[1,234],67:m,82:218,116:C,117:S,118:T},s(Xe,[2,77]),s(Ze,[2,80]),{77:[1,235],79:197,116:st,119:it},{30:236,67:m,80:J,81:q,82:171,116:C,117:S,118:T},s(lt,o,{5:237}),s(F,[2,102]),s(y,[2,35]),{43:238,44:x,45:39,47:40,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},{10:xe,18:239},{10:be,60:ke,84:ge,92:240,105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},{10:be,60:ke,84:ge,92:251,104:[1,252],105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},{10:be,60:ke,84:ge,92:253,104:[1,254],105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},{105:[1,255]},{10:be,60:ke,84:ge,92:256,105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},{44:x,47:257,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},s(L,[2,108]),{80:[1,258]},{80:[1,259],98:[1,260]},s(L,[2,116]),s(L,[2,118],{10:[1,261]}),s(L,[2,119]),s(z,[2,56]),s(ye,[2,87]),s(z,[2,57]),{51:[1,262],67:m,82:218,116:C,117:S,118:T},s(z,[2,64]),s(z,[2,59]),s(z,[2,60]),s(z,[2,61]),{109:[1,263]},s(z,[2,63]),s(z,[2,65]),{66:[1,264],67:m,82:218,116:C,117:S,118:T},s(z,[2,67]),s(z,[2,68]),s(z,[2,70]),s(z,[2,69]),s(z,[2,71]),s([10,44,60,89,102,105,106,109,111,114,115,116],[2,85]),s(Xe,[2,78]),{31:[1,265],67:m,82:218,116:C,117:S,118:T},{6:11,7:12,8:p,9:c,10:A,11:r,20:17,22:18,23:19,24:20,25:21,26:22,27:E,32:[1,266],33:24,34:D,36:_,38:j,42:28,43:38,44:x,45:39,47:40,60:f,84:Z,85:Q,86:oe,87:Te,88:H,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K,121:qe,122:$e,123:et,124:tt},s(ve,[2,53]),{43:267,44:x,45:39,47:40,60:f,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K},s(L,[2,121],{106:Qe}),s(Qt,[2,130],{108:269,10:be,60:ke,84:ge,105:Ae,109:_e,110:Ee,111:De,112:me}),s(le,[2,132]),s(le,[2,134]),s(le,[2,135]),s(le,[2,136]),s(le,[2,137]),s(le,[2,138]),s(le,[2,139]),s(le,[2,140]),s(le,[2,141]),s(L,[2,122],{106:Qe}),{10:[1,270]},s(L,[2,123],{106:Qe}),{10:[1,271]},s(Zt,[2,129]),s(L,[2,105],{106:Qe}),s(L,[2,106],{113:112,44:x,60:f,89:V,102:P,105:M,106:N,109:O,111:W,114:G,115:U,116:K}),s(L,[2,110]),s(L,[2,112],{10:[1,272]}),s(L,[2,113]),{98:[1,273]},{51:[1,274]},{62:[1,275]},{66:[1,276]},{8:de,9:pe,11:fe,21:277},s(y,[2,34]),s(ve,[2,52]),{10:be,60:ke,84:ge,105:Ae,107:278,108:242,109:_e,110:Ee,111:De,112:me},s(le,[2,133]),{14:Me,44:Ne,60:Oe,89:We,101:279,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze,120:87},{14:Me,44:Ne,60:Oe,89:We,101:280,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze,120:87},{98:[1,281]},s(L,[2,120]),s(z,[2,58]),{30:282,67:m,80:J,81:q,82:171,116:C,117:S,118:T},s(z,[2,66]),s(lt,o,{5:283}),s(Qt,[2,131],{108:269,10:be,60:ke,84:ge,105:Ae,109:_e,110:Ee,111:De,112:me}),s(L,[2,126],{120:167,10:[1,284],14:Me,44:Ne,60:Oe,89:We,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze}),s(L,[2,127],{120:167,10:[1,285],14:Me,44:Ne,60:Oe,89:We,105:Ge,106:Ue,109:Ke,111:Ye,114:je,115:He,116:ze}),s(L,[2,114]),{31:[1,286],67:m,82:218,116:C,117:S,118:T},{6:11,7:12,8:p,9:c,10:A,11:r,20:17,22:18,23:19,24:20,25:21,26:22,27:E,32:[1,287],33:24,34:D,36:_,38:j,42:28,43:38,44:x,45:39,47:40,60:f,84:Z,85:Q,86:oe,87:Te,88:H,89:V,102:P,105:M,106:N,109:O,111:W,113:41,114:G,115:U,116:K,121:qe,122:$e,123:et,124:tt},{10:be,60:ke,84:ge,92:288,105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},{10:be,60:ke,84:ge,92:289,105:Ae,107:241,108:242,109:_e,110:Ee,111:De,112:me},s(z,[2,62]),s(y,[2,33]),s(L,[2,124],{106:Qe}),s(L,[2,125],{106:Qe})],defaultActions:{},parseError:(0,h.eW)(function(d,b){if(b.recoverable)this.trace(d);else{var g=new Error(d);throw g.hash=b,g}},"parseError"),parse:(0,h.eW)(function(d){var b=this,g=[0],l=[],B=[null],e=[],Le=this.table,t="",w=0,Jt=0,qt=0,A1=2,$t=1,_1=e.slice.call(arguments,1),Y=Object.create(this.lexer),Ve={yy:{}};for(var ht in this.yy)Object.prototype.hasOwnProperty.call(this.yy,ht)&&(Ve.yy[ht]=this.yy[ht]);Y.setInput(d,Ve.yy),Ve.yy.lexer=Y,Ve.yy.parser=this,typeof Y.yylloc=="undefined"&&(Y.yylloc={});var dt=Y.yylloc;e.push(dt);var E1=Y.options&&Y.options.ranges;typeof Ve.yy.parseError=="function"?this.parseError=Ve.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function D1(ae){g.length=g.length-2*ae,B.length=B.length-ae,e.length=e.length-ae}(0,h.eW)(D1,"popStack");function e1(){var ae;return ae=l.pop()||Y.lex()||$t,typeof ae!="number"&&(ae instanceof Array&&(l=ae,ae=l.pop()),ae=b.symbols_[ae]||ae),ae}(0,h.eW)(e1,"lex");for(var te,pt,Ie,ne,y1,ft,Je={},at,Ce,t1,nt;;){if(Ie=g[g.length-1],this.defaultActions[Ie]?ne=this.defaultActions[Ie]:((te===null||typeof te=="undefined")&&(te=e1()),ne=Le[Ie]&&Le[Ie][te]),typeof ne=="undefined"||!ne.length||!ne[0]){var bt="";nt=[];for(at in Le[Ie])this.terminals_[at]&&at>A1&&nt.push("'"+this.terminals_[at]+"'");Y.showPosition?bt="Parse error on line "+(w+1)+`:
`+Y.showPosition()+`
Expecting `+nt.join(", ")+", got '"+(this.terminals_[te]||te)+"'":bt="Parse error on line "+(w+1)+": Unexpected "+(te==$t?"end of input":"'"+(this.terminals_[te]||te)+"'"),this.parseError(bt,{text:Y.match,token:this.terminals_[te]||te,line:Y.yylineno,loc:dt,expected:nt})}if(ne[0]instanceof Array&&ne.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Ie+", token: "+te);switch(ne[0]){case 1:g.push(te),B.push(Y.yytext),e.push(Y.yylloc),g.push(ne[1]),te=null,pt?(te=pt,pt=null):(Jt=Y.yyleng,t=Y.yytext,w=Y.yylineno,dt=Y.yylloc,qt>0&&qt--);break;case 2:if(Ce=this.productions_[ne[1]][1],Je.$=B[B.length-Ce],Je._$={first_line:e[e.length-(Ce||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(Ce||1)].first_column,last_column:e[e.length-1].last_column},E1&&(Je._$.range=[e[e.length-(Ce||1)].range[0],e[e.length-1].range[1]]),ft=this.performAction.apply(Je,[t,Jt,w,Ve.yy,ne[1],B,e].concat(_1)),typeof ft!="undefined")return ft;Ce&&(g=g.slice(0,-1*Ce*2),B=B.slice(0,-1*Ce),e=e.slice(0,-1*Ce)),g.push(this.productions_[ne[1]][0]),B.push(Je.$),e.push(Je._$),t1=Le[g[g.length-2]][g[g.length-1]],g.push(t1);break;case 3:return!0}}return!0},"parse")},g1=function(){var Fe={EOF:1,parseError:(0,h.eW)(function(b,g){if(this.yy.parser)this.yy.parser.parseError(b,g);else throw new Error(b)},"parseError"),setInput:(0,h.eW)(function(d,b){return this.yy=b||this.yy||{},this._input=d,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,h.eW)(function(){var d=this._input[0];this.yytext+=d,this.yyleng++,this.offset++,this.match+=d,this.matched+=d;var b=d.match(/(?:\r\n?|\n).*/g);return b?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),d},"input"),unput:(0,h.eW)(function(d){var b=d.length,g=d.split(/(?:\r\n?|\n)/g);this._input=d+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-b),this.offset-=b;var l=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),g.length-1&&(this.yylineno-=g.length-1);var B=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:g?(g.length===l.length?this.yylloc.first_column:0)+l[l.length-g.length].length-g[0].length:this.yylloc.first_column-b},this.options.ranges&&(this.yylloc.range=[B[0],B[0]+this.yyleng-b]),this.yyleng=this.yytext.length,this},"unput"),more:(0,h.eW)(function(){return this._more=!0,this},"more"),reject:(0,h.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,h.eW)(function(d){this.unput(this.match.slice(d))},"less"),pastInput:(0,h.eW)(function(){var d=this.matched.substr(0,this.matched.length-this.match.length);return(d.length>20?"...":"")+d.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,h.eW)(function(){var d=this.match;return d.length<20&&(d+=this._input.substr(0,20-d.length)),(d.substr(0,20)+(d.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,h.eW)(function(){var d=this.pastInput(),b=new Array(d.length+1).join("-");return d+this.upcomingInput()+`
`+b+"^"},"showPosition"),test_match:(0,h.eW)(function(d,b){var g,l,B;if(this.options.backtrack_lexer&&(B={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(B.yylloc.range=this.yylloc.range.slice(0))),l=d[0].match(/(?:\r\n?|\n).*/g),l&&(this.yylineno+=l.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:l?l[l.length-1].length-l[l.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+d[0].length},this.yytext+=d[0],this.match+=d[0],this.matches=d,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(d[0].length),this.matched+=d[0],g=this.performAction.call(this,this.yy,this,b,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),g)return g;if(this._backtrack){for(var e in B)this[e]=B[e];return!1}return!1},"test_match"),next:(0,h.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var d,b,g,l;this._more||(this.yytext="",this.match="");for(var B=this._currentRules(),e=0;e<B.length;e++)if(g=this._input.match(this.rules[B[e]]),g&&(!b||g[0].length>b[0].length)){if(b=g,l=e,this.options.backtrack_lexer){if(d=this.test_match(g,B[e]),d!==!1)return d;if(this._backtrack){b=!1;continue}else return!1}else if(!this.options.flex)break}return b?(d=this.test_match(b,B[l]),d!==!1?d:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,h.eW)(function(){var b=this.next();return b||this.lex()},"lex"),begin:(0,h.eW)(function(b){this.conditionStack.push(b)},"begin"),popState:(0,h.eW)(function(){var b=this.conditionStack.length-1;return b>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,h.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,h.eW)(function(b){return b=this.conditionStack.length-1-Math.abs(b||0),b>=0?this.conditionStack[b]:"INITIAL"},"topState"),pushState:(0,h.eW)(function(b){this.begin(b)},"pushState"),stateStackSize:(0,h.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,h.eW)(function(b,g,l,B){var e=B;switch(l){case 0:return this.begin("acc_title"),34;break;case 1:return this.popState(),"acc_title_value";break;case 2:return this.begin("acc_descr"),36;break;case 3:return this.popState(),"acc_descr_value";break;case 4:this.begin("acc_descr_multiline");break;case 5:this.popState();break;case 6:return"acc_descr_multiline_value";case 7:return this.pushState("shapeData"),g.yytext="",40;break;case 8:return this.pushState("shapeDataStr"),40;break;case 9:return this.popState(),40;break;case 10:const Le=/\n\s*/g;return g.yytext=g.yytext.replace(Le,"<br/>"),40;break;case 11:return 40;case 12:this.popState();break;case 13:this.begin("callbackname");break;case 14:this.popState();break;case 15:this.popState(),this.begin("callbackargs");break;case 16:return 95;case 17:this.popState();break;case 18:return 96;case 19:return"MD_STR";case 20:this.popState();break;case 21:this.begin("md_string");break;case 22:return"STR";case 23:this.popState();break;case 24:this.pushState("string");break;case 25:return 84;case 26:return 102;case 27:return 85;case 28:return 104;case 29:return 86;case 30:return 87;case 31:return 97;case 32:this.begin("click");break;case 33:this.popState();break;case 34:return 88;case 35:return b.lex.firstGraph()&&this.begin("dir"),12;break;case 36:return b.lex.firstGraph()&&this.begin("dir"),12;break;case 37:return b.lex.firstGraph()&&this.begin("dir"),12;break;case 38:return 27;case 39:return 32;case 40:return 98;case 41:return 98;case 42:return 98;case 43:return 98;case 44:return this.popState(),13;break;case 45:return this.popState(),14;break;case 46:return this.popState(),14;break;case 47:return this.popState(),14;break;case 48:return this.popState(),14;break;case 49:return this.popState(),14;break;case 50:return this.popState(),14;break;case 51:return this.popState(),14;break;case 52:return this.popState(),14;break;case 53:return this.popState(),14;break;case 54:return this.popState(),14;break;case 55:return 121;case 56:return 122;case 57:return 123;case 58:return 124;case 59:return 78;case 60:return 105;case 61:return 111;case 62:return 46;case 63:return 60;case 64:return 44;case 65:return 8;case 66:return 106;case 67:return 115;case 68:return this.popState(),77;break;case 69:return this.pushState("edgeText"),75;break;case 70:return 119;case 71:return this.popState(),77;break;case 72:return this.pushState("thickEdgeText"),75;break;case 73:return 119;case 74:return this.popState(),77;break;case 75:return this.pushState("dottedEdgeText"),75;break;case 76:return 119;case 77:return 77;case 78:return this.popState(),53;break;case 79:return"TEXT";case 80:return this.pushState("ellipseText"),52;break;case 81:return this.popState(),55;break;case 82:return this.pushState("text"),54;break;case 83:return this.popState(),57;break;case 84:return this.pushState("text"),56;break;case 85:return 58;case 86:return this.pushState("text"),67;break;case 87:return this.popState(),64;break;case 88:return this.pushState("text"),63;break;case 89:return this.popState(),49;break;case 90:return this.pushState("text"),48;break;case 91:return this.popState(),69;break;case 92:return this.popState(),71;break;case 93:return 117;case 94:return this.pushState("trapText"),68;break;case 95:return this.pushState("trapText"),70;break;case 96:return 118;case 97:return 67;case 98:return 90;case 99:return"SEP";case 100:return 89;case 101:return 115;case 102:return 111;case 103:return 44;case 104:return 109;case 105:return 114;case 106:return 116;case 107:return this.popState(),62;break;case 108:return this.pushState("text"),62;break;case 109:return this.popState(),51;break;case 110:return this.pushState("text"),50;break;case 111:return this.popState(),31;break;case 112:return this.pushState("text"),29;break;case 113:return this.popState(),66;break;case 114:return this.pushState("text"),65;break;case 115:return"TEXT";case 116:return"QUOTE";case 117:return 9;case 118:return 10;case 119:return 11}},"anonymous"),rules:[/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:@\{)/,/^(?:["])/,/^(?:["])/,/^(?:[^\"]+)/,/^(?:[^}^"]+)/,/^(?:\})/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:[^`"]+)/,/^(?:[`]["])/,/^(?:["][`])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:["])/,/^(?:style\b)/,/^(?:default\b)/,/^(?:linkStyle\b)/,/^(?:interpolate\b)/,/^(?:classDef\b)/,/^(?:class\b)/,/^(?:href[\s])/,/^(?:click[\s]+)/,/^(?:[\s\n])/,/^(?:[^\s\n]*)/,/^(?:flowchart-elk\b)/,/^(?:graph\b)/,/^(?:flowchart\b)/,/^(?:subgraph\b)/,/^(?:end\b\s*)/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:(\r?\n)*\s*\n)/,/^(?:\s*LR\b)/,/^(?:\s*RL\b)/,/^(?:\s*TB\b)/,/^(?:\s*BT\b)/,/^(?:\s*TD\b)/,/^(?:\s*BR\b)/,/^(?:\s*<)/,/^(?:\s*>)/,/^(?:\s*\^)/,/^(?:\s*v\b)/,/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:[^\s\"]+@(?=[^\{\"]))/,/^(?:[0-9]+)/,/^(?:#)/,/^(?::::)/,/^(?::)/,/^(?:&)/,/^(?:;)/,/^(?:,)/,/^(?:\*)/,/^(?:\s*[xo<]?--+[-xo>]\s*)/,/^(?:\s*[xo<]?--\s*)/,/^(?:[^-]|-(?!-)+)/,/^(?:\s*[xo<]?==+[=xo>]\s*)/,/^(?:\s*[xo<]?==\s*)/,/^(?:[^=]|=(?!))/,/^(?:\s*[xo<]?-?\.+-[xo>]?\s*)/,/^(?:\s*[xo<]?-\.\s*)/,/^(?:[^\.]|\.(?!))/,/^(?:\s*~~[\~]+\s*)/,/^(?:[-/\)][\)])/,/^(?:[^\(\)\[\]\{\}]|!\)+)/,/^(?:\(-)/,/^(?:\]\))/,/^(?:\(\[)/,/^(?:\]\])/,/^(?:\[\[)/,/^(?:\[\|)/,/^(?:>)/,/^(?:\)\])/,/^(?:\[\()/,/^(?:\)\)\))/,/^(?:\(\(\()/,/^(?:[\\(?=\])][\]])/,/^(?:\/(?=\])\])/,/^(?:\/(?!\])|\\(?!\])|[^\\\[\]\(\)\{\}\/]+)/,/^(?:\[\/)/,/^(?:\[\\)/,/^(?:<)/,/^(?:>)/,/^(?:\^)/,/^(?:\\\|)/,/^(?:v\b)/,/^(?:\*)/,/^(?:#)/,/^(?:&)/,/^(?:([A-Za-z0-9!"\#$%&'*+\.`?\\_\/]|-(?=[^\>\-\.])|(?!))+)/,/^(?:-)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\|)/,/^(?:\|)/,/^(?:\))/,/^(?:\()/,/^(?:\])/,/^(?:\[)/,/^(?:(\}))/,/^(?:\{)/,/^(?:[^\[\]\(\)\{\}\|\"]+)/,/^(?:")/,/^(?:(\r?\n)+)/,/^(?:\s)/,/^(?:$)/],conditions:{shapeDataEndBracket:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},shapeDataStr:{rules:[9,10,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},shapeData:{rules:[8,11,12,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},callbackargs:{rules:[17,18,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},callbackname:{rules:[14,15,16,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},href:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},click:{rules:[21,24,33,34,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},dottedEdgeText:{rules:[21,24,74,76,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},thickEdgeText:{rules:[21,24,71,73,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},edgeText:{rules:[21,24,68,70,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},trapText:{rules:[21,24,77,80,82,84,88,90,91,92,93,94,95,108,110,112,114],inclusive:!1},ellipseText:{rules:[21,24,77,78,79,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},text:{rules:[21,24,77,80,81,82,83,84,87,88,89,90,94,95,107,108,109,110,111,112,113,114,115],inclusive:!1},vertex:{rules:[21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},dir:{rules:[21,24,44,45,46,47,48,49,50,51,52,53,54,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},acc_descr_multiline:{rules:[5,6,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},acc_descr:{rules:[3,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},acc_title:{rules:[1,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},md_string:{rules:[19,20,21,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},string:{rules:[21,22,23,24,77,80,82,84,88,90,94,95,108,110,112,114],inclusive:!1},INITIAL:{rules:[0,2,4,7,13,21,24,25,26,27,28,29,30,31,32,35,36,37,38,39,40,41,42,43,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,71,72,74,75,77,80,82,84,85,86,88,90,94,95,96,97,98,99,100,101,102,103,104,105,106,108,110,112,114,116,117,118,119],inclusive:!0}}};return Fe}();ct.lexer=g1;function rt(){this.yy={}}return(0,h.eW)(rt,"Parser"),rt.prototype=ct,ct.Parser=rt,new rt}();ot.parser=ot;var _t=ot,Et=Object.assign({},_t);Et.parse=s=>{const i=s.replace(/}\s*\n/g,`}
`);return _t.parse(i)};var d1=Et,p1=(0,h.eW)((s,i)=>{const a=a1.Z,n=a(s,"r"),u=a(s,"g"),o=a(s,"b");return n1.Z(n,u,o,i)},"fade"),f1=(0,h.eW)(s=>`.label {
    font-family: ${s.fontFamily};
    color: ${s.nodeTextColor||s.textColor};
  }
  .cluster-label text {
    fill: ${s.titleColor};
  }
  .cluster-label span {
    color: ${s.titleColor};
  }
  .cluster-label span p {
    background-color: transparent;
  }

  .label text,span {
    fill: ${s.nodeTextColor||s.textColor};
    color: ${s.nodeTextColor||s.textColor};
  }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
    stroke-width: 1px;
  }
  .rough-node .label text , .node .label text, .image-shape .label, .icon-shape .label {
    text-anchor: middle;
  }
  // .flowchart-label .text-outer-tspan {
  //   text-anchor: middle;
  // }
  // .flowchart-label .text-inner-tspan {
  //   text-anchor: start;
  // }

  .node .katex path {
    fill: #000;
    stroke: #000;
    stroke-width: 1px;
  }

  .rough-node .label,.node .label, .image-shape .label, .icon-shape .label {
    text-align: center;
  }
  .node.clickable {
    cursor: pointer;
  }


  .root .anchor path {
    fill: ${s.lineColor} !important;
    stroke-width: 0;
    stroke: ${s.lineColor};
  }

  .arrowheadPath {
    fill: ${s.arrowheadColor};
  }

  .edgePath .path {
    stroke: ${s.lineColor};
    stroke-width: 2.0px;
  }

  .flowchart-link {
    stroke: ${s.lineColor};
    fill: none;
  }

  .edgeLabel {
    background-color: ${s.edgeLabelBackground};
    p {
      background-color: ${s.edgeLabelBackground};
    }
    rect {
      opacity: 0.5;
      background-color: ${s.edgeLabelBackground};
      fill: ${s.edgeLabelBackground};
    }
    text-align: center;
  }

  /* For html labels only */
  .labelBkg {
    background-color: ${p1(s.edgeLabelBackground,.5)};
    // background-color:
  }

  .cluster rect {
    fill: ${s.clusterBkg};
    stroke: ${s.clusterBorder};
    stroke-width: 1px;
  }

  .cluster text {
    fill: ${s.titleColor};
  }

  .cluster span {
    color: ${s.titleColor};
  }
  /* .cluster div {
    color: ${s.titleColor};
  } */

  div.mermaidTooltip {
    position: absolute;
    text-align: center;
    max-width: 200px;
    padding: 2px;
    font-family: ${s.fontFamily};
    font-size: 12px;
    background: ${s.tertiaryColor};
    border: 1px solid ${s.border2};
    border-radius: 2px;
    pointer-events: none;
    z-index: 100;
  }

  .flowchartTitleText {
    text-anchor: middle;
    font-size: 18px;
    fill: ${s.textColor};
  }

  rect.text {
    fill: none;
    stroke-width: 0;
  }

  .icon-shape, .image-shape {
    background-color: ${s.edgeLabelBackground};
    p {
      background-color: ${s.edgeLabelBackground};
      padding: 2px;
    }
    rect {
      opacity: 0.5;
      background-color: ${s.edgeLabelBackground};
      fill: ${s.edgeLabelBackground};
    }
    text-align: center;
  }
  ${(0,$.G)()}
`,"getStyles"),b1=f1,k1={parser:d1,get db(){return new o1},renderer:h1,styles:b1,init:(0,h.eW)(s=>{s.flowchart||(s.flowchart={}),s.layout&&(0,h.Y4)({layout:s.layout}),s.flowchart.arrowMarkerAbsolute=s.arrowMarkerAbsolute,(0,h.Y4)({flowchart:{arrowMarkerAbsolute:s.arrowMarkerAbsolute}})},"init")}}}]);
}());