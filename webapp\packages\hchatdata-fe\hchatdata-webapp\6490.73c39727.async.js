"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[6490],{74310:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-616-64h536c4.4 0 8-3.6 8-8V284c0-7.2-8.7-10.7-13.7-5.7L592 488.6l-125.4-124a8.03 8.03 0 00-11.3 0l-189 189.6a7.87 7.87 0 00-2.3 5.6V720c0 4.4 3.6 8 8 8z"}}]},name:"area-chart",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},47712:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},20034:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 289.1a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1zm0-617.2c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 551c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 439c0-29.9 11.7-58 32.8-79.2C454 338.6 482.1 327 512 327c29.9 0 58 11.6 79.2 32.8C612.4 381 624 409.1 624 439c0 29.9-11.6 58-32.8 79.2z"}}]},name:"environment",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},15722:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},11090:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},9835:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M640.6 429.8h257.1c7.9 0 14.3-6.4 14.3-14.3V158.3c0-7.9-6.4-14.3-14.3-14.3H640.6c-7.9 0-14.3 6.4-14.3 14.3v92.9H490.6c-3.9 0-7.1 3.2-7.1 7.1v221.5h-85.7v-96.5c0-7.9-6.4-14.3-14.3-14.3H126.3c-7.9 0-14.3 6.4-14.3 14.3v257.2c0 7.9 6.4 14.3 14.3 14.3h257.1c7.9 0 14.3-6.4 14.3-14.3V544h85.7v221.5c0 3.9 3.2 7.1 7.1 7.1h135.7v92.9c0 7.9 6.4 14.3 14.3 14.3h257.1c7.9 0 14.3-6.4 14.3-14.3v-257c0-7.9-6.4-14.3-14.3-14.3h-257c-7.9 0-14.3 6.4-14.3 14.3v100h-78.6v-393h78.6v100c0 7.9 6.4 14.3 14.3 14.3zm53.5-217.9h150V362h-150V211.9zM329.9 587h-150V437h150v150zm364.2 75.1h150v150.1h-150V662.1z"}}]},name:"partition",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},37137:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M705.6 124.9a8 8 0 00-11.6 7.2v64.2c0 5.5 2.9 10.6 7.5 13.6a352.2 352.2 0 0162.2 49.8c32.7 32.8 58.4 70.9 76.3 113.3a355 355 0 0127.9 138.7c0 48.1-9.4 94.8-27.9 138.7a355.92 355.92 0 01-76.3 113.3 353.06 353.06 0 01-113.2 76.4c-43.8 18.6-90.5 28-138.5 28s-94.7-9.4-138.5-28a353.06 353.06 0 01-113.2-76.4A355.92 355.92 0 01184 650.4a355 355 0 01-27.9-138.7c0-48.1 9.4-94.8 27.9-138.7 17.9-42.4 43.6-80.5 76.3-113.3 19-19 39.8-35.6 62.2-49.8 4.7-2.9 7.5-8.1 7.5-13.6V132c0-6-6.3-9.8-11.6-7.2C178.5 195.2 82 339.3 80 506.3 77.2 745.1 272.5 943.5 511.2 944c239 .5 432.8-193.3 432.8-432.4 0-169.2-97-315.7-238.4-386.7zM480 560h64c4.4 0 8-3.6 8-8V88c0-4.4-3.6-8-8-8h-64c-4.4 0-8 3.6-8 8v464c0 4.4 3.6 8 8 8z"}}]},name:"poweroff",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},27995:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M688 264c0-4.4-3.6-8-8-8H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48zm-8 136H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM480 544H296c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-48 308H208V148h560v344c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V108c0-17.7-14.3-32-32-32H168c-17.7 0-32 14.3-32 32v784c0 17.7 14.3 32 32 32h264c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm356.8-74.4c29-26.3 47.2-64.3 47.2-106.6 0-79.5-64.5-144-144-144s-144 64.5-144 144c0 42.3 18.2 80.3 47.2 106.6-57 32.5-96.2 92.7-99.2 162.1-.2 4.5 3.5 8.3 8 8.3h48.1c4.2 0 7.7-3.3 8-7.6C564 871.2 621.7 816 692 816s128 55.2 131.9 124.4c.2 4.2 3.7 7.6 8 7.6H880c4.6 0 8.2-3.8 8-8.3-2.9-69.5-42.2-129.6-99.2-162.1zM692 591c44.2 0 80 35.8 80 80s-35.8 80-80 80-80-35.8-80-80 35.8-80 80-80z"}}]},name:"solution",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},12322:function(O,d,t){t.d(d,{Z:function(){return h}});var c=t(95687),r=t(44194),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},u=o,s=t(54183),v=function(i,f){return r.createElement(s.Z,(0,c.Z)({},i,{ref:f,icon:u}))},m=r.forwardRef(v),h=m},49877:function(O,d,t){t.d(d,{Z:function(){return B}});var c=t(44194),r=t(51865),o=t.n(r),u=t(34573),s=t(16986),v=t(47506),m=t(88370),h=t(77167);const x=["wrap","nowrap","wrap-reverse"],i=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],f=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"],Z=(n,e)=>{const l=e.wrap===!0?"wrap":e.wrap;return{[`${n}-wrap-${l}`]:l&&x.includes(l)}},A=(n,e)=>{const l={};return f.forEach(a=>{l[`${n}-align-${a}`]=e.align===a}),l[`${n}-align-stretch`]=!e.align&&!!e.vertical,l},w=(n,e)=>{const l={};return i.forEach(a=>{l[`${n}-justify-${a}`]=e.justify===a}),l};function I(n,e){return o()(Object.assign(Object.assign(Object.assign({},Z(n,e)),A(n,e)),w(n,e)))}var V=I;const M=n=>{const{componentCls:e}=n;return{[e]:{display:"flex",margin:0,padding:0,"&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},R=n=>{const{componentCls:e}=n;return{[e]:{"&-gap-small":{gap:n.flexGapSM},"&-gap-middle":{gap:n.flexGap},"&-gap-large":{gap:n.flexGapLG}}}},j=n=>{const{componentCls:e}=n,l={};return x.forEach(a=>{l[`${e}-wrap-${a}`]={flexWrap:a}}),l},L=n=>{const{componentCls:e}=n,l={};return f.forEach(a=>{l[`${e}-align-${a}`]={alignItems:a}}),l},P=n=>{const{componentCls:e}=n,l={};return i.forEach(a=>{l[`${e}-justify-${a}`]={justifyContent:a}}),l},$=()=>({});var H=(0,m.I$)("Flex",n=>{const{paddingXS:e,padding:l,paddingLG:a}=n,g=(0,h.mergeToken)(n,{flexGapSM:e,flexGap:l,flexGapLG:a});return[M(g),R(g),j(g),L(g),P(g)]},$,{resetStyle:!1}),G=function(n,e){var l={};for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&e.indexOf(a)<0&&(l[a]=n[a]);if(n!=null&&typeof Object.getOwnPropertySymbols=="function")for(var g=0,a=Object.getOwnPropertySymbols(n);g<a.length;g++)e.indexOf(a[g])<0&&Object.prototype.propertyIsEnumerable.call(n,a[g])&&(l[a[g]]=n[a[g]]);return l},B=c.forwardRef((n,e)=>{const{prefixCls:l,rootClassName:a,className:g,style:F,flex:z,gap:y,children:N,vertical:E=!1,component:W="div"}=n,T=G(n,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:p,direction:D,getPrefixCls:J}=c.useContext(v.E_),C=J("flex",l),[b,U,X]=H(C),Q=E!=null?E:p==null?void 0:p.vertical,Y=o()(g,a,p==null?void 0:p.className,C,U,X,V(C,n),{[`${C}-rtl`]:D==="rtl",[`${C}-gap-${y}`]:(0,s.n)(y),[`${C}-vertical`]:Q}),S=Object.assign(Object.assign({},p==null?void 0:p.style),F);return z&&(S.flex=z),y&&!(0,s.n)(y)&&(S.gap=y),b(c.createElement(W,Object.assign({ref:e,className:Y,style:S},(0,u.Z)(T,["justify","wrap","align"])),N))})}}]);
