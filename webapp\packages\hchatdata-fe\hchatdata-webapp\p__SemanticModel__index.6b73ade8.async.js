(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[4255],{65213:function(H,M){"use strict";var r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};M.Z=r},80610:function(H,M){"use strict";var r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM305.8 637.7c3.1 3.1 8.1 3.1 11.3 0l138.3-137.6L583 628.5c3.1 3.1 8.2 3.1 11.3 0l275.4-275.3c3.1-3.1 3.1-8.2 0-11.3l-39.6-39.6a8.03 8.03 0 00-11.3 0l-230 229.9L461.4 404a8.03 8.03 0 00-11.3 0L266.3 586.7a8.03 8.03 0 000 11.3l39.5 39.7z"}}]},name:"line-chart",theme:"outlined"};M.Z=r},51308:function(H,M,r){"use strict";r.d(M,{Z:function(){return F}});var ie=r(10947),K=r(2206),se={title:"title___V3aE9",subTitleContainer:"subTitleContainer___gJqJ1"},V=r(31549),W=ie.Z.Paragraph,ce=function(O){var ne=O.title,Z=O.subTitle,ae=O.subTitleEditable,q=ae===void 0?!1:ae,G=O.onSubTitleChange;return(0,V.jsxs)(K.Z,{direction:"vertical",size:2,style:{width:"100%"},children:[(0,V.jsx)("div",{className:se.title,children:ne}),(0,V.jsx)("div",{className:se.subTitleContainer,children:q?(0,V.jsx)(W,{editable:{onChange:function(h){G==null||G(h)}},children:Z||"\u6DFB\u52A0\u63CF\u8FF0"}):Z&&(0,V.jsx)("span",{style:{fontSize:"12px",color:"#7b809a"},children:Z})})]})},F=ce},34945:function(H,M,r){"use strict";r.r(M),r.d(M,{default:function(){return et}});var ie=r(90819),K=r.n(ie),se=r(89933),V=r.n(se),W=r(49841),ce=r.n(W),F=r(7477),d=r(44194),O=r(20221),ne=r(9113),Z=r(2206),ae=r(51865),q=r.n(ae),G=r(54387),de=r(63941),h=r(11778),ue=r(47506),Oe=r(70402),Se=r(59715);const Y=({children:e})=>{const{getPrefixCls:n}=d.useContext(ue.E_),a=n("breadcrumb");return d.createElement("li",{className:`${a}-separator`,"aria-hidden":"true"},e===""?e:e||"/")};Y.__ANT_BREADCRUMB_SEPARATOR=!0;var le=Y,Te=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};function we(e,n){if(e.title===void 0||e.title===null)return null;const a=Object.keys(n).join("|");return typeof e.title=="object"?e.title:String(e.title).replace(new RegExp(`:(${a})`,"g"),(t,l)=>n[l]||t)}function pe(e,n,a,t){if(a==null)return null;const{className:l,onClick:p}=n,g=Te(n,["className","onClick"]),S=Object.assign(Object.assign({},(0,de.Z)(g,{data:!0,aria:!0})),{onClick:p});return t!==void 0?d.createElement("a",Object.assign({},S,{className:q()(`${e}-link`,l),href:t}),a):d.createElement("span",Object.assign({},S,{className:q()(`${e}-link`,l)}),a)}function Me(e,n){return(t,l,p,g,S)=>{if(n)return n(t,l,p,g);const L=we(t,l);return pe(e,t,L,S)}}var he=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};const ge=e=>{const{prefixCls:n,separator:a="/",children:t,menu:l,overlay:p,dropdownProps:g,href:S}=e,R=(A=>{if(l||p){const Q=Object.assign({},g);if(l){const w=l||{},{items:X}=w,b=he(w,["items"]);Q.menu=Object.assign(Object.assign({},b),{items:X==null?void 0:X.map((z,y)=>{var{key:C,title:T,label:D,path:j}=z,J=he(z,["key","title","label","path"]);let te=D!=null?D:T;return j&&(te=d.createElement("a",{href:`${S}${j}`},te)),Object.assign(Object.assign({},J),{key:C!=null?C:y,label:te})})})}else p&&(Q.overlay=p);return d.createElement(Se.Z,Object.assign({placement:"bottom"},Q),d.createElement("span",{className:`${n}-overlay-link`},A,d.createElement(Oe.Z,null)))}return A})(t);return R!=null?d.createElement(d.Fragment,null,d.createElement("li",null,R),a&&d.createElement(le,null,a)):null},be=e=>{const{prefixCls:n,children:a,href:t}=e,l=he(e,["prefixCls","children","href"]),{getPrefixCls:p}=d.useContext(ue.E_),g=p("breadcrumb",n);return d.createElement(ge,Object.assign({},l,{prefixCls:g}),pe(g,l,a,t))};be.__ANT_BREADCRUMB_ITEM=!0;var Xe=be,ye=r(40044),Ce=r(19107),De=r(88370),Ie=r(77167);const Pe=e=>{const{componentCls:n,iconCls:a,calc:t}=e;return{[n]:Object.assign(Object.assign({},(0,Ce.Wf)(e)),{color:e.itemColor,fontSize:e.fontSize,[a]:{fontSize:e.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:e.linkColor,transition:`color ${e.motionDurationMid}`,padding:`0 ${(0,ye.unit)(e.paddingXXS)}`,borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",marginInline:t(e.marginXXS).mul(-1).equal(),"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover}},(0,Ce.Qy)(e)),"li:last-child":{color:e.lastItemColor},[`${n}-separator`]:{marginInline:e.separatorMargin,color:e.separatorColor},[`${n}-link`]:{[`
          > ${a} + span,
          > ${a} + a
        `]:{marginInlineStart:e.marginXXS}},[`${n}-overlay-link`]:{borderRadius:e.borderRadiusSM,height:e.fontHeight,display:"inline-block",padding:`0 ${(0,ye.unit)(e.paddingXXS)}`,marginInline:t(e.marginXXS).mul(-1).equal(),[`> ${a}`]:{marginInlineStart:e.marginXXS,fontSize:e.fontSizeIcon},"&:hover":{color:e.linkHoverColor,backgroundColor:e.colorBgTextHover,a:{color:e.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${e.componentCls}-rtl`]:{direction:"rtl"}})}},Ee=e=>({itemColor:e.colorTextDescription,lastItemColor:e.colorText,iconFontSize:e.fontSize,linkColor:e.colorTextDescription,linkHoverColor:e.colorText,separatorColor:e.colorTextDescription,separatorMargin:e.marginXS});var Ne=(0,De.I$)("Breadcrumb",e=>{const n=(0,Ie.mergeToken)(e,{});return Pe(n)},Ee),m=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};function i(e){const{breadcrumbName:n,children:a}=e,t=m(e,["breadcrumbName","children"]),l=Object.assign({title:n},t);return a&&(l.menu={items:a.map(p=>{var{breadcrumbName:g}=p,S=m(p,["breadcrumbName"]);return Object.assign(Object.assign({},S),{title:g})})}),l}function s(e,n){return(0,d.useMemo)(()=>e||(n?n.map(i):null),[e,n])}var o=function(e,n){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,t=Object.getOwnPropertySymbols(e);l<t.length;l++)n.indexOf(t[l])<0&&Object.prototype.propertyIsEnumerable.call(e,t[l])&&(a[t[l]]=e[t[l]]);return a};const c=(e,n)=>{if(n===void 0)return n;let a=(n||"").replace(/^\//,"");return Object.keys(e).forEach(t=>{a=a.replace(`:${t}`,e[t])}),a},u=e=>{const{prefixCls:n,separator:a="/",style:t,className:l,rootClassName:p,routes:g,items:S,children:L,itemRender:R,params:A={}}=e,Q=o(e,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:w,direction:X,breadcrumb:b}=d.useContext(ue.E_);let z;const y=w("breadcrumb",n),[C,T,D]=Ne(y),j=s(S,g),J=Me(y,R);if(j&&j.length>0){const oe=[],ve=S||g;z=j.map((re,xe)=>{const{path:tt,key:Ve,type:rt,menu:We,overlay:Ge,onClick:nt,className:at,separator:lt,dropdownProps:ot}=re,$e=c(A,tt);$e!==void 0&&oe.push($e);const Ue=Ve!=null?Ve:xe;if(rt==="separator")return d.createElement(le,{key:Ue},lt);const Re={},it=xe===j.length-1;We?Re.menu=We:Ge&&(Re.overlay=Ge);let{href:Ae}=re;return oe.length&&$e!==void 0&&(Ae=`#/${oe.join("/")}`),d.createElement(ge,Object.assign({key:Ue},Re,(0,de.Z)(re,{data:!0,aria:!0}),{className:at,dropdownProps:ot,href:Ae,separator:it?"":a,onClick:nt,prefixCls:y}),J(re,A,ve,oe,Ae))})}else if(L){const oe=(0,G.Z)(L).length;z=(0,G.Z)(L).map((ve,re)=>{if(!ve)return ve;const xe=re===oe-1;return(0,h.Tm)(ve,{separator:xe?"":a,key:re})})}const te=q()(y,b==null?void 0:b.className,{[`${y}-rtl`]:X==="rtl"},l,p,T,D),N=Object.assign(Object.assign({},b==null?void 0:b.style),t);return C(d.createElement("nav",Object.assign({className:te,style:N},Q),d.createElement("ol",null,z)))};u.Item=Xe,u.Separator=le;var x=u,I=x,P=r(95687),_={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M946.5 505L560.1 118.8l-25.9-25.9a31.5 31.5 0 00-44.4 0L77.5 505a63.9 63.9 0 00-18.8 46c.4 35.2 29.7 63.3 64.9 63.3h42.5V940h691.8V614.3h43.4c17.1 0 33.2-6.7 45.3-18.8a63.6 63.6 0 0018.7-45.3c0-17-6.7-33.1-18.8-45.2zM568 868H456V664h112v204zm217.9-325.7V868H632V640c0-22.1-17.9-40-40-40H432c-22.1 0-40 17.9-40 40v228H238.1V542.3h-96l370-369.7 23.1 23.1L882 542.3h-96.1z"}}]},name:"home",theme:"outlined"},ee=_,$=r(54183),k=function(n,a){return d.createElement($.Z,(0,P.Z)({},n,{ref:a,icon:ee}))},fe=d.forwardRef(k),U=fe,Be={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M956 686.5l-.1-.1-.1-.1C911.7 593 843.4 545 752.5 545s-159.2 48.1-203.4 141.3v.1a42.92 42.92 0 000 36.4C593.3 816 661.6 864 752.5 864s159.2-48.1 203.4-141.3c5.4-11.5 5.4-24.8.1-36.2zM752.5 800c-62.1 0-107.4-30-141.1-95.5C645 639 690.4 609 752.5 609c62.1 0 107.4 30 141.1 95.5C860 770 814.6 800 752.5 800z"}},{tag:"path",attrs:{d:"M697 705a56 56 0 10112 0 56 56 0 10-112 0zM136 232h704v253h72V192c0-17.7-14.3-32-32-32H96c-17.7 0-32 14.3-32 32v520c0 17.7 14.3 32 32 32h352v-72H136V232z"}},{tag:"path",attrs:{d:"M724.9 338.1l-36.8-36.8a8.03 8.03 0 00-11.3 0L493 485.3l-86.1-86.2a8.03 8.03 0 00-11.3 0L251.3 543.4a8.03 8.03 0 000 11.3l36.8 36.8c3.1 3.1 8.2 3.1 11.3 0l101.8-101.8 86.1 86.2c3.1 3.1 8.2 3.1 11.3 0l226.3-226.5c3.2-3.1 3.2-8.2 0-11.3z"}}]},name:"fund-view",theme:"outlined"},Le=Be,Fe=function(n,a){return d.createElement($.Z,(0,P.Z)({},n,{ref:a,icon:Le}))},f=d.forwardRef(Fe),B=f,je=r(65213),Ze=function(n,a){return d.createElement($.Z,(0,P.Z)({},n,{ref:a,icon:je.Z}))},ze=d.forwardRef(Ze),E=ze,me=r(80610),Qe=function(n,a){return d.createElement($.Z,(0,P.Z)({},n,{ref:a,icon:me.Z}))},Je=d.forwardRef(Qe),Ke=Je,Ye=r(40862),He=r(88054),v=r(31549),_e=function(){var n=(0,O.useModel)("SemanticModel.domainData"),a=(0,O.useModel)("SemanticModel.modelData"),t=(0,O.useModel)("SemanticModel.metricData"),l=(0,O.useModel)("SemanticModel.dimensionData"),p=n.selectDomainId,g=n.selectDomainName,S=n.selectDataSet,L=n.setSelectDataSet,R=a.selectModelId,A=a.selectModelName,Q=a.setSelectModel,w=l.selectDimension,X=l.setSelectDimension,b=t.selectMetric,z=t.setSelectMetric,y=[{title:(0,v.jsxs)(Z.Z,{onClick:function(){Q(void 0),X(void 0),z(void 0),L(void 0),(0,He.$w)(p,"overview")},children:[(0,v.jsx)(U,{}),(0,v.jsx)("span",{children:g})]})}];return S&&y.push({type:"separator",separator:"/"},{title:(0,v.jsxs)(Z.Z,{onClick:function(){},children:[(0,v.jsx)(B,{style:{position:"relative",top:"2px"}}),(0,v.jsx)("span",{children:S.name})]})}),A&&y.push({type:"separator",separator:"/"},{title:(0,v.jsxs)(Z.Z,{onClick:function(){if(X(void 0),z(void 0),w){(0,He.Ey)(p,R,"dimension");return}(0,He.Ey)(p,R,"metric")},children:[(0,v.jsx)(B,{style:{position:"relative",top:"2px"}}),(0,v.jsx)("span",{children:A})]})}),w&&y.push({type:"separator",separator:"/"},{title:(0,v.jsxs)(Z.Z,{onClick:function(){},children:[(0,v.jsx)(E,{style:{position:"relative",top:"2px"}}),(0,v.jsx)("span",{children:w.name})]})}),b!=null&&b.name&&y.push({type:"separator",separator:"/"},{title:b!=null&&b.name?(0,v.jsxs)(Z.Z,{children:[(0,v.jsx)(Ke,{style:{position:"relative",top:"2px"}}),(0,v.jsx)("span",{children:b.name})]}):void 0}),(0,v.jsx)(v.Fragment,{children:(0,v.jsx)(I,{className:Ye.Z.breadcrumb,separator:"",items:y})})},ke=_e,qe=function(n){ce()(n);var a=(0,O.useParams)(),t=a.domainId,l=a.modelId,p=(0,O.useModel)("SemanticModel.domainData"),g=(0,O.useModel)("SemanticModel.modelData"),S=(0,O.useModel)("SemanticModel.databaseData"),L=p.setSelectDomain,R=p.setDomainList,A=g.selectModel,Q=g.setSelectModel,w=S.MrefreshDatabaseList,X=function(C){var T=C.filter(function(j){return"".concat(j.id)===t})[0];if(T)L(T);else{var D=C.filter(function(j){return j.parentId===0})[0];D&&L(D)}},b=function(){var y=V()(K()().mark(function C(){var T,D,j,J;return K()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:return N.next=2,(0,ne.i7)();case 2:T=N.sent,D=T.code,j=T.data,J=T.msg,D===200?(X(j),R(j)):F.ZP.error(J);case 7:case"end":return N.stop()}},C)}));return function(){return y.apply(this,arguments)}}(),z=function(){var y=V()(K()().mark(function C(){var T,D,j,J;return K()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:return N.next=2,(0,ne.vu)({modelId:l});case 2:T=N.sent,D=T.code,j=T.data,J=T.msg,D===200?Q(j):F.ZP.error(J);case 7:case"end":return N.stop()}},C)}));return function(){return y.apply(this,arguments)}}();return(0,d.useEffect)(function(){return b(),w(),l&&l!==A&&z(),function(){L(void 0)}},[]),(0,v.jsxs)("div",{children:[(0,v.jsx)("div",{style:{background:"#fff"},children:(0,v.jsx)(ke,{})}),(0,v.jsx)("div",{children:(0,v.jsx)(O.Outlet,{})})]})},et=qe},88054:function(H,M,r){"use strict";r.d(M,{H3:function(){return pe},Mf:function(){return Me},DJ:function(){return ge},vX:function(){return ye},Ww:function(){return Ee},YA:function(){return Ne},$w:function(){return De},mr:function(){return Pe},Ey:function(){return Ie},pV:function(){return Ce}});var ie=r(76711),K=r.n(ie),se=r(86222),V=r(73193),W=r.n(V),ce=r(20263),F=r(46504),d=r(44319),O=r(88732),ne=r(26574),Z=r(51308),ae=r(49841),q=r.n(ae),G=r(44194),de=r(37069),h=r(31549),ue=function(i){var s=Object.assign({},(q()(i),i)),o=(0,G.useRef)(null);(0,G.useEffect)(function(){o.current&&o.current.addEventListener("wheel",c)},[]);var c=function(x){x.stopPropagation(),x.preventDefault()};return(0,h.jsx)(de.Z,W()({ref:o},s))},Oe=ue,Se=r(39378),Y=r(20221),le=ce.Z.Item,Te=F.default.TextArea,we=function m(i,s){return i.map(function(o){var c=_objectSpread(_objectSpread({},o),{},{key:o.id,disabled:s,children:o.children?m(o.children,s):[]});return c})},pe=function m(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return i==null?void 0:i.map(function(o){var c=o.children,u=o.parentId,x=u===void 0?[]:u,I=s.slice();return I.push(x),c?W()(W()({},o),{},{path:I,children:m(c,I)}):W()(W()({},o),{},{path:I})})},Me=function m(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,o=i==null?void 0:i.reduce(function(c,u){if(u.parentId==s){var x=m(i,u.id);x.length&&(u.children=x),u.key=u.id,u.value=u.id,u.title=u.name||u.categoryName,c.push(u)}return c},[]);return o},he=function m(i){var s=[];return i==null||i.forEach(function(o){o.children&&o.children.length>0&&(s.push(o.id),s=s.concat(m(o.children)))}),s},ge=function m(i,s){if(i.length===0)return[];var o=[],c=i.find(function(u){return u.subOrganizations&&(o=o.concat(u.subOrganizations)),u.id===s});return c||m(o,s)},be=function m(i,s,o){if(i.parentId===s)return!0;var c=o.find(function(u){return u.id===i.parentId});return c?m(c,s,o):!1},Xe=function(i){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,o=[],c=_createForOfIteratorHelper(i),u;try{for(c.s();!(u=c.n()).done;){var x=u.value,I=!0,P=_createForOfIteratorHelper(i),_;try{for(P.s();!(_=P.n()).done;){var ee=_.value;if(ee.parentId===x.id){I=!1;break}}}catch($){P.e($)}finally{P.f()}I&&(s===null||be(x,s,i))&&o.push(x)}}catch($){c.e($)}finally{c.f()}return o},ye=function(i){var s=i.reduce(function(o,c){var u,x,I,P,_,ee,$=c.dataType,k=c.name,fe=c.comment,U=c.placeholder,Be=c.description,Le=c.require,Fe=c.visible,f=c.sliderConfig;if(Fe===!1)return o;var B=(0,h.jsx)(F.default,{});switch($){case"string":k==="password"?B=(0,h.jsx)(F.default.Password,{placeholder:U}):B=(0,h.jsx)(F.default,{placeholder:U});break;case"password":{B=(0,h.jsx)(F.default.Password,{placeholder:U,visibilityToggle:k!=="apiKey"});break}case"longText":B=(0,h.jsx)(Te,{placeholder:U,style:{height:100}});break;case"number":B=(0,h.jsx)(Oe,{placeholder:U,style:{width:"100%"}});break;case"slider":B=(0,h.jsx)(d.Z,{min:f!=null&&(u=f.start)!==null&&u!==void 0&&u.value?Number(f==null||(x=f.start)===null||x===void 0?void 0:x.value):0,max:f!=null&&(I=f.end)!==null&&I!==void 0&&I.value?Number(f==null||(P=f.end)===null||P===void 0?void 0:P.value):1,step:f!=null&&f.unit?Number(f==null?void 0:f.unit):.1,marks:{0:(f==null||(_=f.start)===null||_===void 0?void 0:_.text)||"\u7CBE\u786E",1:(f==null||(ee=f.end)===null||ee===void 0?void 0:ee.text)||"\u968F\u673A"}});break;case"bool":return o.push((0,h.jsx)(le,{name:k,label:fe,valuePropName:"checked",getValueFromEvent:function(me){return me===!0?"true":"false"},getValueProps:function(me){return{checked:me==="true"}},children:(0,h.jsx)(O.Z,{})},k)),o;case"list":{var je=c.candidateValues,Ze=je===void 0?[]:je,ze=Ze.map(function(E){return(0,Se.isString)(E)?{label:E,value:E}:E!=null&&E.label?E:{label:E,value:E}});B=(0,h.jsx)(ne.default,{style:{width:"100%"},options:ze,placeholder:U});break}default:B=(0,h.jsx)(F.default,{placeholder:U});break}return o.push((0,h.jsx)(le,{name:k,rules:[{required:!!Le,message:"\u8BF7\u8F93\u5165".concat(fe)}],label:(0,h.jsx)(Z.Z,{title:fe,subTitle:Be}),children:B},k)),o},[]);return K()(s)},Ce=function(i,s){return"".concat(i,"-").concat(s)},De=function(i,s){Y.history.push("/model/domain/".concat(i,"/").concat(s))},Ie=function(i,s,o){Y.history.push("/model/domain/manager/".concat(i,"/").concat(s).concat(o?"/".concat(o):""))},Pe=function(i,s,o,c){Y.history.push("/model/metric/".concat(i,"/").concat(s,"/").concat(o).concat(c?"/".concat(c):""))},Ee=function(i,s,o){Y.history.push("/model/dataset/".concat(i,"/").concat(s).concat(o?"/".concat(o):""))},Ne=function(i,s,o,c){Y.history.push("/model/dimension/".concat(i,"/").concat(s,"/").concat(o).concat(c?"/".concat(c):""))}},40862:function(H,M){"use strict";M.Z={projectBody:"projectBody___GMHNv",projectManger:"projectManger___PuzNL",sider:"sider___S55QW",treeContainer:"treeContainer___F_MHZ",siderCollapsed:"siderCollapsed___e3Tzs",siderCollapsedButton:"siderCollapsedButton___kKCa5",domainTitle:"domainTitle___dLg1Z",addBtn:"addBtn___NgJnu",treeTitle:"treeTitle___pxP2c",title:"title___zDYEt",search:"search___ajh0Z",projectItem:"projectItem___MVjgE",projectItemTitle:"projectItemTitle___UNleh",operation:"operation___KBe7k",icon:"icon___OI409",rowHover:"rowHover___R8Gkz",content:"content___UnDIy",collapseLeftBtn:"collapseLeftBtn___gyWmB",navContainer:"navContainer___ylpZ4",tab:"tab___DNc2o",backBtn:"backBtn___Fkk3o",chatSettingSectionTab:"chatSettingSectionTab___q0yhD",mainTip:"mainTip___yDA5G",domainList:"domainList___FUQht",searchContainer:"searchContainer___CQdSq",disabledSearchTable:"disabledSearchTable___utFaT",classTable:"classTable___pu8oZ",classTableSelectColumnAlignLeft:"classTableSelectColumnAlignLeft___UMTDD",permissionDrawer:"permissionDrawer___lYLJK",domainSelector:"domainSelector___kWpyu",infoTagList:"infoTagList___WQDUE",siteTagPlus:"siteTagPlus___bAXGP",editTag:"editTag___b9EMV",tagInput:"tagInput___KMQyO",semanticGraphCanvas:"semanticGraphCanvas___P82Ie",toolbar:"toolbar___yCX3L",canvasContainer:"canvasContainer___KeiAn",taskStateRefreshIcon:"taskStateRefreshIcon___Sdq_u",ctrlBtnContainer:"ctrlBtnContainer___vAsJ4",breadcrumb:"breadcrumb___eYTKG",classDataSourceTypeModal:"classDataSourceTypeModal___yqnh1",desc:"desc___LQoJ5",markerTag:"markerTag___Uz0h9",textLink:"textLink___IzpNx",tableHeaderTitle:"tableHeaderTitle___xRfxO",headerTitleLabel:"headerTitleLabel___AGBUN",infoCard:"infoCard___MPayl",infoCardTitle:"infoCardTitle___yh9jd",infoCardContainer:"infoCardContainer___rdVzt",infoCardFooter:"infoCardFooter___b3PhH",infoCardFooterContainer:"infoCardFooterContainer___g2QAw",dimensionValueFilterTable:"dimensionValueFilterTable___i1D1i"}},49841:function(H){function M(r){if(r==null)throw new TypeError("Cannot destructure "+r)}H.exports=M,H.exports.__esModule=!0,H.exports.default=H.exports}}]);
