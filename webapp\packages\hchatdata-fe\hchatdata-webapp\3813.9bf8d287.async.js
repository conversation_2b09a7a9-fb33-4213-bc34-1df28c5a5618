!(function(){"use strict";var de=Object.defineProperty,le=Object.defineProperties;var fe=Object.getOwnPropertyDescriptors;var $=Object.getOwnPropertySymbols;var ue=Object.prototype.hasOwnProperty,ge=Object.prototype.propertyIsEnumerable;var L=(E,M,u)=>M in E?de(E,M,{enumerable:!0,configurable:!0,writable:!0,value:u}):E[M]=u,Z=(E,M)=>{for(var u in M||(M={}))ue.call(M,u)&&L(E,u,M[u]);if($)for(var u of $(M))ge.call(M,u)&&L(E,u,M[u]);return E},I=(E,M)=>le(E,fe(M));var P=(E,M,u)=>new Promise((p,A)=>{var N=b=>{try{R(u.next(b))}catch(G){A(G)}},k=b=>{try{R(u.throw(b))}catch(G){A(G)}},R=b=>b.done?p(b.value):Promise.resolve(b.value).then(N,k);R((u=u.apply(E,M)).next())});(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3813],{24220:function(E,M,u){var p=u(68194),A=4;function N(k){return(0,p.Z)(k,A)}M.Z=N},23813:function(E,M,u){u.r(M),u.d(M,{render:function(){return ae}});var p=u(78933),A=u(61150),N=u(52387),k=u(50538),R=u(57956),b=u(70919),G=u(50854),s=u(29134),q=u(47840),B=u(80155),ee=u(24220),T=u(857),we=u(55133);function C(e){var t={options:{directed:e.isDirected(),multigraph:e.isMultigraph(),compound:e.isCompound()},nodes:ne(e),edges:te(e)};return B.Z(e.graph())||(t.value=ee.Z(e.graph())),t}function ne(e){return T.Z(e.nodes(),function(t){var n=e.node(t),r=e.parent(t),a={v:t};return B.Z(n)||(a.value=n),B.Z(r)||(a.parent=r),a})}function te(e){return T.Z(e.edges(),function(t){var n=e.edge(t),r={v:t.v,w:t.w};return B.Z(t.name)||(r.name=t.name),B.Z(n)||(r.value=n),r})}function Me(e){var t=new Graph(e.options).setGraph(e.value);return _.each(e.nodes,function(n){t.setNode(n.v,n.value),n.parent&&t.setParent(n.v,n.parent)}),_.each(e.edges,function(n){t.setEdge({v:n.v,w:n.w,name:n.name},n.value)}),t}var H=u(56561),l=new Map,D=new Map,F=new Map,se=(0,s.eW)(()=>{D.clear(),F.clear(),l.clear()},"clear"),Y=(0,s.eW)((e,t)=>{const n=D.get(t)||[];return s.cM.trace("In isDescendant",t," ",e," = ",n.includes(e)),n.includes(e)},"isDescendant"),ce=(0,s.eW)((e,t)=>{const n=D.get(t)||[];return s.cM.info("Descendants of ",t," is ",n),s.cM.info("Edge is ",e),e.v===t||e.w===t?!1:n?n.includes(e.v)||Y(e.v,t)||Y(e.w,t)||n.includes(e.w):(s.cM.debug("Tilt, ",t,",not in descendants"),!1)},"edgeInCluster"),j=(0,s.eW)((e,t,n,r)=>{s.cM.warn("Copying children of ",e,"root",r,"data",t.node(e),r);const a=t.children(e)||[];e!==r&&a.push(e),s.cM.warn("Copying (nodes) clusterId",e,"nodes",a),a.forEach(o=>{if(t.children(o).length>0)j(o,t,n,r);else{const i=t.node(o);s.cM.info("cp ",o," to ",r," with parent ",e),n.setNode(o,i),r!==t.parent(o)&&(s.cM.warn("Setting parent",o,t.parent(o)),n.setParent(o,t.parent(o))),e!==r&&o!==e?(s.cM.debug("Setting parent",o,e),n.setParent(o,e)):(s.cM.info("In copy ",e,"root",r,"data",t.node(e),r),s.cM.debug("Not Setting parent for node=",o,"cluster!==rootId",e!==r,"node!==clusterId",o!==e));const g=t.edges(o);s.cM.debug("Copying Edges",g),g.forEach(f=>{s.cM.info("Edge",f);const X=t.edge(f.v,f.w,f.name);s.cM.info("Edge data",X,r);try{ce(f,r)?(s.cM.info("Copying as ",f.v,f.w,X,f.name),n.setEdge(f.v,f.w,X,f.name),s.cM.info("newGraph edges ",n.edges(),n.edge(n.edges()[0]))):s.cM.info("Skipping copy of edge ",f.v,"-->",f.w," rootId: ",r," clusterId:",e)}catch(O){s.cM.error(O)}})}s.cM.debug("Removing node",o),t.removeNode(o)})},"copy"),V=(0,s.eW)((e,t)=>{const n=t.children(e);let r=[...n];for(const a of n)F.set(a,e),r=[...r,...V(a,t)];return r},"extractDescendants"),ie=(0,s.eW)((e,t,n)=>{const r=e.edges().filter(f=>f.v===t||f.w===t),a=e.edges().filter(f=>f.v===n||f.w===n),o=r.map(f=>({v:f.v===t?n:f.v,w:f.w===t?t:f.w})),i=a.map(f=>({v:f.v,w:f.w}));return o.filter(f=>i.some(X=>f.v===X.v&&f.w===X.w))},"findCommonEdges"),J=(0,s.eW)((e,t,n)=>{const r=t.children(e);if(s.cM.trace("Searching children of id ",e,r),r.length<1)return e;let a;for(const o of r){const i=J(o,t,n),g=ie(t,n,i);if(i)if(g.length>0)a=i;else return i}return a},"findNonClusterChild"),Q=(0,s.eW)(e=>!l.has(e)||!l.get(e).externalConnections?e:l.has(e)?l.get(e).id:e,"getAnchorId"),re=(0,s.eW)((e,t)=>{if(!e||t>10){s.cM.debug("Opting out, no graph ");return}else s.cM.debug("Opting in, graph ");e.nodes().forEach(function(n){e.children(n).length>0&&(s.cM.warn("Cluster identified",n," Replacement id in edges: ",J(n,e,n)),D.set(n,V(n,e)),l.set(n,{id:J(n,e,n),clusterData:e.node(n)}))}),e.nodes().forEach(function(n){const r=e.children(n),a=e.edges();r.length>0?(s.cM.debug("Cluster identified",n,D),a.forEach(o=>{const i=Y(o.v,n),g=Y(o.w,n);i^g&&(s.cM.warn("Edge: ",o," leaves cluster ",n),s.cM.warn("Descendants of XXX ",n,": ",D.get(n)),l.get(n).externalConnections=!0)})):s.cM.debug("Not a cluster ",n,D)});for(let n of l.keys()){const r=l.get(n).id,a=e.parent(r);a!==n&&l.has(a)&&!l.get(a).externalConnections&&(l.get(n).id=a)}e.edges().forEach(function(n){const r=e.edge(n);s.cM.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(n)),s.cM.warn("Edge "+n.v+" -> "+n.w+": "+JSON.stringify(e.edge(n)));let a=n.v,o=n.w;if(s.cM.warn("Fix XXX",l,"ids:",n.v,n.w,"Translating: ",l.get(n.v)," --- ",l.get(n.w)),l.get(n.v)||l.get(n.w)){if(s.cM.warn("Fixing and trying - removing XXX",n.v,n.w,n.name),a=Q(n.v),o=Q(n.w),e.removeEdge(n.v,n.w,n.name),a!==n.v){const i=e.parent(a);l.get(i).externalConnections=!0,r.fromCluster=n.v}if(o!==n.w){const i=e.parent(o);l.get(i).externalConnections=!0,r.toCluster=n.w}s.cM.warn("Fix Replacing with XXX",a,o,n.name),e.setEdge(a,o,r,n.name)}}),s.cM.warn("Adjusted Graph",C(e)),U(e,0),s.cM.trace(l)},"adjustClustersAndEdges"),U=(0,s.eW)((e,t)=>{var a,o;if(s.cM.warn("extractor - ",t,C(e),e.children("D")),t>10){s.cM.error("Bailing out");return}let n=e.nodes(),r=!1;for(const i of n){const g=e.children(i);r=r||g.length>0}if(!r){s.cM.debug("Done, no node has children",e.nodes());return}s.cM.debug("Nodes = ",n,t);for(const i of n)if(s.cM.debug("Extracting node",i,l,l.has(i)&&!l.get(i).externalConnections,!e.parent(i),e.node(i),e.children("D")," Depth ",t),!l.has(i))s.cM.debug("Not a cluster",i,t);else if(!l.get(i).externalConnections&&e.children(i)&&e.children(i).length>0){s.cM.warn("Cluster without external connections, without a parent and with children",i,t);let f=e.graph().rankdir==="TB"?"LR":"TB";(o=(a=l.get(i))==null?void 0:a.clusterData)!=null&&o.dir&&(f=l.get(i).clusterData.dir,s.cM.warn("Fixing dir",l.get(i).clusterData.dir,f));const X=new H.k({multigraph:!0,compound:!0}).setGraph({rankdir:f,nodesep:50,ranksep:50,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}});s.cM.warn("Old graph before copy",C(e)),j(i,e,X,i),e.setNode(i,{clusterNode:!0,id:i,clusterData:l.get(i).clusterData,label:l.get(i).label,graph:X}),s.cM.warn("New graph after copy node: (",i,")",C(X)),s.cM.debug("Old graph after copy",C(e))}else s.cM.warn("Cluster ** ",i," **not meeting the criteria !externalConnections:",!l.get(i).externalConnections," no parent: ",!e.parent(i)," children ",e.children(i)&&e.children(i).length>0,e.children("D"),t),s.cM.debug(l);n=e.nodes(),s.cM.warn("New list of nodes",n);for(const i of n){const g=e.node(i);s.cM.warn(" Now next level",i,g),g!=null&&g.clusterNode&&U(g.graph,t+1)}},"extractor"),K=(0,s.eW)((e,t)=>{if(t.length===0)return[];let n=Object.assign([],t);return t.forEach(r=>{const a=e.children(r),o=K(e,a);n=[...n,...o]}),n},"sorter"),oe=(0,s.eW)(e=>K(e,e.children()),"sortNodesByHierarchy"),z=(0,s.eW)((e,t,n,r,a,o)=>P(this,null,function*(){s.cM.warn("Graph in recursive render:XAX",C(t),a);const i=t.graph().rankdir;s.cM.trace("Dir in recursive render - dir:",i);const g=e.insert("g").attr("class","root");t.nodes()?s.cM.info("Recursive render XXX",t.nodes()):s.cM.info("No nodes found for",t),t.edges().length>0&&s.cM.info("Recursive edges",t.edge(t.edges()[0]));const f=g.insert("g").attr("class","clusters"),X=g.insert("g").attr("class","edgePaths"),O=g.insert("g").attr("class","edgeLabels"),w=g.insert("g").attr("class","nodes");yield Promise.all(t.nodes().map(function(d){return P(this,null,function*(){const c=t.node(d);if(a!==void 0){const v=JSON.parse(JSON.stringify(a.clusterData));s.cM.trace(`Setting data for parent cluster XXX
 Node.id = `,d,`
 data=`,v.height,`
Parent cluster`,a.height),t.setNode(a.id,v),t.parent(d)||(s.cM.trace("Setting parent",d,a.id),t.setParent(d,a.id,v))}if(s.cM.info("(Insert) Node XXX"+d+": "+JSON.stringify(t.node(d))),c!=null&&c.clusterNode){s.cM.info("Cluster identified XBX",d,c.width,t.node(d));const{ranksep:v,nodesep:h}=t.graph();c.graph.setGraph(I(Z({},c.graph.graph()),{ranksep:v+25,nodesep:h}));const x=yield z(w,c.graph,n,r,t.node(d),o),W=x.elem;(0,N.jr)(c,W),c.diff=x.diff||0,s.cM.info("New compound node after recursive render XAX",d,"width",c.width,"height",c.height),(0,N.Yn)(W,c)}else t.children(d).length>0?(s.cM.trace("Cluster - the non recursive path XBX",d,c.id,c,c.width,"Graph:",t),s.cM.trace(J(c.id,t)),l.set(c.id,{id:J(c.id,t),node:c})):(s.cM.trace("Node - the non recursive path XAX",d,w,t.node(d),i),yield(0,N.Lf)(w,t.node(d),{config:o,dir:i}))})})),yield(0,s.eW)(()=>P(this,null,function*(){const d=t.edges().map(function(c){return P(this,null,function*(){const v=t.edge(c.v,c.w,c.name);s.cM.info("Edge "+c.v+" -> "+c.w+": "+JSON.stringify(c)),s.cM.info("Edge "+c.v+" -> "+c.w+": ",c," ",JSON.stringify(t.edge(c))),s.cM.info("Fix",l,"ids:",c.v,c.w,"Translating: ",l.get(c.v),l.get(c.w)),yield(0,p.I_)(O,v)})});yield Promise.all(d)}),"processEdges")(),s.cM.info("Graph before layout:",JSON.stringify(C(t))),s.cM.info("############################################# XXX"),s.cM.info("###                Layout                 ### XXX"),s.cM.info("############################################# XXX"),(0,q.bK)(t),s.cM.info("Graph after layout:",JSON.stringify(C(t)));let S=0,{subGraphTitleTotalMargin:y}=(0,k.L)(o);return yield Promise.all(oe(t).map(function(d){return P(this,null,function*(){var v;const c=t.node(d);if(s.cM.info("Position XBX => "+d+": ("+c.x,","+c.y,") width: ",c.width," height: ",c.height),c!=null&&c.clusterNode)c.y+=y,s.cM.info("A tainted cluster node XBX1",d,c.id,c.width,c.height,c.x,c.y,t.parent(d)),l.get(c.id).node=c,(0,N.aH)(c);else if(t.children(d).length>0){s.cM.info("A pure cluster node XBX1",d,c.id,c.x,c.y,c.width,c.height,t.parent(d)),c.height+=y,t.node(c.parentId);const h=(c==null?void 0:c.padding)/2||0,x=((v=c==null?void 0:c.labelBBox)==null?void 0:v.height)||0,W=x-h||0;s.cM.debug("OffsetY",W,"labelHeight",x,"halfPadding",h),yield(0,N.us)(f,c),l.get(c.id).node=c}else{const h=t.node(c.parentId);c.y+=y/2,s.cM.info("A regular node XBX1 - using the padding",c.id,"parent",c.parentId,c.width,c.height,c.x,c.y,"offsetY",c.offsetY,"parent",h,h==null?void 0:h.offsetY,c),(0,N.aH)(c)}})})),t.edges().forEach(function(d){const c=t.edge(d);s.cM.info("Edge "+d.v+" -> "+d.w+": "+JSON.stringify(c),c),c.points.forEach(W=>W.y+=y/2);const v=t.node(d.v);var h=t.node(d.w);const x=(0,p.QP)(X,c,l,n,v,h,r);(0,p.Jj)(c,x)}),t.nodes().forEach(function(d){const c=t.node(d);s.cM.info(d,c.type,c.diff),c.isGroup&&(S=c.diff)}),s.cM.warn("Returning from recursive render XAX",g,S),{elem:g,diff:S}}),"recursiveRender"),ae=(0,s.eW)((e,t)=>P(this,null,function*(){var o,i,g,f,X,O;const n=new H.k({multigraph:!0,compound:!0}).setGraph({rankdir:e.direction,nodesep:((o=e.config)==null?void 0:o.nodeSpacing)||((g=(i=e.config)==null?void 0:i.flowchart)==null?void 0:g.nodeSpacing)||e.nodeSpacing,ranksep:((f=e.config)==null?void 0:f.rankSpacing)||((O=(X=e.config)==null?void 0:X.flowchart)==null?void 0:O.rankSpacing)||e.rankSpacing,marginx:8,marginy:8}).setDefaultEdgeLabel(function(){return{}}),r=t.select("g");(0,p.DQ)(r,e.markers,e.type,e.diagramId),(0,N.gU)(),(0,p.ZH)(),(0,N.ZH)(),se(),e.nodes.forEach(w=>{n.setNode(w.id,Z({},w)),w.parentId&&n.setParent(w.id,w.parentId)}),s.cM.debug("Edges:",e.edges),e.edges.forEach(w=>{if(w.start===w.end){const m=w.start,S=m+"---"+m+"---1",y=m+"---"+m+"---2",d=n.node(m);n.setNode(S,{domId:S,id:S,parentId:d.parentId,labelStyle:"",label:"",padding:0,shape:"labelRect",style:"",width:10,height:10}),n.setParent(S,d.parentId),n.setNode(y,{domId:y,id:y,parentId:d.parentId,labelStyle:"",padding:0,shape:"labelRect",label:"",style:"",width:10,height:10}),n.setParent(y,d.parentId);const c=structuredClone(w),v=structuredClone(w),h=structuredClone(w);c.label="",c.arrowTypeEnd="none",c.id=m+"-cyclic-special-1",v.arrowTypeStart="none",v.arrowTypeEnd="none",v.id=m+"-cyclic-special-mid",h.label="",d.isGroup&&(c.fromCluster=m,h.toCluster=m),h.id=m+"-cyclic-special-2",h.arrowTypeStart="none",n.setEdge(m,S,c,m+"-cyclic-special-0"),n.setEdge(S,y,v,m+"-cyclic-special-1"),n.setEdge(y,m,h,m+"-cyc<lic-special-2")}else n.setEdge(w.start,w.end,Z({},w),w.id)}),s.cM.warn("Graph at first:",JSON.stringify(C(n))),re(n),s.cM.warn("Graph after XAX:",JSON.stringify(C(n)));const a=(0,s.nV)();yield z(r,n,e.type,e.diagramId,void 0,a)}),"render")}}]);
}());