"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3980],{13980:function(Ot,Ke,q){var De,Me;q.d(Ke,{diagram:function(){return m0}});var Q=q(85163),F=q(50854),d=q(29134),We=q(69471),Ge=q(33565),Pe=function(){var t=(0,d.eW)(function(Ce,x,v,k){for(v=v||{},k=Ce.length;k--;v[Ce[k]]=x);return v},"o"),e=[1,24],s=[1,25],o=[1,26],l=[1,27],a=[1,28],i=[1,63],n=[1,64],r=[1,65],u=[1,66],f=[1,67],p=[1,68],b=[1,69],E=[1,29],S=[1,30],M=[1,31],I=[1,32],j=[1,33],H=[1,34],ee=[1,35],te=[1,36],ae=[1,37],re=[1,38],ne=[1,39],ie=[1,40],se=[1,41],le=[1,42],oe=[1,43],ce=[1,44],he=[1,45],ue=[1,46],de=[1,47],fe=[1,48],pe=[1,50],ye=[1,51],be=[1,52],ge=[1,53],_e=[1,54],xe=[1,55],me=[1,56],ve=[1,57],ke=[1,58],Ee=[1,59],Ae=[1,60],Be=[14,42],rt=[14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],Ne=[12,14,34,36,37,38,39,40,41,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],A=[1,82],C=[1,83],w=[1,84],O=[1,85],T=[12,14,42],vt=[12,14,33,42],ze=[12,14,33,42,76,77,79,80],Oe=[12,33],nt=[34,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74],it={trace:(0,d.eW)(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,direction:5,direction_tb:6,direction_bt:7,direction_rl:8,direction_lr:9,graphConfig:10,C4_CONTEXT:11,NEWLINE:12,statements:13,EOF:14,C4_CONTAINER:15,C4_COMPONENT:16,C4_DYNAMIC:17,C4_DEPLOYMENT:18,otherStatements:19,diagramStatements:20,otherStatement:21,title:22,accDescription:23,acc_title:24,acc_title_value:25,acc_descr:26,acc_descr_value:27,acc_descr_multiline_value:28,boundaryStatement:29,boundaryStartStatement:30,boundaryStopStatement:31,boundaryStart:32,LBRACE:33,ENTERPRISE_BOUNDARY:34,attributes:35,SYSTEM_BOUNDARY:36,BOUNDARY:37,CONTAINER_BOUNDARY:38,NODE:39,NODE_L:40,NODE_R:41,RBRACE:42,diagramStatement:43,PERSON:44,PERSON_EXT:45,SYSTEM:46,SYSTEM_DB:47,SYSTEM_QUEUE:48,SYSTEM_EXT:49,SYSTEM_EXT_DB:50,SYSTEM_EXT_QUEUE:51,CONTAINER:52,CONTAINER_DB:53,CONTAINER_QUEUE:54,CONTAINER_EXT:55,CONTAINER_EXT_DB:56,CONTAINER_EXT_QUEUE:57,COMPONENT:58,COMPONENT_DB:59,COMPONENT_QUEUE:60,COMPONENT_EXT:61,COMPONENT_EXT_DB:62,COMPONENT_EXT_QUEUE:63,REL:64,BIREL:65,REL_U:66,REL_D:67,REL_L:68,REL_R:69,REL_B:70,REL_INDEX:71,UPDATE_EL_STYLE:72,UPDATE_REL_STYLE:73,UPDATE_LAYOUT_CONFIG:74,attribute:75,STR:76,STR_KEY:77,STR_VALUE:78,ATTRIBUTE:79,ATTRIBUTE_EMPTY:80,$accept:0,$end:1},terminals_:{2:"error",6:"direction_tb",7:"direction_bt",8:"direction_rl",9:"direction_lr",11:"C4_CONTEXT",12:"NEWLINE",14:"EOF",15:"C4_CONTAINER",16:"C4_COMPONENT",17:"C4_DYNAMIC",18:"C4_DEPLOYMENT",22:"title",23:"accDescription",24:"acc_title",25:"acc_title_value",26:"acc_descr",27:"acc_descr_value",28:"acc_descr_multiline_value",33:"LBRACE",34:"ENTERPRISE_BOUNDARY",36:"SYSTEM_BOUNDARY",37:"BOUNDARY",38:"CONTAINER_BOUNDARY",39:"NODE",40:"NODE_L",41:"NODE_R",42:"RBRACE",44:"PERSON",45:"PERSON_EXT",46:"SYSTEM",47:"SYSTEM_DB",48:"SYSTEM_QUEUE",49:"SYSTEM_EXT",50:"SYSTEM_EXT_DB",51:"SYSTEM_EXT_QUEUE",52:"CONTAINER",53:"CONTAINER_DB",54:"CONTAINER_QUEUE",55:"CONTAINER_EXT",56:"CONTAINER_EXT_DB",57:"CONTAINER_EXT_QUEUE",58:"COMPONENT",59:"COMPONENT_DB",60:"COMPONENT_QUEUE",61:"COMPONENT_EXT",62:"COMPONENT_EXT_DB",63:"COMPONENT_EXT_QUEUE",64:"REL",65:"BIREL",66:"REL_U",67:"REL_D",68:"REL_L",69:"REL_R",70:"REL_B",71:"REL_INDEX",72:"UPDATE_EL_STYLE",73:"UPDATE_REL_STYLE",74:"UPDATE_LAYOUT_CONFIG",76:"STR",77:"STR_KEY",78:"STR_VALUE",79:"ATTRIBUTE",80:"ATTRIBUTE_EMPTY"},productions_:[0,[3,1],[3,1],[5,1],[5,1],[5,1],[5,1],[4,1],[10,4],[10,4],[10,4],[10,4],[10,4],[13,1],[13,1],[13,2],[19,1],[19,2],[19,3],[21,1],[21,1],[21,2],[21,2],[21,1],[29,3],[30,3],[30,3],[30,4],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[32,2],[31,1],[20,1],[20,2],[20,3],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,1],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[43,2],[35,1],[35,2],[75,1],[75,2],[75,1],[75,1]],performAction:(0,d.eW)(function(x,v,k,g,P,h,Ye){var y=h.length-1;switch(P){case 3:g.setDirection("TB");break;case 4:g.setDirection("BT");break;case 5:g.setDirection("RL");break;case 6:g.setDirection("LR");break;case 8:case 9:case 10:case 11:case 12:g.setC4Type(h[y-3]);break;case 19:g.setTitle(h[y].substring(6)),this.$=h[y].substring(6);break;case 20:g.setAccDescription(h[y].substring(15)),this.$=h[y].substring(15);break;case 21:this.$=h[y].trim(),g.setTitle(this.$);break;case 22:case 23:this.$=h[y].trim(),g.setAccDescription(this.$);break;case 28:h[y].splice(2,0,"ENTERPRISE"),g.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 29:h[y].splice(2,0,"SYSTEM"),g.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 30:g.addPersonOrSystemBoundary(...h[y]),this.$=h[y];break;case 31:h[y].splice(2,0,"CONTAINER"),g.addContainerBoundary(...h[y]),this.$=h[y];break;case 32:g.addDeploymentNode("node",...h[y]),this.$=h[y];break;case 33:g.addDeploymentNode("nodeL",...h[y]),this.$=h[y];break;case 34:g.addDeploymentNode("nodeR",...h[y]),this.$=h[y];break;case 35:g.popBoundaryParseStack();break;case 39:g.addPersonOrSystem("person",...h[y]),this.$=h[y];break;case 40:g.addPersonOrSystem("external_person",...h[y]),this.$=h[y];break;case 41:g.addPersonOrSystem("system",...h[y]),this.$=h[y];break;case 42:g.addPersonOrSystem("system_db",...h[y]),this.$=h[y];break;case 43:g.addPersonOrSystem("system_queue",...h[y]),this.$=h[y];break;case 44:g.addPersonOrSystem("external_system",...h[y]),this.$=h[y];break;case 45:g.addPersonOrSystem("external_system_db",...h[y]),this.$=h[y];break;case 46:g.addPersonOrSystem("external_system_queue",...h[y]),this.$=h[y];break;case 47:g.addContainer("container",...h[y]),this.$=h[y];break;case 48:g.addContainer("container_db",...h[y]),this.$=h[y];break;case 49:g.addContainer("container_queue",...h[y]),this.$=h[y];break;case 50:g.addContainer("external_container",...h[y]),this.$=h[y];break;case 51:g.addContainer("external_container_db",...h[y]),this.$=h[y];break;case 52:g.addContainer("external_container_queue",...h[y]),this.$=h[y];break;case 53:g.addComponent("component",...h[y]),this.$=h[y];break;case 54:g.addComponent("component_db",...h[y]),this.$=h[y];break;case 55:g.addComponent("component_queue",...h[y]),this.$=h[y];break;case 56:g.addComponent("external_component",...h[y]),this.$=h[y];break;case 57:g.addComponent("external_component_db",...h[y]),this.$=h[y];break;case 58:g.addComponent("external_component_queue",...h[y]),this.$=h[y];break;case 60:g.addRel("rel",...h[y]),this.$=h[y];break;case 61:g.addRel("birel",...h[y]),this.$=h[y];break;case 62:g.addRel("rel_u",...h[y]),this.$=h[y];break;case 63:g.addRel("rel_d",...h[y]),this.$=h[y];break;case 64:g.addRel("rel_l",...h[y]),this.$=h[y];break;case 65:g.addRel("rel_r",...h[y]),this.$=h[y];break;case 66:g.addRel("rel_b",...h[y]),this.$=h[y];break;case 67:h[y].splice(0,1),g.addRel("rel",...h[y]),this.$=h[y];break;case 68:g.updateElStyle("update_el_style",...h[y]),this.$=h[y];break;case 69:g.updateRelStyle("update_rel_style",...h[y]),this.$=h[y];break;case 70:g.updateLayoutConfig("update_layout_config",...h[y]),this.$=h[y];break;case 71:this.$=[h[y]];break;case 72:h[y].unshift(h[y-1]),this.$=h[y];break;case 73:case 75:this.$=h[y].trim();break;case 74:let Te={};Te[h[y-1].trim()]=h[y].trim(),this.$=Te;break;case 76:this.$="";break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],7:[1,6],8:[1,7],9:[1,8],10:4,11:[1,9],15:[1,10],16:[1,11],17:[1,12],18:[1,13]},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,7]},{1:[2,3]},{1:[2,4]},{1:[2,5]},{1:[2,6]},{12:[1,14]},{12:[1,15]},{12:[1,16]},{12:[1,17]},{12:[1,18]},{13:19,19:20,20:21,21:22,22:e,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{13:70,19:20,20:21,21:22,22:e,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{13:71,19:20,20:21,21:22,22:e,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{13:72,19:20,20:21,21:22,22:e,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{13:73,19:20,20:21,21:22,22:e,23:s,24:o,26:l,28:a,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{14:[1,74]},t(Be,[2,13],{43:23,29:49,30:61,32:62,20:75,34:i,36:n,37:r,38:u,39:f,40:p,41:b,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae}),t(Be,[2,14]),t(rt,[2,16],{12:[1,76]}),t(Be,[2,36],{12:[1,77]}),t(Ne,[2,19]),t(Ne,[2,20]),{25:[1,78]},{27:[1,79]},t(Ne,[2,23]),{35:80,75:81,76:A,77:C,79:w,80:O},{35:86,75:81,76:A,77:C,79:w,80:O},{35:87,75:81,76:A,77:C,79:w,80:O},{35:88,75:81,76:A,77:C,79:w,80:O},{35:89,75:81,76:A,77:C,79:w,80:O},{35:90,75:81,76:A,77:C,79:w,80:O},{35:91,75:81,76:A,77:C,79:w,80:O},{35:92,75:81,76:A,77:C,79:w,80:O},{35:93,75:81,76:A,77:C,79:w,80:O},{35:94,75:81,76:A,77:C,79:w,80:O},{35:95,75:81,76:A,77:C,79:w,80:O},{35:96,75:81,76:A,77:C,79:w,80:O},{35:97,75:81,76:A,77:C,79:w,80:O},{35:98,75:81,76:A,77:C,79:w,80:O},{35:99,75:81,76:A,77:C,79:w,80:O},{35:100,75:81,76:A,77:C,79:w,80:O},{35:101,75:81,76:A,77:C,79:w,80:O},{35:102,75:81,76:A,77:C,79:w,80:O},{35:103,75:81,76:A,77:C,79:w,80:O},{35:104,75:81,76:A,77:C,79:w,80:O},t(T,[2,59]),{35:105,75:81,76:A,77:C,79:w,80:O},{35:106,75:81,76:A,77:C,79:w,80:O},{35:107,75:81,76:A,77:C,79:w,80:O},{35:108,75:81,76:A,77:C,79:w,80:O},{35:109,75:81,76:A,77:C,79:w,80:O},{35:110,75:81,76:A,77:C,79:w,80:O},{35:111,75:81,76:A,77:C,79:w,80:O},{35:112,75:81,76:A,77:C,79:w,80:O},{35:113,75:81,76:A,77:C,79:w,80:O},{35:114,75:81,76:A,77:C,79:w,80:O},{35:115,75:81,76:A,77:C,79:w,80:O},{20:116,29:49,30:61,32:62,34:i,36:n,37:r,38:u,39:f,40:p,41:b,43:23,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae},{12:[1,118],33:[1,117]},{35:119,75:81,76:A,77:C,79:w,80:O},{35:120,75:81,76:A,77:C,79:w,80:O},{35:121,75:81,76:A,77:C,79:w,80:O},{35:122,75:81,76:A,77:C,79:w,80:O},{35:123,75:81,76:A,77:C,79:w,80:O},{35:124,75:81,76:A,77:C,79:w,80:O},{35:125,75:81,76:A,77:C,79:w,80:O},{14:[1,126]},{14:[1,127]},{14:[1,128]},{14:[1,129]},{1:[2,8]},t(Be,[2,15]),t(rt,[2,17],{21:22,19:130,22:e,23:s,24:o,26:l,28:a}),t(Be,[2,37],{19:20,20:21,21:22,43:23,29:49,30:61,32:62,13:131,22:e,23:s,24:o,26:l,28:a,34:i,36:n,37:r,38:u,39:f,40:p,41:b,44:E,45:S,46:M,47:I,48:j,49:H,50:ee,51:te,52:ae,53:re,54:ne,55:ie,56:se,57:le,58:oe,59:ce,60:he,61:ue,62:de,63:fe,64:pe,65:ye,66:be,67:ge,68:_e,69:xe,70:me,71:ve,72:ke,73:Ee,74:Ae}),t(Ne,[2,21]),t(Ne,[2,22]),t(T,[2,39]),t(vt,[2,71],{75:81,35:132,76:A,77:C,79:w,80:O}),t(ze,[2,73]),{78:[1,133]},t(ze,[2,75]),t(ze,[2,76]),t(T,[2,40]),t(T,[2,41]),t(T,[2,42]),t(T,[2,43]),t(T,[2,44]),t(T,[2,45]),t(T,[2,46]),t(T,[2,47]),t(T,[2,48]),t(T,[2,49]),t(T,[2,50]),t(T,[2,51]),t(T,[2,52]),t(T,[2,53]),t(T,[2,54]),t(T,[2,55]),t(T,[2,56]),t(T,[2,57]),t(T,[2,58]),t(T,[2,60]),t(T,[2,61]),t(T,[2,62]),t(T,[2,63]),t(T,[2,64]),t(T,[2,65]),t(T,[2,66]),t(T,[2,67]),t(T,[2,68]),t(T,[2,69]),t(T,[2,70]),{31:134,42:[1,135]},{12:[1,136]},{33:[1,137]},t(Oe,[2,28]),t(Oe,[2,29]),t(Oe,[2,30]),t(Oe,[2,31]),t(Oe,[2,32]),t(Oe,[2,33]),t(Oe,[2,34]),{1:[2,9]},{1:[2,10]},{1:[2,11]},{1:[2,12]},t(rt,[2,18]),t(Be,[2,38]),t(vt,[2,72]),t(ze,[2,74]),t(T,[2,24]),t(T,[2,35]),t(nt,[2,25]),t(nt,[2,26],{12:[1,138]}),t(nt,[2,27])],defaultActions:{2:[2,1],3:[2,2],4:[2,7],5:[2,3],6:[2,4],7:[2,5],8:[2,6],74:[2,8],126:[2,9],127:[2,10],128:[2,11],129:[2,12]},parseError:(0,d.eW)(function(x,v){if(v.recoverable)this.trace(x);else{var k=new Error(x);throw k.hash=v,k}},"parseError"),parse:(0,d.eW)(function(x){var v=this,k=[0],g=[],P=[null],h=[],Ye=this.table,y="",Te=0,kt=0,Et=0,k0=2,At=1,E0=h.slice.call(arguments,1),B=Object.create(this.lexer),Re={yy:{}};for(var st in this.yy)Object.prototype.hasOwnProperty.call(this.yy,st)&&(Re.yy[st]=this.yy[st]);B.setInput(x,Re.yy),Re.yy.lexer=B,Re.yy.parser=this,typeof B.yylloc=="undefined"&&(B.yylloc={});var lt=B.yylloc;h.push(lt);var A0=B.options&&B.options.ranges;typeof Re.yy.parseError=="function"?this.parseError=Re.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function C0(U){k.length=k.length-2*U,P.length=P.length-U,h.length=h.length-U}(0,d.eW)(C0,"popStack");function Ct(){var U;return U=g.pop()||B.lex()||At,typeof U!="number"&&(U instanceof Array&&(g=U,U=g.pop()),U=v.symbols_[U]||U),U}(0,d.eW)(Ct,"lex");for(var L,ot,Se,z,w0,ct,Ie={},He,Z,wt,qe;;){if(Se=k[k.length-1],this.defaultActions[Se]?z=this.defaultActions[Se]:((L===null||typeof L=="undefined")&&(L=Ct()),z=Ye[Se]&&Ye[Se][L]),typeof z=="undefined"||!z.length||!z[0]){var ht="";qe=[];for(He in Ye[Se])this.terminals_[He]&&He>k0&&qe.push("'"+this.terminals_[He]+"'");B.showPosition?ht="Parse error on line "+(Te+1)+`:
`+B.showPosition()+`
Expecting `+qe.join(", ")+", got '"+(this.terminals_[L]||L)+"'":ht="Parse error on line "+(Te+1)+": Unexpected "+(L==At?"end of input":"'"+(this.terminals_[L]||L)+"'"),this.parseError(ht,{text:B.match,token:this.terminals_[L]||L,line:B.yylineno,loc:lt,expected:qe})}if(z[0]instanceof Array&&z.length>1)throw new Error("Parse Error: multiple actions possible at state: "+Se+", token: "+L);switch(z[0]){case 1:k.push(L),P.push(B.yytext),h.push(B.yylloc),k.push(z[1]),L=null,ot?(L=ot,ot=null):(kt=B.yyleng,y=B.yytext,Te=B.yylineno,lt=B.yylloc,Et>0&&Et--);break;case 2:if(Z=this.productions_[z[1]][1],Ie.$=P[P.length-Z],Ie._$={first_line:h[h.length-(Z||1)].first_line,last_line:h[h.length-1].last_line,first_column:h[h.length-(Z||1)].first_column,last_column:h[h.length-1].last_column},A0&&(Ie._$.range=[h[h.length-(Z||1)].range[0],h[h.length-1].range[1]]),ct=this.performAction.apply(Ie,[y,kt,Te,Re.yy,z[1],P,h].concat(E0)),typeof ct!="undefined")return ct;Z&&(k=k.slice(0,-1*Z*2),P=P.slice(0,-1*Z),h=h.slice(0,-1*Z)),k.push(this.productions_[z[1]][0]),P.push(Ie.$),h.push(Ie._$),wt=Ye[k[k.length-2]][k[k.length-1]],k.push(wt);break;case 3:return!0}}return!0},"parse")},v0=function(){var Ce={EOF:1,parseError:(0,d.eW)(function(v,k){if(this.yy.parser)this.yy.parser.parseError(v,k);else throw new Error(v)},"parseError"),setInput:(0,d.eW)(function(x,v){return this.yy=v||this.yy||{},this._input=x,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:(0,d.eW)(function(){var x=this._input[0];this.yytext+=x,this.yyleng++,this.offset++,this.match+=x,this.matched+=x;var v=x.match(/(?:\r\n?|\n).*/g);return v?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),x},"input"),unput:(0,d.eW)(function(x){var v=x.length,k=x.split(/(?:\r\n?|\n)/g);this._input=x+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-v),this.offset-=v;var g=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),k.length-1&&(this.yylineno-=k.length-1);var P=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:k?(k.length===g.length?this.yylloc.first_column:0)+g[g.length-k.length].length-k[0].length:this.yylloc.first_column-v},this.options.ranges&&(this.yylloc.range=[P[0],P[0]+this.yyleng-v]),this.yyleng=this.yytext.length,this},"unput"),more:(0,d.eW)(function(){return this._more=!0,this},"more"),reject:(0,d.eW)(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:(0,d.eW)(function(x){this.unput(this.match.slice(x))},"less"),pastInput:(0,d.eW)(function(){var x=this.matched.substr(0,this.matched.length-this.match.length);return(x.length>20?"...":"")+x.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:(0,d.eW)(function(){var x=this.match;return x.length<20&&(x+=this._input.substr(0,20-x.length)),(x.substr(0,20)+(x.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:(0,d.eW)(function(){var x=this.pastInput(),v=new Array(x.length+1).join("-");return x+this.upcomingInput()+`
`+v+"^"},"showPosition"),test_match:(0,d.eW)(function(x,v){var k,g,P;if(this.options.backtrack_lexer&&(P={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(P.yylloc.range=this.yylloc.range.slice(0))),g=x[0].match(/(?:\r\n?|\n).*/g),g&&(this.yylineno+=g.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:g?g[g.length-1].length-g[g.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+x[0].length},this.yytext+=x[0],this.match+=x[0],this.matches=x,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(x[0].length),this.matched+=x[0],k=this.performAction.call(this,this.yy,this,v,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),k)return k;if(this._backtrack){for(var h in P)this[h]=P[h];return!1}return!1},"test_match"),next:(0,d.eW)(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var x,v,k,g;this._more||(this.yytext="",this.match="");for(var P=this._currentRules(),h=0;h<P.length;h++)if(k=this._input.match(this.rules[P[h]]),k&&(!v||k[0].length>v[0].length)){if(v=k,g=h,this.options.backtrack_lexer){if(x=this.test_match(k,P[h]),x!==!1)return x;if(this._backtrack){v=!1;continue}else return!1}else if(!this.options.flex)break}return v?(x=this.test_match(v,P[g]),x!==!1?x:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:(0,d.eW)(function(){var v=this.next();return v||this.lex()},"lex"),begin:(0,d.eW)(function(v){this.conditionStack.push(v)},"begin"),popState:(0,d.eW)(function(){var v=this.conditionStack.length-1;return v>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:(0,d.eW)(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:(0,d.eW)(function(v){return v=this.conditionStack.length-1-Math.abs(v||0),v>=0?this.conditionStack[v]:"INITIAL"},"topState"),pushState:(0,d.eW)(function(v){this.begin(v)},"pushState"),stateStackSize:(0,d.eW)(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:(0,d.eW)(function(v,k,g,P){var h=P;switch(g){case 0:return 6;case 1:return 7;case 2:return 8;case 3:return 9;case 4:return 22;case 5:return 23;case 6:return this.begin("acc_title"),24;break;case 7:return this.popState(),"acc_title_value";break;case 8:return this.begin("acc_descr"),26;break;case 9:return this.popState(),"acc_descr_value";break;case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:break;case 14:c;break;case 15:return 12;case 16:break;case 17:return 11;case 18:return 15;case 19:return 16;case 20:return 17;case 21:return 18;case 22:return this.begin("person_ext"),45;break;case 23:return this.begin("person"),44;break;case 24:return this.begin("system_ext_queue"),51;break;case 25:return this.begin("system_ext_db"),50;break;case 26:return this.begin("system_ext"),49;break;case 27:return this.begin("system_queue"),48;break;case 28:return this.begin("system_db"),47;break;case 29:return this.begin("system"),46;break;case 30:return this.begin("boundary"),37;break;case 31:return this.begin("enterprise_boundary"),34;break;case 32:return this.begin("system_boundary"),36;break;case 33:return this.begin("container_ext_queue"),57;break;case 34:return this.begin("container_ext_db"),56;break;case 35:return this.begin("container_ext"),55;break;case 36:return this.begin("container_queue"),54;break;case 37:return this.begin("container_db"),53;break;case 38:return this.begin("container"),52;break;case 39:return this.begin("container_boundary"),38;break;case 40:return this.begin("component_ext_queue"),63;break;case 41:return this.begin("component_ext_db"),62;break;case 42:return this.begin("component_ext"),61;break;case 43:return this.begin("component_queue"),60;break;case 44:return this.begin("component_db"),59;break;case 45:return this.begin("component"),58;break;case 46:return this.begin("node"),39;break;case 47:return this.begin("node"),39;break;case 48:return this.begin("node_l"),40;break;case 49:return this.begin("node_r"),41;break;case 50:return this.begin("rel"),64;break;case 51:return this.begin("birel"),65;break;case 52:return this.begin("rel_u"),66;break;case 53:return this.begin("rel_u"),66;break;case 54:return this.begin("rel_d"),67;break;case 55:return this.begin("rel_d"),67;break;case 56:return this.begin("rel_l"),68;break;case 57:return this.begin("rel_l"),68;break;case 58:return this.begin("rel_r"),69;break;case 59:return this.begin("rel_r"),69;break;case 60:return this.begin("rel_b"),70;break;case 61:return this.begin("rel_index"),71;break;case 62:return this.begin("update_el_style"),72;break;case 63:return this.begin("update_rel_style"),73;break;case 64:return this.begin("update_layout_config"),74;break;case 65:return"EOF_IN_STRUCT";case 66:return this.begin("attribute"),"ATTRIBUTE_EMPTY";break;case 67:this.begin("attribute");break;case 68:this.popState(),this.popState();break;case 69:return 80;case 70:break;case 71:return 80;case 72:this.begin("string");break;case 73:this.popState();break;case 74:return"STR";case 75:this.begin("string_kv");break;case 76:return this.begin("string_kv_key"),"STR_KEY";break;case 77:this.popState(),this.begin("string_kv_value");break;case 78:return"STR_VALUE";case 79:this.popState(),this.popState();break;case 80:return"STR";case 81:return"LBRACE";case 82:return"RBRACE";case 83:return"SPACE";case 84:return"EOL";case 85:return 14}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:title\s[^#\n;]+)/,/^(?:accDescription\s[^#\n;]+)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:C4Context\b)/,/^(?:C4Container\b)/,/^(?:C4Component\b)/,/^(?:C4Dynamic\b)/,/^(?:C4Deployment\b)/,/^(?:Person_Ext\b)/,/^(?:Person\b)/,/^(?:SystemQueue_Ext\b)/,/^(?:SystemDb_Ext\b)/,/^(?:System_Ext\b)/,/^(?:SystemQueue\b)/,/^(?:SystemDb\b)/,/^(?:System\b)/,/^(?:Boundary\b)/,/^(?:Enterprise_Boundary\b)/,/^(?:System_Boundary\b)/,/^(?:ContainerQueue_Ext\b)/,/^(?:ContainerDb_Ext\b)/,/^(?:Container_Ext\b)/,/^(?:ContainerQueue\b)/,/^(?:ContainerDb\b)/,/^(?:Container\b)/,/^(?:Container_Boundary\b)/,/^(?:ComponentQueue_Ext\b)/,/^(?:ComponentDb_Ext\b)/,/^(?:Component_Ext\b)/,/^(?:ComponentQueue\b)/,/^(?:ComponentDb\b)/,/^(?:Component\b)/,/^(?:Deployment_Node\b)/,/^(?:Node\b)/,/^(?:Node_L\b)/,/^(?:Node_R\b)/,/^(?:Rel\b)/,/^(?:BiRel\b)/,/^(?:Rel_Up\b)/,/^(?:Rel_U\b)/,/^(?:Rel_Down\b)/,/^(?:Rel_D\b)/,/^(?:Rel_Left\b)/,/^(?:Rel_L\b)/,/^(?:Rel_Right\b)/,/^(?:Rel_R\b)/,/^(?:Rel_Back\b)/,/^(?:RelIndex\b)/,/^(?:UpdateElementStyle\b)/,/^(?:UpdateRelStyle\b)/,/^(?:UpdateLayoutConfig\b)/,/^(?:$)/,/^(?:[(][ ]*[,])/,/^(?:[(])/,/^(?:[)])/,/^(?:,,)/,/^(?:,)/,/^(?:[ ]*["]["])/,/^(?:[ ]*["])/,/^(?:["])/,/^(?:[^"]*)/,/^(?:[ ]*[\$])/,/^(?:[^=]*)/,/^(?:[=][ ]*["])/,/^(?:[^"]+)/,/^(?:["])/,/^(?:[^,]+)/,/^(?:\{)/,/^(?:\})/,/^(?:[\s]+)/,/^(?:[\n\r]+)/,/^(?:$)/],conditions:{acc_descr_multiline:{rules:[11,12],inclusive:!1},acc_descr:{rules:[9],inclusive:!1},acc_title:{rules:[7],inclusive:!1},string_kv_value:{rules:[78,79],inclusive:!1},string_kv_key:{rules:[77],inclusive:!1},string_kv:{rules:[76],inclusive:!1},string:{rules:[73,74],inclusive:!1},attribute:{rules:[68,69,70,71,72,75,80],inclusive:!1},update_layout_config:{rules:[65,66,67,68],inclusive:!1},update_rel_style:{rules:[65,66,67,68],inclusive:!1},update_el_style:{rules:[65,66,67,68],inclusive:!1},rel_b:{rules:[65,66,67,68],inclusive:!1},rel_r:{rules:[65,66,67,68],inclusive:!1},rel_l:{rules:[65,66,67,68],inclusive:!1},rel_d:{rules:[65,66,67,68],inclusive:!1},rel_u:{rules:[65,66,67,68],inclusive:!1},rel_bi:{rules:[],inclusive:!1},rel:{rules:[65,66,67,68],inclusive:!1},node_r:{rules:[65,66,67,68],inclusive:!1},node_l:{rules:[65,66,67,68],inclusive:!1},node:{rules:[65,66,67,68],inclusive:!1},index:{rules:[],inclusive:!1},rel_index:{rules:[65,66,67,68],inclusive:!1},component_ext_queue:{rules:[],inclusive:!1},component_ext_db:{rules:[65,66,67,68],inclusive:!1},component_ext:{rules:[65,66,67,68],inclusive:!1},component_queue:{rules:[65,66,67,68],inclusive:!1},component_db:{rules:[65,66,67,68],inclusive:!1},component:{rules:[65,66,67,68],inclusive:!1},container_boundary:{rules:[65,66,67,68],inclusive:!1},container_ext_queue:{rules:[65,66,67,68],inclusive:!1},container_ext_db:{rules:[65,66,67,68],inclusive:!1},container_ext:{rules:[65,66,67,68],inclusive:!1},container_queue:{rules:[65,66,67,68],inclusive:!1},container_db:{rules:[65,66,67,68],inclusive:!1},container:{rules:[65,66,67,68],inclusive:!1},birel:{rules:[65,66,67,68],inclusive:!1},system_boundary:{rules:[65,66,67,68],inclusive:!1},enterprise_boundary:{rules:[65,66,67,68],inclusive:!1},boundary:{rules:[65,66,67,68],inclusive:!1},system_ext_queue:{rules:[65,66,67,68],inclusive:!1},system_ext_db:{rules:[65,66,67,68],inclusive:!1},system_ext:{rules:[65,66,67,68],inclusive:!1},system_queue:{rules:[65,66,67,68],inclusive:!1},system_db:{rules:[65,66,67,68],inclusive:!1},system:{rules:[65,66,67,68],inclusive:!1},person_ext:{rules:[65,66,67,68],inclusive:!1},person:{rules:[65,66,67,68],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,81,82,83,84,85],inclusive:!0}}};return Ce}();it.lexer=v0;function Qe(){this.yy={}}return(0,d.eW)(Qe,"Parser"),Qe.prototype=it,it.Parser=Qe,new Qe}();Pe.parser=Pe;var Je=Pe,Y=[],K=[""],W="global",m="",R=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],D=[],N="",we=!1,je=4,Ue=2,ut,Tt=(0,d.eW)(function(){return ut},"getC4Type"),Rt=(0,d.eW)(function(t){ut=(0,d.oO)(t,(0,d.nV)())},"setC4Type"),St=(0,d.eW)(function(t,e,s,o,l,a,i,n,r){if(t==null||e===void 0||e===null||s===void 0||s===null||o===void 0||o===null)return;let u={};const f=D.find(p=>p.from===e&&p.to===s);if(f?u=f:D.push(u),u.type=t,u.from=e,u.to=s,u.label={text:o},l==null)u.techn={text:""};else if(typeof l=="object"){let[p,b]=Object.entries(l)[0];u[p]={text:b}}else u.techn={text:l};if(a==null)u.descr={text:""};else if(typeof a=="object"){let[p,b]=Object.entries(a)[0];u[p]={text:b}}else u.descr={text:a};if(typeof i=="object"){let[p,b]=Object.entries(i)[0];u[p]=b}else u.sprite=i;if(typeof n=="object"){let[p,b]=Object.entries(n)[0];u[p]=b}else u.tags=n;if(typeof r=="object"){let[p,b]=Object.entries(r)[0];u[p]=b}else u.link=r;u.wrap=$()},"addRel"),Wt=(0,d.eW)(function(t,e,s,o,l,a,i){if(e===null||s===null)return;let n={};const r=Y.find(u=>u.alias===e);if(r&&e===r.alias?n=r:(n.alias=e,Y.push(n)),s==null?n.label={text:""}:n.label={text:s},o==null)n.descr={text:""};else if(typeof o=="object"){let[u,f]=Object.entries(o)[0];n[u]={text:f}}else n.descr={text:o};if(typeof l=="object"){let[u,f]=Object.entries(l)[0];n[u]=f}else n.sprite=l;if(typeof a=="object"){let[u,f]=Object.entries(a)[0];n[u]=f}else n.tags=a;if(typeof i=="object"){let[u,f]=Object.entries(i)[0];n[u]=f}else n.link=i;n.typeC4Shape={text:t},n.parentBoundary=W,n.wrap=$()},"addPersonOrSystem"),Pt=(0,d.eW)(function(t,e,s,o,l,a,i,n){if(e===null||s===null)return;let r={};const u=Y.find(f=>f.alias===e);if(u&&e===u.alias?r=u:(r.alias=e,Y.push(r)),s==null?r.label={text:""}:r.label={text:s},o==null)r.techn={text:""};else if(typeof o=="object"){let[f,p]=Object.entries(o)[0];r[f]={text:p}}else r.techn={text:o};if(l==null)r.descr={text:""};else if(typeof l=="object"){let[f,p]=Object.entries(l)[0];r[f]={text:p}}else r.descr={text:l};if(typeof a=="object"){let[f,p]=Object.entries(a)[0];r[f]=p}else r.sprite=a;if(typeof i=="object"){let[f,p]=Object.entries(i)[0];r[f]=p}else r.tags=i;if(typeof n=="object"){let[f,p]=Object.entries(n)[0];r[f]=p}else r.link=n;r.wrap=$(),r.typeC4Shape={text:t},r.parentBoundary=W},"addContainer"),Dt=(0,d.eW)(function(t,e,s,o,l,a,i,n){if(e===null||s===null)return;let r={};const u=Y.find(f=>f.alias===e);if(u&&e===u.alias?r=u:(r.alias=e,Y.push(r)),s==null?r.label={text:""}:r.label={text:s},o==null)r.techn={text:""};else if(typeof o=="object"){let[f,p]=Object.entries(o)[0];r[f]={text:p}}else r.techn={text:o};if(l==null)r.descr={text:""};else if(typeof l=="object"){let[f,p]=Object.entries(l)[0];r[f]={text:p}}else r.descr={text:l};if(typeof a=="object"){let[f,p]=Object.entries(a)[0];r[f]=p}else r.sprite=a;if(typeof i=="object"){let[f,p]=Object.entries(i)[0];r[f]=p}else r.tags=i;if(typeof n=="object"){let[f,p]=Object.entries(n)[0];r[f]=p}else r.link=n;r.wrap=$(),r.typeC4Shape={text:t},r.parentBoundary=W},"addComponent"),Mt=(0,d.eW)(function(t,e,s,o,l){if(t===null||e===null)return;let a={};const i=R.find(n=>n.alias===t);if(i&&t===i.alias?a=i:(a.alias=t,R.push(a)),e==null?a.label={text:""}:a.label={text:e},s==null)a.type={text:"system"};else if(typeof s=="object"){let[n,r]=Object.entries(s)[0];a[n]={text:r}}else a.type={text:s};if(typeof o=="object"){let[n,r]=Object.entries(o)[0];a[n]=r}else a.tags=o;if(typeof l=="object"){let[n,r]=Object.entries(l)[0];a[n]=r}else a.link=l;a.parentBoundary=W,a.wrap=$(),m=W,W=t,K.push(m)},"addPersonOrSystemBoundary"),Bt=(0,d.eW)(function(t,e,s,o,l){if(t===null||e===null)return;let a={};const i=R.find(n=>n.alias===t);if(i&&t===i.alias?a=i:(a.alias=t,R.push(a)),e==null?a.label={text:""}:a.label={text:e},s==null)a.type={text:"container"};else if(typeof s=="object"){let[n,r]=Object.entries(s)[0];a[n]={text:r}}else a.type={text:s};if(typeof o=="object"){let[n,r]=Object.entries(o)[0];a[n]=r}else a.tags=o;if(typeof l=="object"){let[n,r]=Object.entries(l)[0];a[n]=r}else a.link=l;a.parentBoundary=W,a.wrap=$(),m=W,W=t,K.push(m)},"addContainerBoundary"),It=(0,d.eW)(function(t,e,s,o,l,a,i,n){if(e===null||s===null)return;let r={};const u=R.find(f=>f.alias===e);if(u&&e===u.alias?r=u:(r.alias=e,R.push(r)),s==null?r.label={text:""}:r.label={text:s},o==null)r.type={text:"node"};else if(typeof o=="object"){let[f,p]=Object.entries(o)[0];r[f]={text:p}}else r.type={text:o};if(l==null)r.descr={text:""};else if(typeof l=="object"){let[f,p]=Object.entries(l)[0];r[f]={text:p}}else r.descr={text:l};if(typeof i=="object"){let[f,p]=Object.entries(i)[0];r[f]=p}else r.tags=i;if(typeof n=="object"){let[f,p]=Object.entries(n)[0];r[f]=p}else r.link=n;r.nodeType=t,r.parentBoundary=W,r.wrap=$(),m=W,W=e,K.push(m)},"addDeploymentNode"),Lt=(0,d.eW)(function(){W=m,K.pop(),m=K.pop(),K.push(m)},"popBoundaryParseStack"),Nt=(0,d.eW)(function(t,e,s,o,l,a,i,n,r,u,f){let p=Y.find(b=>b.alias===e);if(!(p===void 0&&(p=R.find(b=>b.alias===e),p===void 0))){if(s!=null)if(typeof s=="object"){let[b,E]=Object.entries(s)[0];p[b]=E}else p.bgColor=s;if(o!=null)if(typeof o=="object"){let[b,E]=Object.entries(o)[0];p[b]=E}else p.fontColor=o;if(l!=null)if(typeof l=="object"){let[b,E]=Object.entries(l)[0];p[b]=E}else p.borderColor=l;if(a!=null)if(typeof a=="object"){let[b,E]=Object.entries(a)[0];p[b]=E}else p.shadowing=a;if(i!=null)if(typeof i=="object"){let[b,E]=Object.entries(i)[0];p[b]=E}else p.shape=i;if(n!=null)if(typeof n=="object"){let[b,E]=Object.entries(n)[0];p[b]=E}else p.sprite=n;if(r!=null)if(typeof r=="object"){let[b,E]=Object.entries(r)[0];p[b]=E}else p.techn=r;if(u!=null)if(typeof u=="object"){let[b,E]=Object.entries(u)[0];p[b]=E}else p.legendText=u;if(f!=null)if(typeof f=="object"){let[b,E]=Object.entries(f)[0];p[b]=E}else p.legendSprite=f}},"updateElStyle"),Yt=(0,d.eW)(function(t,e,s,o,l,a,i){const n=D.find(r=>r.from===e&&r.to===s);if(n!==void 0){if(o!=null)if(typeof o=="object"){let[r,u]=Object.entries(o)[0];n[r]=u}else n.textColor=o;if(l!=null)if(typeof l=="object"){let[r,u]=Object.entries(l)[0];n[r]=u}else n.lineColor=l;if(a!=null)if(typeof a=="object"){let[r,u]=Object.entries(a)[0];n[r]=parseInt(u)}else n.offsetX=parseInt(a);if(i!=null)if(typeof i=="object"){let[r,u]=Object.entries(i)[0];n[r]=parseInt(u)}else n.offsetY=parseInt(i)}},"updateRelStyle"),jt=(0,d.eW)(function(t,e,s){let o=je,l=Ue;if(typeof e=="object"){const a=Object.values(e)[0];o=parseInt(a)}else o=parseInt(e);if(typeof s=="object"){const a=Object.values(s)[0];l=parseInt(a)}else l=parseInt(s);o>=1&&(je=o),l>=1&&(Ue=l)},"updateLayoutConfig"),Ut=(0,d.eW)(function(){return je},"getC4ShapeInRow"),Ft=(0,d.eW)(function(){return Ue},"getC4BoundaryInRow"),Vt=(0,d.eW)(function(){return W},"getCurrentBoundaryParse"),Xt=(0,d.eW)(function(){return m},"getParentBoundaryParse"),dt=(0,d.eW)(function(t){return t==null?Y:Y.filter(e=>e.parentBoundary===t)},"getC4ShapeArray"),zt=(0,d.eW)(function(t){return Y.find(e=>e.alias===t)},"getC4Shape"),Qt=(0,d.eW)(function(t){return Object.keys(dt(t))},"getC4ShapeKeys"),ft=(0,d.eW)(function(t){return t==null?R:R.filter(e=>e.parentBoundary===t)},"getBoundaries"),Ht=ft,qt=(0,d.eW)(function(){return D},"getRels"),Kt=(0,d.eW)(function(){return N},"getTitle"),Gt=(0,d.eW)(function(t){we=t},"setWrap"),$=(0,d.eW)(function(){return we},"autoWrap"),Jt=(0,d.eW)(function(){Y=[],R=[{alias:"global",label:{text:"global"},type:{text:"global"},tags:null,link:null,parentBoundary:""}],m="",W="global",K=[""],D=[],K=[""],N="",we=!1,je=4,Ue=2},"clear"),Zt={SOLID:0,DOTTED:1,NOTE:2,SOLID_CROSS:3,DOTTED_CROSS:4,SOLID_OPEN:5,DOTTED_OPEN:6,LOOP_START:10,LOOP_END:11,ALT_START:12,ALT_ELSE:13,ALT_END:14,OPT_START:15,OPT_END:16,ACTIVE_START:17,ACTIVE_END:18,PAR_START:19,PAR_AND:20,PAR_END:21,RECT_START:22,RECT_END:23,SOLID_POINT:24,DOTTED_POINT:25},$t={FILLED:0,OPEN:1},e0={LEFTOF:0,RIGHTOF:1,OVER:2},t0=(0,d.eW)(function(t){N=(0,d.oO)(t,(0,d.nV)())},"setTitle"),Ze={addPersonOrSystem:Wt,addPersonOrSystemBoundary:Mt,addContainer:Pt,addContainerBoundary:Bt,addComponent:Dt,addDeploymentNode:It,popBoundaryParseStack:Lt,addRel:St,updateElStyle:Nt,updateRelStyle:Yt,updateLayoutConfig:jt,autoWrap:$,setWrap:Gt,getC4ShapeArray:dt,getC4Shape:zt,getC4ShapeKeys:Qt,getBoundaries:ft,getBoundarys:Ht,getCurrentBoundaryParse:Vt,getParentBoundaryParse:Xt,getRels:qt,getTitle:Kt,getC4Type:Tt,getC4ShapeInRow:Ut,getC4BoundaryInRow:Ft,setAccTitle:d.GN,getAccTitle:d.eu,getAccDescription:d.Mx,setAccDescription:d.U$,getConfig:(0,d.eW)(()=>(0,d.nV)().c4,"getConfig"),clear:Jt,LINETYPE:Zt,ARROWTYPE:$t,PLACEMENT:e0,setTitle:t0,setC4Type:Rt},$e=(0,d.eW)(function(t,e){return(0,Q.Mu)(t,e)},"drawRect"),pt=(0,d.eW)(function(t,e,s,o,l,a){const i=t.append("image");i.attr("width",e),i.attr("height",s),i.attr("x",o),i.attr("y",l);let n=a.startsWith("data:image/png;base64")?a:(0,Ge.N)(a);i.attr("xlink:href",n)},"drawImage"),a0=(0,d.eW)((t,e,s)=>{const o=t.append("g");let l=0;for(let a of e){let i=a.textColor?a.textColor:"#444444",n=a.lineColor?a.lineColor:"#444444",r=a.offsetX?parseInt(a.offsetX):0,u=a.offsetY?parseInt(a.offsetY):0,f="";if(l===0){let b=o.append("line");b.attr("x1",a.startPoint.x),b.attr("y1",a.startPoint.y),b.attr("x2",a.endPoint.x),b.attr("y2",a.endPoint.y),b.attr("stroke-width","1"),b.attr("stroke",n),b.style("fill","none"),a.type!=="rel_b"&&b.attr("marker-end","url("+f+"#arrowhead)"),(a.type==="birel"||a.type==="rel_b")&&b.attr("marker-start","url("+f+"#arrowend)"),l=-1}else{let b=o.append("path");b.attr("fill","none").attr("stroke-width","1").attr("stroke",n).attr("d","Mstartx,starty Qcontrolx,controly stopx,stopy ".replaceAll("startx",a.startPoint.x).replaceAll("starty",a.startPoint.y).replaceAll("controlx",a.startPoint.x+(a.endPoint.x-a.startPoint.x)/2-(a.endPoint.x-a.startPoint.x)/4).replaceAll("controly",a.startPoint.y+(a.endPoint.y-a.startPoint.y)/2).replaceAll("stopx",a.endPoint.x).replaceAll("stopy",a.endPoint.y)),a.type!=="rel_b"&&b.attr("marker-end","url("+f+"#arrowhead)"),(a.type==="birel"||a.type==="rel_b")&&b.attr("marker-start","url("+f+"#arrowend)")}let p=s.messageFont();J(s)(a.label.text,o,Math.min(a.startPoint.x,a.endPoint.x)+Math.abs(a.endPoint.x-a.startPoint.x)/2+r,Math.min(a.startPoint.y,a.endPoint.y)+Math.abs(a.endPoint.y-a.startPoint.y)/2+u,a.label.width,a.label.height,{fill:i},p),a.techn&&a.techn.text!==""&&(p=s.messageFont(),J(s)("["+a.techn.text+"]",o,Math.min(a.startPoint.x,a.endPoint.x)+Math.abs(a.endPoint.x-a.startPoint.x)/2+r,Math.min(a.startPoint.y,a.endPoint.y)+Math.abs(a.endPoint.y-a.startPoint.y)/2+s.messageFontSize+5+u,Math.max(a.label.width,a.techn.width),a.techn.height,{fill:i,"font-style":"italic"},p))}},"drawRels"),r0=(0,d.eW)(function(t,e,s){const o=t.append("g");let l=e.bgColor?e.bgColor:"none",a=e.borderColor?e.borderColor:"#444444",i=e.fontColor?e.fontColor:"black",n={"stroke-width":1,"stroke-dasharray":"7.0,7.0"};e.nodeType&&(n={"stroke-width":1});let r={x:e.x,y:e.y,fill:l,stroke:a,width:e.width,height:e.height,rx:2.5,ry:2.5,attrs:n};$e(o,r);let u=s.boundaryFont();u.fontWeight="bold",u.fontSize=u.fontSize+2,u.fontColor=i,J(s)(e.label.text,o,e.x,e.y+e.label.Y,e.width,e.height,{fill:"#444444"},u),e.type&&e.type.text!==""&&(u=s.boundaryFont(),u.fontColor=i,J(s)(e.type.text,o,e.x,e.y+e.type.Y,e.width,e.height,{fill:"#444444"},u)),e.descr&&e.descr.text!==""&&(u=s.boundaryFont(),u.fontSize=u.fontSize-2,u.fontColor=i,J(s)(e.descr.text,o,e.x,e.y+e.descr.Y,e.width,e.height,{fill:"#444444"},u))},"drawBoundary"),n0=(0,d.eW)(function(t,e,s){var p;let o=e.bgColor?e.bgColor:s[e.typeC4Shape.text+"_bg_color"],l=e.borderColor?e.borderColor:s[e.typeC4Shape.text+"_border_color"],a=e.fontColor?e.fontColor:"#FFFFFF",i="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";switch(e.typeC4Shape.text){case"person":i="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAACD0lEQVR4Xu2YoU4EMRCGT+4j8Ai8AhaH4QHgAUjQuFMECUgMIUgwJAgMhgQsAYUiJCiQIBBY+EITsjfTdme6V24v4c8vyGbb+ZjOtN0bNcvjQXmkH83WvYBWto6PLm6v7p7uH1/w2fXD+PBycX1Pv2l3IdDm/vn7x+dXQiAubRzoURa7gRZWd0iGRIiJbOnhnfYBQZNJjNbuyY2eJG8fkDE3bbG4ep6MHUAsgYxmE3nVs6VsBWJSGccsOlFPmLIViMzLOB7pCVO2AtHJMohH7Fh6zqitQK7m0rJvAVYgGcEpe//PLdDz65sM4pF9N7ICcXDKIB5Nv6j7tD0NoSdM2QrU9Gg0ewE1LqBhHR3BBdvj2vapnidjHxD/q6vd7Pvhr31AwcY8eXMTXAKECZZJFXuEq27aLgQK5uLMohCenGGuGewOxSjBvYBqeG6B+Nqiblggdjnc+ZXDy+FNFpFzw76O3UBAROuXh6FoiAcf5g9eTvUgzy0nWg6I8cXHRUpg5bOVBCo+KDpFajOf23GgPme7RSQ+lacIENUgJ6gg1k6HjgOlqnLqip4tEuhv0hNEMXUD0clyXE3p6pZA0S2nnvTlXwLJEZWlb7cTQH1+USgTN4VhAenm/wea1OCAOmqo6fE1WCb9WSKBah+rbUWPWAmE2Rvk0ApiB45eOyNAzU8xcTvj8KvkKEoOaIYeHNA3ZuygAvFMUO0AAAAASUVORK5CYII=";break;case"external_person":i="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAIAAADYYG7QAAAB6ElEQVR4Xu2YLY+EMBCG9+dWr0aj0Wg0Go1Go0+j8Xdv2uTCvv1gpt0ebHKPuhDaeW4605Z9mJvx4AdXUyTUdd08z+u6flmWZRnHsWkafk9DptAwDPu+f0eAYtu2PEaGWuj5fCIZrBAC2eLBAnRCsEkkxmeaJp7iDJ2QMDdHsLg8SxKFEJaAo8lAXnmuOFIhTMpxxKATebo4UiFknuNo4OniSIXQyRxEA3YsnjGCVEjVXD7yLUAqxBGUyPv/Y4W2beMgGuS7kVQIBycH0fD+oi5pezQETxdHKmQKGk1eQEYldK+jw5GxPfZ9z7Mk0Qnhf1W1m3w//EUn5BDmSZsbR44QQLBEqrBHqOrmSKaQAxdnLArCrxZcM7A7ZKs4ioRq8LFC+NpC3WCBJsvpVw5edm9iEXFuyNfxXAgSwfrFQ1c0iNda8AdejvUgnktOtJQQxmcfFzGglc5WVCj7oDgFqU18boeFSs52CUh8LE8BIVQDT1ABrB0HtgSEYlX5doJnCwv9TXocKCaKbnwhdDKPq4lf3SwU3HLq4V/+WYhHVMa/3b4IlfyikAduCkcBc7mQ3/z/Qq/cTuikhkzB12Ae/mcJC9U+Vo8Ej1gWAtgbeGgFsAMHr50BIWOLCbezvhpBFUdY6EJuJ/QDW0XoMX60zZ0AAAAASUVORK5CYII=";break}const n=t.append("g");n.attr("class","person-man");const r=(0,Q.kc)();switch(e.typeC4Shape.text){case"person":case"external_person":case"system":case"external_system":case"container":case"external_container":case"component":case"external_component":r.x=e.x,r.y=e.y,r.fill=o,r.width=e.width,r.height=e.height,r.stroke=l,r.rx=2.5,r.ry=2.5,r.attrs={"stroke-width":.5},$e(n,r);break;case"system_db":case"external_system_db":case"container_db":case"external_container_db":case"component_db":case"external_component_db":n.append("path").attr("fill",o).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,-10 half,-10 half,-10c0,0 half,0 half,10l0,heightc0,10 -half,10 -half,10c0,0 -half,0 -half,-10l0,-height".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2).replaceAll("height",e.height)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc0,10 half,10 half,10c0,0 half,0 half,-10".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("half",e.width/2));break;case"system_queue":case"external_system_queue":case"container_queue":case"external_container_queue":case"component_queue":case"external_component_queue":n.append("path").attr("fill",o).attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startylwidth,0c5,0 5,half 5,halfc0,0 0,half -5,halfl-width,0c-5,0 -5,-half -5,-halfc0,0 0,-half 5,-half".replaceAll("startx",e.x).replaceAll("starty",e.y).replaceAll("width",e.width).replaceAll("half",e.height/2)),n.append("path").attr("fill","none").attr("stroke-width","0.5").attr("stroke",l).attr("d","Mstartx,startyc-5,0 -5,half -5,halfc0,half 5,half 5,half".replaceAll("startx",e.x+e.width).replaceAll("starty",e.y).replaceAll("half",e.height/2));break}let u=f0(s,e.typeC4Shape.text);switch(n.append("text").attr("fill",a).attr("font-family",u.fontFamily).attr("font-size",u.fontSize-2).attr("font-style","italic").attr("lengthAdjust","spacing").attr("textLength",e.typeC4Shape.width).attr("x",e.x+e.width/2-e.typeC4Shape.width/2).attr("y",e.y+e.typeC4Shape.Y).text("<<"+e.typeC4Shape.text+">>"),e.typeC4Shape.text){case"person":case"external_person":pt(n,48,48,e.x+e.width/2-24,e.y+e.image.Y,i);break}let f=s[e.typeC4Shape.text+"Font"]();return f.fontWeight="bold",f.fontSize=f.fontSize+2,f.fontColor=a,J(s)(e.label.text,n,e.x,e.y+e.label.Y,e.width,e.height,{fill:a},f),f=s[e.typeC4Shape.text+"Font"](),f.fontColor=a,e.techn&&((p=e.techn)==null?void 0:p.text)!==""?J(s)(e.techn.text,n,e.x,e.y+e.techn.Y,e.width,e.height,{fill:a,"font-style":"italic"},f):e.type&&e.type.text!==""&&J(s)(e.type.text,n,e.x,e.y+e.type.Y,e.width,e.height,{fill:a,"font-style":"italic"},f),e.descr&&e.descr.text!==""&&(f=s.personFont(),f.fontColor=a,J(s)(e.descr.text,n,e.x,e.y+e.descr.Y,e.width,e.height,{fill:a},f)),e.height},"drawC4Shape"),i0=(0,d.eW)(function(t){t.append("defs").append("symbol").attr("id","database").attr("fill-rule","evenodd").attr("clip-rule","evenodd").append("path").attr("transform","scale(.5)").attr("d","M12.258.001l.256.***************.**************.***************.***************.**************.***************.***************.**************.***************.**************.***************.***************.**************.**************.***************.**************.**************.**************.***************.**************.***************.***************.***************.*************.***************.***************.***************.**************.***************.**************.*************.*************.***************.***************.**************.***************.***************.***************.**************.***************.***************.***************.***************.***************.045.001.045v17l-.001.045-.002.045-.004.045-.006.045-.007.045-.009.044-.011.045-.012.044-.013.044-.015.044-.017.043-.018.044-.02.043-.021.043-.023.043-.024.043-.026.043-.027.042-.029.042-.03.042-.032.042-.033.042-.034.041-.036.041-.037.041-.039.041-.04.041-.041.04-.043.04-.044.04-.045.04-.047.039-.048.039-.05.039-.051.039-.052.038-.053.038-.055.038-.055.038-.058.037-.058.037-.06.037-.06.036-.062.036-.064.036-.064.036-.066.035-.067.035-.068.035-.069.035-.07.034-.071.034-.073.033-.074.033-.15.066-.155.064-.16.063-.163.061-.168.06-.172.059-.175.057-.18.056-.183.054-.187.053-.191.051-.194.05-.198.048-.201.046-.205.045-.208.043-.211.041-.214.04-.217.038-.22.036-.223.034-.225.032-.229.031-.231.028-.233.027-.236.024-.239.023-.241.02-.242.019-.246.016-.247.015-.249.012-.251.01-.253.008-.255.005-.256.004-.258.001-.258-.001-.256-.004-.255-.005-.253-.008-.251-.01-.249-.012-.247-.015-.245-.016-.243-.019-.241-.02-.238-.023-.236-.024-.234-.027-.231-.028-.228-.031-.226-.032-.223-.034-.22-.036-.217-.038-.214-.04-.211-.041-.208-.043-.204-.045-.201-.046-.198-.048-.195-.05-.19-.051-.187-.053-.184-.054-.179-.056-.176-.057-.172-.059-.167-.06-.164-.061-.159-.063-.155-.064-.151-.066-.074-.033-.072-.033-.072-.034-.07-.034-.069-.035-.068-.035-.067-.035-.066-.035-.064-.036-.063-.036-.062-.036-.061-.036-.06-.037-.058-.037-.057-.037-.056-.038-.055-.038-.053-.038-.052-.038-.051-.039-.049-.039-.049-.039-.046-.039-.046-.04-.044-.04-.043-.04-.041-.04-.04-.041-.039-.041-.037-.041-.036-.041-.034-.041-.033-.042-.032-.042-.03-.042-.029-.042-.027-.042-.026-.043-.024-.043-.023-.043-.021-.043-.02-.043-.018-.044-.017-.043-.015-.044-.013-.044-.012-.044-.011-.045-.009-.044-.007-.045-.006-.045-.004-.045-.002-.045-.001-.045v-17l.001-.045.002-.045.004-.045.006-.045.007-.045.009-.044.011-.045.012-.044.013-.044.015-.044.017-.043.018-.044.02-.043.021-.043.023-.043.024-.043.026-.043.027-.042.029-.042.03-.042.032-.042.033-.042.034-.041.036-.041.037-.041.039-.041.04-.041.041-.04.043-.04.044-.04.046-.04.046-.039.049-.039.049-.039.051-.039.052-.038.053-.038.055-.038.056-.038.057-.037.058-.037.06-.037.061-.036.062-.036.063-.036.064-.036.066-.035.067-.035.068-.035.069-.035.07-.034.072-.034.072-.033.074-.033.151-.066.155-.064.159-.063.164-.061.167-.06.172-.059.176-.057.179-.056.184-.054.187-.053.19-.051.195-.05.198-.048.201-.046.204-.045.208-.043.211-.041.214-.04.217-.038.22-.036.223-.034.226-.032.228-.031.231-.028.234-.027.236-.024.238-.023.241-.02.243-.019.245-.016.247-.015.249-.012.251-.01.253-.008.255-.005.256-.004.258-.001.258.001zm-9.258 20.499v.01l.001.021.003.021.004.022.005.021.006.022.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.023.018.024.019.024.021.024.022.025.023.024.024.025.052.049.056.05.061.051.066.051.07.051.075.051.079.052.084.052.088.052.092.052.097.052.102.051.105.052.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.048.144.049.147.047.152.047.155.047.16.045.163.045.167.043.171.043.176.041.178.041.183.039.187.039.19.037.194.035.197.035.202.033.204.031.209.03.212.029.216.027.219.025.222.024.226.021.23.02.233.018.236.016.24.015.243.012.246.01.249.008.253.005.256.004.259.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.021.224-.024.22-.026.216-.027.212-.028.21-.031.205-.031.202-.034.198-.034.194-.036.191-.037.187-.039.183-.04.179-.04.175-.042.172-.043.168-.044.163-.045.16-.046.155-.046.152-.047.148-.048.143-.049.139-.049.136-.05.131-.05.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.053.083-.051.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.05.023-.024.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.023.01-.022.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.127l-.077.055-.08.053-.083.054-.085.053-.087.052-.09.052-.093.051-.095.05-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.045-.118.044-.12.043-.122.042-.124.042-.126.041-.128.04-.13.04-.132.038-.134.038-.135.037-.138.037-.139.035-.142.035-.143.034-.144.033-.147.032-.148.031-.15.03-.151.03-.153.029-.154.027-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.01-.179.008-.179.008-.181.006-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.006-.179-.008-.179-.008-.178-.01-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.027-.153-.029-.151-.03-.15-.03-.148-.031-.146-.032-.145-.033-.143-.034-.141-.035-.14-.035-.137-.037-.136-.037-.134-.038-.132-.038-.13-.04-.128-.04-.126-.041-.124-.042-.122-.042-.12-.044-.117-.043-.116-.045-.113-.045-.112-.046-.109-.047-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.05-.093-.052-.09-.051-.087-.052-.085-.053-.083-.054-.08-.054-.077-.054v4.127zm0-5.654v.011l.001.021.003.021.004.021.005.022.006.022.007.022.009.022.01.022.011.023.012.023.013.023.015.024.016.023.017.024.018.024.019.024.021.024.022.024.023.025.024.024.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.052.11.051.114.051.119.052.123.05.127.051.131.05.135.049.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.044.171.042.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.022.23.02.233.018.236.016.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.012.241-.015.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.048.139-.05.136-.049.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.051.051-.049.023-.025.023-.024.021-.025.02-.024.019-.024.018-.024.017-.024.015-.023.014-.023.013-.024.012-.022.01-.023.01-.023.008-.022.006-.022.006-.022.004-.021.004-.022.001-.021.001-.021v-4.139l-.077.054-.08.054-.083.054-.085.052-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.049-.105.048-.106.047-.109.047-.111.046-.114.045-.115.044-.118.044-.12.044-.122.042-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.035-.143.033-.144.033-.147.033-.148.031-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.025-.161.024-.162.023-.163.022-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.011-.178.009-.179.009-.179.007-.181.007-.182.005-.182.004-.184.003-.184.002h-.37l-.184-.002-.184-.003-.182-.004-.182-.005-.181-.007-.179-.007-.179-.009-.178-.009-.176-.011-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.022-.162-.023-.161-.024-.159-.025-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.031-.146-.033-.145-.033-.143-.033-.141-.035-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.04-.126-.041-.124-.042-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.047-.105-.048-.102-.049-.1-.049-.097-.05-.095-.051-.093-.051-.09-.051-.087-.053-.085-.052-.083-.054-.08-.054-.077-.054v4.139zm0-5.666v.011l.001.02.003.022.004.021.005.022.006.021.007.022.009.023.01.022.011.023.012.023.013.023.015.023.016.024.017.024.018.023.019.024.021.025.022.024.023.024.024.025.052.05.056.05.061.05.066.051.07.051.075.052.079.051.084.052.088.052.092.052.097.052.102.052.105.051.11.052.114.051.119.051.123.051.127.05.131.05.135.05.139.049.144.048.147.048.152.047.155.046.16.045.163.045.167.043.171.043.176.042.178.04.183.04.187.038.19.037.194.036.197.034.202.033.204.032.209.03.212.028.216.027.219.025.222.024.226.021.23.02.233.018.236.017.24.014.243.012.246.01.249.008.253.006.256.003.259.001.26-.001.257-.003.254-.006.25-.008.247-.01.244-.013.241-.014.237-.016.233-.018.231-.02.226-.022.224-.024.22-.025.216-.027.212-.029.21-.03.205-.032.202-.033.198-.035.194-.036.191-.037.187-.039.183-.039.179-.041.175-.042.172-.043.168-.044.163-.045.16-.045.155-.047.152-.047.148-.048.143-.049.139-.049.136-.049.131-.051.126-.05.123-.051.118-.052.114-.051.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.052.07-.051.065-.051.06-.051.056-.05.051-.049.023-.025.023-.025.021-.024.02-.024.019-.024.018-.024.017-.024.015-.023.014-.024.013-.023.012-.023.01-.022.01-.023.008-.022.006-.022.006-.022.004-.022.004-.021.001-.021.001-.021v-4.153l-.077.054-.08.054-.083.053-.085.053-.087.053-.09.051-.093.051-.095.051-.097.05-.1.049-.102.048-.105.048-.106.048-.109.046-.111.046-.114.046-.115.044-.118.044-.12.043-.122.043-.124.042-.126.041-.128.04-.13.039-.132.039-.134.038-.135.037-.138.036-.139.036-.142.034-.143.034-.144.033-.147.032-.148.032-.15.03-.151.03-.153.028-.154.028-.156.027-.158.026-.159.024-.161.024-.162.023-.163.023-.165.021-.166.02-.167.019-.169.018-.169.017-.171.016-.173.015-.173.014-.175.013-.175.012-.177.01-.178.01-.179.009-.179.007-.181.006-.182.006-.182.004-.184.003-.184.001-.185.001-.185-.001-.184-.001-.184-.003-.182-.004-.182-.006-.181-.006-.179-.007-.179-.009-.178-.01-.176-.01-.176-.012-.175-.013-.173-.014-.172-.015-.171-.016-.17-.017-.169-.018-.167-.019-.166-.02-.165-.021-.163-.023-.162-.023-.161-.024-.159-.024-.157-.026-.156-.027-.155-.028-.153-.028-.151-.03-.15-.03-.148-.032-.146-.032-.145-.033-.143-.034-.141-.034-.14-.036-.137-.036-.136-.037-.134-.038-.132-.039-.13-.039-.128-.041-.126-.041-.124-.041-.122-.043-.12-.043-.117-.044-.116-.044-.113-.046-.112-.046-.109-.046-.106-.048-.105-.048-.102-.048-.1-.05-.097-.049-.095-.051-.093-.051-.09-.052-.087-.052-.085-.053-.083-.053-.08-.054-.077-.054v4.153zm8.74-8.179l-.257.004-.254.005-.25.008-.247.011-.244.012-.241.014-.237.016-.233.018-.231.021-.226.022-.224.023-.22.026-.216.027-.212.028-.21.031-.205.032-.202.033-.198.034-.194.036-.191.038-.187.038-.183.04-.179.041-.175.042-.172.043-.168.043-.163.045-.16.046-.155.046-.152.048-.148.048-.143.048-.139.049-.136.05-.131.05-.126.051-.123.051-.118.051-.114.052-.11.052-.106.052-.101.052-.096.052-.092.052-.088.052-.083.052-.079.052-.074.051-.07.052-.065.051-.06.05-.056.05-.051.05-.023.025-.023.024-.021.024-.02.025-.019.024-.018.024-.017.023-.015.024-.014.023-.013.023-.012.023-.01.023-.01.022-.008.022-.006.023-.006.021-.004.022-.004.021-.001.021-.001.021.001.021.001.021.004.021.004.022.006.021.006.023.008.022.01.022.01.023.012.023.013.023.014.023.015.024.017.023.018.024.019.024.02.025.021.024.023.024.023.025.051.05.056.05.06.05.065.051.07.052.074.051.079.052.083.052.088.052.092.052.096.052.101.052.106.052.11.052.114.052.118.051.123.051.126.051.131.05.136.05.139.049.143.048.148.048.152.048.155.046.16.046.163.045.168.043.172.043.175.042.179.041.183.04.187.038.191.038.194.036.198.034.202.033.205.032.21.031.212.028.216.027.22.026.224.023.226.022.231.021.233.018.237.016.241.014.244.012.247.011.25.008.254.005.257.004.26.001.26-.001.257-.004.254-.005.25-.008.247-.011.244-.012.241-.014.237-.016.233-.018.231-.021.226-.022.224-.023.22-.026.216-.027.212-.028.21-.031.205-.032.202-.033.198-.034.194-.036.191-.038.187-.038.183-.04.179-.041.175-.042.172-.043.168-.043.163-.045.16-.046.155-.046.152-.048.148-.048.143-.048.139-.049.136-.05.131-.05.126-.051.123-.051.118-.051.114-.052.11-.052.106-.052.101-.052.096-.052.092-.052.088-.052.083-.052.079-.052.074-.051.07-.052.065-.051.06-.05.056-.05.051-.05.023-.025.023-.024.021-.024.02-.025.019-.024.018-.024.017-.023.015-.024.014-.023.013-.023.012-.023.01-.023.01-.022.008-.022.006-.023.006-.021.004-.022.004-.021.001-.021.001-.021-.001-.021-.001-.021-.004-.021-.004-.022-.006-.021-.006-.023-.008-.022-.01-.022-.01-.023-.012-.023-.013-.023-.014-.023-.015-.024-.017-.023-.018-.024-.019-.024-.02-.025-.021-.024-.023-.024-.023-.025-.051-.05-.056-.05-.06-.05-.065-.051-.07-.052-.074-.051-.079-.052-.083-.052-.088-.052-.092-.052-.096-.052-.101-.052-.106-.052-.11-.052-.114-.052-.118-.051-.123-.051-.126-.051-.131-.05-.136-.05-.139-.049-.143-.048-.148-.048-.152-.048-.155-.046-.16-.046-.163-.045-.168-.043-.172-.043-.175-.042-.179-.041-.183-.04-.187-.038-.191-.038-.194-.036-.198-.034-.202-.033-.205-.032-.21-.031-.212-.028-.216-.027-.22-.026-.224-.023-.226-.022-.231-.021-.233-.018-.237-.016-.241-.014-.244-.012-.247-.011-.25-.008-.254-.005-.257-.004-.26-.001-.26.001z")},"insertDatabaseIcon"),s0=(0,d.eW)(function(t){t.append("defs").append("symbol").attr("id","computer").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M2 2v13h20v-13h-20zm18 11h-16v-9h16v9zm-10.228 6l.466-1h3.524l.467 1h-4.457zm14.228 3h-24l2-6h2.104l-1.33 4h18.45l-1.297-4h2.073l2 6zm-5-10h-14v-7h14v7z")},"insertComputerIcon"),l0=(0,d.eW)(function(t){t.append("defs").append("symbol").attr("id","clock").attr("width","24").attr("height","24").append("path").attr("transform","scale(.5)").attr("d","M12 2c5.514 0 10 4.486 10 10s-4.486 10-10 10-10-4.486-10-10 4.486-10 10-10zm0-2c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12-5.373-12-12-12zm5.848 12.459c.202.038.202.333.001.372-1.907.361-6.045 1.111-6.547 1.111-.719 0-1.301-.582-1.301-1.301 0-.512.77-5.447 1.125-7.445.034-.192.312-.181.343.014l.985 6.238 5.394 1.011z")},"insertClockIcon"),o0=(0,d.eW)(function(t){t.append("defs").append("marker").attr("id","arrowhead").attr("refX",9).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 0 0 L 10 5 L 0 10 z")},"insertArrowHead"),c0=(0,d.eW)(function(t){t.append("defs").append("marker").attr("id","arrowend").attr("refX",1).attr("refY",5).attr("markerUnits","userSpaceOnUse").attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").append("path").attr("d","M 10 0 L 0 5 L 10 10 z")},"insertArrowEnd"),h0=(0,d.eW)(function(t){t.append("defs").append("marker").attr("id","filled-head").attr("refX",18).attr("refY",7).attr("markerWidth",20).attr("markerHeight",28).attr("orient","auto").append("path").attr("d","M 18,7 L9,13 L14,7 L9,1 Z")},"insertArrowFilledHead"),u0=(0,d.eW)(function(t){t.append("defs").append("marker").attr("id","sequencenumber").attr("refX",15).attr("refY",15).attr("markerWidth",60).attr("markerHeight",40).attr("orient","auto").append("circle").attr("cx",15).attr("cy",15).attr("r",6)},"insertDynamicNumber"),d0=(0,d.eW)(function(t){const s=t.append("defs").append("marker").attr("id","crosshead").attr("markerWidth",15).attr("markerHeight",8).attr("orient","auto").attr("refX",16).attr("refY",4);s.append("path").attr("fill","black").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 9,2 V 6 L16,4 Z"),s.append("path").attr("fill","none").attr("stroke","#000000").style("stroke-dasharray","0, 0").attr("stroke-width","1px").attr("d","M 0,1 L 6,7 M 6,1 L 0,7")},"insertArrowCrossHead"),f0=(0,d.eW)((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]}),"getC4ShapeFont"),J=function(){function t(l,a,i,n,r,u,f){const p=a.append("text").attr("x",i+r/2).attr("y",n+u/2+5).style("text-anchor","middle").text(l);o(p,f)}(0,d.eW)(t,"byText");function e(l,a,i,n,r,u,f,p){const{fontSize:b,fontFamily:E,fontWeight:S}=p,M=l.split(d.SY.lineBreakRegex);for(let I=0;I<M.length;I++){const j=I*b-b*(M.length-1)/2,H=a.append("text").attr("x",i+r/2).attr("y",n).style("text-anchor","middle").attr("dominant-baseline","middle").style("font-size",b).style("font-weight",S).style("font-family",E);H.append("tspan").attr("dy",j).text(M[I]).attr("alignment-baseline","mathematical"),o(H,f)}}(0,d.eW)(e,"byTspan");function s(l,a,i,n,r,u,f,p){const b=a.append("switch"),S=b.append("foreignObject").attr("x",i).attr("y",n).attr("width",r).attr("height",u).append("xhtml:div").style("display","table").style("height","100%").style("width","100%");S.append("div").style("display","table-cell").style("text-align","center").style("vertical-align","middle").text(l),e(l,b,i,n,r,u,f,p),o(S,f)}(0,d.eW)(s,"byFo");function o(l,a){for(const i in a)a.hasOwnProperty(i)&&l.attr(i,a[i])}return(0,d.eW)(o,"_setTextAttrs"),function(l){return l.textPlacement==="fo"?s:l.textPlacement==="old"?t:e}}(),G={drawRect:$e,drawBoundary:r0,drawC4Shape:n0,drawRels:a0,drawImage:pt,insertArrowHead:o0,insertArrowEnd:c0,insertArrowFilledHead:h0,insertDynamicNumber:u0,insertArrowCrossHead:d0,insertDatabaseIcon:i0,insertComputerIcon:s0,insertClockIcon:l0},Fe=0,Ve=0,yt=4,et=2;Pe.yy=Ze;var _={},bt=(De=class{constructor(e){this.name="",this.data={},this.data.startx=void 0,this.data.stopx=void 0,this.data.starty=void 0,this.data.stopy=void 0,this.data.widthLimit=void 0,this.nextData={},this.nextData.startx=void 0,this.nextData.stopx=void 0,this.nextData.starty=void 0,this.nextData.stopy=void 0,this.nextData.cnt=0,tt(e.db.getConfig())}setData(e,s,o,l){this.nextData.startx=this.data.startx=e,this.nextData.stopx=this.data.stopx=s,this.nextData.starty=this.data.starty=o,this.nextData.stopy=this.data.stopy=l}updateVal(e,s,o,l){e[s]===void 0?e[s]=o:e[s]=l(o,e[s])}insert(e){this.nextData.cnt=this.nextData.cnt+1;let s=this.nextData.startx===this.nextData.stopx?this.nextData.stopx+e.margin:this.nextData.stopx+e.margin*2,o=s+e.width,l=this.nextData.starty+e.margin*2,a=l+e.height;(s>=this.data.widthLimit||o>=this.data.widthLimit||this.nextData.cnt>yt)&&(s=this.nextData.startx+e.margin+_.nextLinePaddingX,l=this.nextData.stopy+e.margin*2,this.nextData.stopx=o=s+e.width,this.nextData.starty=this.nextData.stopy,this.nextData.stopy=a=l+e.height,this.nextData.cnt=1),e.x=s,e.y=l,this.updateVal(this.data,"startx",s,Math.min),this.updateVal(this.data,"starty",l,Math.min),this.updateVal(this.data,"stopx",o,Math.max),this.updateVal(this.data,"stopy",a,Math.max),this.updateVal(this.nextData,"startx",s,Math.min),this.updateVal(this.nextData,"starty",l,Math.min),this.updateVal(this.nextData,"stopx",o,Math.max),this.updateVal(this.nextData,"stopy",a,Math.max)}init(e){this.name="",this.data={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,widthLimit:void 0},this.nextData={startx:void 0,stopx:void 0,starty:void 0,stopy:void 0,cnt:0},tt(e.db.getConfig())}bumpLastMargin(e){this.data.stopx+=e,this.data.stopy+=e}},(0,d.eW)(De,"Bounds"),De),tt=(0,d.eW)(function(t){(0,d.Yc)(_,t),t.fontFamily&&(_.personFontFamily=_.systemFontFamily=_.messageFontFamily=t.fontFamily),t.fontSize&&(_.personFontSize=_.systemFontSize=_.messageFontSize=t.fontSize),t.fontWeight&&(_.personFontWeight=_.systemFontWeight=_.messageFontWeight=t.fontWeight)},"setConf"),Le=(0,d.eW)((t,e)=>({fontFamily:t[e+"FontFamily"],fontSize:t[e+"FontSize"],fontWeight:t[e+"FontWeight"]}),"c4ShapeFont"),Xe=(0,d.eW)(t=>({fontFamily:t.boundaryFontFamily,fontSize:t.boundaryFontSize,fontWeight:t.boundaryFontWeight}),"boundaryFont"),p0=(0,d.eW)(t=>({fontFamily:t.messageFontFamily,fontSize:t.messageFontSize,fontWeight:t.messageFontWeight}),"messageFont");function V(t,e,s,o,l){if(!e[t].width)if(s)e[t].text=(0,F.X4)(e[t].text,l,o),e[t].textLines=e[t].text.split(d.SY.lineBreakRegex).length,e[t].width=l,e[t].height=(0,F.XD)(e[t].text,o);else{let a=e[t].text.split(d.SY.lineBreakRegex);e[t].textLines=a.length;let i=0;e[t].height=0,e[t].width=0;for(const n of a)e[t].width=Math.max((0,F.Cq)(n,o),e[t].width),i=(0,F.XD)(n,o),e[t].height=e[t].height+i}}(0,d.eW)(V,"calcC4ShapeTextWH");var gt=(0,d.eW)(function(t,e,s){e.x=s.data.startx,e.y=s.data.starty,e.width=s.data.stopx-s.data.startx,e.height=s.data.stopy-s.data.starty,e.label.y=_.c4ShapeMargin-35;let o=e.wrap&&_.wrap,l=Xe(_);l.fontSize=l.fontSize+2,l.fontWeight="bold";let a=(0,F.Cq)(e.label.text,l);V("label",e,o,l,a),G.drawBoundary(t,e,_)},"drawBoundary"),_t=(0,d.eW)(function(t,e,s,o){let l=0;for(const a of o){l=0;const i=s[a];let n=Le(_,i.typeC4Shape.text);switch(n.fontSize=n.fontSize-2,i.typeC4Shape.width=(0,F.Cq)("\xAB"+i.typeC4Shape.text+"\xBB",n),i.typeC4Shape.height=n.fontSize+2,i.typeC4Shape.Y=_.c4ShapePadding,l=i.typeC4Shape.Y+i.typeC4Shape.height-4,i.image={width:0,height:0,Y:0},i.typeC4Shape.text){case"person":case"external_person":i.image.width=48,i.image.height=48,i.image.Y=l,l=i.image.Y+i.image.height;break}i.sprite&&(i.image.width=48,i.image.height=48,i.image.Y=l,l=i.image.Y+i.image.height);let r=i.wrap&&_.wrap,u=_.width-_.c4ShapePadding*2,f=Le(_,i.typeC4Shape.text);if(f.fontSize=f.fontSize+2,f.fontWeight="bold",V("label",i,r,f,u),i.label.Y=l+8,l=i.label.Y+i.label.height,i.type&&i.type.text!==""){i.type.text="["+i.type.text+"]";let E=Le(_,i.typeC4Shape.text);V("type",i,r,E,u),i.type.Y=l+5,l=i.type.Y+i.type.height}else if(i.techn&&i.techn.text!==""){i.techn.text="["+i.techn.text+"]";let E=Le(_,i.techn.text);V("techn",i,r,E,u),i.techn.Y=l+5,l=i.techn.Y+i.techn.height}let p=l,b=i.label.width;if(i.descr&&i.descr.text!==""){let E=Le(_,i.typeC4Shape.text);V("descr",i,r,E,u),i.descr.Y=l+20,l=i.descr.Y+i.descr.height,b=Math.max(i.label.width,i.descr.width),p=l-i.descr.textLines*5}b=b+_.c4ShapePadding,i.width=Math.max(i.width||_.width,b,_.width),i.height=Math.max(i.height||_.height,p,_.height),i.margin=i.margin||_.c4ShapeMargin,t.insert(i),G.drawC4Shape(e,i,_)}t.bumpLastMargin(_.c4ShapeMargin)},"drawC4ShapeArray"),X=(Me=class{constructor(e,s){this.x=e,this.y=s}},(0,d.eW)(Me,"Point"),Me),xt=(0,d.eW)(function(t,e){let s=t.x,o=t.y,l=e.x,a=e.y,i=s+t.width/2,n=o+t.height/2,r=Math.abs(s-l),u=Math.abs(o-a),f=u/r,p=t.height/t.width,b=null;return o==a&&s<l?b=new X(s+t.width,n):o==a&&s>l?b=new X(s,n):s==l&&o<a?b=new X(i,o+t.height):s==l&&o>a&&(b=new X(i,o)),s>l&&o<a?p>=f?b=new X(s,n+f*t.width/2):b=new X(i-r/u*t.height/2,o+t.height):s<l&&o<a?p>=f?b=new X(s+t.width,n+f*t.width/2):b=new X(i+r/u*t.height/2,o+t.height):s<l&&o>a?p>=f?b=new X(s+t.width,n-f*t.width/2):b=new X(i+t.height/2*r/u,o):s>l&&o>a&&(p>=f?b=new X(s,n-t.width/2*f):b=new X(i-t.height/2*r/u,o)),b},"getIntersectPoint"),y0=(0,d.eW)(function(t,e){let s={x:0,y:0};s.x=e.x+e.width/2,s.y=e.y+e.height/2;let o=xt(t,s);s.x=t.x+t.width/2,s.y=t.y+t.height/2;let l=xt(e,s);return{startPoint:o,endPoint:l}},"getIntersectPoints"),b0=(0,d.eW)(function(t,e,s,o){let l=0;for(let a of e){l=l+1;let i=a.wrap&&_.wrap,n=p0(_);o.db.getC4Type()==="C4Dynamic"&&(a.label.text=l+": "+a.label.text);let u=(0,F.Cq)(a.label.text,n);V("label",a,i,n,u),a.techn&&a.techn.text!==""&&(u=(0,F.Cq)(a.techn.text,n),V("techn",a,i,n,u)),a.descr&&a.descr.text!==""&&(u=(0,F.Cq)(a.descr.text,n),V("descr",a,i,n,u));let f=s(a.from),p=s(a.to),b=y0(f,p);a.startPoint=b.startPoint,a.endPoint=b.endPoint}G.drawRels(t,e,_)},"drawRels");function at(t,e,s,o,l){let a=new bt(l);a.data.widthLimit=s.data.widthLimit/Math.min(et,o.length);for(let[i,n]of o.entries()){let r=0;n.image={width:0,height:0,Y:0},n.sprite&&(n.image.width=48,n.image.height=48,n.image.Y=r,r=n.image.Y+n.image.height);let u=n.wrap&&_.wrap,f=Xe(_);if(f.fontSize=f.fontSize+2,f.fontWeight="bold",V("label",n,u,f,a.data.widthLimit),n.label.Y=r+8,r=n.label.Y+n.label.height,n.type&&n.type.text!==""){n.type.text="["+n.type.text+"]";let S=Xe(_);V("type",n,u,S,a.data.widthLimit),n.type.Y=r+5,r=n.type.Y+n.type.height}if(n.descr&&n.descr.text!==""){let S=Xe(_);S.fontSize=S.fontSize-2,V("descr",n,u,S,a.data.widthLimit),n.descr.Y=r+20,r=n.descr.Y+n.descr.height}if(i==0||i%et===0){let S=s.data.startx+_.diagramMarginX,M=s.data.stopy+_.diagramMarginY+r;a.setData(S,S,M,M)}else{let S=a.data.stopx!==a.data.startx?a.data.stopx+_.diagramMarginX:a.data.startx,M=a.data.starty;a.setData(S,S,M,M)}a.name=n.alias;let p=l.db.getC4ShapeArray(n.alias),b=l.db.getC4ShapeKeys(n.alias);b.length>0&&_t(a,t,p,b),e=n.alias;let E=l.db.getBoundaries(e);E.length>0&&at(t,e,a,E,l),n.alias!=="global"&&gt(t,n,a),s.data.stopy=Math.max(a.data.stopy+_.c4ShapeMargin,s.data.stopy),s.data.stopx=Math.max(a.data.stopx+_.c4ShapeMargin,s.data.stopx),Fe=Math.max(Fe,s.data.stopx),Ve=Math.max(Ve,s.data.stopy)}}(0,d.eW)(at,"drawInsideBoundary");var g0=(0,d.eW)(function(t,e,s,o){_=(0,d.nV)().c4;const l=(0,d.nV)().securityLevel;let a;l==="sandbox"&&(a=(0,We.Ys)("#i"+e));const i=l==="sandbox"?(0,We.Ys)(a.nodes()[0].contentDocument.body):(0,We.Ys)("body");let n=o.db;o.db.setWrap(_.wrap),yt=n.getC4ShapeInRow(),et=n.getC4BoundaryInRow(),d.cM.debug(`C:${JSON.stringify(_,null,2)}`);const r=l==="sandbox"?i.select(`[id="${e}"]`):(0,We.Ys)(`[id="${e}"]`);G.insertComputerIcon(r),G.insertDatabaseIcon(r),G.insertClockIcon(r);let u=new bt(o);u.setData(_.diagramMarginX,_.diagramMarginX,_.diagramMarginY,_.diagramMarginY),u.data.widthLimit=screen.availWidth,Fe=_.diagramMarginX,Ve=_.diagramMarginY;const f=o.db.getTitle();let p=o.db.getBoundaries("");at(r,"",u,p,o),G.insertArrowHead(r),G.insertArrowEnd(r),G.insertArrowCrossHead(r),G.insertArrowFilledHead(r),b0(r,o.db.getRels(),o.db.getC4Shape,o),u.data.stopx=Fe,u.data.stopy=Ve;const b=u.data;let S=b.stopy-b.starty+2*_.diagramMarginY;const I=b.stopx-b.startx+2*_.diagramMarginX;f&&r.append("text").text(f).attr("x",(b.stopx-b.startx)/2-4*_.diagramMarginX).attr("y",b.starty+_.diagramMarginY),(0,d.v2)(r,S,I,_.useMaxWidth);const j=f?60:0;r.attr("viewBox",b.startx-_.diagramMarginX+" -"+(_.diagramMarginY+j)+" "+I+" "+(S+j)),d.cM.debug("models:",b)},"draw"),mt={drawPersonOrSystemArray:_t,drawBoundary:gt,setConf:tt,draw:g0},_0=(0,d.eW)(t=>`.person {
    stroke: ${t.personBorder};
    fill: ${t.personBkg};
  }
`,"getStyles"),x0=_0,m0={parser:Je,db:Ze,renderer:mt,styles:x0,init:(0,d.eW)(({c4:t,wrap:e})=>{mt.setConf(t),Ze.setWrap(e)},"init")}},85163:function(Ot,Ke,q){q.d(Ke,{AD:function(){return K},AE:function(){return Pe},Mu:function(){return d},O:function(){return We},kc:function(){return Y},rB:function(){return Je},yU:function(){return Ge}});var Q=q(29134),F=q(33565),d=(0,Q.eW)((W,m)=>{const R=W.append("rect");if(R.attr("x",m.x),R.attr("y",m.y),R.attr("fill",m.fill),R.attr("stroke",m.stroke),R.attr("width",m.width),R.attr("height",m.height),m.name&&R.attr("name",m.name),m.rx&&R.attr("rx",m.rx),m.ry&&R.attr("ry",m.ry),m.attrs!==void 0)for(const D in m.attrs)R.attr(D,m.attrs[D]);return m.class&&R.attr("class",m.class),R},"drawRect"),We=(0,Q.eW)((W,m)=>{const R={x:m.startx,y:m.starty,width:m.stopx-m.startx,height:m.stopy-m.starty,fill:m.fill,stroke:m.stroke,class:"rect"};d(W,R).lower()},"drawBackgroundRect"),Ge=(0,Q.eW)((W,m)=>{const R=m.text.replace(Q.Vw," "),D=W.append("text");D.attr("x",m.x),D.attr("y",m.y),D.attr("class","legend"),D.style("text-anchor",m.anchor),m.class&&D.attr("class",m.class);const N=D.append("tspan");return N.attr("x",m.x+m.textMargin*2),N.text(R),D},"drawText"),Pe=(0,Q.eW)((W,m,R,D)=>{const N=W.append("image");N.attr("x",m),N.attr("y",R);const we=(0,F.N)(D);N.attr("xlink:href",we)},"drawImage"),Je=(0,Q.eW)((W,m,R,D)=>{const N=W.append("use");N.attr("x",m),N.attr("y",R);const we=(0,F.N)(D);N.attr("xlink:href",`#${we}`)},"drawEmbeddedImage"),Y=(0,Q.eW)(()=>({x:0,y:0,width:100,height:100,fill:"#EDF2AE",stroke:"#666",anchor:"start",rx:0,ry:0}),"getNoteRect"),K=(0,Q.eW)(()=>({x:0,y:0,width:100,height:100,"text-anchor":"start",style:"#666",textMargin:0,rx:0,ry:0,tspan:!0}),"getTextObj")}}]);
