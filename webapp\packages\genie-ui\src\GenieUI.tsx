import React, { useState, useCallback, memo, useEffect } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import GeneralInput from './components/GeneralInput';
import Slogn from './components/Slogn';
import ChatView from './components/ChatView';
import { productList, defaultProduct, demoList } from './utils/constants';
import { ServiceProvider, ServiceConfig } from './context/ServiceContext';
import { updateRequestConfig } from './utils/request';
import './global.css';

export interface GenieUIProps {
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 初始产品类型 */
  initialProduct?: CHAT.Product;
  /** 自定义产品列表 */
  customProductList?: CHAT.Product[];
  /** 自定义案例列表 */
  customDemoList?: any[];
  /** 输入框占位符 */
  placeholder?: string;
  /** 是否显示Logo */
  showLogo?: boolean;
  /** 自定义Logo */
  customLogo?: React.ReactNode;
  /** 主题配置 */
  theme?: {
    primaryColor?: string;
    borderRadius?: number;
  };
  /** 服务配置 */
  serviceConfig?: ServiceConfig;
}

const GenieUI: React.FC<GenieUIProps> = memo((props) => {
  const {
    className,
    style,
    initialProduct,
    customProductList,
    customDemoList,
    placeholder,
    showLogo = true,
    customLogo,
    theme,
    serviceConfig
  } = props;

  // 更新请求配置
  useEffect(() => {
    if (serviceConfig) {
      updateRequestConfig({
        baseURL: serviceConfig.baseURL,
        timeout: serviceConfig.timeout,
        headers: serviceConfig.headers,
      });
    }
  }, [serviceConfig]);

  const [inputInfo, setInputInfo] = useState<CHAT.TInputInfo>({
    message: "",
    deepThink: false,
  });
  
  const [product, setProduct] = useState(initialProduct || defaultProduct);

  const changeInputInfo = useCallback((info: CHAT.TInputInfo) => {
    setInputInfo(info);
  }, []);

  const renderContent = () => {
    if (inputInfo.message.length === 0) {
      return (
        <div className="flex flex-col items-center">
          {showLogo && (customLogo || <Slogn />)}
          <div className="w-640 rounded-xl shadow-[0_18px_39px_0_rgba(198,202,240,0.1)]">
            <GeneralInput
              placeholder={placeholder || product.placeholder}
              showBtn={true}
              size="big"
              disabled={false}
              product={product}
              send={changeInputInfo}
            />
          </div>
          <div className="w-640 flex flex-wrap gap-16 mt-[16px]">
            {(customProductList || productList).map((item, i) => {
              const isSelected = item.type === product.type;

              return (
                <div
                  key={i}
                  className={`w-[22%] h-[36px] cursor-pointer flex items-center justify-center border rounded-lg transition-all duration-200 ${
                    isSelected
                      ? "border-blue-500 text-blue-500"
                      : "border-gray-200 text-gray-600"
                  }`}
                  onClick={() => setProduct(item)}
                >
                  <i className={`font_family ${item.img} ${item.color}`}></i>
                  <div className="ml-[6px]">{item.name}</div>
                </div>
              );
            })}
          </div>
        </div>
      );
    }
    return <ChatView inputInfo={inputInfo} product={product} />;
  };

  const themeConfig = theme ? {
    token: {
      colorPrimary: theme.primaryColor || '#4040ff',
      borderRadius: theme.borderRadius || 8,
    }
  } : {};

  return (
    <ServiceProvider config={serviceConfig}>
      <ConfigProvider locale={zhCN} theme={themeConfig}>
        <div
          className={`h-full flex flex-col items-center justify-center ${className || ''}`}
          style={style}
        >
          {renderContent()}
        </div>
      </ConfigProvider>
    </ServiceProvider>
  );
});

GenieUI.displayName = 'GenieUI';

export default GenieUI;
