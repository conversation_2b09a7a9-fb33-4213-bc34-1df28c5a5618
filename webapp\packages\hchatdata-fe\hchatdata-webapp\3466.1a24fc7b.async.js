"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[3466],{51308:function(_,b,n){n.d(b,{Z:function(){return h}});var x=n(10947),c=n(2206),E={title:"title___V3aE9",subTitleContainer:"subTitleContainer___gJqJ1"},m=n(31549),d=x.Z.Paragraph,D=function(C){var f=C.title,O=C.subTitle,P=C.subTitleEditable,y=P===void 0?!1:P,I=C.onSubTitleChange;return(0,m.jsxs)(c.Z,{direction:"vertical",size:2,style:{width:"100%"},children:[(0,m.jsx)("div",{className:E.title,children:f}),(0,m.jsx)("div",{className:<PERSON>.subTitle<PERSON>ontainer,children:y?(0,m.jsx)(d,{editable:{onChange:function(o){I==null||I(o)}},children:O||"\u6DFB\u52A0\u63CF\u8FF0"}):O&&(0,m.jsx)("span",{style:{fontSize:"12px",color:"#7b809a"},children:O})})]})},h=D},94580:function(_,b,n){n.d(b,{c:function(){return x}});var x={layout:"vertical"}},77135:function(_,b,n){n.d(b,{Z:function(){return Z}});var x=n(45332),c=n.n(x),E=n(44194),m=n(61831),d={normalState:"normalState___mWHxa",backNormal:"backNormal___XxVER",maxState:"maxState___Wb1S1",innerWrap:"innerWrap___kzZ2v",fullscreenExitIcon:"fullscreenExitIcon___o1Lj4"},D=n(31549),h=function(f){var O=f.children,P=f.maxRef,y=f.top,I=y===void 0?"50px":y,L=f.isFullScreen,o=f.triggerBackToNormal,e=(0,E.useState)(d.normalState),H=c()(e,2),A=H[0],T=H[1],U=function(){T(d.maxState)},F=function(){T(d.normalState)},w=function(){typeof o=="function"&&o()};return(0,E.useEffect)(function(){L?U():F()},[L]),(0,E.useImperativeHandle)(P,function(){return{changeToMax:U,changeToNormal:F}}),(0,D.jsx)("div",{className:A,style:A===d.maxState?{paddingTop:I}:{},children:(0,D.jsxs)("div",{className:d.innerWrap,style:A===d.maxState?{top:I}:{height:"100%"},children:[(0,D.jsx)("div",{className:d.backNormal,children:(0,D.jsx)(m.Z,{className:d.fullscreenExitIcon,title:"\u9000\u51FA\u5168\u5C4F",onClick:w})}),O]})})},Z=h},94204:function(_,b,n){n.d(b,{Z:function(){return ae}});var x=n(76711),c=n.n(x),E=n(73193),m=n.n(E),d=n(45332),D=n.n(d),h=n(44194),Z=n(90162),C=n(77939),f=n.n(C),O=n(8699),P=n(74910),y=n(17612),I=n(19428),L=n(10947),o=n(39378),e=n(77135),H={sqlEditor:"sqlEditor___LJEVq",fullScreenBtnBox:"fullScreenBtnBox___R5IFy"},A=n(31549),T=function(u){return u.table="table",u.variable="variable",u.column="column",u}(T||{}),U="14px",F="sql",w="100%",$={behavioursEnabled:!0,enableSnippets:!1,enableBasicAutocompletion:!0,enableLiveAutocompletion:!0,autoScrollEditorIntoView:!0,wrap:!0,useWorker:!1};function z(u){var g=(0,h.useRef)(),S=u.hints,i=S===void 0?{}:S,a=u.value,r=u.height,t=r===void 0?w:r,l=u.mode,s=l===void 0?F:l,N=u.isRightTheme,p=N===void 0?!1:N,j=u.sizeChanged,R=u.editorConfig,K=u.fullScreenBtnVisible,Q=K===void 0?!0:K,B=u.isFullScreen,X=B===void 0?!1:B,M=u.onSqlChange,ne=u.onChange,te=u.onSelect,se=u.onCmdEnter,v=u.triggerBackToNormal,G=(0,h.useCallback)((0,o.debounce)(function(){var V;(V=g.current)===null||V===void 0||V.editor.resize()},300),[]),ue=(0,h.useCallback)(function(V){M==null||M(V),ne==null||ne(V)},[]),ce=(0,h.useCallback)((0,o.debounce)(function(V){var fe,ie=(fe=g.current)===null||fe===void 0?void 0:fe.editor.session.doc.getTextRange(V.getRange()),Ie=(ie==null?void 0:ie.length)>1?ie:null;te==null||te(Ie)},300),[]),de=(0,h.useMemo)(function(){return[{name:"execute",bindKey:{win:"Ctrl-Enter",mac:"Command-Enter"},exec:se}]},[]);(0,h.useEffect)(function(){G()},[j,t]),(0,h.useEffect)(function(){Y(i)},[i]);var W=(0,h.useState)(X),oe=D()(W,2),he=oe[0],ve=oe[1];(0,h.useEffect)(function(){ve(X)},[X]);var me=function(){ve(!1),v==null||v()};return(0,A.jsxs)("div",{className:H.sqlEditor,style:{height:t},children:[(0,A.jsx)(e.Z,{isFullScreen:he,top:"".concat(0,"px"),triggerBackToNormal:me,children:(0,A.jsx)(Z.ZP,m()({ref:g,name:"aceEditor",width:"100%",height:"100%",fontSize:U,mode:s,theme:p?"sqlserver":"monokai",value:a,showPrintMargin:!1,highlightActiveLine:!0,setOptions:$,commands:de,onChange:ue,onSelectionChange:ce},R))}),Q&&(0,A.jsx)("span",{className:H.fullScreenBtnBox,onClick:function(){ve(!0)},children:(0,A.jsx)(L.Z.Link,{children:"\u5168\u5C4F\u67E5\u770B"})})]})}function Y(u){var g=f().textCompleter,S=f().keyWordCompleter,i=f().setCompleters,a={identifierRegexps:[/[a-zA-Z_0-9.\-\u00A2-\uFFFF]/],getCompletions:function(l,s,N,p,j){var R=J(u),K=R.tableKeywords,Q=R.tableColumnKeywords,B=R.variableKeywords,X=R.columns;if(p[p.length-1]==="."){var M=p.substring(0,p.length-1),ne=ee(l,M,u),te=K.concat(B,ne,Q[M]||[]);return j(null,te)}j(null,K.concat(B,X))}},r=[g,S,a];i(r)}function J(u){var g=[],S=[],i={},a=[],r=1e3;return Object.keys(u).forEach(function(t){var l=le(t);if(l)g.push({score:r--,value:t,meta:l});else{var s=k(u[t],t),N=s.columnWithTableName,p=s.column;i[t]=N,a.push.apply(a,c()(p)),S.push({name:t,value:t,score:r--,meta:re()})}}),{tableKeywords:S,tableColumnKeywords:i,variableKeywords:g,columns:a}}function k(u,g){var S=100,i=[],a=[];return u.forEach(function(r){var t={score:S--,meta:q()};i.push(m()({caption:"".concat(g,".").concat(r),name:"".concat(g,".").concat(r),value:"".concat(g,".").concat(r)},t)),a.push(m()({value:r,name:r},t))}),{columnWithTableName:i,column:a}}function ee(u,g,S){var i=u.getSession().getValue(),a=Object.keys(S).find(function(l){var s=new RegExp(".+".concat(l,"\\s*(as|AS)?(?=\\s+").concat(g,"\\s*)"),"im");return s.test(i)});if(!a)return[];var r=k(S[a],g),t=r.columnWithTableName;return t}function le(u){return u.startsWith("$")&&u.endsWith("$")&&T.variable}function re(u){return T.table}function q(u){return T.column}var ae=z},60996:function(_,b,n){n.d(b,{Z:function(){return H}});var x=n(90819),c=n.n(x),E=n(89933),m=n.n(E),d=n(45332),D=n.n(d),h=n(7477),Z=n(16156),C=n(44194),f=n(9113),O=n(12322),P=n(48965),y={collectDashboard:"collectDashboard___o1pSS",dashboardCollected:"dashboardCollected___bLBw0"},I=n(31549),L=function(T){var U=T.star,F=U===void 0?!1:U,w=T.onToggleCollect,$=(0,C.useState)(F),z=D()($,2),Y=z[0],J=z[1];return(0,C.useEffect)(function(){J(F)},[F]),(0,I.jsx)("div",{className:"".concat(y.collectDashboard," ").concat(Y===!0?"dashboardCollected":""),onClick:function(ee){ee.stopPropagation(),J(!Y),w(!Y)},children:Y===!1?(0,I.jsx)(O.Z,{}):(0,I.jsx)(P.Z,{style:{color:"#eac54f"}})})},o=L,e=function(T){var U=T.indicatorId,F=T.type,w=F===void 0?"metric":F,$=T.initState,z=$===void 0?!1:$,Y=(0,C.useState)(z),J=D()(Y,2),k=J[0],ee=J[1];(0,C.useEffect)(function(){ee(z)},[z]);var le=function(){var re=m()(c()().mark(function q(ae,u){var g,S,i;return c()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,(0,f.Pl)({id:ae,type:w,state:u});case 2:g=r.sent,S=g.code,i=g.msg,S===200?ee(u):h.ZP.error(i);case 6:case"end":return r.stop()}},q)}));return function(ae,u){return re.apply(this,arguments)}}();return(0,I.jsx)(Z.Z,{title:"".concat(k?"\u53D6\u6D88":"\u52A0\u5165","\u6536\u85CF"),children:(0,I.jsx)("div",{children:(0,I.jsx)(o,{star:k,onToggleCollect:function(q){le(U,q)}})})})},H=e},97180:function(_,b,n){n.d(b,{D$:function(){return C},DD:function(){return y},Ze:function(){return h},hu:function(){return O},iw:function(){return f},sz:function(){return I},uc:function(){return D},x1:function(){return L}});var x=n(10154),c=n.n(x),E=n(57605),m=n(42520),d=function(o){return o[o.LOW=0]="LOW",o[o.MID=1]="MID",o[o.HIGH=2]="HIGH",o}({}),D=[{label:"\u666E\u901A",value:d.LOW},{label:"\u91CD\u8981",value:d.MID},{label:"\u6838\u5FC3",value:d.HIGH}],h=D.reduce(function(o,e){var H=e.label,A=e.value;return o[A]=H,o},{}),Z={1:"\u662F",0:"\u5426"},C=c()(c()(c()({},d.LOW,"default"),d.MID,"orange"),d.HIGH,"volcano"),f=c()(c()(c()({},E.Z.DATASOURCE,{label:"\u6A21\u578B",value:E.Z.DATASOURCE,color:"cyan"}),E.Z.DIMENSION,{label:"\u7EF4\u5EA6",value:E.Z.DIMENSION,color:"blue"}),E.Z.METRIC,{label:"\u6307\u6807",value:E.Z.METRIC,color:"orange"}),O=c()(c()(c()({},m.NU.DAY,"sys_imp_date"),m.NU.WEEK,"sys_imp_week"),m.NU.MONTH,"sys_imp_month"),P={sys_imp_date:m.NU.DAY,sys_imp_week:m.NU.WEEK,sys_imp_month:m.NU.MONTH},y=function(o){return o.FIELD="FIELD",o.MEASURE="MEASURE",o.METRIC="METRIC",o}({}),I=function(o){return o.FIELD="FIELD",o.DIMENSION="DIMENSION",o.METRIC="METRIC",o}({}),L=c()(c()(c()({},I.FIELD,"\u5B57\u6BB5"),I.DIMENSION,"\u7EF4\u5EA6"),I.METRIC,"\u6307\u6807")},57605:function(_,b,n){n.d(b,{C3:function(){return f},E0:function(){return O},Kr:function(){return m},MU:function(){return L},Mq:function(){return y},Or:function(){return I},Z:function(){return d},cX:function(){return P},fs:function(){return E},lf:function(){return C},nG:function(){return o},sB:function(){return Z},v8:function(){return h}});var x=n(10154),c=n.n(x),E=function(e){return e.TAG="TAG",e.METRIC="METRIC",e}({}),m=function(e){return e.DIMENSION="DIMENSION",e.METRIC="METRIC",e.TAG="TAG",e}({}),d=function(e){return e.DATASOURCE="DATASOURCE",e.DIMENSION="DIMENSION",e.METRIC="METRIC",e.TAG="TAG",e}({}),D=function(e){return e.ATOMIC="\u539F\u5B50\u6307\u6807",e.DERIVED="\u884D\u751F\u6307\u6807",e}({}),h=function(e){return e.initial="--",e.error="\u9519\u8BEF",e.pending="\u7B49\u5F85",e.running="\u6B63\u5728\u6267\u884C",e.success="\u6210\u529F",e.unknown="\u672A\u77E5",e}({}),Z=function(e){return e[e.UNKNOWN=-1]="UNKNOWN",e[e.INITIALIZED=0]="INITIALIZED",e[e.ONLINE=1]="ONLINE",e[e.OFFLINE=2]="OFFLINE",e[e.DELETED=3]="DELETED",e[e.UNAVAILABLE=4]="UNAVAILABLE",e}({}),C=function(e){return e.EQUAL="=",e.IN="IN",e.LIKE="LIKE",e}({}),f=function(e){return e.DIMENSION="DIMENSION",e.TAG="TAG",e.METRIC="METRIC",e.DOMAIN="DOMAIN",e.ENTITY="ENTITY",e.VIEW="VIEW",e.MODEL="MODEL",e.UNKNOWN="UNKNOWN",e}({}),O=c()(c()(c()(c()(c()(c()(c()(c()({},f.DIMENSION,"\u7EF4\u5EA6"),f.TAG,"\u6807\u7B7E"),f.METRIC,"\u6307\u6807"),f.DOMAIN,"\u57DF"),f.ENTITY,"\u5B9E\u4F53"),f.VIEW,"\u89C6\u56FE"),f.MODEL,"\u6A21\u578B"),f.UNKNOWN,"\u672A\u77E5"),P=function(e){return e.ONLINE="ONLINE",e.OFFLINE="OFFLINE",e.DELETED="DELETED",e.INITIALIZED="INITIALIZED",e.UNAVAILABLE="UNAVAILABLE",e.UNKNOWN="UNKNOWN",e}({}),y=c()(c()({},E.TAG,"detailTypeDefaultConfig"),E.METRIC,"aggregateTypeDefaultConfig"),I=function(e){return e.LAST="LAST",e.RECENT="RECENT",e.CURRENT="CURRENT",e}({}),L=function(e){return e.DAY="DAY",e.WEEK="WEEK",e.MONTH="MONTH",e.YEAR="YEAR",e}({}),o=function(e){return e.BLACK_LIST="blackList",e.WHITE_LIST="whiteList",e.RULE_LIST="ruleList",e}({})},88054:function(_,b,n){n.d(b,{H3:function(){return $},Mf:function(){return z},DJ:function(){return J},vX:function(){return le},Ww:function(){return g},YA:function(){return S},$w:function(){return q},mr:function(){return u},Ey:function(){return ae},pV:function(){return re}});var x=n(76711),c=n.n(x),E=n(86222),m=n(73193),d=n.n(m),D=n(20263),h=n(46504),Z=n(44319),C=n(88732),f=n(26574),O=n(51308),P=n(49841),y=n.n(P),I=n(44194),L=n(37069),o=n(31549),e=function(a){var r=Object.assign({},(y()(a),a)),t=(0,I.useRef)(null);(0,I.useEffect)(function(){t.current&&t.current.addEventListener("wheel",l)},[]);var l=function(N){N.stopPropagation(),N.preventDefault()};return(0,o.jsx)(L.Z,d()({ref:t},r))},H=e,A=n(39378),T=n(20221),U=D.Z.Item,F=h.default.TextArea,w=function i(a,r){return a.map(function(t){var l=_objectSpread(_objectSpread({},t),{},{key:t.id,disabled:r,children:t.children?i(t.children,r):[]});return l})},$=function i(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return a==null?void 0:a.map(function(t){var l=t.children,s=t.parentId,N=s===void 0?[]:s,p=r.slice();return p.push(N),l?d()(d()({},t),{},{path:p,children:i(l,p)}):d()(d()({},t),{},{path:p})})},z=function i(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,t=a==null?void 0:a.reduce(function(l,s){if(s.parentId==r){var N=i(a,s.id);N.length&&(s.children=N),s.key=s.id,s.value=s.id,s.title=s.name||s.categoryName,l.push(s)}return l},[]);return t},Y=function i(a){var r=[];return a==null||a.forEach(function(t){t.children&&t.children.length>0&&(r.push(t.id),r=r.concat(i(t.children)))}),r},J=function i(a,r){if(a.length===0)return[];var t=[],l=a.find(function(s){return s.subOrganizations&&(t=t.concat(s.subOrganizations)),s.id===r});return l||i(t,r)},k=function i(a,r,t){if(a.parentId===r)return!0;var l=t.find(function(s){return s.id===a.parentId});return l?i(l,r,t):!1},ee=function(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null,t=[],l=_createForOfIteratorHelper(a),s;try{for(l.s();!(s=l.n()).done;){var N=s.value,p=!0,j=_createForOfIteratorHelper(a),R;try{for(j.s();!(R=j.n()).done;){var K=R.value;if(K.parentId===N.id){p=!1;break}}}catch(Q){j.e(Q)}finally{j.f()}p&&(r===null||k(N,r,a))&&t.push(N)}}catch(Q){l.e(Q)}finally{l.f()}return t},le=function(a){var r=a.reduce(function(t,l){var s,N,p,j,R,K,Q=l.dataType,B=l.name,X=l.comment,M=l.placeholder,ne=l.description,te=l.require,se=l.visible,v=l.sliderConfig;if(se===!1)return t;var G=(0,o.jsx)(h.default,{});switch(Q){case"string":B==="password"?G=(0,o.jsx)(h.default.Password,{placeholder:M}):G=(0,o.jsx)(h.default,{placeholder:M});break;case"password":{G=(0,o.jsx)(h.default.Password,{placeholder:M,visibilityToggle:B!=="apiKey"});break}case"longText":G=(0,o.jsx)(F,{placeholder:M,style:{height:100}});break;case"number":G=(0,o.jsx)(H,{placeholder:M,style:{width:"100%"}});break;case"slider":G=(0,o.jsx)(Z.Z,{min:v!=null&&(s=v.start)!==null&&s!==void 0&&s.value?Number(v==null||(N=v.start)===null||N===void 0?void 0:N.value):0,max:v!=null&&(p=v.end)!==null&&p!==void 0&&p.value?Number(v==null||(j=v.end)===null||j===void 0?void 0:j.value):1,step:v!=null&&v.unit?Number(v==null?void 0:v.unit):.1,marks:{0:(v==null||(R=v.start)===null||R===void 0?void 0:R.text)||"\u7CBE\u786E",1:(v==null||(K=v.end)===null||K===void 0?void 0:K.text)||"\u968F\u673A"}});break;case"bool":return t.push((0,o.jsx)(U,{name:B,label:X,valuePropName:"checked",getValueFromEvent:function(oe){return oe===!0?"true":"false"},getValueProps:function(oe){return{checked:oe==="true"}},children:(0,o.jsx)(C.Z,{})},B)),t;case"list":{var ue=l.candidateValues,ce=ue===void 0?[]:ue,de=ce.map(function(W){return(0,A.isString)(W)?{label:W,value:W}:W!=null&&W.label?W:{label:W,value:W}});G=(0,o.jsx)(f.default,{style:{width:"100%"},options:de,placeholder:M});break}default:G=(0,o.jsx)(h.default,{placeholder:M});break}return t.push((0,o.jsx)(U,{name:B,rules:[{required:!!te,message:"\u8BF7\u8F93\u5165".concat(X)}],label:(0,o.jsx)(O.Z,{title:X,subTitle:ne}),children:G},B)),t},[]);return c()(r)},re=function(a,r){return"".concat(a,"-").concat(r)},q=function(a,r){T.history.push("/model/domain/".concat(a,"/").concat(r))},ae=function(a,r,t){T.history.push("/model/domain/manager/".concat(a,"/").concat(r).concat(t?"/".concat(t):""))},u=function(a,r,t,l){T.history.push("/model/metric/".concat(a,"/").concat(r,"/").concat(t).concat(l?"/".concat(l):""))},g=function(a,r,t){T.history.push("/model/dataset/".concat(a,"/").concat(r).concat(t?"/".concat(t):""))},S=function(a,r,t,l){T.history.push("/model/dimension/".concat(a,"/").concat(r,"/").concat(t).concat(l?"/".concat(l):""))}}}]);
