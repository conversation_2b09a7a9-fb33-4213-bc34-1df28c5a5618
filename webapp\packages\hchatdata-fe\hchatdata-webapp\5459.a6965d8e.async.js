"use strict";(self.webpackChunkhchatdata_fe=self.webpackChunkhchatdata_fe||[]).push([[5459],{20034:function(lt,X,r){r.d(X,{Z:function(){return D}});var Z=r(95687),n=r(44194),F={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 289.1a362.49 362.49 0 00-79.9-115.7 370.83 370.83 0 00-118.2-77.8C610.7 76.6 562.1 67 512 67c-50.1 0-98.7 9.6-144.5 28.5-44.3 18.3-84 44.5-118.2 77.8A363.6 363.6 0 00169.4 289c-19.5 45-29.4 92.8-29.4 142 0 70.6 16.9 140.9 50.1 208.7 26.7 54.5 64 107.6 111 158.1 80.3 86.2 164.5 138.9 188.4 153a43.9 43.9 0 0022.4 6.1c7.8 0 15.5-2 22.4-6.1 23.9-14.1 108.1-66.8 188.4-153 47-50.4 84.3-103.6 111-158.1C867.1 572 884 501.8 884 431.1c0-49.2-9.9-97-29.4-142zM512 880.2c-65.9-41.9-300-207.8-300-449.1 0-77.9 31.1-151.1 87.6-206.3C356.3 169.5 431.7 139 512 139s155.7 30.5 212.4 85.9C780.9 280 812 353.2 812 431.1c0 241.3-234.1 407.2-300 449.1zm0-617.2c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 551c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 439c0-29.9 11.7-58 32.8-79.2C454 338.6 482.1 327 512 327c29.9 0 58 11.6 79.2 32.8C612.4 381 624 409.1 624 439c0 29.9-11.6 58-32.8 79.2z"}}]},name:"environment",theme:"outlined"},x=F,V=r(54183),G=function(J,K){return n.createElement(V.Z,(0,Z.Z)({},J,{ref:K,icon:x}))},H=n.forwardRef(G),D=H},77595:function(lt,X,r){r.d(X,{Z:function(){return bt}});var Z=r(18191),n=r(44194),F=r(51865),x=r.n(F),V=r(70163),G=r(35509),H=r(47506),D=r(41589),tt=r(577),J=r(86211),K=r(56630),st=r(31020),ct=r(58184);const U=n.createContext({}),Vt=U.Consumer;var dt=r(11778),mt=r(55188),et=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(i[a[o]]=e[a[o]]);return i};const gt=e=>{var{prefixCls:t,className:i,avatar:a,title:o,description:c}=e,u=et(e,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:S}=(0,n.useContext)(H.E_),g=S("list",t),O=x()(`${g}-item-meta`,i),C=n.createElement("div",{className:`${g}-item-meta-content`},o&&n.createElement("h4",{className:`${g}-item-meta-title`},o),c&&n.createElement("div",{className:`${g}-item-meta-description`},c));return n.createElement("div",Object.assign({},u,{className:O}),a&&n.createElement("div",{className:`${g}-item-meta-avatar`},a),(o||c)&&C)},nt=n.forwardRef((e,t)=>{const{prefixCls:i,children:a,actions:o,extra:c,styles:u,className:S,classNames:g,colStyle:O}=e,C=et(e,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:P,itemLayout:d}=(0,n.useContext)(U),{getPrefixCls:I,list:p}=(0,n.useContext)(H.E_),E=$=>{var m,b;return x()((b=(m=p==null?void 0:p.item)===null||m===void 0?void 0:m.classNames)===null||b===void 0?void 0:b[$],g==null?void 0:g[$])},N=$=>{var m,b;return Object.assign(Object.assign({},(b=(m=p==null?void 0:p.item)===null||m===void 0?void 0:m.styles)===null||b===void 0?void 0:b[$]),u==null?void 0:u[$])},T=()=>{let $=!1;return n.Children.forEach(a,m=>{typeof m=="string"&&($=!0)}),$&&n.Children.count(a)>1},B=()=>d==="vertical"?!!c:!T(),h=I("list",i),L=o&&o.length>0&&n.createElement("ul",{className:x()(`${h}-item-action`,E("actions")),key:"actions",style:N("actions")},o.map(($,m)=>n.createElement("li",{key:`${h}-item-action-${m}`},$,m!==o.length-1&&n.createElement("em",{className:`${h}-item-action-split`})))),W=P?"div":"li",M=n.createElement(W,Object.assign({},C,P?{}:{ref:t},{className:x()(`${h}-item`,{[`${h}-item-no-flex`]:!B()},S)}),d==="vertical"&&c?[n.createElement("div",{className:`${h}-item-main`,key:"content"},a,L),n.createElement("div",{className:x()(`${h}-item-extra`,E("extra")),key:"extra",style:N("extra")},c)]:[a,L,(0,dt.Tm)(c,{key:"extra"})]);return P?n.createElement(mt.Z,{ref:t,flex:1,style:O},M):M});nt.Meta=gt;var ft=nt,s=r(40044),ut=r(19107),pt=r(88370),$t=r(77167);const vt=e=>{const{listBorderedCls:t,componentCls:i,paddingLG:a,margin:o,itemPaddingSM:c,itemPaddingLG:u,marginLG:S,borderRadiusLG:g}=e;return{[t]:{border:`${(0,s.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:g,[`${i}-header,${i}-footer,${i}-item`]:{paddingInline:a},[`${i}-pagination`]:{margin:`${(0,s.unit)(o)} ${(0,s.unit)(S)}`}},[`${t}${i}-sm`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:c}},[`${t}${i}-lg`]:{[`${i}-item,${i}-header,${i}-footer`]:{padding:u}}}},ht=e=>{const{componentCls:t,screenSM:i,screenMD:a,marginLG:o,marginSM:c,margin:u}=e;return{[`@media screen and (max-width:${a}px)`]:{[t]:{[`${t}-item`]:{[`${t}-item-action`]:{marginInlineStart:o}}},[`${t}-vertical`]:{[`${t}-item`]:{[`${t}-item-extra`]:{marginInlineStart:o}}}},[`@media screen and (max-width: ${i}px)`]:{[t]:{[`${t}-item`]:{flexWrap:"wrap",[`${t}-action`]:{marginInlineStart:c}}},[`${t}-vertical`]:{[`${t}-item`]:{flexWrap:"wrap-reverse",[`${t}-item-main`]:{minWidth:e.contentWidth},[`${t}-item-extra`]:{margin:`auto auto ${(0,s.unit)(u)}`}}}}}},St=e=>{const{componentCls:t,antCls:i,controlHeight:a,minHeight:o,paddingSM:c,marginLG:u,padding:S,itemPadding:g,colorPrimary:O,itemPaddingSM:C,itemPaddingLG:P,paddingXS:d,margin:I,colorText:p,colorTextDescription:E,motionDurationSlow:N,lineWidth:T,headerBg:B,footerBg:h,emptyTextPadding:L,metaMarginBottom:W,avatarMarginRight:M,titleMarginBottom:$,descriptionFontSize:m}=e;return{[t]:Object.assign(Object.assign({},(0,ut.Wf)(e)),{position:"relative","*":{outline:"none"},[`${t}-header`]:{background:B},[`${t}-footer`]:{background:h},[`${t}-header, ${t}-footer`]:{paddingBlock:c},[`${t}-pagination`]:{marginBlockStart:u,[`${i}-pagination-options`]:{textAlign:"start"}},[`${t}-spin`]:{minHeight:o,textAlign:"center"},[`${t}-items`]:{margin:0,padding:0,listStyle:"none"},[`${t}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:g,color:p,[`${t}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${t}-item-meta-avatar`]:{marginInlineEnd:M},[`${t}-item-meta-content`]:{flex:"1 0",width:0,color:p},[`${t}-item-meta-title`]:{margin:`0 0 ${(0,s.unit)(e.marginXXS)} 0`,color:p,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:p,transition:`all ${N}`,"&:hover":{color:O}}},[`${t}-item-meta-description`]:{color:E,fontSize:m,lineHeight:e.lineHeight}},[`${t}-item-action`]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${(0,s.unit)(d)}`,color:E,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${t}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:T,height:e.calc(e.fontHeight).sub(e.calc(e.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},[`${t}-empty`]:{padding:`${(0,s.unit)(S)} 0`,color:E,fontSize:e.fontSizeSM,textAlign:"center"},[`${t}-empty-text`]:{padding:L,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},[`${t}-item-no-flex`]:{display:"block"}}),[`${t}-grid ${i}-col > ${t}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:I,paddingBlock:0,borderBlockEnd:"none"},[`${t}-vertical ${t}-item`]:{alignItems:"initial",[`${t}-item-main`]:{display:"block",flex:1},[`${t}-item-extra`]:{marginInlineStart:u},[`${t}-item-meta`]:{marginBlockEnd:W,[`${t}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:$,color:p,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},[`${t}-item-action`]:{marginBlockStart:S,marginInlineStart:"auto","> li":{padding:`0 ${(0,s.unit)(S)}`,"&:first-child":{paddingInlineStart:0}}}},[`${t}-split ${t}-item`]:{borderBlockEnd:`${(0,s.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${t}-split ${t}-header`]:{borderBlockEnd:`${(0,s.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-split${t}-empty ${t}-footer`]:{borderTop:`${(0,s.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-loading ${t}-spin-nested-loading`]:{minHeight:a},[`${t}-split${t}-something-after-last-item ${i}-spin-container > ${t}-items > ${t}-item:last-child`]:{borderBlockEnd:`${(0,s.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-lg ${t}-item`]:{padding:P},[`${t}-sm ${t}-item`]:{padding:C},[`${t}:not(${t}-vertical)`]:{[`${t}-item-no-flex`]:{[`${t}-item-action`]:{float:"right"}}}}},yt=e=>({contentWidth:220,itemPadding:`${(0,s.unit)(e.paddingContentVertical)} 0`,itemPaddingSM:`${(0,s.unit)(e.paddingContentVerticalSM)} ${(0,s.unit)(e.paddingContentHorizontal)}`,itemPaddingLG:`${(0,s.unit)(e.paddingContentVerticalLG)} ${(0,s.unit)(e.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize});var xt=(0,pt.I$)("List",e=>{const t=(0,$t.mergeToken)(e,{listBorderedCls:`${e.componentCls}-bordered`,minHeight:e.controlHeightLG});return[St(t),vt(t),ht(t)]},yt),Ct=function(e,t){var i={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(i[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)t.indexOf(a[o])<0&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(i[a[o]]=e[a[o]]);return i};function Et(e,t){const{pagination:i=!1,prefixCls:a,bordered:o=!1,split:c=!0,className:u,rootClassName:S,style:g,children:O,itemLayout:C,loadMore:P,grid:d,dataSource:I=[],size:p,header:E,footer:N,loading:T=!1,rowKey:B,renderItem:h,locale:L}=e,W=Ct(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),M=i&&typeof i=="object"?i:{},[$,m]=n.useState(M.defaultCurrent||1),[b,zt]=n.useState(M.defaultPageSize||10),{getPrefixCls:Ot,direction:Pt,className:It,style:Nt}=(0,H.dj)("list"),{renderEmpty:Y}=n.useContext(H.E_),Bt={current:1,total:0,position:"bottom"},at=l=>(v,z)=>{var _;m(v),zt(z),i&&((_=i==null?void 0:i[l])===null||_===void 0||_.call(i,v,z))},Lt=at("onChange"),Mt=at("onShowSizeChange"),jt=(l,v)=>{if(!h)return null;let z;return typeof B=="function"?z=B(l):B?z=l[B]:z=l.key,z||(z=`list-item-${v}`),n.createElement(n.Fragment,{key:z},h(l,v))},Ht=!!(P||i||N),f=Ot("list",a),[Tt,Wt,Zt]=xt(f);let j=T;typeof j=="boolean"&&(j={spinning:j});const Q=!!(j!=null&&j.spinning),Gt=(0,tt.Z)(p);let R="";switch(Gt){case"large":R="lg";break;case"small":R="sm";break;default:break}const Rt=x()(f,{[`${f}-vertical`]:C==="vertical",[`${f}-${R}`]:R,[`${f}-split`]:c,[`${f}-bordered`]:o,[`${f}-loading`]:Q,[`${f}-grid`]:!!d,[`${f}-something-after-last-item`]:Ht,[`${f}-rtl`]:Pt==="rtl"},It,u,S,Wt,Zt),y=(0,V.Z)(Bt,{total:I.length,current:$,pageSize:b},i||{}),At=Math.ceil(y.total/y.pageSize);y.current=Math.min(y.current,At);const ot=i&&n.createElement("div",{className:x()(`${f}-pagination`)},n.createElement(st.Z,Object.assign({align:"end"},y,{onChange:Lt,onShowSizeChange:Mt})));let k=(0,Z.Z)(I);i&&I.length>(y.current-1)*y.pageSize&&(k=(0,Z.Z)(I).splice((y.current-1)*y.pageSize,y.pageSize));const wt=Object.keys(d||{}).some(l=>["xs","sm","md","lg","xl","xxl"].includes(l)),rt=(0,K.Z)(wt),A=n.useMemo(()=>{for(let l=0;l<G.c4.length;l+=1){const v=G.c4[l];if(rt[v])return v}},[rt]),Xt=n.useMemo(()=>{if(!d)return;const l=A&&d[A]?d[A]:d.column;if(l)return{width:`${100/l}%`,maxWidth:`${100/l}%`}},[JSON.stringify(d),A]);let q=Q&&n.createElement("div",{style:{minHeight:53}});if(k.length>0){const l=k.map(jt);q=d?n.createElement(J.Z,{gutter:d.gutter},n.Children.map(l,v=>n.createElement("div",{key:v==null?void 0:v.key,style:Xt},v))):n.createElement("ul",{className:`${f}-items`},l)}else!O&&!Q&&(q=n.createElement("div",{className:`${f}-empty-text`},(L==null?void 0:L.emptyText)||(Y==null?void 0:Y("List"))||n.createElement(D.Z,{componentName:"List"})));const w=y.position,Ft=n.useMemo(()=>({grid:d,itemLayout:C}),[JSON.stringify(d),C]);return Tt(n.createElement(U.Provider,{value:Ft},n.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},Nt),g),className:Rt},W),(w==="top"||w==="both")&&ot,E&&n.createElement("div",{className:`${f}-header`},E),n.createElement(ct.Z,Object.assign({},j),q,O),N&&n.createElement("div",{className:`${f}-footer`},N),P||(w==="bottom"||w==="both")&&ot)))}const it=n.forwardRef(Et);it.Item=ft;var bt=it}}]);
